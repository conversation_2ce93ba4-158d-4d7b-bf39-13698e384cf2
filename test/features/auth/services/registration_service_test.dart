import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/auth/services/registration_service.dart';
import 'package:kiloba_biz/features/auth/models/check_registration_response_model.dart';
import 'package:kiloba_biz/features/auth/models/registration_model.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('RegistrationService - Check Registration Tests', () {
    late RegistrationService registrationService;

    setUp(() {
      registrationService = RegistrationService();
    });

    group('CheckRegistrationResponseModel', () {
      test('should create from JSON with existing registration', () {
        final json = {
          'success': true,
          'message': 'Registration record found',
          'data': {
            'id': 'test-id-123',
            'status': 'PENDING',
            'created_at': '2024-01-01T00:00:00Z',
          },
        };

        final response = CheckRegistrationResponseModel.fromJson(json);

        expect(response.exists, true);
        expect(response.id, 'test-id-123');
        expect(response.status, 'PENDING');
        expect(response.createdAt, isA<DateTime>());
      });

      test('should create from JSON with non-existing registration', () {
        final json = {
          'success': false,
          'message': 'Registration record not found',
          'data': null,
        };

        final response = CheckRegistrationResponseModel.fromJson(json);

        expect(response.exists, false);
        expect(response.id, null);
        expect(response.status, null);
        expect(response.createdAt, null);
      });

      test('should create not found response', () {
        final response = CheckRegistrationResponseModel.notFound();

        expect(response.exists, false);
        expect(response.id, null);
        expect(response.status, null);
        expect(response.createdAt, null);
      });
    });

    group('Helper Methods', () {
      test('should check registration exists', () {
        final existingResponse = CheckRegistrationResponseModel(
          id: 'test-id',
          status: 'PENDING',
          createdAt: DateTime.now(),
          exists: true,
        );

        final nonExistingResponse = CheckRegistrationResponseModel(
          exists: false,
        );

        expect(
          registrationService.isRegistrationExists(existingResponse),
          true,
        );
        expect(
          registrationService.isRegistrationExists(nonExistingResponse),
          false,
        );
      });

      test('should get status description', () {
        final pendingResponse = CheckRegistrationResponseModel(
          status: 'PENDING',
          exists: true,
        );

        final approvedResponse = CheckRegistrationResponseModel(
          status: 'APPROVED',
          exists: true,
        );

        final notFoundResponse = CheckRegistrationResponseModel(exists: false);

        expect(
          registrationService.getRegistrationStatusDescription(pendingResponse),
          'Chờ xử lý',
        );
        expect(
          registrationService.getRegistrationStatusDescription(
            approvedResponse,
          ),
          'Đã duyệt',
        );
        expect(
          registrationService.getRegistrationStatusDescription(
            notFoundResponse,
          ),
          'Chưa đăng ký',
        );
      });

      test('should check if can update registration', () {
        final pendingResponse = CheckRegistrationResponseModel(
          status: 'PENDING',
          exists: true,
        );

        final rejectedResponse = CheckRegistrationResponseModel(
          status: 'REJECTED',
          exists: true,
        );

        final approvedResponse = CheckRegistrationResponseModel(
          status: 'APPROVED',
          exists: true,
        );

        final notFoundResponse = CheckRegistrationResponseModel(exists: false);

        expect(
          registrationService.canUpdateRegistration(pendingResponse),
          true,
        );
        expect(
          registrationService.canUpdateRegistration(rejectedResponse),
          true,
        );
        expect(
          registrationService.canUpdateRegistration(approvedResponse),
          false,
        );
        expect(
          registrationService.canUpdateRegistration(notFoundResponse),
          false,
        );
      });

      test('should check if can delete registration', () {
        final pendingResponse = CheckRegistrationResponseModel(
          status: 'PENDING',
          exists: true,
        );

        final approvedResponse = CheckRegistrationResponseModel(
          status: 'APPROVED',
          exists: true,
        );

        final notFoundResponse = CheckRegistrationResponseModel(exists: false);

        expect(
          registrationService.canDeleteRegistration(pendingResponse),
          true,
        );
        expect(
          registrationService.canDeleteRegistration(approvedResponse),
          false,
        );
        expect(
          registrationService.canDeleteRegistration(notFoundResponse),
          false,
        );
      });
    });

    group('ID Card Validation', () {
      test('should validate valid ID card number format', () {
        // Test through validation errors method
        final validIdCard = '123456789012';
        final invalidIdCard1 = '12345678901';
        final invalidIdCard2 = '1234567890123';
        final invalidIdCard3 = '12345678901a';

        // Test valid ID card
        final validRegistration = RegistrationModel(
          fullName: 'Test User',
          idCardType: 'CHIP_ID',
          idCardNo: validIdCard,
          issueDate: '2020-01-01',
          issuePlace: 'Test Place',
          permanentAddress: 'Test Address',
          phoneNumber: '0123456789',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'test-province-id',
          branchId: 'test-branch-id',
          dataSource: 'APP_SALE',
        );

        expect(
          registrationService.getValidationErrors(validRegistration),
          isNot(contains('Số CMND/CCCD không đúng định dạng (12 chữ số)')),
        );

        // Test invalid ID cards
        final invalidRegistration1 = RegistrationModel(
          fullName: 'Test User',
          idCardType: 'CHIP_ID',
          idCardNo: invalidIdCard1,
          issueDate: '2020-01-01',
          issuePlace: 'Test Place',
          permanentAddress: 'Test Address',
          phoneNumber: '0123456789',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'test-province-id',
          branchId: 'test-branch-id',
          dataSource: 'APP_SALE',
        );

        expect(
          registrationService.getValidationErrors(invalidRegistration1),
          contains('Số CMND/CCCD không đúng định dạng (12 chữ số)'),
        );

        final invalidRegistration2 = RegistrationModel(
          fullName: 'Test User',
          idCardType: 'CHIP_ID',
          idCardNo: invalidIdCard2,
          issueDate: '2020-01-01',
          issuePlace: 'Test Place',
          permanentAddress: 'Test Address',
          phoneNumber: '0123456789',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'test-province-id',
          branchId: 'test-branch-id',
          dataSource: 'APP_SALE',
        );

        expect(
          registrationService.getValidationErrors(invalidRegistration2),
          contains('Số CMND/CCCD không đúng định dạng (12 chữ số)'),
        );

        final invalidRegistration3 = RegistrationModel(
          fullName: 'Test User',
          idCardType: 'CHIP_ID',
          idCardNo: invalidIdCard3,
          issueDate: '2020-01-01',
          issuePlace: 'Test Place',
          permanentAddress: 'Test Address',
          phoneNumber: '0123456789',
          email: '<EMAIL>',
          registerType: 'COLLABORATOR',
          provinceId: 'test-province-id',
          branchId: 'test-branch-id',
          dataSource: 'APP_SALE',
        );

        expect(
          registrationService.getValidationErrors(invalidRegistration3),
          contains('Số CMND/CCCD không đúng định dạng (12 chữ số)'),
        );
      });
    });
  });
}
