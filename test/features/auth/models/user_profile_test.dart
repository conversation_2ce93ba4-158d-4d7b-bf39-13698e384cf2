import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/auth/models/user_profile.dart';

void main() {
  group('UserProfile', () {
    group('fromJson', () {
      test('should parse nested API response correctly', () {
        // Arrange
        final jsonData = {
          "id": "355acd86-204f-4728-90af-8a14c3418679",
          "username": "dongtq",
          "email": "<EMAIL>",
          "display_name": "<PERSON><PERSON> (CNTT- Dong (CNTT- HO)",
          "roles": null,
          "status": "ACTIVE",
          "avatar": null,
          "person": {
            "id": "e7e4e8a5-687e-47f8-bbf4-52dabba7cff0",
            "code": "EMP0967595420",
            "full_name": "Trần Quý Đ<PERSON>ng",
            "cif_no": "CIF0967595420",
            "phone_number": "0967595420",
            "id_card_no": "IDNO0967595420",
            "issue_date": "2010-01-01",
            "expiry_date": "2030-01-01",
            "issue_place": "Công an TP.HCM",
            "permanent_address": "123 Đường ABC, Quận 1, TP.HCM",
            "status": "ACTIVE",
            "type": "EMPLOYEE"
          },
          "branch": {
            "id": "b78d179d-6e8b-4aad-a6e7-43fea057e939",
            "code": "073",
            "name": "CN An Giang",
            "address": "Lô 21 - 22A2 Lý Thái Tổ, Khu phố 3, phường Long Xuyên, tỉnh An Giang"
          },
          "position": {
            "id": "e517ec2a-6466-4ac3-b94e-aa57e45f592a",
            "code": "CEO",
            "name": "Tổng Giám đốc"
          },
          "manager": {
            "id": null,
            "full_name": null
          }
        };

        // Act
        final userProfile = UserProfile.fromJson(jsonData);

        // Assert
        expect(userProfile.id, equals("355acd86-204f-4728-90af-8a14c3418679"));
        expect(userProfile.username, equals("dongtq"));
        expect(userProfile.email, equals("<EMAIL>"));
        expect(userProfile.displayName, equals("Tran Quy Dong (CNTT- Dong (CNTT- HO)"));
        expect(userProfile.status, equals("ACTIVE"));
        expect(userProfile.roles, isNull);
        expect(userProfile.avatar, isNull);

        // Person information
        expect(userProfile.personId, equals("e7e4e8a5-687e-47f8-bbf4-52dabba7cff0"));
        expect(userProfile.personCode, equals("EMP0967595420"));
        expect(userProfile.personFullName, equals("Trần Quý Đông"));
        expect(userProfile.personCifNo, equals("CIF0967595420"));
        expect(userProfile.personPhoneNumber, equals("0967595420"));
        expect(userProfile.personIdCardNo, equals("IDNO0967595420"));
        expect(userProfile.personIssueDate, equals("2010-01-01"));
        expect(userProfile.personExpiryDate, equals("2030-01-01"));
        expect(userProfile.personIssuePlace, equals("Công an TP.HCM"));
        expect(userProfile.personPermanentAddress, equals("123 Đường ABC, Quận 1, TP.HCM"));
        expect(userProfile.personStatus, equals("ACTIVE"));
        expect(userProfile.personType, equals("EMPLOYEE"));

        // Branch information
        expect(userProfile.branchId, equals("b78d179d-6e8b-4aad-a6e7-43fea057e939"));
        expect(userProfile.branchCode, equals("073"));
        expect(userProfile.branchName, equals("CN An Giang"));
        expect(userProfile.branchAddress, equals("Lô 21 - 22A2 Lý Thái Tổ, Khu phố 3, phường Long Xuyên, tỉnh An Giang"));

        // Position information
        expect(userProfile.positionId, equals("e517ec2a-6466-4ac3-b94e-aa57e45f592a"));
        expect(userProfile.positionCode, equals("CEO"));
        expect(userProfile.positionName, equals("Tổng Giám đốc"));

        // Manager information (null in this case)
        expect(userProfile.managerId, isNull);
        expect(userProfile.managerFullName, isNull);
      });

      test('should handle missing nested objects gracefully', () {
        // Arrange
        final jsonData = {
          "id": "test-id",
          "username": "testuser",
          "email": "<EMAIL>",
          "display_name": "Test User",
          "status": "ACTIVE",
          // No nested objects
        };

        // Act
        final userProfile = UserProfile.fromJson(jsonData);

        // Assert
        expect(userProfile.id, equals("test-id"));
        expect(userProfile.username, equals("testuser"));
        expect(userProfile.email, equals("<EMAIL>"));
        expect(userProfile.displayName, equals("Test User"));
        expect(userProfile.status, equals("ACTIVE"));

        // All nested fields should be null
        expect(userProfile.personId, isNull);
        expect(userProfile.personCifNo, isNull);
        expect(userProfile.branchId, isNull);
        expect(userProfile.positionId, isNull);
        expect(userProfile.managerId, isNull);
      });

      test('should parse roles as string', () {
        // Arrange
        final jsonData = {
          "id": "test-id",
          "username": "testuser",
          "email": "<EMAIL>",
          "display_name": "Test User",
          "status": "ACTIVE",
          "roles": "ADMIN,USER,MANAGER",
        };

        // Act
        final userProfile = UserProfile.fromJson(jsonData);

        // Assert
        expect(userProfile.roles, equals(["ADMIN", "USER", "MANAGER"]));
      });

      test('should parse roles as array', () {
        // Arrange
        final jsonData = {
          "id": "test-id",
          "username": "testuser",
          "email": "<EMAIL>",
          "display_name": "Test User",
          "status": "ACTIVE",
          "roles": ["ADMIN", "USER", "MANAGER"],
        };

        // Act
        final userProfile = UserProfile.fromJson(jsonData);

        // Assert
        expect(userProfile.roles, equals(["ADMIN", "USER", "MANAGER"]));
      });
    });

    group('toJson', () {
      test('should convert to flat JSON structure for storage', () {
        // Arrange
        final userProfile = UserProfile(
          id: "test-id",
          username: "testuser",
          email: "<EMAIL>",
          displayName: "Test User",
          status: "ACTIVE",
          roles: ["ADMIN", "USER"],
          personCifNo: "CIF123456",
          branchName: "Test Branch",
          positionName: "Test Position",
        );

        // Act
        final json = userProfile.toJson();

        // Assert
        expect(json['id'], equals("test-id"));
        expect(json['username'], equals("testuser"));
        expect(json['email'], equals("<EMAIL>"));
        expect(json['display_name'], equals("Test User"));
        expect(json['status'], equals("ACTIVE"));
        expect(json['roles'], equals("ADMIN,USER")); // Converted to comma-separated string
        expect(json['person_cif_no'], equals("CIF123456"));
        expect(json['branch_name'], equals("Test Branch"));
        expect(json['position_name'], equals("Test Position"));
      });
    });
  });
}
