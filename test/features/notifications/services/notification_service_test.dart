import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/notifications/services/notification_service.dart';
import 'package:kiloba_biz/features/notifications/models/notification_summary.dart';
import 'package:kiloba_biz/shared/services/api/simple_api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import '../../../helpers/simple_api_test_helper.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('NotificationService Integration Tests', () {
    setUpAll(() async {
      // Setup và login với real server
      final loginSuccess =
          await SimpleApiTestHelper.setupAndLoginTestApiService(
            baseUrl: TestConfig.testBaseUrl,
            username: TestConfig.testUsername,
            password: TestConfig.testPassword,
          );

      print('🧪 Notification Test Environment Setup Complete');
      print(TestConfig.configurationSummary);
      print('🔐 Login Status: ${loginSuccess ? "SUCCESS" : "FAILED"}');

      if (!loginSuccess) {
        throw Exception('Failed to login with test credentials');
      }
    });

    tearDownAll(() async {
      await SimpleApiTestHelper.resetTestEnvironment();
      print('🧹 Notification Test Environment Cleaned Up');
    });

    test('should setup test environment correctly', () {
      expect(SimpleApiTestHelper.isTestEnvironmentReady(), isTrue);
      expect(TestConfig.isTestEnvironment, isTrue);
    });

    test('should be logged in with valid token', () {
      final simpleApiService = getIt.get<IApiService>() as SimpleApiService;
      expect(simpleApiService.isLoggedIn, isTrue);
      expect(simpleApiService.accessToken, isNotEmpty);
      print(
        '✅ Login verified - Token: ${simpleApiService.accessToken?.substring(0, 20)}...',
      );
    });

    test('should fetch notification summary successfully', () async {
      final service = NotificationService();

      // Clear cache để đảm bảo fetch từ API
      service.clearCache();

      final result = await service.getNotificationSummary();

      // Kiểm tra kết quả
      if (result != null) {
        expect(result, isA<NotificationSummary>());
        expect(result.totalNotifications, isA<int>());
        expect(result.unreadCount, isA<int>());
        expect(result.readCount, isA<int>());
        expect(result.todayCount, isA<int>());
        expect(result.importantCount, isA<int>());

        // Kiểm tra logic business rules
        expect(result.totalNotifications, equals(result.unreadCount + result.readCount));
        expect(result.unreadCount, greaterThanOrEqualTo(0));
        expect(result.readCount, greaterThanOrEqualTo(0));
        expect(result.todayCount, greaterThanOrEqualTo(0));
        expect(result.importantCount, greaterThanOrEqualTo(0));
        expect(result.todayCount, lessThanOrEqualTo(result.totalNotifications));
        expect(result.importantCount, lessThanOrEqualTo(result.totalNotifications));

        // Test helper properties
        expect(result.readPercentage, inInclusiveRange(0.0, 1.0));
        expect(result.unreadPercentage, inInclusiveRange(0.0, 1.0));
        if (result.totalNotifications > 0) {
          expect(result.readPercentage + result.unreadPercentage, closeTo(1.0, 0.001));
        }

        print('✅ Notification summary fetched successfully:');
        print('- Total Notifications: ${result.totalNotifications}');
        print('- Unread Count: ${result.unreadCount}');
        print('- Read Count: ${result.readCount}');
        print('- Today Count: ${result.todayCount}');
        print('- Important Count: ${result.importantCount}');
        print('- Read Percentage: ${(result.readPercentage * 100).toStringAsFixed(1)}%');
        print('- Has Unread: ${result.hasUnreadNotifications}');
        print('- Has Important: ${result.hasImportantNotifications}');
        print('- Has Today: ${result.hasTodayNotifications}');
      } else {
        // Trường hợp không có dữ liệu (có thể do không có thông báo hoặc JWT issue)
        print('⚠️ Notification summary is null - could be no notifications or JWT issue');
        expect(result, isNull);
      }
    });

    test('should handle cache mechanism correctly', () async {
      final service = NotificationService();

      // Clear cache trước
      service.clearCache();
      expect(service.getCachedSummary(), isNull);

      // Lần đầu fetch từ API
      final result1 = await service.getNotificationSummary();
      final cachedResult1 = service.getCachedSummary();

      if (result1 != null) {
        expect(cachedResult1, isNotNull);
        expect(cachedResult1!.totalNotifications, equals(result1.totalNotifications));
        expect(cachedResult1.unreadCount, equals(result1.unreadCount));

        print('✅ Cache mechanism working - first fetch cached');
        
        // Lần thứ 2 fetch từ cache (nên trả về same object)
        final result2 = await service.getNotificationSummary();
        expect(result2, isNotNull);
        expect(result2!.totalNotifications, equals(result1.totalNotifications));
        
        print('✅ Cache mechanism working - second fetch from cache');
      } else {
        print('⚠️ Cannot test cache with null result');
      }
    });

    test('should clear cache correctly', () async {
      final service = NotificationService();

      // Fetch để có cache
      await service.getNotificationSummary();
      
      // Kiểm tra cache có data
      final cachedBefore = service.getCachedSummary();
      
      // Clear cache
      service.clearCache();
      
      // Kiểm tra cache đã bị clear
      final cachedAfter = service.getCachedSummary();
      expect(cachedAfter, isNull);
      
      print('✅ Cache clear test passed');
      if (cachedBefore != null) {
        print('- Had cached data before clear: ${cachedBefore.totalNotifications} notifications');
      }
      print('- Cache cleared successfully');
    });

    test('should check notification API availability', () async {
      final service = NotificationService();

      final isAvailable = await service.checkNotificationApiAvailability();

      expect(isAvailable, isTrue);
      print('✅ Notification API availability check passed');
    });
  });

  group('NotificationService Error Handling Tests', () {
    setUpAll(() async {
      // Setup với invalid credentials để test error handling
      final loginSuccess =
          await SimpleApiTestHelper.setupAndLoginTestApiService(
            baseUrl: TestConfig.testBaseUrl,
            username: 'invalid_user',
            password: 'invalid_password',
          );

      print(
        '🔐 Invalid Login Status: ${loginSuccess ? "UNEXPECTED SUCCESS" : "EXPECTED FAILED"}',
      );
    });

    tearDownAll(() async {
      await SimpleApiTestHelper.resetTestEnvironment();
    });

    test('should handle authentication failure gracefully', () async {
      final service = NotificationService();

      // With invalid credentials, should either throw exception or return null
      try {
        final result = await service.getNotificationSummary();
        // Nếu không throw exception, thì result should be null (no JWT case)
        expect(result, isNull);
        print('✅ Authentication failure handled gracefully - returned null');
      } on NotificationException catch (e) {
        // Nếu throw exception, kiểm tra type
        expect(e.type, anyOf([
          NotificationExceptionType.unauthorized,
          NotificationExceptionType.apiError,
        ]));
        print('✅ Authentication failure handled gracefully - threw NotificationException: ${e.message}');
      }
    });

    test('should return null when no JWT token', () async {
      final service = NotificationService();

      // Clear any existing cache
      service.clearCache();

      final result = await service.getNotificationSummary();

      // With invalid auth, API should return empty array, resulting in null
      expect(result, isNull);
      print('✅ No JWT handling test passed - returned null as expected');
    });
  });
}