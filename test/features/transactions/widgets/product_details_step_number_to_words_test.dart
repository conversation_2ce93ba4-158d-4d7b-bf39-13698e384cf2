import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/transactions/widgets/product_details_step.dart';
import 'package:kiloba_biz/shared/utils/number_to_words_util.dart';

void main() {
  group('ProductDetailsStep Number to Words Conversion Tests', () {
    
    test('should convert numbers to words correctly', () {
      // Create a test instance to access the private method
      final testCases = [
        {'input': '', 'expected': 'Không đồng'},
        {'input': '0', 'expected': 'Không đồng'},
        {'input': '1', 'expected': '1 đồng'},
        {'input': '100', 'expected': '100 đồng'},
        {'input': '1000', 'expected': '1.0 nghìn đồng'},
        {'input': '10000', 'expected': '10.0 nghìn đồng'},
        {'input': '100000', 'expected': '100.0 nghìn đồng'},
        {'input': '1000000', 'expected': '1.0 triệu đồng'},
        {'input': '10000000', 'expected': '10.0 triệu đồng'},
        {'input': '100000000', 'expected': '100.0 triệu đồng'},
        {'input': '1000000000', 'expected': '1.0 tỷ đồng'},
        {'input': '1500000000', 'expected': '1.5 tỷ đồng'},
        {'input': '2000000000', 'expected': '2.0 tỷ đồng'},
      ];
      
      // Test the current implementation
      for (final testCase in testCases) {
        final result = _testConvertNumberToWords(testCase['input']!);
        expect(result, equals(testCase['expected']), 
          reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
    
    test('should use NumberToWordsUtil for proper conversion', () {
      // Test with NumberToWordsUtil for comparison
      final testCases = [
        {'input': '0', 'expected': 'không đồng'},
        {'input': '1', 'expected': 'một đồng'},
        {'input': '10', 'expected': 'mười đồng'},
        {'input': '100', 'expected': 'một trăm đồng'},
        {'input': '1000', 'expected': 'một nghìn đồng'},
        {'input': '10000', 'expected': 'mười nghìn đồng'},
        {'input': '100000', 'expected': 'một trăm nghìn đồng'},
        {'input': '1000000', 'expected': 'một triệu đồng'},
        {'input': '10000000', 'expected': 'mười triệu đồng'},
        {'input': '100000000', 'expected': 'một trăm triệu đồng'},
        {'input': '1000000000', 'expected': 'một tỷ đồng'},
        {'input': '1500000000', 'expected': 'một tỷ năm trăm triệu đồng'},
        {'input': '2000000000', 'expected': 'hai tỷ đồng'},
      ];
      
      for (final testCase in testCases) {
        final result = NumberToWordsUtil.convertToWords(int.parse(testCase['input']!));
        expect(result.toLowerCase(), equals(testCase['expected']), 
          reason: 'NumberToWordsUtil input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
    
    test('should handle edge cases correctly', () {
      final testCases = [
        {'input': 'abc', 'expected': 'Không đồng'}, // Invalid input
        {'input': '-1', 'expected': '-1 đồng'}, // Negative number (current behavior)
        {'input': '999999999999', 'expected': '1000.0 tỷ đồng'}, // Very large number
      ];
      
      for (final testCase in testCases) {
        final result = _testConvertNumberToWords(testCase['input']!);
        expect(result, equals(testCase['expected']), 
          reason: 'Edge case input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
    
    test('should format currency amounts correctly', () {
      final testCases = [
        {'input': '1234567', 'expected': '1.2 triệu đồng'},
        {'input': '12345678', 'expected': '12.3 triệu đồng'},
        {'input': '123456789', 'expected': '123.5 triệu đồng'},
        {'input': '1234567890', 'expected': '1.2 tỷ đồng'},
        {'input': '500000', 'expected': '500.0 nghìn đồng'},
        {'input': '5000000', 'expected': '5.0 triệu đồng'},
        {'input': '500000000', 'expected': '500.0 triệu đồng'},
        {'input': '5000000000', 'expected': '5.0 tỷ đồng'},
      ];
      
      for (final testCase in testCases) {
        final result = _testConvertNumberToWords(testCase['input']!);
        expect(result, equals(testCase['expected']), 
          reason: 'Currency input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
  });
}

// Helper function to test the private method
String _testConvertNumberToWords(String number) {
  // Simulate the current implementation
  if (number.isEmpty || number == '0') {
    return 'Không đồng';
  }
  
  final amount = int.tryParse(number) ?? 0;
  if (amount == 0) {
    return 'Không đồng';
  }
  
  // Current simplified conversion
  if (amount >= 1000000000) {
    return '${(amount / 1000000000).toStringAsFixed(1)} tỷ đồng';
  } else if (amount >= 1000000) {
    return '${(amount / 1000000).toStringAsFixed(1)} triệu đồng';
  } else if (amount >= 1000) {
    return '${(amount / 1000).toStringAsFixed(1)} nghìn đồng';
  } else {
    return '$amount đồng';
  }
}
