import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/transactions/widgets/product_details_step.dart';

void main() {
  group('ProductDetailsStep Validation Tests', () {
    
    group('V<PERSON>n tự có (Own Capital) Validation', () {
      test('should accept valid amounts within 1 billion range', () {
        final testCases = [
          '0',           // Minimum valid
          '1',           // 1 VND
          '1000000',     // 1 million
          '10000000',    // 10 million
          '100000000',   // 100 million
          '500000000',   // 500 million
          '999999999',   // 999 million (just below 1 billion)
          '1000000000',  // 1 billion (maximum)
        ];
        
        for (final amount in testCases) {
          expect(_isValidOwnCapital(amount), isTrue, 
            reason: 'Amount $amount should be valid');
        }
      });
      
      test('should reject amounts exceeding 1 billion', () {
        final testCases = [
          '1000000001',  // 1 billion + 1
          '1000000002',  // 1 billion + 2
          '1500000000',  // 1.5 billion
          '2000000000',  // 2 billion
          '5000000000',  // 5 billion
          '9999999999',  // 10 billion
          '10000000000', // 10 billion
        ];
        
        for (final amount in testCases) {
          expect(_isValidOwnCapital(amount), isFalse, 
            reason: 'Amount $amount should be invalid (exceeds 1 billion)');
        }
      });
      
      test('should accept exactly 1 billion as maximum valid value', () {
        expect(_isValidOwnCapital('1000000000'), isTrue, 
          reason: 'Exactly 1 billion should be valid');
      });
      
      test('should reject negative amounts', () {
        final testCases = [
          '-1',
          '-1000000',
          '-1000000000',
        ];
        
        for (final amount in testCases) {
          expect(_isValidOwnCapital(amount), isFalse, 
            reason: 'Amount $amount should be invalid (negative)');
        }
      });
      
      test('should reject non-numeric values', () {
        final testCases = [
          'abc',
          '1a2b3c',
          '1,000,000',  // Contains commas
          '1.000.000',  // Contains dots
          '',
          ' ',
        ];
        
        for (final amount in testCases) {
          expect(_isValidOwnCapital(amount), isFalse, 
            reason: 'Amount "$amount" should be invalid (non-numeric)');
        }
      });
    });
    
    group('Số tiền đề nghị vay (Loan Amount) Validation', () {
      test('should accept valid amounts within 1 million to 1 billion range', () {
        final testCases = [
          '1000000',     // 1 million (minimum)
          '1000001',     // 1 million + 1
          '5000000',     // 5 million
          '10000000',    // 10 million
          '100000000',   // 100 million
          '500000000',   // 500 million
          '999999999',   // 999 million (just below 1 billion)
          '1000000000',  // 1 billion (maximum)
        ];
        
        for (final amount in testCases) {
          expect(_isValidLoanAmount(amount), isTrue, 
            reason: 'Amount $amount should be valid');
        }
      });
      
      test('should reject amounts below 1 million', () {
        final testCases = [
          '0',
          '1',
          '100000',      // 100 thousand
          '999999',      // Just below 1 million
        ];
        
        for (final amount in testCases) {
          expect(_isValidLoanAmount(amount), isFalse, 
            reason: 'Amount $amount should be invalid (below 1 million)');
        }
      });
      
      test('should reject amounts exceeding 1 billion', () {
        final testCases = [
          '1000000001',  // 1 billion + 1
          '1000000002',  // 1 billion + 2
          '1500000000',  // 1.5 billion
          '2000000000',  // 2 billion
          '5000000000',  // 5 billion
          '9999999999',  // 10 billion
          '10000000000', // 10 billion
        ];
        
        for (final amount in testCases) {
          expect(_isValidLoanAmount(amount), isFalse, 
            reason: 'Amount $amount should be invalid (exceeds 1 billion)');
        }
      });
      
      test('should accept exactly 1 billion as maximum valid value', () {
        expect(_isValidLoanAmount('1000000000'), isTrue, 
          reason: 'Exactly 1 billion should be valid');
      });
      
      test('should reject negative amounts', () {
        final testCases = [
          '-1',
          '-1000000',
          '-1000000000',
        ];
        
        for (final amount in testCases) {
          expect(_isValidLoanAmount(amount), isFalse, 
            reason: 'Amount $amount should be invalid (negative)');
        }
      });
      
      test('should reject non-numeric values', () {
        final testCases = [
          'abc',
          '1a2b3c',
          '1,000,000',  // Contains commas
          '1.000.000',  // Contains dots
          '',
          ' ',
        ];
        
        for (final amount in testCases) {
          expect(_isValidLoanAmount(amount), isFalse, 
            reason: 'Amount "$amount" should be invalid (non-numeric)');
        }
      });
    });
    
    group('Tổng nhu cầu vốn (Total Capital Need) Calculation', () {
      test('should calculate total capital need correctly', () {
        final testCases = [
          {
            'ownCapital': '0',
            'loanAmount': '1000000',
            'expected': '1000000',
          },
          {
            'ownCapital': '5000000',
            'loanAmount': '10000000',
            'expected': '15000000',
          },
          {
            'ownCapital': '100000000',
            'loanAmount': '500000000',
            'expected': '600000000',
          },
          {
            'ownCapital': '500000000',
            'loanAmount': '500000000',
            'expected': '1000000000',
          },
          {
            'ownCapital': '1000000000',
            'loanAmount': '1000000000',
            'expected': '2000000000',
          },
        ];
        
        for (final testCase in testCases) {
          final ownCapital = int.parse(testCase['ownCapital']!);
          final loanAmount = int.parse(testCase['loanAmount']!);
          final expected = int.parse(testCase['expected']!);
          
          final result = _calculateTotalCapitalNeed(ownCapital, loanAmount);
          expect(result, equals(expected), 
            reason: 'Total capital need for own: ${testCase['ownCapital']}, loan: ${testCase['loanAmount']} should be ${testCase['expected']}');
        }
      });
      
      test('should cap total at 2 billion maximum', () {
        final testCases = [
          {
            'ownCapital': 1000000000,
            'loanAmount': 1000000000,
            'expected': 2000000000,
          },
          {
            'ownCapital': 1500000000,
            'loanAmount': 1000000000,
            'expected': 2000000000, // Capped at 2 billion
          },
          {
            'ownCapital': 1000000000,
            'loanAmount': 1500000000,
            'expected': 2000000000, // Capped at 2 billion
          },
        ];
        
        for (final testCase in testCases) {
          final result = _calculateTotalCapitalNeed(
            testCase['ownCapital'] as int, 
            testCase['loanAmount'] as int
          );
          expect(result, equals(testCase['expected']), 
            reason: 'Total capital need for own: ${testCase['ownCapital']}, loan: ${testCase['loanAmount']} should be ${testCase['expected']}');
        }
      });
      
      test('should handle edge cases in calculation', () {
        final testCases = [
          {
            'ownCapital': 0,
            'loanAmount': 0,
            'expected': 0,
          },
          {
            'ownCapital': 1000000000,
            'loanAmount': 0,
            'expected': 1000000000,
          },
          {
            'ownCapital': 0,
            'loanAmount': 1000000000,
            'expected': 1000000000,
          },
        ];
        
        for (final testCase in testCases) {
          final result = _calculateTotalCapitalNeed(
            testCase['ownCapital'] as int, 
            testCase['loanAmount'] as int
          );
          expect(result, equals(testCase['expected']), 
            reason: 'Total capital need for own: ${testCase['ownCapital']}, loan: ${testCase['loanAmount']} should be ${testCase['expected']}');
        }
      });
    });
    
    group('Currency Formatting Tests', () {
      test('should format numbers with thousand separators correctly', () {
        final testCases = [
          {'input': '1000000', 'expected': '1.000.000'},
          {'input': '10000000', 'expected': '10.000.000'},
          {'input': '100000000', 'expected': '100.000.000'},
          {'input': '1000000000', 'expected': '1.000.000.000'},
          {'input': '1234567', 'expected': '1.234.567'},
          {'input': '12345678', 'expected': '12.345.678'},
          {'input': '123456789', 'expected': '123.456.789'},
          {'input': '0', 'expected': '0'},
          {'input': '123', 'expected': '123'},
          {'input': '1234', 'expected': '1.234'},
        ];
        
        for (final testCase in testCases) {
          final result = _formatCurrency(testCase['input']!);
          expect(result, equals(testCase['expected']), 
            reason: 'Formatting ${testCase['input']} should result in ${testCase['expected']}');
        }
      });
      
      test('should handle empty and invalid inputs', () {
        final testCases = [
          {'input': '', 'expected': ''},
          {'input': 'abc', 'expected': ''},
          {'input': '1,000,000', 'expected': '1.000.000'},  // Remove commas, add dots
          {'input': '1.000.000', 'expected': '1.000.000'},  // Already formatted with dots
        ];
        
        for (final testCase in testCases) {
          final result = _formatCurrency(testCase['input']!);
          expect(result, equals(testCase['expected']), 
            reason: 'Formatting "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
    });
    
    group('Input Sanitization Tests', () {
      test('should remove non-numeric characters correctly', () {
        final testCases = [
          {'input': '1,000,000', 'expected': '1000000'},
          {'input': '1.000.000', 'expected': '1000000'},
          {'input': '1 000 000', 'expected': '1000000'},
          {'input': '1,000.000', 'expected': '1000000'},
          {'input': '1.000,000', 'expected': '1000000'},
          {'input': '1a2b3c', 'expected': '123'},
          {'input': 'abc123def', 'expected': '123'},
          {'input': '123', 'expected': '123'},
          {'input': '', 'expected': ''},
        ];
        
        for (final testCase in testCases) {
          final result = _sanitizeNumericInput(testCase['input']!);
          expect(result, equals(testCase['expected']), 
            reason: 'Sanitizing "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should remove leading zeros correctly', () {
        final testCases = [
          {'input': '000123', 'expected': '123'},
          {'input': '000000', 'expected': '0'},
          {'input': '0000000', 'expected': '0'},
          {'input': '123000', 'expected': '123000'},
          {'input': '0', 'expected': '0'},
          {'input': '123', 'expected': '123'},
        ];
        
        for (final testCase in testCases) {
          final result = _removeLeadingZeros(testCase['input']!);
          expect(result, equals(testCase['expected']), 
            reason: 'Removing leading zeros from "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
    });
  });
}

// Helper functions for testing (these would be extracted from ProductDetailsStep)

/// Validate own capital amount
bool _isValidOwnCapital(String amount) {
  if (amount.isEmpty) return false;
  
  // Check for negative sign first
  if (amount.contains('-')) return false;
  
  // Check if input contains only digits (no commas, dots, letters, etc.)
  if (!RegExp(r'^\d+$').hasMatch(amount)) return false;
  
  // Remove leading zeros
  final sanitizedAmount = _removeLeadingZeros(amount);
  if (sanitizedAmount.isEmpty) return false;
  
  final numericAmount = int.tryParse(sanitizedAmount);
  if (numericAmount == null) return false;
  
  // Check range: 0 to 1 billion
  return numericAmount >= 0 && numericAmount <= 1000000000;
}

/// Validate loan amount
bool _isValidLoanAmount(String amount) {
  if (amount.isEmpty) return false;
  
  // Check for negative sign first
  if (amount.contains('-')) return false;
  
  // Check if input contains only digits (no commas, dots, letters, etc.)
  if (!RegExp(r'^\d+$').hasMatch(amount)) return false;
  
  // Remove leading zeros
  final sanitizedAmount = _removeLeadingZeros(amount);
  if (sanitizedAmount.isEmpty) return false;
  
  final numericAmount = int.tryParse(sanitizedAmount);
  if (numericAmount == null) return false;
  
  // Check range: 1 million to 1 billion
  return numericAmount >= 1000000 && numericAmount <= 1000000000;
}

/// Calculate total capital need
int _calculateTotalCapitalNeed(int ownCapital, int loanAmount) {
  final total = ownCapital + loanAmount;
  // Cap at 2 billion maximum
  return total > 2000000000 ? 2000000000 : total;
}

/// Format currency with thousand separators
String _formatCurrency(String value) {
  if (value.isEmpty) return '';
  
  final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
  if (cleanValue.isEmpty) return '';
  
  final number = int.tryParse(cleanValue) ?? 0;
  return number.toString().replaceAllMapped(
    RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
    (match) => '${match[1]}.',
  );
}

/// Sanitize numeric input by removing non-numeric characters
String _sanitizeNumericInput(String input) {
  return input.replaceAll(RegExp(r'[^\d]'), '');
}

/// Remove leading zeros from numeric string
String _removeLeadingZeros(String input) {
  if (input.isEmpty) return '';
  
  // Remove all leading zeros
  String cleaned = input.replaceFirst(RegExp(r'^0+'), '');
  
  // If the result is empty (all zeros), keep one zero
  if (cleaned.isEmpty && input.isNotEmpty) {
    cleaned = '0';
  }
  
  return cleaned;
}
