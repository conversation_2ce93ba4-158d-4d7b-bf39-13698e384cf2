import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/number_to_words_util.dart';

void main() {
  group('ProductDetailsStep Number to Words Updated Tests', () {
    
    test('should use NumberToWordsUtil for proper Vietnamese conversion', () {
      final testCases = [
        {'input': '0', 'expected': 'không đồng'},
        {'input': '1', 'expected': 'một đồng'},
        {'input': '10', 'expected': 'mười đồng'},
        {'input': '100', 'expected': 'một trăm đồng'},
        {'input': '1000', 'expected': 'một nghìn đồng'},
        {'input': '10000', 'expected': 'mười nghìn đồng'},
        {'input': '100000', 'expected': 'một trăm nghìn đồng'},
        {'input': '1000000', 'expected': 'một triệu đồng'},
        {'input': '10000000', 'expected': 'mười triệu đồng'},
        {'input': '100000000', 'expected': 'một trăm triệu đồng'},
        {'input': '1000000000', 'expected': 'một tỷ đồng'},
        {'input': '1500000000', 'expected': 'một tỷ năm trăm triệu đồng'},
        {'input': '2000000000', 'expected': 'hai tỷ đồng'},
      ];
      
      for (final testCase in testCases) {
        final result = NumberToWordsUtil.convertToWords(int.parse(testCase['input']!));
        expect(result.toLowerCase(), equals(testCase['expected']), 
          reason: 'NumberToWordsUtil input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
    
    test('should handle complex numbers correctly', () {
      final testCases = [
        {'input': '1234567', 'expected': 'một triệu hai trăm ba mươi bốn nghìn năm trăm sáu mươi bảy đồng'},
        {'input': '12345678', 'expected': 'mười hai triệu ba trăm bốn mươi lăm nghìn sáu trăm bảy mươi tám đồng'},
        {'input': '123456789', 'expected': 'một trăm hai mươi ba triệu bốn trăm năm mươi sáu nghìn bảy trăm tám mươi chín đồng'},
        {'input': '1234567890', 'expected': 'một tỷ hai trăm ba mươi bốn triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi đồng'},
        {'input': '500000', 'expected': 'năm trăm nghìn đồng'},
        {'input': '5000000', 'expected': 'năm triệu đồng'},
        {'input': '500000000', 'expected': 'năm trăm triệu đồng'},
        {'input': '5000000000', 'expected': 'năm tỷ đồng'},
      ];
      
      for (final testCase in testCases) {
        final result = NumberToWordsUtil.convertToWords(int.parse(testCase['input']!));
        expect(result.toLowerCase(), equals(testCase['expected']), 
          reason: 'Complex number input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
    
    test('should handle edge cases properly', () {
      final testCases = [
        {'input': '0', 'expected': 'không đồng'},
        {'input': '1', 'expected': 'một đồng'},
        {'input': '2', 'expected': 'hai đồng'},
        {'input': '3', 'expected': 'ba đồng'},
        {'input': '4', 'expected': 'bốn đồng'},
        {'input': '5', 'expected': 'năm đồng'},
        {'input': '6', 'expected': 'sáu đồng'},
        {'input': '7', 'expected': 'bảy đồng'},
        {'input': '8', 'expected': 'tám đồng'},
        {'input': '9', 'expected': 'chín đồng'},
        {'input': '10', 'expected': 'mười đồng'},
        {'input': '11', 'expected': 'mười một đồng'},
        {'input': '20', 'expected': 'hai mươi đồng'},
        {'input': '21', 'expected': 'hai mươi một đồng'},
        {'input': '100', 'expected': 'một trăm đồng'},
        {'input': '101', 'expected': 'một trăm lẻ một đồng'},
        {'input': '110', 'expected': 'một trăm mười đồng'},
        {'input': '111', 'expected': 'một trăm mười một đồng'},
      ];
      
      for (final testCase in testCases) {
        final result = NumberToWordsUtil.convertToWords(int.parse(testCase['input']!));
        expect(result.toLowerCase(), equals(testCase['expected']), 
          reason: 'Edge case input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
    
    test('should handle large numbers correctly (within NumberToWordsUtil limits)', () {
      final testCases = [
        {'input': '1000000000', 'expected': 'một tỷ đồng'},
        {'input': '2000000000', 'expected': 'hai tỷ đồng'},
      ];
      
      for (final testCase in testCases) {
        final result = NumberToWordsUtil.convertToWords(int.parse(testCase['input']!));
        expect(result.toLowerCase(), equals(testCase['expected']), 
          reason: 'Large number input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
    
    test('should handle mixed numbers correctly', () {
      final testCases = [
        {'input': '1234', 'expected': 'một nghìn hai trăm ba mươi bốn đồng'},
        {'input': '12345', 'expected': 'mười hai nghìn ba trăm bốn mươi lăm đồng'},
        {'input': '123456', 'expected': 'một trăm hai mươi ba nghìn bốn trăm năm mươi sáu đồng'},
        {'input': '1234567', 'expected': 'một triệu hai trăm ba mươi bốn nghìn năm trăm sáu mươi bảy đồng'},
        {'input': '12345678', 'expected': 'mười hai triệu ba trăm bốn mươi lăm nghìn sáu trăm bảy mươi tám đồng'},
        {'input': '123456789', 'expected': 'một trăm hai mươi ba triệu bốn trăm năm mươi sáu nghìn bảy trăm tám mươi chín đồng'},
      ];
      
      for (final testCase in testCases) {
        final result = NumberToWordsUtil.convertToWords(int.parse(testCase['input']!));
        expect(result.toLowerCase(), equals(testCase['expected']), 
          reason: 'Mixed number input "${testCase['input']}" should result in "${testCase['expected']}"');
      }
    });
  });
}
