import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/products/services/product_service.dart';
import 'package:kiloba_biz/features/products/models/product_model.dart';
import 'package:kiloba_biz/shared/services/api/simple_api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/models/index.dart';
import '../../../helpers/simple_api_test_helper.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('ProductService Integration Tests', () {
    setUpAll(() async {
      // Setup và login với real server
      final loginSuccess =
          await SimpleApiTestHelper.setupAndLoginTestApiService(
            baseUrl: TestConfig.testBaseUrl,
            username: TestConfig.testUsername,
            password: TestConfig.testPassword,
          );

      print('🧪 Product Test Environment Setup Complete');
      print(TestConfig.configurationSummary);
      print('🔐 Login Status: ${loginSuccess ? "SUCCESS" : "FAILED"}');

      if (!loginSuccess) {
        throw Exception('Failed to login with test credentials');
      }
    });

    tearDownAll(() async {
      await SimpleApiTestHelper.resetTestEnvironment();
      print('🧹 Product Test Environment Cleaned Up');
    });

    test('should setup test environment correctly', () {
      expect(SimpleApiTestHelper.isTestEnvironmentReady(), isTrue);
      expect(TestConfig.isTestEnvironment, isTrue);
    });

    test('should be logged in with valid token', () {
      final simpleApiService = getIt.get<IApiService>() as SimpleApiService;
      expect(simpleApiService.isLoggedIn, isTrue);
      expect(simpleApiService.accessToken, isNotEmpty);
      print(
        '✅ Login verified - Token: ${simpleApiService.accessToken?.substring(0, 20)}...',
      );
    });

    test('should fetch products successfully', () async {
      final service = ProductService();

      // Clear cache để đảm bảo fetch từ API
      service.clearCache();

      final result = await service.getProducts();

      expect(result, isA<BaseResponse<ProductsListData>>());
      expect(result.isSuccess, isTrue);
      expect(result.data, isNotNull);
      expect(result.data!.products, isA<List<ProductModel>>());
      expect(result.data!.totalCount, isA<int>());
      expect(result.data!.totalCount, equals(result.data!.products.length));

      if (result.data!.hasProducts) {
        // Kiểm tra structure của product đầu tiên
        final firstProduct = result.data!.products.first;
        expect(firstProduct.id, isNotEmpty);
        expect(firstProduct.code, isNotEmpty);
        expect(firstProduct.name, isNotEmpty);
        expect(firstProduct.group, isNotEmpty);
        expect(firstProduct.status, isNotEmpty);

        // Kiểm tra helper methods
        expect(firstProduct.displayName, isNotEmpty);
        expect(firstProduct.displayDescription, isNotEmpty);
        expect(firstProduct.isActive || firstProduct.isInactive, isTrue);

        print('✅ Products fetched successfully:');
        print('- Total Products: ${result.data!.totalCount}');
        print('- Active Products: ${result.data!.activeProducts.length}');
        print('- Featured Products: ${result.data!.featuredProducts.length}');
        print('- First Product: ${firstProduct.name} (${firstProduct.code})');
        print('- First Product Status: ${firstProduct.status}');
        print('- First Product Group: ${firstProduct.group}');
      } else {
        print('⚠️ No products found in response');
        expect(result.data!.isEmpty, isTrue);
        expect(result.data!.totalCount, equals(0));
      }
    });

    test('should handle cache mechanism correctly', () async {
      final service = ProductService();

      // Clear cache trước
      service.clearCache();
      expect(service.getCachedProducts(), isNull);

      // Lần đầu fetch từ API
      final result1 = await service.getProducts();
      final cachedResult1 = service.getCachedProducts();

      expect(cachedResult1, isNotNull);
      expect(cachedResult1!.data!.totalCount, equals(result1.data!.totalCount));
      expect(cachedResult1!.data!.products.length, equals(result1.data!.products.length));

      print('✅ Cache mechanism working - first fetch cached');
      
      // Lần thứ 2 fetch từ cache (nên trả về same data)
      final result2 = await service.getProducts();
      expect(result2.data!.totalCount, equals(result1.data!.totalCount));
      expect(result2.data!.products.length, equals(result1.data!.products.length));
      
      print('✅ Cache mechanism working - second fetch from cache');
    });

    test('should clear cache correctly', () async {
      final service = ProductService();

      // Fetch để có cache
      await service.getProducts();
      
      // Kiểm tra cache có data
      final cachedBefore = service.getCachedProducts();
      expect(cachedBefore, isNotNull);
      
      // Clear cache
      service.clearCache();
      
      // Kiểm tra cache đã bị clear
      final cachedAfter = service.getCachedProducts();
      expect(cachedAfter, isNull);
      
      print('✅ Cache clear test passed');
      print('- Had cached data before clear: ${cachedBefore!.data!.totalCount} products');
      print('- Cache cleared successfully');
    });

    test('should refresh products correctly', () async {
      final service = ProductService();

      // Fetch lần đầu
      final result1 = await service.getProducts();
      expect(result1, isNotNull);

      // Refresh
      final result2 = await service.refreshProducts();
      expect(result2, isNotNull);
      expect(result2.data!.totalCount, equals(result1.data!.totalCount));

      print('✅ Products refresh test passed');
      print('- Original count: ${result1.data!.totalCount}');
      print('- Refreshed count: ${result2.data!.totalCount}');
    });

    test('should get active products correctly', () async {
      final service = ProductService();

      final activeProducts = await service.getActiveProducts();

      expect(activeProducts, isA<List<ProductModel>>());
      
      // Kiểm tra tất cả products đều active
      for (final product in activeProducts) {
        expect(product.isActive, isTrue);
        expect(product.status.toUpperCase(), equals('ACTIVE'));
      }

      print('✅ Active products test passed');
      print('- Active products count: ${activeProducts.length}');
      
      if (activeProducts.isNotEmpty) {
        print('- First active product: ${activeProducts.first.name}');
      }
    });

    test('should search products correctly', () async {
      final service = ProductService();

      // Test empty query
      final allProducts = await service.searchProducts('');
      expect(allProducts, isA<List<ProductModel>>());

      // Test with actual search query if we have products
      final productsData = await service.getProducts();
      if (productsData.data!.hasProducts) {
        final firstProduct = productsData.data!.products.first;
        final searchQuery = firstProduct.name.substring(0, 3); // Take first 3 chars
        
        final searchResults = await service.searchProducts(searchQuery);
        expect(searchResults, isA<List<ProductModel>>());
        
        // Kiểm tra kết quả search có chứa query
        for (final product in searchResults) {
          final containsQuery = product.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
                               product.description.toLowerCase().contains(searchQuery.toLowerCase()) ||
                               product.code.toLowerCase().contains(searchQuery.toLowerCase());
          expect(containsQuery, isTrue);
        }

        print('✅ Product search test passed');
        print('- Search query: "$searchQuery"');
        print('- Search results: ${searchResults.length}');
      } else {
        print('⚠️ Cannot test search with no products available');
      }
    });

    test('should get product groups correctly', () async {
      final service = ProductService();

      final groups = await service.getProductGroups();

      expect(groups, isA<List<String>>());
      
      // Kiểm tra groups không có duplicates và đã được sort
      final uniqueGroups = groups.toSet().toList();
      expect(groups.length, equals(uniqueGroups.length));
      
      // Kiểm tra sorted
      final sortedGroups = [...groups]..sort();
      expect(groups, equals(sortedGroups));

      print('✅ Product groups test passed');
      print('- Groups found: ${groups.length}');
      print('- Groups: ${groups.join(", ")}');
    });

    test('should get products by group correctly', () async {
      final service = ProductService();

      final groups = await service.getProductGroups();
      
      if (groups.isNotEmpty) {
        final testGroup = groups.first;
        final groupProducts = await service.getProductsByGroup(testGroup);

        expect(groupProducts, isA<List<ProductModel>>());
        
        // Kiểm tra tất cả products đều thuộc group này
        for (final product in groupProducts) {
          expect(product.group.toLowerCase(), equals(testGroup.toLowerCase()));
        }

        print('✅ Products by group test passed');
        print('- Test group: "$testGroup"');
        print('- Products in group: ${groupProducts.length}');
      } else {
        print('⚠️ Cannot test products by group - no groups available');
      }
    });

    test('should get featured products correctly', () async {
      final service = ProductService();

      final featuredProducts = await service.getFeaturedProducts();

      expect(featuredProducts, isA<List<ProductModel>>());
      
      // Kiểm tra tất cả products đều featured
      for (final product in featuredProducts) {
        expect(product.displayConfig?.featured, isTrue);
      }

      print('✅ Featured products test passed');
      print('- Featured products count: ${featuredProducts.length}');
    });

    test('should validate product ID and code correctly', () {
      final service = ProductService();

      // Valid cases
      expect(service.isValidProductId('123'), isTrue);
      expect(service.isValidProductId('PROD_001'), isTrue);
      expect(service.isValidProductCode('CODE123'), isTrue);
      expect(service.isValidProductCode('LOAN'), isTrue);

      // Invalid cases
      expect(service.isValidProductId(''), isFalse);
      expect(service.isValidProductId('   '), isFalse);
      expect(service.isValidProductCode(''), isFalse);
      expect(service.isValidProductCode('   '), isFalse);

      print('✅ Product validation test passed');
    });

    test('should check products API availability', () async {
      final service = ProductService();

      final isAvailable = await service.checkProductsApiAvailability();

      expect(isAvailable, isTrue);
      print('✅ Products API availability check passed');
    });

    test('should format product display correctly', () async {
      final service = ProductService();

      final productsData = await service.getProducts();
      
      if (productsData.data!.hasProducts) {
        final product = productsData.data!.products.first;
        
        final formattedName = service.formatProductName(product);
        final formattedDescription = service.formatProductDescription(product);

        expect(formattedName, isNotEmpty);
        expect(formattedDescription, isNotEmpty);
        expect(formattedName, equals(product.displayName));
        expect(formattedDescription, equals(product.displayDescription));

        print('✅ Product formatting test passed');
        print('- Formatted name: "$formattedName"');
        print('- Formatted description: "$formattedDescription"');
      } else {
        print('⚠️ Cannot test formatting with no products available');
      }
    });
  });

  group('ProductService Error Handling Tests', () {
    setUpAll(() async {
      // Setup với invalid credentials để test error handling
      final loginSuccess =
          await SimpleApiTestHelper.setupAndLoginTestApiService(
            baseUrl: TestConfig.testBaseUrl,
            username: 'invalid_user',
            password: 'invalid_password',
          );

      print(
        '🔐 Invalid Login Status: ${loginSuccess ? "UNEXPECTED SUCCESS" : "EXPECTED FAILED"}',
      );
    });

    tearDownAll(() async {
      await SimpleApiTestHelper.resetTestEnvironment();
    });

    test('should handle authentication failure gracefully', () async {
      final service = ProductService();

      // Clear cache để đảm bảo không có cache từ lần trước
      service.clearCache();

      expect(
        () => service.getProducts(),
        throwsA(isA<ProductException>()),
      );

      print('✅ Authentication failure handling test passed');
    });

    test('should handle API availability check with invalid auth', () async {
      final service = ProductService();

      // Clear cache để đảm bảo không có cache từ lần trước
      service.clearCache();

      final isAvailable = await service.checkProductsApiAvailability();

      expect(isAvailable, isFalse);
      print('✅ API availability check with invalid auth test passed');
    });
  });

  group('ProductService Product Lookup Tests', () {
    late ProductService service;
    late BaseResponse<ProductsListData> testData;

    setUpAll(() async {
      // Setup với valid credentials
      final loginSuccess =
          await SimpleApiTestHelper.setupAndLoginTestApiService(
            baseUrl: TestConfig.testBaseUrl,
            username: TestConfig.testUsername,
            password: TestConfig.testPassword,
          );

      if (!loginSuccess) {
        throw Exception('Failed to login with test credentials');
      }

      service = ProductService();
      service.clearCache();
      testData = await service.getProducts();
    });

    tearDownAll(() async {
      await SimpleApiTestHelper.resetTestEnvironment();
    });

    test('should get product by ID successfully', () async {
      if (testData.data!.hasProducts) {
        final testProduct = testData.data!.products.first;
        
        final foundProduct = await service.getProductById(testProduct.id);

        expect(foundProduct, isNotNull);
        expect(foundProduct!.id, equals(testProduct.id));
        expect(foundProduct.name, equals(testProduct.name));
        expect(foundProduct.code, equals(testProduct.code));

        print('✅ Get product by ID test passed');
        print('- Test product ID: ${testProduct.id}');
        print('- Found product: ${foundProduct.name}');
      } else {
        print('⚠️ Cannot test get by ID with no products available');
      }
    });

    test('should get product by code successfully', () async {
      if (testData.data!.hasProducts) {
        final testProduct = testData.data!.products.first;
        
        final foundProduct = await service.getProductByCode(testProduct.code);

        expect(foundProduct, isNotNull);
        expect(foundProduct!.code.toLowerCase(), equals(testProduct.code.toLowerCase()));
        expect(foundProduct.id, equals(testProduct.id));
        expect(foundProduct.name, equals(testProduct.name));

        print('✅ Get product by code test passed');
        print('- Test product code: ${testProduct.code}');
        print('- Found product: ${foundProduct.name}');
      } else {
        print('⚠️ Cannot test get by code with no products available');
      }
    });

    test('should throw exception for non-existent product ID', () async {
      expect(
        () => service.getProductById('NON_EXISTENT_ID'),
        throwsA(isA<ProductException>().having(
          (e) => e.type,
          'type',
          ProductExceptionType.notFound,
        )),
      );

      print('✅ Non-existent product ID exception test passed');
    });

    test('should throw exception for non-existent product code', () async {
      expect(
        () => service.getProductByCode('NON_EXISTENT_CODE'),
        throwsA(isA<ProductException>().having(
          (e) => e.type,
          'type',
          ProductExceptionType.notFound,
        )),
      );

      print('✅ Non-existent product code exception test passed');
    });
  });

  group('ProductService Model Tests', () {
    test('should create ProductModel from JSON correctly', () {
      final json = {
        'id': '1',
        'code': 'LOAN001',
        'name': 'Personal Loan',
        'description': 'Personal loan product',
        'group': 'LOANS',
        'status': 'ACTIVE',
        'display_config': {
          'icon': 'loan_icon',
          'color': '#FF5722',
          'order': 1,
          'featured': true,
          'metadata': {'key': 'value'},
        },
      };

      final product = ProductModel.fromJson(json);

      expect(product.id, equals('1'));
      expect(product.code, equals('LOAN001'));
      expect(product.name, equals('Personal Loan'));
      expect(product.description, equals('Personal loan product'));
      expect(product.group, equals('LOANS'));
      expect(product.status, equals('ACTIVE'));
      expect(product.isActive, isTrue);
      expect(product.isInactive, isFalse);
      expect(product.displayName, equals('Personal Loan'));
      expect(product.displayDescription, equals('Personal loan product'));

      expect(product.displayConfig, isNotNull);
      expect(product.displayConfig!.icon, equals('loan_icon'));
      expect(product.displayConfig!.color, equals('#FF5722'));
      expect(product.displayConfig!.order, equals(1));
      expect(product.displayConfig!.featured, isTrue);

      print('✅ ProductModel fromJson test passed');
    });

    test('should create ProductsListData from JSON correctly', () {
      final json = {
        'products': [
          {
            'id': '1',
            'code': 'LOAN001',
            'name': 'Personal Loan',
            'description': 'Personal loan product',
            'group': 'LOANS',
            'status': 'ACTIVE',
          },
          {
            'id': '2',
            'code': 'CARD001',
            'name': 'Credit Card',
            'description': 'Credit card product',
            'group': 'CARDS',
            'status': 'INACTIVE',
          },
        ],
        'total_count': 2,
      };

      final productsData = ProductsListData.fromJson(json);

      expect(productsData.totalCount, equals(2));
      expect(productsData.products.length, equals(2));
      expect(productsData.hasProducts, isTrue);
      expect(productsData.isEmpty, isFalse);

      final activeProducts = productsData.activeProducts;
      expect(activeProducts.length, equals(1));
      expect(activeProducts.first.code, equals('LOAN001'));

      final loanProducts = productsData.getProductsByGroup('LOANS');
      expect(loanProducts.length, equals(1));
      expect(loanProducts.first.code, equals('LOAN001'));

      print('✅ ProductsListData fromJson test passed');
    });

    test('should handle empty ProductsListData correctly', () {
      const emptyData = ProductsListData.empty;

      expect(emptyData.totalCount, equals(0));
      expect(emptyData.products.length, equals(0));
      expect(emptyData.hasProducts, isFalse);
      expect(emptyData.isEmpty, isTrue);
      expect(emptyData.activeProducts.length, equals(0));
      expect(emptyData.featuredProducts.length, equals(0));

      print('✅ Empty ProductsListData test passed');
    });
  });
}
