import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/features/dashboard/services/dashboard_stats_service.dart';
import 'package:kiloba_biz/features/dashboard/models/dashboard_stats.dart';
import 'package:kiloba_biz/shared/services/api/simple_api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import '../../../helpers/simple_api_test_helper.dart';
import '../../../helpers/test_config.dart';

void main() {
  group('DashboardStatsService Integration Tests', () {
    setUpAll(() async {
      // Setup và login với real server
      final loginSuccess =
          await SimpleApiTestHelper.setupAndLoginTestApiService(
            baseUrl: TestConfig.testBaseUrl,
            username: TestConfig.testUsername,
            password: TestConfig.testPassword,
          );

      print('🧪 Test Environment Setup Complete');
      print(TestConfig.configurationSummary);
      print('🔐 Login Status: ${loginSuccess ? "SUCCESS" : "FAILED"}');

      if (!loginSuccess) {
        throw Exception('Failed to login with test credentials');
      }
    });

    tearDownAll(() async {
      await SimpleApiTestHelper.resetTestEnvironment();
      print('🧹 Test Environment Cleaned Up');
    });

    test('should setup test environment correctly', () {
      expect(SimpleApiTestHelper.isTestEnvironmentReady(), isTrue);
      expect(TestConfig.isTestEnvironment, isTrue);
    });

    test('should be logged in with valid token', () {
      final simpleApiService = getIt.get<IApiService>() as SimpleApiService;
      expect(simpleApiService.isLoggedIn, isTrue);
      expect(simpleApiService.accessToken, isNotEmpty);
      print(
        '✅ Login verified - Token: ${simpleApiService.accessToken?.substring(0, 20)}...',
      );
    });

    test('should fetch dashboard stats successfully', () async {
      final service = DashboardStatsService();

      final result = await service.getUserBusinessDashboardStats();

      expect(result, isA<DashboardStats>());
      expect(result.userCif, isNotEmpty);
      expect(result.userType, isNotEmpty);

      print('✅ Dashboard stats fetched successfully:');
      print('- User CIF: ${result.userCif}');
      print('- User Type: ${result.userType}');
      print('- Total Customers: ${result.totalCustomers}');
      print('- New Customers This Week: ${result.newCustomersThisWeek}');
      print('- Total Revenue: ${result.totalRevenue}');
      print('- Revenue This Week: ${result.revenueThisWeek}');
    });

    test('should handle 401 and auto-login successfully', () async {
      // Test scenario: service should auto-login khi gặp 401
      final service = DashboardStatsService();

      final result = await service.getUserBusinessDashboardStats();

      expect(result, isA<DashboardStats>());
      expect(result.userCif, isNotEmpty);

      print(
        '✅ Auto-login test passed - service handled authentication automatically',
      );
    });

    test('should check data API availability', () async {
      final service = DashboardStatsService();

      final isAvailable = await service.checkDataApiAvailability();

      expect(isAvailable, isTrue);
      print('✅ Data API availability check passed');
    });

    test('should refresh dashboard stats', () async {
      final service = DashboardStatsService();

      final result = await service.refreshDashboardStats();

      expect(result, isA<DashboardStats>());
      expect(result?.userCif, isNotEmpty);

      print('✅ Dashboard stats refresh test passed');
    });

    test('should format currency correctly', () {
      final service = DashboardStatsService();

      expect(service.formatCurrency(1000), equals('1K'));
      expect(service.formatCurrency(1000000), equals('1M'));
      expect(service.formatCurrency(1500000000), equals('1.5B'));
      expect(service.formatCurrency(500), equals('500'));

      print('✅ Currency formatting test passed');
    });

    test('should format new customers text correctly', () {
      final service = DashboardStatsService();

      expect(service.formatNewCustomersText(5), equals('+5 tuần'));
      expect(service.formatNewCustomersText(0), equals('0 tuần này'));

      print('✅ New customers text formatting test passed');
    });

    test('should format rejected proposals text correctly', () {
      final service = DashboardStatsService();

      expect(service.formatRejectedProposalsText(3, 10), equals('3 từ chối'));
      expect(service.formatRejectedProposalsText(0, 10), equals('0 từ chối'));

      print('✅ Rejected proposals text formatting test passed');
    });

    test('should format revenue this week text correctly', () {
      final service = DashboardStatsService();

      expect(service.formatRevenueThisWeekText(1000000), equals('+1M tuần'));
      expect(service.formatRevenueThisWeekText(0), equals('+0M tuần'));

      print('✅ Revenue this week text formatting test passed');
    });
  });

  group('DashboardStatsService Error Handling Tests', () {
    setUpAll(() async {
      // Setup với invalid credentials để test error handling
      final loginSuccess =
          await SimpleApiTestHelper.setupAndLoginTestApiService(
            baseUrl: TestConfig.testBaseUrl,
            username: 'invalid_user',
            password: 'invalid_password',
          );

      print(
        '🔐 Invalid Login Status: ${loginSuccess ? "SUEXPECTED SUCCESS" : "EXPECTED FAILED"}',
      );
    });

    tearDownAll(() async {
      await SimpleApiTestHelper.resetTestEnvironment();
    });

    test('should handle authentication failure gracefully', () async {
      final service = DashboardStatsService();

      expect(
        () => service.getUserBusinessDashboardStats(),
        throwsA(isA<DashboardStatsException>()),
      );

      print('✅ Authentication failure handling test passed');
    });
  });
}
