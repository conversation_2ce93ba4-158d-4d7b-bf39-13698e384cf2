#!/bin/bash

# Script để chạy integration tests với SimpleApiService
# Sử dụng real server data

echo "🧪 Running DashboardStatsService Integration Tests"
echo "=================================================="

# Default test configuration
DEFAULT_BASE_URL="http://************:8190"
DEFAULT_USERNAME="testuser"
DEFAULT_PASSWORD="password123"

# Override với environment variables nếu có
BASE_URL="${TEST_BASE_URL:-$DEFAULT_BASE_URL}"
USERNAME="${TEST_USERNAME:-$DEFAULT_USERNAME}"
PASSWORD="${TEST_PASSWORD:-$DEFAULT_PASSWORD}"

echo "Test Configuration:"
echo "- Base URL: $BASE_URL"
echo "- Username: $USERNAME"
echo "- Password: ${PASSWORD:0:3}***"
echo ""

# Check if server is reachable
echo "🔍 Checking server connectivity..."
if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null; then
    echo "✅ Server is reachable"
else
    echo "❌ Server is not reachable. Please check your network connection."
    echo "   Make sure the server is running at: $BASE_URL"
    exit 1
fi

echo ""
echo "🚀 Starting integration tests..."

# Run the specific test file
flutter test test/features/dashboard/services/dashboard_stats_service_test.dart \
    --dart-define=TEST_BASE_URL="$BASE_URL" \
    --dart-define=TEST_USERNAME="$USERNAME" \
    --dart-define=TEST_PASSWORD="$PASSWORD"

# Check exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ All integration tests passed!"
else
    echo ""
    echo "❌ Some tests failed. Check the output above for details."
    exit 1
fi 