import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:kiloba_biz/core/article/services/article_service.dart';
import 'package:kiloba_biz/core/article/services/article_exception.dart';
import 'package:kiloba_biz/core/article/models/article_model.dart';
import 'package:integration_test/integration_test.dart';

/// Basic integration test để kiểm tra cơ bản
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Basic ArticleService Integration Tests', () {
    late ArticleService articleService;

    setUpAll(() {
      // Reset singleton instance
      ArticleService.resetInstance();
      
      // Khởi tạo service với default dependencies
      articleService = ArticleService();
    });

    tearDownAll(() {
      // Cleanup
      ArticleService.resetInstance();
    });

    test('should get articles and log results', () async {
      try {
        debugPrint('Bắt đầu test lấy danh sách articles...');
        
        final articles = await articleService.getArticles(limit: 1);
        
        debugPrint('Kết quả getArticles: ${articles.length} articles');
        
        if (articles.isNotEmpty) {
          final firstArticle = articles.first;
          debugPrint('Article đầu tiên:');
          debugPrint('  - ID: ${firstArticle.id}');
          debugPrint('  - Title: ${firstArticle.title}');
          debugPrint('  - Type: ${firstArticle.type}');
          debugPrint('  - Status: ${firstArticle.status}');
        } else {
          debugPrint('Không có articles nào được trả về');
        }

        expect(articles, isA<List<ArticleModel>>());
        debugPrint('Test hoàn thành thành công!');
        
      } catch (e) {
        debugPrint('Lỗi khi lấy articles: $e');
        // Expected error nếu API không available
        expect(e, isA<ArticleException>());
      }
    });
  });
} 