# Article Integration Tests

## Tổng quan

Integration tests cho Article module test các tương tác thực tế với backend API thông qua postgREST endpoints. Các test này đảm bảo rằng:

- ✅ Kết nối với backend hoạt động chính xác
- ✅ Response parsing hoạt động đúng
- ✅ Error handling xử lý các trường hợp lỗi
- ✅ Business logic hoạt động với real data
- ✅ Performance đáp ứng yêu cầu

## Cấu trúc Files

```
integration/
├── article_service_integration_test.dart  # Main integration tests
├── integration_test_helpers.dart          # Test helpers và utilities
├── integration_test_config.dart           # Configuration constants
├── run_integration_tests.sh              # Script chạy tests
└── README.md                             # Documentation này
```

## Test Categories

### 1. API Endpoint Tests
- **getArticles()** - Test lấy danh sách bài viết
- **getArticleById()** - Test lấy bài viết theo ID
- **getArticleByCode()** - Test lấy bài viết theo code
- **getFeaturedArticles()** - Test lấy bài viết nổi bật
- **getArticleVersions()** - Test lấy lịch sử phiên bản
- **searchArticlesByTags()** - Test tìm kiếm theo tags

### 2. Parameter Testing
- **Search parameters** - Test tìm kiếm với các từ khóa khác nhau
- **Type filtering** - Test lọc theo loại bài viết
- **Featured filtering** - Test lọc bài viết nổi bật
- **Pagination** - Test phân trang với limit/offset
- **Status filtering** - Test lọc theo trạng thái

### 3. Error Handling
- **Invalid IDs** - Test với ID không tồn tại
- **Invalid codes** - Test với code không tồn tại
- **Network errors** - Test xử lý lỗi mạng
- **Malformed responses** - Test xử lý response không hợp lệ

### 4. Business Logic
- **Displayable articles** - Test logic hiển thị bài viết
- **Date validation** - Test validation ngày tháng
- **Status validation** - Test validation trạng thái
- **Priority sorting** - Test sắp xếp theo độ ưu tiên

### 5. Performance Tests
- **Response time** - Test thời gian phản hồi
- **Concurrent requests** - Test xử lý nhiều request đồng thời
- **Timeout handling** - Test xử lý timeout

## Chạy Tests

### Sử dụng script (Khuyến nghị)
```bash
# Từ thư mục gốc của dự án
./test/core/article/run_integration_tests.sh
```

### Sử dụng Flutter CLI
```bash
# Chạy tất cả integration tests
flutter test test/core/article/integration/

# Chạy với verbose output
flutter test test/core/article/integration/ --verbose

# Chạy với coverage
flutter test test/core/article/integration/ --coverage

# Chạy test cụ thể
flutter test test/core/article/integration/article_service_integration_test.dart
```

## Configuration

### Timeout Settings
```dart
// Trong integration_test_config.dart
static const Duration apiTimeout = Duration(seconds: 30);
static const Duration performanceTimeout = Duration(seconds: 10);
```

### Test Data
```dart
// Search terms cho testing
static const List<String> testSearchTerms = [
  'test', 'news', 'update', 'policy', 'promotion'
];

// Article types cho testing
static const List<String> testArticleTypes = [
  'NEWS', 'POLICY', 'PROMOTION', 'GUIDELINE'
];
```

## Test Helpers

### IntegrationTestHelpers
- `waitForApiResponse()` - Chờ API response với timeout
- `isValidArticle()` - Kiểm tra article hợp lệ
- `isSortedByPriority()` - Kiểm tra sắp xếp theo priority
- `getSampleArticleId()` - Lấy sample article ID
- `isServiceAvailable()` - Kiểm tra service available

### Test Data Creation
- `createTestSearchTerms()` - Tạo search terms cho testing
- `createTestTagGroups()` - Tạo tag groups cho testing

## Best Practices

### 1. Test Setup
- Reset singleton instances trước mỗi test
- Sử dụng real dependencies (ApiService, AppLogger)
- Cleanup sau khi test xong

### 2. Test Structure
- Arrange-Act-Assert pattern
- Descriptive test names
- Proper error handling expectations

### 3. Performance Considerations
- Set reasonable timeouts
- Test concurrent scenarios
- Monitor response times

### 4. Error Testing
- Test với invalid data
- Test network error scenarios
- Test malformed responses

## Troubleshooting

### Common Issues

1. **Timeout Errors**
   - Kiểm tra kết nối mạng
   - Tăng timeout duration
   - Kiểm tra backend availability

2. **API Errors**
   - Kiểm tra backend status
   - Verify API endpoints
   - Check authentication

3. **Test Failures**
   - Kiểm tra test data
   - Verify business logic
   - Check response format

### Debug Mode
```bash
# Chạy với debug output
flutter test test/core/article/integration/ --verbose --reporter=expanded
```

## Coverage Report

Integration tests cung cấp coverage cho:
- ✅ API endpoint interactions
- ✅ Response parsing
- ✅ Error handling paths
- ✅ Business logic validation
- ✅ Performance scenarios

## Continuous Integration

Integration tests nên được chạy trong CI/CD pipeline:
- Trước khi merge code
- Trong staging environment
- Với real backend endpoints

## Maintenance

### Regular Tasks
- Update test data khi API thay đổi
- Review timeout settings
- Monitor test performance
- Update documentation

### Test Data Management
- Keep test data realistic
- Update invalid test cases
- Maintain test coverage 