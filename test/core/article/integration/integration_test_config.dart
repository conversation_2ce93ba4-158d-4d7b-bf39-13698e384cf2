/// Configuration cho integration tests
class IntegrationTestConfig {
  /// Timeout cho API calls
  static const Duration apiTimeout = Duration(seconds: 30);
  
  /// Timeout cho performance tests
  static const Duration performanceTimeout = Duration(seconds: 10);
  
  /// Số lượng concurrent requests cho performance test
  static const int concurrentRequestCount = 5;
  
  /// Limit mặc định cho pagination tests
  static const int defaultLimit = 20;
  
  /// Offset mặc định cho pagination tests
  static const int defaultOffset = 0;
  
  /// Search terms cho testing
  static const List<String> testSearchTerms = [
    'test',
    'news',
    'update',
    'policy',
    'promotion',
  ];
  
  /// Article types cho testing
  static const List<String> testArticleTypes = [
    'NEWS',
    'POLICY',
    'PROMOTION',
    'GUIDELINE',
  ];
  
  /// Tags cho testing
  static const List<List<String>> testTagGroups = [
    ['news', 'update'],
    ['policy', 'guideline'],
    ['promotion', 'training'],
  ];
  
  /// Invalid IDs cho error testing
  static const List<String> invalidIds = [
    'invalid-id-12345',
    'non-existent-id',
    'test-invalid-id',
  ];
  
  /// Invalid codes cho error testing
  static const List<String> invalidCodes = [
    'INVALID-CODE-12345',
    'NON-EXISTENT-CODE',
    'TEST-INVALID-CODE',
  ];
  
  /// Test data cho pagination
  static const Map<String, int> paginationTests = {
    'small_limit': 5,
    'medium_limit': 10,
    'large_limit': 50,
  };
  
  /// Test data cho search
  static const Map<String, String> searchTests = {
    'short_term': 'test',
    'medium_term': 'news update',
    'long_term': 'policy guideline promotion',
  };
} 