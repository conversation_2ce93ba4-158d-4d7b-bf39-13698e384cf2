import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'dart:io';
import 'package:integration_test/integration_test.dart';
/// Test để kiểm tra network connectivity
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Network Connectivity Test', () {
    Dio? dio;
    http.Client? httpClient;

    setUpAll(() {
      dio = Dio(BaseOptions(
        baseUrl: 'http://************:8190',
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ));
      
      httpClient = http.Client();
    });

    tearDownAll(() {
      dio?.close();
      httpClient?.close();
    });

    test('Test basic network connectivity with Socket', () async {
      print('🔍 Bắt đầu test network connectivity với Socket...');
      
      try {
        print('📡 Thử kết nối <PERSON>cket đến ************:8190...');
        
        final socket = await Socket.connect('************', 8190, timeout: const Duration(seconds: 5));
        print('✅ Socket connection thành công!');
        print('   - Local address: ${socket.address}');
        print('   - Local port: ${socket.port}');
        print('   - Remote address: ${socket.remoteAddress}');
        print('   - Remote port: ${socket.remotePort}');
        
        await socket.close();
        print('✅ Socket đã đóng thành công');
        
      } catch (e) {
        print('❌ Socket connection thất bại: $e');
        print('   - Exception type: ${e.runtimeType}');
      }
      
      print('🔍 Kết thúc test Socket connectivity');
    });

    test('Test HTTP connectivity with http package', () async {
      print('🔍 Bắt đầu test HTTP connectivity với http package...');
      
      try {
        print('📡 Thử HTTP GET đến http://************:8190/rest/...');
        
        final response = await httpClient!.get(
          Uri.parse('http://************:8190/rest/'),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        );
        
        print('✅ HTTP request thành công!');
        print('   - Status code: ${response.statusCode}');
        print('   - Response headers: ${response.headers}');
        print('   - Response body length: ${response.body.length}');
        
        if (response.statusCode == 200) {
          print('   - ✅ Server respond thành công!');
        } else {
          print('   - ⚠️ Server respond với status: ${response.statusCode}');
          print('   - Response body: ${response.body}');
        }
        
      } catch (e) {
        print('❌ HTTP request thất bại: $e');
        print('   - Exception type: ${e.runtimeType}');
      }
      
      print('🔍 Kết thúc test HTTP connectivity');
    });

    test('Test POST request with http package', () async {
      print('🔍 Bắt đầu test POST request với http package...');
      
      try {
        print('📡 Thử POST đến http://************:8190/rest/rpc/get_articles...');
        
        final response = await httpClient!.post(
          Uri.parse('http://************:8190/rest/rpc/get_articles'),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          body: '{}',
        );
        
        print('✅ POST request thành công!');
        print('   - Status code: ${response.statusCode}');
        print('   - Response headers: ${response.headers}');
        print('   - Response body length: ${response.body.length}');
        
        if (response.statusCode == 200) {
          print('   - ✅ API hoạt động với http package!');
          print('   - Response preview: ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');
        } else {
          print('   - ⚠️ API respond với status: ${response.statusCode}');
          print('   - Response body: ${response.body}');
        }
        
      } catch (e) {
        print('❌ POST request thất bại: $e');
        print('   - Exception type: ${e.runtimeType}');
      }
      
      print('🔍 Kết thúc test POST request');
    });

    test('Test Dio vs HTTP package comparison', () async {
      print('🔍 Bắt đầu so sánh Dio vs HTTP package...');
      
      // Test 1: HTTP package
      try {
        print('📡 Testing với HTTP package...');
        final httpResponse = await httpClient!.post(
          Uri.parse('http://************:8190/rest/rpc/get_articles'),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          body: '{}',
        );
        
        print('   - HTTP package status: ${httpResponse.statusCode}');
        print('   - HTTP package body length: ${httpResponse.body.length}');
        
      } catch (e) {
        print('   - HTTP package error: ${e.toString().split('\n').first}');
      }
      
      // Test 2: Dio
      try {
        print('📡 Testing với Dio...');
        final dioResponse = await dio!.post(
          '/rest/rpc/get_articles',
          data: {},
        );
        
        print('   - Dio status: ${dioResponse.statusCode}');
        print('   - Dio data type: ${dioResponse.data.runtimeType}');
        
      } catch (e) {
        print('   - Dio error: ${e.toString().split('\n').first}');
        if (e is DioException) {
          print('     * DioException type: ${e.type}');
          print('     * Status code: ${e.response?.statusCode}');
        }
      }
      
      print('🔍 Kết thúc so sánh Dio vs HTTP package');
    });

    test('Test different network configurations', () async {
      print('🔍 Bắt đầu test các cấu hình network khác nhau...');
      
      final testUrls = [
        'http://************:8190/rest/rpc/get_articles',
        'http://localhost:8190/rest/rpc/get_articles',
        'http://127.0.0.1:8190/rest/rpc/get_articles',
      ];
      
      for (final url in testUrls) {
        try {
          print('📡 Testing: $url');
          
          final response = await httpClient!.post(
            Uri.parse(url),
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
            },
            body: '{}',
          );
          
          print('   - Status: ${response.statusCode}');
          print('   - Body length: ${response.body.length}');
          
          if (response.statusCode == 200) {
            print('   - ✅ Success!');
          } else {
            print('   - ⚠️ Status: ${response.statusCode}');
          }
          
        } catch (e) {
          print('   - ❌ Error: ${e.toString().split('\n').first}');
        }
      }
      
      print('🔍 Kết thúc test network configurations');
    });

    test('Test environment information', () async {
      print('🔍 Bắt đầu test environment information...');
      
      try {
        // Test 1: Kiểm tra localhost
        print('📡 Testing localhost connectivity...');
        final localhostResponse = await httpClient!.get(
          Uri.parse('http://localhost:8190/rest/'),
          headers: {'Accept': 'application/json'},
        );
        print('   - Localhost status: ${localhostResponse.statusCode}');
        
      } catch (e) {
        print('   - Localhost error: ${e.toString().split('\n').first}');
      }
      
      try {
        // Test 2: Kiểm tra 127.0.0.1
        print('📡 Testing 127.0.0.1 connectivity...');
        final loopbackResponse = await httpClient!.get(
          Uri.parse('http://127.0.0.1:8190/rest/'),
          headers: {'Accept': 'application/json'},
        );
        print('   - 127.0.0.1 status: ${loopbackResponse.statusCode}');
        
      } catch (e) {
        print('   - 127.0.0.1 error: ${e.toString().split('\n').first}');
      }
      
      try {
        // Test 3: Kiểm tra external IP
        print('📡 Testing external IP connectivity...');
        final externalResponse = await httpClient!.get(
          Uri.parse('http://************:8190/rest/'),
          headers: {'Accept': 'application/json'},
        );
        print('   - External IP status: ${externalResponse.statusCode}');
        
      } catch (e) {
        print('   - External IP error: ${e.toString().split('\n').first}');
      }
      
      print('🔍 Kết thúc test environment information');
    });
  });
} 