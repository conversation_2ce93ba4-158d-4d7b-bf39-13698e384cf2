import 'package:kiloba_biz/core/article/models/article_model.dart';
import 'package:kiloba_biz/core/article/services/article_service.dart';

/// Test helpers cho integration tests
class IntegrationTestHelpers {
  /// Chờ cho API response với timeout
  static Future<T> waitForApiResponse<T>(
    Future<T> Function() apiCall, {
    Duration timeout = const Duration(seconds: 30),
  }) async {
    return await apiCall().timeout(
      timeout,
      onTimeout: () {
        throw TimeoutException('API call timed out after ${timeout.inSeconds} seconds');
      },
    );
  }

  /// Kiểm tra xem article có hợp lệ không
  static bool isValidArticle(ArticleModel article) {
    return article.id.isNotEmpty &&
           article.code.isNotEmpty &&
           article.title.isNotEmpty &&
           article.createdAt.isBefore(DateTime.now()) &&
           article.updatedAt.isBefore(DateTime.now());
  }

  /// Kiểm tra xem articles có được sắp xếp theo priority không
  static bool isSortedByPriority(List<ArticleModel> articles) {
    for (int i = 0; i < articles.length - 1; i++) {
      if (articles[i].priority < articles[i + 1].priority) {
        return false;
      }
    }
    return true;
  }

  /// Kiểm tra xem articles có được sắp xếp theo published date không
  static bool isSortedByPublishedDate(List<ArticleModel> articles) {
    for (int i = 0; i < articles.length - 1; i++) {
      final currentDate = articles[i].displayDate;
      final nextDate = articles[i + 1].displayDate;
      if (currentDate.isBefore(nextDate)) {
        return false;
      }
    }
    return true;
  }

  /// Lấy sample article ID từ service
  static Future<String?> getSampleArticleId(ArticleService service) async {
    try {
      final articles = await service.getArticles(limit: 1);
      if (articles.isNotEmpty) {
        return articles.first.id;
      }
    } catch (e) {
      // Ignore errors for test setup
    }
    return null;
  }

  /// Lấy sample article code từ service
  static Future<String?> getSampleArticleCode(ArticleService service) async {
    try {
      final articles = await service.getArticles(limit: 1);
      if (articles.isNotEmpty) {
        return articles.first.code;
      }
    } catch (e) {
      // Ignore errors for test setup
    }
    return null;
  }

  /// Kiểm tra xem service có available không
  static Future<bool> isServiceAvailable(ArticleService service) async {
    try {
      return await service.checkArticleApiAvailability();
    } catch (e) {
      return false;
    }
  }

  /// Tạo test data cho performance testing
  static List<String> createTestSearchTerms() {
    return [
      'test',
      'news',
      'update',
      'policy',
      'promotion',
      'training',
      'guideline',
      'announcement',
    ];
  }

  /// Tạo test tags cho search testing
  static List<List<String>> createTestTagGroups() {
    return [
      ['news', 'update'],
      ['policy', 'guideline'],
      ['promotion', 'training'],
      ['announcement', 'competition'],
      ['product', 'intro'],
      ['terms', 'privacy'],
    ];
  }
}

/// Custom exception cho timeout
class TimeoutException implements Exception {
  final String message;
  TimeoutException(this.message);

  @override
  String toString() => 'TimeoutException: $message';
} 