#!/bin/bash

# Script để chạy integration tests cho article module
# Sử dụng: ./run_integration_tests.sh

echo "🚀 Bắt đầu chạy Article Integration Tests..."

# Kiểm tra xem có đang ở đúng thư mục không
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Lỗi: Không tìm thấy pubspec.yaml. Hãy chạy script này từ thư mục gốc của dự án."
    exit 1
fi

# Kiểm tra xem Flutter có được cài đặt không
if ! command -v flutter &> /dev/null; then
    echo "❌ Lỗi: Flutter không được cài đặt hoặc không có trong PATH."
    exit 1
fi

echo "📋 Kiểm tra dependencies..."
flutter pub get

echo "🧪 Chạy integration tests..."
flutter test test/core/article/integration/ --verbose

# Kiểm tra exit code
if [ $? -eq 0 ]; then
    echo "✅ Integration tests đã pass thành công!"
else
    echo "❌ Integration tests đã fail!"
    exit 1
fi

echo "📊 Chạy test coverage..."
flutter test test/core/article/integration/ --coverage

echo "🎉 Hoàn thành integration tests!" 