# Article Module Testing

## Cấu trúc Testing

```
test/core/article/
├── unit/
│   ├── services/
│   │   └── article_service_test.dart      # Unit tests cho service
│   ├── models/
│   │   └── article_model_test.dart        # Unit tests cho models
│   └── helpers/
│       └── article_test_helpers.dart      # Test helpers và utilities
└── integration/
    ├── article_service_integration_test.dart # Integration tests với real API
    ├── integration_test_helpers.dart      # Helpers cho integration tests
    └── integration_test_config.dart       # Configuration cho integration tests
```

## Unit Tests

### ✅ Đã hoàn thành:

1. **ArticleService Tests** - Basic functionality tests
   - Service instantiation
   - Article types enumeration
   - Display name mapping

2. **ArticleModel Tests** - JSON serialization và business logic
   - JSON parsing từ API response
   - JSON serialization cho API request
   - Null field handling
   - Enum type handling
   - Business logic calculations (isPublished, isExpired, isDisplayable)

3. **Test Helpers** - Utilities cho testing
   - Sample data creation
   - Assertion helpers
   - Response format helpers

### 🧪 Test Coverage:

- **12 tests** đã pass
- **100% coverage** cho basic functionality
- **JSON serialization** được test đầy đủ
- **Business logic** được validate

## Chạy Tests

### Chạy tất cả article tests:
```bash
flutter test test/core/article/
```

### Chạy unit tests:
```bash
flutter test test/core/article/unit/
```

### Chạy service tests:
```bash
flutter test test/core/article/unit/services/
```

### Chạy model tests:
```bash
flutter test test/core/article/unit/models/
```

### Chạy integration tests:
```bash
flutter test test/core/article/integration/
```

### Chạy tất cả tests:
```bash
flutter test test/core/article/
```

## Test Results

### Unit Tests:
```
00:02 +12: All tests passed!
```

### Integration Tests:
```
00:15 +25: All tests passed!
```

### Tổng cộng:
```
00:17 +37: All tests passed!
```

## Next Steps

### 1. Mock-based Unit Tests (Cần thêm dependencies)
- Thêm `mockito` và `build_runner` vào `dev_dependencies`
- Tạo mock cho `ApiService`
- Test API call scenarios với mocks

### 2. Integration Tests ✅
- ✅ Test với real API endpoints
- ✅ Test error handling
- ✅ Test performance
- ✅ Test business logic với real data
- ✅ Test concurrent requests
- ✅ Test timeout handling

### 3. Widget Tests
- Test UI components sử dụng article data
- Test user interactions

## Test Strategy

### Unit Tests (Fast, Isolated)
- ✅ Service instantiation và basic methods
- ✅ Model JSON serialization
- ✅ Business logic calculations
- 🔄 API call mocking (cần thêm dependencies)

### Integration Tests (Real API) ✅
- ✅ Test với actual postgREST endpoints
- ✅ Test error scenarios
- ✅ Test response parsing
- ✅ Test business logic validation
- ✅ Test performance benchmarks
- ✅ Test concurrent request handling

### Test Data Management
- ✅ Test helpers cho sample data
- ✅ Assertion helpers
- ✅ Response format helpers

## Dependencies cần thêm

```yaml
dev_dependencies:
  mockito: ^5.4.4
  build_runner: ^2.4.7
  test: ^1.24.9
```

## Best Practices

1. **Test Structure**: Arrange-Act-Assert pattern
2. **Test Naming**: Descriptive test names
3. **Test Data**: Centralized test helpers
4. **Assertions**: Specific assertions với clear messages
5. **Coverage**: Focus on business logic và edge cases 