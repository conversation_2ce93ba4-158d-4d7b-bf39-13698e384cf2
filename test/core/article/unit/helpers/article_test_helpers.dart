import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/core/article/models/article_model.dart';
import 'package:kiloba_biz/core/article/models/article_version_model.dart';
import 'package:kiloba_biz/core/article/models/article_search_params.dart';

/// Test helpers cho article module
class ArticleTestHelpers {
  /// Tạo sample article JSON
  static Map<String, dynamic> createSampleArticleJson({
    String? id,
    String? code,
    String? title,
    ArticleType? type,
    bool? isFeatured,
  }) {
    return {
      'id': id ?? 'test-id',
      'code': code ?? 'TEST-001',
      'title': title ?? 'Test Article',
      'summary': 'Test summary',
      'content': 'Test content',
      'type': (type ?? ArticleType.news).name.toUpperCase(),
      'status': 'PUBLISHED',
      'published_at': '2024-01-01T00:00:00.000Z',
      'expiry_at': null,
      'priority': 1,
      'view_count': 100,
      'is_featured': isFeatured ?? false,
      'thumbnail_url': 'https://example.com/image.jpg',
      'tags': ['test', 'news'],
      'target_audience': 'ALL',
      'created_at': '2024-01-01T00:00:00.000Z',
      'updated_at': '2024-01-01T00:00:00.000Z',
      'created_by': 'test-user',
      'updated_by': 'test-user',
    };
  }

  /// Tạo sample article model
  static ArticleModel createSampleArticle({
    String? id,
    String? code,
    String? title,
    ArticleType? type,
    bool? isFeatured,
  }) {
    final now = DateTime(2024, 1, 1);
    return ArticleModel(
      id: id ?? 'test-id',
      code: code ?? 'TEST-001',
      title: title ?? 'Test Article',
      summary: 'Test summary',
      content: 'Test content',
      type: type ?? ArticleType.news,
      status: 'PUBLISHED',
      publishedAt: now,
      expiryAt: null,
      priority: 1,
      viewCount: 100,
      isFeatured: isFeatured ?? false,
      thumbnailUrl: 'https://example.com/image.jpg',
      tags: ['test', 'news'],
      targetAudience: 'ALL',
      createdAt: now,
      updatedAt: now,
      createdBy: 'test-user',
      updatedBy: 'test-user',
    );
  }

  /// Tạo list sample articles
  static List<ArticleModel> createSampleArticleList(int count) {
    return List.generate(count, (index) {
      return createSampleArticle(
        id: 'test-id-$index',
        code: 'TEST-${index.toString().padLeft(3, '0')}',
        title: 'Test Article $index',
        type: ArticleType.values[index % ArticleType.values.length],
        isFeatured: index % 2 == 0,
      );
    });
  }

  /// Tạo sample article version model
  static ArticleVersionModel createSampleVersion({
    int? version,
    String? title,
    ArticleType? type,
  }) {
    return ArticleVersionModel(
      version: version ?? 1,
      changedAt: DateTime(2024, 1, 1),
      changedBy: 'test-user',
      changeReason: 'Test change',
      title: title ?? 'Test Article',
      summary: 'Test summary',
      type: type ?? ArticleType.news,
      status: 'PUBLISHED',
    );
  }

  /// Tạo list sample article versions
  static List<ArticleVersionModel> createSampleArticleVersionList(int count) {
    return List.generate(count, (index) {
      return createSampleVersion(
        version: index + 1,
        title: 'Test Article v${index + 1}',
        type: ArticleType.values[index % ArticleType.values.length],
      );
    });
  }

  /// Tạo sample search params
  static ArticleSearchParams createSampleSearchParams({
    String? search,
    ArticleType? articleType,
    bool? isFeatured,
    int? limit,
    int? offset,
  }) {
    return ArticleSearchParams(
      search: search,
      articleType: articleType,
      isFeatured: isFeatured,
      limit: limit,
      offset: offset,
    );
  }

  /// Tạo API response format cho success
  static Map<String, dynamic> createSuccessResponse(dynamic data) {
    return {
      'success': true,
      'message': 'Success',
      'data': data,
    };
  }

  /// Tạo API response format cho error
  static Map<String, dynamic> createErrorResponse({
    String? message,
    String? code,
  }) {
    return {
      'success': false,
      'message': message ?? 'Error occurred',
      'code': code,
      'data': null,
    };
  }

  /// Assert article equals
  static void assertArticleEquals(ArticleModel expected, ArticleModel actual) {
    expect(actual.id, equals(expected.id));
    expect(actual.code, equals(expected.code));
    expect(actual.title, equals(expected.title));
    expect(actual.type, equals(expected.type));
    expect(actual.status, equals(expected.status));
    expect(actual.isFeatured, equals(expected.isFeatured));
  }

  /// Assert article list equals
  static void assertArticleListEquals(
    List<ArticleModel> expected,
    List<ArticleModel> actual,
  ) {
    expect(actual.length, equals(expected.length));
    for (int i = 0; i < expected.length; i++) {
      assertArticleEquals(expected[i], actual[i]);
    }
  }
} 