import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/core/article/models/article_model.dart';
import '../helpers/article_test_helpers.dart';

void main() {
  group('ArticleModel Tests', () {
    group('JSON Serialization', () {
      test('should handle all article types correctly', () {
        final typeMappings = [
          ('PRODUCT_INTRO', ArticleType.productIntro),
          ('POLICY', ArticleType.policy),
          ('PROMOTION', ArticleType.promotion),
          ('COMPETITION', ArticleType.competition),
          ('NEWS', ArticleType.news),
          ('GUIDELINE', ArticleType.guideline),
          ('ANNOUNCEMENT', ArticleType.announcement),
          ('TRAINING', ArticleType.training),
          ('TERMS_OF_SERVICE', ArticleType.termsOfService),
          ('PRIVACY_POLICY', ArticleType.privacyPolicy),
        ];

        for (final (jsonValue, expectedType) in typeMappings) {
          final json = {
            'id': 'test-id',
            'code': 'TEST-001',
            'title': 'Test Article',
            'type': jsonValue,
            'status': 'PUBLISHED',
            'priority': 1,
            'view_count': 100,
            'is_featured': false,
            'published_at': '2024-01-01T00:00:00.000Z',
            'created_at': '2024-01-01T00:00:00.000Z',
            'updated_at': '2024-01-01T00:00:00.000Z',
          };

          final article = ArticleModel.fromJson(json);
          expect(article.type, equals(expectedType));
        }
      });
    });

    group('Business Logic', () {
      test('should calculate isPublished correctly', () {
        final publishedArticle = ArticleModel(
          id: '1',
          code: 'TEST-001',
          title: 'Published Article',
          type: ArticleType.news,
          status: 'PUBLISHED',
          priority: 1,
          viewCount: 100,
          isFeatured: false,
          publishedAt: DateTime.now().subtract(Duration(days: 1)),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final draftArticle = ArticleModel(
          id: '2',
          code: 'TEST-002',
          title: 'Draft Article',
          type: ArticleType.news,
          status: 'DRAFT',
          priority: 1,
          viewCount: 0,
          isFeatured: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(publishedArticle.isPublished, isTrue);
        expect(draftArticle.isPublished, isFalse);
      });

      test('should calculate isDisplayable correctly', () {
        final now = DateTime.now();
        final displayableArticle = ArticleModel(
          id: '1',
          code: 'TEST-001',
          title: 'Displayable Article',
          type: ArticleType.news,
          status: 'PUBLISHED',
          priority: 1,
          viewCount: 100,
          isFeatured: false,
          publishedAt: now.subtract(Duration(days: 1)),
          createdAt: now,
          updatedAt: now,
        );

        final notDisplayableArticle = ArticleModel(
          id: '2',
          code: 'TEST-002',
          title: 'Not Displayable Article',
          type: ArticleType.news,
          status: 'DRAFT',
          priority: 1,
          viewCount: 0,
          isFeatured: false,
          createdAt: now,
          updatedAt: now,
        );

        final futureArticle = ArticleModel(
          id: '3',
          code: 'TEST-003',
          title: 'Future Article',
          type: ArticleType.news,
          status: 'PUBLISHED',
          priority: 1,
          viewCount: 0,
          isFeatured: false,
          publishedAt: now.add(Duration(days: 1)),
          createdAt: now,
          updatedAt: now,
        );

        expect(displayableArticle.isDisplayable, isTrue);
        expect(notDisplayableArticle.isDisplayable, isFalse);
        expect(futureArticle.isDisplayable, isFalse);
      });

      test('should calculate displayDate correctly', () {
        final now = DateTime.now();
        final publishedDate = now.subtract(Duration(days: 5));

        final articleWithPublishedDate = ArticleModel(
          id: 'test-id',
          code: 'TEST-001',
          title: 'Test Article',
          type: ArticleType.news,
          status: 'PUBLISHED',
          priority: 1,
          viewCount: 100,
          isFeatured: false,
          publishedAt: publishedDate,
          createdAt: now,
          updatedAt: now,
        );

        final articleWithoutPublishedDate = ArticleModel(
          id: 'test-id',
          code: 'TEST-001',
          title: 'Test Article',
          type: ArticleType.news,
          status: 'PUBLISHED',
          priority: 1,
          viewCount: 100,
          isFeatured: false,
          createdAt: now,
          updatedAt: now,
        );

        expect(articleWithPublishedDate.displayDate, equals(publishedDate));
        expect(articleWithoutPublishedDate.displayDate, equals(now));
      });
    });

    group('Copy With', () {
      test('should create copy with updated properties', () {
        final original = ArticleTestHelpers.createSampleArticle();
        final copy = original.copyWith(
          title: 'Updated Title',
          isFeatured: true,
          viewCount: 200,
        );

        expect(copy.id, equals(original.id));
        expect(copy.code, equals(original.code));
        expect(copy.title, equals('Updated Title'));
        expect(copy.isFeatured, isTrue);
        expect(copy.viewCount, equals(200));
        expect(copy.type, equals(original.type));
        expect(copy.status, equals(original.status));
      });
    });
  });
} 