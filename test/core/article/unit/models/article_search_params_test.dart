import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/core/article/models/article_search_params.dart';
import 'package:kiloba_biz/core/article/models/article_model.dart';

void main() {
  group('ArticleSearchParams Tests', () {
    group('Factory Constructors', () {
      test('should create search params with default constructor', () {
        final params = ArticleSearchParams();

        expect(params.search, isNull);
        expect(params.articleType, isNull);
        expect(params.status, equals('PUBLISHED'));
        expect(params.isFeatured, isNull);
        expect(params.limit, equals(20));
        expect(params.offset, equals(0));
        expect(params.tags, isNull);
      });

      test('should create search params for featured articles', () {
        final params = ArticleSearchParams.featured(limit: 10);

        expect(params.search, isNull);
        expect(params.articleType, isNull);
        expect(params.status, equals('PUBLISHED'));
        expect(params.isFeatured, isTrue);
        expect(params.limit, equals(10));
        expect(params.offset, equals(0));
        expect(params.tags, isNull);
      });

      test('should create search params for basic search', () {
        final params = ArticleSearchParams.basic(
          search: 'test query',
          articleType: ArticleType.news,
          limit: 15,
          offset: 5,
        );

        expect(params.search, equals('test query'));
        expect(params.articleType, equals(ArticleType.news));
        expect(params.status, equals('PUBLISHED'));
        expect(params.isFeatured, isNull);
        expect(params.limit, equals(15));
        expect(params.offset, equals(5));
        expect(params.tags, isNull);
      });

      test('should create search params for tags search', () {
        final params = ArticleSearchParams.byTags(
          tags: ['test', 'news'],
          limit: 25,
          offset: 10,
        );

        expect(params.search, isNull);
        expect(params.articleType, isNull);
        expect(params.status, equals('PUBLISHED'));
        expect(params.isFeatured, isNull);
        expect(params.limit, equals(25));
        expect(params.offset, equals(10));
        expect(params.tags, equals(['test', 'news']));
      });

      test('should create search params with custom filters', () {
        final params = ArticleSearchParams(
          search: 'custom search',
          articleType: ArticleType.training,
          status: 'DRAFT',
          isFeatured: true,
          limit: 30,
          offset: 10,
          tags: ['custom'],
        );

        expect(params.search, equals('custom search'));
        expect(params.articleType, equals(ArticleType.training));
        expect(params.status, equals('DRAFT'));
        expect(params.isFeatured, isTrue);
        expect(params.limit, equals(30));
        expect(params.offset, equals(10));
        expect(params.tags, equals(['custom']));
      });
    });

    group('toMap Method', () {
      test('should convert to map with null parameters', () {
        final params = ArticleSearchParams(
          search: null,
          articleType: null,
          status: null,
          isFeatured: null,
          limit: 20,
          offset: 0,
        );

        final map = params.toMap();

        expect(map['p_search'], isNull);
        expect(map['p_article_type'], isNull);
        expect(map['p_status'], isNull);
        expect(map['p_is_featured'], isNull);
        expect(map['p_limit'], equals(20));
        expect(map['p_offset'], equals(0));
      });

      test('should use default values when not specified', () {
        final params = ArticleSearchParams();

        final map = params.toMap();

        expect(map['p_status'], equals('PUBLISHED'));
        expect(map['p_limit'], equals(20));
        expect(map['p_offset'], equals(0));
      });
    });

    group('Copy With', () {
      test('should create copy with updated properties', () {
        final original = ArticleSearchParams(
          search: 'original',
          articleType: ArticleType.news,
          limit: 10,
        );

        final copy = original.copyWith(
          search: 'updated',
          limit: 20,
          isFeatured: true,
        );

        expect(copy.search, equals('updated'));
        expect(copy.articleType, equals(ArticleType.news));
        expect(copy.limit, equals(20));
        expect(copy.isFeatured, isTrue);
        expect(copy.offset, equals(original.offset));
        expect(copy.status, equals(original.status));
      });
    });
  });
} 