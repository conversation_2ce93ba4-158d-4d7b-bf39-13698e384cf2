import 'package:kiloba_biz/shared/services/api/simple_api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/utils/simple_app_logger.dart';

/// Helper class để setup SimpleApiService cho testing
class SimpleApiTestHelper {
  /// Setup test environment với SimpleApiService
  static Future<void> setupTestApiService({
    required String baseUrl,
    required String username,
    required String password,
    IAppLogger? logger,
  }) async {
    // Reset service locator
    await ServiceLocator.reset();

    // Create và configure SimpleApiService
    final simpleApiService = SimpleApiService();
    simpleApiService.configure(
      baseUrl: baseUrl,
      username: username,
      password: password,
      logger: logger ?? SimpleAppLogger(),
    );

    // Register vào get_it
    getIt.registerLazySingleton<IApiService>(() => simpleApiService);

    // Register logger
    getIt.registerLazySingleton<IAppLogger>(() => logger ?? SimpleAppLogger());

    // Register SimpleAppLogger for direct access
    getIt.registerLazySingleton<SimpleAppLogger>(() => SimpleAppLogger());
  }

  /// Setup test environment với default test credentials
  static Future<void> setupDefaultTestApiService() async {
    await setupTestApiService(
      baseUrl: 'http://192.168.5.39:8190',
      username: 'testuser',
      password: 'password123',
    );
  }

  /// Setup và login với test credentials
  static Future<bool> setupAndLoginTestApiService({
    required String baseUrl,
    required String username,
    required String password,
    IAppLogger? logger,
  }) async {
    await setupTestApiService(
      baseUrl: baseUrl,
      username: username,
      password: password,
      logger: logger,
    );

    // Login để lấy token
    final simpleApiService = getIt.get<IApiService>() as SimpleApiService;
    return await simpleApiService.login();
  }

  /// Setup và login với default test credentials
  static Future<bool> setupAndLoginDefaultTestApiService() async {
    return await setupAndLoginTestApiService(
      baseUrl: 'http://192.168.5.39:8190',
      username: 'testuser',
      password: 'password123',
    );
  }

  /// Reset test environment
  static Future<void> resetTestEnvironment() async {
    await ServiceLocator.reset();
  }

  /// Check if test environment is ready
  static bool isTestEnvironmentReady() {
    return getIt.isRegistered<IApiService>() &&
        getIt.isRegistered<IAppLogger>();
  }
}
