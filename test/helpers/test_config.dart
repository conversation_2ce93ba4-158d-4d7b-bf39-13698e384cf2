/// Configuration cho testing environment
class TestConfig {
  /// Test server base URL
  static const String testBaseUrl = String.fromEnvironment(
    'TEST_BASE_URL',
    defaultValue: 'http://192.168.2.61:8190',
  );

  /// Test username
  static const String testUsername = String.fromEnvironment(
    'TEST_USERNAME',
    defaultValue: 'testuser',
  );

  /// Test password
  static const String testPassword = String.fromEnvironment(
    'TEST_PASSWORD',
    defaultValue: 'password123',
  );

  /// Test timeout duration
  static const Duration testTimeout = Duration(seconds: 30);

  /// Check if running in test environment
  static bool get isTestEnvironment {
    return testBaseUrl.isNotEmpty &&
        testUsername.isNotEmpty &&
        testPassword.isNotEmpty;
  }

  /// Get test configuration summary
  static String get configurationSummary {
    return '''
Test Configuration:
- Base URL: $testBaseUrl
- Username: $testUsername
- Password: ${testPassword.isNotEmpty ? '***' : 'NOT SET'}
- Timeout: ${testTimeout.inSeconds}s
- Environment Ready: $isTestEnvironment
''';
  }
}
