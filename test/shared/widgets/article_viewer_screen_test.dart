import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/widgets/article_viewer_screen.dart';
import 'package:kiloba_biz/shared/constants/article_codes.dart';

void main() {
  group('ArticleViewerScreen', () {
    testWidgets('should display loading state initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ArticleViewerScreen(
            articleCode: ArticleCodes.termsOfServiceRegisterV1,
            title: '<PERSON><PERSON><PERSON>u khoản sử dụng',
          ),
        ),
      );

      expect(find.text('Đang tải nội dung...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display correct title in app bar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ArticleViewerScreen(
            articleCode: ArticleCodes.privacyPolicyRegisterV1,
            title: '<PERSON><PERSON><PERSON> s<PERSON>ch bảo mật',
          ),
        ),
      );

      expect(find.text('<PERSON><PERSON><PERSON> sách bảo mật'), findsOneWidget);
    });

    testWidgets('should have back button in app bar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ArticleViewerScreen(
            articleCode: ArticleCodes.termsOfServiceRegisterV1,
            title: 'Test',
          ),
        ),
      );

      expect(find.byType(IconButton), findsOneWidget);
    });
  });
} 