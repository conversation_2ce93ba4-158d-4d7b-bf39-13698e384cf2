import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';

void main() {
  test('Simple Dio test - check if <PERSON><PERSON> can make a real HTTP request', () async {
    // Tạo một instance Dio đơn giản
    final dio = Dio();

    try {
      // Test với một API đơn giản
      final response = await dio.get('https://httpbin.org/ip');

      // Kiểm tra response cơ bản
      expect(response.statusCode, equals(200));
      expect(response.data, isNotNull);
      expect(response.data, isA<Map<String, dynamic>>());

      // In ra kết quả để xem
      print('✅ Dio hoạt động bình thường!');
      print('Status Code: ${response.statusCode}');
      print('Response Data: ${response.data}');
    } catch (e) {
      fail('❌ Dio test failed: $e');
    } finally {
      // Đóng Dio để giải phóng tài nguyên
      dio.close();
    }
  });
}
