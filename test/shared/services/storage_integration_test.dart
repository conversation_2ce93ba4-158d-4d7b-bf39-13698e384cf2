import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/services/storage/storage_service_interface.dart';
import 'package:kiloba_biz/shared/services/storage/shared_preferences_storage_service.dart';

void main() {
  group('Storage Service Integration Tests', () {
    test('should create SharedPreferencesStorageService instance', () {
      // Act
      final storageService = SharedPreferencesStorageService();

      // Assert
      expect(storageService, isA<IStorageService>());
      expect(storageService, isA<SharedPreferencesStorageService>());
    });

    test('should have all required methods', () {
      // Arrange
      final storageService = SharedPreferencesStorageService();

      // Assert - kiểm tra tất cả methods có tồn tại
      expect(storageService.init, isA<Function>());
      expect(storageService.setString, isA<Function>());
      expect(storageService.getString, isA<Function>());
      expect(storageService.setBool, isA<Function>());
      expect(storageService.getBool, isA<Function>());
      expect(storageService.setInt, isA<Function>());
      expect(storageService.getInt, isA<Function>());
      expect(storageService.setDouble, isA<Function>());
      expect(storageService.getDouble, isA<Function>());
      expect(storageService.setStringList, isA<Function>());
      expect(storageService.getStringList, isA<Function>());
      expect(storageService.remove, isA<Function>());
      expect(storageService.containsKey, isA<Function>());
      expect(storageService.clear, isA<Function>());
    });

    test('should implement IStorageService interface correctly', () {
      // Arrange
      final storageService = SharedPreferencesStorageService();

      // Assert
      expect(storageService, implements<IStorageService>());
    });
  });
}
