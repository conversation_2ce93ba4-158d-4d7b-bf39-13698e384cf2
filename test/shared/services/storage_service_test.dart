import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/services/storage/storage_service_interface.dart';

// Mock implementation cho testing
class MockStorageService implements IStorageService {
  final Map<String, dynamic> _storage = {};

  @override
  Future<void> init() async {
    // Mock implementation - không cần làm gì
  }

  @override
  Future<bool> setString(String key, String value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<String?> getString(String key) async {
    return _storage[key] as String?;
  }

  @override
  Future<bool> setBool(String key, bool value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<bool?> getBool(String key) async {
    return _storage[key] as bool?;
  }

  @override
  Future<bool> setInt(String key, int value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<int?> getInt(String key) async {
    return _storage[key] as int?;
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<double?> getDouble(String key) async {
    return _storage[key] as double?;
  }

  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<List<String>?> getStringList(String key) async {
    return _storage[key] as List<String>?;
  }

  @override
  Future<bool> remove(String key) async {
    _storage.remove(key);
    return true;
  }

  @override
  Future<bool> containsKey(String key) async {
    return _storage.containsKey(key);
  }

  @override
  Future<bool> clear() async {
    _storage.clear();
    return true;
  }
}

void main() {
  group('IStorageService Interface Tests', () {
    late IStorageService storageService;

    setUp(() {
      storageService = MockStorageService();
    });

    test('should initialize successfully', () async {
      // Act
      await storageService.init();
      
      // Assert
      expect(storageService, isA<IStorageService>());
    });

    test('should save and retrieve string', () async {
      // Arrange
      const key = 'test_string_key';
      const value = 'test_string_value';
      
      // Act
      await storageService.init();
      final saveResult = await storageService.setString(key, value);
      final retrievedValue = await storageService.getString(key);
      
      // Assert
      expect(saveResult, isTrue);
      expect(retrievedValue, equals(value));
    });

    test('should save and retrieve boolean', () async {
      // Arrange
      const key = 'test_bool_key';
      const value = true;
      
      // Act
      await storageService.init();
      final saveResult = await storageService.setBool(key, value);
      final retrievedValue = await storageService.getBool(key);
      
      // Assert
      expect(saveResult, isTrue);
      expect(retrievedValue, equals(value));
    });

    test('should save and retrieve integer', () async {
      // Arrange
      const key = 'test_int_key';
      const value = 42;
      
      // Act
      await storageService.init();
      final saveResult = await storageService.setInt(key, value);
      final retrievedValue = await storageService.getInt(key);
      
      // Assert
      expect(saveResult, isTrue);
      expect(retrievedValue, equals(value));
    });

    test('should save and retrieve double', () async {
      // Arrange
      const key = 'test_double_key';
      const value = 3.14;
      
      // Act
      await storageService.init();
      final saveResult = await storageService.setDouble(key, value);
      final retrievedValue = await storageService.getDouble(key);
      
      // Assert
      expect(saveResult, isTrue);
      expect(retrievedValue, equals(value));
    });

    test('should save and retrieve string list', () async {
      // Arrange
      const key = 'test_list_key';
      const value = ['item1', 'item2', 'item3'];
      
      // Act
      await storageService.init();
      final saveResult = await storageService.setStringList(key, value);
      final retrievedValue = await storageService.getStringList(key);
      
      // Assert
      expect(saveResult, isTrue);
      expect(retrievedValue, equals(value));
    });

    test('should check if key exists', () async {
      // Arrange
      const key = 'test_exists_key';
      const value = 'test_value';
      
      // Act
      await storageService.init();
      await storageService.setString(key, value);
      final exists = await storageService.containsKey(key);
      
      // Assert
      expect(exists, isTrue);
    });

    test('should remove key', () async {
      // Arrange
      const key = 'test_remove_key';
      const value = 'test_value';
      
      // Act
      await storageService.init();
      await storageService.setString(key, value);
      final removeResult = await storageService.remove(key);
      final exists = await storageService.containsKey(key);
      
      // Assert
      expect(removeResult, isTrue);
      expect(exists, isFalse);
    });

    test('should clear all data', () async {
      // Arrange
      const key1 = 'test_clear_key1';
      const key2 = 'test_clear_key2';
      const value = 'test_value';
      
      // Act
      await storageService.init();
      await storageService.setString(key1, value);
      await storageService.setString(key2, value);
      final clearResult = await storageService.clear();
      final exists1 = await storageService.containsKey(key1);
      final exists2 = await storageService.containsKey(key2);
      
      // Assert
      expect(clearResult, isTrue);
      expect(exists1, isFalse);
      expect(exists2, isFalse);
    });
  });
} 