import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';

void main() {
  group('Dio Integration Test', () {
    late Dio dio;

    setUp(() {
      dio = Dio();
    });

    tearDown(() {
      dio.close();
    });

    test('<PERSON><PERSON> should be able to make a real HTTP GET request', () async {
      // Sử dụng httpbin.org API để test - đây là một API test miễn phí
      const url = 'https://httpbin.org/json';

      try {
        final response = await dio.get(url);

        // Kiểm tra response status
        expect(response.statusCode, equals(200));

        // Kiểm tra response data có tồn tại
        expect(response.data, isNotNull);
        expect(response.data, isA<Map<String, dynamic>>());

        // Kiểm tra một số field cơ bản
        final data = response.data as Map<String, dynamic>;
        expect(data['slideshow'], isNotNull);

        print('✅ Dio test passed! Response data: ${response.data}');
      } catch (e) {
        fail('Dio request failed: $e');
      }
    });

    test('Dio should handle network errors gracefully', () async {
      // Test với một URL không tồn tại
      const invalidUrl = 'https://invalid-url-that-does-not-exist.com/api/test';

      try {
        await dio.get(invalidUrl);
        fail('Should have thrown an exception for invalid URL');
      } catch (e) {
        expect(e, isA<DioException>());
        print('✅ Dio correctly handled network error: ${e.toString()}');
      }
    });

    test('Dio should work with different HTTP methods', () async {
      // Test POST request với httpbin.org
      const url = 'https://httpbin.org/post';
      final postData = {
        'title': 'Test Post',
        'body': 'This is a test post',
        'userId': 1,
      };

      try {
        final response = await dio.post(url, data: postData);

        // Kiểm tra response status
        expect(response.statusCode, equals(200));

        // Kiểm tra response data
        expect(response.data, isNotNull);
        expect(response.data, isA<Map<String, dynamic>>());

        final data = response.data as Map<String, dynamic>;
        expect(data['json'], isNotNull);
        expect(data['json']['title'], equals('Test Post'));
        expect(data['json']['body'], equals('This is a test post'));
        expect(data['json']['userId'], equals(1));

        print('✅ Dio POST test passed! Response: ${response.data}');
      } catch (e) {
        fail('Dio POST request failed: $e');
      }
    });

    test('Dio should handle timeout correctly', () async {
      // Test timeout với một URL chậm
      const url = 'https://httpbin.org/delay/3'; // Delay 3 giây

      try {
        await dio.get(
          url,
          options: Options(
            sendTimeout: const Duration(seconds: 1),
            receiveTimeout: const Duration(seconds: 1),
          ),
        );
        fail('Should have thrown a timeout exception');
      } catch (e) {
        expect(e, isA<DioException>());
        final dioError = e as DioException;
        // Có thể là connectionTimeout hoặc receiveTimeout
        expect(
          dioError.type == DioExceptionType.connectionTimeout ||
              dioError.type == DioExceptionType.receiveTimeout,
          isTrue,
        );
        print('✅ Dio correctly handled timeout: ${dioError.message}');
      }
    });

    test('Dio should handle query parameters correctly', () async {
      // Test với query parameters
      const url = 'https://httpbin.org/get';
      final queryParams = {'param1': 'value1', 'param2': 'value2'};

      try {
        final response = await dio.get(url, queryParameters: queryParams);

        expect(response.statusCode, equals(200));
        expect(response.data, isNotNull);

        final data = response.data as Map<String, dynamic>;
        expect(data['args'], isNotNull);
        expect(data['args']['param1'], equals('value1'));
        expect(data['args']['param2'], equals('value2'));

        print('✅ Dio query parameters test passed!');
      } catch (e) {
        fail('Dio query parameters test failed: $e');
      }
    });
  });
}
