import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import 'package:kiloba_biz/shared/services/settings_service.dart';
import 'package:kiloba_biz/shared/services/storage/storage_service_interface.dart';
import 'package:kiloba_biz/shared/services/storage/shared_preferences_storage_service.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/models/settings_model.dart';
import 'package:kiloba_biz/shared/models/app_theme_mode.dart';
import 'package:kiloba_biz/features/account/models/log_entry.dart'
    show LogLevel;
import 'package:kiloba_biz/shared/services/service_locator.dart';

// Mock storage service cho testing
class MockStorageService implements IStorageService {
  final Map<String, dynamic> _storage = {};

  @override
  Future<void> init() async {}

  @override
  Future<bool> setString(String key, String value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<String?> getString(String key) async {
    return _storage[key] as String?;
  }

  @override
  Future<bool> setBool(String key, bool value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<bool?> getBool(String key) async {
    return _storage[key] as bool?;
  }

  @override
  Future<bool> setInt(String key, int value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<int?> getInt(String key) async {
    return _storage[key] as int?;
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<double?> getDouble(String key) async {
    return _storage[key] as double?;
  }

  @override
  Future<bool> setStringList(String key, List<String> value) async {
    _storage[key] = value;
    return true;
  }

  @override
  Future<List<String>?> getStringList(String key) async {
    return _storage[key] as List<String>?;
  }

  @override
  Future<bool> remove(String key) async {
    _storage.remove(key);
    return true;
  }

  @override
  Future<bool> containsKey(String key) async {
    return _storage.containsKey(key);
  }

  @override
  Future<bool> clear() async {
    _storage.clear();
    return true;
  }
}

// Mock logger cho testing
class MockLogger implements IAppLogger {
  @override
  Future<void> v(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {}

  @override
  Future<void> d(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {}

  @override
  Future<void> i(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {}

  @override
  Future<void> w(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {}

  @override
  Future<void> e(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {}

  @override
  Future<void> logWithTag(
    String tag,
    String message, [
    LogLevel level = LogLevel.info,
  ]) async {}

  @override
  Future<void> logWithFeature(
    String feature,
    String message, [
    LogLevel level = LogLevel.info,
  ]) async {}

  @override
  Future<void> initialize() async {}

  @override
  Logger get logger => Logger();
}

void main() {
  group('SettingsService Tests', () {
    late SettingsService settingsService;
    late MockStorageService mockStorage;
    late MockLogger mockLogger;

    setUp(() async {
      mockStorage = MockStorageService();
      mockLogger = MockLogger();

      // Setup service locator với mock services
      await ServiceLocator.reset();
      getIt.registerLazySingleton<IStorageService>(() => mockStorage);
      getIt.registerLazySingleton<IAppLogger>(() => mockLogger);
    });

    test(
      'should return default settings when no settings saved',
      () async {
        // Act
        final settings = await SettingsService.instance.getSettings();

        // Assert
        expect(settings.themeMode, equals(AppThemeMode.system));
        expect(settings.notificationsEnabled, isTrue);
        expect(settings.biometricEnabled, isFalse);
        expect(settings.language, equals('vi'));
        expect(settings.rememberLogin, isFalse);
      },
      timeout: const Timeout(Duration(seconds: 10)),
    );

    test('should save and retrieve settings', () async {
      // Arrange
      final testSettings = SettingsModel(
        themeMode: AppThemeMode.dark,
        notificationsEnabled: false,
        biometricEnabled: true,
        language: 'en',
        rememberLogin: true,
      );

      // Act
      final saveResult = await SettingsService.instance.saveSettings(
        testSettings,
      );
      final retrievedSettings = await SettingsService.instance.getSettings();

      // Assert
      expect(saveResult, isTrue);
      expect(retrievedSettings.themeMode, equals(AppThemeMode.dark));
      expect(retrievedSettings.notificationsEnabled, isFalse);
      expect(retrievedSettings.biometricEnabled, isTrue);
      expect(retrievedSettings.language, equals('en'));
      expect(retrievedSettings.rememberLogin, isTrue);
    });

    test('should update theme mode', () async {
      // Arrange
      const newThemeMode = AppThemeMode.light;

      // Act
      final updateResult = await SettingsService.instance.updateThemeMode(
        newThemeMode,
      );
      final settings = await SettingsService.instance.getSettings();

      // Assert
      expect(updateResult, isTrue);
      expect(settings.themeMode, equals(newThemeMode));
    });

    test('should update notifications setting', () async {
      // Arrange
      const notificationsEnabled = false;

      // Act
      final updateResult = await SettingsService.instance.updateNotifications(
        notificationsEnabled,
      );
      final settings = await SettingsService.instance.getSettings();

      // Assert
      expect(updateResult, isTrue);
      expect(settings.notificationsEnabled, equals(notificationsEnabled));
    });

    test('should update biometric setting', () async {
      // Arrange
      const biometricEnabled = true;

      // Act
      final updateResult = await SettingsService.instance.updateBiometric(
        biometricEnabled,
      );
      final settings = await SettingsService.instance.getSettings();

      // Assert
      expect(updateResult, isTrue);
      expect(settings.biometricEnabled, equals(biometricEnabled));
    });

    test('should update language setting', () async {
      // Arrange
      const language = 'en';

      // Act
      final updateResult = await SettingsService.instance.updateLanguage(
        language,
      );
      final settings = await SettingsService.instance.getSettings();

      // Assert
      expect(updateResult, isTrue);
      expect(settings.language, equals(language));
    });

    test('should update remember login setting', () async {
      // Arrange
      const rememberLogin = true;

      // Act
      final updateResult = await SettingsService.instance.updateRememberLogin(
        rememberLogin,
      );
      final settings = await SettingsService.instance.getSettings();

      // Assert
      expect(updateResult, isTrue);
      expect(settings.rememberLogin, equals(rememberLogin));
    });

    test('should clear settings', () async {
      // Arrange
      final testSettings = SettingsModel(
        themeMode: AppThemeMode.dark,
        notificationsEnabled: false,
        biometricEnabled: true,
        language: 'en',
        rememberLogin: true,
      );
      await SettingsService.instance.saveSettings(testSettings);

      // Act
      final clearResult = await SettingsService.instance.clearSettings();
      final hasSettings = await SettingsService.instance.hasSettings();

      // Assert
      expect(clearResult, isTrue);
      expect(hasSettings, isFalse);
    });

    test('should check if settings exist', () async {
      // Act
      final hasSettings = await SettingsService.instance.hasSettings();

      // Assert
      expect(hasSettings, isFalse);
    });
  });
}
