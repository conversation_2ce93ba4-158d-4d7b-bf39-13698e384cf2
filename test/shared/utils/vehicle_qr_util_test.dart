import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/vehicle_qr_util.dart';
import 'package:kiloba_biz/shared/utils/vehicle_qr_sample_data.dart';

void main() {
  group('VehicleQRUtil Tests', () {
    
    group('parseVehicleRegistrationData', () {
      test('should parse English field names correctly', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "vehicle_name": "Honda Wave RSX",
  "frame_number": "MH1JE1300K1234567",
  "engine_number": "JE1300K1234567",
  "registration_number": "DK123456789",
  "registration_place": "<PERSON><PERSON>c Đăng kiểm Việt Nam",
  "registration_date": "2023-01-15",
  "owner_name": "Nguyễn Văn A",
  "owner_address": "123 Đường ABC, Quận 1, TP.HCM"
}
''';
        
        final result = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        
        expect(result['vehicle_plate_number'], equals('30A-12345'));
        expect(result['vehicle_name'], equals('Honda Wave RSX'));
        expect(result['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(result['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(result['vehicle_registration_number'], equals('DK123456789'));
        expect(result['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(result['vehicle_registration_date'], equals('15/01/2023'));
        expect(result['collateral_owner'], equals('Nguyễn Văn A'));
        expect(result['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });

      test('should parse Vietnamese field names correctly', () {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567",
  "so_dang_ky": "DK123456789",
  "noi_cap": "Cục Đăng kiểm Việt Nam",
  "ngay_cap": "2023-01-15",
  "chu_xe": "Nguyễn Văn A",
  "dia_chi_chu_xe": "123 Đường ABC, Quận 1, TP.HCM"
}
''';
        
        final result = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        
        expect(result['vehicle_plate_number'], equals('30A-12345'));
        expect(result['vehicle_name'], equals('Honda Wave RSX'));
        expect(result['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(result['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(result['vehicle_registration_number'], equals('DK123456789'));
        expect(result['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(result['vehicle_registration_date'], equals('15/01/2023'));
        expect(result['collateral_owner'], equals('Nguyễn Văn A'));
        expect(result['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });

      test('should parse mixed field names correctly', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "engine_number": "JE1300K1234567"
}
''';
        
        final result = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        
        expect(result['vehicle_plate_number'], equals('30A-12345'));
        expect(result['vehicle_name'], equals('Honda Wave RSX'));
        expect(result['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(result['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(result['vehicle_registration_number'], equals(''));
        expect(result['vehicle_registration_place'], equals(''));
        expect(result['vehicle_registration_date'], equals(''));
        expect(result['collateral_owner'], equals(''));
        expect(result['collateral_owner_address'], equals(''));
      });

      test('should handle alternative Vietnamese field names', () {
        final qrData = '''
{
  "bien_so": "30A-12345",
  "ten_loai_xe": "Honda Wave RSX",
  "so_khung_xe": "MH1JE1300K1234567",
  "so_may_xe": "JE1300K1234567",
  "so_giay_dang_ky": "DK123456789",
  "co_quan_cap": "Cục Đăng kiểm Việt Nam",
  "ngay_dang_ky": "2023-01-15",
  "ten_chu_xe": "Nguyễn Văn A",
  "dia_chi": "123 Đường ABC, Quận 1, TP.HCM"
}
''';
        
        final result = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        
        expect(result['vehicle_plate_number'], equals('30A-12345'));
        expect(result['vehicle_name'], equals('Honda Wave RSX'));
        expect(result['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(result['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(result['vehicle_registration_number'], equals('DK123456789'));
        expect(result['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(result['vehicle_registration_date'], equals('15/01/2023'));
        expect(result['collateral_owner'], equals('Nguyễn Văn A'));
        expect(result['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });

      test('should throw FormatException for invalid JSON', () {
        expect(
          () => VehicleQRUtil.parseVehicleRegistrationData('invalid json'),
          throwsA(isA<FormatException>()),
        );
      });

      test('should throw ArgumentError for empty data', () {
        expect(
          () => VehicleQRUtil.parseVehicleRegistrationData(''),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('isValidVehicleQRData', () {
      test('should return true for valid English field names', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "vehicle_name": "Honda Wave RSX",
  "frame_number": "MH1JE1300K1234567",
  "engine_number": "JE1300K1234567"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
      });

      test('should return true for valid Vietnamese field names', () {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
      });

      test('should return true for mixed field names', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "engine_number": "JE1300K1234567"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
      });

      test('should return false for missing required fields', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "vehicle_name": "Honda Wave RSX"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isFalse);
      });

      test('should return false for empty required fields', () {
        final qrData = '''
{
  "plate_number": "",
  "vehicle_name": "Honda Wave RSX",
  "frame_number": "MH1JE1300K1234567",
  "engine_number": "JE1300K1234567"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isFalse);
      });

      test('should return false for null required fields', () {
        final qrData = '''
{
  "plate_number": null,
  "vehicle_name": "Honda Wave RSX",
  "frame_number": "MH1JE1300K1234567",
  "engine_number": "JE1300K1234567"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isFalse);
      });

      test('should return false for invalid JSON', () {
        expect(VehicleQRUtil.isValidVehicleQRData('invalid json'), isFalse);
      });
    });

    group('extractField', () {
      test('should extract field value correctly', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "vehicle_name": "Honda Wave RSX"
}
''';
        
        expect(VehicleQRUtil.extractField(qrData, 'plate_number'), equals('30A-12345'));
        expect(VehicleQRUtil.extractField(qrData, 'vehicle_name'), equals('Honda Wave RSX'));
        expect(VehicleQRUtil.extractField(qrData, 'non_existent'), equals(''));
      });
    });

    group('getAvailableFields', () {
      test('should return available fields', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "vehicle_name": "Honda Wave RSX",
  "frame_number": "MH1JE1300K1234567"
}
''';
        
        final fields = VehicleQRUtil.getAvailableFields(qrData);
        expect(fields, contains('plate_number'));
        expect(fields, contains('vehicle_name'));
        expect(fields, contains('frame_number'));
        expect(fields.length, equals(3));
      });

      test('should return empty list for invalid JSON', () {
        final fields = VehicleQRUtil.getAvailableFields('invalid json');
        expect(fields, isEmpty);
      });
    });

    group('generateMockQRData', () {
      test('should generate valid mock data', () {
        final mockData = VehicleQRUtil.generateMockQRData();
        
        expect(VehicleQRUtil.isValidVehicleQRData(mockData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(mockData);
        expect(parsedData['vehicle_plate_number'], isNotEmpty);
        expect(parsedData['vehicle_name'], isNotEmpty);
        expect(parsedData['vehicle_frame_number'], isNotEmpty);
        expect(parsedData['vehicle_engine_number'], isNotEmpty);
      });

      test('should generate custom mock data', () {
        final mockData = VehicleQRUtil.generateMockQRData(
          plateNumber: '51A-99999',
          vehicleName: 'Yamaha Exciter',
        );
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(mockData);
        expect(parsedData['vehicle_plate_number'], equals('51A-99999'));
        expect(parsedData['vehicle_name'], equals('Yamaha Exciter'));
      });
    });

    group('Sample Data Format Tests', () {
      test('should parse sample format 1 (Standard Vietnamese)', () {
        final qrData = VehicleQRSampleData.sampleFormat1;
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('30A-12345'));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(parsedData['vehicle_registration_number'], equals('DK123456789'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('15/01/2023'));
        expect(parsedData['collateral_owner'], equals('Nguyễn Văn A'));
        expect(parsedData['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });

      test('should parse sample format 2 (Alternative Vietnamese)', () {
        final qrData = VehicleQRSampleData.sampleFormat2;
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('51A-99999'));
        expect(parsedData['vehicle_name'], equals('Yamaha Exciter'));
        expect(parsedData['vehicle_frame_number'], equals('YH1JE1300K9876543'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K9876543'));
        expect(parsedData['vehicle_registration_number'], equals('DK987654321'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('20/06/2023'));
        expect(parsedData['collateral_owner'], equals('Trần Thị B'));
        expect(parsedData['collateral_owner_address'], equals('456 Đường XYZ, Quận 2, TP.HCM'));
      });

      test('should parse sample format 3 (Mixed format)', () {
        final qrData = VehicleQRSampleData.sampleFormat3;
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('43A-88888'));
        expect(parsedData['vehicle_name'], equals('Suzuki Raider'));
        expect(parsedData['vehicle_frame_number'], equals('SZ1JE1300K5555555'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K5555555'));
        expect(parsedData['vehicle_registration_number'], equals('DK555555555'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('10/03/2023'));
        expect(parsedData['collateral_owner'], equals('Lê Văn C'));
        expect(parsedData['collateral_owner_address'], equals('789 Đường DEF, Quận 3, TP.HCM'));
      });

      test('should parse sample format 4 (English format)', () {
        final qrData = VehicleQRSampleData.sampleFormat4;
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29A-77777'));
        expect(parsedData['vehicle_name'], equals('Kawasaki Ninja'));
        expect(parsedData['vehicle_frame_number'], equals('KW1JE1300K4444444'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K4444444'));
        expect(parsedData['vehicle_registration_number'], equals('DK444444444'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('05/09/2023'));
        expect(parsedData['collateral_owner'], equals('Phạm Thị D'));
        expect(parsedData['collateral_owner_address'], equals('321 Đường GHI, Quận 4, TP.HCM'));
      });

      test('should parse sample format 5 (Minimal required fields)', () {
        final qrData = VehicleQRSampleData.sampleFormat5;
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('92A-66666'));
        expect(parsedData['vehicle_name'], equals('Piaggio Vespa'));
        expect(parsedData['vehicle_frame_number'], equals('PG1JE1300K3333333'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K3333333'));
        expect(parsedData['vehicle_registration_number'], equals(''));
        expect(parsedData['vehicle_registration_place'], equals(''));
        expect(parsedData['vehicle_registration_date'], equals(''));
        expect(parsedData['collateral_owner'], equals(''));
        expect(parsedData['collateral_owner_address'], equals(''));
      });

      test('should parse sample format 6 (Nested object format)', () {
        final qrData = VehicleQRSampleData.sampleFormat6;
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('30A-12345'));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(parsedData['vehicle_registration_number'], equals('DK123456789'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('15/01/2023'));
        expect(parsedData['collateral_owner'], equals('Nguyễn Văn A'));
        expect(parsedData['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });

      test('should parse sample format 7 (Array format)', () {
        final qrData = VehicleQRSampleData.sampleFormat7;
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('30A-12345'));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(parsedData['vehicle_registration_number'], equals('DK123456789'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('15/01/2023'));
        expect(parsedData['collateral_owner'], equals('Nguyễn Văn A'));
        expect(parsedData['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });
    });

    group('Sample Data Integration Tests', () {
      test('should handle all sample formats', () {
        final allFormats = VehicleQRSampleData.getAllSampleFormats();
        
        for (int i = 0; i < allFormats.length; i++) {
          final format = allFormats[i];
          final description = VehicleQRSampleData.getSampleDescription(i);
          
          expect(VehicleQRUtil.isValidVehicleQRData(format), isTrue, 
            reason: 'Format $i ($description) should be valid');
          
          final parsedData = VehicleQRUtil.parseVehicleRegistrationData(format);
          expect(parsedData['vehicle_plate_number'], isNotEmpty,
            reason: 'Format $i ($description) should have plate number');
          expect(parsedData['vehicle_name'], isNotEmpty,
            reason: 'Format $i ($description) should have vehicle name');
          expect(parsedData['vehicle_frame_number'], isNotEmpty,
            reason: 'Format $i ($description) should have frame number');
          expect(parsedData['vehicle_engine_number'], isNotEmpty,
            reason: 'Format $i ($description) should have engine number');
        }
      });

      test('should generate random sample data', () {
        final randomData = VehicleQRSampleData.generateRandomSample();
        expect(VehicleQRUtil.isValidVehicleQRData(randomData), isTrue);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(randomData);
        expect(parsedData['vehicle_plate_number'], isNotEmpty);
        expect(parsedData['vehicle_name'], isNotEmpty);
        expect(parsedData['vehicle_frame_number'], isNotEmpty);
        expect(parsedData['vehicle_engine_number'], isNotEmpty);
      });
    });
  });
}
