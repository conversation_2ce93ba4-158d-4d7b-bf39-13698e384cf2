import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/vehicle_qr_util.dart';
import 'package:kiloba_biz/shared/utils/vehicle_qr_debug_util.dart';
import 'package:kiloba_biz/shared/utils/vehicle_qr_sample_data.dart';

void main() {
  group('Vehicle QR Scan Function Tests', () {
    
    group('QR Data Processing', () {
      test('should process Vietnamese QR data correctly', () {
        final qrData = VehicleQRSampleData.sampleFormat1;
        
        // Test validation
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('30A-12345'));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(parsedData['vehicle_registration_number'], equals('DK123456789'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('15/01/2023'));
        expect(parsedData['collateral_owner'], equals('Nguyễn Văn A'));
        expect(parsedData['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });

      test('should process English QR data correctly', () {
        final qrData = VehicleQRSampleData.sampleFormat4;
        
        // Test validation
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29A-77777'));
        expect(parsedData['vehicle_name'], equals('Kawasaki Ninja'));
        expect(parsedData['vehicle_frame_number'], equals('KW1JE1300K4444444'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K4444444'));
        expect(parsedData['vehicle_registration_number'], equals('DK444444444'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('05/09/2023'));
        expect(parsedData['collateral_owner'], equals('Phạm Thị D'));
        expect(parsedData['collateral_owner_address'], equals('321 Đường GHI, Quận 4, TP.HCM'));
      });

      test('should process nested object QR data correctly', () {
        final qrData = VehicleQRSampleData.sampleFormat6;
        
        // Test validation
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('30A-12345'));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(parsedData['vehicle_registration_number'], equals('DK123456789'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('15/01/2023'));
        expect(parsedData['collateral_owner'], equals('Nguyễn Văn A'));
        expect(parsedData['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });

      test('should process array format QR data correctly', () {
        final qrData = VehicleQRSampleData.sampleFormat7;
        
        // Test validation
        expect(VehicleQRUtil.isValidVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('30A-12345'));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K1234567'));
        expect(parsedData['vehicle_registration_number'], equals('DK123456789'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('15/01/2023'));
        expect(parsedData['collateral_owner'], equals('Nguyễn Văn A'));
        expect(parsedData['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });
    });

    group('Debug Functionality', () {
      test('should generate debug report for all sample formats', () {
        final allFormats = VehicleQRSampleData.getAllSampleFormats();
        
        for (int i = 0; i < allFormats.length; i++) {
          final format = allFormats[i];
          final report = VehicleQRDebugUtil.generateDebugReport(format);
          
          expect(report, contains('VEHICLE QR REGISTRATION DEBUG REPORT'));
          expect(report, contains('Raw content: $format'));
          expect(report, contains('Valid JSON format'));
          expect(report, contains('Status: ✅ VALID'));
        }
      });

      test('should detect format correctly for all sample formats', () {
        final formats = [
          VehicleQRSampleData.sampleFormat1, // Vietnamese
          VehicleQRSampleData.sampleFormat2, // Alternative Vietnamese
          VehicleQRSampleData.sampleFormat3, // Mixed
          VehicleQRSampleData.sampleFormat4, // English
          VehicleQRSampleData.sampleFormat5, // Minimal
          VehicleQRSampleData.sampleFormat6, // Nested object
          VehicleQRSampleData.sampleFormat7, // Array
        ];
        
        for (final format in formats) {
          final report = VehicleQRDebugUtil.generateDebugReport(format);
          expect(report, contains('Valid JSON format'));
          expect(report, contains('Status: ✅ VALID'));
        }
      });

      test('should save QR data to file', () async {
        final qrData = VehicleQRSampleData.sampleFormat1;
        final filename = await VehicleQRDebugUtil.saveQRDataToFile(qrData);
        
        expect(filename, isNotEmpty);
        expect(filename, contains('vehicle_qr_data_'));
        expect(filename, endsWith('.json'));
      });
    });

    group('Error Handling', () {
      test('should handle invalid QR data gracefully', () {
        final invalidQrData = 'invalid json';
        
        expect(VehicleQRUtil.isValidVehicleQRData(invalidQrData), isFalse);
        
        expect(() => VehicleQRUtil.parseVehicleRegistrationData(invalidQrData), 
          throwsA(isA<FormatException>()));
      });

      test('should handle empty QR data gracefully', () {
        expect(VehicleQRUtil.isValidVehicleQRData(''), isFalse);
        
        expect(() => VehicleQRUtil.parseVehicleRegistrationData(''), 
          throwsA(isA<ArgumentError>()));
      });

      test('should handle QR data with missing required fields', () {
        final incompleteQrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(incompleteQrData), isFalse);
        
        // Should still parse but with empty values for missing fields
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(incompleteQrData);
        expect(parsedData['vehicle_plate_number'], equals('30A-12345'));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals(''));
        expect(parsedData['vehicle_engine_number'], equals(''));
      });

      test('should handle QR data with null values', () {
        final nullQrData = '''
{
  "bien_so_xe": null,
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567"
}
''';
        
        expect(VehicleQRUtil.isValidVehicleQRData(nullQrData), isFalse);
        
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(nullQrData);
        expect(parsedData['vehicle_plate_number'], equals(''));
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_frame_number'], equals('MH1JE1300K1234567'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K1234567'));
      });
    });

    group('UI Mapping Tests', () {
      test('should map all fields correctly to UI form', () {
        final qrData = VehicleQRSampleData.sampleFormat1;
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        
        // Check all UI fields are mapped
        expect(parsedData.containsKey('vehicle_plate_number'), isTrue);
        expect(parsedData.containsKey('vehicle_name'), isTrue);
        expect(parsedData.containsKey('vehicle_frame_number'), isTrue);
        expect(parsedData.containsKey('vehicle_engine_number'), isTrue);
        expect(parsedData.containsKey('vehicle_registration_number'), isTrue);
        expect(parsedData.containsKey('vehicle_registration_place'), isTrue);
        expect(parsedData.containsKey('vehicle_registration_date'), isTrue);
        expect(parsedData.containsKey('collateral_owner'), isTrue);
        expect(parsedData.containsKey('collateral_owner_address'), isTrue);
        
        // Check all fields have values
        expect(parsedData['vehicle_plate_number'], isNotEmpty);
        expect(parsedData['vehicle_name'], isNotEmpty);
        expect(parsedData['vehicle_frame_number'], isNotEmpty);
        expect(parsedData['vehicle_engine_number'], isNotEmpty);
      });

      test('should handle partial data mapping', () {
        final qrData = VehicleQRSampleData.sampleFormat5; // Minimal required fields
        final parsedData = VehicleQRUtil.parseVehicleRegistrationData(qrData);
        
        // Required fields should be mapped
        expect(parsedData['vehicle_plate_number'], equals('92A-66666'));
        expect(parsedData['vehicle_name'], equals('Piaggio Vespa'));
        expect(parsedData['vehicle_frame_number'], equals('PG1JE1300K3333333'));
        expect(parsedData['vehicle_engine_number'], equals('JE1300K3333333'));
        
        // Optional fields should be empty
        expect(parsedData['vehicle_registration_number'], equals(''));
        expect(parsedData['vehicle_registration_place'], equals(''));
        expect(parsedData['vehicle_registration_date'], equals(''));
        expect(parsedData['collateral_owner'], equals(''));
        expect(parsedData['collateral_owner_address'], equals(''));
      });
    });

    group('Integration Tests', () {
      test('should process all sample formats end-to-end', () {
        final allFormats = VehicleQRSampleData.getAllSampleFormats();
        
        for (int i = 0; i < allFormats.length; i++) {
          final format = allFormats[i];
          final description = VehicleQRSampleData.getSampleDescription(i);
          
          // Step 1: Validate QR data
          expect(VehicleQRUtil.isValidVehicleQRData(format), isTrue, 
            reason: 'Format $i ($description) should be valid');
          
          // Step 2: Parse QR data
          final parsedData = VehicleQRUtil.parseVehicleRegistrationData(format);
          
          // Step 3: Verify required fields are mapped
          expect(parsedData['vehicle_plate_number'], isNotEmpty,
            reason: 'Format $i ($description) should have plate number');
          expect(parsedData['vehicle_name'], isNotEmpty,
            reason: 'Format $i ($description) should have vehicle name');
          expect(parsedData['vehicle_frame_number'], isNotEmpty,
            reason: 'Format $i ($description) should have frame number');
          expect(parsedData['vehicle_engine_number'], isNotEmpty,
            reason: 'Format $i ($description) should have engine number');
          
          // Step 4: Generate debug report
          final debugReport = VehicleQRDebugUtil.generateDebugReport(format);
          expect(debugReport, contains('Status: ✅ VALID'),
            reason: 'Format $i ($description) should be valid in debug report');
        }
      });

      test('should handle real-world QR data scenarios', () {
        // Test with various real-world scenarios
        final scenarios = [
          {
            'name': 'Complete Vietnamese QR',
            'data': VehicleQRSampleData.sampleFormat1,
            'expectedValid': true,
          },
          {
            'name': 'Minimal QR',
            'data': VehicleQRSampleData.sampleFormat5,
            'expectedValid': true,
          },
          {
            'name': 'Nested Object QR',
            'data': VehicleQRSampleData.sampleFormat6,
            'expectedValid': true,
          },
          {
            'name': 'Array Format QR',
            'data': VehicleQRSampleData.sampleFormat7,
            'expectedValid': true,
          },
        ];
        
        for (final scenario in scenarios) {
          final name = scenario['name'] as String;
          final data = scenario['data'] as String;
          final expectedValid = scenario['expectedValid'] as bool;
          
          // Test validation
          final isValid = VehicleQRUtil.isValidVehicleQRData(data);
          expect(isValid, equals(expectedValid), reason: '$name validation failed');
          
          if (expectedValid) {
            // Test parsing
            final parsedData = VehicleQRUtil.parseVehicleRegistrationData(data);
            expect(parsedData['vehicle_plate_number'], isNotEmpty, reason: '$name should have plate number');
            expect(parsedData['vehicle_name'], isNotEmpty, reason: '$name should have vehicle name');
            
            // Test debug report
            final debugReport = VehicleQRDebugUtil.generateDebugReport(data);
            expect(debugReport, contains('Status: ✅ VALID'), reason: '$name debug report should show valid');
          }
        }
      });
    });
  });
}

