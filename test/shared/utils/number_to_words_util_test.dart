import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/number_to_words_util.dart';

void main() {
  group('NumberToWordsUtil', () {
    group('convertToWords', () {
      test('should convert zero correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(0),
          equals('không đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(0, includeCurrency: false),
          equals('không'),
        );
      });

      test('should convert single digits correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(1),
          equals('một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(5),
          equals('năm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(9),
          equals('chín đồng'),
        );
      });

      test('should convert teens correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(10),
          equals('mười đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(11),
          equals('mười một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(15),
          equals('mười lăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(19),
          equals('mười chín đồng'),
        );
      });

      test('should convert tens correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(20),
          equals('hai mươi đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(25),
          equals('hai mươi lăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(30),
          equals('ba mươi đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(99),
          equals('chín mươi chín đồng'),
        );
      });

      test('should convert hundreds correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(100),
          equals('một trăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(101),
          equals('một trăm lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(105),
          equals('một trăm lẻ năm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(110),
          equals('một trăm mười đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(115),
          equals('một trăm mười lăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(200),
          equals('hai trăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(250),
          equals('hai trăm năm mươi đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(999),
          equals('chín trăm chín mươi chín đồng'),
        );
      });

      test('should convert thousands correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(1000),
          equals('một nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1001),
          equals('một nghìn lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1010),
          equals('một nghìn mười đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1100),
          equals('một nghìn một trăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1111),
          equals('một nghìn một trăm mười một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(2000),
          equals('hai nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(2500),
          equals('hai nghìn năm trăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(9999),
          equals('chín nghìn chín trăm chín mươi chín đồng'),
        );
      });

      test('should convert ten thousands correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(10000),
          equals('mười nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(10001),
          equals('mười nghìn lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(10100),
          equals('mười nghìn một trăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(11000),
          equals('mười một nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(15000),
          equals('mười lăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(20000),
          equals('hai mươi nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(25000),
          equals('hai mươi lăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(99999),
          equals('chín mươi chín nghìn chín trăm chín mươi chín đồng'),
        );
      });

      test('should convert hundred thousands correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(100000),
          equals('một trăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(100001),
          equals('một trăm nghìn lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(101000),
          equals('một trăm lẻ một nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(110000),
          equals('một trăm mười nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(150000),
          equals('một trăm năm mươi nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(200000),
          equals('hai trăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(250000),
          equals('hai trăm năm mươi nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(999999),
          equals('chín trăm chín mươi chín nghìn chín trăm chín mươi chín đồng'),
        );
      });

      test('should convert millions correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(1000000),
          equals('một triệu đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1000001),
          equals('một triệu lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1010000),
          equals('một triệu mười nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1100000),
          equals('một triệu một trăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1500000),
          equals('một triệu năm trăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(2000000),
          equals('hai triệu đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(2500000),
          equals('hai triệu năm trăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(9999999),
          equals('chín triệu chín trăm chín mươi chín nghìn chín trăm chín mươi chín đồng'),
        );
      });

      test('should convert billions correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(1000000000),
          equals('một tỷ đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1000000001),
          equals('một tỷ lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1010000000),
          equals('một tỷ mười triệu đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1100000000),
          equals('một tỷ một trăm triệu đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1500000000),
          equals('một tỷ năm trăm triệu đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(2000000000),
          equals('hai tỷ đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(2500000000),
          equals('hai tỷ năm trăm triệu đồng'),
        );
      });

      test('should handle complex numbers correctly', () {
        expect(
          NumberToWordsUtil.convertToWords(1234567),
          equals('một triệu hai trăm ba mươi bốn nghìn năm trăm sáu mươi bảy đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(12345678),
          equals('mười hai triệu ba trăm bốn mươi lăm nghìn sáu trăm bảy mươi tám đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(123456789),
          equals('một trăm hai mươi ba triệu bốn trăm năm mươi sáu nghìn bảy trăm tám mươi chín đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1234567890),
          equals('một tỷ hai trăm ba mươi bốn triệu năm trăm sáu mươi bảy nghìn tám trăm chín mươi đồng'),
        );
      });

      test('should throw ArgumentError for negative numbers', () {
        expect(
          () => NumberToWordsUtil.convertToWords(-1),
          throwsA(isA<ArgumentError>()),
        );
        expect(
          () => NumberToWordsUtil.convertToWords(-100),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('convertDecimalToWords', () {
      test('should convert decimal amounts correctly', () {
        expect(
          NumberToWordsUtil.convertDecimalToWords(0.0),
          equals('không đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(0.0, includeCurrency: false),
          equals('không'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(1.0),
          equals('một đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(1.5),
          equals('một phẩy năm mươi đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(1.50),
          equals('một phẩy năm mươi đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(10.25),
          equals('mười phẩy hai mươi lăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(100.99),
          equals('một trăm phẩy chín mươi chín đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(1000.01),
          equals('một nghìn phẩy một đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(1234.56),
          equals('một nghìn hai trăm ba mươi bốn phẩy năm mươi sáu đồng'),
        );
      });

      test('should handle different decimal places', () {
        expect(
          NumberToWordsUtil.convertDecimalToWords(1.234, decimalPlaces: 3),
          equals('một phẩy hai trăm ba mươi bốn đồng'),
        );
        expect(
          NumberToWordsUtil.convertDecimalToWords(1.2, decimalPlaces: 1),
          equals('một phẩy hai đồng'),
        );
      });

      test('should throw ArgumentError for negative amounts', () {
        expect(
          () => NumberToWordsUtil.convertDecimalToWords(-1.0),
          throwsA(isA<ArgumentError>()),
        );
        expect(
          () => NumberToWordsUtil.convertDecimalToWords(-100.5),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('formatNumber', () {
      test('should format numbers with thousand separators', () {
        expect(
          NumberToWordsUtil.formatNumber(0),
          equals('0'),
        );
        expect(
          NumberToWordsUtil.formatNumber(1000),
          equals('1.000'),
        );
        expect(
          NumberToWordsUtil.formatNumber(10000),
          equals('10.000'),
        );
        expect(
          NumberToWordsUtil.formatNumber(100000),
          equals('100.000'),
        );
        expect(
          NumberToWordsUtil.formatNumber(1000000),
          equals('1.000.000'),
        );
        expect(
          NumberToWordsUtil.formatNumber(1234567),
          equals('1.234.567'),
        );
        expect(
          NumberToWordsUtil.formatNumber(12345678),
          equals('12.345.678'),
        );
        expect(
          NumberToWordsUtil.formatNumber(123456789),
          equals('123.456.789'),
        );
        expect(
          NumberToWordsUtil.formatNumber(1234567890),
          equals('1.234.567.890'),
        );
      });

      test('should use custom separator', () {
        expect(
          NumberToWordsUtil.formatNumber(1000, separator: ','),
          equals('1,000'),
        );
        expect(
          NumberToWordsUtil.formatNumber(1000000, separator: ' '),
          equals('1 000 000'),
        );
      });
    });

    group('formatDecimal', () {
      test('should format decimal amounts correctly', () {
        expect(
          NumberToWordsUtil.formatDecimal(0.0),
          equals('0,00'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(1.0),
          equals('1,00'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(1.5),
          equals('1,50'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(10.25),
          equals('10,25'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(100.99),
          equals('100,99'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(1000.01),
          equals('1.000,01'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(1234.56),
          equals('1.234,56'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(1234567.89),
          equals('1.234.567,89'),
        );
      });

      test('should handle different decimal places', () {
        expect(
          NumberToWordsUtil.formatDecimal(1.234, decimalPlaces: 3),
          equals('1,234'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(1.2, decimalPlaces: 1),
          equals('1,2'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(1.0, decimalPlaces: 0),
          equals('1'),
        );
      });

      test('should use custom separators', () {
        expect(
          NumberToWordsUtil.formatDecimal(
            1234.56,
            thousandSeparator: ',',
            decimalSeparator: '.',
          ),
          equals('1,234.56'),
        );
        expect(
          NumberToWordsUtil.formatDecimal(
            1234.56,
            thousandSeparator: ' ',
            decimalSeparator: ',',
          ),
          equals('1 234,56'),
        );
      });
    });

    group('Edge cases and special numbers', () {
      test('should handle special Vietnamese number rules', () {
        // Test "lẻ" for numbers like 101, 201, etc.
        expect(
          NumberToWordsUtil.convertToWords(101),
          equals('một trăm lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(201),
          equals('hai trăm lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1001),
          equals('một nghìn lẻ một đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(10001),
          equals('mười nghìn lẻ một đồng'),
        );
      });

      test('should handle numbers with trailing zeros', () {
        expect(
          NumberToWordsUtil.convertToWords(100),
          equals('một trăm đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1000),
          equals('một nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(10000),
          equals('mười nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(100000),
          equals('một trăm nghìn đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1000000),
          equals('một triệu đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(1000000000),
          equals('một tỷ đồng'),
        );
      });

      test('should handle very large numbers', () {
        expect(
          NumberToWordsUtil.convertToWords(999999999),
          equals('chín trăm chín mươi chín triệu chín trăm chín mươi chín nghìn chín trăm chín mươi chín đồng'),
        );
        expect(
          NumberToWordsUtil.convertToWords(9999999999),
          equals('chín tỷ chín trăm chín mươi chín triệu chín trăm chín mươi chín nghìn chín trăm chín mươi chín đồng'),
        );
      });
    });
  });
}
