import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/vehicle_qr_basic_util.dart';

void main() {
  group('Vehicle QR Basic Fields Tests', () {
    
    group('Basic QR Data Parsing', () {
      test('should parse QR data with only 3 basic fields', () {
        final qrData = '''
{
  "bien_so_xe": "29AA-946.89",
  "so_may": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285"
}
''';
        
        // Test validation - should be valid with just 3 basic fields
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });

      test('should parse text format QR data (semicolon separated)', () {
        final qrData = '29AA-946.89; Nền màu trắng, chữ và số màu đen; JK19E2026765; RLHJK1914PZ012285';
        
        // Test validation
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });

      test('should parse QR data with alternative field names', () {
        final qrData = '''
{
  "plate_number": "29AA-946.89",
  "engine_number": "JK19E2026765",
  "frame_number": "RLHJK1914PZ012285"
}
''';
        
        // Test validation
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });

      test('should parse QR data with mixed field names', () {
        final qrData = '''
{
  "bien_so_xe": "29AA-946.89",
  "engine_number": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285"
}
''';
        
        // Test validation
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });

      test('should parse QR data with nested object format', () {
        final qrData = '''
{
  "thong_tin_xe": {
    "bien_so": "29AA-946.89",
    "so_may": "JK19E2026765",
    "so_khung": "RLHJK1914PZ012285"
  }
}
''';
        
        // Test validation
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });

      test('should parse QR data with array format', () {
        final qrData = '''
{
  "xe_may": [
    {
      "bien_so_xe": "29AA-946.89",
      "so_may": "JK19E2026765",
      "so_khung": "RLHJK1914PZ012285"
    }
  ]
}
''';
        
        // Test validation
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });
    });

    group('Real QR Data Format Tests', () {
      test('should parse real QR data format with 3 basic fields', () {
        final qrData = '''
{
  "bien_so_xe": "29AA-946.89",
  "so_may": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285"
}
''';
        
        // Test validation
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        
        // Verify all 3 basic fields are parsed correctly
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
        
        // Verify other fields are empty (user will input manually)
        expect(parsedData['vehicle_name'], equals(''));
        expect(parsedData['vehicle_registration_number'], equals(''));
        expect(parsedData['vehicle_registration_place'], equals(''));
        expect(parsedData['vehicle_registration_date'], equals(''));
        expect(parsedData['collateral_owner'], equals(''));
        expect(parsedData['collateral_owner_address'], equals(''));
      });

      test('should handle QR data with additional fields but only parse basic 3', () {
        final qrData = '''
{
  "bien_so_xe": "29AA-946.89",
  "so_may": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285",
  "ten_xe": "Honda Wave RSX",
  "so_dang_ky": "DK123456789",
  "noi_cap": "Cục Đăng kiểm Việt Nam",
  "ngay_cap": "2023-01-15",
  "chu_xe": "Nguyễn Văn A",
  "dia_chi_chu_xe": "123 Đường ABC, Quận 1, TP.HCM"
}
''';
        
        // Test validation
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isTrue);
        
        // Test parsing
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        
        // Verify basic 3 fields are parsed correctly
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
        
        // Verify additional fields are also parsed if available
        expect(parsedData['vehicle_name'], equals('Honda Wave RSX'));
        expect(parsedData['vehicle_registration_number'], equals('DK123456789'));
        expect(parsedData['vehicle_registration_place'], equals('Cục Đăng kiểm Việt Nam'));
        expect(parsedData['vehicle_registration_date'], equals('15/01/2023'));
        expect(parsedData['collateral_owner'], equals('Nguyễn Văn A'));
        expect(parsedData['collateral_owner_address'], equals('123 Đường ABC, Quận 1, TP.HCM'));
      });
    });

    group('Error Handling Tests', () {
      test('should handle QR data with missing basic fields', () {
        final qrData = '''
{
  "bien_so_xe": "29AA-946.89",
  "so_may": "JK19E2026765"
}
''';
        
        // Test validation - should be invalid due to missing frame number
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isFalse);
        
        // Test parsing - should still parse available fields
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals(''));
      });

      test('should handle QR data with empty values', () {
        final qrData = '''
{
  "bien_so_xe": "",
  "so_may": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285"
}
''';
        
        // Test validation - should be invalid due to empty plate number
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isFalse);
        
        // Test parsing - should still parse available fields
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals(''));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });

      test('should handle QR data with null values', () {
        final qrData = '''
{
  "bien_so_xe": null,
  "so_may": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285"
}
''';
        
        // Test validation - should be invalid due to null plate number
        expect(VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData), isFalse);
        
        // Test parsing - should still parse available fields
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        expect(parsedData['vehicle_plate_number'], equals(''));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
      });
    });

    group('UI Mapping Tests', () {
      test('should map basic 3 fields to UI form correctly', () {
        final qrData = '''
{
  "bien_so_xe": "29AA-946.89",
  "so_may": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285"
}
''';
        
        final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
        
        // Verify mapping to UI form fields
        expect(parsedData.containsKey('vehicle_plate_number'), isTrue);
        expect(parsedData.containsKey('vehicle_engine_number'), isTrue);
        expect(parsedData.containsKey('vehicle_frame_number'), isTrue);
        
        // Verify values are correct for UI
        expect(parsedData['vehicle_plate_number'], equals('29AA-946.89'));
        expect(parsedData['vehicle_engine_number'], equals('JK19E2026765'));
        expect(parsedData['vehicle_frame_number'], equals('RLHJK1914PZ012285'));
        
        // Verify other fields are available for user input
        expect(parsedData.containsKey('vehicle_name'), isTrue);
        expect(parsedData.containsKey('vehicle_registration_number'), isTrue);
        expect(parsedData.containsKey('vehicle_registration_place'), isTrue);
        expect(parsedData.containsKey('vehicle_registration_date'), isTrue);
        expect(parsedData.containsKey('collateral_owner'), isTrue);
        expect(parsedData.containsKey('collateral_owner_address'), isTrue);
      });
    });
  });
}
