import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/input_formatters.dart';

void main() {
  group('Input Formatters Tests', () {
    
    group('LeadingZeroRemoverFormatter', () {
      test('should remove leading zeros correctly', () {
        final formatter = LeadingZeroRemoverFormatter();
        
        final testCases = [
          {'input': '000123', 'expected': '123'},
          {'input': '000000', 'expected': '0'},
          {'input': '0000000', 'expected': '0'},
          {'input': '123000', 'expected': '123000'},
          {'input': '0', 'expected': '0'},
          {'input': '123', 'expected': '123'},
          {'input': '', 'expected': ''},
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should cap at 1 billion maximum by default', () {
        final formatter = LeadingZeroRemoverFormatter();
        
        final testCases = [
          {'input': '1000000000', 'expected': '1000000000'}, // Exactly 1 billion - should pass
          {'input': '1000000001', 'expected': ''}, // 1 billion + 1 - should be rejected
          {'input': '2000000000', 'expected': ''}, // 2 billion - should be rejected
          {'input': '999999999', 'expected': '999999999'}, // Just below 1 billion - should pass
          {'input': '500000000', 'expected': '500000000'}, // 500 million - should pass
          {'input': '0', 'expected': '0'}, // Zero - should pass
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should cap at custom maximum value', () {
        final formatter = LeadingZeroRemoverFormatter(maxValue: 500000000); // 500 million
        
        final testCases = [
          {'input': '500000000', 'expected': '500000000'}, // Exactly 500 million - should pass
          {'input': '500000001', 'expected': ''}, // 500 million + 1 - should be rejected
          {'input': '1000000000', 'expected': ''}, // 1 billion - should be rejected
          {'input': '499999999', 'expected': '499999999'}, // Just below 500 million - should pass
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" with max 500M should result in "${testCase['expected']}"');
        }
      });
      
      test('should handle incremental typing', () {
        final formatter = LeadingZeroRemoverFormatter(maxValue: 1000000000);
        
        // Simulate typing "1000000001" character by character
        TextEditingValue currentValue = const TextEditingValue(text: '');
        
        final inputs = ['1', '10', '100', '1000', '10000', '100000', '1000000', '10000000', '100000000', '1000000000', '1000000001'];
        final expected = ['1', '10', '100', '1000', '10000', '100000', '1000000', '10000000', '100000000', '1000000000', '1000000000']; // Last one should be capped
        
        for (int i = 0; i < inputs.length; i++) {
          final result = formatter.formatEditUpdate(
            currentValue,
            TextEditingValue(text: inputs[i]),
          );
          
          expect(result.text, equals(expected[i]), 
            reason: 'Step ${i + 1}: Input "${inputs[i]}" should result in "${expected[i]}"');
          
          currentValue = result;
        }
      });
    });
    
    group('PhoneInputFormatter', () {
      test('should format Vietnamese phone numbers correctly with 01234 567 890 format', () {
        final formatter = PhoneInputFormatter();
        
        final testCases = [
          {'input': '0123456789', 'expected': '0123 456 789'},
          {'input': '012345678', 'expected': '0123 456 78'},
          {'input': '0123456', 'expected': '0123 456'},
          {'input': '012345', 'expected': '0123 45'},
          {'input': '01234', 'expected': '0123 4'},
          {'input': '0123', 'expected': '0123'},
          {'input': '012', 'expected': '012'},
          {'input': '01', 'expected': '01'},
          {'input': '0', 'expected': '0'},
          {'input': '', 'expected': ''},
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should format phone numbers with specific examples from requirements', () {
        final formatter = PhoneInputFormatter();
        
        final testCases = [
          {'input': '0123456789', 'expected': '0123 456 789'}, // Full 10 digits
          {'input': '0987654321', 'expected': '0987 654 321'}, // Another valid example
          {'input': '0912345678', 'expected': '0912 345 678'}, // Another valid example
          {'input': '012345678', 'expected': '0123 456 78'},   // 9 digits
          {'input': '01234567', 'expected': '0123 456 7'},    // 8 digits
          {'input': '0123456', 'expected': '0123 456'},       // 7 digits
          {'input': '012345', 'expected': '0123 45'},         // 6 digits
          {'input': '01234', 'expected': '0123 4'},           // 5 digits
          {'input': '0123', 'expected': '0123'},              // 4 digits
          {'input': '012', 'expected': '012'},                // 3 digits
          {'input': '01', 'expected': '01'},                  // 2 digits
          {'input': '0', 'expected': '0'},                    // 1 digit
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should remove non-digit characters', () {
        final formatter = PhoneInputFormatter();
        
        final testCases = [
          {'input': '0123-456-789', 'expected': '0123 456 789'},
          {'input': '0123.456.789', 'expected': '0123 456 789'},
          {'input': '0123 456 789', 'expected': '0123 456 789'},
          {'input': '0123abc456def789', 'expected': '0123 456 789'},
          {'input': '0123!@#456\$%^789', 'expected': '0123 456 789'},
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should limit to 10 digits', () {
        final formatter = PhoneInputFormatter();
        
        final testCases = [
          {'input': '01234567890', 'expected': '0123 456 789'}, // 11 digits -> 10 digits
          {'input': '012345678901', 'expected': '0123 456 789'}, // 12 digits -> 10 digits
          {'input': '0123456789', 'expected': '0123 456 789'}, // 10 digits -> keep as is
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should handle incremental typing for phone number format', () {
        final formatter = PhoneInputFormatter();
        
        // Simulate typing "0123456789" character by character
        TextEditingValue currentValue = const TextEditingValue(text: '');
        
        final inputs = ['0', '01', '012', '0123', '01234', '012345', '0123456', '01234567', '012345678', '0123456789'];
        final expected = ['0', '01', '012', '0123', '0123 4', '0123 45', '0123 456', '0123 456 7', '0123 456 78', '0123 456 789'];
        
        for (int i = 0; i < inputs.length; i++) {
          final result = formatter.formatEditUpdate(
            currentValue,
            TextEditingValue(text: inputs[i]),
          );
          
          expect(result.text, equals(expected[i]), 
            reason: 'Step ${i + 1}: Input "${inputs[i]}" should result in "${expected[i]}"');
          
          currentValue = result;
        }
      });
      
      test('should auto-add leading 0 when user types without 0', () {
        final formatter = PhoneInputFormatter();
        
        final testCases = [
          {'input': '1', 'expected': '01'},
          {'input': '12', 'expected': '012'},
          {'input': '123', 'expected': '0123'},
          {'input': '1234', 'expected': '0123 4'},
          {'input': '12345', 'expected': '0123 45'},
          {'input': '123456', 'expected': '0123 456'},
          {'input': '1234567', 'expected': '0123 456 7'},
          {'input': '12345678', 'expected': '0123 456 78'},
          {'input': '123456789', 'expected': '0123 456 789'},
          {'input': '987654321', 'expected': '0987 654 321'},
          {'input': '912345678', 'expected': '0912 345 678'},
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should auto-add 0 and result in "${testCase['expected']}"');
        }
      });
      
      test('should not auto-add 0 when user already starts with 0', () {
        final formatter = PhoneInputFormatter();
        
        final testCases = [
          {'input': '01', 'expected': '01'},
          {'input': '012', 'expected': '012'},
          {'input': '0123', 'expected': '0123'},
          {'input': '01234', 'expected': '0123 4'},
          {'input': '012345', 'expected': '0123 45'},
          {'input': '0123456', 'expected': '0123 456'},
          {'input': '01234567', 'expected': '0123 456 7'},
          {'input': '012345678', 'expected': '0123 456 78'},
          {'input': '0123456789', 'expected': '0123 456 789'},
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should not auto-add 0 and result in "${testCase['expected']}"');
        }
      });
      
      test('should handle incremental typing without leading 0', () {
        final formatter = PhoneInputFormatter();
        
        // Simulate typing "123456789" character by character (without 0)
        TextEditingValue currentValue = const TextEditingValue(text: '');
        
        final inputs = ['1', '12', '123', '1234', '12345', '123456', '1234567', '12345678', '123456789'];
        final expected = ['01', '012', '0123', '0123 4', '0123 45', '0123 456', '0123 456 7', '0123 456 78', '0123 456 789'];
        
        for (int i = 0; i < inputs.length; i++) {
          final result = formatter.formatEditUpdate(
            currentValue,
            TextEditingValue(text: inputs[i]),
          );
          
          expect(result.text, equals(expected[i]), 
            reason: 'Step ${i + 1}: Input "${inputs[i]}" should auto-add 0 and result in "${expected[i]}"');
          
          currentValue = result;
        }
      });
    });
    
    group('CurrencyInputFormatter', () {
      test('should format currency with thousand separators', () {
        final formatter = CurrencyInputFormatter();
        
        final testCases = [
          {'input': '1000000', 'expected': '1.000.000'},
          {'input': '10000000', 'expected': '10.000.000'},
          {'input': '100000000', 'expected': '100.000.000'},
          {'input': '1000000000', 'expected': '1.000.000.000'},
          {'input': '1234567', 'expected': '1.234.567'},
          {'input': '12345678', 'expected': '12.345.678'},
          {'input': '123456789', 'expected': '123.456.789'},
          {'input': '0', 'expected': '0'},
          {'input': '123', 'expected': '123'},
          {'input': '1234', 'expected': '1.234'},
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should remove non-digit characters', () {
        final formatter = CurrencyInputFormatter();
        
        final testCases = [
          {'input': '1,000,000', 'expected': '1.000.000'},
          {'input': '1.000.000', 'expected': '1.000.000'},
          {'input': '1 000 000', 'expected': '1.000.000'},
          {'input': '1abc000def000', 'expected': '1.000.000'},
          {'input': '1!@#000\$%^000', 'expected': '1.000.000'},
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should cap at 1 billion maximum by default', () {
        final formatter = CurrencyInputFormatter();
        
        final testCases = [
          {'input': '1000000000', 'expected': '1.000.000.000'}, // Exactly 1 billion - should pass
          {'input': '1000000001', 'expected': ''}, // 1 billion + 1 - should be rejected
          {'input': '2000000000', 'expected': ''}, // 2 billion - should be rejected
          {'input': '999999999', 'expected': '999.999.999'}, // Just below 1 billion - should pass
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" should result in "${testCase['expected']}"');
        }
      });
      
      test('should cap at custom maximum value', () {
        final formatter = CurrencyInputFormatter(maxValue: 500000000); // 500 million
        
        final testCases = [
          {'input': '500000000', 'expected': '500.000.000'}, // Exactly 500 million - should pass
          {'input': '500000001', 'expected': ''}, // 500 million + 1 - should be rejected
          {'input': '1000000000', 'expected': ''}, // 1 billion - should be rejected
          {'input': '499999999', 'expected': '499.999.999'}, // Just below 500 million - should pass
        ];
        
        for (final testCase in testCases) {
          final result = formatter.formatEditUpdate(
            const TextEditingValue(text: ''),
            TextEditingValue(text: testCase['input']!),
          );
          
          expect(result.text, equals(testCase['expected']), 
            reason: 'Input "${testCase['input']}" with max 500M should result in "${testCase['expected']}"');
        }
      });
    });
  });
}
