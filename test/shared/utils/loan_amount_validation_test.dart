import 'package:flutter_test/flutter_test.dart';

/// Unit tests for loan amount validation and formatting
/// 
/// Tests validation rules for:
/// - <PERSON><PERSON><PERSON> tự có (Own Capital): max 1 billion, format xxx,xxx
/// - <PERSON><PERSON> tiền đề nghị vay (Loan Amount): min 1,000,000, max 1 billion, format xxx,xxx
/// - T<PERSON><PERSON> nhu cầu vốn (Total Capital Need): Own Capital + Loan Amount, format xxx,xxx
void main() {
  group('Loan Amount Validation Tests', () {
    
    group('Own Capital Validation', () {
      test('should accept valid own capital amounts', () {
        final validAmounts = [
          '0',
          '1000000',
          '50000000',
          '100000000',
          '500000000',
          '1000000000', // 1 billion
        ];
        
        for (final amount in validAmounts) {
          expect(isValidOwnCapital(amount), isTrue, reason: 'Amount $amount should be valid');
        }
      });
      
      test('should reject invalid own capital amounts', () {
        final invalidAmounts = [
          '-1',
          '1000000001', // Over 1 billion
          '2000000000',
          'abc',
          '',
          '1.5',
          '1,000,000', // Contains comma
        ];
        
        for (final amount in invalidAmounts) {
          expect(isValidOwnCapital(amount), isFalse, reason: 'Amount $amount should be invalid');
        }
      });
      
      test('should format own capital correctly', () {
        final testCases = [
          {'input': '0', 'expected': '0'},
          {'input': '1000000', 'expected': '1,000,000'},
          {'input': '50000000', 'expected': '50,000,000'},
          {'input': '100000000', 'expected': '100,000,000'},
          {'input': '1000000000', 'expected': '1,000,000,000'},
        ];
        
        for (final testCase in testCases) {
          expect(
            formatCurrency(testCase['input']!),
            equals(testCase['expected']),
            reason: 'Input ${testCase['input']} should format to ${testCase['expected']}',
          );
        }
      });
    });
    
    group('Loan Amount Validation', () {
      test('should accept valid loan amounts', () {
        final validAmounts = [
          '1000000', // Exactly minimum
          '5000000',
          '50000000',
          '100000000',
          '500000000',
          '1000000000', // Exactly maximum
        ];
        
        for (final amount in validAmounts) {
          expect(isValidLoanAmount(amount), isTrue, reason: 'Amount $amount should be valid');
        }
      });
      
      test('should reject invalid loan amounts', () {
        final invalidAmounts = [
          '0',
          '999999', // Below minimum
          '500000', // Below minimum
          '1000000001', // Over maximum
          '2000000000', // Over maximum
          'abc',
          '',
          '1.5',
          '1,000,000', // Contains comma
          '-1',
        ];
        
        for (final amount in invalidAmounts) {
          expect(isValidLoanAmount(amount), isFalse, reason: 'Amount $amount should be invalid');
        }
      });
      
      test('should format loan amount correctly', () {
        final testCases = [
          {'input': '1000000', 'expected': '1,000,000'},
          {'input': '5000000', 'expected': '5,000,000'},
          {'input': '50000000', 'expected': '50,000,000'},
          {'input': '100000000', 'expected': '100,000,000'},
          {'input': '1000000000', 'expected': '1,000,000,000'},
        ];
        
        for (final testCase in testCases) {
          expect(
            formatCurrency(testCase['input']!),
            equals(testCase['expected']),
            reason: 'Input ${testCase['input']} should format to ${testCase['expected']}',
          );
        }
      });
    });
    
    group('Total Capital Need Calculation', () {
      test('should calculate total capital need correctly', () {
        final testCases = [
          {'ownCapital': '0', 'loanAmount': '1000000', 'expected': '1,000,000'},
          {'ownCapital': '5000000', 'loanAmount': '1000000', 'expected': '6,000,000'},
          {'ownCapital': '100000000', 'loanAmount': '500000000', 'expected': '600,000,000'},
          {'ownCapital': '500000000', 'loanAmount': '500000000', 'expected': '1,000,000,000'},
          {'ownCapital': '0', 'loanAmount': '0', 'expected': '0'},
        ];
        
        for (final testCase in testCases) {
          final result = calculateTotalCapitalNeed(
            testCase['ownCapital']!,
            testCase['loanAmount']!,
          );
          expect(
            result,
            equals(testCase['expected']),
            reason: 'Own Capital ${testCase['ownCapital']} + Loan Amount ${testCase['loanAmount']} should equal ${testCase['expected']}',
          );
        }
      });
      
      test('should handle edge cases in total capital calculation', () {
        // Test with maximum values
        expect(
          calculateTotalCapitalNeed('1000000000', '1000000000'),
          equals('2,000,000,000'),
        );
        
        // Test with empty strings
        expect(
          calculateTotalCapitalNeed('', ''),
          equals('0'),
        );
        
        // Test with invalid strings
        expect(
          calculateTotalCapitalNeed('abc', 'def'),
          equals('0'),
        );
      });
    });
    
    group('Input Formatting', () {
      test('should remove non-numeric characters', () {
        final testCases = [
          {'input': '1,000,000', 'expected': '1000000'},
          {'input': '1.000.000', 'expected': '1000000'},
          {'input': '1 000 000', 'expected': '1000000'},
          {'input': '1,000,000.00', 'expected': '100000000'},
          {'input': 'abc123def', 'expected': '123'},
        ];
        
        for (final testCase in testCases) {
          expect(
            removeNonNumeric(testCase['input']!),
            equals(testCase['expected']),
            reason: 'Input ${testCase['input']} should clean to ${testCase['expected']}',
          );
        }
      });
      
      test('should remove leading zeros', () {
        final testCases = [
          {'input': '0001000000', 'expected': '1000000'},
          {'input': '0000', 'expected': '0'},
          {'input': '00123', 'expected': '123'},
        ];
        
        for (final testCase in testCases) {
          expect(
            removeLeadingZeros(testCase['input']!),
            equals(testCase['expected']),
            reason: 'Input ${testCase['input']} should remove leading zeros to ${testCase['expected']}',
          );
        }
      });
    });
    
    group('Validation Error Messages', () {
      test('should return correct error messages for own capital', () {
        expect(
          getOwnCapitalErrorMessage('1000000001'),
          equals('Vốn tự có không được vượt quá 1 tỷ VND'),
        );
        
        expect(
          getOwnCapitalErrorMessage('abc'),
          equals('Vốn tự có phải là số hợp lệ'),
        );
        
        expect(
          getOwnCapitalErrorMessage(''),
          equals('Vui lòng nhập vốn tự có'),
        );
      });
      
      test('should return correct error messages for loan amount', () {
        expect(
          getLoanAmountErrorMessage('999999'),
          equals('Số tiền vay tối thiểu 1,000,000 VND'),
        );
        
        expect(
          getLoanAmountErrorMessage('1000000001'),
          equals('Số tiền vay không được vượt quá 1 tỷ VND'),
        );
        
        expect(
          getLoanAmountErrorMessage('abc'),
          equals('Số tiền vay phải là số hợp lệ'),
        );
        
        expect(
          getLoanAmountErrorMessage(''),
          equals('Vui lòng nhập số tiền vay'),
        );
      });
    });
  });
}

/// Validation functions

/// Validate own capital amount
bool isValidOwnCapital(String amount) {
  if (amount.isEmpty) return false;
  
  final cleanAmount = removeNonNumeric(amount);
  if (cleanAmount.isEmpty) return false;
  
  final numericAmount = int.tryParse(cleanAmount);
  if (numericAmount == null) return false;
  
  return numericAmount >= 0 && numericAmount <= 1000000000; // 0 to 1 billion
}

/// Validate loan amount
bool isValidLoanAmount(String amount) {
  if (amount.isEmpty) return false;
  
  final cleanAmount = removeNonNumeric(amount);
  if (cleanAmount.isEmpty) return false;
  
  final numericAmount = int.tryParse(cleanAmount);
  if (numericAmount == null) return false;
  
  return numericAmount >= 1000000 && numericAmount <= 1000000000; // 1M to 1B
}

/// Format currency with thousand separators
String formatCurrency(String amount) {
  final cleanAmount = removeNonNumeric(amount);
  if (cleanAmount.isEmpty || cleanAmount == '0') return '0';
  
  final numericAmount = int.tryParse(cleanAmount);
  if (numericAmount == null) return '0';
  
  return numericAmount.toString().replaceAllMapped(
    RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
    (match) => '${match[1]},',
  );
}

/// Calculate total capital need
String calculateTotalCapitalNeed(String ownCapital, String loanAmount) {
  final ownCapitalNum = int.tryParse(removeNonNumeric(ownCapital)) ?? 0;
  final loanAmountNum = int.tryParse(removeNonNumeric(loanAmount)) ?? 0;
  
  final total = ownCapitalNum + loanAmountNum;
  return formatCurrency(total.toString());
}

/// Remove non-numeric characters
String removeNonNumeric(String input) {
  return input.replaceAll(RegExp(r'[^\d]'), '');
}

/// Remove leading zeros
String removeLeadingZeros(String input) {
  if (input.isEmpty) return '0';
  
  final cleaned = input.replaceFirst(RegExp(r'^0+'), '');
  return cleaned.isEmpty ? '0' : cleaned;
}

/// Get error message for own capital validation
String getOwnCapitalErrorMessage(String amount) {
  if (amount.isEmpty) return 'Vui lòng nhập vốn tự có';
  
  final cleanAmount = removeNonNumeric(amount);
  if (cleanAmount.isEmpty) return 'Vốn tự có phải là số hợp lệ';
  
  final numericAmount = int.tryParse(cleanAmount);
  if (numericAmount == null) return 'Vốn tự có phải là số hợp lệ';
  
  if (numericAmount < 0) return 'Vốn tự có không được âm';
  if (numericAmount > 1000000000) return 'Vốn tự có không được vượt quá 1 tỷ VND';
  
  return '';
}

/// Get error message for loan amount validation
String getLoanAmountErrorMessage(String amount) {
  if (amount.isEmpty) return 'Vui lòng nhập số tiền vay';
  
  final cleanAmount = removeNonNumeric(amount);
  if (cleanAmount.isEmpty) return 'Số tiền vay phải là số hợp lệ';
  
  final numericAmount = int.tryParse(cleanAmount);
  if (numericAmount == null) return 'Số tiền vay phải là số hợp lệ';
  
  if (numericAmount < 1000000) return 'Số tiền vay tối thiểu 1,000,000 VND';
  if (numericAmount > 1000000000) return 'Số tiền vay không được vượt quá 1 tỷ VND';
  
  return '';
}

