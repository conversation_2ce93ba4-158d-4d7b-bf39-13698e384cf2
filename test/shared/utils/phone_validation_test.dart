import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Phone Number Validation Tests', () {
    
    group('Vietnamese Phone Number Validation', () {
      test('should validate 10-digit phone numbers starting with 0', () {
        final validPhoneNumbers = [
          '0323456789', // Viettel
          '0987654321', // Viettel
          '0912345678', // Vinaphone
          '0888888888', // Vinaphone
          '0777777777', // Mobifone
          '0333333333', // Viettel
          '0701234567', // Mobifone
          '0961234567', // Viettel
        ];
        
        for (final phone in validPhoneNumbers) {
          expect(_isValidVietnamesePhone(phone), isTrue, 
            reason: 'Phone number "$phone" should be valid');
        }
      });
      
      test('should reject phone numbers that do not start with 0', () {
        final invalidPhoneNumbers = [
          '1234567890',  // Does not start with 0
          '9876543210',  // Does not start with 0
          '8123456789',  // Does not start with 0
          '9123456789',  // Does not start with 0
        ];
        
        for (final phone in invalidPhoneNumbers) {
          expect(_isValidVietnamesePhone(phone), isFalse, 
            reason: 'Phone number "$phone" should be invalid (does not start with 0)');
        }
      });
      
      test('should reject phone numbers with incorrect length', () {
        final invalidPhoneNumbers = [
          '012345678',   // 9 digits - too short
          '01234567890', // 11 digits - too long
          '01234567',    // 8 digits - too short
          '012345678901', // 12 digits - too long
          '01234',       // 5 digits - too short
        ];
        
        for (final phone in invalidPhoneNumbers) {
          expect(_isValidVietnamesePhone(phone), isFalse, 
            reason: 'Phone number "$phone" should be invalid (incorrect length)');
        }
      });
      
      test('should require exactly 10 digits for valid phone numbers', () {
        // Test all possible lengths from 1 to 15 digits
        final testCases = [
          {'phone': '0', 'expected': false, 'reason': '1 digit - too short'},
          {'phone': '01', 'expected': false, 'reason': '2 digits - too short'},
          {'phone': '012', 'expected': false, 'reason': '3 digits - too short'},
          {'phone': '0123', 'expected': false, 'reason': '4 digits - too short'},
          {'phone': '01234', 'expected': false, 'reason': '5 digits - too short'},
          {'phone': '012345', 'expected': false, 'reason': '6 digits - too short'},
          {'phone': '0123456', 'expected': false, 'reason': '7 digits - too short'},
          {'phone': '01234567', 'expected': false, 'reason': '8 digits - too short'},
          {'phone': '012345678', 'expected': false, 'reason': '9 digits - too short'},
          {'phone': '0323456789', 'expected': true, 'reason': '10 digits - valid'},
          {'phone': '03234567890', 'expected': false, 'reason': '11 digits - too long'},
          {'phone': '032345678901', 'expected': false, 'reason': '12 digits - too long'},
          {'phone': '0323456789012', 'expected': false, 'reason': '13 digits - too long'},
          {'phone': '03234567890123', 'expected': false, 'reason': '14 digits - too long'},
          {'phone': '032345678901234', 'expected': false, 'reason': '15 digits - too long'},
        ];
        
        for (final testCase in testCases) {
          final result = _isValidVietnamesePhone(testCase['phone'] as String);
          expect(result, equals(testCase['expected']), 
            reason: 'Phone number "${testCase['phone']}" should be ${testCase['expected']} (${testCase['reason']})');
        }
      });
      
      test('should validate 10-digit requirement with different prefixes', () {
        final testCases = [
          // Valid 10-digit numbers with different prefixes
          {'phone': '0323456789', 'expected': true, 'reason': '10 digits with 032 prefix'},
          {'phone': '0987654321', 'expected': true, 'reason': '10 digits with 098 prefix'},
          {'phone': '0912345678', 'expected': true, 'reason': '10 digits with 091 prefix'},
          {'phone': '0777777777', 'expected': true, 'reason': '10 digits with 077 prefix'},
          
          // Invalid 9-digit numbers (too short)
          {'phone': '032345678', 'expected': false, 'reason': '9 digits with 032 prefix - too short'},
          {'phone': '098765432', 'expected': false, 'reason': '9 digits with 098 prefix - too short'},
          {'phone': '091234567', 'expected': false, 'reason': '9 digits with 091 prefix - too short'},
          
          // Invalid 11-digit numbers (too long)
          {'phone': '03234567890', 'expected': false, 'reason': '11 digits with 032 prefix - too long'},
          {'phone': '09876543210', 'expected': false, 'reason': '11 digits with 098 prefix - too long'},
          {'phone': '09123456789', 'expected': false, 'reason': '11 digits with 091 prefix - too long'},
        ];
        
        for (final testCase in testCases) {
          final result = _isValidVietnamesePhone(testCase['phone'] as String);
          expect(result, equals(testCase['expected']), 
            reason: 'Phone number "${testCase['phone']}" should be ${testCase['expected']} (${testCase['reason']})');
        }
      });
      
      test('should reject phone numbers with non-digit characters', () {
        final invalidPhoneNumbers = [
          '0123-456-789',
          '0123.456.789',
          '0123 456 789',
          '0123abc456789',
          '0123!@#456789',
        ];
        
        for (final phone in invalidPhoneNumbers) {
          expect(_isValidVietnamesePhone(phone), isFalse, 
            reason: 'Phone number "$phone" should be invalid (contains non-digit characters)');
        }
      });
      
      test('should validate specific Vietnamese mobile number patterns', () {
        // Valid Vietnamese mobile number patterns
        final validPatterns = [
          '032', // Viettel
          '033', // Viettel
          '034', // Viettel
          '035', // Viettel
          '036', // Viettel
          '037', // Viettel
          '038', // Viettel
          '039', // Viettel
          '056', // Viettel
          '058', // Viettel
          '070', // Mobifone
          '076', // Mobifone
          '077', // Mobifone
          '078', // Mobifone
          '079', // Mobifone
          '081', // Mobifone
          '082', // Mobifone
          '083', // Mobifone
          '084', // Mobifone
          '085', // Mobifone
          '088', // Vinaphone
          '091', // Vinaphone
          '094', // Vinaphone
          '096', // Viettel
          '097', // Viettel
          '098', // Viettel
        ];
        
        for (final prefix in validPatterns) {
          final phone = '$prefix${'0' * (10 - prefix.length)}';
          expect(_isValidVietnamesePhone(phone), isTrue, 
            reason: 'Phone number "$phone" should be valid (prefix: $prefix)');
        }
      });
      
      test('should reject invalid Vietnamese mobile number patterns', () {
        final invalidPatterns = [
          '000', // Invalid prefix
          '001', // Invalid prefix
          '002', // Invalid prefix
          '010', // Invalid prefix
          '011', // Invalid prefix
          '012', // Invalid prefix
          '013', // Invalid prefix
          '014', // Invalid prefix
          '015', // Invalid prefix
          '016', // Invalid prefix
          '017', // Invalid prefix
          '018', // Invalid prefix
          '019', // Invalid prefix
          '020', // Invalid prefix
          '021', // Invalid prefix
          '022', // Invalid prefix
          '023', // Invalid prefix
          '024', // Invalid prefix
          '025', // Invalid prefix
          '026', // Invalid prefix
          '027', // Invalid prefix
          '028', // Invalid prefix
          '029', // Invalid prefix
          '030', // Invalid prefix
          '031', // Invalid prefix
        ];
        
        for (final prefix in invalidPatterns) {
          final phone = '$prefix${'0' * (10 - prefix.length)}';
          expect(_isValidVietnamesePhone(phone), isFalse, 
            reason: 'Phone number "$phone" should be invalid (prefix: $prefix)');
        }
      });
    });
    
    group('Phone Number Formatting Validation', () {
      test('should format phone numbers correctly for display', () {
        final testCases = [
          {'input': '0323456789', 'expected': '0323 456 789'},
          {'input': '0987654321', 'expected': '0987 654 321'},
          {'input': '0912345678', 'expected': '0912 345 678'},
        ];
        
        for (final testCase in testCases) {
          final formatted = _formatPhoneNumber(testCase['input']!);
          expect(formatted, equals(testCase['expected']), 
            reason: 'Phone number "${testCase['input']}" should format to "${testCase['expected']}"');
        }
      });
      
      test('should auto-add leading 0 when formatting numbers without 0', () {
        final testCases = [
          {'input': '123456789', 'expected': '0123 456 789'},
          {'input': '987654321', 'expected': '0987 654 321'},
          {'input': '912345678', 'expected': '0912 345 678'},
          {'input': '12345678', 'expected': '0123 456 78'},
          {'input': '1234567', 'expected': '0123 456 7'},
          {'input': '123456', 'expected': '0123 456'},
          {'input': '12345', 'expected': '0123 45'},
          {'input': '1234', 'expected': '0123 4'},
          {'input': '123', 'expected': '0123'},
          {'input': '12', 'expected': '012'},
          {'input': '1', 'expected': '01'},
        ];
        
        for (final testCase in testCases) {
          final formatted = _formatPhoneNumberWithAutoZero(testCase['input']!);
          expect(formatted, equals(testCase['expected']), 
            reason: 'Phone number "${testCase['input']}" should auto-add 0 and format to "${testCase['expected']}"');
        }
      });
    });
  });
}

/// Validates Vietnamese phone number
/// - Must be exactly 10 digits
/// - Must start with 0
/// - Must match valid Vietnamese mobile number patterns
bool _isValidVietnamesePhone(String phone) {
  // Remove all non-digit characters
  final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  // Check length
  if (cleanPhone.length != 10) {
    return false;
  }
  
  // Check if starts with 0
  if (!cleanPhone.startsWith('0')) {
    return false;
  }
  
  // Check for valid Vietnamese mobile number patterns
  final validPatterns = [
    '032', '033', '034', '035', '036', '037', '038', '039', '056', '058', // Viettel
    '070', '076', '077', '078', '079', '081', '082', '083', '084', '085', // Mobifone
    '088', '091', '094', '096', '097', '098', // Vinaphone & Viettel
  ];
  
  final prefix = cleanPhone.substring(0, 3);
  return validPatterns.contains(prefix);
}

/// Formats phone number for display (01234 567 890)
String _formatPhoneNumber(String phone) {
  final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  if (cleanPhone.isEmpty) return '';
  
  if (cleanPhone.length <= 4) {
    return cleanPhone;
  } else if (cleanPhone.length <= 7) {
    return '${cleanPhone.substring(0, 4)} ${cleanPhone.substring(4)}';
  } else {
    return '${cleanPhone.substring(0, 4)} ${cleanPhone.substring(4, 7)} ${cleanPhone.substring(7)}';
  }
}

/// Formats phone number with auto-add leading 0 if not present
String _formatPhoneNumberWithAutoZero(String phone) {
  final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  if (cleanPhone.isEmpty) return '';
  
  // Auto-add leading 0 if not present
  String phoneWithZero = cleanPhone;
  if (!cleanPhone.startsWith('0') && cleanPhone.isNotEmpty) {
    phoneWithZero = '0$cleanPhone';
  }
  
  if (phoneWithZero.length <= 4) {
    return phoneWithZero;
  } else if (phoneWithZero.length <= 7) {
    return '${phoneWithZero.substring(0, 4)} ${phoneWithZero.substring(4)}';
  } else {
    return '${phoneWithZero.substring(0, 4)} ${phoneWithZero.substring(4, 7)} ${phoneWithZero.substring(7)}';
  }
}
