import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/simple_app_logger.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/features/account/models/log_entry.dart';

void main() {
  group('SimpleAppLogger', () {
    late SimpleAppLogger logger;

    setUp(() async {
      await ServiceLocator.setup();
      logger = getIt.get<SimpleAppLogger>();
    });

    tearDown(() async {
      await ServiceLocator.reset();
    });

    test('should be registered in service locator', () {
      expect(ServiceLocator.isRegistered<SimpleAppLogger>(), isTrue);
      expect(ServiceLocator.isRegistered<IAppLogger>(), isTrue);
    });

    test('should initialize successfully', () async {
      await logger.initialize();
      expect(logger.isInitialized, isTrue);
    });

    test('should be initialized in constructor', () async {
      expect(logger.isInitialized, isTrue);
    });

    test('should log info message', () async {
      await logger.i('Test info message');
      // Since we can't easily test console output, we just verify no exception is thrown
      expect(logger.isInitialized, isTrue);
    });

    test('should log debug message', () async {
      await logger.d('Test debug message');
      expect(logger.isInitialized, isTrue);
    });

    test('should log warning message', () async {
      await logger.w('Test warning message');
      expect(logger.isInitialized, isTrue);
    });

    test('should log error message', () async {
      final error = Exception('Test error');
      final stackTrace = StackTrace.current;

      await logger.e('Test error message', error, stackTrace);
      expect(logger.isInitialized, isTrue);
    });

    test('should log verbose message', () async {
      await logger.v('Test verbose message');
      expect(logger.isInitialized, isTrue);
    });

    test('should log with tag', () async {
      await logger.logWithTag('TEST_TAG', 'Test message with tag');
      expect(logger.isInitialized, isTrue);
    });

    test('should log with feature', () async {
      await logger.logWithFeature('test_feature', 'Test message with feature');
      expect(logger.isInitialized, isTrue);
    });

    test('should log with custom level', () async {
      await logger.logWithTag(
        'TEST_TAG',
        'Test message with error level',
        LogLevel.error,
      );
      expect(logger.isInitialized, isTrue);
    });

    test('should log API request', () async {
      await logger.logApiRequest(
        'POST',
        '/api/test',
        headers: {'Content-Type': 'application/json'},
        data: {'test': 'data'},
      );
      expect(logger.isInitialized, isTrue);
    });

    test('should log API response', () async {
      await logger.logApiResponse(200, '/api/test', data: {'success': true});
      expect(logger.isInitialized, isTrue);
    });

    test('should log API error', () async {
      await logger.logApiError(
        'POST',
        '/api/test',
        'Network error',
        responseData: {'error': 'timeout'},
      );
      expect(logger.isInitialized, isTrue);
    });

    test('should log feature initialization', () async {
      await logger.logFeatureInit('TestFeature');
      expect(logger.isInitialized, isTrue);
    });

    test('should log feature success', () async {
      await logger.logFeatureSuccess('TestFeature', 'Initialized successfully');
      expect(logger.isInitialized, isTrue);
    });

    test('should log feature error', () async {
      await logger.logFeatureError('TestFeature', 'Configuration failed');
      expect(logger.isInitialized, isTrue);
    });

    test('should log user action', () async {
      await logger.logUserAction(
        'test_action',
        parameters: {'param1': 'value1'},
      );
      expect(logger.isInitialized, isTrue);
    });

    test('should log navigation', () async {
      await logger.logNavigation(
        'FromScreen',
        'ToScreen',
        arguments: {'arg1': 'value1'},
      );
      expect(logger.isInitialized, isTrue);
    });

    test('should log database operation', () async {
      await logger.logDatabaseOp(
        'INSERT',
        'users',
        data: {'id': 1, 'name': 'Test'},
      );
      expect(logger.isInitialized, isTrue);
    });

    test('should log cache operation', () async {
      await logger.logCacheOp('SET', 'test:key', data: {'value': 'test'});
      expect(logger.isInitialized, isTrue);
    });

    test('should log performance metric', () async {
      await logger.logPerformance(
        'Test operation',
        Duration(milliseconds: 100),
      );
      expect(logger.isInitialized, isTrue);
    });

    test('should mask sensitive data in headers', () async {
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer secret-token-12345',
        'X-API-Key': 'api-key-67890',
      };

      await logger.logApiRequest('POST', '/api/test', headers: headers);
      expect(logger.isInitialized, isTrue);
    });

    test('should mask sensitive data in request/response', () async {
      final data = {
        'email': '<EMAIL>',
        'password': 'secret-password',
        'token': 'access-token-123',
      };

      await logger.logApiRequest('POST', '/api/test', data: data);
      expect(logger.isInitialized, isTrue);
    });

    test('should handle multiple rapid log calls', () async {
      final futures = <Future>[];

      for (int i = 0; i < 10; i++) {
        futures.add(logger.i('Rapid log call $i'));
      }

      await Future.wait(futures);
      expect(logger.isInitialized, isTrue);
    });

    test('should handle concurrent initialization', () async {
      final futures = <Future>[];

      for (int i = 0; i < 5; i++) {
        futures.add(logger.initialize());
      }

      await Future.wait(futures);
      expect(logger.isInitialized, isTrue);
    });
  });

  group('SimpleAppLogger - Service Locator', () {
    setUp(() async {
      await ServiceLocator.setup();
    });

    tearDown(() async {
      await ServiceLocator.reset();
    });

    test('should provide instance via service locator', () {
      final logger1 = getIt.get<SimpleAppLogger>();
      final logger2 = getIt.get<SimpleAppLogger>();
      expect(logger1, isA<SimpleAppLogger>());
      expect(logger2, isA<SimpleAppLogger>());
      expect(identical(logger1, logger2), isTrue); // Should be singleton
    });

    test('should provide IAppLogger interface', () {
      final logger = getIt.get<IAppLogger>();
      expect(logger, isA<SimpleAppLogger>());
    });
  });
}
