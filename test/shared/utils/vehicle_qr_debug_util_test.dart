import 'package:flutter_test/flutter_test.dart';
import 'package:kiloba_biz/shared/utils/vehicle_qr_debug_util.dart';

void main() {
  group('VehicleQRDebugUtil Tests', () {
    
    group('printQRDataDebug', () {
      test('should print debug info for valid QR data', () {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567",
  "so_dang_ky": "DK123456789",
  "noi_cap": "<PERSON>ục Đăng kiểm Việt Nam",
  "ngay_cap": "2023-01-15",
  "chu_xe": "Nguyễn Văn A",
  "dia_chi_chu_xe": "123 Đường ABC, Quận 1, TP.HCM"
}
''';
        
        // This test just ensures the function doesn't throw
        expect(() => VehicleQRDebugUtil.printQRDataDebug(
          qrData: qrData,
          qrFormat: 'QR_CODE',
          qrBoundingBox: 'Rect(0, 0, 100, 100)',
          qrCornerPoints: '[Offset(0, 0), Offset(100, 0), Offset(100, 100), Offset(0, 100)]',
        ), returnsNormally);
      });

      test('should handle invalid JSON gracefully', () {
        final invalidQrData = 'invalid json';
        
        expect(() => VehicleQRDebugUtil.printQRDataDebug(
          qrData: invalidQrData,
          qrFormat: 'QR_CODE',
        ), returnsNormally);
      });

      test('should handle empty QR data', () {
        expect(() => VehicleQRDebugUtil.printQRDataDebug(
          qrData: '',
          qrFormat: 'QR_CODE',
        ), returnsNormally);
      });
    });

    group('generateDebugReport', () {
      test('should generate debug report for valid QR data', () {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        
        expect(report, contains('VEHICLE QR REGISTRATION DEBUG REPORT'));
        expect(report, contains('Raw content: $qrData'));
        expect(report, contains('Content length: ${qrData.length} characters'));
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Required fields: 4/4 groups found'));
        expect(report, contains('Status: ✅ VALID'));
      });

      test('should generate debug report for invalid JSON', () {
        final invalidQrData = 'invalid json';
        
        final report = VehicleQRDebugUtil.generateDebugReport(invalidQrData);
        
        expect(report, contains('VEHICLE QR REGISTRATION DEBUG REPORT'));
        expect(report, contains('Raw content: $invalidQrData'));
        expect(report, contains('Invalid JSON'));
      });

      test('should generate debug report for missing required fields', () {
        final incompleteQrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(incompleteQrData);
        
        expect(report, contains('VEHICLE QR REGISTRATION DEBUG REPORT'));
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Required fields: 2/4 groups found'));
        expect(report, contains('Status: ❌ INVALID'));
      });
    });

    group('saveQRDataToFile', () {
      test('should save QR data to file', () async {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX"
}
''';
        
        final filename = await VehicleQRDebugUtil.saveQRDataToFile(qrData);
        
        expect(filename, isNotEmpty);
        expect(filename, contains('vehicle_qr_data_'));
        expect(filename, endsWith('.json'));
      });

      test('should save QR data with custom filename', () async {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX"
}
''';
        
        final customFilename = 'test_qr_data.json';
        final filename = await VehicleQRDebugUtil.saveQRDataToFile(qrData, filename: customFilename);
        
        expect(filename, equals(customFilename));
      });
    });

    group('Format Detection Tests', () {
      test('should detect Vietnamese format', () {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Status: ✅ VALID'));
      });

      test('should detect English format', () {
        final qrData = '''
{
  "plate_number": "30A-12345",
  "vehicle_name": "Honda Wave RSX",
  "frame_number": "MH1JE1300K1234567",
  "engine_number": "JE1300K1234567"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Status: ✅ VALID'));
      });

      test('should detect nested object format', () {
        final qrData = '''
{
  "thong_tin_xe": {
    "bien_so": "30A-12345",
    "ten_xe": "Honda Wave RSX",
    "so_khung": "MH1JE1300K1234567",
    "so_may": "JE1300K1234567"
  }
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Status: ✅ VALID'));
      });

      test('should detect array format', () {
        final qrData = '''
{
  "xe_may": [
    {
      "bien_so_xe": "30A-12345",
      "ten_xe": "Honda Wave RSX",
      "so_khung": "MH1JE1300K1234567",
      "so_may": "JE1300K1234567"
    }
  ]
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Status: ✅ VALID'));
      });
    });

    group('Field Analysis Tests', () {
      test('should analyze vehicle info fields', () {
        final qrData = '''
{
  "bien_so_xe": "30A-12345",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('bien_so_xe'));
        expect(report, contains('ten_xe'));
        expect(report, contains('so_khung'));
        expect(report, contains('so_may'));
      });

      test('should analyze registration info fields', () {
        final qrData = '''
{
  "so_dang_ky": "DK123456789",
  "noi_cap": "Cục Đăng kiểm Việt Nam",
  "ngay_cap": "2023-01-15"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('so_dang_ky'));
        expect(report, contains('noi_cap'));
        expect(report, contains('ngay_cap'));
      });

      test('should analyze owner info fields', () {
        final qrData = '''
{
  "chu_xe": "Nguyễn Văn A",
  "dia_chi_chu_xe": "123 Đường ABC, Quận 1, TP.HCM"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('chu_xe'));
        expect(report, contains('dia_chi_chu_xe'));
      });
    });

    group('Error Handling Tests', () {
      test('should handle null values gracefully', () {
        final qrData = '''
{
  "bien_so_xe": null,
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Status: ❌ INVALID'));
      });

      test('should handle empty string values gracefully', () {
        final qrData = '''
{
  "bien_so_xe": "",
  "ten_xe": "Honda Wave RSX",
  "so_khung": "MH1JE1300K1234567",
  "so_may": "JE1300K1234567"
}
''';
        
        final report = VehicleQRDebugUtil.generateDebugReport(qrData);
        expect(report, contains('Valid JSON format'));
        expect(report, contains('Status: ❌ INVALID'));
      });

      test('should handle malformed JSON gracefully', () {
        final invalidQrData = 'invalid json data';
        
        final report = VehicleQRDebugUtil.generateDebugReport(invalidQrData);
        expect(report, contains('Invalid JSON'));
      });
    });
  });
}
