import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Enum định nghĩa các môi trường
enum ApiEnvironment { development, local, staging, custom, production }

/// Constants cho API endpoints trong các environment khác nhau
class ApiEndpoints {
  // Private constructor để ngăn khởi tạo
  const ApiEndpoints._();

  // Logger instance cho class
  static final Logger _logger = Logger();

  // ============== DEVELOPMENT URLs ==============
  /// URL backend API cho development (Spring Boot)
  static const String devBackendApiUrl = 'http://************:8190';

  // ============== LOCAL DEVELOPMENT URLs ==============
  /// URL backend API cho local development
  static const String localBackendApiUrl = 'http://localhost:8097';

  // ============== STAGING URLs ==============
  /// URL backend API cho staging
  static const String stagingBackendApiUrl = 'https://api-staging.example.com';

  // ============== CUSTOM DEVELOPMENT URLs ==============
  /// Cho phép developers set URL riêng cho testing
  /// Thay đổi các giá trị này để test với server riêng
  static const String customBackendApiUrl = 'http://*************:8097';

  // ============== ENVIRONMENT DETECTION ==============

  /// Môi trường hiện tại - thay đổi để switch environment trong development
  /// Trong production, luôn sử dụng remote config
  static const ApiEnvironment currentEnvironment = ApiEnvironment.development;

  // ============== URL GETTERS ==============

  /// Lấy backend URL theo environment hiện tại
  static String getBackendUrl() {
    // Trong production build, luôn return null để force sử dụng remote config
    if (kReleaseMode) {
      throw UnsupportedError('Production build must use remote config');
    }

    switch (currentEnvironment) {
      case ApiEnvironment.development:
        return devBackendApiUrl;
      case ApiEnvironment.local:
        return localBackendApiUrl;
      case ApiEnvironment.staging:
        return stagingBackendApiUrl;
      case ApiEnvironment.custom:
        return customBackendApiUrl;
      case ApiEnvironment.production:
        throw UnsupportedError('Production environment must use remote config');
    }
  }

  // ============== UTILITY METHODS ==============

  /// Check xem có đang ở development mode không
  static bool get isDevelopmentMode {
    return kDebugMode || kProfileMode;
  }

  /// Check xem có đang ở production mode không
  static bool get isProductionMode {
    return kReleaseMode;
  }

  /// Lấy thông tin environment hiện tại
  static String get currentEnvironmentName {
    if (isProductionMode) return 'production';
    return currentEnvironment.toString().split('.').last;
  }

  // ============== DEVELOPMENT HELPERS ==============

  /// Helper cho developers để nhanh chóng switch environment
  /// Chỉ hoạt động trong development mode
  static void printAvailableEnvironments() {
    if (!isDevelopmentMode) return;

    _logger.i('=== Available Development Environments ===');
    _logger.i('Current: $currentEnvironmentName');
    _logger.i('');
    _logger.i('Development URLs:');
    _logger.i('  Backend: $devBackendApiUrl');
    _logger.i('');
    _logger.i('Local URLs:');
    _logger.i('  Backend: $localBackendApiUrl');
    _logger.i('');
    _logger.i('Staging URLs:');
    _logger.i('  Backend: $stagingBackendApiUrl');
    _logger.i('');
    _logger.i('Custom URLs:');
    _logger.i('  Backend: $customBackendApiUrl');
    _logger.i('');
    _logger.i('To change environment, update ApiEndpoints.currentEnvironment');
    _logger.i('=========================================');
  }

  /// Validate URL format
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Lấy host từ URL
  static String getHostFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return '${uri.host}:${uri.port}';
    } catch (e) {
      return url;
    }
  }
}
