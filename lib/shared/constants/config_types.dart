/// Constants cho các loại config trong hệ thống
class ConfigTypes {
  // Customer related configs
  static const String CUSTOMER_SOURCE = 'CUST_SOURCE';
  static const String CUSTOMER_STATUS = 'CUST_STATUS';
  static const String WORK_EXPERIENCE = 'WORK_EXPERIENCE';
  static const String SEX = 'SEX';
  static const String MARITAL_STATUS = 'MARITAL_STATUS';
  static const String ID_CARD_TYPE = 'ID_CARD_TYPE';
  
  // Transaction/Loan related configs
  static const String LOAN_TERM_DAYS = 'LOAN_TERM_DAYS';
  static const String LOAN_PURPOSE = 'LOAN_PURPOSE';
  static const String INCOME_SOURCE = 'INCOME_SOURCE';
  static const String ASSET_CONDITION = 'ASSET_CONDITION';
  static const String LOAN_METHOD = 'LOAN_METHOD';
  static const String REPAYMENT_METHOD = 'REPAYMENT_METHOD';
  static const String DISBURSEMENT_METHOD = 'DISBURSEMENT_METHOD';
  static const String HANDOVER_CONDITION = 'HANDOVER_CONDITION';
  
  // Other configs (có thể mở rộng sau)
  static const String POSITION = 'POSITION';
  static const String REGION = 'REGION';
  static const String BRANCH_TYPE = 'BRANCH_TYPE';
  
  // Private constructor để tránh instantiation
  ConfigTypes._();
}
