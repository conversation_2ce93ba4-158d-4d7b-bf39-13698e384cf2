/// Constants cho Storage API
class StorageConstants {
  // File size limits
  static const int maxFileSizeInMB = 50;
  static const int maxImageSizeInMB = 10;
  static const int maxDocumentSizeInMB = 25;

  // Allowed file types
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
  ];

  static const List<String> allowedDocumentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ];

  static const List<String> allowedAllTypes = [
    ...allowedImageTypes,
    ...allowedDocumentTypes,
  ];

  // Folder paths
  static const String documentsFolder = 'documents';
  static const String customersFolder = 'customers';
  static const String employeesFolder = 'employees';
  static const String tempFolder = 'temp';
  static const String idCardsFolder = 'id_cards';
  static const String contractsFolder = 'contracts';
  static const String portraitsFolder = 'portraits';

  // URL types
  static const String urlTypeUpload = 'UPLOAD';
  static const String urlTypeDownload = 'DOWNLOAD';

  // Default expiration times (minutes)
  static const int defaultDownloadUrlExpiration = 60;
  static const int defaultUploadUrlExpiration = 30;
  static const int maxUrlExpiration = 1440; // 24 hours

  // File extensions
  static const List<String> allowedImageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'webp',
  ];

  static const List<String> allowedDocumentExtensions = [
    'pdf',
    'doc',
    'docx',
  ];

  static const List<String> allowedAllExtensions = [
    ...allowedImageExtensions,
    ...allowedDocumentExtensions,
  ];

  // MIME type mappings
  static const Map<String, String> mimeTypeMap = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'webp': 'image/webp',
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  };

  // Helper methods
  static String? getMimeTypeFromExtension(String extension) {
    return mimeTypeMap[extension.toLowerCase()];
  }

  static bool isImageFile(String fileName) {
    final extension = _getFileExtension(fileName);
    return allowedImageExtensions.contains(extension.toLowerCase());
  }

  static bool isDocumentFile(String fileName) {
    final extension = _getFileExtension(fileName);
    return allowedDocumentExtensions.contains(extension.toLowerCase());
  }

  static bool isAllowedFile(String fileName) {
    final extension = _getFileExtension(fileName);
    return allowedAllExtensions.contains(extension.toLowerCase());
  }

  static String _getFileExtension(String filename) {
    final parts = filename.split('.');
    if (parts.length > 1) {
      return parts.last.toLowerCase();
    }
    return '';
  }

  static int getMaxFileSizeInBytes(String fileName) {
    if (isImageFile(fileName)) {
      return maxImageSizeInMB * 1024 * 1024;
    } else if (isDocumentFile(fileName)) {
      return maxDocumentSizeInMB * 1024 * 1024;
    }
    return maxFileSizeInMB * 1024 * 1024;
  }

  static List<String> getAllowedMimeTypes(String fileName) {
    if (isImageFile(fileName)) {
      return allowedImageTypes;
    } else if (isDocumentFile(fileName)) {
      return allowedDocumentTypes;
    }
    return allowedAllTypes;
  }

  // Folder path helpers
  static String createCustomerFolderPath(String customerId) {
    return '$documentsFolder/$customersFolder/$customerId';
  }

  static String createEmployeeFolderPath(String employeeId) {
    return '$documentsFolder/$employeesFolder/$employeeId';
  }

  static String createIdCardFolderPath(String userId) {
    return '$documentsFolder/$customersFolder/$userId/$idCardsFolder';
  }

  static String createContractFolderPath(String userId) {
    return '$documentsFolder/$customersFolder/$userId/$contractsFolder';
  }

  static String createPortraitFolderPath(String userId) {
    return '$documentsFolder/$customersFolder/$userId/$portraitsFolder';
  }

  static String createTempFolderPath(String sessionId) {
    return '$tempFolder/$sessionId';
  }
} 