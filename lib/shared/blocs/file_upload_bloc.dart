import 'dart:async';
import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../utils/app_logger.dart';
import '../services/storage_service.dart';
import '../services/document_service.dart';
import '../models/storage_upload_response.dart';

// Events
abstract class FileUploadEvent extends Equatable {
  const FileUploadEvent();

  @override
  List<Object?> get props => [];
}

class UploadIdPhotoEvent extends FileUploadEvent {
  final String photoType; // 'front' hoặc 'back'
  final String imagePath;
  final Map<String, String>? metadata;
  
  const UploadIdPhotoEvent(this.photoType, this.imagePath, {this.metadata});

  @override
  List<Object?> get props => [photoType, imagePath, metadata];
}

class UploadPortraitEvent extends FileUploadEvent {
  final String imagePath;
  final Map<String, String>? metadata;
  
  const UploadPortraitEvent(this.imagePath, {this.metadata});

  @override
  List<Object?> get props => [imagePath, metadata];
}

class UploadFileWithProgressEvent extends FileUploadEvent {
  final String filePath;
  final String folderPath;
  final String documentType;
  final Map<String, String>? metadata;
  final Function(double)? onProgress;
  
  const UploadFileWithProgressEvent(
    this.filePath,
    this.folderPath,
    this.documentType, {
    this.metadata,
    this.onProgress,
  });

  @override
  List<Object?> get props => [filePath, folderPath, documentType, metadata];
}

class ClearUploadedFilesEvent extends FileUploadEvent {}

class RetryUploadEvent extends FileUploadEvent {
  final String filePath;
  final String documentType;
  
  const RetryUploadEvent(this.filePath, this.documentType);

  @override
  List<Object?> get props => [filePath, documentType];
}

// States
abstract class FileUploadState extends Equatable {
  const FileUploadState();

  @override
  List<Object?> get props => [];
}

class FileUploadInitial extends FileUploadState {}

class FileUploading extends FileUploadState {
  final String filePath;
  final String documentType;
  final double progress;
  final String message;
  
  const FileUploading({
    required this.filePath,
    required this.documentType,
    required this.progress,
    required this.message,
  });

  @override
  List<Object?> get props => [filePath, documentType, progress, message];
}

class FileUploaded extends FileUploadState {
  final String filePath;
  final String documentType;
  final String documentId;
  final StorageUploadResponse uploadResponse;
  
  const FileUploaded({
    required this.filePath,
    required this.documentType,
    required this.documentId,
    required this.uploadResponse,
  });

  @override
  List<Object?> get props => [filePath, documentType, documentId, uploadResponse];
}

class FileUploadError extends FileUploadState {
  final String filePath;
  final String documentType;
  final String message;
  final String? errorCode;
  
  const FileUploadError({
    required this.filePath,
    required this.documentType,
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [filePath, documentType, message, errorCode];
}

class FileUploadCompleted extends FileUploadState {
  final Map<String, String> documentIds; // documentType -> documentId
  final List<StorageUploadResponse> uploadResponses;
  
  const FileUploadCompleted({
    required this.documentIds,
    required this.uploadResponses,
  });

  @override
  List<Object?> get props => [documentIds, uploadResponses];
}

/// BLoC để quản lý file upload state
class FileUploadBloc extends Bloc<FileUploadEvent, FileUploadState> {
  final StorageService _storageService = StorageService();
  final DocumentService _documentService = DocumentService();
  final AppLogger _logger = AppLogger();

  // Track uploaded files
  final Map<String, String> _documentIds = {}; // documentType -> documentId
  final List<StorageUploadResponse> _uploadResponses = [];

  FileUploadBloc() : super(FileUploadInitial()) {
    on<UploadIdPhotoEvent>(_onUploadIdPhoto);
    on<UploadPortraitEvent>(_onUploadPortrait);
    on<UploadFileWithProgressEvent>(_onUploadFileWithProgress);
    on<ClearUploadedFilesEvent>(_onClearUploadedFiles);
    on<RetryUploadEvent>(_onRetryUpload);
  }

  /// Upload ID photo
  Future<void> _onUploadIdPhoto(
    UploadIdPhotoEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      const documentType = 'ID_CARD'; // Sử dụng ID_CARD thay vì ID_CARD_FRONT/BACK
      
      emit(FileUploading(
        filePath: event.imagePath,
        documentType: documentType,
        progress: 0.0,
        message: 'Đang upload ảnh ${event.photoType == 'front' ? 'mặt trước' : 'mặt sau'}...',
      ));

      await _logger.i('Uploading ID photo: ${event.photoType}');

      // Upload file
      final uploadResponse = await _storageService.uploadFile(
        file: File(event.imagePath),
        folderPath: 'id_cards/${DateTime.now().year}/${DateTime.now().month}',
        metadata: {
          'photoType': event.photoType,
          'uploadedAt': DateTime.now().toIso8601String(),
          'userId': 'temp_${DateTime.now().millisecondsSinceEpoch}',
          ...?event.metadata,
        },
      );

      if (!uploadResponse.isSuccess || uploadResponse.data == null) {
        await _logger.e('Upload failed - success: ${uploadResponse.isSuccess}, message: ${uploadResponse.message}, code: ${uploadResponse.code}');
        throw Exception('Upload failed: ${uploadResponse.message} (Code: ${uploadResponse.code})');
      }

      final uploadData = uploadResponse.data!;

      // Insert document record
      final documentResponse = await _documentService.insertDocument(
        documentTypeCode: documentType,
        originalFilename: event.imagePath.split('/').last,
        storedFilename: uploadData.objectName,
        filePath: uploadData.objectName,
        fileSize: uploadData.size,
        mimeType: uploadData.contentType,
        fileExtension: event.imagePath.split('.').last,
        storageType: 'minio',
        metadata: {
          'photoType': event.photoType,
          'uploadResponse': uploadData.toJson(),
          'originalPath': event.imagePath,
        },
      );

      if (!documentResponse.isSuccess || documentResponse.data == null) {
        await _logger.e('Document insertion failed - success: ${documentResponse.isSuccess}, message: ${documentResponse.message}, code: ${documentResponse.code}');
        throw Exception('Document insertion failed: ${documentResponse.message} (Code: ${documentResponse.code})');
      }

      final document = documentResponse.data!;

      // Track uploaded file
      final documentId = document.documentId ?? '';
      _documentIds[documentType] = documentId;
      _uploadResponses.add(uploadData);

      await _logger.i('ID photo uploaded successfully: $documentId');

      emit(FileUploaded(
        filePath: event.imagePath,
        documentType: documentType,
        documentId: documentId,
        uploadResponse: uploadData,
      ));

    } catch (e) {
      await _logger.e('Error uploading ID photo: $e');
      await _logger.e('Error type: ${e.runtimeType}');
      await _logger.e('Error details: ${e.toString()}');
      
      String errorMessage = 'Lỗi upload ảnh ${event.photoType == 'front' ? 'mặt trước' : 'mặt sau'}';
      if (e.toString().contains('Upload failed:')) {
        errorMessage = e.toString();
      } else if (e.toString().contains('Document insertion failed:')) {
        errorMessage = e.toString();
      } else {
        errorMessage = '$errorMessage: $e';
      }
      
      emit(FileUploadError(
        filePath: event.imagePath,
        documentType: 'ID_CARD',
        message: errorMessage,
      ));
    }
  }

  /// Upload portrait photo
  Future<void> _onUploadPortrait(
    UploadPortraitEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      const documentType = 'OTHER_DOC'; // PORTRAIT không có trong danh sách backend
      
      emit(FileUploading(
        filePath: event.imagePath,
        documentType: documentType,
        progress: 0.0,
        message: 'Đang upload ảnh chân dung...',
      ));

      await _logger.i('Uploading portrait photo');

      // Upload file
      final uploadResponse = await _storageService.uploadFile(
        file: File(event.imagePath),
        folderPath: 'portraits/${DateTime.now().year}/${DateTime.now().month}',
        metadata: {
          'uploadedAt': DateTime.now().toIso8601String(),
          'userId': 'temp_${DateTime.now().millisecondsSinceEpoch}',
          ...?event.metadata,
        },
      );

      if (!uploadResponse.isSuccess || uploadResponse.data == null) {
        await _logger.e('Upload failed - success: ${uploadResponse.isSuccess}, message: ${uploadResponse.message}, code: ${uploadResponse.code}');
        throw Exception('Upload failed: ${uploadResponse.message} (Code: ${uploadResponse.code})');
      }

      final uploadData = uploadResponse.data!;

      // Insert document record
      final documentResponse = await _documentService.insertDocument(
        documentTypeCode: documentType,
        originalFilename: event.imagePath.split('/').last,
        storedFilename: uploadData.objectName,
        filePath: uploadData.objectName,
        fileSize: uploadData.size,
        mimeType: uploadData.contentType,
        fileExtension: event.imagePath.split('.').last,
        storageType: 'minio',
        metadata: {
          'uploadResponse': uploadData.toJson(),
          'originalPath': event.imagePath,
        },
      );

      if (!documentResponse.isSuccess || documentResponse.data == null) {
        await _logger.e('Document insertion failed - success: ${documentResponse.isSuccess}, message: ${documentResponse.message}, code: ${documentResponse.code}');
        throw Exception('Document insertion failed: ${documentResponse.message} (Code: ${documentResponse.code})');
      }

      final document = documentResponse.data!;

      // Track uploaded file
      final documentId = document.documentId ?? '';
      _documentIds[documentType] = documentId;
      _uploadResponses.add(uploadData);

      await _logger.i('Portrait uploaded successfully: $documentId');

      emit(FileUploaded(
        filePath: event.imagePath,
        documentType: documentType,
        documentId: documentId,
        uploadResponse: uploadData,
      ));

    } catch (e) {
      await _logger.e('Error uploading portrait: $e');
      await _logger.e('Error type: ${e.runtimeType}');
      await _logger.e('Error details: ${e.toString()}');
      
      String errorMessage = 'Lỗi upload ảnh chân dung';
      if (e.toString().contains('Upload failed:')) {
        errorMessage = e.toString();
      } else if (e.toString().contains('Document insertion failed:')) {
        errorMessage = e.toString();
      } else {
        errorMessage = '$errorMessage: $e';
      }
      
      emit(FileUploadError(
        filePath: event.imagePath,
        documentType: 'OTHER_DOC',
        message: errorMessage,
      ));
    }
  }

  /// Upload file với progress tracking
  Future<void> _onUploadFileWithProgress(
    UploadFileWithProgressEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      emit(FileUploading(
        filePath: event.filePath,
        documentType: event.documentType,
        progress: 0.0,
        message: 'Đang upload file...',
      ));

      await _logger.i('Uploading file with progress: ${event.filePath}');

      // Upload file với progress callback
      // TODO: Fix ProgressCallback type issue
      final uploadResponse = await _storageService.uploadFileWithProgress(
        file: File(event.filePath),
        folderPath: event.folderPath,
        // onProgress: (int progress) {
        //   final double progressValue = progress / 100.0;
        //   event.onProgress?.call(progressValue);
        //   emit(FileUploading(
        //     filePath: event.filePath,
        //     documentType: event.documentType,
        //     progress: progressValue,
        //     message: 'Đang upload... $progress%',
        //   ));
        // },
      );

      if (!uploadResponse.isSuccess || uploadResponse.data == null) {
        await _logger.e('Upload failed - success: ${uploadResponse.isSuccess}, message: ${uploadResponse.message}, code: ${uploadResponse.code}');
        throw Exception('Upload failed: ${uploadResponse.message} (Code: ${uploadResponse.code})');
      }

      final uploadData = uploadResponse.data!;

      // Insert document record
      final documentResponse = await _documentService.insertDocument(
        documentTypeCode: event.documentType,
        originalFilename: event.filePath.split('/').last,
        storedFilename: uploadData.objectName,
        filePath: uploadData.objectName,
        fileSize: uploadData.size,
        mimeType: uploadData.contentType,
        fileExtension: event.filePath.split('.').last,
        storageType: 'minio',
        metadata: {
          'uploadResponse': uploadData.toJson(),
          'originalPath': event.filePath,
          ...?event.metadata,
        },
      );

      if (!documentResponse.isSuccess || documentResponse.data == null) {
        await _logger.e('Document insertion failed - success: ${documentResponse.isSuccess}, message: ${documentResponse.message}, code: ${documentResponse.code}');
        throw Exception('Document insertion failed: ${documentResponse.message} (Code: ${documentResponse.code})');
      }

      final document = documentResponse.data!;

      // Track uploaded file
      final documentId = document.documentId ?? '';
      _documentIds[event.documentType] = documentId;
      _uploadResponses.add(uploadData);

      await _logger.i('File uploaded successfully: $documentId');

      emit(FileUploaded(
        filePath: event.filePath,
        documentType: event.documentType,
        documentId: documentId,
        uploadResponse: uploadData,
      ));

    } catch (e) {
      await _logger.e('Error uploading file: $e');
      await _logger.e('Error type: ${e.runtimeType}');
      await _logger.e('Error details: ${e.toString()}');
      
      String errorMessage = 'Lỗi upload file';
      if (e.toString().contains('Upload failed:')) {
        errorMessage = e.toString();
      } else if (e.toString().contains('Document insertion failed:')) {
        errorMessage = e.toString();
      } else {
        errorMessage = '$errorMessage: $e';
      }
      
      emit(FileUploadError(
        filePath: event.filePath,
        documentType: event.documentType,
        message: errorMessage,
      ));
    }
  }

  /// Clear uploaded files
  Future<void> _onClearUploadedFiles(
    ClearUploadedFilesEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      await _logger.i('Clearing uploaded files');
      
      _documentIds.clear();
      _uploadResponses.clear();
      
      emit(FileUploadInitial());
    } catch (e) {
      await _logger.e('Error clearing uploaded files: $e');
    }
  }

  /// Retry upload
  Future<void> _onRetryUpload(
    RetryUploadEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    try {
      await _logger.i('Retrying upload: ${event.filePath}');
      
      // Re-upload file
      add(UploadFileWithProgressEvent(
        event.filePath,
        'retry/${DateTime.now().year}/${DateTime.now().month}',
        event.documentType,
      ));
      
    } catch (e) {
      await _logger.e('Error retrying upload: $e');
    }
  }

  /// Get uploaded document IDs
  Map<String, String> get documentIds => Map.from(_documentIds);

  /// Get upload responses
  List<StorageUploadResponse> get uploadResponses => List.from(_uploadResponses);

  /// Check if all required files are uploaded
  bool get isAllFilesUploaded {
    return _documentIds.containsKey('ID_CARD');
  }

  @override
  Future<void> close() {
    _logger.i('FileUploadBloc disposed');
    return super.close();
  }
}
