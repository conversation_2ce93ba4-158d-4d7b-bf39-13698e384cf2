import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';

/// Database helper riêng cho logging system
/// Quản lý SQLite database tách biệt với database nghiệp vụ ch<PERSON>h
class LogDatabaseHelper {
  static const String _databaseName = 'kiloba_logs.db';
  static const int _databaseVersion = 1;

  static Database? _database;

  /// Private constructor cho singleton pattern
  LogDatabaseHelper._privateConstructor();
  static final LogDatabaseHelper instance =
      LogDatabaseHelper._privateConstructor();

  /// Get database instance với lazy initialization
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// Initialize database riêng cho logs
  Future<Database> _initDatabase() async {
    try {
      // Sử dụng path riêng cho log database
      final path = join(await getDatabasesPath(), _databaseName);

      if (kDebugMode) {
        debugPrint('Log database path: $path');
      }

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _createLogTables,
        onUpgrade: _upgradeLogDatabase,
        onOpen: (db) async {
          try {
            // Enable foreign key constraints
            await db.execute('PRAGMA foreign_keys = ON');

            // Basic optimization settings that work on all platforms
            await db.execute('PRAGMA synchronous = NORMAL');
            await db.execute('PRAGMA cache_size = 10000');
            await db.execute('PRAGMA temp_store = MEMORY');

            if (kDebugMode) {
              debugPrint('Log database opened successfully');
            }
          } catch (e) {
            if (kDebugMode) {
              debugPrint('Warning: Some PRAGMA settings failed: $e');
            }
            // Continue anyway, database will still work
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing log database: $e');
      }
      rethrow;
    }
  }

  /// Create log tables
  Future<void> _createLogTables(Database db, int version) async {
    await db.transaction((txn) async {
      // Create app_logs table
      await txn.execute('''
        CREATE TABLE app_logs (
          id TEXT PRIMARY KEY,
          level TEXT NOT NULL,
          message TEXT NOT NULL,
          tag TEXT NULL,
          feature TEXT NULL,
          timestamp INTEGER NOT NULL,
          error_data TEXT NULL,
          stack_trace TEXT NULL,
          metadata TEXT NULL,
          user_id TEXT NULL,
          session_id TEXT NULL,
          device_info TEXT NULL,
          
          CONSTRAINT chk_level CHECK (level IN ('verbose', 'debug', 'info', 'warning', 'error')),
          CONSTRAINT chk_feature CHECK (feature IN ('auth', 'api', 'database', 'ui', 'system', 'custom', 'app', 'general'))
        )
      ''');

      // Create log_stats table for caching statistics
      await txn.execute('''
        CREATE TABLE log_stats (
          id TEXT PRIMARY KEY,
          stat_type TEXT NOT NULL,
          stat_value TEXT NOT NULL,
          updated_at INTEGER NOT NULL
        )
      ''');

      // Create performance indexes
      await _createLogIndexes(txn);
    });

    if (kDebugMode) {
      debugPrint('Log database tables created successfully');
    }
  }

  /// Create performance indexes cho log database
  Future<void> _createLogIndexes(Transaction txn) async {
    // Primary index cho timestamp (most common query)
    await txn.execute('''
      CREATE INDEX idx_logs_timestamp ON app_logs(timestamp DESC)
    ''');

    // Index cho level filtering
    await txn.execute('''
      CREATE INDEX idx_logs_level ON app_logs(level, timestamp DESC)
    ''');

    // Index cho feature filtering
    await txn.execute('''
      CREATE INDEX idx_logs_feature ON app_logs(feature, timestamp DESC)
    ''');

    // Index cho tag searching
    await txn.execute('''
      CREATE INDEX idx_logs_tag ON app_logs(tag, timestamp DESC)
    ''');

    // Composite index cho common queries
    await txn.execute('''
      CREATE INDEX idx_logs_level_feature ON app_logs(level, feature, timestamp DESC)
    ''');

    // Partial index cho error logs (high priority)
    await txn.execute('''
      CREATE INDEX idx_logs_errors ON app_logs(timestamp DESC) 
      WHERE level = 'error'
    ''');

    // Index cho user filtering
    await txn.execute('''
      CREATE INDEX idx_logs_user ON app_logs(user_id, timestamp DESC)
      WHERE user_id IS NOT NULL
    ''');

    if (kDebugMode) {
      debugPrint('Log database indexes created successfully');
    }
  }

  /// Handle database upgrades
  Future<void> _upgradeLogDatabase(
    Database db,
    int oldVersion,
    int newVersion,
  ) async {
    if (kDebugMode) {
      debugPrint(
        'Upgrading log database from version $oldVersion to $newVersion',
      );
    }

    for (int version = oldVersion + 1; version <= newVersion; version++) {
      await _migrateLogDatabaseToVersion(db, version);
    }
  }

  /// Migrate to specific version
  Future<void> _migrateLogDatabaseToVersion(Database db, int version) async {
    switch (version) {
      default:
        if (kDebugMode) {
          debugPrint('No migration needed for log database version $version');
        }
    }
  }

  /// Close log database
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
      if (kDebugMode) {
        debugPrint('Log database closed');
      }
    }
  }

  /// Delete log database file
  Future<void> deleteDatabase() async {
    try {
      final path = join(await getDatabasesPath(), _databaseName);
      await databaseFactory.deleteDatabase(path);
      _database = null;
      if (kDebugMode) {
        debugPrint('Log database deleted successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error deleting log database: $e');
      }
      rethrow;
    }
  }

  /// Get log database info
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    final db = await database;

    try {
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table'",
      );

      final logCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM app_logs',
      );

      final errorCount = await db.rawQuery(
        "SELECT COUNT(*) as count FROM app_logs WHERE level = 'error'",
      );

      final version = await db.getVersion();

      return {
        'version': version,
        'tables': tables.map((t) => t['name']).toList(),
        'total_logs': logCount.first['count'],
        'error_logs': errorCount.first['count'],
        'database_path': db.path,
        'database_name': _databaseName,
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting log database info: $e');
      }
      return {'error': e.toString()};
    }
  }

  /// Perform log database maintenance
  Future<void> performMaintenance() async {
    final db = await database;

    try {
      // Vacuum database to reclaim space
      await db.execute('VACUUM');

      // Update table statistics
      await db.execute('ANALYZE');

      // Rebuild indexes
      await db.execute('REINDEX');

      if (kDebugMode) {
        debugPrint('Log database maintenance completed');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error performing log database maintenance: $e');
      }
      rethrow;
    }
  }

  /// Check database integrity
  Future<bool> checkIntegrity() async {
    final db = await database;

    try {
      final result = await db.rawQuery('PRAGMA integrity_check');
      final isOk = result.isNotEmpty && result.first.values.first == 'ok';

      if (kDebugMode) {
        debugPrint('Log database integrity check: ${isOk ? 'PASS' : 'FAIL'}');
      }

      return isOk;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking log database integrity: $e');
      }
      return false;
    }
  }

  /// Get database size
  Future<int> getDatabaseSize() async {
    try {
      final path = join(await getDatabasesPath(), _databaseName);
      final file = File(path);
      if (await file.exists()) {
        return await file.length();
      }
      return 0;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting log database size: $e');
      }
      return 0;
    }
  }
}
