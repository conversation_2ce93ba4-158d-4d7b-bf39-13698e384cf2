// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DocumentModel _$DocumentModelFromJson(Map<String, dynamic> json) =>
    DocumentModel(
      documentId: json['documentId'] as String?,
      documentTypeCode: json['documentTypeCode'] as String?,
      originalFilename: json['originalFilename'] as String?,
      storedFilename: json['storedFilename'] as String?,
      filePath: json['filePath'] as String?,
      fileSize: (json['fileSize'] as num?)?.toInt(),
      mimeType: json['mimeType'] as String?,
      fileExtension: json['fileExtension'] as String?,
      storageType: json['storageType'] as String?,
      status: json['status'] as String?,
      createdBy: json['createdBy'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      checksum: json['checksum'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$DocumentModelToJson(DocumentModel instance) =>
    <String, dynamic>{
      'documentId': instance.documentId,
      'documentTypeCode': instance.documentTypeCode,
      'originalFilename': instance.originalFilename,
      'storedFilename': instance.storedFilename,
      'filePath': instance.filePath,
      'fileSize': instance.fileSize,
      'mimeType': instance.mimeType,
      'fileExtension': instance.fileExtension,
      'storageType': instance.storageType,
      'status': instance.status,
      'createdBy': instance.createdBy,
      'createdAt': instance.createdAt?.toIso8601String(),
      'checksum': instance.checksum,
      'metadata': instance.metadata,
    };
