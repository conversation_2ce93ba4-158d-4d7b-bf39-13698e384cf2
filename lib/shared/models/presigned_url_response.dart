import 'package:json_annotation/json_annotation.dart';

part 'presigned_url_response.g.dart';

@JsonSerializable()
class PresignedUrlResponse {
  final String url;
  final String objectName;
  final String urlType;
  final DateTime expirationTime;
  final int expirationMinutes;

  const PresignedUrlResponse({
    required this.url,
    required this.objectName,
    required this.urlType,
    required this.expirationTime,
    required this.expirationMinutes,
  });

  factory PresignedUrlResponse.fromJson(Map<String, dynamic> json) =>
      _$PresignedUrlResponseFromJson(json);

  Map<String, dynamic> toJson() => _$PresignedUrlResponseToJson(this);

  PresignedUrlResponse copyWith({
    String? url,
    String? objectName,
    String? urlType,
    DateTime? expirationTime,
    int? expirationMinutes,
  }) {
    return PresignedUrlResponse(
      url: url ?? this.url,
      objectName: objectName ?? this.objectName,
      urlType: urlType ?? this.urlType,
      expirationTime: expirationTime ?? this.expirationTime,
      expirationMinutes: expirationMinutes ?? this.expirationMinutes,
    );
  }

  /// Check if URL is expired
  bool get isExpired => DateTime.now().isAfter(expirationTime);

  /// Get remaining time in minutes
  int get remainingMinutes {
    final now = DateTime.now();
    if (now.isAfter(expirationTime)) return 0;
    return expirationTime.difference(now).inMinutes;
  }

  @override
  String toString() {
    return 'PresignedUrlResponse(objectName: $objectName, urlType: $urlType, expirationMinutes: $expirationMinutes, isExpired: $isExpired)';
  }
} 