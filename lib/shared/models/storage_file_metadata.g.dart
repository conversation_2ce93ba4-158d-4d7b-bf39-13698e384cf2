// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'storage_file_metadata.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StorageFileMetadata _$StorageFileMetadataFromJson(Map<String, dynamic> json) =>
    StorageFileMetadata(
      objectName: json['objectName'] as String,
      fileName: json['fileName'] as String,
      contentType: json['contentType'] as String,
      size: (json['size'] as num).toInt(),
      lastModified: DateTime.parse(json['lastModified'] as String),
      etag: json['etag'] as String,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$StorageFileMetadataToJson(
  StorageFileMetadata instance,
) => <String, dynamic>{
  'objectName': instance.objectName,
  'fileName': instance.fileName,
  'contentType': instance.contentType,
  'size': instance.size,
  'lastModified': instance.lastModified.toIso8601String(),
  'etag': instance.etag,
  'metadata': instance.metadata,
};
