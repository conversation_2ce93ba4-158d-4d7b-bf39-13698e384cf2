import 'package:json_annotation/json_annotation.dart';

part 'document_model.g.dart';

@JsonSerializable()
@JsonSerializable(fieldRename: FieldRename.snake)
class DocumentModel {
  final String? documentId;
  final String? documentTypeCode;
  final String? originalFilename;
  final String? storedFilename;
  final String? filePath;
  final int? fileSize;
  final String? mimeType;
  final String? fileExtension;
  final String? storageType;
  final String? status;
  final String? createdBy;
  final DateTime? createdAt;
  final String? checksum;
  final Map<String, dynamic>? metadata;

  const DocumentModel({
    this.documentId,
    this.documentTypeCode,
    this.originalFilename,
    this.storedFilename,
    this.filePath,
    this.fileSize,
    this.mimeType,
    this.fileExtension,
    this.storageType,
    this.status,
    this.createdBy,
    this.createdAt,
    this.checksum,
    this.metadata,
  });

  factory DocumentModel.fromJson(Map<String, dynamic> json) =>
      _$DocumentModelFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentModelToJson(this);

  DocumentModel copyWith({
    String? documentId,
    String? documentTypeCode,
    String? originalFilename,
    String? storedFilename,
    String? filePath,
    int? fileSize,
    String? mimeType,
    String? fileExtension,
    String? storageType,
    String? status,
    String? createdBy,
    DateTime? createdAt,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) {
    return DocumentModel(
      documentId: documentId ?? this.documentId,
      documentTypeCode: documentTypeCode ?? this.documentTypeCode,
      originalFilename: originalFilename ?? this.originalFilename,
      storedFilename: storedFilename ?? this.storedFilename,
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      mimeType: mimeType ?? this.mimeType,
      fileExtension: fileExtension ?? this.fileExtension,
      storageType: storageType ?? this.storageType,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      checksum: checksum ?? this.checksum,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'DocumentModel(documentId: $documentId, documentTypeCode: $documentTypeCode, originalFilename: $originalFilename, status: $status)';
  }
} 