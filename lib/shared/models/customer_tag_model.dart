import 'package:json_annotation/json_annotation.dart';

part 'customer_tag_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class CustomerTagModel {
  final String? id;
  final String? name;
  final String? description;
  final String? color;

  const CustomerTagModel({
    this.id,
    this.name,
    this.description,
    this.color,
  });

  factory CustomerTagModel.fromJson(Map<String, dynamic> json) =>
      _$CustomerTagModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerTagModelToJson(this);

  CustomerTagModel copyWith({
    String? id,
    String? name,
    String? description,
    String? color,
  }) {
    return CustomerTagModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
    );
  }

  @override
  String toString() {
    return 'CustomerTagModel(id: $id, name: $name, color: $color)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerTagModel &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        other.color == color;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      color,
    );
  }
}
