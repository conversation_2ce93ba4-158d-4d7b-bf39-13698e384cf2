// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'presigned_url_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PresignedUrlResponse _$PresignedUrlResponseFromJson(
  Map<String, dynamic> json,
) => PresignedUrlResponse(
  url: json['url'] as String,
  objectName: json['objectName'] as String,
  urlType: json['urlType'] as String,
  expirationTime: DateTime.parse(json['expirationTime'] as String),
  expirationMinutes: (json['expirationMinutes'] as num).toInt(),
);

Map<String, dynamic> _$PresignedUrlResponseToJson(
  PresignedUrlResponse instance,
) => <String, dynamic>{
  'url': instance.url,
  'objectName': instance.objectName,
  'urlType': instance.urlType,
  'expirationTime': instance.expirationTime.toIso8601String(),
  'expirationMinutes': instance.expirationMinutes,
};
