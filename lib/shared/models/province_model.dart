import 'package:json_annotation/json_annotation.dart';

part 'province_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class ProvinceModel {
  final String id;
  final String gsoCode;
  final String name;

  const ProvinceModel({
    required this.id,
    required this.gsoCode,
    required this.name,
  });

  factory ProvinceModel.fromJson(Map<String, dynamic> json) =>
      _$ProvinceModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProvinceModelToJson(this);

  ProvinceModel copyWith({
    String? id,
    String? gsoCode,
    String? name,
  }) {
    return ProvinceModel(
      id: id ?? this.id,
      gsoCode: gsoCode ?? this.gsoCode,
      name: name ?? this.name,
    );
  }

  @override
  String toString() {
    return 'ProvinceModel(id: $id, gsoCode: $gsoCode, name: $name)';
  }
} 