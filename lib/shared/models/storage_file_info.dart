import 'package:json_annotation/json_annotation.dart';

part 'storage_file_info.g.dart';

@JsonSerializable()
class StorageFileInfo {
  final String objectName;
  final String fileName;
  final String contentType;
  final int size;
  final DateTime lastModified;

  const StorageFileInfo({
    required this.objectName,
    required this.fileName,
    required this.contentType,
    required this.size,
    required this.lastModified,
  });

  factory StorageFileInfo.fromJson(Map<String, dynamic> json) =>
      _$StorageFileInfoFromJson(json);

  Map<String, dynamic> toJson() => _$StorageFileInfoToJson(this);

  StorageFileInfo copyWith({
    String? objectName,
    String? fileName,
    String? contentType,
    int? size,
    DateTime? lastModified,
  }) {
    return StorageFileInfo(
      objectName: objectName ?? this.objectName,
      fileName: fileName ?? this.fileName,
      contentType: contentType ?? this.contentType,
      size: size ?? this.size,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  @override
  String toString() {
    return 'StorageFileInfo(objectName: $objectName, fileName: $fileName, size: $size, contentType: $contentType)';
  }
} 