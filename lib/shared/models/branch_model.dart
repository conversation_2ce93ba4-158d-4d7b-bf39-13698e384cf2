import 'package:json_annotation/json_annotation.dart';

part 'branch_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class BranchModel {
  final String id;
  final String code;
  final String name;
  final String? address;
  final String provinceId;
  final String? provinceName;
  final String? regionId;
  final String? regionCode;
  final String? regionName;

  const BranchModel({
    required this.id,
    required this.code,
    required this.name,
    this.address,
    required this.provinceId,
    this.provinceName,
    this.regionId,
    this.regionCode,
    this.regionName,
  });

  factory BranchModel.fromJson(Map<String, dynamic> json) =>
      _$BranchModelFromJson(json);

  Map<String, dynamic> toJson() => _$BranchModelToJson(this);

  BranchModel copyWith({
    String? id,
    String? code,
    String? name,
    String? address,
    String? provinceId,
    String? provinceName,
    String? regionId,
    String? regionCode,
    String? regionName,
  }) {
    return BranchModel(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      address: address ?? this.address,
      provinceId: provinceId ?? this.provinceId,
      provinceName: provinceName ?? this.provinceName,
      regionId: regionId ?? this.regionId,
      regionCode: regionCode ?? this.regionCode,
      regionName: regionName ?? this.regionName,
    );
  }

  @override
  String toString() {
    return 'BranchModel(id: $id, code: $code, name: $name, provinceName: $provinceName, regionName: $regionName)';
  }
} 