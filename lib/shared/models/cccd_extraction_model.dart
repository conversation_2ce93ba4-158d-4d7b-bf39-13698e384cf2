/// Model cho thông tin trích xuất từ CCCD
class CccdExtractionModel {
  final String idNumber;
  final String fullName;
  final String dateOfBirth;
  final String gender;
  final String issueDate;
  final String issuePlace;
  final String expiryDate;
  final String nationality;
  final String placeOfOrigin;
  final String placeOfResidence;
  final double confidence;
  final String extractionTime;
  final bool isExpired;
  final int daysUntilExpiry;
  final List<String> mrzLines;
  final List<String> warnings;

  const CccdExtractionModel({
    required this.idNumber,
    required this.fullName,
    required this.dateOfBirth,
    required this.gender,
    required this.issueDate,
    required this.issuePlace,
    required this.expiryDate,
    required this.nationality,
    required this.placeOfOrigin,
    required this.placeOfResidence,
    required this.confidence,
    required this.extractionTime,
    required this.isExpired,
    required this.daysUntilExpiry,
    required this.mrzLines,
    required this.warnings,
  });

  factory CccdExtractionModel.fromJson(Map<String, dynamic> json) {
    return CccdExtractionModel(
      idNumber: json['idNumber'] ?? '',
      fullName: json['fullName'] ?? '',
      dateOfBirth: json['dateOfBirth'] ?? '',
      gender: json['gender'] ?? '',
      issueDate: json['issueDate'] ?? '',
      issuePlace: json['issuePlace'] ?? '',
      expiryDate: json['expiryDate'] ?? '',
      nationality: json['nationality'] ?? '',
      placeOfOrigin: json['placeOfOrigin'] ?? '',
      placeOfResidence: json['placeOfResidence'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      extractionTime: json['extractionTime'] ?? '',
      isExpired: json['isExpired'] ?? false,
      daysUntilExpiry: json['daysUntilExpiry'] ?? 0,
      mrzLines: List<String>.from(json['mrzLines'] ?? []),
      warnings: List<String>.from(json['warnings'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'idNumber': idNumber,
      'fullName': fullName,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'issueDate': issueDate,
      'issuePlace': issuePlace,
      'expiryDate': expiryDate,
      'nationality': nationality,
      'placeOfOrigin': placeOfOrigin,
      'placeOfResidence': placeOfResidence,
      'confidence': confidence,
      'extractionTime': extractionTime,
      'isExpired': isExpired,
      'daysUntilExpiry': daysUntilExpiry,
      'mrzLines': mrzLines,
      'warnings': warnings,
    };
  }

  @override
  String toString() {
    return 'CccdExtractionModel(idNumber: $idNumber, fullName: $fullName, dateOfBirth: $dateOfBirth, gender: $gender, issueDate: $issueDate, issuePlace: $issuePlace, expiryDate: $expiryDate, nationality: $nationality, placeOfOrigin: $placeOfOrigin, placeOfResidence: $placeOfResidence, confidence: $confidence, extractionTime: $extractionTime, isExpired: $isExpired, daysUntilExpiry: $daysUntilExpiry, mrzLines: $mrzLines, warnings: $warnings)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CccdExtractionModel &&
        other.idNumber == idNumber &&
        other.fullName == fullName &&
        other.dateOfBirth == dateOfBirth &&
        other.gender == gender &&
        other.issueDate == issueDate &&
        other.issuePlace == issuePlace &&
        other.expiryDate == expiryDate &&
        other.nationality == nationality &&
        other.placeOfOrigin == placeOfOrigin &&
        other.placeOfResidence == placeOfResidence &&
        other.confidence == confidence &&
        other.extractionTime == extractionTime &&
        other.isExpired == isExpired &&
        other.daysUntilExpiry == daysUntilExpiry &&
        other.mrzLines == mrzLines &&
        other.warnings == warnings;
  }

  @override
  int get hashCode {
    return idNumber.hashCode ^
        fullName.hashCode ^
        dateOfBirth.hashCode ^
        gender.hashCode ^
        issueDate.hashCode ^
        issuePlace.hashCode ^
        expiryDate.hashCode ^
        nationality.hashCode ^
        placeOfOrigin.hashCode ^
        placeOfResidence.hashCode ^
        confidence.hashCode ^
        extractionTime.hashCode ^
        isExpired.hashCode ^
        daysUntilExpiry.hashCode ^
        mrzLines.hashCode ^
        warnings.hashCode;
  }
}

/// Model cho request trích xuất CCCD
class CccdExtractionRequest {
  final String frontImage;
  final String backImage;
  final String model;
  final int maxTokens;
  final double temperature;

  const CccdExtractionRequest({
    required this.frontImage,
    required this.backImage,
    this.model = 'gemini-2.0-flash-001',
    this.maxTokens = 1000,
    this.temperature = 0.1,
  });

  Map<String, dynamic> toJson() {
    return {
      'frontImage': frontImage,
      'backImage': backImage,
      'model': model,
      'maxTokens': maxTokens,
      'temperature': temperature,
    };
  }

  @override
  String toString() {
    return 'CccdExtractionRequest(frontImage: ${frontImage.length} chars, backImage: ${backImage.length} chars, model: $model, maxTokens: $maxTokens, temperature: $temperature)';
  }
}

/// Model cho response API trích xuất CCCD
class CccdExtractionResponse {
  final bool success;
  final CccdExtractionModel? data;
  final String message;
  final String? requestId;
  final int? responseTime;

  const CccdExtractionResponse({
    required this.success,
    this.data,
    required this.message,
    this.requestId,
    this.responseTime,
  });

  factory CccdExtractionResponse.fromJson(Map<String, dynamic> json) {
    return CccdExtractionResponse(
      success: json['success'] ?? false,
      data: json['data'] != null ? CccdExtractionModel.fromJson(json['data']) : null,
      message: json['message'] ?? '',
      requestId: json['requestId'],
      responseTime: json['responseTime'],
    );
  }

  @override
  String toString() {
    return 'CccdExtractionResponse(success: $success, data: $data, message: $message, requestId: $requestId, responseTime: $responseTime)';
  }
} 