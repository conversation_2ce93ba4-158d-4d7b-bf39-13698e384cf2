import 'package:json_annotation/json_annotation.dart';

part 'assigned_manager_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class AssignedManagerModel {
  final String? id;
  final String? fullName;
  final String? cifNo;

  const AssignedManagerModel({
    this.id,
    this.fullName,
    this.cifNo,
  });

  factory AssignedManagerModel.fromJson(Map<String, dynamic> json) =>
      _$AssignedManagerModelFromJson(json);

  Map<String, dynamic> toJson() => _$AssignedManagerModelToJson(this);

  AssignedManagerModel copyWith({
    String? id,
    String? fullName,
    String? cifNo,
  }) {
    return AssignedManagerModel(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      cifNo: cifNo ?? this.cifNo,
    );
  }

  @override
  String toString() {
    return 'AssignedManagerModel(id: $id, fullName: $fullName, cifNo: $cifNo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssignedManagerModel && other.id == id;
  }

  @override
  int get hashCode => id?.hashCode ?? 0;
}
