// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'storage_upload_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StorageUploadResponse _$StorageUploadResponseFromJson(
  Map<String, dynamic> json,
) => StorageUploadResponse(
  objectName: json['objectName'] as String,
  fileName: json['fileName'] as String,
  contentType: json['contentType'] as String,
  size: (json['size'] as num).toInt(),
  etag: json['etag'] as String,
  uploadTime: DateTime.parse(json['uploadTime'] as String),
  downloadUrl: json['downloadUrl'] as String?,
);

Map<String, dynamic> _$StorageUploadResponseToJson(
  StorageUploadResponse instance,
) => <String, dynamic>{
  'objectName': instance.objectName,
  'fileName': instance.fileName,
  'contentType': instance.contentType,
  'size': instance.size,
  'etag': instance.etag,
  'uploadTime': instance.uploadTime.toIso8601String(),
  'downloadUrl': instance.downloadUrl,
};
