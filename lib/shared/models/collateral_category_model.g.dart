// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'collateral_category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CollateralCategoryMetadata _$CollateralCategoryMetadataFromJson(
  Map<String, dynamic> json,
) => CollateralCategoryMetadata(
  requiresValuation: json['requiresValuation'] as bool?,
  depreciationRate: (json['depreciationRate'] as num?)?.toDouble(),
  maxLtvRatio: (json['maxLtvRatio'] as num?)?.toDouble(),
);

Map<String, dynamic> _$CollateralCategoryMetadataToJson(
  CollateralCategoryMetadata instance,
) => <String, dynamic>{
  'requiresValuation': instance.requiresValuation,
  'depreciationRate': instance.depreciationRate,
  'maxLtvRatio': instance.maxLtvRatio,
};

CollateralCategoryModel _$CollateralCategoryModelFromJson(
  Map<String, dynamic> json,
) => CollateralCategoryModel(
  id: json['id'] as String?,
  code: json['code'] as String?,
  name: json['name'] as String?,
  description: json['description'] as String?,
  displayOrder: (json['displayOrder'] as num?)?.toInt(),
  metadata: json['metadata'] == null
      ? null
      : CollateralCategoryMetadata.fromJson(
          json['metadata'] as Map<String, dynamic>,
        ),
  createdAt: json['createdAt'] as String?,
  updatedAt: json['updatedAt'] as String?,
);

Map<String, dynamic> _$CollateralCategoryModelToJson(
  CollateralCategoryModel instance,
) => <String, dynamic>{
  'id': instance.id,
  'code': instance.code,
  'name': instance.name,
  'description': instance.description,
  'displayOrder': instance.displayOrder,
  'metadata': instance.metadata,
  'createdAt': instance.createdAt,
  'updatedAt': instance.updatedAt,
};

CollateralCategoriesResponse _$CollateralCategoriesResponseFromJson(
  Map<String, dynamic> json,
) => CollateralCategoriesResponse(
  collateralCategories: (json['collateralCategories'] as List<dynamic>)
      .map((e) => CollateralCategoryModel.fromJson(e as Map<String, dynamic>))
      .toList(),
);

Map<String, dynamic> _$CollateralCategoriesResponseToJson(
  CollateralCategoriesResponse instance,
) => <String, dynamic>{'collateralCategories': instance.collateralCategories};
