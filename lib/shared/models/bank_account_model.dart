import 'package:json_annotation/json_annotation.dart';

part 'bank_account_model.g.dart';

/// Model cho Bank Account
@JsonSerializable()
class BankAccountModel {
  @JsonKey(name: 'accountNo')
  final String accountNo;
  
  @JsonKey(name: 'accountName')
  final String accountName;

  const BankAccountModel({
    required this.accountNo,
    required this.accountName,
  });

  /// Factory constructor từ JSON
  factory BankAccountModel.fromJson(Map<String, dynamic> json) =>
      _$BankAccountModelFromJson(json);

  /// Convert sang JSON
  Map<String, dynamic> toJson() => _$BankAccountModelToJson(this);

  /// Copy with method
  BankAccountModel copyWith({
    String? accountNo,
    String? accountName,
  }) {
    return BankAccountModel(
      accountNo: accountNo ?? this.accountNo,
      accountName: accountName ?? this.accountName,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BankAccountModel &&
        other.accountNo == accountNo &&
        other.accountName == accountName;
  }

  @override
  int get hashCode => accountNo.hashCode ^ accountName.hashCode;

  @override
  String toString() {
    return 'BankAccountModel(accountNo: $accountNo, accountName: $accountName)';
  }

  /// Display name cho UI
  String get displayName => '$accountNo - $accountName';
  
  /// Short display name cho dropdown
  String get shortDisplayName => accountNo;
}
