import 'package:json_annotation/json_annotation.dart';

part 'presigned_url_request.g.dart';

@JsonSerializable()
class PresignedUrlRequest {
  final String objectName;
  final String urlType;
  final int? expirationMinutes;

  const PresignedUrlRequest({
    required this.objectName,
    required this.urlType,
    this.expirationMinutes,
  });

  factory PresignedUrlRequest.fromJson(Map<String, dynamic> json) =>
      _$PresignedUrlRequestFromJson(json);

  Map<String, dynamic> toJson() => _$PresignedUrlRequestToJson(this);

  PresignedUrlRequest copyWith({
    String? objectName,
    String? urlType,
    int? expirationMinutes,
  }) {
    return PresignedUrlRequest(
      objectName: objectName ?? this.objectName,
      urlType: urlType ?? this.urlType,
      expirationMinutes: expirationMinutes ?? this.expirationMinutes,
    );
  }

  @override
  String toString() {
    return 'PresignedUrlRequest(objectName: $objectName, urlType: $urlType, expirationMinutes: $expirationMinutes)';
  }
} 