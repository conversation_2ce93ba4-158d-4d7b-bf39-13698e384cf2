import 'package:json_annotation/json_annotation.dart';

part 'position_model.g.dart';

@JsonSerializable()
@JsonSerializable(fieldRename: FieldRename.snake)
class PositionModel {
  final String id;
  final String code;
  final String name;

  const PositionModel({
    required this.id,
    required this.code,
    required this.name,
  });

  factory PositionModel.fromJson(Map<String, dynamic> json) =>
      _$PositionModelFromJson(json);

  Map<String, dynamic> toJson() => _$PositionModelToJson(this);

  PositionModel copyWith({
    String? id,
    String? code,
    String? name,
  }) {
    return PositionModel(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
    );
  }

  @override
  String toString() {
    return 'PositionModel(id: $id, code: $code, name: $name)';
  }
} 