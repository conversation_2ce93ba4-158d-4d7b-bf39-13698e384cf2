import 'package:json_annotation/json_annotation.dart';

part 'storage_file_metadata.g.dart';

@JsonSerializable()
class StorageFileMetadata {
  final String objectName;
  final String fileName;
  final String contentType;
  final int size;
  final DateTime lastModified;
  final String etag;
  final Map<String, dynamic> metadata;

  const StorageFileMetadata({
    required this.objectName,
    required this.fileName,
    required this.contentType,
    required this.size,
    required this.lastModified,
    required this.etag,
    this.metadata = const {},
  });

  factory StorageFileMetadata.fromJson(Map<String, dynamic> json) =>
      _$StorageFileMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$StorageFileMetadataToJson(this);

  StorageFileMetadata copyWith({
    String? objectName,
    String? fileName,
    String? contentType,
    int? size,
    DateTime? lastModified,
    String? etag,
    Map<String, dynamic>? metadata,
  }) {
    return StorageFileMetadata(
      objectName: objectName ?? this.objectName,
      fileName: fileName ?? this.fileName,
      contentType: contentType ?? this.contentType,
      size: size ?? this.size,
      lastModified: lastModified ?? this.lastModified,
      etag: etag ?? this.etag,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'StorageFileMetadata(objectName: $objectName, fileName: $fileName, size: $size, contentType: $contentType)';
  }
} 