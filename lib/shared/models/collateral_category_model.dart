import 'package:json_annotation/json_annotation.dart';

part 'collateral_category_model.g.dart';

/// Model cho Collateral Category metadata
@JsonSerializable()
class CollateralCategoryMetadata {
  /// <PERSON><PERSON> yêu cầu định giá không
  final bool? requiresValuation;
  
  /// Tỷ lệ khấu hao
  final double? depreciationRate;
  
  /// Tỷ lệ LTV tối đa
  final double? maxLtvRatio;

  const CollateralCategoryMetadata({
    this.requiresValuation,
    this.depreciationRate,
    this.maxLtvRatio,
  });

  factory CollateralCategoryMetadata.fromJson(Map<String, dynamic> json) =>
      _$CollateralCategoryMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$CollateralCategoryMetadataToJson(this);

  @override
  String toString() {
    return 'CollateralCategoryMetadata('
        'requiresValuation: $requiresValuation, '
        'depreciationRate: $depreciationRate, '
        'maxLtvRatio: $maxLtvRatio'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is CollateralCategoryMetadata &&
        other.requiresValuation == requiresValuation &&
        other.depreciationRate == depreciationRate &&
        other.maxLtvRatio == maxLtvRatio;
  }

  @override
  int get hashCode {
    return (requiresValuation?.hashCode ?? 0) ^
        (depreciationRate?.hashCode ?? 0) ^
        (maxLtvRatio?.hashCode ?? 0);
  }
}

/// Model cho Collateral Category
@JsonSerializable()
class CollateralCategoryModel {
  /// ID của category
  final String? id;
  
  /// Mã code của category
  final String? code;
  
  /// Tên hiển thị của category
  final String? name;
  
  /// Mô tả chi tiết
  final String? description;
  
  /// Thứ tự hiển thị
  final int? displayOrder;
  
  /// Metadata chứa thông tin bổ sung
  final CollateralCategoryMetadata? metadata;
  
  /// Thời gian tạo
  final String? createdAt;
  
  /// Thời gian cập nhật
  final String? updatedAt;

  const CollateralCategoryModel({
    this.id,
    this.code,
    this.name,
    this.description,
    this.displayOrder,
    this.metadata,
    this.createdAt,
    this.updatedAt,
  });

  factory CollateralCategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CollateralCategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CollateralCategoryModelToJson(this);

  /// Copy with method
  CollateralCategoryModel copyWith({
    String? id,
    String? code,
    String? name,
    String? description,
    int? displayOrder,
    CollateralCategoryMetadata? metadata,
    String? createdAt,
    String? updatedAt,
  }) {
    return CollateralCategoryModel(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      displayOrder: displayOrder ?? this.displayOrder,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CollateralCategoryModel('
        'id: $id, '
        'code: $code, '
        'name: $name, '
        'description: $description, '
        'displayOrder: $displayOrder, '
        'metadata: $metadata, '
        'createdAt: $createdAt, '
        'updatedAt: $updatedAt'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is CollateralCategoryModel &&
        other.id == id &&
        other.code == code &&
        other.name == name &&
        other.description == description &&
        other.displayOrder == displayOrder &&
        other.metadata == metadata &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return (id?.hashCode ?? 0) ^
        (code?.hashCode ?? 0) ^
        (name?.hashCode ?? 0) ^
        (description?.hashCode ?? 0) ^
        (displayOrder?.hashCode ?? 0) ^
        (metadata?.hashCode ?? 0) ^
        (createdAt?.hashCode ?? 0) ^
        (updatedAt?.hashCode ?? 0);
  }
}

/// Model cho response chứa danh sách collateral categories
@JsonSerializable()
class CollateralCategoriesResponse {
  /// Danh sách các collateral categories
  final List<CollateralCategoryModel> collateralCategories;

  const CollateralCategoriesResponse({
    required this.collateralCategories,
  });

  factory CollateralCategoriesResponse.fromJson(Map<String, dynamic> json) =>
      _$CollateralCategoriesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CollateralCategoriesResponseToJson(this);

  /// Copy with method
  CollateralCategoriesResponse copyWith({
    List<CollateralCategoryModel>? collateralCategories,
  }) {
    return CollateralCategoriesResponse(
      collateralCategories: collateralCategories ?? this.collateralCategories,
    );
  }

  @override
  String toString() {
    return 'CollateralCategoriesResponse('
        'collateralCategories: ${collateralCategories.length} items'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is CollateralCategoriesResponse &&
        other.collateralCategories == collateralCategories;
  }

  @override
  int get hashCode => collateralCategories.hashCode;
}
