enum AppThemeMode {
  system('system', '<PERSON> hệ thống'),
  light('light', 'Sáng'),
  dark('dark', 'Tối');

  const AppThemeMode(this.value, this.displayName);

  final String value;
  final String displayName;

  static AppThemeMode fromValue(String value) {
    switch (value) {
      case 'system':
        return AppThemeMode.system;
      case 'light':
        return AppThemeMode.light;
      case 'dark':
        return AppThemeMode.dark;
      default:
        return AppThemeMode.system;
    }
  }

  bool get isSystem => this == AppThemeMode.system;
  bool get isLight => this == AppThemeMode.light;
  bool get isDark => this == AppThemeMode.dark;
} 