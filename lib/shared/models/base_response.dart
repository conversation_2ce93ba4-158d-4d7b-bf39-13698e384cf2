import 'package:json_annotation/json_annotation.dart';

part 'base_response.g.dart';

/// Base response model cho tất cả API responses
/// Hỗ trợ generic type T để type-safe
@JsonSerializable(genericArgumentFactories: true)
class BaseResponse<T> {
  /// Trạng thái thành công/thất bại
  final bool success;
  
  /// Mã code từ server (VD: "200000" cho thành công)
  final String code;
  
  /// Thông báo từ server
  final String message;
  
  /// Dữ liệu response (generic type T)
  final T? data;

  BaseResponse({
    required this.success,
    required this.code,
    required this.message,
    this.data,
  });

  /// Factory constructor cho success response
  factory BaseResponse.success({
    required T data,
    required String code,
    required String message,
  }) {
    return BaseResponse<T>(
      success: true,
      code: code,
      message: message,
      data: data,
    );
  }

  /// Factory constructor cho failure response
  factory BaseResponse.failure({
    required String code,
    required String message,
    T? data,
  }) {
    return BaseResponse<T>(
      success: false,
      code: code,
      message: message,
      data: data,
    );
  }

  /// Factory constructor từ JSON
  factory BaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) {
    return _$BaseResponseFromJson<T>(json, fromJsonT);
  }

  /// Convert to JSON
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$BaseResponseToJson<T>(this, toJsonT);
  }

  /// Kiểm tra có phải response thành công
  bool get isSuccess => success;

  /// Kiểm tra có phải response thất bại
  bool get isFailure => !success;

  /// Kiểm tra có data không
  bool get hasData => data != null;

  /// Kiểm tra response có hợp lệ không
  bool get isValid {
    return code.isNotEmpty && message.isNotEmpty;
  }

  /// Copy với thông tin mới
  BaseResponse<T> copyWith({
    bool? success,
    String? code,
    String? message,
    T? data,
  }) {
    return BaseResponse<T>(
      success: success ?? this.success,
      code: code ?? this.code,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  @override
  String toString() {
    return 'BaseResponse<$T>('
        'success: $success, '
        'code: $code, '
        'message: $message, '
        'hasData: $hasData'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is BaseResponse<T> &&
        other.success == success &&
        other.code == code &&
        other.message == message &&
        other.data == data;
  }

  @override
  int get hashCode {
    return success.hashCode ^
        code.hashCode ^
        message.hashCode ^
        data.hashCode;
  }
}

