import 'package:json_annotation/json_annotation.dart';

part 'config_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class ConfigModel {
  final String? id;
  final String? groupCode;
  final String? code;
  final String? label;
  final String? value;
  final String? description;
  final int? orderNo;

  const ConfigModel({
    this.id,
    this.groupCode,
    this.code,
    this.label,
    this.value,
    this.description,
    this.orderNo,
  });

  factory ConfigModel.fromJson(Map<String, dynamic> json) =>
      _$ConfigModelFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigModelToJson(this);

  ConfigModel copyWith({
    String? id,
    String? groupCode,
    String? code,
    String? label,
    String? value,
    String? description,
    int? orderNo,
  }) {
    return ConfigModel(
      id: id ?? this.id,
      groupCode: groupCode ?? this.groupCode,
      code: code ?? this.code,
      label: label ?? this.label,
      value: value ?? this.value,
      description: description ?? this.description,
      orderNo: orderNo ?? this.orderNo,
    );
  }

  @override
  String toString() {
    return 'ConfigModel(id: $id, groupCode: $groupCode, code: $code, label: $label, value: $value)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ConfigModel &&
        other.id == id &&
        other.groupCode == groupCode &&
        other.code == code &&
        other.label == label &&
        other.value == value &&
        other.description == description &&
        other.orderNo == orderNo;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      groupCode,
      code,
      label,
      value,
      description,
      orderNo,
    );
  }
}
