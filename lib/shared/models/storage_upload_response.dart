import 'package:json_annotation/json_annotation.dart';

part 'storage_upload_response.g.dart';

@JsonSerializable()
class StorageUploadResponse {
  final String objectName;
  final String fileName;
  final String contentType;
  final int size;
  final String etag;
  final DateTime uploadTime;
  final String? downloadUrl;

  const StorageUploadResponse({
    required this.objectName,
    required this.fileName,
    required this.contentType,
    required this.size,
    required this.etag,
    required this.uploadTime,
    this.downloadUrl,
  });

  factory StorageUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$StorageUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$StorageUploadResponseToJson(this);

  StorageUploadResponse copyWith({
    String? objectName,
    String? fileName,
    String? contentType,
    int? size,
    String? etag,
    DateTime? uploadTime,
    String? downloadUrl,
  }) {
    return StorageUploadResponse(
      objectName: objectName ?? this.objectName,
      fileName: fileName ?? this.fileName,
      contentType: contentType ?? this.contentType,
      size: size ?? this.size,
      etag: etag ?? this.etag,
      uploadTime: uploadTime ?? this.uploadTime,
      downloadUrl: downloadUrl ?? this.downloadUrl,
    );
  }

  @override
  String toString() {
    return 'StorageUploadResponse(objectName: $objectName, fileName: $fileName, size: $size, contentType: $contentType)';
  }
} 