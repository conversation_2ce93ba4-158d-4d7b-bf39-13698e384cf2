import 'package:json_annotation/json_annotation.dart';

part 'region_model.g.dart';

@JsonSerializable()
@JsonSerializable(fieldRename: FieldRename.snake)
class RegionModel {
  final String id;
  final String code;
  final String name;
  final String? description;

  const RegionModel({
    required this.id,
    required this.code,
    required this.name,
    this.description,
  });

  factory RegionModel.fromJson(Map<String, dynamic> json) =>
      _$RegionModelFromJson(json);

  Map<String, dynamic> toJson() => _$RegionModelToJson(this);

  RegionModel copyWith({
    String? id,
    String? code,
    String? name,
    String? description,
  }) {
    return RegionModel(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
    );
  }

  @override
  String toString() {
    return 'RegionModel(id: $id, code: $code, name: $name, description: $description)';
  }
} 