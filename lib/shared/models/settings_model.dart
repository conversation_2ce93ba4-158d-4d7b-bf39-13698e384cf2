import 'app_theme_mode.dart';

class SettingsModel {
  final AppThemeMode themeMode;
  final bool notificationsEnabled;
  final bool biometricEnabled;
  final String language;
  final bool rememberLogin;

  const SettingsModel({
    this.themeMode = AppThemeMode.system,
    this.notificationsEnabled = true,
    this.biometricEnabled = false,
    this.language = 'vi',
    this.rememberLogin = false,
  });

  SettingsModel copyWith({
    AppThemeMode? themeMode,
    bool? notificationsEnabled,
    bool? biometricEnabled,
    String? language,
    bool? rememberLogin,
  }) {
    return SettingsModel(
      themeMode: themeMode ?? this.themeMode,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      language: language ?? this.language,
      rememberLogin: rememberLogin ?? this.rememberLogin,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.value,
      'notificationsEnabled': notificationsEnabled,
      'biometricEnabled': biometricEnabled,
      'language': language,
      'rememberLogin': rememberLogin,
    };
  }

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      themeMode: AppThemeMode.fromValue(json['themeMode'] ?? 'system'),
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      biometricEnabled: json['biometricEnabled'] ?? false,
      language: json['language'] ?? 'vi',
      rememberLogin: json['rememberLogin'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SettingsModel &&
        other.themeMode == themeMode &&
        other.notificationsEnabled == notificationsEnabled &&
        other.biometricEnabled == biometricEnabled &&
        other.language == language &&
        other.rememberLogin == rememberLogin;
  }

  @override
  int get hashCode {
    return Object.hash(
      themeMode,
      notificationsEnabled,
      biometricEnabled,
      language,
      rememberLogin,
    );
  }

  @override
  String toString() {
    return 'SettingsModel(themeMode: $themeMode, notificationsEnabled: $notificationsEnabled, biometricEnabled: $biometricEnabled, language: $language, rememberLogin: $rememberLogin)';
  }
} 