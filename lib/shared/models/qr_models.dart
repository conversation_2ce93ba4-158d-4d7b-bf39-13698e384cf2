// Models for QR code parsing and data mapping
// 
// This file contains all the data models used for QR code parsing
// including CCCD QR data and Vehicle QR data models.

/// Model for CCCD QR parsed data
class CccdQrData {
  final String? idNumber;           // Số CCCD
  final String? fullName;           // Họ và tên
  final String? dateOfBirth;        // Ngày sinh
  final String? gender;             // Giới tính
  final String? nationality;        // Quốc tịch
  final String? placeOfOrigin;      // Quê quán
  final String? placeOfResidence;   // Nơi thường trú
  final String? personalId;         // Số CMND/CCCD cũ
  final String? issueDate;          // Ngày cấp
  final String? issuePlace;         // Nơi cấp
  final String? expiryDate;         // Ngày hết hạn

  const CccdQrData({
    this.idNumber,
    this.fullName,
    this.dateOfBirth,
    this.gender,
    this.nationality,
    this.placeOfOrigin,
    this.placeOfResidence,
    this.personalId,
    this.issueDate,
    this.issuePlace,
    this.expiryDate,
  });

  /// Create CccdQrData from Map
  factory CccdQrData.fromMap(Map<String, String?> map) {
    return CccdQrData(
      idNumber: map['id_number'],
      fullName: map['full_name'],
      dateOfBirth: map['date_of_birth'],
      gender: map['gender'],
      nationality: map['nationality'],
      placeOfOrigin: map['place_of_origin'],
      placeOfResidence: map['place_of_residence'],
      personalId: map['personal_id'],
      issueDate: map['issue_date'],
      issuePlace: map['issue_place'],
      expiryDate: map['expiry_date'],
    );
  }

  /// Convert to Map
  Map<String, String?> toMap() {
    return {
      'id_number': idNumber,
      'full_name': fullName,
      'date_of_birth': dateOfBirth,
      'gender': gender,
      'nationality': nationality,
      'place_of_origin': placeOfOrigin,
      'place_of_residence': placeOfResidence,
      'personal_id': personalId,
      'issue_date': issueDate,
      'issue_place': issuePlace,
      'expiry_date': expiryDate,
    };
  }

  /// Check if essential fields are present
  bool get isValid {
    return (idNumber?.isNotEmpty ?? false) && 
           (fullName?.isNotEmpty ?? false) && 
           (dateOfBirth?.isNotEmpty ?? false);
  }

  @override
  String toString() {
    return 'CccdQrData(idNumber: $idNumber, fullName: $fullName, dateOfBirth: $dateOfBirth)';
  }
}

/// Model for Vehicle QR parsed data
class VehicleQrData {
  final String? plateNumber;              // Biển kiểm soát
  final String? engineNumber;             // Số máy
  final String? frameNumber;              // Số khung
  final String? vehicleName;              // Tên xe
  final String? registrationNumber;       // Số đăng ký
  final String? registrationPlace;        // Nơi đăng ký
  final String? registrationDate;         // Ngày đăng ký
  final String? collateralOwner;          // Chủ thế chấp
  final String? collateralOwnerAddress;   // Địa chỉ chủ thế chấp

  const VehicleQrData({
    this.plateNumber,
    this.engineNumber,
    this.frameNumber,
    this.vehicleName,
    this.registrationNumber,
    this.registrationPlace,
    this.registrationDate,
    this.collateralOwner,
    this.collateralOwnerAddress,
  });

  /// Create VehicleQrData from Map
  factory VehicleQrData.fromMap(Map<String, String?> map) {
    return VehicleQrData(
      plateNumber: map['vehicle_plate_number'],
      engineNumber: map['vehicle_engine_number'],
      frameNumber: map['vehicle_frame_number'],
      vehicleName: map['vehicle_name'],
      registrationNumber: map['vehicle_registration_number'],
      registrationPlace: map['vehicle_registration_place'],
      registrationDate: map['vehicle_registration_date'],
      collateralOwner: map['collateral_owner'],
      collateralOwnerAddress: map['collateral_owner_address'],
    );
  }

  /// Convert to Map
  Map<String, String?> toMap() {
    return {
      'vehicle_plate_number': plateNumber,
      'vehicle_engine_number': engineNumber,
      'vehicle_frame_number': frameNumber,
      'vehicle_name': vehicleName,
      'vehicle_registration_number': registrationNumber,
      'vehicle_registration_place': registrationPlace,
      'vehicle_registration_date': registrationDate,
      'collateral_owner': collateralOwner,
      'collateral_owner_address': collateralOwnerAddress,
    };
  }

  /// Check if essential fields are present
  bool get isValid {
    return (plateNumber?.isNotEmpty ?? false) && 
           (engineNumber?.isNotEmpty ?? false) && 
           (frameNumber?.isNotEmpty ?? false);
  }

  @override
  String toString() {
    return 'VehicleQrData(plateNumber: $plateNumber, engineNumber: $engineNumber, frameNumber: $frameNumber)';
  }
}

/// Model for Borrower form data mapping
class BorrowerFormData {
  final String? fullName;
  final String? idNumber;
  final String? dateOfBirth;
  final String? gender;
  final String? nationality;
  final String? placeOfOrigin;
  final String? placeOfResidence;
  final String? personalId;
  final String? issueDate;
  final String? issuePlace;
  final String? expiryDate;

  const BorrowerFormData({
    this.fullName,
    this.idNumber,
    this.dateOfBirth,
    this.gender,
    this.nationality,
    this.placeOfOrigin,
    this.placeOfResidence,
    this.personalId,
    this.issueDate,
    this.issuePlace,
    this.expiryDate,
  });

  /// Create BorrowerFormData from CccdQrData
  factory BorrowerFormData.fromCccdQr(CccdQrData cccdData) {
    return BorrowerFormData(
      fullName: cccdData.fullName,
      idNumber: cccdData.idNumber,
      dateOfBirth: cccdData.dateOfBirth,
      gender: cccdData.gender,
      nationality: cccdData.nationality,
      placeOfOrigin: cccdData.placeOfOrigin,
      placeOfResidence: cccdData.placeOfResidence,
      personalId: cccdData.personalId,
      issueDate: cccdData.issueDate,
      issuePlace: cccdData.issuePlace,
      expiryDate: cccdData.expiryDate,
    );
  }

  /// Convert to Map for form mapping
  Map<String, String?> toFormMap() {
    return {
      'borrower_full_name': fullName,
      'borrower_id_number': idNumber,
      'borrower_date_of_birth': dateOfBirth,
      'borrower_gender': gender,
      'borrower_nationality': nationality,
      'borrower_place_of_origin': placeOfOrigin,
      'borrower_place_of_residence': placeOfResidence,
      'borrower_personal_id': personalId,
      'borrower_issue_date': issueDate,
      'borrower_issue_place': issuePlace,
      'borrower_expiry_date': expiryDate,
    };
  }
}

/// Model for Co-borrower form data mapping
class CoBorrowerFormData {
  final String? fullName;
  final String? idNumber;
  final String? dateOfBirth;
  final String? gender;
  final String? nationality;
  final String? placeOfOrigin;
  final String? placeOfResidence;
  final String? personalId;
  final String? issueDate;
  final String? issuePlace;
  final String? expiryDate;

  const CoBorrowerFormData({
    this.fullName,
    this.idNumber,
    this.dateOfBirth,
    this.gender,
    this.nationality,
    this.placeOfOrigin,
    this.placeOfResidence,
    this.personalId,
    this.issueDate,
    this.issuePlace,
    this.expiryDate,
  });

  /// Create CoBorrowerFormData from CccdQrData
  factory CoBorrowerFormData.fromCccdQr(CccdQrData cccdData) {
    return CoBorrowerFormData(
      fullName: cccdData.fullName,
      idNumber: cccdData.idNumber,
      dateOfBirth: cccdData.dateOfBirth,
      gender: cccdData.gender,
      nationality: cccdData.nationality,
      placeOfOrigin: cccdData.placeOfOrigin,
      placeOfResidence: cccdData.placeOfResidence,
      personalId: cccdData.personalId,
      issueDate: cccdData.issueDate,
      issuePlace: cccdData.issuePlace,
      expiryDate: cccdData.expiryDate,
    );
  }

  /// Convert to Map for form mapping
  Map<String, String?> toFormMap() {
    return {
      'co_borrower_full_name': fullName,
      'co_borrower_id_number': idNumber,
      'co_borrower_date_of_birth': dateOfBirth,
      'co_borrower_gender': gender,
      'co_borrower_nationality': nationality,
      'co_borrower_place_of_origin': placeOfOrigin,
      'co_borrower_place_of_residence': placeOfResidence,
      'co_borrower_personal_id': personalId,
      'co_borrower_issue_date': issueDate,
      'co_borrower_issue_place': issuePlace,
      'co_borrower_expiry_date': expiryDate,
    };
  }
}

/// Model for Customer form data mapping
class CustomerFormData {
  final String? fullName;
  final String? idNumber;
  final String? dateOfBirth;
  final String? gender;
  final String? nationality;
  final String? placeOfOrigin;
  final String? placeOfResidence;
  final String? personalId;
  final String? issueDate;
  final String? issuePlace;
  final String? expiryDate;

  const CustomerFormData({
    this.fullName,
    this.idNumber,
    this.dateOfBirth,
    this.gender,
    this.nationality,
    this.placeOfOrigin,
    this.placeOfResidence,
    this.personalId,
    this.issueDate,
    this.issuePlace,
    this.expiryDate,
  });

  /// Create CustomerFormData from CccdQrData
  factory CustomerFormData.fromCccdQr(CccdQrData cccdData) {
    return CustomerFormData(
      fullName: cccdData.fullName,
      idNumber: cccdData.idNumber,
      dateOfBirth: cccdData.dateOfBirth,
      gender: cccdData.gender,
      nationality: cccdData.nationality,
      placeOfOrigin: cccdData.placeOfOrigin,
      placeOfResidence: cccdData.placeOfResidence,
      personalId: cccdData.personalId,
      issueDate: cccdData.issueDate,
      issuePlace: cccdData.issuePlace,
      expiryDate: cccdData.expiryDate,
    );
  }

  /// Convert to Map for form mapping
  Map<String, String?> toFormMap() {
    return {
      'customer_full_name': fullName,
      'customer_id_number': idNumber,
      'customer_date_of_birth': dateOfBirth,
      'customer_gender': gender,
      'customer_nationality': nationality,
      'customer_place_of_origin': placeOfOrigin,
      'customer_place_of_residence': placeOfResidence,
      'customer_personal_id': personalId,
      'customer_issue_date': issueDate,
      'customer_issue_place': issuePlace,
      'customer_expiry_date': expiryDate,
    };
  }
}
