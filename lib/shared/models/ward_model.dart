import 'package:json_annotation/json_annotation.dart';

part 'ward_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class WardModel {
  final String? id;
  final String? code;
  final String? name;

  const WardModel({
    this.id,
    this.code,
    this.name,
  });

  factory WardModel.fromJson(Map<String, dynamic> json) =>
      _$WardModelFromJson(json);

  Map<String, dynamic> toJson() => _$WardModelToJson(this);

  WardModel copyWith({
    String? id,
    String? code,
    String? name,
  }) {
    return WardModel(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
    );
  }

  @override
  String toString() {
    return 'WardModel(id: $id, code: $code, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WardModel &&
        other.id == id &&
        other.code == code &&
        other.name == name;
  }

  @override
  int get hashCode {
    return Object.hash(id, code, name);
  }
}
