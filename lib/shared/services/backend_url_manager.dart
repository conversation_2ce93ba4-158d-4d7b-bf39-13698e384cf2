import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';
import '../constants/api_endpoints.dart';

/// Service quản lý backend URL cho development mode
class BackendUrlManager {
  static const String _storageKey = 'selected_backend_url';
  static const String _healthCheckEndpoint = '/rest/rpc/health_check';

  static final AppLogger _logger = AppLogger();

  /// Lưu URL được chọn vào SharedPreferences
  static Future<void> setSelectedUrl(String url) async {
    try {
      _logger.i('💾 Saving backend URL: $url');
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_storageKey, url);
      _logger.i('✅ Backend URL saved successfully');
    } catch (e) {
      _logger.e('❌ Error saving backend URL: $e');
      rethrow;
    }
  }

  /// L<PERSON>y URL từ SharedPreferences hoặc trả về default
  static Future<String> getSelectedUrl() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final url = prefs.getString(_storageKey);
      _logger.d('🔍 Retrieved saved backend URL: ${url ?? 'null'}');
      return url ?? ApiEndpoints.devBackendApiUrl;
    } catch (e) {
      _logger.e('❌ Error getting saved backend URL: $e');
      return ApiEndpoints.devBackendApiUrl;
    }
  }

  /// Reset về URL mặc định
  static Future<void> resetToDefault() async {
    try {
      _logger.i('🔄 Resetting backend URL to default');
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
      _logger.i('✅ Backend URL reset successfully');
    } catch (e) {
      _logger.e('❌ Error resetting backend URL: $e');
      rethrow;
    }
  }

  /// Test connection với health check endpoint
  static Future<HealthCheckResult> testConnection(String baseUrl) async {
    try {
      _logger.i('🔍 Testing connection to: $baseUrl');

      final url = '$baseUrl$_healthCheckEndpoint';
      _logger.d('📡 Health check URL: $url');

      final response = await http
          .get(Uri.parse(url), headers: {'Content-Type': 'application/json'})
          .timeout(const Duration(seconds: 10));

      _logger.d('📡 Response status: ${response.statusCode}');
      _logger.d('📡 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'ok') {
          _logger.i('✅ Health check successful');
          return HealthCheckResult.success(
            timestamp: data['timestamp'],
            database: data['database'],
            schema: data['schema'],
          );
        } else {
          _logger.w('⚠️ Invalid status in response: ${data['status']}');
          return HealthCheckResult.failure('Invalid status: ${data['status']}');
        }
      } else {
        _logger.w('⚠️ HTTP error: ${response.statusCode}');
        return HealthCheckResult.failure('HTTP ${response.statusCode}');
      }
    } catch (e) {
      _logger.e('❌ Connection test failed: $e');
      return HealthCheckResult.failure(e.toString());
    }
  }

  /// Validate URL format
  static bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }
}

/// Model cho health check result
class HealthCheckResult {
  final bool isSuccess;
  final String? error;
  final String? timestamp;
  final String? database;
  final String? schema;

  HealthCheckResult.success({this.timestamp, this.database, this.schema})
    : isSuccess = true,
      error = null;

  HealthCheckResult.failure(this.error)
    : isSuccess = false,
      timestamp = null,
      database = null,
      schema = null;

  @override
  String toString() {
    if (isSuccess) {
      return 'HealthCheckResult.success(database: $database, schema: $schema, timestamp: $timestamp)';
    } else {
      return 'HealthCheckResult.failure(error: $error)';
    }
  }
}
