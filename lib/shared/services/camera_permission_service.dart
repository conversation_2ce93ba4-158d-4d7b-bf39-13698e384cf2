import 'package:permission_handler/permission_handler.dart';
import '../utils/app_logger.dart';

/// Service để quản lý camera permissions có thể tái sử dụng
/// Chỉ chứa business logic, không có UI
class CameraPermissionService {
  static final CameraPermissionService _instance = CameraPermissionService._internal();
  factory CameraPermissionService() => _instance;
  CameraPermissionService._internal();

  final AppLogger _logger = AppLogger();

  /// Kiểm tra trạng thái camera permission hiện tại
  Future<PermissionStatus> checkCameraPermission() async {
    try {
      await _logger.d('Checking camera permission status');
      final status = await Permission.camera.status;
      await _logger.d('Camera permission status: $status');
      return status;
    } catch (e) {
      await _logger.e('Error checking camera permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Yêu cầu camera permission
  Future<PermissionStatus> requestCameraPermission() async {
    try {
      await _logger.d('Requesting camera permission');
      final status = await Permission.camera.request();
      await _logger.d('Camera permission request result: $status');
      return status;
    } catch (e) {
      await _logger.e('Error requesting camera permission: $e');
      return PermissionStatus.denied;
    }
  }

  /// Kiểm tra xem có thể sử dụng camera không
  Future<bool> canUseCamera() async {
    final status = await checkCameraPermission();
    return status == PermissionStatus.granted;
  }

  /// Yêu cầu camera permission và trả về kết quả
  Future<CameraPermissionResult> requestCameraPermissionIfNeeded() async {
    try {
      // Kiểm tra trạng thái hiện tại
      final currentStatus = await checkCameraPermission();
      
      if (currentStatus == PermissionStatus.granted) {
        return CameraPermissionResult.granted;
      }

      if (currentStatus == PermissionStatus.permanentlyDenied) {
        return CameraPermissionResult.permanentlyDenied;
      }

      // Yêu cầu permission
      final status = await requestCameraPermission();
      
      switch (status) {
        case PermissionStatus.granted:
          return CameraPermissionResult.granted;
        case PermissionStatus.permanentlyDenied:
          return CameraPermissionResult.permanentlyDenied;
        default:
          return CameraPermissionResult.denied;
      }
    } catch (e) {
      await _logger.e('Error in requestCameraPermissionIfNeeded: $e');
      return CameraPermissionResult.error;
    }
  }

  /// Mở app settings
  Future<bool> openSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      await _logger.e('Error opening app settings: $e');
      return false;
    }
  }
}

/// Enum để biểu thị kết quả kiểm tra camera permission
enum CameraPermissionResult {
  granted,
  denied,
  permanentlyDenied,
  error,
}

/// Extension để chuyển đổi CameraPermissionResult thành message
extension CameraPermissionResultExtension on CameraPermissionResult {
  String get message {
    switch (this) {
      case CameraPermissionResult.granted:
        return 'Quyền camera đã được cấp';
      case CameraPermissionResult.denied:
        return 'Quyền camera bị từ chối';
      case CameraPermissionResult.permanentlyDenied:
        return 'Quyền camera bị từ chối vĩnh viễn. Vui lòng vào cài đặt để cấp quyền.';
      case CameraPermissionResult.error:
        return 'Có lỗi xảy ra khi kiểm tra quyền camera';
    }
  }

  bool get isGranted => this == CameraPermissionResult.granted;
  bool get isDenied => this == CameraPermissionResult.denied;
  bool get isPermanentlyDenied => this == CameraPermissionResult.permanentlyDenied;
  bool get isError => this == CameraPermissionResult.error;
}
