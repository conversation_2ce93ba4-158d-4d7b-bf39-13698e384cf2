import 'package:flutter/foundation.dart';
import 'package:kiloba_biz/shared/utils/app_logger.dart';

import 'firebase_service.dart';
import 'api/api_service.dart';
import 'token_manager.dart';
import 'device_info_service.dart';
import 'performance_monitor.dart';

import 'navigation_service.dart';
import '../../features/auth/services/fcm_token_service.dart';

/// Global services initializer và coordinator
/// Setup các services và wire up callbacks giữa chúng
class GlobalServices {
  static final GlobalServices _instance = GlobalServices._internal();
  factory GlobalServices() => _instance;
  GlobalServices._internal();

  final logger = AppLogger();
  bool _isInitialized = false;

  /// Initialize all global services và setup callbacks
  Future<void> initialize() async {
    if (_isInitialized) {
      logger.d('GlobalServices already initialized');
      return;
    }

    try {
      logger.i('Initializing global services...');

      // Initialize core services first
      await _initializeCoreServices();

      // Setup service callbacks
      _setupServiceCallbacks();

      _isInitialized = true;
      logger.i('Global services initialized successfully');
    } catch (e) {
      logger.e('GlobalServices initialization failed: $e');
      rethrow;
    }
  }

  /// Initialize core services trong order phù hợp
  Future<void> _initializeCoreServices() async {
    // 1. FirebaseService - cần setup sau Firebase.initializeApp() từ main.dart
    await FirebaseService().initialize();
    logger.d('✓ FirebaseService initialized');

    // 2. DeviceInfoService - cần explicit initialize cho connectivity
    await DeviceInfoService().initialize();
    logger.d('✓ DeviceInfoService initialized');

    // 3. TokenManager - cần có trước để ApiService có thể sử dụng
    await TokenManager().initialize();
    logger.d('✓ TokenManager initialized');

    // 4. ApiService - phụ thuộc vào các services trên
    await ApiService().initialize();
    logger.d('✓ ApiService initialized');

    // 5. Cập nhật backend URL từ preferences (development mode)
    await ApiService().updateBackendUrlFromPreferences();
    logger.d('✓ Backend URL updated from preferences');

    // 5. NavigationService - ready to use
    // NavigationService không cần explicit initialization
    logger.d('✓ NavigationService ready');
  }

  /// Setup callbacks giữa các services
  void _setupServiceCallbacks() {
    logger.d('Setting up service callbacks...');

    // Setup ApiService auth required callback
    ApiService().setAuthRequiredCallback(() {
      _handleAuthRequired();
    });

    // Setup FCM token refresh listener
    FcmTokenService().setupTokenRefreshListener();

    // Register anonymous FCM token for push notifications
    _registerAnonymousFcmToken();

    logger.d('✓ Service callbacks configured');
    logger.d('✓ FCM token refresh listener setup');
    logger.d('✓ Anonymous FCM token registration initiated');
  }

  /// Register FCM token for anonymous users (not logged in)
  Future<void> _registerAnonymousFcmToken() async {
    try {
      // Small delay to ensure all services are ready
      await Future.delayed(const Duration(seconds: 2));

      logger.i('Registering anonymous FCM token...');

      // Register FCM token without userId (anonymous)
      final result = await FcmTokenService().upsertFcmToken();

      if (result.isSuccess) {
        logger.i('✓ Anonymous FCM token registered successfully');
        logger.d(
          'Token details: ${result.data?.action} - ${result.data?.tokenId}',
        );
      } else {
        logger.w(
          '⚠ Anonymous FCM token registration failed: ${result.message}',
        );
      }
    } catch (e) {
      logger.e('❌ Error registering anonymous FCM token: $e');
      // Don't rethrow - này không nên block app initialization
    }
  }

  /// Handle khi ApiService require authentication
  Future<void> _handleAuthRequired() async {
    try {
      logger.w('Authentication required - navigating to login');

      // Direct navigation với clear stack để về login làm root
      await NavigationService().navigateToLogin(clearStack: true, force: true);
    } catch (e) {
      logger.e('Error handling auth required: $e');

      // Fallback - try again with force
      await NavigationService().navigateToLogin(clearStack: true, force: true);
    }
  }

  /// Get initialization status
  bool get isInitialized => _isInitialized;

  /// Reset navigation flags (delegate to NavigationService)
  void resetAuthRequiredFlag() {
    NavigationService().resetNavigationFlags();
    logger.d('Auth navigation flags reset via NavigationService');
  }

  /// Dispose all services (for testing hoặc app shutdown)
  Future<void> dispose() async {
    try {
      logger.i('Disposing global services...');

      // Clear callbacks
      ApiService().clearAuthRequiredCallback();

      // Note: Các services khác có thể có dispose methods riêng
      // nhưng thường không cần dispose vì chúng là singletons

      _isInitialized = false;
      logger.i('Global services disposed');
    } catch (e) {
      logger.e('Error disposing global services: $e');
    }
  }

  /// Debug current services state
  void debugServicesState() {
    if (!kDebugMode) return;

    logger.d('=== GLOBAL SERVICES DEBUG ===');
    logger.d('Is Initialized: $_isInitialized');
    logger.d('TokenManager: ${TokenManager()}');
    logger.d('ApiService: ${ApiService()}');
    logger.d('NavigationService: ${NavigationService()}');
    logger.d('=============================');
  }

  // Quick access to common services (optional)
  static FirebaseService get firebase => FirebaseService();
  static ApiService get api => ApiService();
  static TokenManager get tokenManager => TokenManager();
  static DeviceInfoService get deviceInfo => DeviceInfoService();
  static PerformanceMonitor get performanceMonitor => PerformanceMonitor();
}
