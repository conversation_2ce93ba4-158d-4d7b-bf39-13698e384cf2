import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/index.dart';
import 'settings_service.dart';

class ThemeState {
  final AppThemeMode themeMode;
  final ThemeMode materialThemeMode;

  const ThemeState({required this.themeMode, required this.materialThemeMode});

  ThemeState copyWith({AppThemeMode? themeMode, ThemeMode? materialThemeMode}) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
      materialThemeMode: materialThemeMode ?? this.materialThemeMode,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ThemeState &&
        other.themeMode == themeMode &&
        other.materialThemeMode == materialThemeMode;
  }

  @override
  int get hashCode => Object.hash(themeMode, materialThemeMode);
}

class ThemeCubit extends Cubit<ThemeState> {
  final SettingsService _settingsService;

  ThemeCubit(this._settingsService)
    : super(
        const ThemeState(
          themeMode: AppThemeMode.system,
          materialThemeMode: ThemeMode.system,
        ),
      );

  /// Initialize theme from saved settings
  Future<void> initializeTheme() async {
    try {
      final settings = await _settingsService.getSettings();
      debugPrint(
        '🔍 ThemeCubit: Loaded settings - themeMode: ${settings.themeMode}',
      );
      await changeTheme(settings.themeMode);
    } catch (e) {
      debugPrint('❌ ThemeCubit: Failed to initialize theme: $e');
      // If initialization fails, keep default system theme
      emit(
        const ThemeState(
          themeMode: AppThemeMode.system,
          materialThemeMode: ThemeMode.system,
        ),
      );
    }
  }

  /// Change theme and save to preferences
  Future<void> changeTheme(AppThemeMode newThemeMode) async {
    try {
      debugPrint('🎨 ThemeCubit: Changing theme to: $newThemeMode');

      // Convert to MaterialThemeMode
      final materialThemeMode = _convertToMaterialThemeMode(newThemeMode);

      // Emit new state
      emit(
        ThemeState(
          themeMode: newThemeMode,
          materialThemeMode: materialThemeMode,
        ),
      );

      // Save to preferences
      final saveResult = await _settingsService.updateThemeMode(newThemeMode);
      debugPrint('💾 ThemeCubit: Save result: $saveResult');
    } catch (e) {
      debugPrint('❌ ThemeCubit: Failed to change theme: $e');
      // If saving fails, revert to previous state or system default
      emit(
        const ThemeState(
          themeMode: AppThemeMode.system,
          materialThemeMode: ThemeMode.system,
        ),
      );
    }
  }

  /// Get current brightness based on theme mode
  Brightness getCurrentBrightness(BuildContext context) {
    switch (state.themeMode) {
      case AppThemeMode.system:
        return MediaQuery.of(context).platformBrightness;
      case AppThemeMode.light:
        return Brightness.light;
      case AppThemeMode.dark:
        return Brightness.dark;
    }
  }

  /// Check if current theme is dark
  bool isDarkMode(BuildContext context) {
    return getCurrentBrightness(context) == Brightness.dark;
  }

  /// Convert AppThemeMode to Material ThemeMode
  ThemeMode _convertToMaterialThemeMode(AppThemeMode appThemeMode) {
    switch (appThemeMode) {
      case AppThemeMode.system:
        return ThemeMode.system;
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
    }
  }

  /// Get theme mode display name
  String getThemeModeDisplayName() {
    return state.themeMode.displayName;
  }

  /// Get theme mode icon
  IconData getThemeModeIcon() {
    switch (state.themeMode) {
      case AppThemeMode.system:
        return Icons.brightness_auto;
      case AppThemeMode.light:
        return Icons.brightness_7;
      case AppThemeMode.dark:
        return Icons.brightness_4;
    }
  }
}
