import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/index.dart';
import 'storage/storage_service_interface.dart';
import '../utils/app_logger_interface.dart';
import 'service_locator.dart';

class SettingsService {
  static const String _settingsKey = 'app_settings';

  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();

  SettingsService._();

  IStorageService? _storageService;
  IAppLogger? _logger;
  bool _initialized = false;

  /// Initialize SettingsService với dependencies
  Future<void> init() async {
    if (_initialized) {
      debugPrint('🔄 SettingsService: Already initialized, skipping...');
      return;
    }

    try {
      // Lấy dependencies từ service locator
      _storageService = getIt.get<IStorageService>();
      _logger = getIt.get<IAppLogger>();

      // Initialize storage service
      await _storageService!.init();

      _initialized = true;
      debugPrint('✅ SettingsService: Initialized successfully');
    } catch (e) {
      debugPrint('❌ SettingsService: Failed to initialize: $e');
      rethrow;
    }
  }

  /// Get current settings from storage
  Future<SettingsModel> getSettings() async {
    await init();

    final String? settingsJson = await _storageService!.getString(_settingsKey);
    debugPrint('🔍 SettingsService: Retrieved settings JSON: $settingsJson');

    if (settingsJson == null) {
      debugPrint('📝 SettingsService: No settings found, returning defaults');
      // Return default settings if none saved
      return const SettingsModel();
    }

    try {
      final Map<String, dynamic> settingsMap = json.decode(settingsJson);
      final settings = SettingsModel.fromJson(settingsMap);
      debugPrint(
        '✅ SettingsService: Successfully loaded settings - themeMode: ${settings.themeMode}',
      );
      return settings;
    } catch (e) {
      debugPrint('❌ SettingsService: Failed to parse settings JSON: $e');
      _logger!.e('Failed to parse settings JSON: $e');
      // Return default settings if parsing fails
      return const SettingsModel();
    }
  }

  /// Save settings to storage
  Future<bool> saveSettings(SettingsModel settings) async {
    await init();

    try {
      final String settingsJson = json.encode(settings.toJson());
      return await _storageService!.setString(_settingsKey, settingsJson);
    } catch (e) {
      _logger!.e('Failed to save settings: $e');
      return false;
    }
  }

  /// Update theme mode only
  Future<bool> updateThemeMode(AppThemeMode themeMode) async {
    debugPrint('🎨 SettingsService: Updating theme mode to: $themeMode');
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(themeMode: themeMode);
    final result = await saveSettings(updatedSettings);
    debugPrint('💾 SettingsService: Theme mode update result: $result');
    return result;
  }

  /// Update notifications setting only
  Future<bool> updateNotifications(bool enabled) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(
      notificationsEnabled: enabled,
    );
    return await saveSettings(updatedSettings);
  }

  /// Update biometric setting only
  Future<bool> updateBiometric(bool enabled) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(biometricEnabled: enabled);
    return await saveSettings(updatedSettings);
  }

  /// Update language setting only
  Future<bool> updateLanguage(String language) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(language: language);
    return await saveSettings(updatedSettings);
  }

  /// Update remember login setting only
  Future<bool> updateRememberLogin(bool enabled) async {
    final currentSettings = await getSettings();
    final updatedSettings = currentSettings.copyWith(rememberLogin: enabled);
    return await saveSettings(updatedSettings);
  }

  /// Clear all settings (reset to defaults)
  Future<bool> clearSettings() async {
    await init();
    return await _storageService!.remove(_settingsKey);
  }

  /// Check if settings exist
  Future<bool> hasSettings() async {
    await init();
    return await _storageService!.containsKey(_settingsKey);
  }
}
