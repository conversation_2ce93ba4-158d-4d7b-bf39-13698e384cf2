import 'package:flutter/material.dart';

import '../../features/notifications/blocs/notification_bloc.dart';
import '../../features/notifications/blocs/notification_event.dart';
import '../../features/notifications/models/notification_detail.dart';
import '../../features/notifications/widgets/notification_detail_dialog.dart';

/// Service để quản lý việc hiển thị notification dialog
class NotificationDialogService {
  static final NotificationDialogService _instance = NotificationDialogService._internal();
  factory NotificationDialogService() => _instance;
  NotificationDialogService._internal();

  /// GlobalKey để truy cập Navigator
  GlobalKey<NavigatorState>? _navigatorKey;
  
  /// NotificationBloc instance
  NotificationBloc? _notificationBloc;

  /// Setup service với Navigator và Bloc
  void setup({
    required GlobalKey<NavigatorState> navigatorKey,
    required NotificationBloc notificationBloc,
  }) {
    _navigatorKey = navigatorKey;
    _notificationBloc = notificationBloc;
    debugPrint('🔧 NotificationDialogService setup with:');
    debugPrint('🔧 NavigatorKey: ${_navigatorKey != null}');
    debugPrint('🔧 NotificationBloc: ${_notificationBloc != null}');
  }

  /// Callback function để hiển thị dialog từ notification ID
  void showNotificationDialog(String notificationId) {
    debugPrint('🎯 showNotificationDialog called with ID: $notificationId');
    
    if (_notificationBloc != null) {
      debugPrint('✅ Sending ShowNotificationDetailDialog event');
      _notificationBloc!.add(ShowNotificationDetailDialog(notificationId));
    } else {
      debugPrint('❌ NotificationBloc not available');
    }
  }

  /// Hiển thị dialog trực tiếp với NotificationDetail
  Future<void> showNotificationDetailDialog(NotificationDetail notification) async {
    try {
      debugPrint('🎭 showNotificationDetailDialog called for: ${notification.title}');
      
      if (_navigatorKey?.currentContext != null) {
        await NotificationDetailDialogHelper.showNotificationDetail(
          _navigatorKey!.currentContext!,
          notification,
        );
      } else {
        debugPrint('❌ Navigator context not available');
      }
    } catch (e) {
      debugPrint('❌ Error showing notification detail dialog: $e');
    }
  }

  /// Kiểm tra service đã được setup chưa
  bool get isSetup => _navigatorKey != null && _notificationBloc != null;
} 