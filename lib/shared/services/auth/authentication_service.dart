import 'dart:async';
import 'package:dio/dio.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'token_repository.dart';
import '../../../features/auth/models/auth_response.dart';
import '../../../features/auth/models/refresh_token_request.dart';

/// Service để quản lý authentication
class AuthenticationService {
  final TokenRepository _tokenRepository;
  final IAppLogger _logger;
  final Duration _refreshCooldown;
  final int _maxRefreshAttempts;
  final String _refreshEndpointPath;

  // State management
  bool _isRefreshing = false;
  int _refreshAttempts = 0;
  DateTime? _lastRefreshTime;

  AuthenticationService({
    required TokenRepository tokenRepository,
    required Duration refreshCooldown,
    required int maxRefreshAttempts,
    required String refreshEndpointPath,
    required IAppLogger logger,
  }) : _tokenRepository = tokenRepository,      
       _refreshCooldown = refreshCooldown,
       _maxRefreshAttempts = maxRefreshAttempts,
       _refreshEndpointPath = refreshEndpointPath,
       _logger = logger;

  /// Kiểm tra có đang refresh không
  bool get isRefreshing => _isRefreshing;

  /// Kiểm tra có thể refresh không
  bool get canRefresh {
    // Check max attempts
    if (_refreshAttempts >= _maxRefreshAttempts) {
      _logger.w('⚠️ Max refresh attempts reached ($_maxRefreshAttempts)');
      return false;
    }

    // Check cooldown
    if (_lastRefreshTime != null) {
      final timeSinceLastRefresh = DateTime.now().difference(_lastRefreshTime!);
      if (timeSinceLastRefresh < _refreshCooldown) {
        final remainingSeconds =
            _refreshCooldown.inSeconds - timeSinceLastRefresh.inSeconds;
        _logger.w(
          '⚠️ Refresh cooldown active ($remainingSeconds seconds remaining)',
        );
        return false;
      }
    }

    return true;
  }

  /// Refresh token
  Future<bool> refreshToken(Dio dio) async {
    if (!canRefresh) {
      return false;
    }

    try {
      _isRefreshing = true;
      _refreshAttempts++;
      _lastRefreshTime = DateTime.now();

      _logger.i(
        '🔄 Attempting token refresh (Attempt $_refreshAttempts/$_maxRefreshAttempts)',
      );

      final refreshToken = await _tokenRepository.getRefreshToken();
      if (refreshToken == null) {
        _logger.w('⚠️ No refresh token available');
        return false;
      }

      // Call refresh token endpoint with timeout
      final request = RefreshTokenRequest(refreshToken: refreshToken);
      _logger.d('🔄 Calling refresh token endpoint: $_refreshEndpointPath');

      final response = await dio
          .post(_refreshEndpointPath, data: request.toJson())
          .timeout(
            Duration(seconds: 10),
            onTimeout: () {
              _logger.e('⏰ Token refresh timeout after 10 seconds');
              throw TimeoutException(
                'Token refresh timeout',
                Duration(seconds: 10),
              );
            },
          );

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(response.data);
        await _tokenRepository.saveTokens(authResponse);

        _logger.i('✅ Token refresh successful');
        return true;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          message: 'Token refresh failed with status: ${response.statusCode}',
          response: response,
        );
      }
    } catch (refreshError) {
      _logger.e('❌ Token refresh failed: $refreshError');

      // Log more details about the error
      if (refreshError is DioException) {
        _logger.e('❌ DioException details: ${refreshError.message}');
        _logger.e('❌ Status code: ${refreshError.response?.statusCode}');
        _logger.e('❌ Response data: ${refreshError.response?.data}');
      } else if (refreshError is TimeoutException) {
        _logger.e('❌ TimeoutException: ${refreshError.message}');
      }

      return false;
    } finally {
      _isRefreshing = false;
    }
  }

  /// Reset refresh attempts (call after successful login)
  void resetRefreshAttempts() {
    _refreshAttempts = 0;
    _lastRefreshTime = null;
    _logger.i('🔄 Reset refresh attempts counter');
  }

  /// Clear authentication state
  Future<void> clearAuthentication() async {
    _logger.w('Clearing authentication state');

    // Reset refresh attempts
    _refreshAttempts = 0;
    _lastRefreshTime = null;
    _isRefreshing = false;

    // Clear tokens
    await _tokenRepository.clearTokens();
  }

  /// Lấy thông tin debug
  Map<String, dynamic> getDebugInfo() {
    return {
      'isRefreshing': _isRefreshing,
      'refreshAttempts': _refreshAttempts,
      'maxRefreshAttempts': _maxRefreshAttempts,
      'lastRefreshTime': _lastRefreshTime?.toIso8601String(),
      'refreshCooldown': _refreshCooldown.inSeconds,
      'canRefresh': canRefresh,
    };
  }

  /// Debug method để xem trạng thái authentication
  void debugAuthStatus() {
    _logger.i('🔍 === Authentication Status ===');
    _logger.i('isRefreshing: $_isRefreshing');
    _logger.i('refreshAttempts: $_refreshAttempts/$_maxRefreshAttempts');
    _logger.i(
      'lastRefreshTime: ${_lastRefreshTime?.toIso8601String() ?? "None"}',
    );
    _logger.i('refreshCooldown: ${_refreshCooldown.inSeconds}s');
    _logger.i('canRefresh: $canRefresh');
    _logger.i('===============================');
  }
}
