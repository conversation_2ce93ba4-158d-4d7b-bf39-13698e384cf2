import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import '../models/index.dart';
import 'api/api_service.dart';

/// Configuration class cho từng loại master data API
class _MasterDataConfig<T> {
  final String endpoint;
  final List<T> Function(List<dynamic>) parser;
  final String cacheKey;
  final String logType;
  final String displayName;
  final void Function(List<T>)? setCacheFunction;
  final Map<String, dynamic>? Function(Map<String, dynamic>?)? paramTransformer;

  const _MasterDataConfig({
    required this.endpoint,
    required this.parser,
    required this.cacheKey,
    required this.logType,
    required this.displayName,
    this.setCacheFunction,
    this.paramTransformer,
  });
}

/// Smart cache entry với expiration
class _CacheEntry<T> {
  final List<T> data;
  final DateTime timestamp;
  final Duration ttl;
  
  _CacheEntry(this.data, this.timestamp, this.ttl);
  
  bool get isExpired => DateTime.now().difference(timestamp) > ttl;
}

/// Service để quản lý master data (provinces, positions, regions, branches)
class MasterDataService {
  static final MasterDataService _instance = MasterDataService._internal();
  factory MasterDataService() => _instance;
  MasterDataService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // Cache data
  List<ProvinceModel>? _provincesCache;
  List<PositionModel>? _positionsCache;
  List<RegionModel>? _regionsCache;
  final Map<String, List<BranchModel>> _branchesCache = {};
  final Map<String, List<WardModel>> _wardsCache = {};
  final Map<String, List<ConfigModel>> _configCache = {};
  List<CustomerTagModel>? _customerTagsCache;
  final Map<String, List<BankAccountModel>> _bankAccountsCache = {};
  List<CollateralCategoryModel>? _collateralCategoriesCache;

  /// Lấy danh sách tỉnh/thành phố (compatibility method for bloc)
  Future<List<ProvinceModel>> getProvinces({String? search}) async {
    final result = await _fetchMasterData<ProvinceModel>(
      'provinces',
      search != null ? {'p_search': search} : null,
    );
    
    // Backward compatibility: set legacy cache
    _setLegacyCache('provinces', result);
    
    return result;
  }

  /// Lấy danh sách chức vụ (compatibility method for bloc)
  Future<List<PositionModel>> getPositions({String? search}) async {
    final result = await _fetchMasterData<PositionModel>(
      'positions',
      search != null ? {'p_search': search} : null,
    );
    
    // Backward compatibility: set legacy cache
    _setLegacyCache('positions', result);
    
    return result;
  }

  /// Lấy danh sách khu vực (compatibility method for bloc)
  Future<List<RegionModel>> getRegions({String? search}) async {
    final result = await _fetchMasterData<RegionModel>(
      'regions',
      search != null ? {'p_search': search} : null,
    );
    
    // Backward compatibility: set legacy cache
    _setLegacyCache('regions', result);
    
    return result;
  }

  /// Lấy danh sách chi nhánh (compatibility method for bloc)
  Future<List<BranchModel>> getBranches({
    String? provinceId,
    String? regionId,
    List<String>? regionIds,
    String? search,
  }) async {
    final Map<String, dynamic> params = {};
    if (provinceId != null) params['p_province_id'] = provinceId;
    if (regionId != null) params['p_region_id'] = regionId;
    if (regionIds != null && regionIds.isNotEmpty) params['p_region_ids'] = regionIds;
    if (search != null) params['p_search'] = search;

    final cacheKey = provinceId ?? 'all';
    
    final result = await _fetchMasterData<BranchModel>(
      'branches',
      params.isNotEmpty ? params : null,
      additionalCacheKey: cacheKey,
    );
    
    // Backward compatibility: set legacy cache
    _setLegacyCache('branches', result, additionalKey: cacheKey);
    
    return result;
  }

  /// Lấy danh sách phường/xã theo tỉnh/thành (compatibility method for bloc)
  Future<List<WardModel>> getWards({
    String? provinceId,
    String? search,
  }) async {
    final Map<String, dynamic> params = {};
    if (provinceId != null) params['p_province_id'] = provinceId;
    if (search != null) params['p_search'] = search;

    final cacheKey = provinceId ?? 'all';
    
    final result = await _fetchMasterData<WardModel>(
      'wards',
      params.isNotEmpty ? params : null,
      additionalCacheKey: cacheKey,
    );
    
    // Backward compatibility: set legacy cache
    _setLegacyCache('wards', result, additionalKey: cacheKey);
    
    return result;
  }

  /// Lấy danh sách config theo group code (compatibility method for bloc)
  Future<List<ConfigModel>> getConfig(String groupCode) async {
    final result = await _fetchMasterData<ConfigModel>(
      'configs',
      {'p_group_code': groupCode},
      additionalCacheKey: groupCode,
    );
    
    // Backward compatibility: set legacy cache
    _setLegacyCache('configs', result, additionalKey: groupCode);
    
    return result;
  }

  /// Lấy danh sách customer tags (compatibility method for bloc)
  Future<List<CustomerTagModel>> getCustomerTags() async {
    final result = await _fetchMasterData<CustomerTagModel>(
      'customer_tag',
      null, // Không cần params
    );
    
    // Lưu vào cache sau khi fetch thành công
    _customerTagsCache = result;
    _logger.i('Customer tags cached successfully: ${_customerTagsCache!.length} items');
    
    return result;
  }

  /// Lấy danh sách tài khoản ngân hàng theo ID card number
  Future<List<BankAccountModel>> getBankAccounts(String idCardNo) async {
    final result = await _fetchMasterData<BankAccountModel>(
      'bank_accounts',
      {'idCardNo': idCardNo},
      additionalCacheKey: idCardNo,
    );
    
    // Lưu vào cache sau khi fetch thành công
    _bankAccountsCache[idCardNo] = result;
    _logger.i('Bank accounts cached successfully for $idCardNo: ${result.length} items');
    
    return result;
  }

  /// Lấy danh sách collateral categories (compatibility method for bloc)
  Future<List<CollateralCategoryModel>> getCollateralCategories() async {
    _logger.i('=== START: Loading collateral categories ===');
    
    final result = await _fetchMasterData<CollateralCategoryModel>(
      'collateral_categories',
      null, // Không cần params
    );
    
    _logger.i('Collateral categories fetched: ${result.length} items');
    for (int i = 0; i < result.length; i++) {
      _logger.i('Category $i: ${result[i].toJson()}');
    }
    
    // Lưu vào cache sau khi fetch thành công
    _collateralCategoriesCache = result;
    _logger.i('Collateral categories cached successfully: ${_collateralCategoriesCache!.length} items');
    _logger.i('=== END: Loading collateral categories ===');
    
    return result;
  }

  /// Clear cache
  void clearCache() {
    // Clear legacy caches
    _provincesCache = null;
    _positionsCache = null;
    _regionsCache = null;
    _branchesCache.clear();
    _wardsCache.clear();
    _configCache.clear();
    _customerTagsCache = null;
    _bankAccountsCache.clear();
    _collateralCategoriesCache = null;
    
    // Clear smart cache
    _smartCache.clear();
    
    _logger.i('Master data cache cleared (legacy + smart cache)');
  }

  // ============================================================================
  // OPTIMIZATION: Generic Method Pattern & Configuration-Driven Approach
  // GENERIC: dùng chung cho các base Master data
  // ============================================================================

  // Smart cache storage
  final Map<String, _CacheEntry<dynamic>> _smartCache = {};
  /// Default TTL cho các loại data chưa được định nghĩa
  static const Duration _defaultCacheTTL = Duration(days: 30);

  /// Configuration map cho tất cả master data APIs
  /// [Cần tối ưu] Bổ sung base endpoint URL để tích hợp với backend
  static final Map<String, _MasterDataConfig<dynamic>> _apiConfigs = {
    'provinces': _MasterDataConfig<ProvinceModel>(
      endpoint: '/rest/rpc/get_provinces',
      parser: (data) => data.map((item) => ProvinceModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'provinces',
      logType: 'provinces',
      displayName: 'tỉnh/thành',
      paramTransformer: (params) => params,
    ),
    'positions': _MasterDataConfig<PositionModel>(
      endpoint: '/rest/rpc/get_positions',
      parser: (data) => data.map((item) => PositionModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'positions',
      logType: 'positions',
      displayName: 'chức vụ',
      paramTransformer: (params) => params,
    ),
    'regions': _MasterDataConfig<RegionModel>(
      endpoint: '/rest/rpc/get_regions',
      parser: (data) => data.map((item) => RegionModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'regions',
      logType: 'regions',
      displayName: 'khu vực',
      paramTransformer: (params) => params,
    ),
    'branches': _MasterDataConfig<BranchModel>(
      endpoint: '/rest/rpc/get_branches',
      parser: (data) => data.map((item) => BranchModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'branches',
      logType: 'branches',
      displayName: 'chi nhánh',
      paramTransformer: (params) => params,
    ),
    'wards': _MasterDataConfig<WardModel>(
      endpoint: '/rest/rpc/get_wards',
      parser: (data) => data.map((item) => WardModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'wards',
      logType: 'wards',
      displayName: 'phường/xã',
      paramTransformer: (params) => params,
    ),
    'configs': _MasterDataConfig<ConfigModel>(
      endpoint: '/rest/rpc/get_config',
      parser: (data) => data.map((item) => ConfigModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'configs',
      logType: 'config',
      displayName: 'config',
      paramTransformer: (params) => params,
    ),
    'customer_tag': _MasterDataConfig<CustomerTagModel>(
      endpoint: '/rest/rpc/get_customer_tag',
      parser: (data) => data.map((item) => CustomerTagModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'tags',
      logType: 'tags',
      displayName: 'customertag',
      paramTransformer: (params) => params,
    ),
    'bank_accounts': _MasterDataConfig<BankAccountModel>(
      endpoint: '/kilobabiz-api/api/v1/accounts/getPaymentAccount',
      parser: (data) => data.map((item) => BankAccountModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'accounts',
      logType: 'accounts',
      displayName: 'tài khoản ngân hàng',
      paramTransformer: (params) => params,
    ),
    'collateral_categories': _MasterDataConfig<CollateralCategoryModel>(
      endpoint: '/rest/rpc/get_collateral_categories',
      parser: (data) => data.map((item) => CollateralCategoryModel.fromJson(item as Map<String, dynamic>)).toList(),
      cacheKey: 'collateral_categories',
      logType: 'collateral_categories',
      displayName: 'loại tài sản đảm bảo',
      paramTransformer: (params) => params,
    ),
  };

  /// Generic method để fetch master data với BaseResponse
  Future<BaseResponse<List<T>>> _fetchMasterDataWithBaseResponse<T>(
    String configKey,
    Map<String, dynamic>? params, {
    String? additionalCacheKey,
  }) async {
    _logger.i('=== _fetchMasterDataWithBaseResponse START ===');
    _logger.i('Config key: $configKey');
    _logger.i('Params: $params');
    _logger.i('Additional cache key: $additionalCacheKey');
    
    final config = _apiConfigs[configKey] as _MasterDataConfig<T>?;
    if (config == null) {
      _logger.e('Config not found for key: $configKey');
      _logger.e('Available configs: ${_apiConfigs.keys.toList()}');
      throw MasterDataException(
        message: 'Invalid config key: $configKey',
        type: MasterDataExceptionType.unknown,
      );
    }
    
    _logger.i('Config found: ${config.displayName}');
    _logger.i('Endpoint: ${config.endpoint}');

    try {
      _logger.i('=== START: Generic fetch ${config.displayName} ===');
      _logger.i('Endpoint: ${config.endpoint}');
      _logger.i('Params: $params');

      final transformedParams = config.paramTransformer?.call(params) ?? params;

      _logger.i('Calling API: ${config.endpoint}');
      _logger.i('With data: $transformedParams');
      
      final response = await _apiService.post(
        config.endpoint,
        data: transformedParams,
      );

      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response type: ${response.data.runtimeType}');
      _logger.i('Response data: ${response.data}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic>) {
          // API trả về BaseResponse format
          _logger.i('Response is Map, parsing with BaseResponse.fromJson');
          _logger.i('Response keys: ${responseData.keys.toList()}');
          return BaseResponse.fromJson(
            responseData,
            (data) => _parseGenericData<T>(data, config),
          );
        } else if (responseData is List) {
          // API trả về array trực tiếp - tạo BaseResponse wrapper
          final parsedData = config.parser(responseData);
          
          // Smart caching với cache key
          final cacheKey = additionalCacheKey != null 
              ? '${config.cacheKey}_$additionalCacheKey' 
              : config.cacheKey;
          _setSmartCache<T>(cacheKey, parsedData);
          
          _logger.i('${config.displayName} parsed successfully: ${parsedData.length} items');
          _logger.i('=== END: Generic fetch ${config.displayName} ===');
          
          return BaseResponse.success(
            data: parsedData,
            code: '200',
            message: 'Lấy danh sách ${config.displayName} thành công',
          );
        } else {
          _logger.e('Invalid response format - expected Map or List but got ${responseData.runtimeType}');
          throw MasterDataException(
            message: 'Invalid response format for ${config.displayName}',
            type: MasterDataExceptionType.invalidResponse,
          );
        }
      } else {
        throw MasterDataException(
          message: 'HTTP error: ${response.statusCode}',
          type: MasterDataExceptionType.apiError,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching ${config.displayName}: ${e.message}');
      throw MasterDataException(
        message: 'Không thể lấy danh sách ${config.displayName}: ${e.message}',
        type: MasterDataExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching ${config.displayName}: $e');
      _logger.e('Error type: ${e.runtimeType}');
      _logger.e('Error details: ${e.toString()}');
      if (e is Exception) {
        _logger.e('Exception details: ${e.toString()}');
      }
      throw MasterDataException(
        message: 'Lỗi không xác định khi lấy danh sách ${config.displayName}: $e',
        type: MasterDataExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Generic method để fetch master data (compatibility method)
  Future<List<T>> _fetchMasterData<T>(
    String configKey,
    Map<String, dynamic>? params, {
    String? additionalCacheKey,
  }) async {
    final baseResponse = await _fetchMasterDataWithBaseResponse<T>(
      configKey,
      params,
      additionalCacheKey: additionalCacheKey,
    );
    
    if (baseResponse.isSuccess && baseResponse.data != null) {
      return baseResponse.data!;
    } else {
      throw MasterDataException(
        message: baseResponse.message,
        type: MasterDataExceptionType.apiError,
      );
    }
  }

  /// Generic data parser từ BaseResponse.data
  List<T> _parseGenericData<T>(Object? data, _MasterDataConfig<T> config) {
    _logger.i('=== _parseGenericData START ===');
    _logger.i('Data type: ${data.runtimeType}');
    _logger.i('Data: $data');
    _logger.i('Config cache key: ${config.cacheKey}');
    
    if (data == null) {
      _logger.i('Data is null, returning empty list');
      return <T>[];
    }
    
    if (data is List) {
      _logger.i('Data is List with ${data.length} items');
      final result = config.parser(data);
      _logger.i('Parsed result: ${result.length} items');
      return result;
    } else if (data is Map<String, dynamic>) {
      // Special handling cho bank accounts API
      if (config.cacheKey == 'bank_accounts') {
        final accounts = data['accounts'];
        if (accounts is List) {
          return config.parser(accounts);
        }
      }
      
      // Special handling cho collateral categories API
      if (config.cacheKey == 'collateral_categories') {
        _logger.i('Parsing collateral categories data: $data');
        final categories = data['collateral_categories'];
        _logger.i('Categories field: $categories');
        if (categories is List) {
          _logger.i('Categories is List with ${categories.length} items');
          final result = config.parser(categories);
          _logger.i('Parsed result: ${result.length} items');
          return result;
        } else {
          _logger.i('Categories is not a List, type: ${categories.runtimeType}');
        }
      }
      
      // Thử tìm data trong các field có thể có
      for (final key in [config.cacheKey, 'data', 'items', 'list']) {
        final fieldData = data[key];
        if (fieldData is List) {
          return config.parser(fieldData);
        }
      }
    }
    
    return <T>[];
  }

  /// Smart cache setter với TTL
  void _setSmartCache<T>(String key, List<T> data) {
    _smartCache[key] = _CacheEntry<T>(data, DateTime.now(), _defaultCacheTTL);
    _cleanupExpiredCache();
  }

  /// Cleanup expired cache entries
  void _cleanupExpiredCache() {
    _smartCache.removeWhere((key, entry) => entry.isExpired);
  }

  /// Legacy cache setters for backward compatibility
  void _setLegacyCache<T>(String configKey, List<T> data, {String? additionalKey}) {
    switch (configKey) {
      case 'provinces':
        _provincesCache = data as List<ProvinceModel>;
        break;
      case 'positions':
        _positionsCache = data as List<PositionModel>;
        break;
      case 'regions':
        _regionsCache = data as List<RegionModel>;
        break;
      case 'branches':
        final key = additionalKey ?? 'all';
        _branchesCache[key] = data as List<BranchModel>;
        break;
      case 'wards':
        final key = additionalKey ?? 'all';
        _wardsCache[key] = data as List<WardModel>;
        break;
      case 'configs':
        if (additionalKey != null) {
          _configCache[additionalKey] = data as List<ConfigModel>;
        }
        case 'customer_tag':
          if (additionalKey != null) {
            _customerTagsCache = data as List<CustomerTagModel>;
        }
        break;
        case 'collateral_categories':
          _collateralCategoriesCache = data as List<CollateralCategoryModel>;
        break;
    }
  }
}

/// Custom exception cho Master Data service
class MasterDataException implements Exception {
  final String message;
  final MasterDataExceptionType type;
  final Object? originalException;

  const MasterDataException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'MasterDataException: $message (Type: $type)';
}

/// Loại lỗi Master Data
enum MasterDataExceptionType {
  notFound,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
} 