import 'package:shared_preferences/shared_preferences.dart';
import 'storage_service_interface.dart';

/// Implementation của IStorageService sử dụng SharedPreferences
class SharedPreferencesStorageService implements IStorageService {
  SharedPreferences? _prefs;

  @override
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  @override
  Future<bool> setString(String key, String value) async {
    await init();
    return await _prefs?.setString(key, value) ?? false;
  }

  @override
  Future<String?> getString(String key) async {
    await init();
    return _prefs?.getString(key);
  }

  @override
  Future<bool> setBool(String key, bool value) async {
    await init();
    return await _prefs?.setBool(key, value) ?? false;
  }

  @override
  Future<bool?> getBool(String key) async {
    await init();
    return _prefs?.getBool(key);
  }

  @override
  Future<bool> setInt(String key, int value) async {
    await init();
    return await _prefs?.setInt(key, value) ?? false;
  }

  @override
  Future<int?> getInt(String key) async {
    await init();
    return _prefs?.getInt(key);
  }

  @override
  Future<bool> setDouble(String key, double value) async {
    await init();
    return await _prefs?.setDouble(key, value) ?? false;
  }

  @override
  Future<double?> getDouble(String key) async {
    await init();
    return _prefs?.getDouble(key);
  }

  @override
  Future<bool> setStringList(String key, List<String> value) async {
    await init();
    return await _prefs?.setStringList(key, value) ?? false;
  }

  @override
  Future<List<String>?> getStringList(String key) async {
    await init();
    return _prefs?.getStringList(key);
  }

  @override
  Future<bool> remove(String key) async {
    await init();
    return await _prefs?.remove(key) ?? false;
  }

  @override
  Future<bool> containsKey(String key) async {
    await init();
    return _prefs?.containsKey(key) ?? false;
  }

  @override
  Future<bool> clear() async {
    await init();
    return await _prefs?.clear() ?? false;
  }
}
