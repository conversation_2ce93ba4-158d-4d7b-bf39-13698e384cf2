/// Interface cho storage service để abstract việc lưu trữ dữ liệu
/// <PERSON><PERSON> thể implement bằng SharedPreferences, file system, hoặc các phương thức khác
abstract class IStorageService {
  /// Khởi tạo storage service
  Future<void> init();

  /// Lưu string value
  Future<bool> setString(String key, String value);

  /// Lấy string value
  Future<String?> getString(String key);

  /// Lưu boolean value
  Future<bool> setBool(String key, bool value);

  /// Lấy boolean value
  Future<bool?> getBool(String key);

  /// Lưu integer value
  Future<bool> setInt(String key, int value);

  /// Lấy integer value
  Future<int?> getInt(String key);

  /// Lưu double value
  Future<bool> setDouble(String key, double value);

  /// Lấy double value
  Future<double?> getDouble(String key);

  /// Lưu list of strings
  Future<bool> setStringList(String key, List<String> value);

  /// Lấy list of strings
  Future<List<String>?> getStringList(String key);

  /// Xóa một key
  Future<bool> remove(String key);

  /// Kiểm tra key có tồn tại không
  Future<bool> containsKey(String key);

  /// Xóa tất cả data
  Future<bool> clear();
}
