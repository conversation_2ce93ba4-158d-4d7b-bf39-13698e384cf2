import 'package:get_it/get_it.dart';
import 'package:kiloba_biz/shared/services/api/api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import '../utils/simple_app_logger.dart';
import '../utils/app_logger.dart';
import '../utils/app_logger_interface.dart';
import 'storage/storage_service_interface.dart';
import 'storage/shared_preferences_storage_service.dart';
import 'settings_service.dart';

/// Global service locator instance
final GetIt getIt = GetIt.instance;

/// Service locator setup class
class ServiceLocator {
  /// Setup all services
  static Future<void> setup() async {
    // Register storage service
    getIt.registerLazySingleton<IStorageService>(
      () => SharedPreferencesStorageService(),
    );

    // Register SimpleAppLogger as IAppLogger
    getIt.registerLazySingleton<IAppLogger>(() => SimpleAppLogger());

    // Register SimpleAppLogger as SimpleAppLogger (for direct access)
    getIt.registerLazySingleton<SimpleAppLogger>(() => SimpleAppLogger());

    // Register AppLogger as AppLogger (for backward compatibility)
    getIt.registerLazySingleton<AppLogger>(() => AppLogger());

    // Register SettingsService
    getIt.registerLazySingleton<SettingsService>(
      () => SettingsService.instance,
    );

    // Register ApiService as IApiService
    getIt.registerLazySingleton<IApiService>(() => ApiService());
  }

  /// Reset all services (useful for testing)
  static Future<void> reset() async {
    await getIt.reset();
  }

  /// Check if service is registered
  static bool isRegistered<T extends Object>() {
    return getIt.isRegistered<T>();
  }
}

/// Extension methods for easier service access
extension ServiceLocatorExtension on GetIt {
  /// Get storage service instance
  IStorageService get storage => get<IStorageService>();

  /// Get SimpleAppLogger instance
  SimpleAppLogger get simpleLogger => get<SimpleAppLogger>();

  /// Get IAppLogger instance
  IAppLogger get logger => get<IAppLogger>();

  /// Get AppLogger instance
  AppLogger get appLogger => get<AppLogger>();

  /// Get IApiService instance
  IApiService get apiService => get<IApiService>();
}
