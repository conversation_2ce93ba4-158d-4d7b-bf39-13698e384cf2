import 'dart:async';
import 'package:dio/dio.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'api_service_interface.dart';

/// Exception cho SimpleApiService
class SimpleApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;

  const SimpleApiException({required this.message, this.statusCode, this.data});

  @override
  String toString() {
    return 'SimpleApiException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}';
  }
}

/// SimpleApiService cho testing với auto-login khi gặp 401
class SimpleApiService implements IApiService {
  late Dio _dio;
  String? _baseUrl;
  String? _username;
  String? _password;
  String? _accessToken;
  bool _isLoggingIn = false;

  IAppLogger? _logger;

  /// Configure SimpleApiService cho testing
  void configure({
    required String baseUrl,
    required String username,
    required String password,
    IAppLogger? logger,
  }) {
    _baseUrl = baseUrl;
    _username = username;
    _password = password;
    _logger = logger;
    _initializeDio();
  }

  /// Initialize Dio với interceptors
  void _initializeDio() {
    _dio = Dio(
      BaseOptions(
        baseUrl: _baseUrl ?? '',
        connectTimeout: Duration(seconds: 30),
        receiveTimeout: Duration(seconds: 30),
        sendTimeout: Duration(seconds: 30),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ),
    );

    _setupInterceptors();
    _logger?.i('🚀 SimpleApiService initialized with baseUrl: $_baseUrl');
  }

  /// Setup interceptors với auto-login logic
  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onError: (error, handler) async {
          if (error.response?.statusCode == 401 && !_isLoggingIn) {
            _logger?.w('🔐 401 error detected, attempting auto-login');

            try {
              await _performLogin();

              // Retry original request với token mới
              final retryResponse = await _dio.request(
                error.requestOptions.path,
                data: error.requestOptions.data,
                queryParameters: error.requestOptions.queryParameters,
                options: Options(
                  method: error.requestOptions.method,
                  headers: {
                    ...error.requestOptions.headers,
                    'Authorization': 'Bearer $_accessToken',
                  },
                ),
              );

              _logger?.i('✅ Auto-login successful, retry request completed');
              handler.resolve(retryResponse);
            } catch (loginError) {
              _logger?.e('❌ Auto-login failed: $loginError');
              handler.reject(error);
            }
          } else {
            handler.next(error);
          }
        },
      ),
    );
  }

  /// Perform login để lấy access token
  Future<void> _performLogin() async {
    if (_isLoggingIn) {
      _logger?.w('⚠️ Login already in progress, waiting...');
      return;
    }

    _isLoggingIn = true;

    try {
      _logger?.i('🔐 Performing login for user: $_username');

      final response = await _dio.post(
        '/kilobabiz-api/api/v1/auth/login',
        data: {'username': _username, 'password': _password},
      );

      if (response.statusCode == 200) {
        final responseData = response.data;
        
        // Handle response structure với hoặc không có data wrapper
        Map<String, dynamic> data;
        if (responseData.containsKey('data') && responseData['data'] is Map<String, dynamic>) {
          // New response format with success/code/message/data wrapper
          data = responseData['data'];
        } else {
          // Old response format - direct data
          data = responseData;
        }
        
        _accessToken = data['access_token'];

        // Update default headers
        _dio.options.headers['Authorization'] = 'Bearer $_accessToken';

        _logger?.i('✅ Login successful, access token obtained');
      } else {
        throw SimpleApiException(
          message: 'Login failed with status: ${response.statusCode}',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      _logger?.e('❌ Login failed: $e');
      throw SimpleApiException(message: 'Login failed: $e');
    } finally {
      _isLoggingIn = false;
    }
  }

  /// Login trước để lấy token (public method cho testing)
  Future<bool> login() async {
    try {
      await _performLogin();
      return _accessToken != null;
    } catch (e) {
      _logger?.e('❌ Login failed: $e');
      return false;
    }
  }

  /// Check if đã login
  bool get isLoggedIn => _accessToken != null;

  /// Get current access token
  String? get accessToken => _accessToken;

  /// Convert DioException to SimpleApiException
  SimpleApiException _convertToApiException(DioException dioError) {
    final statusCode = dioError.response?.statusCode;
    final message = dioError.message ?? 'Network error occurred';
    final data = dioError.response?.data;

    return SimpleApiException(
      message: message,
      statusCode: statusCode,
      data: data,
    );
  }

  // Implement IApiService methods
  @override
  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.get(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  @override
  Future<Response> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.post(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  @override
  Future<Response> put(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.put(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  @override
  Future<Response> delete(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.delete(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }

  @override
  Future<Response> patch(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.patch(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      throw _convertToApiException(e);
    }
  }
}
