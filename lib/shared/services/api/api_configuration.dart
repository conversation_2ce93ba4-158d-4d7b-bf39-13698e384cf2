import 'package:flutter/foundation.dart';
import '../../constants/api_endpoints.dart';
import '../../constants/remote_config_keys.dart';
import '../firebase_service.dart';

/// Configuration cho API Service
class ApiConfiguration {
  final String baseUrl;
  final Duration connectTimeout;
  final Duration receiveTimeout;
  final Duration sendTimeout;
  final int maxRetries;
  final Duration refreshCooldown;
  final int maxRefreshAttempts;
  final Duration pendingRequestTimeout;
  final bool enableLogging;
  final bool enableDeviceHeaders;
  final bool enableAuthToken;
  final bool enableErrorHandling;

  const ApiConfiguration({
    required this.baseUrl,
    required this.connectTimeout,
    required this.receiveTimeout,
    required this.sendTimeout,
    required this.maxRetries,
    required this.refreshCooldown,
    required this.maxRefreshAttempts,
    required this.pendingRequestTimeout,
    required this.enableLogging,
    required this.enableDeviceHeaders,
    required this.enableAuthToken,
    required this.enableErrorHandling,
  });

  /// Tạo configuration từ environment và remote config
  factory ApiConfiguration.fromEnvironment() {
    // Get backend URL based on environment
    String backendUrl;
    if (ApiEndpoints.isProductionMode) {
      // Production: sử dụng remote config
      backendUrl = FirebaseService().getConfigString(
        RemoteConfigKeys.backendApiUrl,
        defaultValue: 'http://192.168.2.61:8090',
      );
    } else {
      // Development: kiểm tra URL được chọn
      // Note: Đây là async operation, nhưng factory constructor không thể async
      // Sẽ được xử lý trong initialize() method
      backendUrl = ApiEndpoints.getBackendUrl();
    }

    final timeoutSeconds = FirebaseService().getConfigInt(
      RemoteConfigKeys.apiTimeoutSeconds,
      defaultValue: 30,
    );

    return ApiConfiguration(
      baseUrl: backendUrl,
      connectTimeout: Duration(seconds: timeoutSeconds),
      receiveTimeout: Duration(seconds: timeoutSeconds + 15),
      sendTimeout: Duration(seconds: timeoutSeconds - 5),
      maxRetries: 3,
      refreshCooldown: Duration(seconds: 30),
      maxRefreshAttempts: 2,
      pendingRequestTimeout: Duration(minutes: 2),
      enableLogging: kDebugMode,
      enableDeviceHeaders: true,
      enableAuthToken: true,
      enableErrorHandling: true,
    );
  }

  /// Tạo configuration cho testing
  factory ApiConfiguration.forTesting({
    String? baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
    int? maxRetries,
    Duration? refreshCooldown,
    int? maxRefreshAttempts,
    Duration? pendingRequestTimeout,
    bool? enableLogging,
    bool? enableDeviceHeaders,
    bool? enableAuthToken,
  }) {
    return ApiConfiguration(
      baseUrl: baseUrl ?? 'http://test.com',
      connectTimeout: connectTimeout ?? Duration(seconds: 10),
      receiveTimeout: receiveTimeout ?? Duration(seconds: 10),
      sendTimeout: sendTimeout ?? Duration(seconds: 10),
      maxRetries: maxRetries ?? 1,
      refreshCooldown: refreshCooldown ?? Duration(seconds: 5),
      maxRefreshAttempts: maxRefreshAttempts ?? 1,
      pendingRequestTimeout: pendingRequestTimeout ?? Duration(seconds: 30),
      enableLogging: enableLogging ?? false,
      enableDeviceHeaders: enableDeviceHeaders ?? false,
      enableAuthToken: enableAuthToken ?? false,
      enableErrorHandling: true,
    );
  }

  /// Copy với các thay đổi
  ApiConfiguration copyWith({
    String? baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
    Duration? sendTimeout,
    int? maxRetries,
    Duration? refreshCooldown,
    int? maxRefreshAttempts,
    Duration? pendingRequestTimeout,
    bool? enableLogging,
    bool? enableDeviceHeaders,
    bool? enableAuthToken,
    bool? enableErrorHandling,
  }) {
    return ApiConfiguration(
      baseUrl: baseUrl ?? this.baseUrl,
      connectTimeout: connectTimeout ?? this.connectTimeout,
      receiveTimeout: receiveTimeout ?? this.receiveTimeout,
      sendTimeout: sendTimeout ?? this.sendTimeout,
      maxRetries: maxRetries ?? this.maxRetries,
      refreshCooldown: refreshCooldown ?? this.refreshCooldown,
      maxRefreshAttempts: maxRefreshAttempts ?? this.maxRefreshAttempts,
      pendingRequestTimeout:
          pendingRequestTimeout ?? this.pendingRequestTimeout,
      enableLogging: enableLogging ?? this.enableLogging,
      enableDeviceHeaders: enableDeviceHeaders ?? this.enableDeviceHeaders,
      enableAuthToken: enableAuthToken ?? this.enableAuthToken,
      enableErrorHandling: enableErrorHandling ?? this.enableErrorHandling,
    );
  }

  @override
  String toString() {
    return 'ApiConfiguration(baseUrl: $baseUrl, connectTimeout: $connectTimeout, maxRetries: $maxRetries)';
  }
}
