import 'dart:io';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import '../models/document_model.dart';
import '../models/base_response.dart';

import 'api/api_service.dart';
import 'storage_service.dart';

/// Service để quản lý document upload và metadata
class DocumentService {
  static final DocumentService _instance = DocumentService._internal();
  factory DocumentService() => _instance;
  DocumentService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();
  
  StorageService get _storageService => StorageService();

  // API endpoints
  static const String _insertDocumentEndpoint = '/rest/rpc/insert_document';

  /// Insert document record vào database
  Future<BaseResponse<DocumentModel>> insertDocument({
    required String documentTypeCode,
    required String originalFilename,
    required String storedFilename,
    required String filePath,
    required int fileSize,
    required String mimeType,
    required String fileExtension,
    required String storageType,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      _logger.i('Inserting document record: $originalFilename');

      final Map<String, dynamic> params = {
        'p_document_type_code': documentTypeCode,
        'p_original_filename': originalFilename,
        'p_stored_filename': storedFilename,
        'p_file_path': filePath,
        'p_file_size': fileSize,
        'p_mime_type': mimeType,
        'p_file_extension': fileExtension,
        'p_storage_type': storageType,
      };

      if (checksum != null) {
        params['p_checksum'] = checksum;
      }

      if (metadata != null) {
        params['p_metadata'] = metadata;
      }

      final response = await _apiService.post(
        _insertDocumentEndpoint,
        data: params,
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Sử dụng BaseResponse.fromJson với custom fromJsonT function
        return BaseResponse.fromJson(
          responseData,
          (data) => _parseDocumentModel(data),
        );
      } else {
        throw DocumentException(
          message: 'Invalid response format for document insertion',
          type: DocumentExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when inserting document: ${e.message}');
      throw DocumentException(
        message: 'Không thể tạo document record: ${e.message}',
        type: DocumentExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when inserting document: $e');
      throw DocumentException(
        message: 'Lỗi không xác định khi tạo document record',
        type: DocumentExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Upload và insert document với thông tin cơ bản
  Future<BaseResponse<DocumentModel>> uploadAndInsertDocument({
    required File file,
    required String documentTypeCode,
    String? folderPath,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      _logger.i('Uploading and inserting document: ${file.path}');

      // 1. Upload file to storage
      final uploadResponse = await _storageService.uploadFile(
        file: file,
        folderPath: folderPath,
        generateDownloadUrl: true,
        metadata: metadata?.map((key, value) => MapEntry(key, value.toString())),
      );

      if (!uploadResponse.isSuccess || uploadResponse.data == null) {
        throw DocumentException(
          message: 'Upload failed: ${uploadResponse.message}',
          type: DocumentExceptionType.apiError,
        );
      }

      final uploadData = uploadResponse.data!;

      // 2. Insert document record
      return await insertDocument(
        documentTypeCode: documentTypeCode,
        originalFilename: uploadData.fileName,
        storedFilename: uploadData.fileName,
        filePath: uploadData.objectName,
        fileSize: uploadData.size,
        mimeType: uploadData.contentType,
        fileExtension: _getFileExtension(uploadData.fileName),
        storageType: 'minio',
        checksum: uploadData.etag,
        metadata: {},
      );
    } catch (e) {
      _logger.e('Error uploading and inserting document: $e');
      rethrow;
    }
  }

  /// Insert document với thông tin cơ bản
  Future<BaseResponse<DocumentModel>> insertBasicDocument({
    required String documentTypeCode,
    required String originalFilename,
    required String storedFilename,
    required String filePath,
    required int fileSize,
    required String mimeType,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) async {
    return await insertDocument(
      documentTypeCode: documentTypeCode,
      originalFilename: originalFilename,
      storedFilename: storedFilename,
      filePath: filePath,
      fileSize: fileSize,
      mimeType: mimeType,
      fileExtension: _getFileExtension(originalFilename),
      storageType: 'minio', // Default storage type
      checksum: checksum,
      metadata: metadata,
    );
  }

  /// Upload và insert document cho CMND/CCCD
  Future<BaseResponse<DocumentModel>> uploadAndInsertIdCardDocument({
    required File file,
    String? folderPath,
    Map<String, dynamic>? metadata,
  }) async {
    return await uploadAndInsertDocument(
      file: file,
      documentTypeCode: 'ID_CARD',
      folderPath: folderPath,
      metadata: metadata,
    );
  }

  /// Insert document cho CMND/CCCD
  Future<BaseResponse<DocumentModel>> insertIdCardDocument({
    required String originalFilename,
    required String storedFilename,
    required String filePath,
    required int fileSize,
    required String mimeType,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) async {
    return await insertBasicDocument(
      documentTypeCode: 'ID_CARD',
      originalFilename: originalFilename,
      storedFilename: storedFilename,
      filePath: filePath,
      fileSize: fileSize,
      mimeType: mimeType,
      checksum: checksum,
      metadata: metadata,
    );
  }

  /// Upload và insert document cho CMND/CCCD mặt trước
  Future<BaseResponse<DocumentModel>> uploadAndInsertIdCardFrontDocument({
    required File file,
    String? folderPath,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'card_side': 'front',
      'document_category': 'identity_card',
    };

    return await uploadAndInsertIdCardDocument(
      file: file,
      folderPath: folderPath,
      metadata: enhancedMetadata,
    );
  }

  /// Insert document cho CMND/CCCD mặt trước
  Future<BaseResponse<DocumentModel>> insertIdCardFrontDocument({
    required String originalFilename,
    required String storedFilename,
    required String filePath,
    required int fileSize,
    required String mimeType,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'card_side': 'front',
      'document_category': 'identity_card',
    };

    return await insertIdCardDocument(
      originalFilename: originalFilename,
      storedFilename: storedFilename,
      filePath: filePath,
      fileSize: fileSize,
      mimeType: mimeType,
      checksum: checksum,
      metadata: enhancedMetadata,
    );
  }

  /// Upload và insert document cho CMND/CCCD mặt sau
  Future<BaseResponse<DocumentModel>> uploadAndInsertIdCardBackDocument({
    required File file,
    String? folderPath,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'card_side': 'back',
      'document_category': 'identity_card',
    };

    return await uploadAndInsertIdCardDocument(
      file: file,
      folderPath: folderPath,
      metadata: enhancedMetadata,
    );
  }

  /// Insert document cho CMND/CCCD mặt sau
  Future<BaseResponse<DocumentModel>> insertIdCardBackDocument({
    required String originalFilename,
    required String storedFilename,
    required String filePath,
    required int fileSize,
    required String mimeType,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'card_side': 'back',
      'document_category': 'identity_card',
    };

    return await insertIdCardDocument(
      originalFilename: originalFilename,
      storedFilename: storedFilename,
      filePath: filePath,
      fileSize: fileSize,
      mimeType: mimeType,
      checksum: checksum,
      metadata: enhancedMetadata,
    );
  }

  /// Upload và insert document cho ảnh chân dung
  Future<BaseResponse<DocumentModel>> uploadAndInsertPortraitDocument({
    required File file,
    String? folderPath,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'document_category': 'portrait',
    };

    return await uploadAndInsertDocument(
      file: file,
      documentTypeCode: 'PORTRAIT',
      folderPath: folderPath,
      metadata: enhancedMetadata,
    );
  }

  /// Insert document cho ảnh chân dung
  Future<BaseResponse<DocumentModel>> insertPortraitDocument({
    required String originalFilename,
    required String storedFilename,
    required String filePath,
    required int fileSize,
    required String mimeType,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'document_category': 'portrait',
    };

    return await insertBasicDocument(
      documentTypeCode: 'PORTRAIT',
      originalFilename: originalFilename,
      storedFilename: storedFilename,
      filePath: filePath,
      fileSize: fileSize,
      mimeType: mimeType,
      checksum: checksum,
      metadata: enhancedMetadata,
    );
  }

  /// Upload và insert document cho hợp đồng
  Future<BaseResponse<DocumentModel>> uploadAndInsertContractDocument({
    required File file,
    String? folderPath,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'document_category': 'contract',
    };

    return await uploadAndInsertDocument(
      file: file,
      documentTypeCode: 'CONTRACT',
      folderPath: folderPath,
      metadata: enhancedMetadata,
    );
  }

  /// Insert document cho hợp đồng
  Future<BaseResponse<DocumentModel>> insertContractDocument({
    required String originalFilename,
    required String storedFilename,
    required String filePath,
    required int fileSize,
    required String mimeType,
    String? checksum,
    Map<String, dynamic>? metadata,
  }) async {
    final enhancedMetadata = {
      ...?metadata,
      'document_category': 'contract',
    };

    return await insertBasicDocument(
      documentTypeCode: 'CONTRACT',
      originalFilename: originalFilename,
      storedFilename: storedFilename,
      filePath: filePath,
      fileSize: fileSize,
      mimeType: mimeType,
      checksum: checksum,
      metadata: enhancedMetadata,
    );
  }

  /// Lấy file extension từ filename
  String _getFileExtension(String filename) {
    final parts = filename.split('.');
    if (parts.length > 1) {
      return parts.last.toLowerCase();
    }
    return '';
  }

  /// Validate file size
  bool validateFileSize(int fileSize, {int maxSizeInMB = 10}) {
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return fileSize <= maxSizeInBytes;
  }

  /// Validate mime type
  bool validateMimeType(String mimeType) {
    final allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/webp',
      'application/pdf',
    ];
    return allowedTypes.contains(mimeType.toLowerCase());
  }

  /// Generate stored filename
  String generateStoredFilename({
    required String originalFilename,
    required String prefix,
    String? suffix,
  }) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = _getFileExtension(originalFilename);
    final baseName = originalFilename.split('.').first;
    
    final storedName = '${prefix}_${baseName}_$timestamp';
    final finalName = suffix != null ? '${storedName}_$suffix' : storedName;
    
    return extension.isNotEmpty ? '$finalName.$extension' : finalName;
  }

  /// Kiểm tra tính khả dụng của document API
  Future<bool> checkDocumentApiAvailability() async {
    try {
      // Thử insert một document test nhỏ
      final response = await insertBasicDocument(
        documentTypeCode: 'TEST',
        originalFilename: 'test.txt',
        storedFilename: 'test_${DateTime.now().millisecondsSinceEpoch}.txt',
        filePath: '/test/test.txt',
        fileSize: 1,
        mimeType: 'text/plain',
      );
      return response.isSuccess;
    } catch (e) {
      _logger.w('Document API not available: $e');
      return false;
    }
  }

  /// Helper function để parse DocumentModel từ API response data
  DocumentModel _parseDocumentModel(Object? data) {
    if (data == null) {
      throw DocumentException(
        message: 'Không có dữ liệu document',
        type: DocumentExceptionType.invalidResponse,
      );
    }

    if (data is! Map<String, dynamic>) {
      throw DocumentException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: DocumentExceptionType.invalidResponse,
      );
    }

    try {
      return DocumentModel.fromJson(data);
    } catch (e) {
      _logger.e('Error parsing DocumentModel: $e');
      throw DocumentException(
        message: 'Lỗi parse dữ liệu document: $e',
        type: DocumentExceptionType.invalidResponse,
      );
    }
  }
}

/// Custom exception cho Document service
class DocumentException implements Exception {
  final String message;
  final DocumentExceptionType type;
  final String? code;
  final Object? originalException;

  const DocumentException({
    required this.message,
    required this.type,
    this.code,
    this.originalException,
  });

  @override
  String toString() => 'DocumentException: $message (Type: $type${code != null ? ', Code: $code' : ''})';
}

/// Loại lỗi Document
enum DocumentExceptionType {
  notFound,
  apiError,
  invalidResponse,
  validationError,
  unauthorized,
  unknown,
} 