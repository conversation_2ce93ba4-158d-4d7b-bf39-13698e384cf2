import 'dart:io';
import 'package:dio/dio.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import '../models/storage_upload_response.dart';
import '../models/storage_file_metadata.dart';
import '../models/storage_file_info.dart';
import '../models/base_response.dart';

import '../utils/storage_url_helper.dart';
import 'api/api_service.dart';

/// Service để quản lý file storage với MinIO
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // API endpoints
  static const String _uploadEndpoint = '/kilobabiz-api/api/v1/storage/upload';
  static const String _downloadEndpoint = '/kilobabiz-api/api/v1/storage/download';
  static const String _presignedUrlEndpoint = '/kilobabiz-api/api/v1/storage/presigned-url';
  static const String _metadataEndpoint = '/kilobabiz-api/api/v1/storage/metadata';
  static const String _listEndpoint = '/kilobabiz-api/api/v1/storage/list';
  static const String _existsEndpoint = '/kilobabiz-api/api/v1/storage/exists';

  /// Upload file với multipart/form-data
  Future<BaseResponse<StorageUploadResponse>> uploadFile({
    required File file,
    String? folderPath,
    bool generateDownloadUrl = false,
    Map<String, String>? metadata,
  }) async {
    try {
      _logger.i('Uploading file: ${file.path}');

      // Validate file
      if (!await file.exists()) {
        throw StorageException(
          message: 'File không tồn tại: ${file.path}',
          type: StorageExceptionType.fileNotFound,
        );
      }

      // Get file info
      final fileInfo = await StorageUrlHelper.getFileInfo(file);
      
      // Validate file size
      if (!StorageUrlHelper.validateFileSize(fileInfo.size)) {
        throw StorageException(
          message: 'File size quá lớn: ${fileInfo.size} bytes',
          type: StorageExceptionType.invalidFile,
        );
      }

      final response = await _apiService.post(
        _uploadEndpoint,
        data: FormData.fromMap({
          'file': await MultipartFile.fromFile(file.path),
          if (folderPath != null) 'folderPath': folderPath,
          'generateDownloadUrl': generateDownloadUrl.toString(),
          if (metadata != null) ...metadata.map((key, value) => MapEntry(key, value)),
        }),
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        _logger.d('Storage upload response data: $responseData');
        _logger.d('Response data keys: ${responseData.keys.toList()}');
        
        // Parse response trước
        final baseResponse = BaseResponse.fromJson(
          responseData,
          (data) => _parseStorageUploadResponse(data),
        );

        _logger.d('BaseResponse parsed - success: ${baseResponse.isSuccess}, code: ${baseResponse.code}, message: ${baseResponse.message}');

        // Handle domain replacement cho download URL nếu có
        if (baseResponse.isSuccess && baseResponse.data != null) {
          final uploadResponse = baseResponse.data!;
          _logger.d('Upload response data: ${uploadResponse.toJson()}');
          
          if (uploadResponse.downloadUrl != null) {
            final replacedUrl = await StorageUrlHelper.replaceDomainInUrl(uploadResponse.downloadUrl!);
            final updatedResponse = uploadResponse.copyWith(downloadUrl: replacedUrl);
            _logger.d('Domain replaced URL: $replacedUrl');
            return BaseResponse.success(
              data: updatedResponse,
              code: baseResponse.code,
              message: baseResponse.message,
            );
          }
        }

        return baseResponse;
      } else {
        throw StorageException(
          message: 'Invalid response format for file upload',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when uploading file: ${e.message}');
      _logger.e('API error type: ${e.runtimeType}');
      _logger.e('API error details: ${e.toString()}');
      
      String errorMessage = 'Không thể upload file';
      if (e.message.contains('Network error')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      } else if (e.message.contains('timeout')) {
        errorMessage = 'Upload quá thời gian. Vui lòng thử lại.';
      } else {
        errorMessage = '$errorMessage: ${e.message}';
      }
      
      throw StorageException(
        message: errorMessage,
        type: StorageExceptionType.uploadFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when uploading file: $e');
      _logger.e('Error type: ${e.runtimeType}');
      
      String errorMessage = 'Lỗi không xác định khi upload file';
      if (e.toString().contains('FormData has already been finalized')) {
        errorMessage = 'Lỗi xử lý file. Vui lòng thử lại.';
      } else {
        errorMessage = '$errorMessage: $e';
      }
      
      throw StorageException(
        message: errorMessage,
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Upload file với progress tracking
  Future<BaseResponse<StorageUploadResponse>> uploadFileWithProgress({
    required File file,
    String? folderPath,
    ProgressCallback? onProgress,
    bool generateDownloadUrl = false,
    Map<String, String>? metadata,
  }) async {
    try {
      _logger.i('Uploading file with progress: ${file.path}');

      // Validate file
      if (!await file.exists()) {
        throw StorageException(
          message: 'File không tồn tại: ${file.path}',
          type: StorageExceptionType.fileNotFound,
        );
      }

      final response = await _apiService.post(
        _uploadEndpoint,
        data: FormData.fromMap({
          'file': await MultipartFile.fromFile(file.path),
          if (folderPath != null) 'folderPath': folderPath,
          'generateDownloadUrl': generateDownloadUrl.toString(),
          if (metadata != null) ...metadata.map((key, value) => MapEntry(key, value)),
        }),
        onSendProgress: onProgress,
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Parse response trước
        final baseResponse = BaseResponse.fromJson(
          responseData,
          (data) => _parseStorageUploadResponse(data),
        );

        // Handle domain replacement cho download URL nếu có
        if (baseResponse.isSuccess && baseResponse.data != null) {
          final uploadResponse = baseResponse.data!;
          if (uploadResponse.downloadUrl != null) {
            final replacedUrl = await StorageUrlHelper.replaceDomainInUrl(uploadResponse.downloadUrl!);
            final updatedResponse = uploadResponse.copyWith(downloadUrl: replacedUrl);
            return BaseResponse.success(
              data: updatedResponse,
              code: baseResponse.code,
              message: baseResponse.message,
            );
          }
        }

        return baseResponse;
      } else {
        throw StorageException(
          message: 'Invalid response format for file upload',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when uploading file with progress: ${e.message}');
      _logger.e('API error type: ${e.runtimeType}');
      _logger.e('API error details: ${e.toString()}');
      
      String errorMessage = 'Không thể upload file';
      if (e.message.contains('Network error')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng kiểm tra kết nối internet và thử lại.';
      } else if (e.message.contains('timeout')) {
        errorMessage = 'Upload quá thời gian. Vui lòng thử lại.';
      } else {
        errorMessage = '$errorMessage: ${e.message}';
      }
      
      throw StorageException(
        message: errorMessage,
        type: StorageExceptionType.uploadFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when uploading file with progress: $e');
      _logger.e('Error type: ${e.runtimeType}');
      
      String errorMessage = 'Lỗi không xác định khi upload file';
      if (e.toString().contains('FormData has already been finalized')) {
        errorMessage = 'Lỗi xử lý file. Vui lòng thử lại.';
      } else {
        errorMessage = '$errorMessage: $e';
      }
      
      throw StorageException(
        message: errorMessage,
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Generate presigned URL với domain replacement
  Future<BaseResponse<String>> generatePresignedUrl({
    required String objectName,
    required String urlType,
    int expirationMinutes = 60,
  }) async {
    try {
      _logger.i('Generating presigned URL for: $objectName ($urlType)');

      final response = await _apiService.post(
        _presignedUrlEndpoint,
        data: {
          'objectName': objectName,
          'urlType': urlType,
          'expirationMinutes': expirationMinutes,
        },
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Parse response trước
        final baseResponse = BaseResponse.fromJson(
          responseData,
          (data) => _parsePresignedUrlResponse(data),
        );

        // Handle domain replacement cho presigned URL nếu có
        if (baseResponse.isSuccess && baseResponse.data != null) {
          final originalUrl = baseResponse.data!;
          final replacedUrl = await StorageUrlHelper.replaceDomainInUrl(originalUrl);
          return BaseResponse.success(
            data: replacedUrl,
            code: baseResponse.code,
            message: baseResponse.message,
          );
        }

        return baseResponse;
      } else {
        throw StorageException(
          message: 'Invalid response format for presigned URL',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when generating presigned URL: ${e.message}');
      throw StorageException(
        message: 'Không thể tạo presigned URL: ${e.message}',
        type: StorageExceptionType.presignedUrlFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when generating presigned URL: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi tạo presigned URL',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Download file sử dụng app domain
  Future<File> downloadFile({
    required String objectName,
    String? localPath,
    ProgressCallback? onProgress,
  }) async {
    try {
      _logger.i('Downloading file: $objectName');

      // Generate presigned download URL với domain replacement
      final presignedResponse = await generatePresignedUrl(
        objectName: objectName,
        urlType: 'DOWNLOAD',
        expirationMinutes: 60,
      );

      if (!presignedResponse.isSuccess || presignedResponse.data == null) {
        throw StorageException(
          message: 'Không thể tạo presigned URL: ${presignedResponse.message}',
          type: StorageExceptionType.presignedUrlFailed,
        );
      }

      final downloadUrl = presignedResponse.data!;

      // Download file từ replaced URL
      final response = await _apiService.get(
        downloadUrl,
        onReceiveProgress: onProgress,
      );

      // Save file locally
      final fileName = objectName.split('/').last;
      final savePath = localPath ?? '/tmp/$fileName';
      final file = File(savePath);
      await file.writeAsBytes(response.data);

      _logger.i('File downloaded successfully: $savePath');
      return file;
    } on ApiException catch (e) {
      _logger.e('API error when downloading file: ${e.message}');
      throw StorageException(
        message: 'Không thể download file: ${e.message}',
        type: StorageExceptionType.downloadFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when downloading file: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi download file',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Get file metadata
  Future<BaseResponse<StorageFileMetadata>> getFileMetadata(String objectName) async {
    try {
      _logger.i('Getting file metadata: $objectName');

      final response = await _apiService.get(
        '$_metadataEndpoint/$objectName',
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Sử dụng BaseResponse.fromJson với custom fromJsonT function
        return BaseResponse.fromJson(
          responseData,
          (data) => _parseStorageFileMetadata(data),
        );
      } else {
        throw StorageException(
          message: 'Invalid response format for file metadata',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when getting file metadata: ${e.message}');
      throw StorageException(
        message: 'Không thể lấy file metadata: ${e.message}',
        type: StorageExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when getting file metadata: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi lấy file metadata',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// List files
  Future<BaseResponse<List<StorageFileInfo>>> listFiles({String? prefix}) async {
    try {
      _logger.i('Listing files with prefix: $prefix');

      final response = await _apiService.get(
        _listEndpoint,
        queryParameters: prefix != null ? {'prefix': prefix} : null,
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Sử dụng BaseResponse.fromJson với custom fromJsonT function
        return BaseResponse.fromJson(
          responseData,
          (data) => _parseStorageFileList(data),
        );
      } else {
        throw StorageException(
          message: 'Invalid response format for file list',
          type: StorageExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when listing files: ${e.message}');
      throw StorageException(
        message: 'Không thể lấy danh sách files: ${e.message}',
        type: StorageExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when listing files: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi lấy danh sách files',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Check file exists
  Future<bool> fileExists(String objectName) async {
    try {
      _logger.i('Checking if file exists: $objectName');

      await _apiService.get('$_existsEndpoint/$objectName');
      
      _logger.i('File exists: $objectName');
      return true;
    } on ApiException catch (e) {
      if (e.statusCode == 404) {
        _logger.i('File does not exist: $objectName');
        return false;
      }
      _logger.e('API error when checking file exists: ${e.message}');
      throw StorageException(
        message: 'Không thể kiểm tra file exists: ${e.message}',
        type: StorageExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when checking file exists: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi kiểm tra file exists',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Delete file
  Future<void> deleteFile(String objectName) async {
    try {
      _logger.i('Deleting file: $objectName');

      await _apiService.delete('$_downloadEndpoint/$objectName');
      
      _logger.i('File deleted successfully: $objectName');
    } on ApiException catch (e) {
      _logger.e('API error when deleting file: ${e.message}');
      throw StorageException(
        message: 'Không thể xóa file: ${e.message}',
        type: StorageExceptionType.deleteFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when deleting file: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi xóa file',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Delete multiple files
  Future<void> deleteMultipleFiles(List<String> objectNames) async {
    try {
      _logger.i('Deleting multiple files: ${objectNames.length} files');

      await _apiService.delete(
        '$_downloadEndpoint/batch',
        data: objectNames,
      );
      
      _logger.i('Multiple files deleted successfully: ${objectNames.length} files');
    } on ApiException catch (e) {
      _logger.e('API error when deleting multiple files: ${e.message}');
      throw StorageException(
        message: 'Không thể xóa multiple files: ${e.message}',
        type: StorageExceptionType.deleteFailed,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when deleting multiple files: $e');
      throw StorageException(
        message: 'Lỗi không xác định khi xóa multiple files',
        type: StorageExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Kiểm tra tính khả dụng của storage API
  Future<bool> checkStorageApiAvailability() async {
    try {
      // Thử list files để kiểm tra API
      final response = await listFiles();
      return response.isSuccess;
    } catch (e) {
      _logger.w('Storage API not available: $e');
      return false;
    }
  }

  /// Helper function để parse StorageUploadResponse từ API response data
  StorageUploadResponse _parseStorageUploadResponse(Object? data) {
    _logger.d('Parsing StorageUploadResponse from data: $data');
    
    if (data == null) {
      _logger.e('Data is null');
      throw StorageException(
        message: 'Không có dữ liệu upload response',
        type: StorageExceptionType.invalidResponse,
      );
    }

    if (data is! Map<String, dynamic>) {
      _logger.e('Data is not Map<String, dynamic>, type: ${data.runtimeType}');
      throw StorageException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: StorageExceptionType.invalidResponse,
      );
    }

    try {
      _logger.d('Data keys: ${data.keys.toList()}');
      
      // Check required fields
      final requiredFields = ['objectName', 'fileName', 'contentType', 'size', 'etag', 'uploadTime'];
      for (final field in requiredFields) {
        if (!data.containsKey(field)) {
          _logger.e('Missing required field: $field');
          throw StorageException(
            message: 'Missing required field: $field',
            type: StorageExceptionType.invalidResponse,
          );
        }
      }

      // Replace domain trong download URL nếu có
      String? downloadUrl = data['downloadUrl'] as String?;
      if (downloadUrl != null) {
        // Note: Không thể await trong sync function, cần handle ở caller
        data = Map<String, dynamic>.from(data);
        data['downloadUrl'] = downloadUrl; // Sẽ được replace domain ở caller
      }

      final result = StorageUploadResponse.fromJson(data);
      _logger.d('Successfully parsed StorageUploadResponse: ${result.toJson()}');
      return result;
    } catch (e) {
      _logger.e('Error parsing StorageUploadResponse: $e');
      _logger.e('Data that caused error: $data');
      throw StorageException(
        message: 'Lỗi parse dữ liệu upload response: $e',
        type: StorageExceptionType.invalidResponse,
      );
    }
  }

  /// Helper function để parse presigned URL response từ API response data
  String _parsePresignedUrlResponse(Object? data) {
    if (data == null) {
      throw StorageException(
        message: 'Không có dữ liệu presigned URL response',
        type: StorageExceptionType.invalidResponse,
      );
    }

    if (data is! Map<String, dynamic>) {
      throw StorageException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: StorageExceptionType.invalidResponse,
      );
    }

    try {
      final originalUrl = data['url'] as String?;
      if (originalUrl == null) {
        throw StorageException(
          message: 'Missing URL in presigned response',
          type: StorageExceptionType.invalidResponse,
        );
      }
      
      // Note: Domain replacement sẽ được handle ở caller vì cần async
      return originalUrl;
    } catch (e) {
      _logger.e('Error parsing presigned URL response: $e');
      throw StorageException(
        message: 'Lỗi parse dữ liệu presigned URL: $e',
        type: StorageExceptionType.invalidResponse,
      );
    }
  }

  /// Helper function để parse StorageFileMetadata từ API response data
  StorageFileMetadata _parseStorageFileMetadata(Object? data) {
    if (data == null) {
      throw StorageException(
        message: 'Không có dữ liệu file metadata',
        type: StorageExceptionType.invalidResponse,
      );
    }

    if (data is! Map<String, dynamic>) {
      throw StorageException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: StorageExceptionType.invalidResponse,
      );
    }

    try {
      return StorageFileMetadata.fromJson(data);
    } catch (e) {
      _logger.e('Error parsing StorageFileMetadata: $e');
      throw StorageException(
        message: 'Lỗi parse dữ liệu file metadata: $e',
        type: StorageExceptionType.invalidResponse,
      );
    }
  }

  /// Helper function để parse StorageFileInfo list từ API response data
  List<StorageFileInfo> _parseStorageFileList(Object? data) {
    if (data == null) {
      _logger.w('No data in file list response, returning empty list');
      return [];
    }

    if (data is! Map<String, dynamic>) {
      throw StorageException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: StorageExceptionType.invalidResponse,
      );
    }

    try {
      final filesData = data['files'] as List<dynamic>?;
      if (filesData == null) {
        _logger.w('No files field in data');
        return [];
      }

      return filesData.map((item) {
        try {
          return StorageFileInfo.fromJson(item as Map<String, dynamic>);
        } catch (e) {
          _logger.e('Error parsing file info: $e');
          return null;
        }
      }).whereType<StorageFileInfo>().toList();
    } catch (e) {
      _logger.e('Error parsing StorageFileInfo list: $e');
      throw StorageException(
        message: 'Lỗi parse danh sách files: $e',
        type: StorageExceptionType.invalidResponse,
      );
    }
  }
}

/// Custom exception cho Storage service
class StorageException implements Exception {
  final String message;
  final StorageExceptionType type;
  final Object? originalException;

  const StorageException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'StorageException: $message (Type: $type)';
}

/// Loại lỗi Storage
enum StorageExceptionType {
  uploadFailed,
  downloadFailed,
  fileNotFound,
  invalidFile,
  presignedUrlFailed,
  deleteFailed,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
} 