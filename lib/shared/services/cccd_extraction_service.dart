import 'dart:io';
import 'dart:convert';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import '../models/cccd_extraction_model.dart';
import 'api/api_service.dart';

/// Service để trích xuất thông tin từ CCCD sử dụng AI Vision
class CccdExtractionService {
  static final CccdExtractionService _instance = CccdExtractionService._internal();
  factory CccdExtractionService() => _instance;
  CccdExtractionService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // API endpoints
  static const String _extractCccdEndpoint = '/kilobabiz-api/api/v1/ai/extract-cccd';

  /// Trích xuất thông tin từ CCCD
  /// 
  /// [frontImage] - Ảnh mặt trước CCCD (base64)
  /// [backImage] - Ảnh mặt sau CCCD (base64)
  /// [model] - Model AI sử dụng (mặc định: gemini-2.5-flash-lite)
  /// [maxTokens] - Số token tối đa (mặc định: 1000)
  /// [temperature] - Nhiệt độ cho AI model (mặc định: 0.1)
  Future<CccdExtractionModel> extractCccd({
    required String frontImage,
    required String backImage,
    String model = 'gemini-2.5-flash-lite',
    int maxTokens = 1000,
    double temperature = 0.1,
  }) async {
    try {
      _logger.i('=== START: CccdExtractionService.extractCccd ===');
      _logger.i('Model: $model');
      _logger.i('Max tokens: $maxTokens');
      _logger.i('Temperature: $temperature');
      _logger.i('Front image length: ${frontImage.length}');
      _logger.i('Back image length: ${backImage.length}');
      _logger.i('API endpoint: $_extractCccdEndpoint');

      // Validate input
      if (frontImage.isEmpty) {
        throw CccdExtractionException(
          message: 'Ảnh mặt trước CCCD không được để trống',
          type: CccdExtractionExceptionType.missingFrontImage,
        );
      }

      if (backImage.isEmpty) {
        throw CccdExtractionException(
          message: 'Ảnh mặt sau CCCD không được để trống',
          type: CccdExtractionExceptionType.missingBackImage,
        );
      }

      // Validate base64 format
      if (!_isValidBase64Image(frontImage)) {
        throw CccdExtractionException(
          message: 'Format ảnh mặt trước không hợp lệ',
          type: CccdExtractionExceptionType.invalidBase64Format,
        );
      }

      if (!_isValidBase64Image(backImage)) {
        throw CccdExtractionException(
          message: 'Format ảnh mặt sau không hợp lệ',
          type: CccdExtractionExceptionType.invalidBase64Format,
        );
      }

      final request = CccdExtractionRequest(
        frontImage: frontImage,
        backImage: backImage,
        model: model,
        maxTokens: maxTokens,
        temperature: temperature,
      );

      _logger.i('Request data prepared');
      _logger.i('Request: $request');

      final response = await _apiService.post(
        _extractCccdEndpoint,
        data: request.toJson(),
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Parse response
      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        final cccdResponse = CccdExtractionResponse.fromJson(responseData);

        if (cccdResponse.success && cccdResponse.data != null) {
          final extractedData = cccdResponse.data!;
          
          _logger.i('CCCD extraction completed successfully');
          _logger.i('Extracted ID: ${extractedData.idNumber}');
          _logger.i('Extracted name: ${extractedData.fullName}');
          _logger.i('Confidence: ${extractedData.confidence}');
          _logger.i('Is expired: ${extractedData.isExpired}');
          _logger.i('Days until expiry: ${extractedData.daysUntilExpiry}');
          _logger.i('MRZ lines: ${extractedData.mrzLines}');
          _logger.i('Warnings: ${extractedData.warnings}');
          _logger.i('Response time: ${cccdResponse.responseTime}ms');
          _logger.i('Request ID: ${cccdResponse.requestId}');
          
          // Validate required fields
          if (extractedData.idNumber.isEmpty) {
            throw CccdExtractionException(
              message: 'Không thể trích xuất số CCCD',
              type: CccdExtractionExceptionType.extractionFailed,
            );
          }

          if (extractedData.fullName.isEmpty) {
            throw CccdExtractionException(
              message: 'Không thể trích xuất họ tên',
              type: CccdExtractionExceptionType.extractionFailed,
            );
          }

          _logger.i('=== END: CccdExtractionService.extractCccd ===');
          return extractedData;
        } else {
          _logger.e('API returned success=false: ${cccdResponse.message}');
          throw CccdExtractionException(
            message: 'Trích xuất thông tin CCCD thất bại: ${cccdResponse.message}',
            type: CccdExtractionExceptionType.extractionFailed,
          );
        }
      } else {
        _logger.e('Invalid response format - expected Map but got ${response.data.runtimeType}');
        throw CccdExtractionException(
          message: 'Format response không hợp lệ',
          type: CccdExtractionExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when extracting CCCD: ${e.message}');
      
      // Map API error codes to specific exceptions based on response data
      if (e.data is Map<String, dynamic>) {
        final errorData = e.data as Map<String, dynamic>;
        final errorCode = errorData['code']?.toString();
        
        if (errorCode == '7101') {
          throw CccdExtractionException(
            message: 'Format CCCD không hợp lệ',
            type: CccdExtractionExceptionType.invalidCccdFormat,
            originalException: e,
          );
        } else if (errorCode == '7102') {
          throw CccdExtractionException(
            message: 'CCCD đã hết hạn',
            type: CccdExtractionExceptionType.cccdExpired,
            originalException: e,
          );
        } else if (errorCode == '7103') {
          throw CccdExtractionException(
            message: 'CCCD sắp hết hạn',
            type: CccdExtractionExceptionType.cccdExpiringSoon,
            originalException: e,
          );
        } else if (errorCode == '7104') {
          throw CccdExtractionException(
            message: 'Chất lượng ảnh CCCD kém',
            type: CccdExtractionExceptionType.poorImageQuality,
            originalException: e,
          );
        } else if (errorCode == '7105') {
          throw CccdExtractionException(
            message: 'Thiếu ảnh CCCD',
            type: CccdExtractionExceptionType.missingImages,
            originalException: e,
          );
        } else if (errorCode == '7106') {
          throw CccdExtractionException(
            message: 'Format base64 không hợp lệ',
            type: CccdExtractionExceptionType.invalidBase64Format,
            originalException: e,
          );
        } else if (errorCode == '7107') {
          throw CccdExtractionException(
            message: 'Trích xuất thông tin CCCD thất bại',
            type: CccdExtractionExceptionType.extractionFailed,
            originalException: e,
          );
        } else if (errorCode == '7108') {
          throw CccdExtractionException(
            message: 'Format ngày tháng không hợp lệ',
            type: CccdExtractionExceptionType.invalidDateFormat,
            originalException: e,
          );
        }
      }
      
      // Default API error
      throw CccdExtractionException(
        message: 'Lỗi API khi trích xuất CCCD: ${e.message}',
        type: CccdExtractionExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when extracting CCCD: $e');
      throw CccdExtractionException(
        message: 'Lỗi không xác định khi trích xuất CCCD',
        type: CccdExtractionExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Trích xuất thông tin CCCD với ảnh từ file
  /// 
  /// [frontImagePath] - Đường dẫn file ảnh mặt trước
  /// [backImagePath] - Đường dẫn file ảnh mặt sau
  Future<CccdExtractionModel> extractCccdFromFiles({
    required String frontImagePath,
    required String backImagePath,
    String model = 'gemini-2.0-flash-001',
    int maxTokens = 1000,
    double temperature = 0.1,
  }) async {
    try {
      _logger.i('=== START: CccdExtractionService.extractCccdFromFiles ===');
      _logger.i('Front image path: $frontImagePath');
      _logger.i('Back image path: $backImagePath');

      // Convert images to base64
      final frontImageBase64 = await _convertImageToBase64(frontImagePath);
      final backImageBase64 = await _convertImageToBase64(backImagePath);

      _logger.i('Images converted to base64 successfully');

      return await extractCccd(
        frontImage: frontImageBase64,
        backImage: backImageBase64,
        model: model,
        maxTokens: maxTokens,
        temperature: temperature,
      );
    } catch (e) {
      _logger.e('Error converting images to base64: $e');
      throw CccdExtractionException(
        message: 'Lỗi chuyển đổi ảnh sang base64: $e',
        type: CccdExtractionExceptionType.imageConversionError,
        originalException: e,
      );
    }
  }

  /// Kiểm tra tính khả dụng của CCCD extraction API
  Future<bool> checkApiAvailability() async {
    try {
      // Gửi request test với ảnh rỗng để kiểm tra kết nối
      await _apiService.post(
        _extractCccdEndpoint,
        data: {
          'frontImage': '',
          'backImage': '',
        },
      );
      return true;
    } catch (e) {
      _logger.w('CCCD extraction API not available: $e');
      return false;
    }
  }

  /// Validate base64 image format
  bool _isValidBase64Image(String base64String) {
    if (base64String.isEmpty) return false;
    
    // Kiểm tra format data:image/...
    if (!base64String.startsWith('data:image/')) return false;
    
    // Kiểm tra có chứa base64 data
    if (!base64String.contains(';base64,')) return false;
    
    // Kiểm tra phần base64 có hợp lệ
    final parts = base64String.split(';base64,');
    if (parts.length != 2) return false;
    
    final base64Data = parts[1];
    if (base64Data.isEmpty) return false;
    
    // Kiểm tra base64 chỉ chứa ký tự hợp lệ
    final validChars = RegExp(r'^[A-Za-z0-9+/]*={0,2}$');
    return validChars.hasMatch(base64Data);
  }

  /// Convert image file to base64
  Future<String> _convertImageToBase64(String imagePath) async {
    try {
      final bytes = await File(imagePath).readAsBytes();
      final base64String = base64Encode(bytes);
      
      // Xác định mime type từ extension
      final extension = imagePath.split('.').last.toLowerCase();
      String mimeType;
      
      switch (extension) {
        case 'jpg':
        case 'jpeg':
          mimeType = 'image/jpeg';
          break;
        case 'png':
          mimeType = 'image/png';
          break;
        case 'gif':
          mimeType = 'image/gif';
          break;
        case 'webp':
          mimeType = 'image/webp';
          break;
        default:
          mimeType = 'image/jpeg';
      }
      
      return 'data:$mimeType;base64,$base64String';
    } catch (e) {
      throw Exception('Không thể đọc file ảnh: $e');
    }
  }
}

/// Custom exception cho CCCD Extraction service
class CccdExtractionException implements Exception {
  final String message;
  final CccdExtractionExceptionType type;
  final Object? originalException;

  const CccdExtractionException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'CccdExtractionException: $message (Type: $type)';
}

/// Loại lỗi CCCD Extraction
enum CccdExtractionExceptionType {
  missingFrontImage,
  missingBackImage,
  missingImages,
  invalidBase64Format,
  invalidCccdFormat,
  cccdExpired,
  cccdExpiringSoon,
  poorImageQuality,
  extractionFailed,
  invalidResponse,
  invalidDateFormat,
  imageConversionError,
  apiError,
  unauthorized,
  unknown,
} 