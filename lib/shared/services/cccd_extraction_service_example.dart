import 'package:kiloba_biz/shared/services/cccd_extraction_service.dart';
import 'package:kiloba_biz/shared/utils/app_logger.dart';

/// <PERSON><PERSON> dụ sử dụng CCCD Extraction Service
class CccdExtractionExample {
  static final CccdExtractionService _service = CccdExtractionService();
  static final AppLogger _logger = AppLogger();

  /// V<PERSON> dụ trích xuất CCCD từ base64 images
  static Future<void> extractCccdFromBase64() async {
    try {
      _logger.i('=== START: CccdExtractionExample.extractCccdFromBase64 ===');

      // <PERSON><PERSON><PERSON> sử có ảnh base64 từ camera hoặc file picker
      const String frontImageBase64 = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...';
      const String backImageBase64 = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...';

      final result = await _service.extractCccd(
        frontImage: frontImageBase64,
        backImage: backImageBase64,
        model: 'gemini-2.0-flash-001',
        maxTokens: 1000,
        temperature: 0.1,
      );

      _logger.i('CCCD extraction successful!');
      _logger.i('ID Number: ${result.idNumber}');
      _logger.i('Full Name: ${result.fullName}');
      _logger.i('Date of Birth: ${result.dateOfBirth}');
      _logger.i('Gender: ${result.gender}');
      _logger.i('Issue Date: ${result.issueDate}');
      _logger.i('Issue Place: ${result.issuePlace}');
      _logger.i('Expiry Date: ${result.expiryDate}');
      _logger.i('Nationality: ${result.nationality}');
      _logger.i('Place of Origin: ${result.placeOfOrigin}');
      _logger.i('Place of Residence: ${result.placeOfResidence}');
      _logger.i('Confidence: ${result.confidence}');
      _logger.i('Is Expired: ${result.isExpired}');
      _logger.i('Days Until Expiry: ${result.daysUntilExpiry}');
      _logger.i('Warnings: ${result.warnings}');

      // Xử lý kết quả
      if (result.isExpired) {
        _logger.w('CCCD đã hết hạn!');
      } else if (result.daysUntilExpiry <= 180) {
        _logger.w('CCCD sắp hết hạn trong ${result.daysUntilExpiry} ngày');
      }

      if (result.warnings.isNotEmpty) {
        _logger.w('Có cảnh báo: ${result.warnings.join(', ')}');
      }

      _logger.i('=== END: CccdExtractionExample.extractCccdFromBase64 ===');
    } on CccdExtractionException catch (e) {
      _logger.e('CCCD extraction failed: ${e.message}');
      _logger.e('Error type: ${e.type}');
      
      // Xử lý các loại lỗi cụ thể
      switch (e.type) {
        case CccdExtractionExceptionType.missingFrontImage:
        case CccdExtractionExceptionType.missingBackImage:
          _logger.e('Vui lòng chụp đủ ảnh mặt trước và mặt sau CCCD');
          break;
        case CccdExtractionExceptionType.invalidBase64Format:
          _logger.e('Format ảnh không hợp lệ, vui lòng chụp lại');
          break;
        case CccdExtractionExceptionType.poorImageQuality:
          _logger.e('Chất lượng ảnh kém, vui lòng chụp rõ hơn');
          break;
        case CccdExtractionExceptionType.cccdExpired:
          _logger.e('CCCD đã hết hạn, vui lòng sử dụng CCCD còn hạn');
          break;
        case CccdExtractionExceptionType.cccdExpiringSoon:
          _logger.e('CCCD sắp hết hạn, vui lòng gia hạn CCCD');
          break;
        case CccdExtractionExceptionType.extractionFailed:
          _logger.e('Không thể trích xuất thông tin, vui lòng thử lại');
          break;
        default:
          _logger.e('Lỗi không xác định: ${e.message}');
      }
    } catch (e) {
      _logger.e('Unexpected error: $e');
    }
  }

  /// Ví dụ trích xuất CCCD từ file paths
  static Future<void> extractCccdFromFiles() async {
    try {
      _logger.i('=== START: CccdExtractionExample.extractCccdFromFiles ===');

      const String frontImagePath = '/path/to/front_image.jpg';
      const String backImagePath = '/path/to/back_image.jpg';

      final result = await _service.extractCccdFromFiles(
        frontImagePath: frontImagePath,
        backImagePath: backImagePath,
      );

      _logger.i('CCCD extraction from files successful!');
      _logger.i('Extracted data: $result');

      _logger.i('=== END: CccdExtractionExample.extractCccdFromFiles ===');
    } on CccdExtractionException catch (e) {
      _logger.e('CCCD extraction from files failed: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error: $e');
    }
  }

  /// Ví dụ kiểm tra tính khả dụng của API
  static Future<void> checkApiAvailability() async {
    try {
      _logger.i('=== START: CccdExtractionExample.checkApiAvailability ===');

      final isAvailable = await _service.checkApiAvailability();
      
      if (isAvailable) {
        _logger.i('CCCD extraction API is available');
      } else {
        _logger.w('CCCD extraction API is not available');
      }

      _logger.i('=== END: CccdExtractionExample.checkApiAvailability ===');
    } catch (e) {
      _logger.e('Error checking API availability: $e');
    }
  }

  /// Ví dụ xử lý lỗi chi tiết
  static void handleCccdExtractionError(CccdExtractionException e) {
    _logger.e('Handling CCCD extraction error: ${e.type}');
    
    String userMessage;
    bool canRetry = false;
    
    switch (e.type) {
      case CccdExtractionExceptionType.missingFrontImage:
        userMessage = 'Vui lòng chụp ảnh mặt trước CCCD';
        canRetry = true;
        break;
      case CccdExtractionExceptionType.missingBackImage:
        userMessage = 'Vui lòng chụp ảnh mặt sau CCCD';
        canRetry = true;
        break;
      case CccdExtractionExceptionType.invalidBase64Format:
        userMessage = 'Format ảnh không hợp lệ, vui lòng chụp lại';
        canRetry = true;
        break;
      case CccdExtractionExceptionType.poorImageQuality:
        userMessage = 'Chất lượng ảnh kém, vui lòng chụp rõ hơn trong điều kiện ánh sáng tốt';
        canRetry = true;
        break;
      case CccdExtractionExceptionType.cccdExpired:
        userMessage = 'CCCD đã hết hạn, vui lòng sử dụng CCCD còn hạn';
        canRetry = false;
        break;
      case CccdExtractionExceptionType.cccdExpiringSoon:
        userMessage = 'CCCD sắp hết hạn, vui lòng gia hạn CCCD';
        canRetry = false;
        break;
      case CccdExtractionExceptionType.extractionFailed:
        userMessage = 'Không thể trích xuất thông tin, vui lòng thử lại';
        canRetry = true;
        break;
      case CccdExtractionExceptionType.apiError:
        userMessage = 'Lỗi kết nối, vui lòng kiểm tra mạng và thử lại';
        canRetry = true;
        break;
      default:
        userMessage = 'Có lỗi xảy ra, vui lòng thử lại sau';
        canRetry = true;
    }
    
    _logger.i('User message: $userMessage');
    _logger.i('Can retry: $canRetry');
    
    // Ở đây có thể hiển thị dialog hoặc snackbar cho user
    // showErrorDialog(userMessage, canRetry);
  }
} 