import 'package:dio/dio.dart';

/// Model cho pending request trong queue
class PendingRequest {
  final DioException originalError;
  final ErrorInterceptorHandler handler;
  final DateTime queuedAt;
  final int retryCount;

  const PendingRequest({
    required this.originalError,
    required this.handler,
    required this.queuedAt,
    this.retryCount = 0,
  });

  /// Tạo pending request mới
  factory PendingRequest.create(
    DioException error,
    ErrorInterceptorHandler handler,
  ) {
    return PendingRequest(
      originalError: error,
      handler: handler,
      queuedAt: DateTime.now(),
    );
  }

  /// Tạo copy với retry count tăng
  PendingRequest incrementRetry() {
    return PendingRequest(
      originalError: originalError,
      handler: handler,
      queuedAt: queuedAt,
      retryCount: retryCount + 1,
    );
  }

  /// Kiểm tra request có expired không
  bool isExpired(Duration timeout) {
    final waitTime = DateTime.now().difference(queuedAt);
    return waitTime > timeout;
  }

  /// <PERSON><PERSON>y thời gian đã chờ
  Duration getWaitTime() {
    return DateTime.now().difference(queuedAt);
  }

  /// Lấy thông tin request
  String getRequestInfo() {
    return '${originalError.requestOptions.method} ${originalError.requestOptions.path}';
  }

  @override
  String toString() {
    return 'PendingRequest(${getRequestInfo()}, retryCount: $retryCount, waitTime: ${getWaitTime().inSeconds}s)';
  }
}
