import 'dart:async';
import 'package:dio/dio.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'pending_request.dart';
import '../auth/token_repository.dart';

/// Service để quản lý queue requests
class RequestQueueService {
  final TokenRepository _tokenRepository;
  final IAppLogger _logger;
  final Duration _timeout;
  final int _maxRetries;

  final List<PendingRequest> _pendingRequests = [];
  bool _isProcessing = false;

  RequestQueueService({
    required TokenRepository tokenRepository,
    required Duration timeout,
    required int maxRetries,
    required IAppLogger logger,
  }) : _tokenRepository = tokenRepository,
       _timeout = timeout,
       _maxRetries = maxRetries,
       _logger = logger;

  /// Thêm request vào queue
  void enqueue(DioException error, ErrorInterceptorHandler handler) {
    final pendingRequest = PendingRequest.create(error, handler);
    _pendingRequests.add(pendingRequest);

    _logger.i(
      '📋 Queued request: ${pendingRequest.getRequestInfo()} (Total queued: ${_pendingRequests.length})',
    );

    // Cleanup expired requests
    _cleanupExpiredRequests();
  }

  /// Xử lý tất cả pending requests
  Future<void> processPendingRequests(Dio dio) async {
    if (_pendingRequests.isEmpty) {
      _logger.d('📋 No pending requests to process');
      return;
    }

    if (_isProcessing) {
      _logger.d('📋 Already processing pending requests');
      return;
    }

    _isProcessing = true;

    try {
      _logger.i('🔄 Processing ${_pendingRequests.length} pending requests');

      final token = await _tokenRepository.getAccessToken();
      if (token == null) {
        _logger.w(
          '⚠️ No access token available for pending requests - clearing all',
        );
        _clearAllRequests();
        return;
      }

      // Process all pending requests
      final requestsToProcess = List<PendingRequest>.from(_pendingRequests);
      _pendingRequests.clear();

      _logger.d(
        '📋 Processing ${requestsToProcess.length} requests with token: ${token.substring(0, 10)}...',
      );

      for (int i = 0; i < requestsToProcess.length; i++) {
        final request = requestsToProcess[i];
        await _processRequest(
          request,
          dio,
          token,
          i + 1,
          requestsToProcess.length,
        );
      }

      _logger.i(
        '✅ Finished processing ${requestsToProcess.length} pending requests',
      );
    } catch (e) {
      _logger.e('❌ Error processing pending requests: $e');
      _clearAllRequests();
    } finally {
      _isProcessing = false;
    }
  }

  /// Xử lý một request cụ thể
  Future<void> _processRequest(
    PendingRequest request,
    Dio dio,
    String token,
    int index,
    int total,
  ) async {
    try {
      _logger.d(
        '🔄 Retrying request $index/$total: ${request.getRequestInfo()}',
      );

      final requestOptions = request.originalError.requestOptions.copyWith();
      requestOptions.headers['Authorization'] = 'Bearer $token';

      final retryResponse = await dio.fetch(requestOptions);
      request.handler.resolve(retryResponse);

      _logger.i('✅ Successfully retried: ${request.getRequestInfo()}');
    } catch (retryError) {
      _logger.e('❌ Failed to retry pending request $index/$total: $retryError');

      if (retryError is DioException &&
          retryError.response?.statusCode == 401) {
        // Still 401 after refresh - auth is completely invalid
        _logger.w('⚠️ Still getting 401 after token refresh - auth is invalid');
        _logger.w(
          '⚠️ This request will NOT trigger another refresh to avoid infinite loop',
        );
        request.handler.next(request.originalError);
      } else {
        // Other error during retry
        request.handler.next(
          retryError is DioException ? retryError : request.originalError,
        );
      }
    }
  }

  /// Cleanup expired requests
  void _cleanupExpiredRequests() {
    final toRemove = <PendingRequest>[];

    for (final request in _pendingRequests) {
      if (request.isExpired(_timeout)) {
        toRemove.add(request);
        _logger.w(
          '⏰ Removing expired pending request: ${request.getRequestInfo()} (waited: ${request.getWaitTime().inSeconds}s)',
        );

        // Complete with timeout error
        request.handler.next(
          DioException(
            requestOptions: request.originalError.requestOptions,
            message:
                'Request timeout while waiting for token refresh (${request.getWaitTime().inSeconds}s)',
            type: DioExceptionType.receiveTimeout,
          ),
        );
      }
    }

    for (final request in toRemove) {
      _pendingRequests.remove(request);
    }

    if (toRemove.isNotEmpty) {
      _logger.w(
        '⏰ Cleaned up ${toRemove.length} expired requests. Remaining: ${_pendingRequests.length}',
      );
    }
  }

  /// Clear tất cả pending requests
  void _clearAllRequests() {
    final count = _pendingRequests.length;
    _pendingRequests.clear();
    _logger.w('🧹 Cleared $count pending requests');
  }

  /// Clear tất cả pending requests (public method)
  void clearAllRequests() {
    _clearAllRequests();
  }

  /// Lấy số lượng pending requests
  int get pendingRequestsCount => _pendingRequests.length;

  /// Kiểm tra có đang processing không
  bool get isProcessing => _isProcessing;

  /// Lấy thông tin debug
  Map<String, dynamic> getDebugInfo() {
    return {
      'pendingRequestsCount': _pendingRequests.length,
      'isProcessing': _isProcessing,
      'timeout': _timeout.inSeconds,
      'maxRetries': _maxRetries,
    };
  }

  /// Debug method để xem chi tiết queue
  void debugQueueDetails() {
    _logger.i('🔍 === Request Queue Details ===');
    _logger.i('isProcessing: $_isProcessing');
    _logger.i('pendingRequests count: ${_pendingRequests.length}');
    _logger.i('timeout: ${_timeout.inSeconds}s');
    _logger.i('maxRetries: $_maxRetries');

    if (_pendingRequests.isNotEmpty) {
      _logger.i('📋 Pending requests:');
      for (int i = 0; i < _pendingRequests.length; i++) {
        final request = _pendingRequests[i];
        _logger.i('  ${i + 1}. ${request.getRequestInfo()}');
        _logger.i('     Queued at: ${request.queuedAt}');
        _logger.i('     Wait time: ${request.getWaitTime().inSeconds}s');
        _logger.i('     Retry count: ${request.retryCount}');
      }
    }

    _logger.i('================================');
  }
}
