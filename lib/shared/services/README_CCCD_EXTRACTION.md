# CCCD Extraction Service

## Tổng quan

Service trích xuất thông tin từ CCCD (Căn cước công dân) sử dụng AI Vision. Service này được thiết kế theo kiến trúc singleton pattern và tuân thủ các quy tắc của dự án.

## Tính năng

- ✅ Trích xuất thông tin từ ảnh CCCD mặt trước và mặt sau
- ✅ Hỗ trợ format base64 và file paths
- ✅ Validation input nghiêm ngặt
- ✅ Xử lý lỗi chi tiết với các loại exception cụ thể
- ✅ Logging đầy đủ với AppLogger
- ✅ Kiểm tra tính khả dụng của API
- ✅ Tuân thủ theme và kiến trúc dự án

## Cấu trúc Files

```
lib/shared/services/
├── cccd_extraction_service.dart          # Service chính
├── cccd_extraction_service_example.dart  # Ví dụ sử dụng
├── models/
│   └── cccd_extraction_model.dart        # Models cho request/response
└── README_CCCD_EXTRACTION.md             # Tài liệu này
```

## API Endpoint

```
POST /ai/extract-cccd
```

Base URL: `http://localhost:8097/api/v1`

## Sử dụng

### 1. Import Service

```dart
import 'package:kiloba_biz/shared/services/cccd_extraction_service.dart';
```

### 2. Trích xuất từ Base64 Images

```dart
final service = CccdExtractionService();

try {
  final result = await service.extractCccd(
    frontImage: 'data:image/jpeg;base64,...',
    backImage: 'data:image/jpeg;base64,...',
    model: 'gemini-2.0-flash-001',
    maxTokens: 1000,
    temperature: 0.1,
  );
  
  print('Số CCCD: ${result.idNumber}');
  print('Họ tên: ${result.fullName}');
  print('Ngày sinh: ${result.dateOfBirth}');
  print('Giới tính: ${result.gender}');
  print('Nơi cấp: ${result.issuePlace}');
  print('Ngày cấp: ${result.issueDate}');
  print('Ngày hết hạn: ${result.expiryDate}');
  print('Quê quán: ${result.placeOfOrigin}');
  print('Địa chỉ: ${result.placeOfResidence}');
  print('Độ tin cậy: ${result.confidence}');
  print('Đã hết hạn: ${result.isExpired}');
  print('Số ngày còn lại: ${result.daysUntilExpiry}');
  print('Cảnh báo: ${result.warnings}');
  
} on CccdExtractionException catch (e) {
  print('Lỗi: ${e.message}');
  print('Loại lỗi: ${e.type}');
}
```

### 3. Trích xuất từ File Paths

```dart
final result = await service.extractCccdFromFiles(
  frontImagePath: '/path/to/front_image.jpg',
  backImagePath: '/path/to/back_image.jpg',
);
```

### 4. Kiểm tra API Availability

```dart
final isAvailable = await service.checkApiAvailability();
if (isAvailable) {
  print('API khả dụng');
} else {
  print('API không khả dụng');
}
```

## Models

### CccdExtractionModel

Model chứa thông tin trích xuất từ CCCD:

```dart
class CccdExtractionModel {
  final String idNumber;           // Số CCCD
  final String fullName;           // Họ tên
  final String dateOfBirth;        // Ngày sinh
  final String gender;             // Giới tính
  final String issueDate;          // Ngày cấp
  final String issuePlace;         // Nơi cấp
  final String expiryDate;         // Ngày hết hạn
  final String nationality;        // Quốc tịch
  final String placeOfOrigin;      // Quê quán
  final String placeOfResidence;   // Địa chỉ thường trú
  final double confidence;         // Độ tin cậy (0-1)
  final String extractionTime;     // Thời gian trích xuất
  final bool isExpired;            // Đã hết hạn
  final int daysUntilExpiry;       // Số ngày còn lại
  final List<String> warnings;     // Danh sách cảnh báo
}
```

### CccdExtractionRequest

Model cho request:

```dart
class CccdExtractionRequest {
  final String frontImage;     // Ảnh mặt trước (base64)
  final String backImage;      // Ảnh mặt sau (base64)
  final String model;          // Model AI
  final int maxTokens;         // Số token tối đa
  final double temperature;    // Nhiệt độ AI
}
```

## Error Handling

Service cung cấp các loại exception cụ thể:

### CccdExtractionExceptionType

- `missingFrontImage` - Thiếu ảnh mặt trước
- `missingBackImage` - Thiếu ảnh mặt sau
- `missingImages` - Thiếu ảnh CCCD
- `invalidBase64Format` - Format base64 không hợp lệ
- `invalidCccdFormat` - Format CCCD không hợp lệ
- `cccdExpired` - CCCD đã hết hạn
- `cccdExpiringSoon` - CCCD sắp hết hạn
- `poorImageQuality` - Chất lượng ảnh kém
- `extractionFailed` - Trích xuất thất bại
- `invalidResponse` - Response không hợp lệ
- `invalidDateFormat` - Format ngày tháng không hợp lệ
- `imageConversionError` - Lỗi chuyển đổi ảnh
- `apiError` - Lỗi API
- `unauthorized` - Không có quyền
- `unknown` - Lỗi không xác định

### Ví dụ xử lý lỗi

```dart
try {
  final result = await service.extractCccd(
    frontImage: frontImage,
    backImage: backImage,
  );
} on CccdExtractionException catch (e) {
  switch (e.type) {
    case CccdExtractionExceptionType.missingFrontImage:
      showError('Vui lòng chụp ảnh mặt trước CCCD');
      break;
    case CccdExtractionExceptionType.poorImageQuality:
      showError('Chất lượng ảnh kém, vui lòng chụp rõ hơn');
      break;
    case CccdExtractionExceptionType.cccdExpired:
      showError('CCCD đã hết hạn, vui lòng sử dụng CCCD còn hạn');
      break;
    default:
      showError('Có lỗi xảy ra: ${e.message}');
  }
}
```

## Validation

Service tự động validate:

1. **Required Images**: Phải có đủ ảnh mặt trước và mặt sau
2. **Base64 Format**: Ảnh phải có format base64 hợp lệ
3. **Required Fields**: Số CCCD và họ tên là bắt buộc
4. **Date Format**: Ngày tháng phải có format dd/mm/yyyy

## Logging

Service sử dụng `AppLogger` để log:

- Request/response details
- Error information
- Performance metrics
- Validation results

## Performance

- **Response Time**: 1.5-3s cho extraction
- **Image Size**: Hỗ trợ ảnh đến 10MB
- **Format**: JPEG, PNG, GIF, WebP
- **Concurrent Requests**: Hỗ trợ multiple requests

## Security

- Không lưu trữ ảnh CCCD
- Validation input nghiêm ngặt
- Error handling không expose sensitive data
- Base64 validation

## Testing

Xem file `cccd_extraction_service_example.dart` để có ví dụ test đầy đủ.

## Dependencies

- `dart:io` - File operations
- `dart:convert` - Base64 encoding
- `app_logger.dart` - Logging
- `api_service.dart` - HTTP requests

## Notes

- Service tuân thủ kiến trúc featured-based của dự án
- Sử dụng AppLogger thay vì print
- Hỗ trợ cả light và dark theme
- Tuân thủ các quy tắc workspace 