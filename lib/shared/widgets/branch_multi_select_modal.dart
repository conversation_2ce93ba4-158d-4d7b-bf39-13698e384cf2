import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../core/theme/index.dart';
import '../models/branch_model.dart';

/// Branch Multi-Select Modal for transactions
class BranchMultiSelectModal extends StatefulWidget {
  final String title;
  final List<BranchModel> items;
  final List<BranchModel> selectedItems;
  final Color color;
  final IconData icon;
  final Function(List<BranchModel>) onSelectionChanged;
  final ScrollController scrollController;
  final String Function(BranchModel) itemToString;

  const BranchMultiSelectModal({
    super.key,
    required this.title,
    required this.items,
    required this.selectedItems,
    required this.color,
    required this.icon,
    required this.onSelectionChanged,
    required this.itemToString,
    required this.scrollController,
  });

  @override
  State<BranchMultiSelectModal> createState() => _BranchMultiSelectModalState();
}

class _BranchMultiSelectModalState extends State<BranchMultiSelectModal> {
  late List<BranchModel> _tempSelectedItems;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tempSelectedItems = List<BranchModel>.from(widget.selectedItems);
  }

  List<BranchModel> get _filteredItems {
    if (_searchQuery.isEmpty) {
      return widget.items;
    }
    return widget.items.where((item) => 
      widget.itemToString(item).toLowerCase().contains(_searchQuery.toLowerCase())
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppDimensions.paddingS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Row(
                children: [
                  Icon(
                    widget.icon,
                    color: widget.color,
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: AppTypography.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (_tempSelectedItems.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: widget.color.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(
                          color: widget.color.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        '${_tempSelectedItems.length}',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: widget.color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  const SizedBox(width: AppDimensions.spacingS),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      TablerIcons.x,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: InputDecoration(
                hintText: 'Tìm kiếm...',
                prefixIcon: Icon(TablerIcons.search, color: widget.color),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: widget.color, width: 2),
                ),
              ),
            ),
          ),

          const SizedBox(height: AppDimensions.spacingM),

          // Items list
          Expanded(
            child: ListView.builder(
              controller: widget.scrollController,
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final item = _filteredItems[index];
                final isSelected = _tempSelectedItems.any((selected) => 
                    selected.id == item.id);
                
                return Container(
                  margin: const EdgeInsets.only(bottom: AppDimensions.spacingS),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            _tempSelectedItems.removeWhere((selected) => 
                              selected.id == item.id);
                          } else {
                            _tempSelectedItems.add(item);
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        decoration: BoxDecoration(
                          color: isSelected 
                            ? widget.color.withValues(alpha: 0.15)
                            : Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                          border: Border.all(
                            color: isSelected
                              ? widget.color
                              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                widget.itemToString(item),
                                style: AppTypography.textTheme.bodyMedium?.copyWith(
                                  color: isSelected
                                    ? widget.color
                                    : Theme.of(context).colorScheme.onSurface,
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                ),
                              ),
                            ),
                            if (isSelected)
                              Icon(
                                TablerIcons.check,
                                color: widget.color,
                                size: AppDimensions.iconS,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Bottom actions
          Container(
            padding: EdgeInsets.only(
              left: AppDimensions.paddingL,
              right: AppDimensions.paddingL,
              top: AppDimensions.paddingM,
              bottom: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom + AppDimensions.paddingL,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _tempSelectedItems.clear();
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                      side: BorderSide(color: widget.color),
                    ),
                    child: Text(
                      'Xóa tất cả',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: widget.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onSelectionChanged(_tempSelectedItems);
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.color,
                      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                    ),
                    child: Text(
                      'Áp dụng',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
