import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:markdown_widget/markdown_widget.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:kiloba_biz/core/theme/index.dart';
import 'package:kiloba_biz/core/article/services/article_service.dart';
import 'package:kiloba_biz/core/article/models/article_model.dart';
import 'package:kiloba_biz/shared/utils/app_logger.dart';

/// Màn hình hiển thị nội dung bài viết dạng markdown
class ArticleViewerScreen extends StatefulWidget {
  final String articleCode;
  final String title;

  const ArticleViewerScreen({
    super.key,
    required this.articleCode,
    required this.title,
  });

  @override
  State<ArticleViewerScreen> createState() => _ArticleViewerScreenState();
}

class _ArticleViewerScreenState extends State<ArticleViewerScreen> {
  final ArticleService _articleService = ArticleService();
  final AppLogger _logger = AppLogger();

  ArticleModel? _article;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // Đợi animation transition hoàn thành trước khi gọi API
    // để tránh làm giật animation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Future.delayed(const Duration(milliseconds: 350), () {
        if (mounted) {
          _loadArticle();
        }
      });
    });
  }

  Future<void> _loadArticle() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      _logger.i('Loading article with code: ${widget.articleCode}');

      final article = await _articleService.getArticleByCode(
        widget.articleCode,
      );

      if (mounted) {
        setState(() {
          _article = article;
          _isLoading = false;
        });

        _logger.i('Article loaded successfully: ${article.title}');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = e.toString();
        });

        _logger.e('Error loading article: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode
          ? AppColors.backgroundDark
          : AppColors.backgroundPrimary,
      appBar: AppBar(
        backgroundColor: isDarkMode
            ? AppColors.kienlongDarkBlue
            : AppColors.kienlongOrange,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppDimensions.appBarElevation,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(TablerIcons.arrow_left, color: AppColors.textOnPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          widget.title,
          style: AppTypography.textTheme.titleLarge?.copyWith(
            color: AppColors.textOnPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: isDarkMode ? Brightness.dark : Brightness.dark,
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_article == null) {
      return _buildEmptyState();
    }

    return _buildArticleContent();
  }

  Widget _buildLoadingState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      color: isDarkMode
          ? AppColors.backgroundDark
          : AppColors.backgroundPrimary,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // Skeleton header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.backgroundDarkSecondary
                  : AppColors.backgroundSecondary,
              border: Border(
                bottom: BorderSide(
                  color: isDarkMode
                      ? AppColors.borderDark.withValues(alpha: 0.3)
                      : AppColors.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title skeleton
                Container(
                  height: 28,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? AppColors.neutral700.withValues(alpha: 0.3)
                        : AppColors.neutral300.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                SizedBox(height: AppDimensions.spacingM),
                // Summary skeleton
                Container(
                  height: 16,
                  width: MediaQuery.of(context).size.width * 0.8,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? AppColors.neutral700.withValues(alpha: 0.2)
                        : AppColors.neutral300.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                SizedBox(height: AppDimensions.spacingS),
                Container(
                  height: 16,
                  width: MediaQuery.of(context).size.width * 0.6,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? AppColors.neutral700.withValues(alpha: 0.2)
                        : AppColors.neutral300.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                SizedBox(height: AppDimensions.spacingS),
                // Date skeleton
                Container(
                  height: 14,
                  width: MediaQuery.of(context).size.width * 0.4,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? AppColors.neutral700.withValues(alpha: 0.15)
                        : AppColors.neutral300.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ],
            ),
          ),

          // Content skeleton
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: AppDimensions.spacingM),

                // Loading indicator với text
                Center(
                  child: Column(
                    children: [
                      SizedBox(
                        width: 32,
                        height: 32,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.kienlongOrange,
                          ),
                        ),
                      ),
                      SizedBox(height: AppDimensions.spacingM),
                      Text(
                        'Đang tải nội dung...',
                        style: AppTypography.textTheme.bodyMedium?.copyWith(
                          color: isDarkMode
                              ? AppColors.neutral300
                              : AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: AppDimensions.spacingXL),

                // Content lines skeleton
                ...List.generate(
                  12,
                  (index) => Container(
                    margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
                    height: 14,
                    width: index % 4 == 0
                        ? MediaQuery.of(context).size.width * 0.9
                        : index % 4 == 1
                        ? MediaQuery.of(context).size.width * 0.7
                        : index % 4 == 2
                        ? MediaQuery.of(context).size.width * 0.85
                        : MediaQuery.of(context).size.width * 0.6,
                    decoration: BoxDecoration(
                      color: isDarkMode
                          ? AppColors.neutral700.withValues(alpha: 0.1)
                          : AppColors.neutral300.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),

                SizedBox(height: AppDimensions.spacingXL),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      color: isDarkMode
          ? AppColors.backgroundDark
          : AppColors.backgroundPrimary,
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  TablerIcons.alert_circle,
                  size: 40,
                  color: AppColors.error,
                ),
              ),
              SizedBox(height: AppDimensions.spacingL),
              Text(
                'Không thể tải nội dung',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  color: isDarkMode
                      ? AppColors.neutral100
                      : AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingM),
              Text(
                _errorMessage ?? 'Đã xảy ra lỗi không xác định',
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  color: isDarkMode
                      ? AppColors.neutral300
                      : AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingXL),
              ElevatedButton.icon(
                onPressed: _loadArticle,
                icon: Icon(TablerIcons.refresh),
                label: Text('Thử lại'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongOrange,
                  foregroundColor: AppColors.textOnPrimary,
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingL,
                    vertical: AppDimensions.paddingM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      color: isDarkMode
          ? AppColors.backgroundDark
          : AppColors.backgroundPrimary,
      child: Center(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  TablerIcons.file_text,
                  size: 40,
                  color: AppColors.warning,
                ),
              ),
              SizedBox(height: AppDimensions.spacingL),
              Text(
                'Không tìm thấy nội dung',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  color: isDarkMode
                      ? AppColors.neutral100
                      : AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingM),
              Text(
                'Bài viết này có thể đã bị xóa hoặc không tồn tại',
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  color: isDarkMode
                      ? AppColors.neutral300
                      : AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildArticleContent() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final article = _article!;

    return Container(
      color: isDarkMode
          ? AppColors.backgroundDark
          : AppColors.backgroundPrimary,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // Article header - có thể cuộn cùng với content
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.backgroundDarkSecondary
                  : AppColors.backgroundSecondary,
              border: Border(
                bottom: BorderSide(
                  color: isDarkMode
                      ? AppColors.borderDark.withValues(alpha: 0.3)
                      : AppColors.borderLight,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  article.title,
                  style: AppTypography.textTheme.headlineSmall?.copyWith(
                    color: isDarkMode
                        ? AppColors.neutral100
                        : AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (article.summary != null) ...[
                  SizedBox(height: AppDimensions.spacingM),
                  Text(
                    article.summary!,
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: isDarkMode
                          ? AppColors.neutral300
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
                SizedBox(height: AppDimensions.spacingM),
                Row(
                  children: [
                    Icon(
                      TablerIcons.calendar,
                      size: 16,
                      color: isDarkMode
                          ? AppColors.neutral400
                          : AppColors.textTertiary,
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Cập nhật: ${_formatDate(article.updatedAt)}',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: isDarkMode
                            ? AppColors.neutral400
                            : AppColors.textTertiary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Article content
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: _buildMarkdownContent(article.content ?? ''),
          ),
        ],
      ),
    );
  }

  Widget _buildMarkdownContent(String content) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Cấu hình markdown cho dark/light theme
    final config = isDarkMode
        ? MarkdownConfig.darkConfig
        : MarkdownConfig.defaultConfig;

    // Custom code wrapper cho syntax highlighting
    Widget codeWrapper(Widget child, String text, String? language) =>
        _buildCodeWrapper(child, text, language, isDarkMode);

    return MarkdownWidget(
      data: content,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      config: config.copy(
        configs: [
          // Cấu hình cho code blocks
          isDarkMode
              ? PreConfig.darkConfig.copy(wrapper: codeWrapper)
              : PreConfig().copy(wrapper: codeWrapper),

          // Cấu hình cho links
          LinkConfig(
            style: TextStyle(
              color: AppColors.kienlongOrange,
              decoration: TextDecoration.underline,
              fontWeight: FontWeight.w500,
            ),
            onTap: (url) {
              _logger.i('Link tapped: $url');
              // TODO: Implement link handling (open in browser, etc.)
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCodeWrapper(
    Widget child,
    String text,
    String? language,
    bool isDarkMode,
  ) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.8)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (language != null && language.isNotEmpty) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingM,
                vertical: AppDimensions.paddingS,
              ),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? AppColors.neutral700.withValues(alpha: 0.8)
                    : AppColors.neutral200,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusM),
                  topRight: Radius.circular(AppDimensions.radiusM),
                ),
              ),
              child: Text(
                language.toUpperCase(),
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: child,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
