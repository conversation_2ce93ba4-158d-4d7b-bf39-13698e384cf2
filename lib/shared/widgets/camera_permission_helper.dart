import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../core/theme/index.dart';
import '../services/camera_permission_service.dart';
import '../utils/app_logger.dart';

/// Helper widget để xử lý UI cho camera permission
class CameraPermissionHelper {
  static final AppLogger _logger = AppLogger();
  static final CameraPermissionService _permissionService = CameraPermissionService();

  /// Kiểm tra và yêu cầu camera permission với UI feedback
  static Future<bool> ensureCameraPermission(BuildContext context) async {
    try {
      final result = await _permissionService.requestCameraPermissionIfNeeded();
      
      switch (result) {
        case CameraPermissionResult.granted:
          return true;
          
        case CameraPermissionResult.permanentlyDenied:
          if (context.mounted) {
            return await _showPermanentlyDeniedDialog(context);
          }
          return false;
          
        case CameraPermissionResult.denied:
          if (context.mounted) {
            await _showPermissionDeniedDialog(context);
          }
          return false;
          
        case CameraPermissionResult.error:
          if (context.mounted) {
            _showPermissionErrorSnackBar(context, 'Có lỗi xảy ra khi kiểm tra quyền camera');
          }
          return false;
      }
    } catch (e) {
      await _logger.e('Error in ensureCameraPermission: $e');
      if (context.mounted) {
        _showPermissionErrorSnackBar(context, 'Có lỗi xảy ra khi kiểm tra quyền camera');
      }
      return false;
    }
  }

  /// Hiển thị dialog khi permission bị từ chối vĩnh viễn
  static Future<bool> _showPermanentlyDeniedDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              TablerIcons.camera_off,
              color: AppColors.error,
              size: AppDimensions.iconM,
            ),
            SizedBox(width: AppDimensions.spacingS),
            const Text('Cần quyền truy cập camera'),
          ],
        ),
        content: const Text(
          'Ứng dụng cần quyền truy cập camera để quét mã QR. '
          'Vui lòng vào Cài đặt để cấp quyền.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop(true);
              await _permissionService.openSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Mở cài đặt'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  /// Hiển thị dialog khi permission bị từ chối tạm thời
  static Future<void> _showPermissionDeniedDialog(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              TablerIcons.camera_off,
              color: AppColors.warning,
              size: AppDimensions.iconM,
            ),
            SizedBox(width: AppDimensions.spacingS),
            const Text('Không thể truy cập camera'),
          ],
        ),
        content: const Text(
          'Ứng dụng cần quyền truy cập camera để quét mã QR. '
          'Vui lòng cấp quyền để tiếp tục.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // Thử yêu cầu permission lại
              await ensureCameraPermission(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  /// Hiển thị SnackBar thông báo lỗi permission
  static void _showPermissionErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              TablerIcons.alert_circle,
              color: Colors.white,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: AppColors.error,
        action: SnackBarAction(
          label: 'Cài đặt',
          textColor: Colors.white,
          onPressed: () => _permissionService.openSettings(),
        ),
      ),
    );
  }

  /// Hiển thị bottom sheet hướng dẫn cấp quyền camera
  static Future<bool?> showPermissionGuideBottomSheet(BuildContext context) async {
    return await showModalBottomSheet<bool>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        margin: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(AppDimensions.paddingS),
                    decoration: BoxDecoration(
                      color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      TablerIcons.camera,
                      color: AppColors.kienlongSkyBlue,
                      size: AppDimensions.iconL,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Quyền truy cập camera',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: AppDimensions.spacingXS),
                        Text(
                          'Cần thiết để quét mã QR',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: AppDimensions.spacingL),
              
              // Content
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.neutral100,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: Column(
                  children: [
                    _buildPermissionStep(
                      context,
                      '1',
                      'Nhấn "Cấp quyền" bên dưới',
                      'Hệ thống sẽ hiển thị dialog yêu cầu quyền',
                    ),
                    SizedBox(height: AppDimensions.spacingM),
                    _buildPermissionStep(
                      context,
                      '2',
                      'Chọn "Cho phép" trong dialog',
                      'Ứng dụng sẽ có thể truy cập camera',
                    ),
                    SizedBox(height: AppDimensions.spacingM),
                    _buildPermissionStep(
                      context,
                      '3',
                      'Bắt đầu quét mã QR',
                      'Camera sẽ mở và sẵn sàng quét',
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: AppDimensions.spacingL),
              
              // Actions
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Hủy'),
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () async {
                        Navigator.of(context).pop(true);
                        await ensureCameraPermission(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.kienlongOrange,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          vertical: AppDimensions.paddingM,
                        ),
                      ),
                      child: const Text('Cấp quyền'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build permission step widget
  static Widget _buildPermissionStep(
    BuildContext context,
    String stepNumber,
    String title,
    String description,
  ) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: AppColors.kienlongOrange,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              stepNumber,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),
        SizedBox(width: AppDimensions.spacingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: AppDimensions.spacingXS),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
