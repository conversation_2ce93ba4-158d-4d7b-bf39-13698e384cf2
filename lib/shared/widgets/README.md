# Shared Widgets

Th<PERSON> viện các widget dùng chung trong ứng dụng Kiloba Business.

## ArticleViewerScreen

Màn hình hiển thị nội dung bài viết dạng markdown với hỗ trợ dark/light theme.

### Tính năng

- ✅ Hiển thị nội dung markdown từ article
- ✅ Hỗ trợ dark/light theme
- ✅ Loading state và error handling
- ✅ Code syntax highlighting
- ✅ Custom link handling
- ✅ Responsive design
- ✅ Tuân thủ theme system của dự án

### Cách sử dụng

```dart
import 'package:kiloba_biz/shared/widgets/article_viewer_screen.dart';
import 'package:kiloba_biz/shared/constants/article_codes.dart';

// Mở màn hình Article Viewer
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => ArticleViewerScreen(
      articleCode: ArticleCodes.termsOfServiceRegisterV1,
      title: '<PERSON><PERSON><PERSON><PERSON> khoản sử dụng',
    ),
  ),
);
```

### Article Codes

Các mã bài viết có sẵn trong `ArticleCodes`:

```dart
// Điều khoản sử dụng
ArticleCodes.termsOfServiceRegisterV1

// Chính sách bảo mật  
ArticleCodes.privacyPolicyRegisterV1
```

### Theme Support

ArticleViewerScreen tự động:
- Phát hiện dark/light theme hiện tại
- Áp dụng `MarkdownConfig.darkConfig` cho dark mode
- Sử dụng colors từ `AppColors` theme system
- Tuân thủ typography từ `AppTypography`

### Error Handling

- **Loading State**: Hiển thị spinner và text "Đang tải nội dung..."
- **Error State**: Hiển thị icon lỗi, message và nút "Thử lại"
- **Empty State**: Hiển thị khi không tìm thấy bài viết

### Customization

Có thể tùy chỉnh:
- Code block styling
- Link colors và behavior
- Typography cho headings
- Error messages
- Loading indicators 