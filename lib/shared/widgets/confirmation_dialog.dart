import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../core/theme/index.dart';

/// Custom confirmation dialog với hỗ trợ đầy đủ dark/light theme
/// và thiết kế chuyên nghiệp theo guidelines của dự án
class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String content;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final IconData? icon;
  final Color? iconColor;
  final Color? confirmButtonColor;
  final bool isDestructive;

  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.content,
    this.confirmText = 'Xác nhận',
    this.cancelText = 'Hủy',
    this.onConfirm,
    this.onCancel,
    this.icon,
    this.iconColor,
    this.confirmButtonColor,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final defaultIconColor = isDestructive
        ? AppColors.error
        : AppColors.kienlongOrange;
    final defaultConfirmColor = isDestructive
        ? AppColors.error
        : AppColors.kienlongOrange;

    return AlertDialog(
      // Thiết lập background và surface phù hợp với dark theme
      backgroundColor: isDarkMode
          ? AppColors.backgroundDarkSecondary
          : AppColors.backgroundSecondary,
      surfaceTintColor: Colors.transparent,
      elevation: AppDimensions.cardElevationHover,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
      ),

      // Title với icon design chuyên nghiệp
      title: Row(
        children: [
          // Icon container với background alpha
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: (iconColor ?? defaultIconColor).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              icon ??
                  (isDestructive
                      ? TablerIcons.alert_triangle
                      : TablerIcons.info_circle),
              color: iconColor ?? defaultIconColor,
              size: AppDimensions.iconM,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),

          // Title text với theme-aware color
          Expanded(
            child: Text(
              title,
              style: AppTypography.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDarkMode
                    ? AppColors.neutral100
                    : AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),

      // Content với theme-aware color và proper line height
      content: Text(
        content,
        style: AppTypography.textTheme.bodyMedium?.copyWith(
          height: 1.5,
          color: isDarkMode ? AppColors.neutral300 : AppColors.textSecondary,
        ),
      ),

      // Actions với improved spacing và styling
      actions: [
        // Cancel button với subtle styling
        TextButton(
          onPressed: onCancel ?? () => Navigator.of(context).pop(false),
          style: TextButton.styleFrom(
            foregroundColor: isDarkMode
                ? AppColors.neutral400
                : AppColors.textSecondary,
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingL,
              vertical: AppDimensions.paddingM,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
          ),
          child: Text(
            cancelText,
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        SizedBox(width: AppDimensions.spacingS),

        // Confirm button với prominent styling
        ElevatedButton(
          onPressed: onConfirm ?? () => Navigator.of(context).pop(true),
          style: ElevatedButton.styleFrom(
            backgroundColor: confirmButtonColor ?? defaultConfirmColor,
            foregroundColor: Colors.white,
            elevation: AppDimensions.cardElevation,
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingL,
              vertical: AppDimensions.paddingM,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
          ),
          child: Text(
            confirmText,
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ],

      // Action padding để improve spacing
      actionsPadding: EdgeInsets.only(
        left: AppDimensions.paddingL,
        right: AppDimensions.paddingL,
        bottom: AppDimensions.paddingL,
        top: AppDimensions.paddingS,
      ),
    );
  }
}

/// Utility function để show confirmation dialog và return result
Future<bool?> showConfirmationDialog(
  BuildContext context, {
  required String title,
  required String content,
  String confirmText = 'Xác nhận',
  String cancelText = 'Hủy',
  IconData? icon,
  Color? iconColor,
  Color? confirmButtonColor,
  bool isDestructive = false,
}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => ConfirmationDialog(
      title: title,
      content: content,
      confirmText: confirmText,
      cancelText: cancelText,
      icon: icon,
      iconColor: iconColor,
      confirmButtonColor: confirmButtonColor,
      isDestructive: isDestructive,
      onConfirm: () => Navigator.of(context).pop(true),
      onCancel: () => Navigator.of(context).pop(false),
    ),
  );
}
