import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../core/theme/index.dart';

class CustomSnackBar {
  static void show(
    BuildContext context, {
    required String message,
    required SnackBarType type,
    Duration? duration,
    VoidCallback? action,
    String? actionLabel,
  }) {
    // Tính toán margin bottom để tránh chồng lên navigation bar
    // Navigation bar có padding M + SafeArea + button height
    const navigationBarHeight = 80.0; // Ước tính chiều cao navigation bar
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getIconForType(type),
              color: Colors.white,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: _getColorForType(type),
        behavior: SnackBarBehavior.floating,
        duration: duration ?? const Duration(seconds: 4),
        margin: EdgeInsets.only(
          bottom: navigationBarHeight + AppDimensions.spacingM,
          left: AppDimensions.spacingM,
          right: AppDimensions.spacingM,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        action: action != null && actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: action,
              )
            : null,
      ),
    );
  }

  static IconData _getIconForType(SnackBarType type) {
    switch (type) {
      case SnackBarType.success:
        return TablerIcons.check;
      case SnackBarType.error:
        return TablerIcons.x;
      case SnackBarType.warning:
        return TablerIcons.alert_circle;
      case SnackBarType.info:
        return TablerIcons.info_circle;
    }
  }

  static Color _getColorForType(SnackBarType type) {
    switch (type) {
      case SnackBarType.success:
        return AppColors.success;
      case SnackBarType.error:
        return AppColors.error;
      case SnackBarType.warning:
        return AppColors.warning;
      case SnackBarType.info:
        return AppColors.info;
    }
  }
}

enum SnackBarType {
  success,
  error,
  warning,
  info,
} 