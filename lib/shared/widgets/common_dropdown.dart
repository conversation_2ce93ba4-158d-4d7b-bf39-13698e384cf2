import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

/// Generic dropdown widget that can work with any data type
/// Supports loading states, error states, and proper data management
class CommonDropdown<T> extends StatelessWidget {
  final String label;
  final String? value;
  final T? selectedModel;
  final List<T> items;
  final String Function(T) getItemId;
  final String Function(T) getItemDisplayText;
  final Function(T?) onChanged;
  final bool required;
  final String? hintText;
  final bool isLoading;
  final String? errorMessage;
  final Widget? prefixIcon;

  const CommonDropdown({
    super.key,
    required this.label,
    required this.value,
    required this.items,
    required this.getItemId,
    required this.getItemDisplayText,
    required this.onChanged,
    this.selectedModel,
    this.required = false,
    this.hintText,
    this.isLoading = false,
    this.errorMessage,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(context),
        SizedBox(height: AppDimensions.spacingXS),
        _buildDropdown(context),
        if (errorMessage != null) ...[
          SizedBox(height: AppDimensions.spacingXS),
          _buildErrorText(context),
        ],
      ],
    );
  }

  Widget _buildFieldLabel(BuildContext context) {
    return Row(
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        if (required) ...[
          SizedBox(width: AppDimensions.spacingXS),
          Text(
            '*',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDropdown(BuildContext context) {
    // Get current value and check if it exists in items
    final availableValues = items.map(getItemId).toSet().toList();
    final validValue = availableValues.contains(value) ? value : null;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: errorMessage != null 
              ? Theme.of(context).colorScheme.error
              : AppColors.borderLight,
        ),
      ),
      child: DropdownButtonFormField<String>(
        value: validValue,
        isExpanded: true,
        decoration: InputDecoration(
          hintText: _getHintText(),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingS,
          ),
          prefixIcon: prefixIcon,
        ),
        icon: isLoading 
            ? null 
            : Icon(
                Icons.keyboard_arrow_down,
                color: AppColors.textSecondary,
                size: 20,
              ),
        items: items.map((item) {
          return DropdownMenuItem(
            value: getItemId(item),
            child: Text(
              getItemDisplayText(item),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          );
        }).toList(),
        onChanged: _isDropdownEnabled() ? (selectedId) {
          final selectedItem = items.firstWhere(
            (item) => getItemId(item) == selectedId,
            orElse: () => items.first, // fallback, should not happen
          );
          onChanged(selectedId != null ? selectedItem : null);
        } : null,
      ),
    );
  }

  Widget _buildErrorText(BuildContext context) {
    return Text(
      errorMessage!,
      style: Theme.of(context).textTheme.bodySmall?.copyWith(
        color: Theme.of(context).colorScheme.error,
      ),
    );
  }

  String _getHintText() {
    if (isLoading) return 'Đang tải...';
    if (errorMessage != null) return 'Có lỗi xảy ra';
    if (items.isEmpty) return 'Không có dữ liệu';
    return hintText ?? 'Chọn ${label.toLowerCase()}';
  }

  bool _isDropdownEnabled() {
    return !isLoading && errorMessage == null && items.isNotEmpty;
  }
}
