import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import '../../core/theme/index.dart';
import '../../features/auth/services/qr_scanner_service.dart';
import 'camera_permission_helper.dart';
import '../utils/app_logger.dart';

/// QR Scanner Widget với UI được cải thiện
class QrScannerWidget extends StatefulWidget {
  /// Callback khi scan được QR code
  final Function(List<QrScanResult>) onQrDetected;
  
  /// Callback khi có lỗi
  final Function(String)? onError;
  
  /// Callback khi đóng scanner
  final VoidCallback? onClose;
  
  /// Hiển thị nút đóng
  final bool showCloseButton;
  
  /// Hiển thị nút flash
  final bool showFlashButton;
  
  /// Text hướng dẫn
  final String? instructionText;
  
  /// Chế độ scan (continuous hoặc single capture)
  final QrScanMode scanMode;

  const QrScannerWidget({
    super.key,
    required this.onQrDetected,
    this.onError,
    this.onClose,
    this.showCloseButton = true,
    this.showFlashButton = true,
    this.instructionText,
    this.scanMode = QrScanMode.continuous,
  });

  @override
  State<QrScannerWidget> createState() => _QrScannerWidgetState();
}

class _QrScannerWidgetState extends State<QrScannerWidget>
    with WidgetsBindingObserver {
  final QrScannerService _scannerService = QrScannerService();
  final AppLogger _logger = AppLogger();

  QrScannerState _state = QrScannerState.initializing;
  String? _errorMessage;
  bool _isFlashOn = false;
  Timer? _scanCooldownTimer;
  Timer? _autoCaptureTimer;
  bool _canScan = true;
  bool _isInitialized = false;
  bool _isRestarting =
      false; // Flag để tránh restart liên tục như common_qr_scanner
  bool _isCapturing = false; // Flag để tránh multiple capture calls

  @override
  void initState() {
    super.initState();
    _logger.i('QrScannerWidget: initState called');

    // Start listening to lifecycle changes.
    WidgetsBinding.instance.addObserver(this);

    // Initialize camera sau khi widget được mount
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeCamera();
      }
    });
  }

  @override
  void dispose() {
    _logger.i('QrScannerWidget: dispose called');

    // Stop listening to lifecycle changes.
    WidgetsBinding.instance.removeObserver(this);

    // Cancel timers
    _scanCooldownTimer?.cancel();
    _autoCaptureTimer?.cancel();

    // Stop scanning (fire and forget since dispose can't be async)
    _disposeScanner().catchError((error) {
      _logger.e('Error during dispose: $error');
    });

    // Dispose the widget itself.
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // If the controller is not ready, do not try to start or stop it.
    // Permission dialogs can trigger lifecycle changes before the controller is ready.
    final controller = _scannerService.cameraController;
    if (!_isInitialized ||
        controller == null ||
        !controller.value.isInitialized) {
      return;
    }

    debugPrint(
      'QrScannerWidget: App lifecycle changed - state: ${state.toString()}, isInitialized: $_isInitialized, controllerInitialized: ${controller.value.isInitialized}',
    );

    switch (state) {
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
        return;
      case AppLifecycleState.resumed:
        // Restart the scanner when the app is resumed.
        if (_isInitialized) {
          _resumeScanner();
        }
        break;
      case AppLifecycleState.inactive:
        // Stop the scanner when the app is paused.
        _pauseScanner();
        break;
    }
  }

  Future<void> _initializeCamera() async {
    if (_isInitialized || _isRestarting) return;
    _isRestarting = true;

    try {
      _logger.i('QrScannerWidget: Initializing camera...');

      if (mounted) {
        setState(() {
          _state = QrScannerState.initializing;
          _errorMessage = null;
          // Không set _isLoading = true để tránh hiển thị loading overlay
          // Camera sẽ initialize trong background
        });
      }

      // Kiểm tra camera permission
      final hasPermission = mounted
          ? await CameraPermissionHelper.ensureCameraPermission(context)
          : false;

      if (!hasPermission) {
        _logger.w('QrScannerWidget: Camera permission denied');
        if (mounted) {
          setState(() {
            _state = QrScannerState.error;
            _errorMessage = 'Không có quyền truy cập camera';
          });
        }
        widget.onError?.call('Không có quyền truy cập camera');
        return;
      }

      _logger.i(
        'QrScannerWidget: Camera permission granted, starting camera...',
      );
      await _startCamera();
    } catch (e) {
      _logger.e('QrScannerWidget: Error initializing camera', e);
      if (mounted) {
        setState(() {
          _state = QrScannerState.error;
          _errorMessage = 'Lỗi khởi tạo camera: ${e.toString()}';
        });
      }
      widget.onError?.call('Lỗi khởi tạo camera: ${e.toString()}');
    } finally {
      _isRestarting = false;
    }
  }

  /// Khởi động camera sau khi có permission
  Future<void> _startCamera() async {
    try {
      // Khởi tạo camera service
      final initResult = await _scannerService.initializeCamera();

      if (!initResult.isSuccess) {
        throw Exception(initResult.message);
      }

      _isInitialized = true;
      _logger.i('QrScannerWidget: Camera initialized successfully');

      // Camera đã được initialize, không cần set loading state

      // Bắt đầu scanning nếu ở chế độ continuous
      if (widget.scanMode == QrScanMode.continuous) {
        debugPrint('Starting continuous scanning mode');
        await _startContinuousScanning();
      } else {
        debugPrint('Starting capture mode');
        if (mounted) {
          setState(() {
            _state = QrScannerState.ready;
          });
        }
      }
    } catch (e) {
      _logger.e('QrScannerWidget: Error starting camera', e);
      if (mounted) {
        setState(() {
          _state = QrScannerState.error;
          _errorMessage = 'Lỗi khởi động camera: ${e.toString()}';
        });
      }
      widget.onError?.call('Lỗi khởi động camera: ${e.toString()}');
    }
  }

  /// Bắt đầu auto-capture scanning
  Future<void> _startContinuousScanning() async {
    try {
      setState(() {
        _state = QrScannerState.scanning;
      });

      debugPrint('Starting auto-capture scanning...');

      // Auto-capture loop thay vì continuous stream
      _autoCaptureTimer = Timer.periodic(const Duration(seconds: 1), (
        timer,
      ) async {
        if (!mounted ||
            _state != QrScannerState.scanning ||
            !_canScan ||
            _isCapturing) {
          debugPrint(
            'Stopping auto-capture timer - mounted: $mounted, state: $_state, canScan: $_canScan, isCapturing: $_isCapturing',
          );
          if (!mounted || _state != QrScannerState.scanning) {
            timer.cancel();
          }
          return;
        }

        try {
          _isCapturing = true;
          debugPrint('Auto-capturing for QR scan...');
          final results = await _scannerService.captureAndScan();

          if (results.isNotEmpty) {
            debugPrint('Auto-capture found ${results.length} QR codes');
            timer.cancel();
            _handleQrDetected(results);
          } else {
            debugPrint('Auto-capture: No QR codes found');
          }
        } catch (e) {
          debugPrint('Auto-capture error: $e');
          // Nếu lỗi capture, có thể dừng auto-capture để tránh spam errors
          if (e.toString().contains('Previous capture has not returned yet')) {
            debugPrint(
              'Detected concurrent capture issue, slowing down auto-capture',
            );
            timer.cancel();
            // Restart với interval lâu hơn
            _startSlowAutoCaptureScanning();
          }
        } finally {
          _isCapturing = false;
        }
      });
    } catch (e) {
      _logger.e('Error starting auto-capture scanning', e);
      // Fallback: Chuyển sang manual capture mode
      setState(() {
        _state = QrScannerState.ready;
      });

      // Hiển thị thông báo cho user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Auto-scanning không khả dụng. Sử dụng nút chụp để quét QR.',
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  /// Bắt đầu auto-capture scanning với interval chậm hơn
  Future<void> _startSlowAutoCaptureScanning() async {
    try {
      debugPrint('Starting slow auto-capture scanning...');

      // Auto-capture với interval 3 giây thay vì 1 giây
      _autoCaptureTimer = Timer.periodic(const Duration(seconds: 3), (
        timer,
      ) async {
        if (!mounted ||
            _state != QrScannerState.scanning ||
            !_canScan ||
            _isCapturing) {
          debugPrint(
            'Stopping slow auto-capture timer - mounted: $mounted, state: $_state, canScan: $_canScan, isCapturing: $_isCapturing',
          );
          if (!mounted || _state != QrScannerState.scanning) {
            timer.cancel();
          }
          return;
        }

        try {
          _isCapturing = true;
          debugPrint('Slow auto-capturing for QR scan...');
          final results = await _scannerService.captureAndScan();

          if (results.isNotEmpty) {
            debugPrint('Slow auto-capture found ${results.length} QR codes');
            timer.cancel();
            _handleQrDetected(results);
          } else {
            debugPrint('Slow auto-capture: No QR codes found');
          }
        } catch (e) {
          debugPrint('Slow auto-capture error: $e');
          // Nếu vẫn lỗi, chuyển sang manual mode
          if (e.toString().contains('Previous capture has not returned yet')) {
            debugPrint('Still having capture issues, switching to manual mode');
            timer.cancel();
            if (mounted) {
              setState(() {
                _state = QrScannerState.ready;
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Auto-scanning gặp vấn đề. Vui lòng sử dụng nút chụp để quét QR.',
                  ),
                  backgroundColor: Colors.orange,
                ),
              );
            }
          }
        } finally {
          _isCapturing = false;
        }
      });
    } catch (e) {
      _logger.e('Error starting slow auto-capture scanning', e);
      if (mounted) {
        setState(() {
          _state = QrScannerState.ready;
        });
      }
    }
  }

  /// Xử lý khi phát hiện QR code
  void _handleQrDetected(List<QrScanResult> results) {
    if (!_canScan || results.isEmpty) return;

    debugPrint('QR detected: ${results.length} results');
    for (final result in results) {
      debugPrint('QR value: ${result.value}');
      debugPrint('QR format: ${result.format}');
    }

    // Stop auto-capture timer
    _autoCaptureTimer?.cancel();
    _autoCaptureTimer = null;

    // Cooldown để tránh scan liên tục
    _canScan = false;
    _scanCooldownTimer?.cancel();
    _scanCooldownTimer = Timer(const Duration(seconds: 2), () {
      _canScan = true;
    });

    widget.onQrDetected(results);
  }

  /// Chọn ảnh từ thư viện và scan QR
  Future<void> _pickImageFromGallery() async {
    if (!_canScan) return;

    try {
      setState(() {
        _state = QrScannerState.capturing;
      });

      // Sử dụng image_picker để chọn ảnh từ thư viện
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image == null) {
        setState(() {
          _state = QrScannerState.ready;
        });
        return;
      }

      // Scan QR từ ảnh đã chọn
      final results = await _scannerService.scanQrFromImage(image.path);

      setState(() {
        _state = QrScannerState.ready;
      });

      if (results.isNotEmpty) {
        // Delay một chút để đảm bảo setState hoàn tất trước khi gọi callback
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _handleQrDetected(results);
          }
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Không tìm thấy mã QR trong ảnh'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      _logger.e('Error picking image from gallery', e);
      setState(() {
        _state = QrScannerState.ready;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Có lỗi khi chọn ảnh từ thư viện'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Toggle flash
  Future<void> _toggleFlash() async {
    try {
      final controller = _scannerService.cameraController;
      if (controller != null && controller.value.isInitialized) {
        await controller.setFlashMode(
          _isFlashOn ? FlashMode.off : FlashMode.torch,
        );
        setState(() {
          _isFlashOn = !_isFlashOn;
        });
      }
    } catch (e) {
      _logger.e('Error toggling flash', e);
    }
  }

  /// Pause scanner
  Future<void> _pauseScanner() async {
    try {
      await _scannerService.pauseCamera();
      _autoCaptureTimer?.cancel();
      _isCapturing = false; // Reset capturing flag
      _logger.d('Scanner paused');
    } catch (e) {
      _logger.e('Error pausing scanner', e);
    }
  }

  /// Resume scanner
  Future<void> _resumeScanner() async {
    try {
      await _scannerService.resumeCamera();
      _isCapturing = false; // Reset capturing flag

      // Restart auto-capture scanning if needed
      if (widget.scanMode == QrScanMode.continuous &&
          _state == QrScannerState.scanning &&
          _autoCaptureTimer == null) {
        debugPrint('Restarting auto-capture scanning on resume');
        await _startContinuousScanning();
      }

      _logger.d('Scanner resumed');
    } catch (e) {
      _logger.e('Error resuming scanner', e);
    }
  }

  /// Stop camera manually - can be called by parent widget
  Future<void> stopCamera() async {
    if (!mounted) return;

    try {
      _logger.i('QrScannerWidget: Stopping camera manually');
      await _scannerService.stopContinuousScanning();
      _autoCaptureTimer?.cancel();
      _autoCaptureTimer = null;
      _isCapturing = false; // Reset capturing flag
    } catch (e) {
      _logger.e('QrScannerWidget: Error stopping camera', e);
    }
  }

  /// Dispose scanner
  Future<void> _disposeScanner() async {
    try {
      _logger.i('QrScannerWidget: Starting dispose scanner');
      
      // Stop timers first
      _autoCaptureTimer?.cancel();
      _autoCaptureTimer = null;
      _scanCooldownTimer?.cancel();
      _scanCooldownTimer = null;

      // Reset flags
      _isCapturing = false;
      _canScan = false; // Set to false to prevent new operations
      
      // Stop scanning first
      try {
        await _scannerService.stopContinuousScanning();
      } catch (e) {
        _logger.w('Error stopping continuous scanning: $e');
      }

      // Dispose camera resources to prevent ImageReader buffer leak
      // Only dispose if this widget was the one using the camera
      if (_isInitialized) {
        try {
          await _scannerService.disposeCamera();
          _isInitialized = false; // Mark as no longer initialized
          _logger.i('QrScannerWidget: Camera disposed to prevent buffer leak');
        } catch (e) {
          _logger.w('Error disposing camera: $e');
        }
      }

      _logger.i('QrScannerWidget: Scanner disposed successfully');
    } catch (e) {
      _logger.e('QrScannerWidget: Error disposing scanner', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        // Dispose camera when pop is invoked to prevent buffer leak
        // Only dispose if not already disposed
        if (didPop && _isInitialized) {
          await _disposeScanner();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Stack(
            children: [
              // Camera preview
              _buildCameraPreview(),

              // Top controls
              _buildTopControls(),

              // Bottom controls
              _buildBottomControls(),

              // Loading/Error states
              _buildStateOverlay(),
            ],
          ),
        ),
      ),
    );
  }

  /// Build camera preview với khung quét QR
  Widget _buildCameraPreview() {
    final controller = _scannerService.cameraController;

    // Kiểm tra mounted state và controller trước khi render CameraPreview
    if (!mounted || controller == null || !controller.value.isInitialized) {
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }

    // Kiểm tra thêm xem controller có bị dispose không
    try {
      return Stack(
        children: [
          // Camera preview
          Positioned.fill(child: CameraPreview(controller)),

          // Overlay với khung quét
          _buildScanningOverlay(),
        ],
      );
    } catch (e) {
      _logger.e('Error building camera preview: $e');
      return const Center(
        child: CircularProgressIndicator(color: Colors.white),
      );
    }
  }

  /// Build scanning overlay với khung quét QR cải thiện
  Widget _buildScanningOverlay() {
    return Stack(
      children: [
        // Tạo mask overlay với hole trong suốt
        CustomPaint(painter: ScannerOverlayPainter(), child: Container()),

        // Các góc khung quét
        Center(
          child: SizedBox(
            width: 280,
            height: 280,
            child: _buildCornerIndicators(),
          ),
        ),

        // Instruction text ở phía trên khung quét
        if (widget.instructionText != null)
          Positioned(
            top: MediaQuery.of(context).size.height * 0.15,
            left: AppDimensions.paddingL,
            right: AppDimensions.paddingL,
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Text(
                widget.instructionText!,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Build corner indicators cho khung quét
  Widget _buildCornerIndicators() {
    const cornerSize = 24.0;
    const cornerThickness = 4.0;

    return Stack(
      children: [
        // Top-left
        Positioned(
          top: -cornerThickness / 2,
          left: -cornerThickness / 2,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
                left: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
              ),
            ),
          ),
        ),
        // Top-right
        Positioned(
          top: -cornerThickness / 2,
          right: -cornerThickness / 2,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
                right: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
              ),
            ),
          ),
        ),
        // Bottom-left
        Positioned(
          bottom: -cornerThickness / 2,
          left: -cornerThickness / 2,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
                left: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
              ),
            ),
          ),
        ),
        // Bottom-right
        Positioned(
          bottom: -cornerThickness / 2,
          right: -cornerThickness / 2,
          child: Container(
            width: cornerSize,
            height: cornerSize,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
                right: BorderSide(
                  color: AppColors.kienlongOrange,
                  width: cornerThickness,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build top controls
  Widget _buildTopControls() {
    return Positioned(
      top: AppDimensions.paddingM,
      left: AppDimensions.paddingM,
      right: AppDimensions.paddingM,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Close button
          if (widget.showCloseButton)
            _buildControlButton(
              icon: TablerIcons.x,
              onPressed: () async {
                // Dispose camera before closing to prevent buffer leak
                // Only dispose if not already disposed
                if (_isInitialized) {
                  await _disposeScanner();
                }
                if (mounted) {
                  widget.onClose?.call();
                  Navigator.of(context).pop();
                }
              },
            ),

          const Spacer(),

          // Flash button
          if (widget.showFlashButton)
            _buildControlButton(
              icon: _isFlashOn ? TablerIcons.bulb : TablerIcons.bulb_off,
              onPressed: _toggleFlash,
              isActive: _isFlashOn,
            ),
        ],
      ),
    );
  }

  /// Build bottom controls
  Widget _buildBottomControls() {
    return Positioned(
      bottom: AppDimensions.paddingXL,
      left: AppDimensions.paddingL,
      right: AppDimensions.paddingL,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Gallery button
          _buildGalleryButton(),
        ],
      ),
    );
  }

  /// Build control button
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    bool isActive = false,
  }) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: isActive
            ? AppColors.kienlongOrange
            : Colors.black.withValues(alpha: 0.6),
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(24),
          child: Icon(icon, color: Colors.white, size: 24),
        ),
      ),
    );
  }

  /// Build gallery button với design đẹp hơn
  Widget _buildGalleryButton() {
    final canPick =
        _state == QrScannerState.ready || _state == QrScannerState.scanning;
    final isProcessing = _state == QrScannerState.capturing;

    return GestureDetector(
      onTap: canPick ? _pickImageFromGallery : null,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingL,
          vertical: AppDimensions.paddingM,
        ),
        decoration: BoxDecoration(
          gradient: canPick
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.kienlongOrange,
                    AppColors.kienlongOrange.withValues(alpha: 0.8),
                  ],
                )
              : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.grey.shade600, Colors.grey.shade700],
                ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: isProcessing
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Icon(
                        TablerIcons.photo,
                        color: Colors.white,
                        size: 24,
                      ),
              ),
            ),
            SizedBox(width: AppDimensions.spacingM),
            Text(
              isProcessing ? 'Đang xử lý...' : 'Chọn từ thư viện',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                letterSpacing: 0.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build state overlay cho loading và error
  Widget _buildStateOverlay() {
    // Không hiển thị loading overlay trong quá trình initialization để tránh conflict
    // Camera sẽ được initialize nhanh chóng trong background

    if (_state == QrScannerState.error) {
      return Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingXL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 80,
                  height: 80,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    TablerIcons.camera_off,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingL),
                const Text(
                  'Không thể khởi tạo camera',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: AppDimensions.spacingM),
                Text(
                  _errorMessage ?? 'Có lỗi xảy ra',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: AppDimensions.spacingXL),
                ElevatedButton(
                  onPressed: _initializeCamera,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.kienlongOrange,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingXL,
                      vertical: AppDimensions.paddingM,
                    ),
                  ),
                  child: const Text('Thử lại'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }
}

/// Enum cho trạng thái QR scanner
enum QrScannerState { initializing, ready, scanning, capturing, error }

/// Enum cho chế độ scan
enum QrScanMode {
  continuous, // Scan liên tục từ camera stream
  capture, // Chụp ảnh rồi scan
}

/// Custom painter để tạo overlay với hole trong suốt
class ScannerOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = Colors.black.withValues(alpha: 0.5)
      ..style = PaintingStyle.fill;

    // Tạo hole trong suốt ở giữa
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double holeSize = 280;
    final double radius = 20; // Border radius

    final Rect holeRect = Rect.fromCenter(
      center: Offset(centerX, centerY),
      width: holeSize,
      height: holeSize,
    );

    final RRect holeRRect = RRect.fromRectAndRadius(
      holeRect,
      Radius.circular(radius),
    );

    // Vẽ overlay với hole bằng Path
    final Path path = Path()
      ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
      ..addRRect(holeRRect)
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
