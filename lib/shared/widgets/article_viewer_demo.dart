import 'package:flutter/material.dart';
import 'package:kiloba_biz/shared/widgets/article_viewer_screen.dart';
import 'package:kiloba_biz/shared/constants/article_codes.dart';
import 'package:kiloba_biz/core/theme/index.dart';

/// Demo screen để minh họa cách sử dụng ArticleViewerScreen
class ArticleViewerDemo extends StatelessWidget {
  const ArticleViewerDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Article Viewer Demo'),
        backgroundColor: AppColors.kienlongOrange,
        foregroundColor: AppColors.textOnPrimary,
      ),
      body: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Demo Article Viewer',
              style: AppTypography.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'Nhấn vào các nút bên dưới để xem nội dung bài viết:',
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingXL),
            
            // Terms of Service Button
            ElevatedButton.icon(
              onPressed: () => _openArticleViewer(
                context,
                'Điều khoản sử dụng',
                ArticleCodes.termsOfServiceRegisterV1,
              ),
              icon: const Icon(Icons.description),
              label: const Text('Xem Điều khoản sử dụng'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: AppColors.textOnPrimary,
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
            
            SizedBox(height: AppDimensions.spacingM),
            
            // Privacy Policy Button
            ElevatedButton.icon(
              onPressed: () => _openArticleViewer(
                context,
                'Chính sách bảo mật',
                ArticleCodes.privacyPolicyRegisterV1,
              ),
              icon: const Icon(Icons.security),
              label: const Text('Xem Chính sách bảo mật'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongSkyBlue,
                foregroundColor: AppColors.textOnPrimary,
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
            
            SizedBox(height: AppDimensions.spacingXL),
            
            // Info Card
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingL),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppColors.borderDark.withValues(alpha: 0.3)
                      : AppColors.borderLight,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: AppColors.info,
                        size: 24,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Text(
                        'Thông tin',
                        style: AppTypography.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Text(
                    'ArticleViewerScreen hỗ trợ:',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  _buildFeatureItem('✅ Dark/Light theme tự động'),
                  _buildFeatureItem('✅ Loading state và error handling'),
                  _buildFeatureItem('✅ Code syntax highlighting'),
                  _buildFeatureItem('✅ Custom link handling'),
                  _buildFeatureItem('✅ Responsive design'),
                  _buildFeatureItem('✅ Tuân thủ theme system'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Builder(
      builder: (context) => Padding(
        padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
        child: Text(
          text,
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
      ),
    );
  }

  void _openArticleViewer(BuildContext context, String title, String articleCode) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ArticleViewerScreen(
          articleCode: articleCode,
          title: title,
        ),
      ),
    );
  }
} 