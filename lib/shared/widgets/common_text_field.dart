import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/index.dart';
import '../utils/currency_utils.dart';

/// Common text field widget with various input types and formatting options
class CommonTextField extends StatefulWidget {
  final String label;
  final String? value;
  final String? hint;
  final bool required;
  final TextInputType? keyboardType;
  final int? maxLines;
  final int? maxLength;
  final bool readOnly;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? suffixText;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final List<TextInputFormatter>? inputFormatters;
  final bool isCurrency;
  final bool enableCurrencyFormatting;
  final int? currencyMaxValue;
  final TextEditingController? controller;

  const CommonTextField({
    super.key,
    required this.label,
    this.value,
    this.hint,
    this.required = false,
    this.keyboardType,
    this.maxLines = 1,
    this.maxLength,
    this.readOnly = false,
    this.prefixIcon,
    this.suffixIcon,
    this.suffixText,
    this.validator,
    this.onChanged,
    this.inputFormatters,
    this.isCurrency = false,
    this.enableCurrencyFormatting = false,
    this.currencyMaxValue,
    this.controller,
  });

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  late TextEditingController _controller;
  bool _isInternalController = false;

  @override
  void initState() {
    super.initState();
    if (widget.controller != null) {
      _controller = widget.controller!;
    } else {
      _controller = TextEditingController(text: widget.value ?? '');
      _isInternalController = true;
    }
  }

  @override
  void dispose() {
    if (_isInternalController) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(CommonTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only update controller if the value actually changed and is different from current text
    if (widget.value != oldWidget.value && 
        widget.value != _controller.text && 
        widget.value != null) {
      // Use post frame callback to avoid setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _controller.text = widget.value ?? '';
        }
      });
    }
  }

  void _onTextChanged(String value) {
    // CurrencyInputFormatter handles formatting automatically
    // Just pass the clean value to onChanged
    if (widget.isCurrency) {
      final cleanValue = CurrencyUtils.getCleanValue(value);
      widget.onChanged?.call(cleanValue);
    } else {
      widget.onChanged?.call(value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(),
        SizedBox(height: AppDimensions.spacingXS),
        _buildTextField(),
        if (widget.validator != null) ...[
          SizedBox(height: AppDimensions.spacingXS),
          _buildValidationError(),
        ],
      ],
    );
  }

  Widget _buildFieldLabel() {
    return Row(
      children: [
        Text(
          widget.label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        if (widget.required) ...[
          SizedBox(width: AppDimensions.spacingXS),
          Text(
            '*',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTextField() {
    return TextFormField(
      controller: _controller,
      keyboardType: widget.isCurrency ? TextInputType.number : widget.keyboardType,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength,
      readOnly: widget.readOnly,
      inputFormatters: widget.isCurrency 
          ? CurrencyUtils.getCurrencyInputFormatters(maxValue: widget.currencyMaxValue ?? 1000000000)
          : widget.inputFormatters,
      decoration: InputDecoration(
        hintText: widget.hint ?? 'Nhập ${widget.label.toLowerCase()}',
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(AppDimensions.paddingM),
        prefixIcon: widget.prefixIcon,
        suffixIcon: widget.suffixIcon,
        suffixText: widget.suffixText,
        filled: widget.readOnly,
        fillColor: widget.readOnly ? AppColors.backgroundSecondary : null,
      ),
      onChanged: _onTextChanged,
      validator: widget.validator,
    );
  }

  Widget _buildValidationError() {
    return FutureBuilder<String?>(
      future: Future.value(widget.validator?.call(_controller.text)),
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data != null) {
          return Text(
            snapshot.data!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}

/// Specialized currency text field
class CurrencyTextField extends StatelessWidget {
  final String label;
  final String? value;
  final String? hint;
  final bool required;
  final bool readOnly;
  final Widget? prefixIcon;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final TextEditingController? controller;
  final int? maxValue;

  const CurrencyTextField({
    super.key,
    required this.label,
    this.value,
    this.hint,
    this.required = false,
    this.readOnly = false,
    this.prefixIcon,
    this.validator,
    this.onChanged,
    this.controller,
    this.maxValue,
  });

  @override
  Widget build(BuildContext context) {
    return CommonTextField(
      label: label,
      value: value,
      hint: hint,
      required: required,
      readOnly: readOnly,
      prefixIcon: prefixIcon,
      validator: validator,
      onChanged: onChanged,
      controller: controller,
      isCurrency: true,
      currencyMaxValue: maxValue,
      keyboardType: TextInputType.number,
      suffixText: 'VND',
    );
  }
}
