import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';

import '../../core/theme/app_colors.dart';
import '../../core/theme/app_dimensions.dart';

/// Common PDF Viewer Screen for displaying PDF files
/// This widget can be used across the app for viewing PDF documents
class PdfViewerScreen extends StatefulWidget {
  final File file;
  final String fileName;
  final String? fileSize;
  final DateTime? uploadedAt;

  const PdfViewerScreen({
    super.key,
    required this.file,
    required this.fileName,
    this.fileSize,
    this.uploadedAt,
  });

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  late PDFViewController _pdfViewController;
  int _currentPage = 1;
  int _totalPages = 0;
  bool _isReady = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        elevation: 1,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.fileName,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_totalPages > 0)
              Text(
                'Trang $_currentPage/$_totalPages',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
          ],
        ),
        leading: IconButton(
          icon: Icon(TablerIcons.arrow_left, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (_isReady && _totalPages > 0) ...[
            IconButton(
              icon: Icon(TablerIcons.minus, color: AppColors.textPrimary),
              onPressed: () => _pdfViewController.setPage(_currentPage - 1),
            ),
            IconButton(
              icon: Icon(TablerIcons.plus, color: AppColors.textPrimary),
              onPressed: () => _pdfViewController.setPage(_currentPage + 1),
            ),
          ],
          IconButton(
            icon: Icon(TablerIcons.info_circle, color: AppColors.textPrimary),
            onPressed: () => _showPdfInfo(context),
          ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _totalPages > 1 ? _buildBottomNavigation() : null,
    );
  }

  Widget _buildBody() {
    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.file_x,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Không thể mở file PDF',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        PDFView(
          filePath: widget.file.path,
          enableSwipe: true,
          swipeHorizontal: false,
          autoSpacing: false,
          pageFling: true,
          pageSnap: true,
          defaultPage: 0,
          fitPolicy: FitPolicy.WIDTH,
          preventLinkNavigation: false,
          onRender: (pages) {
            setState(() {
              _totalPages = pages ?? 0;
              _isReady = true;
            });
          },
          onError: (error) {
            setState(() {
              _errorMessage = error.toString();
            });
          },
          onPageError: (page, error) {
            setState(() {
              _errorMessage = 'Lỗi tải trang $page: $error';
            });
          },
          onViewCreated: (PDFViewController pdfViewController) {
            _pdfViewController = pdfViewController;
          },
          onLinkHandler: (String? uri) {
            // Handle link clicks if needed
          },
          onPageChanged: (int? page, int? total) {
            setState(() {
              _currentPage = (page ?? 0) + 1;
            });
          },
        ),
        if (!_isReady)
          Container(
            color: Colors.white,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.kienlongOrange,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Text(
                    'Đang tải PDF...',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            onPressed: _currentPage > 1
                ? () => _pdfViewController.setPage(_currentPage - 2)
                : null,
            icon: Icon(
              TablerIcons.chevron_left,
              color: _currentPage > 1
                  ? AppColors.kienlongOrange
                  : AppColors.neutral400,
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingM,
                vertical: AppDimensions.paddingS,
              ),
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Text(
                'Trang $_currentPage / $_totalPages',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          IconButton(
            onPressed: _currentPage < _totalPages
                ? () => _pdfViewController.setPage(_currentPage)
                : null,
            icon: Icon(
              TablerIcons.chevron_right,
              color: _currentPage < _totalPages
                  ? AppColors.kienlongOrange
                  : AppColors.neutral400,
            ),
          ),
        ],
      ),
    );
  }

  void _showPdfInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              TablerIcons.file_type_pdf,
              color: AppColors.error,
              size: 24,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Text('Thông tin PDF'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Tên file:', widget.fileName),
            if (widget.fileSize != null)
              _buildInfoRow('Kích thước:', widget.fileSize!),
            _buildInfoRow(
              'Số trang:',
              _totalPages > 0 ? '$_totalPages trang' : 'Đang tải...',
            ),
            if (widget.uploadedAt != null)
              _buildInfoRow('Thời gian tải:', _formatDateTime(widget.uploadedAt!)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Đóng',
              style: TextStyle(color: AppColors.kienlongOrange),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.spacingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
