import 'package:flutter/material.dart';

/// Utilities cho color operations
class ColorUtils {
  /// Convert hex color string sang Flutter Color
  /// Supports formats: #RRGGBB, #AARRGGBB, RRGGBB, AARRGGBB
  static Color hexToColor(String hexString) {
    try {
      // Remove # if present
      String hex = hexString.replaceAll('#', '');
      
      // Handle different hex formats
      if (hex.length == 6) {
        // RRGGBB format - add full opacity
        hex = 'FF$hex';
      } else if (hex.length == 8) {
        // AARRGGBB format - already correct
      } else {
        // Invalid format - return default color
        return Colors.grey;
      }
      
      // Parse hex to int
      final intValue = int.parse(hex, radix: 16);
      return Color(intValue);
    } catch (e) {
      // Return default color on error
      return Colors.grey;
    }
  }

  /// Convert Flutter Color sang hex string
  /// Returns format: #AARRGGBB
  static String colorToHex(Color color) {
    return '#${color.toARGB32().toRadixString(16).padLeft(8, '0').toUpperCase()}';
  }

  /// Get hex string without alpha channel
  /// Returns format: #RRGGBB
  static String colorToHexWithoutAlpha(Color color) {
    return '#${(color.toARGB32() & 0xFFFFFF).toRadixString(16).padLeft(6, '0').toUpperCase()}';
  }

  /// Check if color is light or dark
  static bool isLightColor(Color color) {
    // Calculate luminance
    final luminance = color.computeLuminance();
    return luminance > 0.5;
  }

  /// Get contrasting text color (black or white) for a background color
  static Color getContrastingTextColor(Color backgroundColor) {
    return isLightColor(backgroundColor) ? Colors.black : Colors.white;
  }

  /// Create a lighter version of a color
  static Color lighten(Color color, double amount) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final lightened = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return lightened.toColor();
  }

  /// Create a darker version of a color
  static Color darken(Color color, double amount) {
    assert(amount >= 0 && amount <= 1);
    
    final hsl = HSLColor.fromColor(color);
    final darkened = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return darkened.toColor();
  }

  /// Create a color with opacity
  static Color withOpacity(Color color, double opacity) {
    assert(opacity >= 0 && opacity <= 1);
    return color.withValues(alpha: opacity);
  }

  /// Blend two colors
  static Color blend(Color color1, Color color2, double ratio) {
    assert(ratio >= 0 && ratio <= 1);
    
    return Color.lerp(color1, color2, ratio)!;
  }

  /// Generate a color from string (for consistent colors based on text)
  static Color generateColorFromString(String text) {
    int hash = 0;
    for (int i = 0; i < text.length; i++) {
      hash = text.codeUnitAt(i) + ((hash << 5) - hash);
    }
    
    // Generate color from hash
    final hue = (hash % 360).toDouble();
    final saturation = 0.6;
    final lightness = 0.5;
    
    return HSLColor.fromAHSL(1.0, hue, saturation, lightness).toColor();
  }
}
