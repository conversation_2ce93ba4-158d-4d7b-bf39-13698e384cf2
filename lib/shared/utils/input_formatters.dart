import 'package:flutter/services.dart';

/// Custom input formatter to remove leading zeros and cap at specified maximum value
class LeadingZeroRemoverFormatter extends TextInputFormatter {
  final int maxValue;
  
  const LeadingZeroRemoverFormatter({this.maxValue = 1000000000});
  
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Only process if the text has changed
    if (oldValue.text == newValue.text) {
      return newValue;
    }
    
    String text = newValue.text;
    
    // Remove leading zeros but keep at least one zero if the text is just zeros
    if (text.isNotEmpty) {
      // Remove all leading zeros
      String cleaned = text.replaceFirst(RegExp(r'^0+'), '');
      
      // If the result is empty (all zeros), keep one zero
      if (cleaned.isEmpty && text.isNotEmpty) {
        cleaned = '0';
      }
      
      // Check if value exceeds maximum
      final numericValue = int.tryParse(cleaned) ?? 0;
      if (numericValue > maxValue) {
        // Return old value if new value exceeds maximum
        return oldValue;
      }
      
      // If the result is different, update the text
      if (cleaned != text) {
        return TextEditingValue(
          text: cleaned,
          selection: TextSelection.collapsed(offset: cleaned.length),
        );
      }
    }
    
    return newValue;
  }
}

/// Custom input formatter for phone numbers with Vietnamese format
class PhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Only process if the text has changed
    if (oldValue.text == newValue.text) {
      return newValue;
    }
    
    String text = newValue.text;
    
    // Remove all non-digit characters
    String digitsOnly = text.replaceAll(RegExp(r'[^\d]'), '');
    
    // Auto-add leading 0 if user starts typing without 0
    // Only do this if the input is not empty and doesn't start with 0
    if (digitsOnly.isNotEmpty && !digitsOnly.startsWith('0')) {
      // If user types 9 digits without 0, add 0 at the beginning
      if (digitsOnly.length == 9) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 8 digits without 0, add 0 at the beginning
      else if (digitsOnly.length == 8) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 7 digits without 0, add 0 at the beginning
      else if (digitsOnly.length == 7) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 6 digits without 0, add 0 at the beginning
      else if (digitsOnly.length == 6) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 5 digits without 0, add 0 at the beginning
      else if (digitsOnly.length == 5) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 4 digits without 0, add 0 at the beginning
      else if (digitsOnly.length == 4) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 3 digits without 0, add 0 at the beginning
      else if (digitsOnly.length == 3) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 2 digits without 0, add 0 at the beginning
      else if (digitsOnly.length == 2) {
        digitsOnly = '0$digitsOnly';
      }
      // If user types 1 digit without 0, add 0 at the beginning
      else if (digitsOnly.length == 1) {
        digitsOnly = '0$digitsOnly';
      }
    }
    
    // Limit to 10 digits for Vietnamese phone numbers
    if (digitsOnly.length > 10) {
      digitsOnly = digitsOnly.substring(0, 10);
    }
    
    // Format as Vietnamese phone number: 0XXX XXX XXXX
    String formatted = '';
    if (digitsOnly.isNotEmpty) {
      if (digitsOnly.length <= 4) {
        formatted = digitsOnly;
      } else if (digitsOnly.length <= 7) {
        formatted = '${digitsOnly.substring(0, 4)} ${digitsOnly.substring(4)}';
      } else {
        formatted = '${digitsOnly.substring(0, 4)} ${digitsOnly.substring(4, 7)} ${digitsOnly.substring(7)}';
      }
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

/// Input formatter for currency amounts with thousand separators
class CurrencyInputFormatter extends TextInputFormatter {
  final int maxValue;
  
  const CurrencyInputFormatter({this.maxValue = 1000000000});
  
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Only process if the text has changed
    if (oldValue.text == newValue.text) {
      return newValue;
    }
    
    String text = newValue.text;
    
    // Remove all non-digit characters
    String digitsOnly = text.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if value exceeds maximum
    final numericValue = int.tryParse(digitsOnly) ?? 0;
    if (numericValue > maxValue) {
      // Return old value if new value exceeds maximum
      return oldValue;
    }
    
    // Remove leading zeros
    String cleaned = digitsOnly.replaceFirst(RegExp(r'^0+'), '');
    if (cleaned.isEmpty && digitsOnly.isNotEmpty) {
      cleaned = '0';
    }
    
    // Format with thousand separators
    String formatted = _formatWithThousandSeparators(cleaned);
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
  
  String _formatWithThousandSeparators(String value) {
    if (value.isEmpty) return '';
    
    // Add thousand separators (dots for Vietnamese format)
    return value.replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (match) => '${match[1]}.',
    );
  }
}

