import 'package:flutter/services.dart';
import 'input_formatters.dart';

/// Utility class for currency formatting and input handling
class CurrencyUtils {
  // Common currency max values based on SRS
  static const int maxLoanAmount = 500000000; // 500 million VND (SRS: tối đa 500 triệu VNĐ)
  static const int maxCollateralValue = 2000000000; // 2 billion VND
  static const int maxDailyIncome = 10000000; // 10 million VND per day
  static const int maxDailyRevenue = 50000000; // 50 million VND per day
  static const int maxOwnCapital = 500000000; // 500 million VND
  static const int maxSmallAmount = 1000000; // 1 million VND
  static const int minLoanAmount = 1000000; // 1 million VND (SRS: tối thiểu 1 triệu VNĐ)
  /// Format a number string to currency format (e.g., "1000000" -> "1.000.000")
  static String formatCurrency(String value) {
    if (value.isEmpty) return '';
    
    // Remove all non-digits
    String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    // Handle large numbers safely without parsing to int
    // This avoids int overflow issues with very large numbers
    String formatted = cleanValue.replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]}.',
    );
    
    return formatted;
  }

  /// Get clean numeric value from formatted currency string
  static String getCleanValue(String formattedValue) {
    return formattedValue.replaceAll(RegExp(r'[^\d]'), '');
  }

  /// Create input formatters for currency input
  static List<TextInputFormatter> getCurrencyInputFormatters({int maxValue = 1000000000}) {
    return [
      CurrencyInputFormatter(maxValue: maxValue),
    ];
  }

  /// Format currency with VND suffix
  static String formatCurrencyWithSuffix(String value) {
    final formatted = formatCurrency(value);
    return formatted.isEmpty ? '' : '$formatted VND';
  }

  /// Parse currency string to int (safely)
  static int? parseCurrencyToInt(String formattedValue) {
    final cleanValue = getCleanValue(formattedValue);
    if (cleanValue.isEmpty) return null;
    
    try {
      return int.parse(cleanValue);
    } catch (e) {
      return null;
    }
  }

  /// Format int to currency string
  static String formatIntToCurrency(int? value) {
    if (value == null) return '';
    return formatCurrency(value.toString());
  }
}
