import 'package:logger/logger.dart';
import '../../features/account/models/log_entry.dart';

/// Interface cho AppLogger để support dependency injection và testing
abstract class IAppLogger {
  /// Log info message
  Future<void> i(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]);

  /// Log debug message
  Future<void> d(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]);

  /// Log warning message
  Future<void> w(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]);

  /// Log error message
  Future<void> e(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]);

  /// Log verbose message
  Future<void> v(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]);

  /// Log with custom tag
  Future<void> logWithTag(
    String tag,
    String message, [
    LogLevel level = LogLevel.info,
  ]);

  /// Log with feature context
  Future<void> logWithFeature(
    String feature,
    String message, [
    LogLevel level = LogLevel.info,
  ]);

  /// Initialize the logger
  Future<void> initialize();

  /// Get logger instance
  Logger get logger;
}

 