import 'dart:io';
import 'dart:isolate';
import 'dart:ui' as ui;

import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;

/// Utility class for cropping images based on overlay coordinates
class ImageCropUtils {
  /// Crop image to overlay area with proper rotation handling
  ///
  /// [imagePath] - Path to the original image file
  /// [overlayRect] - Rectangle coordinates of the overlay in screen pixels
  /// [screenSize] - Screen size in pixels
  /// [cameraRotation] - Camera rotation in degrees (0, 90, 180, 270)
  /// [bodyArea] - Optional body area rectangle (excluding AppBar) for accurate crop calculation
  ///
  /// Returns a map containing paths to both cropped images:
  /// - 'resized': cropped and resized image (max width 500px)
  /// - 'original': cropped image with original size
  static Future<Map<String, String>> cropImageToOverlay({
    required String imagePath,
    required ui.Rect overlayRect,
    required ui.Size screenSize,
    required int cameraRotation,
    ui.Rect? bodyArea,
  }) async {
    try {
      debugPrint('=== ImageCropUtils: Starting crop process ===');
      debugPrint('Image path: $imagePath');
      debugPrint('Screen size: ${screenSize.width}x${screenSize.height}');
      debugPrint(
        'Overlay rect: ${overlayRect.left}, ${overlayRect.top}, ${overlayRect.width}x${overlayRect.height}',
      );
      debugPrint('Camera rotation: $cameraRotation degrees');
      
      if (bodyArea != null) {
        debugPrint('Body area: ${bodyArea.left}, ${bodyArea.top}, ${bodyArea.width}x${bodyArea.height}');
      }

      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      debugPrint('Original image bytes size: ${imageBytes.length} bytes');

      // Get the visible portion of the image (what's actually displayed on screen)
      debugPrint('Getting visible portion of the image...');
      
      // Log image vs screen size comparison
      debugPrint('=== IMAGE SIZE COMPARISON ===');
      debugPrint('Screen size: ${screenSize.width}x${screenSize.height}');
      debugPrint('Image will be decoded to get actual size...');
      final visibleImage = await getVisiblePortion(
        imagePath: imagePath,
        flutterScreenSize: screenSize,
        bodyArea: bodyArea,
      );
      
      if (visibleImage == null) {
        debugPrint('ERROR: Failed to get visible portion');
        throw Exception('Failed to get visible portion');
      }
      
      debugPrint('Visible portion: ${visibleImage.width}x${visibleImage.height}');

      // Convert overlay rectangle from screen coordinates to visible image coordinates
      debugPrint('Converting overlay coordinates to visible image coordinates...');
      final imageRect = _flutterToImageRect(overlayRect, visibleImage, bodyArea);
      debugPrint(
        'Image crop rect: ${imageRect.left}, ${imageRect.top}, ${imageRect.width}x${imageRect.height}',
      );

      // Crop the image to the overlay area
      debugPrint('Cropping image to overlay area...');
      final croppedImage = await Isolate.run<img.Image>(
        () => img.copyCrop(
          visibleImage,
          x: imageRect.left.toInt(),
          y: imageRect.top.toInt(),
          width: imageRect.width.toInt(),
          height: imageRect.height.toInt(),
        ),
      );
      debugPrint(
        'Image cropped successfully: ${croppedImage.width}x${croppedImage.height}',
      );

      // Save original cropped image (without resize)
      debugPrint('Saving original cropped image...');
      final originalPngBytes = await Isolate.run<Uint8List>(() {
        final encoded = img.encodePng(croppedImage);
        final bytes = Uint8List.fromList(encoded);
        debugPrint('Original PNG encoded successfully: ${bytes.length} bytes');
        return bytes;
      });

      final String originalCroppedPath =
          '${imageFile.parent.path}/cropped_original_${DateTime.now().millisecondsSinceEpoch}.png';
      await File(originalCroppedPath).writeAsBytes(originalPngBytes);
      debugPrint('Original cropped image saved to: $originalCroppedPath');

      // Resize image to max width of 500px while maintaining aspect ratio
      final resizedImage = await Isolate.run<img.Image>(() {
        const maxWidth = 500.0;
        final currentWidth = croppedImage.width.toDouble();
        final currentHeight = croppedImage.height.toDouble();

        if (currentWidth <= maxWidth) {
          debugPrint(
            'Image width (${currentWidth.toInt()}px) is already within limit, no resize needed',
          );
          return croppedImage;
        }

        // Calculate new height to maintain aspect ratio
        final scaleFactor = maxWidth / currentWidth;
        final newWidth = maxWidth.toInt();
        final newHeight = (currentHeight * scaleFactor).toInt();

        debugPrint(
          'Resizing image from ${currentWidth.toInt()}x${currentHeight.toInt()} to ${newWidth}x$newHeight}',
        );
        final resized = img.copyResize(
          croppedImage,
          width: newWidth,
          height: newHeight,
        );
        debugPrint(
          'Image resized successfully: ${resized.width}x${resized.height}',
        );
        return resized;
      });

      // Encode resized image to PNG
      debugPrint('Encoding resized image to PNG...');
      final resizedPngBytes = await Isolate.run<Uint8List>(() {
        final encoded = img.encodePng(resizedImage);
        final bytes = Uint8List.fromList(encoded);
        debugPrint('Resized PNG encoded successfully: ${bytes.length} bytes');
        return bytes;
      });

      // Save resized image to file
      final String resizedCroppedPath =
          '${imageFile.parent.path}/cropped_resized_${DateTime.now().millisecondsSinceEpoch}.png';
      await File(resizedCroppedPath).writeAsBytes(resizedPngBytes);
      debugPrint('Resized cropped image saved to: $resizedCroppedPath');
      debugPrint('Final resized image size: ${resizedImage.width}x${resizedImage.height}');
      
      debugPrint('=== CROP RESULT SUMMARY ===');
      debugPrint('Original cropped image: ${croppedImage.width}x${croppedImage.height}');
      debugPrint('Resized cropped image: ${resizedImage.width}x${resizedImage.height}');
      debugPrint('=== ImageCropUtils: Crop process completed successfully ===');

      return {
        'resized': resizedCroppedPath,
        'original': originalCroppedPath,
      };
    } catch (e) {
      debugPrint('ERROR: Image crop error: $e');
      rethrow;
    }
  }

  /// Convert Flutter rectangle coordinates to image rectangle coordinates
  /// Using the same approach as mask_for_camera_view example
  static ui.Rect _flutterToImageRect(ui.Rect flutterRect, img.Image image, ui.Rect? bodyArea) {
    // Get physical screen size and device pixel ratio
    final physicalScreenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    final devicePixelRatio = ui.PlatformDispatcher.instance.views.first.devicePixelRatio;

    debugPrint('Physical screen size: ${physicalScreenSize.width}x${physicalScreenSize.height}');
    debugPrint('Device pixel ratio: $devicePixelRatio');
    debugPrint('Image size: ${image.width}x${image.height}');
    
    if (bodyArea != null) {
      debugPrint('Body area: ${bodyArea.left}, ${bodyArea.top}, ${bodyArea.width}x${bodyArea.height}');
    }

    // Calculate image pixel ratio (same as mask_for_camera_view)
    final imagePixelRatio = image.width / physicalScreenSize.width;

    debugPrint('Image pixel ratio: $imagePixelRatio');

    // Convert coordinates using the same formula as mask_for_camera_view
    final imageX = flutterRect.left * devicePixelRatio * imagePixelRatio;
    final imageY = flutterRect.top * devicePixelRatio * imagePixelRatio;
    final imageWidth = flutterRect.width * devicePixelRatio * imagePixelRatio;
    final imageHeight = flutterRect.height * devicePixelRatio * imagePixelRatio;

    debugPrint('Converted coordinates:');
    debugPrint('  - X: $imageX (from ${flutterRect.left})');
    debugPrint('  - Y: $imageY (from ${flutterRect.top})');
    debugPrint('  - Width: $imageWidth (from ${flutterRect.width})');
    debugPrint('  - Height: $imageHeight (from ${flutterRect.height})');

    return ui.Rect.fromLTWH(imageX, imageY, imageWidth, imageHeight);
  }

  /// Calculate camera rotation in degrees based on sensor orientation
  static int getCameraRotation(CameraDescription cameraDescription) {
    // No rotation needed - camera preview already handles orientation
    return 0;
  }

  /// Calculate aspect ratio adjustment for different screen ratios
  static double calculateAspectRatioAdjustment({
    required ui.Size screenSize,
    required double statusBarHeight,
    required double bottomSafeArea,
    double expectedImageAspectRatio = 16.0 / 9.0,
  }) {
    final screenAspectRatio = screenSize.height / screenSize.width;
    final aspectRatioDifference = (screenAspectRatio - expectedImageAspectRatio).abs();
    
    // Only apply adjustment if aspect ratio difference is significant (> 0.1)
    if (aspectRatioDifference > 0.1) {
      return (statusBarHeight - bottomSafeArea) / 2;
    }
    return 0.0;
  }

  /// Get overlay rectangle based on screen dimensions and overlay parameters
  static ui.Rect getOverlayRect({
    required ui.Size screenSize,
    required double overlayLeft,
    required double overlayTop,
    required double overlayWidth,
    required double overlayHeight,
  }) {
    return ui.Rect.fromLTWH(
      overlayLeft,
      overlayTop,
      overlayWidth,
      overlayHeight,
    );
  }

  /// Get the visible portion of the image that corresponds to the screen display
  /// Similar to mask_for_camera_view's getVisiblePortion
  static Future<img.Image?> getVisiblePortion({
    required String imagePath,
    required ui.Size flutterScreenSize,
    ui.Rect? bodyArea,
  }) async {
    try {
      debugPrint('=== ImageCropUtils: Getting visible portion ===');
      debugPrint('Image path: $imagePath');
      debugPrint('Flutter screen size: ${flutterScreenSize.width}x${flutterScreenSize.height}');

      final imageFile = File(imagePath);
      final imageBytes = await imageFile.readAsBytes();
      
      // Decode the image
      final image = await Isolate.run<img.Image?>(
        () => img.decodeImage(imageBytes),
      );
      if (image == null) {
        debugPrint('ERROR: Failed to decode image');
        return null;
      }
      debugPrint('Original image: ${image.width}x${image.height}');
      debugPrint('=== IMAGE vs SCREEN COMPARISON ===');
      debugPrint('Screen size: ${flutterScreenSize.width}x${flutterScreenSize.height}');
      debugPrint('Image size: ${image.width}x${image.height}');
      debugPrint('Ratio: ${image.width / flutterScreenSize.width}x${image.height / flutterScreenSize.height}');
      
      // Calculate aspect ratios
      final screenAspectRatio = flutterScreenSize.height / flutterScreenSize.width;
      final imageAspectRatio = image.height / image.width;
      debugPrint('Screen aspect ratio: ${screenAspectRatio.toStringAsFixed(3)} (${(screenAspectRatio * 9).toStringAsFixed(1)}:9)');
      debugPrint('Image aspect ratio: ${imageAspectRatio.toStringAsFixed(3)} (${(imageAspectRatio * 9).toStringAsFixed(1)}:9)');
      debugPrint('Aspect ratio difference: ${(screenAspectRatio - imageAspectRatio).abs().toStringAsFixed(3)}');
      
      if (bodyArea != null) {
        debugPrint('Body area: ${bodyArea.left}, ${bodyArea.top}, ${bodyArea.width}x${bodyArea.height}');
      }

      // Calculate screen rect in image size
      final screenRectInImageSize = _flutterToImageSize(flutterScreenSize, image);
      debugPrint('Screen rect in image size: ${screenRectInImageSize.width}x${screenRectInImageSize.height}');

      // Calculate crop position (center the screen rect in the image)
      final x = (image.width ~/ 2) - (screenRectInImageSize.width ~/ 2);
      final y = (image.height ~/ 2) - (screenRectInImageSize.height ~/ 2);

      debugPrint('Crop position: x=$x, y=$y');

      // Crop the image to get the visible portion
      final croppedImage = await Isolate.run<img.Image>(
        () => img.copyCrop(
          image,
          x: x > 0 ? x : 0,
          y: y > 0 ? y : 0,
          width: screenRectInImageSize.width.toInt(),
          height: screenRectInImageSize.height.toInt(),
        ),
      );

      debugPrint('Visible portion cropped: ${croppedImage.width}x${croppedImage.height}');
      debugPrint('=== ImageCropUtils: Visible portion completed ===');

      return croppedImage;
    } catch (e) {
      debugPrint('ERROR: Get visible portion error: $e');
      return null;
    }
  }

  /// Convert Flutter screen size to image size
  /// Similar to mask_for_camera_view's flutterToImageSize
  static ui.Size _flutterToImageSize(ui.Size flutterRect, img.Image image) {
    final physicalScreenSize = ui.PlatformDispatcher.instance.views.first.physicalSize;
    final devicePixelRatio = ui.PlatformDispatcher.instance.views.first.devicePixelRatio;
    
    final imagePixelRatio = image.width / physicalScreenSize.width;
    
    final imageWidth = flutterRect.width * devicePixelRatio * imagePixelRatio;
    final imageHeight = flutterRect.height * devicePixelRatio * imagePixelRatio;
    
    return ui.Size(imageWidth, imageHeight);
  }
}
