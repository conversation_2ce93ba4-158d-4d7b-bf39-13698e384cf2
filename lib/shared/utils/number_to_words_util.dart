/// Utility class for converting numbers to Vietnamese words
/// 
/// This utility provides functionality to convert numeric values to their
/// Vietnamese text representation, commonly used for displaying amounts
/// in invoices, receipts, or other financial documents.
class NumberToWordsUtil {
  // Private constructor to prevent instantiation
  NumberToWordsUtil._();

  // Vietnamese number words
  static const List<String> _units = [
    '',
    'một',
    'hai',
    'ba',
    'bốn',
    'năm',
    'sáu',
    'bảy',
    'tám',
    'chín',
  ];

  static const List<String> _tens = [
    '',
    'mười',
    'hai mươi',
    'ba mươi',
    'bốn mươi',
    'năm mươi',
    's<PERSON><PERSON> mươ<PERSON>',
    'bảy mươ<PERSON>',
    'tám mươi',
    'ch<PERSON> mươi',
  ];

  static const List<String> _hundreds = [
    '',
    'một trăm',
    'hai trăm',
    'ba trăm',
    'bốn trăm',
    'năm trăm',
    's<PERSON><PERSON> trăm',
    'b<PERSON>y trăm',
    'tám trăm',
    'chín trăm',
  ];

  /// Converts a number to Vietnamese words
  /// 
  /// [number] - The number to convert (must be non-negative)
  /// [includeCurrency] - Whether to include currency suffix (đồng)
  /// 
  /// Returns the Vietnamese text representation of the number
  /// 
  /// Throws [ArgumentError] if number is negative
  static String convertToWords(
    int number, {
    bool includeCurrency = true,
  }) {
    if (number < 0) {
      throw ArgumentError('Number must be non-negative');
    }

    if (number == 0) {
      return includeCurrency ? 'không đồng' : 'không';
    }

    final result = _convertNumberToWords(number);
    return includeCurrency ? '$result đồng' : result;
  }

  /// Converts a decimal number to Vietnamese words
  /// 
  /// [amount] - The decimal amount to convert
  /// [includeCurrency] - Whether to include currency suffix (đồng)
  /// [decimalPlaces] - Number of decimal places to consider
  /// 
  /// Returns the Vietnamese text representation of the amount
  static String convertDecimalToWords(
    double amount, {
    bool includeCurrency = true,
    int decimalPlaces = 2,
  }) {
    if (amount < 0) {
      throw ArgumentError('Amount must be non-negative');
    }

    final integerPart = amount.floor();
    
    // Handle decimal part based on decimal places
    int decimalPart;
    if (decimalPlaces == 1) {
      decimalPart = ((amount - integerPart) * 10).round();
    } else if (decimalPlaces == 3) {
      decimalPart = ((amount - integerPart) * 1000).round();
    } else {
      // For default 2 decimal places, always use 2 decimal places
      decimalPart = ((amount - integerPart) * 100).round();
    }

    final integerWords = _convertNumberToWords(integerPart);
    
    if (decimalPart == 0) {
      return includeCurrency ? '$integerWords đồng' : integerWords;
    }

    final decimalWords = _convertNumberToWords(decimalPart);
    final result = '$integerWords phẩy $decimalWords';
    
    return includeCurrency ? '$result đồng' : result;
  }

  /// Internal method to convert number to words
  static String _convertNumberToWords(int number) {
    if (number == 0) return 'không';

    final parts = <String>[];

    // Billions
    if (number >= 1000000000) {
      final billions = number ~/ 1000000000;
      parts.add(_convertHundreds(billions));
      parts.add('tỷ');
      number %= 1000000000;
    }

    // Millions
    if (number >= 1000000) {
      final millions = number ~/ 1000000;
      parts.add(_convertHundreds(millions));
      parts.add('triệu');
      number %= 1000000;
    }

    // Hundred thousands
    if (number >= 100000) {
      final hundredThousands = number ~/ 100000;
      parts.add(_convertHundreds(hundredThousands));
      parts.add('trăm');
      number %= 100000;
      
      // Check if remainder is in thousands range for "lẻ"
      if (number >= 1000 && number < 10000) {
        final thousands = number ~/ 1000;
        if (thousands < 10) {
          parts.add('lẻ');
          parts.add(_convertTensAndUnits(thousands));
          parts.add('nghìn');
          number %= 1000;
        }
      } else if (number > 0 && number < 1000) {
        // If remainder is less than 1000, add "nghìn" and "lẻ"
        parts.add('nghìn');
        parts.add('lẻ');
      } else if (number == 0) {
        // If no remainder, add "nghìn"
        parts.add('nghìn');
      }
    }

    // Thousands (including ten thousands)
    if (number >= 1000) {
      final thousands = number ~/ 1000;
      parts.add(_convertHundreds(thousands));
      parts.add('nghìn');
      number %= 1000;
    }

    // Hundreds
    if (number >= 100) {
      final hundreds = number ~/ 100;
      parts.add(_convertHundreds(hundreds));
      parts.add('trăm');
      number %= 100;
    }

    // Add "lẻ" if remainder is less than 10 and there are higher units
    // But only if "lẻ" hasn't been added already
    if (number > 0 && number < 10 && parts.isNotEmpty && !parts.contains('lẻ')) {
      parts.add('lẻ');
    }

    // Tens and units
    if (number > 0) {
      parts.add(_convertTensAndUnits(number));
    }

    return parts.join(' ');
  }

  /// Converts hundreds to words
  static String _convertHundreds(int number) {
    if (number == 0) return '';
    if (number < 100) return _convertTensAndUnits(number);
    
    final hundreds = number ~/ 100;
    final remainder = number % 100;
    
    if (remainder == 0) {
      return _hundreds[hundreds];
    }
    
    final result = _hundreds[hundreds];
    final tensAndUnits = _convertTensAndUnits(remainder);
    
    // Add "lẻ" for numbers like 101, 201, etc.
    if (remainder < 10 && remainder > 0) {
      return '$result lẻ $tensAndUnits';
    }
    
    return '$result $tensAndUnits';
  }

  /// Converts tens and units to words
  static String _convertTensAndUnits(int number) {
    if (number == 0) return '';
    if (number < 10) return _units[number];
    
    final tens = number ~/ 10;
    final units = number % 10;
    
    if (tens == 1) {
      // Special case for 10-19
      if (units == 0) return 'mười';
      if (units == 5) return 'mười lăm';
      return 'mười ${_units[units]}';
    }
    
    if (units == 0) {
      return _tens[tens];
    }
    
    if (units == 5) {
      return '${_tens[tens]} lăm';
    }
    
    return '${_tens[tens]} ${_units[units]}';
  }

  /// Formats a number with thousand separators
  /// 
  /// [number] - The number to format
  /// [separator] - The separator character (default: '.')
  /// 
  /// Returns the formatted number string
  static String formatNumber(int number, {String separator = '.'}) {
    final numberStr = number.toString();
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    return numberStr.replaceAllMapped(regex, (match) => '${match[1]}$separator');
  }

  /// Formats a decimal number with thousand separators
  /// 
  /// [amount] - The decimal amount to format
  /// [decimalPlaces] - Number of decimal places to show
  /// [thousandSeparator] - The thousand separator character (default: '.')
  /// [decimalSeparator] - The decimal separator character (default: ',')
  /// 
  /// Returns the formatted amount string
  static String formatDecimal(
    double amount, {
    int decimalPlaces = 2,
    String thousandSeparator = '.',
    String decimalSeparator = ',',
  }) {
    final integerPart = amount.floor();
    
    // Handle decimal part based on decimal places
    int decimalPart;
    if (decimalPlaces == 1) {
      decimalPart = ((amount - integerPart) * 10).round();
    } else if (decimalPlaces == 3) {
      decimalPart = ((amount - integerPart) * 1000).round();
    } else if (decimalPlaces == 0) {
      // No decimal part
      return formatNumber(integerPart, separator: thousandSeparator);
    } else {
      decimalPart = ((amount - integerPart) * 100).round();
    }
    
    final formattedInteger = formatNumber(integerPart, separator: thousandSeparator);
    final formattedDecimal = decimalPart.toString().padLeft(decimalPlaces, '0');
    
    return '$formattedInteger$decimalSeparator$formattedDecimal';
  }
}