import 'dart:convert';
import '../models/qr_models.dart';
import 'vehicle_qr_basic_util.dart';

/// Utility class for parsing QR codes and mapping data to forms
/// 
/// This utility handles:
/// - CCCD QR code parsing from multiple formats (JSON, text, pipe-separated)
/// - Data mapping to borrower forms
/// - Data mapping to co-borrower forms  
/// - Data mapping to customer forms
/// - Gender mapping with API config support
/// - Vehicle QR code parsing
class QrUtils {
  // Private constructor to prevent instantiation
  QrUtils._();

  /// Parse CCCD QR data from raw QR string (supports multiple formats)
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a map with parsed CCCD data
  static Map<String, String> parseCccdQrDataFromRaw(String qrData) {
    if (qrData.isEmpty) {
      throw ArgumentError('QR data cannot be empty');
    }

    // Initialize result map with empty values
    final Map<String, String> result = {
      'id_number': '',
      'full_name': '',
      'date_of_birth': '',
      'gender': '',
      'nationality': '',
      'place_of_origin': '',
      'place_of_residence': '',
      'personal_id': '',
      'issue_date': '',
      'issue_place': '',
      'expiry_date': '',
    };

    // Try to parse as JSON format first
    try {
      final Map<String, dynamic> data = jsonDecode(qrData);
      _parseCccdJsonData(data, result);
      return result;
    } catch (e) {
      // If JSON parsing fails, try text format
      _parseCccdTextData(qrData, result);
      return result;
    }
  }

  /// Parse CCCD data from JSON format
  static void _parseCccdJsonData(Map<String, dynamic> data, Map<String, String> result) {
    // Handle nested object format (thong_tin_ca_nhan)
    if (data.containsKey('thong_tin_ca_nhan') && data['thong_tin_ca_nhan'] is Map) {
      final personalInfo = data['thong_tin_ca_nhan'] as Map<String, dynamic>;
      _parseCccdFields(personalInfo, result);
      return;
    }

    // Handle flat object format
    _parseCccdFields(data, result);
  }

  /// Parse CCCD data from text format
  static void _parseCccdTextData(String qrData, Map<String, String> result) {
    // Try different text formats
    final lines = qrData.split('\n');
    
    for (final line in lines) {
      if (line.contains(':')) {
        final parts = line.split(':');
        if (parts.length >= 2) {
          final key = parts[0].trim().toLowerCase();
          final value = parts[1].trim();
          
          _mapCccdTextField(key, value, result);
        }
      }
    }
  }

  /// Parse CCCD fields from JSON data
  static void _parseCccdFields(Map<String, dynamic> data, Map<String, String> result) {
    // Parse ID number (Số CCCD)
    result['id_number'] = _getFieldValue(data, [
      'id_number', 'so_cccd', 'so_cmnd', 'id'
    ]);

    // Parse full name (Họ và tên)
    result['full_name'] = _getFieldValue(data, [
      'full_name', 'ho_ten', 'name', 'ten'
    ]);

    // Parse date of birth (Ngày sinh)
    result['date_of_birth'] = _getFieldValue(data, [
      'date_of_birth', 'ngay_sinh', 'birth_date', 'ngay_sinh_cccd'
    ]);

    // Parse gender (Giới tính)
    result['gender'] = _getFieldValue(data, [
      'gender', 'gioi_tinh', 'sex'
    ]);

    // Parse nationality (Quốc tịch)
    result['nationality'] = _getFieldValue(data, [
      'nationality', 'quoc_tich', 'quoc_gia'
    ]);

    // Parse place of origin (Quê quán)
    result['place_of_origin'] = _getFieldValue(data, [
      'place_of_origin', 'que_quan', 'noi_sinh'
    ]);

    // Parse place of residence (Nơi thường trú)
    result['place_of_residence'] = _getFieldValue(data, [
      'place_of_residence', 'noi_thuong_tru', 'dia_chi_thuong_tru', 'address'
    ]);

    // Parse personal ID (Số CMND/CCCD cũ)
    result['personal_id'] = _getFieldValue(data, [
      'personal_id', 'so_cmnd_cu', 'so_cccd_cu'
    ]);

    // Parse issue date (Ngày cấp)
    result['issue_date'] = _getFieldValue(data, [
      'issue_date', 'ngay_cap', 'ngay_cap_cccd'
    ]);

    // Parse issue place (Nơi cấp)
    result['issue_place'] = _getFieldValue(data, [
      'issue_place', 'noi_cap', 'co_quan_cap'
    ]);

    // Parse expiry date (Ngày hết hạn)
    result['expiry_date'] = _getFieldValue(data, [
      'expiry_date', 'ngay_het_han', 'han_su_dung'
    ]);
  }

  /// Map CCCD text field to result
  static void _mapCccdTextField(String key, String value, Map<String, String> result) {
    if (key.contains('so') && key.contains('cccd')) {
      result['id_number'] = value;
    } else if (key.contains('ho') && key.contains('ten')) {
      result['full_name'] = value;
    } else if (key.contains('ngay') && key.contains('sinh')) {
      result['date_of_birth'] = value;
    } else if (key.contains('gioi') && key.contains('tinh')) {
      result['gender'] = value;
    } else if (key.contains('quoc') && key.contains('tich')) {
      result['nationality'] = value;
    } else if (key.contains('que') && key.contains('quan')) {
      result['place_of_origin'] = value;
    } else if (key.contains('thuong') && key.contains('tru')) {
      result['place_of_residence'] = value;
    } else if (key.contains('ngay') && key.contains('cap')) {
      result['issue_date'] = value;
    } else if (key.contains('noi') && key.contains('cap')) {
      result['issue_place'] = value;
    } else if (key.contains('het') && key.contains('han')) {
      result['expiry_date'] = value;
    }
  }

  /// Get field value from data using multiple possible field names
  static String _getFieldValue(Map<String, dynamic> data, List<String> fieldNames) {
    for (final fieldName in fieldNames) {
      if (data.containsKey(fieldName) && data[fieldName] != null) {
        final value = data[fieldName].toString().trim();
        if (value.isNotEmpty) {
          return value;
        }
      }
    }
    return '';
  }

  /// Map CCCD QR data to borrower form
  /// 
  /// [qrData] - Parsed QR data map
  /// [formData] - Form data map to update
  static void mapCccdToBorrowerForm(Map<String, String> qrData, Map<String, dynamic> formData) {
    final cccdData = CccdQrData.fromMap(qrData);
    final borrowerData = BorrowerFormData.fromCccdQr(cccdData);
    final formMap = borrowerData.toFormMap();
    
    // Update form data with mapped values
    formData.addAll(formMap);
  }

  /// Map CCCD QR data to co-borrower form
  /// 
  /// [qrData] - Parsed QR data map
  /// [formData] - Form data map to update
  static void mapCccdToCoBorrowerForm(Map<String, String> qrData, Map<String, dynamic> formData) {
    final cccdData = CccdQrData.fromMap(qrData);
    final coBorrowerData = CoBorrowerFormData.fromCccdQr(cccdData);
    final formMap = coBorrowerData.toFormMap();
    
    // Update form data with mapped values
    formData.addAll(formMap);
  }

  /// Map CCCD QR data to customer form
  /// 
  /// [qrData] - Parsed QR data map
  /// [formData] - Form data map to update
  static void mapCccdToCustomerForm(Map<String, String> qrData, Map<String, dynamic> formData) {
    final cccdData = CccdQrData.fromMap(qrData);
    final customerData = CustomerFormData.fromCccdQr(cccdData);
    final formMap = customerData.toFormMap();
    
    // Update form data with mapped values
    formData.addAll(formMap);
  }

  /// Get Vehicle QR data as model
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns VehicleQrData model or null if invalid
  static VehicleQrData? getVehicleQrDataModel(String qrData) {
    try {
      // Use existing VehicleQRBasicUtil
      final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
      
      // Format vehicle registration date from DDMMYYYY to DD/MM/YYYY
      final registrationDate = parsedData['vehicle_registration_date'];
      if (registrationDate != null && registrationDate.isNotEmpty) {
        final formattedDate = formatDateFromDDMMYYYY(registrationDate);
        if (formattedDate != null) {
          parsedData['vehicle_registration_date'] = formattedDate;
        }
      }
      
      final vehicleData = VehicleQrData.fromMap(parsedData);
      return vehicleData.isValid ? vehicleData : null;
    } catch (e) {
      return null;
    }
  }

  /// Parse CCCD QR data from pipe-separated format
  /// 
  /// Format: idNumber|personalId|fullName|dateOfBirth|gender|placeOfOrigin|issueDate
  /// Example: 027098009929|125856144|Trần Quý Đông|12021998|Nam|Thôn Chi Nhị, Song Giang, Gia Bình, Bắc Ninh|25082021
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a map with parsed CCCD data
  static Map<String, String?> parseCccdQrData(String qrData) {
    if (qrData.isEmpty) {
      throw ArgumentError('QR data cannot be empty');
    }

    // Split by pipe separator
    final parts = qrData.split('|');
    
    if (parts.length < 7) {
      throw ArgumentError('Invalid QR data format. Expected at least 7 fields separated by |');
    }

    // Map the parts to corresponding fields
    final Map<String, String?> result = {
      'id_number': parts[0].trim().isNotEmpty ? parts[0].trim() : null,
      'personal_id': parts[1].trim().isNotEmpty ? parts[1].trim() : null,
      'full_name': parts[2].trim().isNotEmpty ? parts[2].trim() : null,
      'date_of_birth': parts[3].trim().isNotEmpty ? parts[3].trim() : null,
      'gender': parts[4].trim().isNotEmpty ? parts[4].trim() : null,
      'place_of_origin': parts[5].trim().isNotEmpty ? parts[5].trim() : null,
      'issue_date': parts[6].trim().isNotEmpty ? parts[6].trim() : null,
      'nationality': null,
      'place_of_residence': null,
      'issue_place': null,
      'expiry_date': null,
    };

    return result;
  }

  /// Map gender text to API config code
  /// 
  /// [genderText] - Gender text from QR (e.g., "Nam", "Nữ")
  /// [sexConfigs] - List of sex configs from API
  /// 
  /// Returns the corresponding config code or null if not found
  static String? mapGenderToConfigCode(String? genderText, List<Map<String, dynamic>>? sexConfigs) {
    if (genderText == null || genderText.isEmpty || sexConfigs == null) {
      return null;
    }

    // Normalize gender text for comparison
    final normalizedGender = genderText.trim().toLowerCase();
    
    for (final config in sexConfigs) {
      final label = config['label']?.toString().toLowerCase() ?? '';
      final description = config['description']?.toString().toLowerCase() ?? '';
      final code = config['code']?.toString();
      
      if (code != null && 
          (label == normalizedGender || description == normalizedGender)) {
        return code;
      }
    }
    
    return null;
  }

  /// Validate CCCD QR data
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns true if QR data is valid and contains essential fields
  static bool isValidCccdQrData(String qrData) {
    if (qrData.isEmpty) return false;

    try {
      final parsedData = parseCccdQrData(qrData);
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid;
    } catch (e) {
      return false;
    }
  }

  /// Get CCCD QR data as model
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns CccdQrData model or null if invalid
  static CccdQrData? getCccdQrDataModel(String qrData) {
    try {
      final parsedData = parseCccdQrData(qrData);
      
      // Format dates from DDMMYYYY to DD/MM/YYYY
      parsedData['date_of_birth'] = formatDateFromDDMMYYYY(parsedData['date_of_birth']);
      parsedData['issue_date'] = formatDateFromDDMMYYYY(parsedData['issue_date']);
      
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid ? cccdData : null;
    } catch (e) {
      return null;
    }
  }

  /// Get CCCD QR data as model with gender mapping
  /// 
  /// [qrData] - Raw QR data string
  /// [sexConfigs] - Optional sex configs for gender mapping
  /// 
  /// Returns CccdQrData model with mapped gender or null if invalid
  static CccdQrData? getCccdQrDataModelWithGenderMapping(
    String qrData, {
    List<Map<String, dynamic>>? sexConfigs,
  }) {
    try {
      final parsedData = parseCccdQrData(qrData);
      
      // Format dates from DDMMYYYY to DD/MM/YYYY
      parsedData['date_of_birth'] = formatDateFromDDMMYYYY(parsedData['date_of_birth']);
      parsedData['issue_date'] = formatDateFromDDMMYYYY(parsedData['issue_date']);
      
      // Map gender if sex configs provided
      if (sexConfigs != null && parsedData['gender'] != null) {
        final genderCode = mapGenderToConfigCode(parsedData['gender'], sexConfigs);
        if (genderCode != null) {
          parsedData['gender'] = genderCode;
        }
      }
      
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid ? cccdData : null;
    } catch (e) {
      return null;
    }
  }

  /// Parse date from DDMMYYYY format to DD/MM/YYYY
  /// 
  /// [dateString] - Date string in DDMMYYYY format
  /// 
  /// Returns formatted date string or original if invalid
  static String? formatDateFromDDMMYYYY(String? dateString) {
    if (dateString == null || dateString.isEmpty || dateString.length != 8) {
      return dateString;
    }

    try {
      final day = dateString.substring(0, 2);
      final month = dateString.substring(2, 4);
      final year = dateString.substring(4, 8);
      
      return '$day/$month/$year';
    } catch (e) {
      return dateString;
    }
  }

  /// Parse date from QR format to DateTime
  /// 
  /// Supports multiple formats:
  /// - DD/MM/YYYY (formatted from QR)
  /// - DDMMYYYY (raw from QR)
  /// - ISO string
  /// 
  /// [dateString] - Date string from QR
  /// 
  /// Returns DateTime object or null if invalid
  static DateTime? parseDateFromQr(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return null;
    }

    try {
      // Try DD/MM/YYYY format first (formatted from QR)
      if (dateString.contains('/')) {
        final parts = dateString.split('/');
        if (parts.length == 3) {
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          return DateTime(year, month, day);
        }
      }
      
      // Try DDMMYYYY format (raw from QR)
      if (dateString.length == 8 && RegExp(r'^\d{8}$').hasMatch(dateString)) {
        final day = int.parse(dateString.substring(0, 2));
        final month = int.parse(dateString.substring(2, 4));
        final year = int.parse(dateString.substring(4, 8));
        return DateTime(year, month, day);
      }
      
      // Try parsing as ISO string
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Parse CCCD QR data with formatted dates
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a map with parsed CCCD data and formatted dates
  static Map<String, String?> parseCccdQrDataWithFormattedDates(String qrData) {
    final parsedData = parseCccdQrData(qrData);
    
    // Format dates
    parsedData['date_of_birth'] = formatDateFromDDMMYYYY(parsedData['date_of_birth']);
    parsedData['issue_date'] = formatDateFromDDMMYYYY(parsedData['issue_date']);
    
    return parsedData;
  }
}
