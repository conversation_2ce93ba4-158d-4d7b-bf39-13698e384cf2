import 'dart:io';
import '../utils/app_logger.dart';
import '../constants/api_endpoints.dart';
import '../services/backend_url_manager.dart';

/// Helper để xử lý URL replacement cho storage
class StorageUrlHelper {
  static final AppLogger _logger = AppLogger();

  /// Replace MinIO domain với current backend domain
  static Future<String> replaceDomainInUrl(String presignedUrl) async {
    try {
      // Lấy current backend URL từ BackendUrlManager
      final currentDomain = await BackendUrlManager.getSelectedUrl();
      
      // Extract domain từ current backend URL
      final uri = Uri.parse(currentDomain);
      final appDomain = '${uri.scheme}://${uri.host}${uri.port != 80 && uri.port != 443 ? ':${uri.port}' : ''}';
      
      // Parse presigned URL
      final presignedUri = Uri.parse(presignedUrl);
      
      // Replace domain
      final newUrl = presignedUrl.replaceFirst(
        '${presignedUri.scheme}://${presignedUri.host}${presignedUri.port != 80 && presignedUri.port != 443 ? ':${presignedUri.port}' : ''}',
        appDomain,
      );
      
      _logger.d('Replaced domain in presigned URL: $presignedUrl -> $newUrl');
      return newUrl;
    } catch (e) {
      _logger.w('Failed to replace domain in presigned URL: $e');
      return presignedUrl; // Return original URL if replacement fails
    }
  }

  /// Replace domain trong multiple URLs
  static Future<List<String>> replaceDomainInUrls(List<String> presignedUrls) async {
    final List<String> replacedUrls = [];
    for (final url in presignedUrls) {
      final replacedUrl = await replaceDomainInUrl(url);
      replacedUrls.add(replacedUrl);
    }
    return replacedUrls;
  }

  /// Get current backend domain
  static Future<String> getCurrentBackendDomain() async {
    try {
      final currentUrl = await BackendUrlManager.getSelectedUrl();
      final uri = Uri.parse(currentUrl);
      return '${uri.scheme}://${uri.host}${uri.port != 80 && uri.port != 443 ? ':${uri.port}' : ''}';
    } catch (e) {
      _logger.w('Failed to get current backend domain: $e');
      // Fallback to default
      return ApiEndpoints.devBackendApiUrl;
    }
  }

  /// Extract object name từ presigned URL
  static String? extractObjectNameFromUrl(String presignedUrl) {
    try {
      final uri = Uri.parse(presignedUrl);
      final pathSegments = uri.pathSegments;
      
      // Remove bucket name và lấy object path
      if (pathSegments.length > 1) {
        return pathSegments.skip(1).join('/');
      }
      return null;
    } catch (e) {
      _logger.w('Failed to extract object name from URL: $e');
      return null;
    }
  }

  /// Validate presigned URL format
  static bool isValidPresignedUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.hasAuthority && uri.hasQuery;
    } catch (e) {
      return false;
    }
  }

  /// Get URL expiration time từ presigned URL
  static DateTime? getUrlExpirationTime(String presignedUrl) {
    try {
      final uri = Uri.parse(presignedUrl);
      final expirationParam = uri.queryParameters['X-Amz-Date'];
      
      if (expirationParam != null) {
        // Parse AWS date format
        return DateTime.parse(expirationParam);
      }
      return null;
    } catch (e) {
      _logger.w('Failed to get expiration time from URL: $e');
      return null;
    }
  }

  /// Check if URL needs domain replacement
  static Future<bool> needsDomainReplacement(String url) async {
    try {
      final uri = Uri.parse(url);
      final currentDomain = await BackendUrlManager.getSelectedUrl();
      final currentUri = Uri.parse(currentDomain);
      
      // Check if domains are different
      return uri.host != currentUri.host || uri.port != currentUri.port;
    } catch (e) {
      return false;
    }
  }

  /// Validate file size
  static bool validateFileSize(int fileSize, {int maxSizeInMB = 50}) {
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return fileSize <= maxSizeInBytes;
  }

  /// Validate file type
  static bool validateFileType(String mimeType, List<String> allowedTypes) {
    return allowedTypes.contains(mimeType.toLowerCase());
  }

  /// Generate unique filename
  static String generateUniqueFilename(String originalName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecondsSinceEpoch % 1000;
    final extension = _getFileExtension(originalName);
    final baseName = originalName.split('.').first;
    
    return '${baseName}_$timestamp$random.$extension';
  }

  /// Get file extension từ filename
  static String _getFileExtension(String filename) {
    final parts = filename.split('.');
    if (parts.length > 1) {
      return parts.last.toLowerCase();
    }
    return '';
  }

  /// Create folder path
  static String createFolderPath({
    required String baseFolder,
    String? subFolder,
    String? userId,
  }) {
    final pathParts = <String>[baseFolder];
    
    if (subFolder != null) {
      pathParts.add(subFolder);
    }
    
    if (userId != null) {
      pathParts.add(userId);
    }
    
    return pathParts.join('/');
  }

  /// Get file info
  static Future<FileInfo> getFileInfo(File file) async {
    final stat = await file.stat();
    return FileInfo(
      name: file.path.split('/').last,
      size: stat.size,
      modified: stat.modified,
      path: file.path,
    );
  }
}

/// Model cho file info
class FileInfo {
  final String name;
  final int size;
  final DateTime modified;
  final String path;

  const FileInfo({
    required this.name,
    required this.size,
    required this.modified,
    required this.path,
  });

  @override
  String toString() {
    return 'FileInfo(name: $name, size: $size, path: $path)';
  }
} 