class DateUtils {
  /// Date utility functions for the app
  /// Sử dụng định dạng dd/mm/yyyy theo chuẩn Việt Nam
  /// Format DateTime thành string theo định dạng dd/mm/yyyy
  static String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
           '${date.month.toString().padLeft(2, '0')}/'
           '${date.year}';
  }

  /// Parse string dd/mm/yyyy thành DateTime
  /// Trả về null nếu format không hợp lệ
  static DateTime? parseDate(String dateString) {
    if (dateString.isEmpty) return null;
    
    final parts = dateString.split('/');
    if (parts.length != 3) return null;
    
    try {
      return DateTime(
        int.parse(parts[2]), // year
        int.parse(parts[1]), // month
        int.parse(parts[0]), // day
      );
    } catch (e) {
      return null;
    }
  }

  /// <PERSON><PERSON>m tra xem string có phải là format date hợp lệ không
  static bool isValidDateString(String dateString) {
    return parseDate(dateString) != null;
  }

  /// Lấy ngày hiện tại theo format dd/mm/yyyy
  static String getCurrentDateString() {
    return formatDate(DateTime.now());
  }

  /// Lấy ngày đầu tháng theo format dd/mm/yyyy
  static String getFirstDayOfMonthString() {
    final now = DateTime.now();
    return formatDate(DateTime(now.year, now.month, 1));
  }

  /// Lấy ngày cuối tháng theo format dd/mm/yyyy
  static String getLastDayOfMonthString() {
    final now = DateTime.now();
    return formatDate(DateTime(now.year, now.month + 1, 0));
  }

  /// Lấy ngày đầu năm theo format dd/mm/yyyy
  static String getFirstDayOfYearString() {
    final now = DateTime.now();
    return formatDate(DateTime(now.year, 1, 1));
  }

  /// Lấy ngày cuối năm theo format dd/mm/yyyy
  static String getLastDayOfYearString() {
    final now = DateTime.now();
    return formatDate(DateTime(now.year, 12, 31));
  }

  /// Tính số ngày giữa hai ngày
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return to.difference(from).inDays;
  }

  /// Kiểm tra xem ngày có nằm trong khoảng cho phép không
  static bool isDateInRange(DateTime date, DateTime? firstDate, DateTime? lastDate) {
    if (firstDate != null && date.isBefore(firstDate)) return false;
    if (lastDate != null && date.isAfter(lastDate)) return false;
    return true;
  }

  /// Lấy date range hợp lý cho các trường hợp phổ biến
  static Map<String, DateTime> getDefaultDateRange() {
    final now = DateTime.now();
    return {
      'initial': now,
      'first': DateTime(1940), // Cho phép chọn từ 1940
      'last': DateTime(2100),  // Cho phép chọn đến 2100
    };
  }

  /// Lấy date range cho CCCD (từ 1940 đến hiện tại)
  static Map<String, DateTime> getCccdDateRange() {
    final now = DateTime.now();
    return {
      'initial': now,
      'first': DateTime(1940),
      'last': now,
    };
  }

  /// Lấy date range cho ngày sinh (từ 1940 đến hiện tại)
  static Map<String, DateTime> getBirthDateRange() {
    final now = DateTime.now();
    return {
      'initial': DateTime(now.year - 25, now.month, now.day), // Mặc định 25 tuổi
      'first': DateTime(1940),
      'last': now,
    };
  }

  /// Lấy date range cho ngày cấp CCCD (từ 2010 đến hiện tại)
  static Map<String, DateTime> getCccdIssueDateRange() {
    final now = DateTime.now();
    return {
      'initial': now,
      'first': DateTime(2010), // CCCD mới từ 2010
      'last': now,
    };
  }

  /// Lấy date range cho ngày hết hạn CCCD (từ hiện tại đến 15 năm sau)
  static Map<String, DateTime> getCccdExpiryDateRange() {
    final now = DateTime.now();
    return {
      'initial': now,
      'first': now,
      'last': DateTime(now.year + 15, now.month, now.day), // CCCD có thể hết hạn tối đa 15 năm
    };
  }
}
