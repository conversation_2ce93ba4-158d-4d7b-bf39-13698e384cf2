import 'dart:convert';

/// Utility class for parsing basic vehicle QR data with only 3 essential fields
/// 
/// This utility focuses on parsing only the essential fields:
/// - Biển kiểm soát (Plate Number)
/// - <PERSON><PERSON> máy (Engine Number) 
/// - <PERSON><PERSON> khung (Frame Number)
/// 
/// Other fields will be left empty for user to input manually
class VehicleQRBasicUtil {
  // Private constructor to prevent instantiation
  VehicleQRBasicUtil._();

  /// Parse basic vehicle QR data with only 3 essential fields
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a map with parsed data, focusing on 3 basic fields:
  /// - vehicle_plate_number: Biển kiểm soát
  /// - vehicle_engine_number: Số máy
  /// - vehicle_frame_number: Số khung
  /// 
  /// Other fields will be empty strings for user input
  static Map<String, String> parseBasicVehicleQRData(String qrData) {
    if (qrData.isEmpty) {
      throw ArgumentError('QR data cannot be empty');
    }

    // Initialize result map with empty values
    final Map<String, String> result = {
      'vehicle_plate_number': '',
      'vehicle_engine_number': '',
      'vehicle_frame_number': '',
      'vehicle_name': '',
      'vehicle_registration_number': '',
      'vehicle_registration_place': '',
      'vehicle_registration_date': '',
      'collateral_owner': '',
      'collateral_owner_address': '',
    };

    // Try to parse as text format first (semicolon separated)
    if (_isTextFormat(qrData)) {
      _parseTextFormat(qrData, result);
      return result;
    }

    // Try to parse as JSON format
    try {
      final Map<String, dynamic> data = jsonDecode(qrData);
      
      // Handle nested object format (thong_tin_xe)
      if (data.containsKey('thong_tin_xe') && data['thong_tin_xe'] is Map) {
        final vehicleInfo = data['thong_tin_xe'] as Map<String, dynamic>;
        _parseBasicFields(vehicleInfo, result);
        
        // Parse additional fields if available
        _parseAdditionalFields(data, result);
        return result;
      }

      // Handle array format (xe_may)
      if (data.containsKey('xe_may') && data['xe_may'] is List) {
        final vehicleList = data['xe_may'] as List;
        if (vehicleList.isNotEmpty && vehicleList.first is Map) {
          final vehicleInfo = vehicleList.first as Map<String, dynamic>;
          _parseBasicFields(vehicleInfo, result);
          
          // Parse additional fields if available
          _parseAdditionalFields(data, result);
          return result;
        }
      }

      // Handle flat object format
      _parseBasicFields(data, result);
      _parseAdditionalFields(data, result);
      
      return result;

    } catch (e) {
      throw FormatException('Invalid QR data format: $e');
    }
  }

  /// Check if QR data is in text format (semicolon separated)
  static bool _isTextFormat(String qrData) {
    return qrData.contains(';') && 
           !qrData.trim().startsWith('{') && 
           !qrData.trim().startsWith('[');
  }

  /// Parse text format QR data (semicolon separated)
  /// Format: "29AA-946.89; Nền màu trắng, chữ và số màu đen; JK19E2026765; RLHJK1914PZ012285"
  static void _parseTextFormat(String qrData, Map<String, String> result) {
    final parts = qrData.split(';');
    
    if (parts.length >= 4) {
      result['vehicle_plate_number'] = parts[0].trim();      // Biển kiểm soát
      result['vehicle_engine_number'] = parts[2].trim();     // Số máy
      result['vehicle_frame_number'] = parts[3].trim();      // Số khung
      // parts[1] is color description - skip
    }
  }

  /// Parse basic 3 fields from vehicle info
  static void _parseBasicFields(Map<String, dynamic> vehicleInfo, Map<String, String> result) {
    // Parse plate number (biển kiểm soát)
    result['vehicle_plate_number'] = _getFieldValue(vehicleInfo, [
      'plate_number', 'bien_so_xe', 'bien_so'
    ]);

    // Parse engine number (số máy)
    result['vehicle_engine_number'] = _getFieldValue(vehicleInfo, [
      'engine_number', 'so_may', 'so_may_xe'
    ]);

    // Parse frame number (số khung)
    result['vehicle_frame_number'] = _getFieldValue(vehicleInfo, [
      'frame_number', 'so_khung', 'so_khung_xe'
    ]);
  }

  /// Parse additional fields if available
  static void _parseAdditionalFields(Map<String, dynamic> data, Map<String, String> result) {
    // Parse vehicle name
    result['vehicle_name'] = _getFieldValue(data, [
      'vehicle_name', 'ten_xe', 'ten_loai_xe'
    ]);

    // Parse registration info
    result['vehicle_registration_number'] = _getFieldValue(data, [
      'registration_number', 'so_dang_ky', 'so_giay_dang_ky'
    ]);

    result['vehicle_registration_place'] = _getFieldValue(data, [
      'registration_place', 'noi_cap', 'co_quan_cap'
    ]);

    // Parse registration date and format it
    final registrationDate = _getFieldValue(data, [
      'registration_date', 'ngay_cap', 'ngay_dang_ky'
    ]);
    result['vehicle_registration_date'] = _formatDate(registrationDate);

    // Parse owner info
    result['collateral_owner'] = _getFieldValue(data, [
      'owner_name', 'chu_xe', 'ten_chu_xe', 'ho_ten'
    ]);

    result['collateral_owner_address'] = _getFieldValue(data, [
      'owner_address', 'dia_chi_chu_xe', 'dia_chi'
    ]);
  }

  /// Get field value from multiple possible field names
  static String _getFieldValue(Map<String, dynamic> data, List<String> fieldNames) {
    for (final fieldName in fieldNames) {
      if (data.containsKey(fieldName) && data[fieldName] != null) {
        final value = data[fieldName].toString();
        if (value.isNotEmpty) {
          return value;
        }
      }
    }
    return '';
  }

  /// Format date from various formats to DD/MM/YYYY
  static String _formatDate(String dateString) {
    if (dateString.isEmpty) return '';
    
    try {
      // Handle YYYY-MM-DD format
      if (dateString.contains('-') && dateString.length >= 10) {
        final parts = dateString.split('-');
        if (parts.length >= 3) {
          final year = parts[0];
          final month = parts[1].padLeft(2, '0');
          final day = parts[2].padLeft(2, '0');
          return '$day/$month/$year';
        }
      }
      
      // Handle DD/MM/YYYY format (already formatted)
      if (dateString.contains('/') && dateString.length >= 10) {
        return dateString;
      }
      
      // Return as is if cannot parse
      return dateString;
    } catch (e) {
      return dateString;
    }
  }

  /// Validate if QR data contains the 3 basic required fields
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns true if all 3 basic fields are present and non-empty
  static bool isValidBasicVehicleQRData(String qrData) {
    if (qrData.isEmpty) return false;

    // Check text format first (semicolon separated)
    if (_isTextFormat(qrData)) {
      return _validateTextFormat(qrData);
    }

    // Check JSON format
    try {
      final Map<String, dynamic> data = jsonDecode(qrData);
      
      // Check for nested object format
      if (data.containsKey('thong_tin_xe') && data['thong_tin_xe'] is Map) {
        final vehicleInfo = data['thong_tin_xe'] as Map<String, dynamic>;
        return _validateBasicFields(vehicleInfo);
      }

      // Check for array format
      if (data.containsKey('xe_may') && data['xe_may'] is List) {
        final vehicleList = data['xe_may'] as List;
        if (vehicleList.isNotEmpty && vehicleList.first is Map) {
          final vehicleInfo = vehicleList.first as Map<String, dynamic>;
          return _validateBasicFields(vehicleInfo);
        }
      }

      // Check flat object format
      return _validateBasicFields(data);

    } catch (e) {
      return false;
    }
  }

  /// Validate text format QR data
  static bool _validateTextFormat(String qrData) {
    final parts = qrData.split(';');
    
    if (parts.length < 4) return false;
    
    return parts[0].trim().isNotEmpty && 
           parts[2].trim().isNotEmpty && 
           parts[3].trim().isNotEmpty;
  }

  /// Validate that all 3 basic fields are present and non-empty
  static bool _validateBasicFields(Map<String, dynamic> data) {
    return _hasField(data, ['plate_number', 'bien_so_xe', 'bien_so']) &&
           _hasField(data, ['engine_number', 'so_may', 'so_may_xe']) &&
           _hasField(data, ['frame_number', 'so_khung', 'so_khung_xe']);
  }

  /// Check if any of the field names exists and has non-empty value
  static bool _hasField(Map<String, dynamic> data, List<String> fieldNames) {
    for (final fieldName in fieldNames) {
      if (data.containsKey(fieldName) && 
          data[fieldName] != null && 
          data[fieldName].toString().isNotEmpty) {
        return true;
      }
    }
    return false;
  }

  /// Get available fields in QR data
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a list of available field names
  static List<String> getAvailableFields(String qrData) {
    if (qrData.isEmpty) return [];

    try {
      final Map<String, dynamic> data = jsonDecode(qrData);
      return data.keys.toList();
    } catch (e) {
      return [];
    }
  }

  /// Generate sample QR data with only 3 basic fields
  /// 
  /// Returns a sample QR data string for testing
  static String generateBasicSampleQRData() {
    return '''
{
  "bien_so_xe": "29AA-946.89",
  "so_may": "JK19E2026765",
  "so_khung": "RLHJK1914PZ012285"
}
''';
  }
}
