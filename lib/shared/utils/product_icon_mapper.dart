import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';

/// Helper class để map icon string từ API sang TablerIcons
class ProductIconMapper {
  static const Map<String, IconData> _iconMap = {
    'books': TablerIcons.books,
    'cash': TablerIcons.cash,
    'credit_card': TablerIcons.credit_card,
    'card': TablerIcons.credit_card,
    'building': TablerIcons.building,
    'building-community': TablerIcons.building_community,
    'home': TablerIcons.home,
    'home-dollar': TablerIcons.home_dollar,
    'pig': TablerIcons.pig,
    'piggy_bank': TablerIcons.pig,
    'shield': TablerIcons.shield,
    'protection': TablerIcons.shield,
    'dots': TablerIcons.dots,
    'more': TablerIcons.dots,
    'money': TablerIcons.currency_dollar,
    'loan': TablerIcons.cash,
    'saving': TablerIcons.pig,
    'insurance': TablerIcons.shield,
    'investment': TablerIcons.trending_up,
    'bank': TablerIcons.building_bank,
    'coins': TablerIcons.coins,
    'transfer': TablerIcons.transfer,
    'payment': TablerIcons.credit_card,
    'users': TablerIcons.users,
    'car-suv': TablerIcons.car_suv,
    'shovel-pitchforks': TablerIcons.shovel_pitchforks,
  };

  /// Lấy IconData từ icon string
  static IconData getIcon(String iconName) {
    return _iconMap[iconName.toLowerCase()] ?? TablerIcons.circle;
  }

  /// Kiểm tra icon có tồn tại không
  static bool hasIcon(String iconName) {
    return _iconMap.containsKey(iconName.toLowerCase());
  }

  /// Lấy danh sách tất cả icon names có sẵn
  static List<String> get availableIcons => _iconMap.keys.toList();
}
