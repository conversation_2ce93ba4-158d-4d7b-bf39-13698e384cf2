import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../../features/account/models/log_entry.dart' as log_models;
import '../../features/account/models/log_entry.dart' show LogLevel;
import '../../features/account/models/log_settings.dart';
import '../../features/account/services/log_management_service.dart';
import '../../features/account/services/log_settings_service.dart';
import 'app_logger_interface.dart';

/// App Logger - Centralized logging utility for the entire application
/// Provides consistent logging interface across all features
/// Now integrated with database logging and user settings
class AppLogger implements IAppLogger {
  static final AppLogger _instance = AppLogger._internal();
  factory AppLogger() => _instance;
  AppLogger._internal();

  late final Logger _logger;
  bool _isInitialized = false;

  /// Initialize the logger with custom configuration
  @override
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize logging system
      await LogManagementService.initialize();

      // Initialize logger with settings
      await _initializeLoggerWithSettings();

      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('AppLogger initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing AppLogger: $e');
      }
    }
  }

  /// Initialize logger with current settings
  Future<void> _initializeLoggerWithSettings() async {
    try {
      final settings = await LogSettingsService.getSettings();

      _logger = Logger(
        filter: _CustomLogFilter(settings),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        ),
        output: _CustomOutput(settings),
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing logger with settings: $e');
      }
      // Fallback to default configuration
      _logger = Logger(
        filter: _CustomLogFilter(null),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        ),
        output: ConsoleOutput(),
      );
    }
  }

  /// Get logger instance
  @override
  Logger get logger => _logger;

  /// Log info message
  @override
  Future<void> i(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.info, message, error, stackTrace);
  }

  /// Log debug message
  @override
  Future<void> d(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.debug, message, error, stackTrace);
  }

  /// Log warning message
  @override
  Future<void> w(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.warning, message, error, stackTrace);
  }

  /// Log error message
  @override
  Future<void> e(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.error, message, error, stackTrace);
  }

  /// Log verbose message
  @override
  Future<void> v(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.verbose, message, error, stackTrace);
  }

  /// Internal logging method
  Future<void> _log(
    LogLevel level,
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    try {
      // Check if logging is enabled
      final isEnabled = await LogSettingsService.isLoggingEnabled();
      if (!isEnabled) return;

      // Check minimum level
      final minimumLevel = await LogSettingsService.getMinimumLevel();
      if (level.index < minimumLevel.index) return;

      // Create log entry
      final logEntry = log_models.LogEntry(
        id: _generateLogId(),
        level: _convertToLogEntryLevel(level),
        message: message,
        timestamp: DateTime.now(),
        errorData: error?.toString(),
        stackTrace: stackTrace?.toString(),
        feature: _getCurrentFeature(),
        tag: _getCurrentTag(),
      );

      // Log to console if enabled
      final isConsoleEnabled =
          await LogSettingsService.isConsoleLoggingEnabled();
      if (isConsoleEnabled) {
        _logger.log(
          _convertToLoggerLevel(level),
          message,
          error: error,
          stackTrace: stackTrace,
        );
      }

      // Log to database if enabled
      final isDatabaseEnabled =
          await LogSettingsService.isDatabaseLoggingEnabled();
      if (isDatabaseEnabled && _isInitialized) {
        await LogManagementService.writeLog(logEntry);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error in _log: $e');
      }
    }
  }

  /// Log with custom tag
  @override
  Future<void> logWithTag(
    String tag,
    String message, [
    LogLevel level = LogLevel.info,
  ]) async {
    await _logWithContext(level, message, tag: tag);
  }

  /// Log with feature context
  @override
  Future<void> logWithFeature(
    String feature,
    String message, [
    LogLevel level = LogLevel.info,
  ]) async {
    await _logWithContext(level, message, feature: feature);
  }

  /// Log with context
  Future<void> _logWithContext(
    LogLevel level,
    String message, {
    String? tag,
    String? feature,
  }) async {
    try {
      // Check if logging is enabled
      final isEnabled = await LogSettingsService.isLoggingEnabled();
      if (!isEnabled) return;

      // Check minimum level
      final minimumLevel = await LogSettingsService.getMinimumLevel();
      if (level.index < minimumLevel.index) return;

      // Create log entry
      final logEntry = log_models.LogEntry(
        id: _generateLogId(),
        level: _convertToLogEntryLevel(level),
        message: message,
        timestamp: DateTime.now(),
        feature: feature ?? _getCurrentFeature(),
        tag: tag ?? _getCurrentTag(),
      );

      // Log to console if enabled
      final isConsoleEnabled =
          await LogSettingsService.isConsoleLoggingEnabled();
      if (isConsoleEnabled) {
        final taggedMessage = tag != null ? '[$tag] $message' : message;
        _logger.log(_convertToLoggerLevel(level), taggedMessage);
      }

      // Log to database if enabled
      final isDatabaseEnabled =
          await LogSettingsService.isDatabaseLoggingEnabled();
      if (isDatabaseEnabled && _isInitialized) {
        await LogManagementService.writeLog(logEntry);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error in _logWithContext: $e');
      }
    }
  }

  /// Log API request
  Future<void> logApiRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    dynamic data,
  }) async {
    if (!kDebugMode) return;

    await i('🚀 $method $url');
    if (headers != null) {
      await d('📝 Headers: ${_maskSensitiveHeaders(headers)}');
    }
    if (data != null) {
      await d('📦 Data: ${_maskSensitiveData(data)}');
    }
  }

  /// Log API response
  Future<void> logApiResponse(
    int statusCode,
    String url, {
    dynamic data,
  }) async {
    if (!kDebugMode) return;

    if (statusCode >= 200 && statusCode < 300) {
      await i('✅ $statusCode $url');
    } else {
      await e('❌ $statusCode $url');
    }

    if (data != null) {
      await d('📄 Response: ${_maskSensitiveData(data)}');
    }
  }

  /// Log API error
  Future<void> logApiError(
    String method,
    String url,
    dynamic error, {
    dynamic responseData,
  }) async {
    if (!kDebugMode) return;

    await e('❌ $method $url');
    await e('🔍 Error: $error');

    if (responseData != null) {
      await e('📄 Error Data: ${_maskSensitiveData(responseData)}');
    }
  }

  /// Log feature initialization
  Future<void> logFeatureInit(String featureName) async {
    await logWithFeature(featureName, '🚀 Initializing $featureName');
  }

  /// Log feature success
  Future<void> logFeatureSuccess(String featureName, [String? details]) async {
    final message =
        '✅ $featureName initialized successfully${details != null ? ': $details' : ''}';
    await logWithFeature(featureName, message);
  }

  /// Log feature error
  Future<void> logFeatureError(String featureName, dynamic error) async {
    await logWithFeature(
      featureName,
      '❌ $featureName initialization failed: $error',
      LogLevel.error,
    );
  }

  /// Log user action
  Future<void> logUserAction(
    String action, {
    Map<String, dynamic>? parameters,
  }) async {
    if (!kDebugMode) return;

    final params = parameters != null ? ' with params: $parameters' : '';
    await logWithTag('USER_ACTION', '👤 User action: $action$params');
  }

  /// Log navigation
  Future<void> logNavigation(
    String from,
    String to, {
    Map<String, dynamic>? arguments,
  }) async {
    if (!kDebugMode) return;

    final args = arguments != null ? ' with args: $arguments' : '';
    await logWithTag('NAVIGATION', '🧭 Navigation: $from → $to$args');
  }

  /// Log database operation
  Future<void> logDatabaseOp(
    String operation,
    String table, {
    dynamic data,
  }) async {
    if (!kDebugMode) return;

    await logWithTag('DATABASE', '💾 Database $operation on $table');
    if (data != null) {
      await d('📊 Data: $data');
    }
  }

  /// Log cache operation
  Future<void> logCacheOp(String operation, String key, {dynamic data}) async {
    if (!kDebugMode) return;

    await logWithTag('CACHE', '💿 Cache $operation: $key');
    if (data != null) {
      await d('📊 Data: $data');
    }
  }

  /// Log performance metric
  Future<void> logPerformance(String operation, Duration duration) async {
    if (!kDebugMode) return;

    final ms = duration.inMilliseconds;
    String message;
    LogLevel level;

    if (ms < 100) {
      message = '⚡ $operation: ${ms}ms';
      level = LogLevel.debug;
    } else if (ms < 1000) {
      message = '🐌 $operation: ${ms}ms';
      level = LogLevel.warning;
    } else {
      message = '🐌🐌 $operation: ${ms}ms (slow!)';
      level = LogLevel.error;
    }

    await logWithTag('PERFORMANCE', message, level);
  }

  /// Generate unique log ID
  String _generateLogId() {
    return DateTime.now().millisecondsSinceEpoch.toString() +
        (1000 + (DateTime.now().microsecond % 1000)).toString();
  }

  /// Get current feature name
  String _getCurrentFeature() {
    // This can be enhanced to detect current feature from stack trace
    return 'app';
  }

  /// Get current tag
  String _getCurrentTag() {
    // This can be enhanced to detect current tag from stack trace
    return 'general';
  }

  /// Convert LogLevel to logger Level
  Level _convertToLoggerLevel(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return Level.trace;
      case LogLevel.debug:
        return Level.debug;
      case LogLevel.info:
        return Level.info;
      case LogLevel.warning:
        return Level.warning;
      case LogLevel.error:
        return Level.error;
    }
  }

  /// Convert LogLevel to LogEntry LogLevel
  log_models.LogLevel _convertToLogEntryLevel(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return log_models.LogLevel.verbose;
      case LogLevel.debug:
        return log_models.LogLevel.debug;
      case LogLevel.info:
        return log_models.LogLevel.info;
      case LogLevel.warning:
        return log_models.LogLevel.warning;
      case LogLevel.error:
        return log_models.LogLevel.error;
    }
  }

  /// Mask sensitive data in headers
  Map<String, dynamic> _maskSensitiveHeaders(Map<String, dynamic> headers) {
    final maskedHeaders = Map<String, dynamic>.from(headers);

    const sensitiveHeaderKeys = [
      'authorization',
      'Authorization',
      'AUTHORIZATION',
      'x-api-key',
      'X-API-Key',
      'x-auth-token',
      'X-Auth-Token',
    ];

    for (final key in sensitiveHeaderKeys) {
      if (maskedHeaders.containsKey(key)) {
        final value = maskedHeaders[key]?.toString() ?? '';
        if (value.isNotEmpty) {
          if (value.length > 20) {
            maskedHeaders[key] =
                '${value.substring(0, 10)}***...***${value.substring(value.length - 4)}';
          } else {
            maskedHeaders[key] = '***MASKED***';
          }
        }
      }
    }

    return maskedHeaders;
  }

  /// Mask sensitive data in request/response
  dynamic _maskSensitiveData(dynamic data) {
    if (data is Map) {
      final maskedData = Map<String, dynamic>.from(data);

      const sensitiveKeys = [
        'password',
        'token',
        'access_token',
        'refresh_token',
        'secret',
        'key',
        'api_key',
      ];

      for (final key in sensitiveKeys) {
        if (maskedData.containsKey(key)) {
          final value = maskedData[key]?.toString() ?? '';
          if (value.isNotEmpty) {
            maskedData[key] = '***MASKED***';
          }
        }
      }

      return maskedData;
    }

    return data;
  }

  /// Reload settings and reinitialize logger
  Future<void> reloadSettings() async {
    await _initializeLoggerWithSettings();
  }

  /// Get current settings summary
  Future<String> getSettingsSummary() async {
    return await LogSettingsService.getSettingsSummary();
  }

  /// Check if logging system is initialized
  bool get isInitialized => _isInitialized;
}

/// Custom log filter to control what gets logged based on settings
class _CustomLogFilter extends LogFilter {
  final LogSettings? _settings;

  _CustomLogFilter(this._settings);

  @override
  bool shouldLog(LogEvent event) {
    // If no settings, use default behavior
    if (_settings == null) {
      // In production, only log warnings and errors
      if (kReleaseMode) {
        return event.level.index >= Level.warning.index;
      }
      // In debug mode, log everything
      return true;
    }

    // Check if logging is enabled
    if (!_settings.isEnabled) return false;

    // Check minimum level
    final minimumLevel = _convertToLoggerLevel(_settings.minimumLevel);
    return event.level.index >= minimumLevel.index;
  }

  Level _convertToLoggerLevel(log_models.LogLevel level) {
    switch (level) {
      case log_models.LogLevel.verbose:
        return Level.trace;
      case log_models.LogLevel.debug:
        return Level.debug;
      case log_models.LogLevel.info:
        return Level.info;
      case log_models.LogLevel.warning:
        return Level.warning;
      case log_models.LogLevel.error:
        return Level.error;
    }
  }
}

/// Custom output to handle both console and database logging
class _CustomOutput extends LogOutput {
  final LogSettings _settings;

  _CustomOutput(this._settings);

  @override
  void output(OutputEvent event) {
    // Only output to console if console logging is enabled
    if (_settings.isConsoleLoggingEnabled) {
      // Use default console output
      ConsoleOutput().output(event);
    }
  }
}

/// Log levels for consistent usage


/// Global logger instance for easy access
final appLogger = AppLogger();
