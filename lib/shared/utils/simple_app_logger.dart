import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import '../../features/account/models/log_entry.dart' show LogLevel;
import 'app_logger_interface.dart';

/// Simple App Logger - Đơn giản hóa version của AppLogger
/// Chỉ sử dụng Logger để ghi log console, không tích hợp với database
/// Phù hợp cho development và testing
/// Sử dụng get_it service locator pattern
class SimpleAppLogger implements IAppLogger {
  SimpleAppLogger() {
    _initializeLogger();
  }

  late final Logger _logger;
  bool _isInitialized = false;

  /// Initialize the logger with simple configuration
  void _initializeLogger() {
    if (_isInitialized) return;

    try {
      _logger = Logger(
        filter: _SimpleLogFilter(),
        printer: PrettyPrinter(
          methodCount: 2,
          errorMethodCount: 8,
          lineLength: 120,
          colors: true,
          printEmojis: true,
          dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
        ),
        output: ConsoleOutput(),
      );

      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('SimpleAppLogger initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing SimpleAppLogger: $e');
      }
    }
  }

  /// Initialize the logger (kept for interface compatibility)
  @override
  Future<void> initialize() async {
    // Logger is already initialized in constructor
    return;
  }

  /// Get logger instance
  @override
  Logger get logger => _logger;

  /// Log info message
  @override
  Future<void> i(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.info, message, error, stackTrace);
  }

  /// Log debug message
  @override
  Future<void> d(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.debug, message, error, stackTrace);
  }

  /// Log warning message
  @override
  Future<void> w(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.warning, message, error, stackTrace);
  }

  /// Log error message
  @override
  Future<void> e(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.error, message, error, stackTrace);
  }

  /// Log verbose message
  @override
  Future<void> v(
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    await _log(LogLevel.verbose, message, error, stackTrace);
  }

  /// Internal logging method
  Future<void> _log(
    LogLevel level,
    String message, [
    dynamic error,
    StackTrace? stackTrace,
  ]) async {
    try {
      _logger.log(
        _convertToLoggerLevel(level),
        message,
        error: error,
        stackTrace: stackTrace,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error in SimpleAppLogger._log: $e');
      }
    }
  }

  /// Log with custom tag
  @override
  Future<void> logWithTag(
    String tag,
    String message, [
    LogLevel level = LogLevel.info,
  ]) async {
    await _logWithContext(level, message, tag: tag);
  }

  /// Log with feature context
  @override
  Future<void> logWithFeature(
    String feature,
    String message, [
    LogLevel level = LogLevel.info,
  ]) async {
    await _logWithContext(level, message, feature: feature);
  }

  /// Log with context
  Future<void> _logWithContext(
    LogLevel level,
    String message, {
    String? tag,
    String? feature,
  }) async {
    try {
      final taggedMessage = _buildTaggedMessage(
        message,
        tag: tag,
        feature: feature,
      );

      _logger.log(_convertToLoggerLevel(level), taggedMessage);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error in SimpleAppLogger._logWithContext: $e');
      }
    }
  }

  /// Build tagged message
  String _buildTaggedMessage(String message, {String? tag, String? feature}) {
    final parts = <String>[];

    if (feature != null) {
      parts.add('[$feature]');
    }

    if (tag != null) {
      parts.add('[$tag]');
    }

    if (parts.isNotEmpty) {
      return '${parts.join(' ')} $message';
    }

    return message;
  }

  /// Log API request
  Future<void> logApiRequest(
    String method,
    String url, {
    Map<String, dynamic>? headers,
    dynamic data,
  }) async {
    if (!kDebugMode) return;

    await i('🚀 $method $url');
    if (headers != null) {
      await d('📝 Headers: ${_maskSensitiveHeaders(headers)}');
    }
    if (data != null) {
      await d('📦 Data: ${_maskSensitiveData(data)}');
    }
  }

  /// Log API response
  Future<void> logApiResponse(
    int statusCode,
    String url, {
    dynamic data,
  }) async {
    if (!kDebugMode) return;

    if (statusCode >= 200 && statusCode < 300) {
      await i('✅ $statusCode $url');
    } else {
      await e('❌ $statusCode $url');
    }

    if (data != null) {
      await d('📄 Response: ${_maskSensitiveData(data)}');
    }
  }

  /// Log API error
  Future<void> logApiError(
    String method,
    String url,
    dynamic error, {
    dynamic responseData,
  }) async {
    if (!kDebugMode) return;

    await e('❌ $method $url');
    await e('🔍 Error: $error');

    if (responseData != null) {
      await e('📄 Error Data: ${_maskSensitiveData(responseData)}');
    }
  }

  /// Log feature initialization
  Future<void> logFeatureInit(String featureName) async {
    await logWithFeature(featureName, '🚀 Initializing $featureName');
  }

  /// Log feature success
  Future<void> logFeatureSuccess(String featureName, [String? details]) async {
    final message =
        '✅ $featureName initialized successfully${details != null ? ': $details' : ''}';
    await logWithFeature(featureName, message);
  }

  /// Log feature error
  Future<void> logFeatureError(String featureName, dynamic error) async {
    await logWithFeature(
      featureName,
      '❌ $featureName initialization failed: $error',
      LogLevel.error,
    );
  }

  /// Log user action
  Future<void> logUserAction(
    String action, {
    Map<String, dynamic>? parameters,
  }) async {
    if (!kDebugMode) return;

    final params = parameters != null ? ' with params: $parameters' : '';
    await logWithTag('USER_ACTION', '👤 User action: $action$params');
  }

  /// Log navigation
  Future<void> logNavigation(
    String from,
    String to, {
    Map<String, dynamic>? arguments,
  }) async {
    if (!kDebugMode) return;

    final args = arguments != null ? ' with args: $arguments' : '';
    await logWithTag('NAVIGATION', '🧭 Navigation: $from → $to$args');
  }

  /// Log database operation
  Future<void> logDatabaseOp(
    String operation,
    String table, {
    dynamic data,
  }) async {
    if (!kDebugMode) return;

    await logWithTag('DATABASE', '💾 Database $operation on $table');
    if (data != null) {
      await d('📊 Data: $data');
    }
  }

  /// Log cache operation
  Future<void> logCacheOp(String operation, String key, {dynamic data}) async {
    if (!kDebugMode) return;

    await logWithTag('CACHE', '💿 Cache $operation: $key');
    if (data != null) {
      await d('📊 Data: $data');
    }
  }

  /// Log performance metric
  Future<void> logPerformance(String operation, Duration duration) async {
    if (!kDebugMode) return;

    final ms = duration.inMilliseconds;
    String message;
    LogLevel level;

    if (ms < 100) {
      message = '⚡ $operation: ${ms}ms';
      level = LogLevel.debug;
    } else if (ms < 1000) {
      message = '🐌 $operation: ${ms}ms';
      level = LogLevel.warning;
    } else {
      message = '🐌🐌 $operation: ${ms}ms (slow!)';
      level = LogLevel.error;
    }

    await logWithTag('PERFORMANCE', message, level);
  }

  /// Convert LogLevel to logger Level
  Level _convertToLoggerLevel(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return Level.trace;
      case LogLevel.debug:
        return Level.debug;
      case LogLevel.info:
        return Level.info;
      case LogLevel.warning:
        return Level.warning;
      case LogLevel.error:
        return Level.error;
    }
  }

  /// Mask sensitive data in headers
  Map<String, dynamic> _maskSensitiveHeaders(Map<String, dynamic> headers) {
    final maskedHeaders = Map<String, dynamic>.from(headers);

    const sensitiveHeaderKeys = [
      'authorization',
      'Authorization',
      'AUTHORIZATION',
      'x-api-key',
      'X-API-Key',
      'x-auth-token',
      'X-Auth-Token',
    ];

    for (final key in sensitiveHeaderKeys) {
      if (maskedHeaders.containsKey(key)) {
        final value = maskedHeaders[key]?.toString() ?? '';
        if (value.isNotEmpty) {
          if (value.length > 20) {
            maskedHeaders[key] =
                '${value.substring(0, 10)}***...***${value.substring(value.length - 4)}';
          } else {
            maskedHeaders[key] = '***MASKED***';
          }
        }
      }
    }

    return maskedHeaders;
  }

  /// Mask sensitive data in request/response
  dynamic _maskSensitiveData(dynamic data) {
    if (data is Map) {
      final maskedData = Map<String, dynamic>.from(data);

      const sensitiveKeys = [
        'password',
        'token',
        'access_token',
        'refresh_token',
        'secret',
        'key',
        'api_key',
      ];

      for (final key in sensitiveKeys) {
        if (maskedData.containsKey(key)) {
          final value = maskedData[key]?.toString() ?? '';
          if (value.isNotEmpty) {
            maskedData[key] = '***MASKED***';
          }
        }
      }

      return maskedData;
    }

    return data;
  }

  /// Check if logging system is initialized
  bool get isInitialized => _isInitialized;
}

/// Simple log filter for SimpleAppLogger
class _SimpleLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // In production, only log warnings and errors
    if (kReleaseMode) {
      return event.level.index >= Level.warning.index;
    }
    // In debug mode, log everything
    return true;
  }
}

// Global simple logger instance removed - use get_it service locator instead
