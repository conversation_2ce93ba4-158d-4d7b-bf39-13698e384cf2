# App Logger Utilities

## SimpleAppLogger

`SimpleAppLogger` là một implementation đơn giản của `IAppLogger` interface, chỉ sử dụng `Logger` package để ghi log console mà không cần tích hợp với database.

### Đặc điểm

- ✅ Implements đầy đủ `IAppLogger` interface
- ✅ Sử dụng `Logger` package với PrettyPrinter
- ✅ Không tích hợp với database (không cần LogManagementService)
- ✅ Phù hợp cho development và testing
- ✅ Hỗ trợ đầy đủ các log levels: verbose, debug, info, warning, error
- ✅ Hỗ trợ tagging và feature context
- ✅ Tự động mask sensitive data
- ✅ Singleton pattern

### Cách sử dụng

#### 1. Import và sử dụng service locator

```dart
import 'package:your_app/shared/services/service_locator.dart';

// Sử dụng service locator
final logger = getIt.get<SimpleAppLogger>();
// Hoặc sử dụng extension method
final logger = getIt.simpleLogger;
// Hoặc sử dụng interface
final logger = getIt.logger;
```

#### 2. Service locator đã được setup tự động

```dart
// Service locator được setup trong AppModules.initialize()
// Không cần khởi tạo thủ công
```

#### 3. Ghi log cơ bản

```dart
// Log info
await logger.i('This is an info message');

// Log debug
await logger.d('This is a debug message');

// Log warning
await logger.w('This is a warning message');

// Log error
await logger.e('This is an error message', error, stackTrace);

// Log verbose
await logger.v('This is a verbose message');
```

#### 4. Ghi log với tag và feature

```dart
// Log với tag
await logger.logWithTag('API', 'Making API request');

// Log với feature
await logger.logWithFeature('auth', 'User login attempt');

// Log với cả tag và feature
await logger.logWithTag('API', 'Request completed');
await logger.logWithFeature('auth', 'Login successful');
```

#### 5. Ghi log API

```dart
// Log API request
await logger.logApiRequest(
  'POST',
  '/api/v1/login',
  headers: {'Content-Type': 'application/json'},
  data: {'email': '<EMAIL>'},
);

// Log API response
await logger.logApiResponse(
  200,
  '/api/v1/login',
  data: {'token': '***MASKED***'},
);

// Log API error
await logger.logApiError(
  'POST',
  '/api/v1/login',
  'Network error',
  responseData: {'error': 'Connection timeout'},
);
```

#### 6. Ghi log feature

```dart
// Log feature initialization
await logger.logFeatureInit('Authentication');

// Log feature success
await logger.logFeatureSuccess('Authentication', 'OAuth2 configured');

// Log feature error
await logger.logFeatureError('Authentication', 'OAuth2 configuration failed');
```

#### 7. Ghi log user actions và navigation

```dart
// Log user action
await logger.logUserAction(
  'login',
  parameters: {'method': 'email', 'provider': 'google'},
);

// Log navigation
await logger.logNavigation(
  'LoginScreen',
  'HomeScreen',
  arguments: {'userId': '123'},
);
```

#### 8. Ghi log database và cache

```dart
// Log database operation
await logger.logDatabaseOp(
  'INSERT',
  'users',
  data: {'id': 1, 'name': 'John'},
);

// Log cache operation
await logger.logCacheOp(
  'SET',
  'user:123',
  data: {'name': 'John', 'email': '<EMAIL>'},
);
```

#### 9. Ghi log performance

```dart
// Log performance metric
final stopwatch = Stopwatch()..start();
// ... perform operation
stopwatch.stop();

await logger.logPerformance('Database query', stopwatch.elapsed);
```

### So sánh với AppLogger

| Tính năng | SimpleAppLogger | AppLogger |
|-----------|----------------|-----------|
| Database logging | ❌ | ✅ |
| Settings management | ❌ | ✅ |
| Console logging | ✅ | ✅ |
| API logging | ✅ | ✅ |
| Feature logging | ✅ | ✅ |
| Performance logging | ✅ | ✅ |
| Sensitive data masking | ✅ | ✅ |
| Complexity | Đơn giản | Phức tạp |
| Dependencies | Ít | Nhiều |
| Phù hợp cho | Development, Testing | Production |

### Lưu ý

1. **Auto-initialization**: `SimpleAppLogger` sẽ tự động khởi tạo khi gọi log method đầu tiên nếu chưa được khởi tạo.

2. **Debug mode**: Một số log methods chỉ hoạt động trong debug mode (kDebugMode = true).

3. **Sensitive data masking**: Tự động mask các sensitive data như password, token, etc.

4. **Production behavior**: Trong production mode, chỉ log warnings và errors.

5. **Thread safety**: Sử dụng singleton pattern, thread-safe.

### Ví dụ hoàn chỉnh

```dart
import 'package:flutter/material.dart';
import 'package:your_app/shared/services/service_locator.dart';

class MyService {
  final _logger = getIt.get<SimpleAppLogger>();

  Future<void> initialize() async {
    await _logger.logFeatureInit('MyService');
    
    try {
      // Perform initialization
      await _logger.logFeatureSuccess('MyService', 'Initialized successfully');
    } catch (e) {
      await _logger.logFeatureError('MyService', e);
      rethrow;
    }
  }

  Future<void> performOperation() async {
    await _logger.logWithTag('OPERATION', 'Starting operation');
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Perform operation
      await Future.delayed(Duration(milliseconds: 500));
      
      stopwatch.stop();
      await _logger.logPerformance('Operation', stopwatch.elapsed);
      await _logger.logWithTag('OPERATION', 'Operation completed successfully');
    } catch (e) {
      await _logger.e('Operation failed', e);
      rethrow;
    }
  }
}
``` 