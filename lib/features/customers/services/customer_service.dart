import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/api/api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/models/index.dart';
import '../models/customer_model.dart';
import '../models/create_customer_request.dart';
import '../models/update_customer_request.dart';
import '../models/customer_list_request.dart';
import '../models/customer_list_data.dart';
import '../models/customer_summary_request.dart';
import '../models/customer_summary_data.dart';

/// Service để xử lý customer operations
class CustomerService {
  static final CustomerService _instance = CustomerService._internal();
  factory CustomerService() => _instance;
  CustomerService._internal();

  IAppLogger get _logger => getIt.get<IAppLogger>();
  IApiService get _apiService => getIt.get<IApiService>();

  // API endpoints
  static const String _getCustomersEndpoint = '/rest/rpc/get_customers';
  static const String _getCustomerDetailEndpoint = '/rest/rpc/get_detail_customer';
  static const String _getCustomerSummaryEndpoint = '/rest/rpc/get_customer_summary';
  static const String _createCustomerEndpoint = '/kilobabiz-api/api/v1/customers';
  static const String _updateCustomerEndpoint = '/kilobabiz-api/api/v1/customers';

  /// Tạo customer mới
  Future<bool> createCustomer(CreateCustomerRequest request) async {
    try {
      _logger.i('Creating new customer: ${request.fullName}');

      // Validate request
      final validationErrors = _validateCreateRequest(request);
      if (validationErrors.isNotEmpty) {
        throw CustomerException(
          message: 'Dữ liệu không hợp lệ: ${validationErrors.join(', ')}',
          type: CustomerExceptionType.validationError,
        );
      }

      // Gọi API thật để tạo khách hàng
      final response = await _apiService.post(
        _createCustomerEndpoint,
        data: request.toJson(),
      );

      _logger.i('API response received: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        _logger.i('Customer created successfully');
        
        // Parse response data để kiểm tra success
        final responseData = response.data;
        if (responseData is Map<String, dynamic>) {
          final success = responseData['success'] as bool?;
          if (success == true) {
            return true;
          } else {
            throw CustomerException(
              message: 'API trả về success: false',
              type: CustomerExceptionType.apiError,
            );
          }
        }
        
        // Nếu không parse được response, throw exception
        throw CustomerException(
          message: 'Không thể parse response từ API',
          type: CustomerExceptionType.invalidResponse,
        );
      } else {
        throw CustomerException(
          message: 'API trả về status code: ${response.statusCode}',
          type: CustomerExceptionType.apiError,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error creating customer: ${e.message}');
      throw CustomerException(
        message: 'Không thể tạo khách hàng: ${e.message}',
        type: CustomerExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unexpected error creating customer: $e');
      throw CustomerException(
        message: 'Lỗi không xác định khi tạo khách hàng',
        type: CustomerExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Cập nhật khách hàng
  Future<void> updateCustomer(String id, UpdateCustomerRequest request) async {
    try {
      _logger.i('Updating customer: $id with data: ${request.toJson()}');

      // Validate request
      final validationErrors = _validateUpdateRequest(request);
      if (validationErrors.isNotEmpty) {
        throw CustomerException(
          message: 'Dữ liệu không hợp lệ: ${validationErrors.join(', ')}',
          type: CustomerExceptionType.validationError,
        );
      }

      // Gọi API thật để cập nhật khách hàng
      final response = await _apiService.put(
        '$_updateCustomerEndpoint/$id',
        data: request.toJson(),
      );

      _logger.i('API response received: ${response.statusCode}');

      if (response.statusCode == 200) {
        _logger.i('Customer updated successfully');
        // Không cần parse response, chỉ cần biết API call thành công
      } else {
        throw CustomerException(
          message: 'API trả về status code: ${response.statusCode}',
          type: CustomerExceptionType.apiError,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error updating customer: ${e.message}');
      throw CustomerException(
        message: 'Không thể cập nhật khách hàng: ${e.message}',
        type: CustomerExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unexpected error updating customer: $e');
      throw CustomerException(
        message: 'Lỗi không xác định khi cập nhật khách hàng',
        type: CustomerExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách khách hàng
  Future<BaseResponse<CustomerListData>> getCustomers(CustomerListRequest request) async {
    try {
      _logger.i('Getting customers list with params: ${request.toJson()}');

      // Gọi API thật
      final response = await _apiService.post(
        _getCustomersEndpoint,
        data: request.toJson(),
      );

      _logger.i('API response received: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic>) {
          // Sử dụng BaseResponse.fromJson với custom fromJsonT function
          return BaseResponse.fromJson(
            responseData,
            (data) => _parseCustomerListData(data, request),
          );
        } else {
          throw CustomerException(
            message: 'Response data không đúng định dạng',
            type: CustomerExceptionType.invalidResponse,
          );
        }
      } else {
        throw CustomerException(
          message: 'HTTP error: ${response.statusCode}',
          type: CustomerExceptionType.apiError,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error getting customers: ${e.message}');
      throw CustomerException(
        message: 'Không thể lấy danh sách khách hàng: ${e.message}',
        type: CustomerExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unexpected error getting customers: $e');
      throw CustomerException(
        message: 'Lỗi không xác định khi lấy danh sách khách hàng',
        type: CustomerExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Helper function để parse CustomerListData từ API response data
  CustomerListData _parseCustomerListData(Object? data, CustomerListRequest request) {
    if (data == null) {
      return CustomerListData(
        customers: [],
        totalCount: 0,
        limit: request.pLimit,
        offset: request.pOffset,
      );
    }

    if (data is! Map<String, dynamic>) {
      throw CustomerException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: CustomerExceptionType.invalidResponse,
      );
    }

    final dataMap = data;
    
    // Parse customers using existing logic
    final customers = _parseCustomersFromDataMap(dataMap);
    
    // Get pagination info từ data
    final totalCount = dataMap['total_count'] ?? customers.length;
    final limit = request.pLimit;
    final offset = request.pOffset;
    
    return CustomerListData(
      customers: customers,
      totalCount: totalCount,
      limit: limit,
      offset: offset,
    );
  }

  /// Helper function để parse customers từ data map 
  List<CustomerModel> _parseCustomersFromDataMap(Map<String, dynamic> dataMap) {
    try {
      final customersData = dataMap['customers'] as List<dynamic>?;
      if (customersData == null) {
        _logger.w('No customers field in data');
        return [];
      }

      return customersData.map((customerData) {
        try {
          // Transform customer data để handle null values trước khi parse
          final transformedData = _transformCustomerDataForFromJson(customerData as Map<String, dynamic>);
          return CustomerModel.fromJson(transformedData);
        } catch (e) {
          _logger.e('Error parsing customer data: $e');
          return null;
        }
      }).whereType<CustomerModel>().toList();
    } catch (e) {
      _logger.e('Error parsing customers from data map: $e');
      return [];
    }
  }

  /// Lấy chi tiết khách hàng
  Future<BaseResponse<CustomerModel>> getCustomerDetail(String customerId) async {
    try {
      _logger.i('Getting customer detail for ID: $customerId');

      // Gọi API thật
      final response = await _apiService.post(
        _getCustomerDetailEndpoint,
        data: {'p_customer_id': customerId},
      );

      _logger.i('API response received: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic>) {
          // Sử dụng BaseResponse.fromJson với custom fromJsonT function
          return BaseResponse.fromJson(
            responseData,
            (data) => _parseCustomerDetailFromData(data),
          );
        } else {
          throw CustomerException(
            message: 'Response data không đúng định dạng',
            type: CustomerExceptionType.invalidResponse,
          );
        }
      } else {
        throw CustomerException(
          message: 'HTTP error: ${response.statusCode}',
          type: CustomerExceptionType.apiError,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error getting customer detail: ${e.message}');
      throw CustomerException(
        message: 'Không thể lấy chi tiết khách hàng: ${e.message}',
        type: CustomerExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unexpected error getting customer detail: $e');
      throw CustomerException(
        message: 'Lỗi không xác định khi lấy chi tiết khách hàng',
        type: CustomerExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy customer summary
  Future<BaseResponse<CustomerSummaryData>> getCustomerSummary(CustomerSummaryRequest request) async {
    try {
      _logger.i('Getting customer summary with params: ${request.toJson()}');

      // Gọi API thật
      final response = await _apiService.post(
        _getCustomerSummaryEndpoint,
        data: request.toJson(),
      );

      _logger.i('API response received: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic>) {
          // Sử dụng BaseResponse.fromJson với custom fromJsonT function
          return BaseResponse.fromJson(
            responseData,
            (data) => _parseCustomerSummaryFromData(data),
          );
        } else {
          throw CustomerException(
            message: 'Response data không đúng định dạng',
            type: CustomerExceptionType.invalidResponse,
          );
        }
      } else {
        throw CustomerException(
          message: 'HTTP error: ${response.statusCode}',
          type: CustomerExceptionType.apiError,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error getting customer summary: ${e.message}');
      throw CustomerException(
        message: 'Không thể lấy tổng quan khách hàng: ${e.message}',
        type: CustomerExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unexpected error getting customer summary: $e');
      throw CustomerException(
        message: 'Lỗi không xác định khi lấy tổng quan khách hàng',
        type: CustomerExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Helper function để parse CustomerSummaryData từ API response data
  CustomerSummaryData _parseCustomerSummaryFromData(Object? data) {
    if (data == null) {
      // Trả về empty data thay vì throw exception
      _logger.w('No data in customer summary response, returning empty data');
      return CustomerSummaryData.empty;
    }

    if (data is! Map<String, dynamic>) {
      throw CustomerException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: CustomerExceptionType.invalidResponse,
      );
    }

    try {
      return CustomerSummaryData.fromJson(data);
    } catch (e) {
      _logger.e('Error parsing customer summary: $e');
      throw CustomerException(
        message: 'Lỗi parse dữ liệu tổng quan khách hàng: $e',
        type: CustomerExceptionType.invalidResponse,
      );
    }
  }

  /// Helper function để parse CustomerModel từ API response data
  CustomerModel _parseCustomerDetailFromData(Object? data) {
    if (data == null) {
      throw CustomerException(
        message: 'Không có dữ liệu khách hàng',
        type: CustomerExceptionType.invalidResponse,
      );
    }

    if (data is! Map<String, dynamic>) {
      throw CustomerException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: CustomerExceptionType.invalidResponse,
      );
    }

    final dataMap = data;
    final customerData = dataMap['customer'] as Map<String, dynamic>?;
    
    if (customerData == null) {
      throw CustomerException(
        message: 'Không có field customer trong data',
        type: CustomerExceptionType.invalidResponse,
      );
    }

    try {
      // Transform API response data để khớp với CustomerModel.fromJson
      final transformedData = _transformCustomerDataForFromJson(customerData);
      return CustomerModel.fromJson(transformedData);
    } catch (e) {
      _logger.e('Error parsing customer detail: $e');
      throw CustomerException(
        message: 'Lỗi parse dữ liệu khách hàng: $e',
        type: CustomerExceptionType.invalidResponse,
      );
    }
  }

  /// Validate create customer request (used for both create and update)
  List<String> _validateCreateRequest(CreateCustomerRequest request) {
    final errors = <String>[];

    // 1. Họ và tên (Required) - có
    if (request.fullName.isEmpty) {
      errors.add('Họ tên không được để trống');
    } else if (request.fullName.length < 2) {
      errors.add('Họ tên phải có ít nhất 2 ký tự');
    }

    if (request.phoneNumber != null && request.phoneNumber!.isNotEmpty) {
      if (!isValidVietnamesePhone(request.phoneNumber!)) {
        errors.add('Số điện thoại không đúng định dạng');
      }
    }

    if (request.email != null && request.email!.isNotEmpty) {
      if (!isValidEmail(request.email!)) {
        errors.add('Email không đúng định dạng');
      }
    }

    // 5. Số GTTT (Not Required) - Không
    if (request.idNo != null && request.idNo!.isNotEmpty) {
      if (!isValidIdCardNumber(request.idNo!)) {
        errors.add('Số CMND/CCCD không đúng định dạng (12 chữ số)');
      }
    }

    // 6. Địa chỉ thường trú (Not Required) - Không
    // No validation needed according to image

    // 7. Tỉnh/thành phố (Not Required) - Không
    // No validation needed according to image

    // 8. Nghề nghiệp (Required) - Có
    if (request.occupation?.isEmpty ?? true) {
      errors.add('Nghề nghiệp không được để trống');
    }

    // 9. Nguồn khách hàng (Required) - Có
    if (request.source?.isEmpty ?? true) {
      errors.add('Nguồn khách hàng không được để trống');
    }

    return errors;
  }

  /// Validate update customer request
  List<String> _validateUpdateRequest(UpdateCustomerRequest request) {
    final errors = <String>[];

    // 1. Họ và tên (Required)
    if (request.fullName.isEmpty) {
      errors.add('Họ tên không được để trống');
    } else if (request.fullName.length < 2) {
      errors.add('Họ tên phải có ít nhất 2 ký tự');
    }

    // 2. Số điện thoại (Optional but validate if provided)
    if (request.phoneNumber != null && request.phoneNumber!.isNotEmpty) {
      if (!isValidVietnamesePhone(request.phoneNumber!)) {
        errors.add('Số điện thoại không đúng định dạng');
      }
    }

    // 3. Email (Optional but validate if provided)
    if (request.email != null && request.email!.isNotEmpty) {
      if (!isValidEmail(request.email!)) {
        errors.add('Email không đúng định dạng');
      }
    }

    // 4. Số GTTT (Optional but validate if provided)
    if (request.idNo != null && request.idNo!.isNotEmpty) {
      if (!isValidIdCardNumber(request.idNo!)) {
        errors.add('Số CMND/CCCD không đúng định dạng (12 chữ số)');
      }
    }

    // 5. Nghề nghiệp (Required)
    if (request.occupation?.isEmpty ?? true) {
      errors.add('Nghề nghiệp không được để trống');
    }

    // 6. Nguồn khách hàng (Required)
    if (request.source?.isEmpty ?? true) {
      errors.add('Nguồn khách hàng không được để trống');
    }

    // 7. Trạng thái (Required)
    if (request.status?.isEmpty ?? true) {
      errors.add('Trạng thái khách hàng không được để trống');
    }

    return errors;
  }



  /// Transform API response data để khớp với CustomerModel.fromJson
  Map<String, dynamic> _transformCustomerDataForFromJson(Map<String, dynamic> customerData) {
    final transformed = <String, dynamic>{};
    
    // Copy các field cơ bản
    transformed['id'] = customerData['id'] ?? '';
    transformed['full_name'] = customerData['full_name'] ?? '';
    transformed['phone_number'] = customerData['phone_number']??customerData['phone'];
    transformed['email'] = customerData['email'];
    // Transform sex -> gender
    if (customerData['sex'] != null) {
      transformed['gender'] = {
        'code': customerData['sex']['code'] ?? '',
        'label': customerData['sex']['label'] ?? '',
        'value': customerData['sex']['value'] ?? '',
      };
    }
    
    // Transform dob -> birth_date
    if (customerData['dob'] != null) {
      transformed['birth_date'] = customerData['dob'];
    }
    
    // Transform id fields
    transformed['id_card_number'] = customerData['id_card_no'];
    transformed['id_card_type'] = customerData['id_type'];
    
    // Transform address fields
    transformed['permanent_address'] = customerData['permanent_address'];
    transformed['current_address'] = customerData['current_address'];
    
    // Transform province
    if (customerData['province'] != null) {
      transformed['province'] = {
        'id': customerData['province']['id'] ?? '',
        'gso_code': customerData['province']['gso_code'] ?? '',
        'name': customerData['province']['name'] ?? '',
      };
    }
    
    // Transform ward
    if (customerData['ward'] != null) {
      transformed['ward'] = {
        'id': customerData['ward']['id'] ?? '',
        'code': customerData['ward']['id'] ?? '',
        'name': customerData['ward']['name'] ?? '',
      };
    }
    
    // Transform career fields
    transformed['occupation'] = customerData['occupation'];
    transformed['workplace'] = customerData['workplace'];
    // Transform monthly_income - có thể là number hoặc string
    if (customerData['monthly_income'] != null) {
      transformed['monthly_income'] = customerData['monthly_income'].toString();
    }
    
    // Transform work_experience
    if (customerData['work_experience'] != null) {
      // work_experience có thể là string ID hoặc object
      if (customerData['work_experience'] is String) {
        // Nếu là string ID, tạo object với code = ID
        transformed['work_experience'] = {
          'code': customerData['work_experience'],
          'label': customerData['work_experience'],
          'value': customerData['work_experience'],
        };
      } else if (customerData['work_experience'] is Map<String, dynamic>) {
        // Nếu là object, copy trực tiếp
        transformed['work_experience'] = customerData['work_experience'];
      }
    }
    // Transform status
    if (customerData['status'] != null) {
      transformed['status'] = {
        'code': customerData['status']['code'] ?? '',
        'label': customerData['status']['label'] ?? '',
        'value': customerData['status']['value'] ?? '',
      };
    }
    
    // Transform other fields
    transformed['cif_number'] = customerData['cif_no'];
    
    // Transform source
    if (customerData['source'] != null) {
      // source có thể là string ID hoặc object
      if (customerData['source'] is String) {
        // Nếu là string ID, tạo object với code = ID
        transformed['source'] = {
          'code': customerData['source'],
          'label': customerData['source'],
          'value': customerData['source'],
        };
      } else if (customerData['source'] is Map<String, dynamic>) {
        // Nếu là object, copy trực tiếp
        transformed['source'] = customerData['source'];
      }
    }
    
    // Transform tags
    if (customerData['tags'] != null) {
      transformed['tags'] = (customerData['tags'] as List<dynamic>).map((tag) {
        return {
          'id': tag['id'] ?? '',
          'name': tag['name'] ?? '',
          'color': tag['color'],
        };
      }).toList();
    }
    
    // Transform timestamps
    transformed['created_at'] = customerData['created_at'];
    transformed['updated_at'] = customerData['updated_at'];
    
    // Transform assigned_manager - Handle null values properly
    if (customerData['assigned_manager'] != null) {
      final assignedManager = customerData['assigned_manager'] as Map<String, dynamic>;
      transformed['assigned_manager'] = {
        'id': assignedManager['id'], // nullable
        'full_name': assignedManager['full_name'], // nullable 
        'cif_no': assignedManager['cif_no'], // nullable
      };
    }
    
    // Transform branch - Handle null values properly for required fields
    if (customerData['branch'] != null) {
      final branch = customerData['branch'] as Map<String, dynamic>;
      transformed['branch'] = {
        'id': branch['id'] ?? '', // default empty string for required field
        'code': branch['code'] ?? '', // default empty string for required field
        'name': branch['name'] ?? '', // default empty string for required field
        'address': branch['address'], // nullable
        'province_id': branch['province_id'] ?? '', // default empty string for required field
        'province_name': branch['province_name'], // nullable
        'region_id': branch['region_id'], // nullable
        'region_code': branch['region_code'], // nullable
        'region_name': branch['region_name'], // nullable
      };
    }
    
    return transformed;
  }



  /// Validate Vietnamese phone number
  bool isValidVietnamesePhone(String phone) {
    // Remove all non-digit characters first
    final digits = phone.replaceAll(RegExp(r'[^\d]'), '');

    // Check if it's a valid Vietnamese phone number
    if (digits.startsWith('84') && digits.length == 11) {
      return true; // +84xxxxxxxxx format
    } else if (digits.startsWith('0') && digits.length == 11) {
      return true; // 0xxxxxxxxxx format (11 digits including leading 0)
    } else if (digits.startsWith('0') && digits.length == 10) {
      return true; // 0xxxxxxxxx format (10 digits including leading 0)
    } else if (digits.length == 9) {
      return true; // xxxxxxxxx format (auto-add 0)
    } else if (digits.length == 10 && !digits.startsWith('0')) {
      return true; // xxxxxxxxxx format (might be valid)
    }

    return false;
  }

  /// Validate email format
  bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[\w+\-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// Validate ID card number format
  bool isValidIdCardNumber(String idCardNo) {
    // Vietnamese ID card format: 12 digits
    final idCardRegex = RegExp(r'^[0-9]{12}$');
    return idCardRegex.hasMatch(idCardNo);
  }

  /// Format phone number cho display
  String formatPhoneNumber(String phone) {
    if (phone.startsWith('+84')) {
      return '0${phone.substring(3)}';
    }
    return phone;
  }

  /// Format ID card number cho display
  String formatIdCardNumber(String idCardNo) {
    if (idCardNo.length == 12) {
      return '${idCardNo.substring(0, 3)} ${idCardNo.substring(3, 6)} ${idCardNo.substring(6, 9)} ${idCardNo.substring(9)}';
    }
    return idCardNo;
  }

  /// Convert form data to UpdateCustomerRequest
  UpdateCustomerRequest convertFormDataToUpdateRequest(Map<String, dynamic> formData) {
    return UpdateCustomerRequest(
      fullName: (formData['name']?.toString()??'').isNotEmpty? (formData['name']?.toString() ?? '') : '',
      phoneNumber: (formData['phone']?.toString()??'').isNotEmpty? formData['phone']?.toString() : null, 
      status: (formData['status']?.toString()??'').isNotEmpty? formData['status']?.toString() : null,
      source: (formData['source']?.toString()??'').isNotEmpty? formData['source']?.toString() : null,
      occupation: (formData['occupation']?.toString()??'').isNotEmpty? formData['occupation']?.toString() : null,
      idType: (formData['idType']?.toString()??'').isNotEmpty? formData['idType']?.toString() : null,
      idNo: (formData['idNumber']?.toString().trim().isEmpty ?? true) ? null : formData['idNumber']?.toString().trim(),
      cifNo: (formData['cifNumber']?.toString().trim().isEmpty ?? true) ? null : formData['cifNumber']?.toString().trim(),
      sex: (formData['gender']?.toString()??'').isNotEmpty? formData['gender']?.toString() : null,
      dob: (formData['birthDate']?.toString()??'').isNotEmpty? formData['birthDate']?.toString() : null,
      email: (formData['email']?.toString().trim().isEmpty ?? true) ? null : formData['email']?.toString().trim(),
      provinceId: (formData['province']?.toString().trim().isEmpty ?? true) ? null : formData['province']?.toString().trim(),
      wardsId: (formData['ward']?.toString().trim().isEmpty ?? true) ? null : formData['ward']?.toString().trim(),
      permanentAddress: (formData['permanentAddress']?.toString()??'').isNotEmpty? formData['permanentAddress']?.toString() : null,
      currentAddress: (formData['currentAddress']?.toString().trim().isEmpty ?? true) ? null : formData['currentAddress']?.toString().trim(),
      workplace: (formData['workplace']?.toString().trim().isEmpty ?? true) ? null : formData['workplace']?.toString().trim(),
      monthlyIncome: (formData['monthlyIncome']?.toString()??'').isNotEmpty
          ? (formData['monthlyIncome']?.toString()) 
          : null,
      workExperience: (formData['workExperience']?.toString()??'').isNotEmpty
          ? (formData['workExperience']?.toString()) 
          : null,
      tagIds: formData['tags'] is List 
          ? (formData['tags'] as List).map((tag) => tag.toString()).toList()
          : <String>[],
    );
  }
}

/// Custom exception cho Customer service
class CustomerException implements Exception {
  final String message;
  final CustomerExceptionType type;
  final String? code;
  final Object? originalException;

  const CustomerException({
    required this.message,
    required this.type,
    this.code,
    this.originalException,
  });

  @override
  String toString() =>
      'CustomerException: $message (Type: $type${code != null ? ', Code: $code' : ''})';
}

/// Loại lỗi Customer
enum CustomerExceptionType {
  notFound,
  apiError,
  invalidResponse,
  validationError,
  unauthorized,
  unknown,
} 