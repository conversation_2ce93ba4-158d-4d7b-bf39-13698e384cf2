import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';
import '../blocs/customer_detail_bloc.dart';
import '../blocs/customer_detail_event.dart';
import '../blocs/customer_detail_state.dart';
import '../../transactions/screens/create_transaction_screen.dart';
import '../models/customer_model.dart';
import 'edit_customer_screen.dart';

class CustomerDetailScreen extends StatefulWidget {
  final String customerId;

  const CustomerDetailScreen({super.key, required this.customerId});

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _showTitle = false;
  CustomerModel? _cachedCustomer;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Show title when header is mostly collapsed (320 - 100 = 220px scrolled)
    final shouldShowTitle =
        _scrollController.hasClients && _scrollController.offset > 220;
    if (shouldShowTitle != _showTitle) {
      setState(() {
        _showTitle = shouldShowTitle;
      });
    }
  }

  // TODO: tối ưu sau
  void _makeCall(String? phoneNumber) async {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không có số điện thoại để gọi'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final uri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      // Thử launch với tel:// scheme và external application mode
      try {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        return; // Thành công, thoát hàm
      } catch (e) {
        debugPrint('Launch with tel:// failed: $e');
      }

      // Nếu tel:// thất bại, thử với tel: scheme
      try {
        final fallbackUri = Uri.parse('tel:$phoneNumber');
        await launchUrl(
          fallbackUri,
          mode: LaunchMode.externalApplication,
        );
        return; // Thành công, thoát hàm
      } catch (fallbackError) {
        // Ignore fallback error
      }

      // Nếu cả hai đều thất bại, thử với canLaunchUrl để debug
      try {
        final canLaunch = await canLaunchUrl(uri);

        if (canLaunch) {
          await launchUrl(uri);
          return; // Thành công, thoát hàm
        }
      } catch (checkError) {
        // Ignore check error
      }

      // Nếu tất cả đều thất bại, throw exception
      throw Exception(
        'Không thể mở ứng dụng gọi điện trên thiết bị này. Hãy thử gọi trực tiếp: $phoneNumber',
      );
    } catch (e) {
      if (!mounted) return;

      String errorMessage = 'Lỗi khi gọi điện';
      if (e.toString().contains('canLaunchUrl returned false')) {
        errorMessage =
            'Thiết bị này không hỗ trợ gọi điện. Hãy gọi trực tiếp: $phoneNumber';
      } else if (e.toString().contains('Không thể mở ứng dụng gọi điện')) {
        errorMessage =
            'Không thể mở ứng dụng gọi điện. Hãy gọi trực tiếp: $phoneNumber';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Copy số',
            textColor: Colors.white,
            onPressed: () {
              debugPrint('Copy phone number: $phoneNumber');
            },
          ),
        ),
      );
    }
  }

  // TODO: tối ưu sau
  void _sendMessage(String? phoneNumber) async {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Không có số điện thoại để nhắn tin'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Debug log
    debugPrint('Attempting to send SMS to: $phoneNumber');

    try {
      // Thử launch với sms:// scheme và external application mode
      try {
        final uri = Uri(scheme: 'sms', path: phoneNumber);
        debugPrint('SMS URI created: $uri');
        final result = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        debugPrint('Launch with sms:// successful: $result');
        return; // Thành công, thoát hàm
      } catch (e) {
        debugPrint('Launch with sms:// failed: $e');
      }

      // Nếu sms:// thất bại, thử với sms: scheme
      try {
        final fallbackUri = Uri.parse('sms:$phoneNumber');
        debugPrint('Trying SMS fallback URI: $fallbackUri');
        final result = await launchUrl(
          fallbackUri,
          mode: LaunchMode.externalApplication,
        );
        debugPrint('Fallback launch with sms: successful: $result');
        return; // Thành công, thoát hàm
      } catch (fallbackError) {
        debugPrint('Fallback launch with sms: failed: $fallbackError');
      }

      // Nếu cả hai đều thất bại, throw exception
      throw Exception(
        'Không thể mở ứng dụng tin nhắn trên thiết bị này. Hãy thử nhắn tin trực tiếp: $phoneNumber',
      );
    } catch (e) {
      debugPrint('Error in _sendMessage: $e');
      if (!mounted) return;

      String errorMessage = 'Lỗi khi nhắn tin';
      if (e.toString().contains('Không thể mở ứng dụng tin nhắn')) {
        errorMessage =
            'Không thể mở ứng dụng tin nhắn. Hãy nhắn tin trực tiếp: $phoneNumber';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: AppColors.error,
          duration: const Duration(seconds: 5),
          action: SnackBarAction(
            label: 'Copy số',
            textColor: Colors.white,
            onPressed: () {
              // TODO: Copy phone number to clipboard
              debugPrint('Copy phone number: $phoneNumber');
            },
          ),
        ),
      );
    }
  }

  void _createNewTransaction() {
    final customer = _cachedCustomer;
    if (customer != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => CreateTransactionScreen(
            preselectedCustomer: customer,
          ),
        ),
      );
    } else {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const CreateTransactionScreen()),
      );
    }
  }

  /// Refresh customer detail data - được gọi từ các tab
  Future<void> _onRefresh() async {
    context.read<CustomerDetailBloc>().add(
      CustomerDetailRefreshed(widget.customerId),
    );
  }

  /// Refresh cho tab cụ thể - được gọi từ các tab
  Future<void> _onTabRefresh(int tabIndex) async {
    switch (tabIndex) {
      case 0: // Tab "Thông tin"
        await _onRefresh();
        break;
      case 1: // Tab "Nhật ký"
        // TODO: Implement timeline refresh when bloc is ready
        await _onRefresh(); // Tạm thời refresh customer detail
        break;
      case 2: // Tab "Giao dịch"
        // TODO: Implement transactions refresh when bloc is ready
        await _onRefresh(); // Tạm thời refresh customer detail
        break;
      case 3: // Tab "Tags"
        // TODO: Implement tags refresh when bloc is ready
        await _onRefresh(); // Tạm thời refresh customer detail
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<CustomerDetailBloc>(
          create: (context) =>
              CustomerDetailBloc()
                ..add(CustomerDetailInitialized(widget.customerId)),
        ),
        // TODO: Add more blocs here for future features
        // BlocProvider<CustomerTimelineBloc>(
        //   create: (context) => CustomerTimelineBloc(),
        // ),
        // BlocProvider<CustomerTransactionsBloc>(
        //   create: (context) => CustomerTransactionsBloc(),
        // ),
        // BlocProvider<CustomerTagsBloc>(
        //   create: (context) => CustomerTagsBloc(),
        // ),
      ],
      child: Scaffold(
        body: _buildBody(),
        bottomNavigationBar: _buildBottomNavigationBar(),
      ),
    );
  }

  Widget _buildBody() {
    return BlocConsumer<CustomerDetailBloc, CustomerDetailState>(
      listener: (context, state) {
        // Handle side effects here if needed
        if (state is CustomerDetailFailure) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lỗi: ${state.error}'),
              backgroundColor: AppColors.error,
            ),
          );
        }

        // Cache customer data when success
        if (state is CustomerDetailSuccess) {
          _cachedCustomer = state.customer;
        }
      },
      buildWhen: (previous, current) {
        // Tránh rebuild liên tục khi loading
        if (current is CustomerDetailLoading &&
            previous is CustomerDetailLoading) {
          return false;
        }
        return true;
      },
      builder: (context, state) {
        // Nếu đang loading và có cached data, hiển thị cached data với loading overlay
        if (state is CustomerDetailLoading && _cachedCustomer != null) {
          return Stack(
            children: [
              _buildSuccessState(_cachedCustomer!),
              // Loading overlay mượt mà
              Positioned.fill(
                child: Container(
                  color: Colors.black.withValues(alpha: 0.1),
                  child: Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.kienlongOrange,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        // Hiển thị loading chỉ khi chưa có cached data (lần đầu load)
        if (state is CustomerDetailLoading && _cachedCustomer == null) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is CustomerDetailFailure) {
          return _buildErrorState(state);
        }

        if (state is CustomerDetailSuccess) {
          return _buildSuccessState(state.customer);
        }

        // Initial state
        return const Center(child: CircularProgressIndicator());
      },
    );
  }

  Widget _buildErrorState(CustomerDetailFailure state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Không thể tải thông tin khách hàng',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            state.error,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppDimensions.spacingL),
          ElevatedButton(onPressed: _onRefresh, child: const Text('Thử lại')),
        ],
      ),
    );
  }

  Widget _buildSuccessState(CustomerModel customer) {
    return RefreshIndicator(
      onRefresh: () => _onTabRefresh(_tabController.index),
      child: NestedScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // Customer Detail Header
            CustomerDetailHeader(
              customer: customer,
              showTitle: _showTitle,
              onBack: () => Navigator.of(context).pop(),
            ),
            // Stats Cards
            SliverToBoxAdapter(
              child: CustomerDetailStatsCards(customer: customer),
            ),
            // Quick Actions
            SliverToBoxAdapter(
              child: CustomerDetailActions(
                onCall: () => _makeCall(customer.phoneNumber),
                onMessage: () => _sendMessage(customer.phoneNumber),
                onAddNote: () {
                  // TODO: Add note functionality
                },
                onEdit: () async {
                  final current = _cachedCustomer ?? customer;
                  final bloc = context.read<CustomerDetailBloc>();
                  final updated = await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => EditCustomerScreen(customer: current),
                    ),
                  );
                  // Refresh customer data after edit (regardless of result)
                  if (updated != null && mounted) {
                    bloc.add(CustomerDetailRefreshed(current.id));
                  }
                },
              ),
            ),
            // Tab Bar
            SliverPersistentHeader(
              delegate: _StickyTabBarDelegate(
                TabBar(
                  controller: _tabController,
                  labelColor: AppColors.kienlongOrange,
                  unselectedLabelColor: AppColors.textSecondary,
                  indicatorColor: AppColors.kienlongOrange,
                  indicatorWeight: 3,
                  labelStyle: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                  unselectedLabelStyle: Theme.of(context).textTheme.titleSmall,
                  tabs: const [
                    Tab(text: 'Thông tin'),
                    Tab(text: 'Nhật ký'),
                    Tab(text: 'Giao dịch'),
                    Tab(text: 'Tags'),
                  ],
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            // Tab "Thông tin" - Có thể lắng nghe CustomerDetailBloc state
            CustomerInfoTab(customer: customer),
            // Tab "Nhật ký" - Chưa có bloc, sử dụng customer data trực tiếp
            CustomerTimelineTab(customer: customer),
            // Tab "Giao dịch" - Chưa có bloc, sử dụng customer data trực tiếp
            CustomerTransactionsTab(customer: customer),
            // Tab "Tags" - Chưa có bloc, sử dụng customer data trực tiếp
            CustomerTagsTab(customer: customer),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BlocBuilder<CustomerDetailBloc, CustomerDetailState>(
      buildWhen: (previous, current) {
        // Chỉ rebuild khi có customer data
        return current is CustomerDetailSuccess ||
            (current is CustomerDetailFailure &&
                previous is CustomerDetailSuccess);
      },
      builder: (context, state) {
        // Sử dụng cached data hoặc current data
        final customerData = (state is CustomerDetailSuccess)
            ? state.customer
            : _cachedCustomer;

        if (customerData == null) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            border: Border(
              top: BorderSide(color: AppColors.borderLight, width: 1),
            ),
          ),
          child: SafeArea(
            child: Row(
              children: [
                // Create Transaction Button
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createNewTransaction,
                    icon: const Icon(TablerIcons.plus),
                    label: const Text('Tạo giao dịch'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.kienlongOrange,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(
                        vertical: AppDimensions.paddingM,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusM,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: AppDimensions.spacingM),
                // Call Button
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _makeCall(customerData.phoneNumber),
                    icon: const Icon(TablerIcons.phone),
                    label: const Text('Gọi ngay'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.kienlongSkyBlue,
                      side: BorderSide(color: AppColors.kienlongSkyBlue),
                      padding: EdgeInsets.symmetric(
                        vertical: AppDimensions.paddingM,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusM,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _StickyTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;
  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_StickyTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}
