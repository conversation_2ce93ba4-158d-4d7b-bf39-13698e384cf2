import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/ward_model.dart';
import '../../../shared/utils/color_utils.dart';
import '../../../shared/constants/config_types.dart';
import '../../auth/blocs/master_data_bloc.dart';
import '../blocs/customer_edit_form_bloc.dart';
import '../blocs/customer_edit_form_event.dart';
import '../blocs/customer_edit_form_state.dart';
import '../models/customer_model.dart';

import '../services/customer_service.dart';

class EditCustomerScreen extends StatefulWidget {
  final CustomerModel customer;

  const EditCustomerScreen({super.key, required this.customer});

  @override
  State<EditCustomerScreen> createState() => _EditCustomerScreenState();
}

class _EditCustomerScreenState extends State<EditCustomerScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final Map<String, dynamic> _formData = {};
  List<ConfigModel> _statusOptions = [];
  List<ConfigModel> _sourceOptions = [];
  bool _provincesLoaded = false; // Track if provinces have been loaded
  bool _statusOptionsLoaded = false; // Track if status options have been loaded
  bool _genderOptionsLoaded = false; // Track if status options have been loaded
  bool _workExperienceOptionsLoaded = false; // Track if work experience options have been loaded
  bool _sourceOptionsLoaded = false; // Track if source options have been loaded
  bool _initialWardsLoaded = false; // Track if initial wards have been loaded
  
  // Simple controller for monthly income to avoid focus loss
  late TextEditingController _monthlyIncomeController;

  Map<String, IconData> get _statusIcons => {
    'Tiềm năng': TablerIcons.eye,
    'Đang chăm sóc': TablerIcons.heart,
    'Đã giao dịch': TablerIcons.check,
  };

  @override
  void initState() {
    super.initState();
    _monthlyIncomeController = TextEditingController();
  }
  
  @override
  void dispose() {
    _monthlyIncomeController.dispose();
    super.dispose();
  }

  /// Get status color from ConfigModel value field using ColorUtils
  Color _getStatusColor(ConfigModel status) {
    // Use color from API value field if available
    if (status.value != null && status.value!.isNotEmpty) {
      return ColorUtils.hexToColor(status.value!);
    }

    // Fallback to predefined colors based on status label
    final fallbackColors = {
      'Tiềm năng': AppColors.warning,
      'Đang chăm sóc': AppColors.kienlongSkyBlue,
      'Đã giao dịch': AppColors.success,
    };

    return fallbackColors[status.label ?? ''] ?? AppColors.kienlongDarkBlue;
  }

  String _getStatusDescription(dynamic status) {
    // Handle both ConfigModel and String status values
    String statusLabel = '';

    if (status is ConfigModel) {
      statusLabel = status.label ?? '';
    } else {
      statusLabel = status.toString();
    }

    switch (statusLabel) {
      case 'Tiềm năng':
        return 'Khách hàng có khả năng sử dụng dịch vụ';
      case 'Đang chăm sóc':
        return 'Đang tư vấn và theo dõi nhu cầu';
      case 'Đã giao dịch':
        return 'Đã sử dụng sản phẩm/dịch vụ';
      default:
        return '';
    }
  }

   String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    
    // Remove all non-digits
    String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    // Handle large numbers safely without parsing to int
    // This avoids int overflow issues with very large numbers
    String formatted = cleanValue.replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
    
    return formatted;
  }

  /// Handle monthly income input formatting
  void _onIncomeChanged(String value) {
    String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    String formatted = _formatCurrency(cleanValue);
    
    // Update form data with clean value
    _formData['monthlyIncome'] = cleanValue;
    
    // Update controller with formatted value without losing focus
    if (_monthlyIncomeController.text != formatted) {
      _monthlyIncomeController.value = TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: formatted.length),
      );
    }
  }

  void _submit(BuildContext context) {
    // Ẩn bàn phím trước khi submit
    _hideKeyboard();
    
    if (!_formKey.currentState!.validate()) return;
    
    // Chuyển đổi ConfigModel thành code trước khi submit
    final convertedFormData = _convertConfigModelsToCodes(_formData);
    // Truyền form data đã chuyển đổi
    context.read<CustomerEditFormBloc>().add(
      CustomerEditFormSubmitted(widget.customer.id, convertedFormData),
    );
  }

  /// Chuyển đổi các ConfigModel trong form data thành code tương ứng
  Map<String, dynamic> _convertConfigModelsToCodes(Map<String, dynamic> formData) {
    final convertedData = Map<String, dynamic>.from(formData);
    
    // Chuyển đổi source (ConfigModel -> code)
    if (convertedData['source'] is ConfigModel) {
      final source = convertedData['source'] as ConfigModel;
      convertedData['source'] = source.code;
    }
    
    // Chuyển đổi gender (ConfigModel -> code)
    if (convertedData['gender'] is ConfigModel) {
      final gender = convertedData['gender'] as ConfigModel;
      convertedData['gender'] = gender.code;
    }
    
    // Chuyển đổi status (ConfigModel -> code)
    if (convertedData['status'] is ConfigModel) {
      final status = convertedData['status'] as ConfigModel;
      convertedData['status'] = status.code;
    }
    
    // Chuyển đổi province (ProvinceModel -> id)
    if (convertedData['province'] is ProvinceModel) {
      final province = convertedData['province'] as ProvinceModel;
      convertedData['province'] = province.id;
    }
    
    // Chuyển đổi ward (WardModel -> id)
    if (convertedData['ward'] is WardModel) {
      final ward = convertedData['ward'] as WardModel;
      convertedData['ward'] = ward.id;
    }
    
    // Chuyển đổi birthDate (DateTime -> "YYYY-MM-DD" string)
    if (convertedData['birthDate'] is DateTime) {
      final birthDate = convertedData['birthDate'] as DateTime;
      convertedData['birthDate'] = "${birthDate.year.toString().padLeft(4, '0')}-${birthDate.month.toString().padLeft(2, '0')}-${birthDate.day.toString().padLeft(2, '0')}";
    }
    // Chuyển đổi workExperience (ConfigModel -> code)
    if (convertedData['workExperience'] is ConfigModel) {
      final workExperience = convertedData['workExperience'] as ConfigModel;
      convertedData['workExperience'] = workExperience.code;
    }
    
    return convertedData;
  }

  /// Ẩn bàn phím một cách an toàn
  void _hideKeyboard() {
    // Sử dụng FocusScope để ẩn bàn phím
    FocusScope.of(context).unfocus();
    
    // Thêm delay nhỏ để đảm bảo bàn phím được ẩn hoàn toàn
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        // Kiểm tra xem có còn focus nào không
        final currentFocus = FocusScope.of(context);
        if (currentFocus.hasPrimaryFocus) {
          currentFocus.unfocus();
        }
      }
    });
  }

  void _onExit() {
    // Ẩn bàn phím trước khi xử lý logic thoát
    _hideKeyboard();
    
    // Check if form has data
    bool hasData =
        _formData['name'].toString().isNotEmpty ||
        _formData['phone'].toString().isNotEmpty;

    if (hasData) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thoát không lưu?'),
          content: const Text(
            'Bạn có thông tin chưa được lưu. Bạn có chắc chắn muốn thoát không?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Exit screen
              },
              style: TextButton.styleFrom(foregroundColor: AppColors.error),
              child: const Text('Thoát'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) =>
              CustomerEditFormBloc(customerService: CustomerService())
                ..add(CustomerEditFormInitialized(widget.customer)),
        ),
        BlocProvider(create: (_) => MasterDataBloc()),
      ],
      child: BlocConsumer<CustomerEditFormBloc, CustomerEditFormState>(
        listener: (context, state) {
          if (state is CustomerEditFormValidationFailure) {
            final firstError =
                state.validationErrors.values.firstOrNull?.firstOrNull;
            if (firstError != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      Icon(TablerIcons.alert_circle, color: Colors.white),
                      SizedBox(width: AppDimensions.spacingS),
                      Expanded(
                        child: Text(
                          'Vui lòng kiểm tra lại thông tin: $firstError',
                        ),
                      ),
                    ],
                  ),
                  backgroundColor: AppColors.error,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          } else if (state is CustomerEditFormSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(TablerIcons.check, color: Colors.white),
                    SizedBox(width: AppDimensions.spacingS),
                    Expanded(
                      child: Text(
                        'Đã cập nhật khách hàng thành công',
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                  ],
                ),
                backgroundColor: AppColors.success,
                behavior: SnackBarBehavior.floating,
              ),
            );
            // Navigate back with updated customer data
            Future.delayed(const Duration(milliseconds: 500), () {
              if (mounted && context.mounted) {
                Navigator.of(context).pop(true);
              }
            });
          } else if (state is CustomerEditFormFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    Icon(TablerIcons.alert_circle, color: Colors.white),
                    SizedBox(width: AppDimensions.spacingS),
                    Expanded(child: Text(state.error)),
                  ],
                ),
                backgroundColor: AppColors.error,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        builder: (context, state) {
          // Handle initial loading state
          if (state is CustomerEditFormLoading) {
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }

          // Get form data from appropriate state
          Map<String, dynamic> formData = {};
          if (state is CustomerEditFormReady) {
            formData = state.formData;
          } else if (state is CustomerEditFormValidationFailure) {
            formData = state.formData;
          } else if (state is CustomerEditFormSubmitting) {
            formData = state.formData;
          } else if (state is CustomerEditFormFailure) {
            formData = state.formData;
          }

          // Populate _formData from state data
          if (state is CustomerEditFormReady && _formData.isEmpty) {
            // First time loading - populate from ready state
            _formData.clear();
            _formData.addAll(formData);
            
            // Initialize monthly income controller with formatted value
            if (_formData['monthlyIncome'] != null && _formData['monthlyIncome']!.isNotEmpty) {
              String formatted = _formatCurrency(_formData['monthlyIncome']);
              _monthlyIncomeController.text = formatted;
            }
            
            // Reset flags khi form data mới được load
            _initialWardsLoaded = false;
          } else if (state is CustomerEditFormValidationFailure) {
            // Validation failure - update with current form data
            _formData.clear();
            _formData.addAll(formData);
            
            // Update monthly income controller with formatted value
            if (_formData['monthlyIncome'] != null && _formData['monthlyIncome']!.isNotEmpty) {
              String formatted = _formatCurrency(_formData['monthlyIncome']);
              if (_monthlyIncomeController.text != formatted) {
                _monthlyIncomeController.text = formatted;
              }
            }
          }

          // Determine if we should show loading overlay
          final bool isLoading = state is CustomerEditFormSubmitting;

          return Stack(
            children: [
              // Main Scaffold
              Scaffold(
                appBar: AppNavHeaderExtension.forScreen(
                  title: 'Chỉnh sửa khách hàng',
                  onBack: _onExit,
                ),
                body: AbsorbPointer(
                  absorbing: isLoading,
                  child: Opacity(
                    opacity: isLoading ? 0.6 : 1.0,
                    child: GestureDetector(
                      onTap: () => _hideKeyboard(),
                      child: Form(
                        key: _formKey,
                        child: SafeArea(
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(AppDimensions.paddingM),
                            child: _buildFormContent(
                              key: ValueKey(_formData.toString()),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  child: SafeArea(
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isLoading ? null : () => _submit(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.kienlongOrange,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: AppDimensions.paddingM,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusM,
                            ),
                          ),
                        ),
                        child: const Text('Lưu thay đổi'),
                      ),
                    ),
                  ),
                ),
              ),

              // Loading overlay covering entire Scaffold
              if (isLoading)
                Container(
                  color: Colors.black.withValues(alpha: 0.3),
                  child: const Center(
                    child: Card(
                      child: Padding(
                        padding: EdgeInsets.all(24.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text(
                              'Đang lưu thay đổi...',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFormContent({Key? key}) {
    return Column(
      key: key,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Introduction
        Container(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
          decoration: BoxDecoration(
            color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            children: [
              Icon(
                TablerIcons.info_circle,
                color: AppColors.kienlongSkyBlue,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Text(
                  'Chỉnh sửa thông tin khách hàng. Các trường có dấu (*) là bắt buộc.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.kienlongSkyBlue,
                  ),
                ),
              ),
            ],
          ),
        ),

        // 1. Họ và tên (Bắt buộc)
        Text(
          'Họ và tên *',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          key: ValueKey('name_${_formData['name']}'),
          initialValue: _formData['name'],
          decoration: InputDecoration(
            hintText: 'Nhập họ và tên đầy đủ',
            prefixIcon: Icon(TablerIcons.user, color: AppColors.textSecondary),
          ),
          textCapitalization: TextCapitalization.words,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập họ và tên';
            }
            if (value.trim().length < 2) {
              return 'Họ tên phải có ít nhất 2 ký tự';
            }
            return null;
          },
          onChanged: (v) => _formData['name'] = v,
        ),
        SizedBox(height: AppDimensions.spacingL),

        // 2. Số điện thoại (Bắt buộc)
        Text(
          'Số điện thoại *',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          key: ValueKey('phone_${_formData['phone']}'),
          initialValue: _formData['phone'],
          decoration: InputDecoration(
            hintText: 'Nhập số điện thoại',
            prefixIcon: Icon(TablerIcons.phone, color: AppColors.textSecondary),
          ),
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(11),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Vui lòng nhập số điện thoại';
            }
            if (!RegExp(r'^(0[3-9])\d{8}$').hasMatch(value)) {
              return 'Số điện thoại không hợp lệ';
            }
            return null;
          },
          onChanged: (v) => _formData['phone'] = v,
        ),
        SizedBox(height: AppDimensions.spacingL),

        //  3. Trạng thái khách hàng (Group button)
        _customerTagUpdate(),
        SizedBox(height: AppDimensions.spacingL),
        // 5. Tab thông tin
        _SectionTitle('Thông tin'),
        // 6. Số GTTT
        Text(
          'Số GTTT',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          key: ValueKey('idNumber_${_formData['idNumber']}'),
          initialValue: _formData['idNumber'],
          decoration: InputDecoration(
            hintText: 'Nhập số giấy tờ tùy thân (không bắt buộc)',
            prefixIcon: Icon(TablerIcons.id, color: AppColors.textSecondary),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(12),
          ],
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (value.length != 12) {
                return 'Số GTTT phải có 12 số';
              }
            }
            return null;
          },
          onChanged: (v) => _formData['idNumber'] = v,
        ),
        SizedBox(height: AppDimensions.spacingL),

        // 7. Địa chỉ thường trú
        _SectionTitle('Địa chỉ thường trú'),
        //  8. Tỉnh/thành phố
        _addressInfoUpdate(),
        // 10. Địa chỉ thường trú (chi tiết)
        Text(
          'Địa chỉ thường trú',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          key: ValueKey('permanentAddress_${_formData['permanentAddress']}'),
          initialValue: _formData['permanentAddress'],
          decoration: InputDecoration(
            hintText: 'Nhập số nhà, tên đường. Tối đa 100 ký tự',
            prefixIcon: Icon(TablerIcons.home, color: AppColors.textSecondary),
          ),
          maxLines: 3,
          validator: (v) => (v == null || v.trim().isEmpty)
              ? 'Địa chỉ thường trú không được để trống'
              : null,
          onChanged: (v) => _formData['permanentAddress'] = v,
        ),
        SizedBox(height: AppDimensions.spacingL),

        // 11. Email
        Text(
          'Email',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          key: ValueKey('email_${_formData['email']}'),
          initialValue: _formData['email'],
          decoration: InputDecoration(
            hintText: 'Nhập địa chỉ email (không bắt buộc)',
            prefixIcon: Icon(TablerIcons.mail, color: AppColors.textSecondary),
          ),
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Địa chỉ email không hợp lệ';
              }
            }
            return null;
          },
          onChanged: (v) => _formData['email'] = v,
        ),
        SizedBox(height: AppDimensions.spacingL),

        Text(
          'Địa chỉ hiện tại',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          initialValue: _formData['currentAddress'],
          decoration: InputDecoration(
            hintText: 'Nhập số nhà, tên đường. Tối đa 100 ký tự',
            prefixIcon: Icon(TablerIcons.home, color: AppColors.textSecondary),
          ),
          maxLines: 3,
          onChanged: (v) => _formData['currentAddress'] = v,
        ),
        SizedBox(height: AppDimensions.spacingL),

        // 16. Giới tính
        _genderUpdate(),
        SizedBox(height: AppDimensions.spacingM),

        // 17. Ngày sinh
        Text(
          'Ngày sinh',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        InkWell(
          onTap: () async {
            final DateTime? picked = await showDatePicker(
              context: context,
              initialDate: _formData['birthDate'] ?? DateTime(1990),
              firstDate: DateTime(1940),
              lastDate: DateTime.now(),
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ColorScheme.light(
                      primary: AppColors.kienlongOrange,
                      onPrimary: Colors.white,
                    ),
                  ),
                  child: child!,
                );
              },
            );
            if (picked != null) {
              setState(() {
                _formData['birthDate'] = picked;
              });
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingM + 2,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.borderLight),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Row(
              children: [
                Icon(TablerIcons.calendar, color: AppColors.textSecondary),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    _formData['birthDate'] != null
                        ? _formatDate(_formData['birthDate'])
                        : 'Chọn ngày sinh',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: _formData['birthDate'] != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                  ),
                ),
                Icon(
                  TablerIcons.chevron_down,
                  color: AppColors.textSecondary,
                  size: AppDimensions.iconS,
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: AppDimensions.spacingL),

        // 12. Nơi làm việc
        Text(
          'Nơi làm việc',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          key: ValueKey('workplace_${_formData['workplace']}'),
          initialValue: _formData['workplace'],
          decoration: InputDecoration(
            hintText: 'Nhập nơi làm việc (không bắt buộc)',
            prefixIcon: Icon(
              TablerIcons.building,
              color: AppColors.textSecondary,
            ),
          ),
          onChanged: (v) => _formData['workplace'] = v,
        ),
        SizedBox(height: AppDimensions.spacingM),

        // 12. Kinh nghiệm làm việc
        _workExperienceUpdate(),
        SizedBox(height: AppDimensions.spacingL),

        // 18. Hồ sơ khách hàng
        _SectionTitle('Hồ sơ khách hàng'),
        // 19. Số CIF
        Text(
          'Số CIF',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          key: ValueKey('cifNumber_${_formData['cifNumber']}'),
          initialValue: _formData['cifNumber'] ?? '',
          decoration: InputDecoration(
            hintText: 'Nhập số CIF',
            prefixIcon: Icon(
              TablerIcons.credit_card,
              color: AppColors.textSecondary,
            ),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(12),
          ],
          validator: (value) {
            // Nếu không có giá trị thì không cần validate (không bắt buộc)
            if (value == null || value.isEmpty) {
              return null;
            }
            // Nếu có giá trị thì validate định dạng
            if (value.length < 8 || value.length > 12) {
              return 'Số CIF chỉ được chứa 8-12 ký tự số';
            }
            if (!RegExp(r'^\d+$').hasMatch(value)) {
              return 'Số CIF chỉ được chứa ký tự số';
            }
            return null;
          },
          onChanged: (v) => _formData['cifNumber'] = v,
        ),
        SizedBox(height: AppDimensions.spacingM),

        // 20. Nghề nghiệp (Bắt buộc)
        Text(
          'Nghề nghiệp *',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          initialValue: _formData['occupation'],
          decoration: InputDecoration(
            hintText: 'Nhập nghề nghiệp',
            prefixIcon: Icon(
              TablerIcons.briefcase,
              color: AppColors.textSecondary,
            ),
          ),
          validator: (v) => (v == null || v.trim().isEmpty)
              ? 'Nghề nghiệp không được để trống'
              : null,
          onChanged: (v) => _formData['occupation'] = v,
        ),
        SizedBox(height: AppDimensions.spacingM),

        // 21. Nguồn khách hàng (Bắt buộc)

        _sourceUpdate(),
        SizedBox(height: AppDimensions.spacingM),
        // 22. Thu nhập hàng tháng
        Text(
          'Thu nhập hàng tháng',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          controller: _monthlyIncomeController,
          decoration: InputDecoration(
            hintText: 'Nhập thu nhập hàng tháng',
            suffixText: 'VND',
            suffixStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textPrimary,
                ),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
          ],
          onChanged: _onIncomeChanged,
        ),
        SizedBox(height: AppDimensions.spacingXL * 2),
      ],
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Get gender value for dropdown - handle both ConfigModel and String
  String? _getGenderValue(dynamic gender, List<ConfigModel> genderOptions) {
    if (gender == null) return null;

    if (gender is ConfigModel) {
      // Nếu gender là ConfigModel, trả về value để so sánh với dropdown
      // Chỉ trả về value nếu ConfigModel có dữ liệu hợp lệ
      if (gender.value != null && gender.value!.isNotEmpty) {
        return gender.value;
      }
      return null; // Trả về null nếu ConfigModel không có value hợp lệ
    } else {
      // Nếu gender là String, tìm ConfigModel tương ứng
      final config = genderOptions.firstWhere(
        (config) =>
            config.label == gender.toString() ||
            config.value == gender.toString(),
        orElse: () => ConfigModel(),
      );
      // Chỉ trả về value nếu tìm thấy ConfigModel hợp lệ
      if (config.value != null && config.value!.isNotEmpty) {
        return config.value;
      }
      return null;
    }
  }

  /// Get work experience value for dropdown - handle both ConfigModel and String
  /// Work experience uses 'code' field instead of 'value' field like gender
  String? _getWorkExperienceValue(dynamic workExperience, List<ConfigModel> workExperienceOptions) {
    if (workExperience == null) return null;

    if (workExperience is ConfigModel) {
      // Nếu workExperience là ConfigModel, trả về code để so sánh với dropdown
      // Chỉ trả về code nếu ConfigModel có dữ liệu hợp lệ
      if (workExperience.code != null && workExperience.code!.isNotEmpty) {
        return workExperience.code;
      }
      return null; // Trả về null nếu ConfigModel không có code hợp lệ
    } else {
      // Nếu workExperience là String, tìm ConfigModel tương ứng bằng code hoặc label
      final config = workExperienceOptions.firstWhere(
        (config) =>
            config.code == workExperience.toString() ||
            config.label == workExperience.toString(),
        orElse: () => ConfigModel(),
      );
      // Chỉ trả về code nếu tìm thấy ConfigModel hợp lệ
      if (config.code != null && config.code!.isNotEmpty) {
        return config.code;
      }
      return null;
    }
  }

  /// Get source value for dropdown - handle both ConfigModel and String
  String? _getSourceValue(dynamic source, List<ConfigModel> sourceOptions) {
    if (source == null) return null;

    if (source is ConfigModel) {
      // Nếu source là ConfigModel, trả về label để so sánh với dropdown
      // Chỉ trả về label nếu ConfigModel có dữ liệu hợp lệ
      if (source.label != null && source.label!.isNotEmpty) {
        return source.label;
      }
      return null; // Trả về null nếu ConfigModel không có label hợp lệ
    } else {
      // Nếu source là String, tìm ConfigModel tương ứng
      final config = sourceOptions.firstWhere(
        (config) =>
            config.label == source.toString() ||
            config.value == source.toString(),
        orElse: () => ConfigModel(),
      );
      // Chỉ trả về label nếu tìm thấy ConfigModel hợp lệ
      if (config.label != null && config.label!.isNotEmpty) {
        return config.label;
      }
      return null;
    }
  }

  /// Get province value for dropdown - handle both ProvinceModel and String
  String? _getProvinceValue(dynamic province, List<ProvinceModel> provinces) {
    if (province == null) return null;

    if (province is ProvinceModel) {
      // Nếu province là ProvinceModel, trả về id để so sánh với dropdown
      // Chỉ trả về id nếu có giá trị hợp lệ
      if (province.id.isNotEmpty) {
        return province.id;
      }
      return null; // Trả về null nếu id không hợp lệ
    } else {
      // Nếu province là String, tìm ProvinceModel tương ứng
      final provinceModel = provinces.firstWhere(
        (p) => p.id == province.toString() || p.name == province.toString(),
        orElse: () => ProvinceModel(id: '', gsoCode: '', name: ''),
      );
      // Chỉ trả về id nếu tìm thấy ProvinceModel hợp lệ
      if (provinceModel.id.isNotEmpty) {
        return provinceModel.id;
      }
      return null;
    }
  }

  /// Get ward value for dropdown - handle both WardModel and String
  String? _getWardValue(dynamic ward, List<WardModel> wards) {
    if (ward == null) return null;

    if (ward is WardModel) {
      // Nếu ward là WardModel, trả về id để so sánh với dropdown
      // Chỉ trả về id nếu có giá trị hợp lệ
      if (ward.id != null && ward.id!.isNotEmpty) {
        return ward.id;
      }
      return null; // Trả về null nếu id không hợp lệ
    } else {
      // Nếu ward là String, tìm WardModel tương ứng
      final wardModel = wards.firstWhere(
        (w) => w.id == ward.toString() || w.name == ward.toString(),
        orElse: () => WardModel(id: '', name: ''),
      );
      // Chỉ trả về id nếu tìm thấy WardModel hợp lệ
      if (wardModel.id != null && wardModel.id!.isNotEmpty) {
        return wardModel.id;
      }
      return null;
    }
  }

  Widget _sourceUpdate() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Nguồn khách hàng *',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            // Load CUST_SOURCE config if not loaded yet - SYNC with LoadConfigEvent pattern
            if (state is MasterDataInitial && !_sourceOptionsLoaded) {
              _sourceOptionsLoaded = true;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  debugPrint('Loading CUST_SOURCE config...');
                  context.read<MasterDataBloc>().add(
                    const LoadConfigEvent('CUST_SOURCE'),
                  );
                }
              });
            }

            List<ConfigModel> sourceOptions = [];

            // Sử dụng Composite State để lấy configs
            if (state is MasterDataLoaded) {
              sourceOptions = state.configsByGroup['CUST_SOURCE'] ?? [];
              _sourceOptions = sourceOptions; // Cache for later use
            } else if (state is MasterDataError && state.type == 'config') {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DropdownButtonFormField<String>(
                    value: null,
                    decoration: InputDecoration(
                      hintText: 'Lỗi tải dữ liệu',
                      prefixIcon: Icon(
                        TablerIcons.users,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    items: [],
                    onChanged: null,
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'Không thể tải danh sách nguồn khách hàng',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: AppColors.error),
                  ),
                ],
              );
            }

            // Show loading indicator if still loading
            if (state is MasterDataLoading && state.type == 'config') {
              return const Center(child: CircularProgressIndicator());
            }

            // If no source options loaded yet, show loading
            if (sourceOptions.isEmpty && _sourceOptions.isEmpty) {
              debugPrint('No source options available, showing loading...');
              return const Center(child: Text('Không có dữ liệu nguồn khách hàng'));
            }

            // Use cached options if current state doesn't have them
            final optionsToUse = sourceOptions.isNotEmpty
                ? sourceOptions
                : _sourceOptions;

            debugPrint(
              'Rendering source options: ${optionsToUse.length}, current source: ${_formData['source']}',
            );

            return DropdownButtonFormField<String>(
              value: _getSourceValue(_formData['source'], optionsToUse),
              decoration: InputDecoration(
                hintText: 'Chọn nguồn khách hàng',
                prefixIcon: Icon(TablerIcons.users, color: AppColors.textSecondary),
              ),
              items: optionsToUse.map((config) {
                return DropdownMenuItem<String>(
                  value: config.label,
                  child: Text(config.label ?? ''),
                );
              }).toList(),
              onChanged: (String? value) {
                setState(() {
                  // Tìm ConfigModel tương ứng và lưu vào _formData
                  final selectedConfig = optionsToUse.firstWhere(
                    (config) => config.label == value,
                    orElse: () => ConfigModel(),
                  );
                  _formData['source'] = selectedConfig;
                });
                debugPrint('source: ${_formData['source']}');
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng chọn nguồn khách hàng';
                }
                return null;
              },
            );
          },
        ),
      ],
    );
  }

  Widget _genderUpdate() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Giới tính',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            // Load SEX config if not loaded yet - SYNC with LoadConfigEvent pattern
            if (state is MasterDataInitial && !_genderOptionsLoaded) {
              _genderOptionsLoaded = true;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  debugPrint('Loading SEX config...');
                  context.read<MasterDataBloc>().add(const LoadConfigEvent('SEX'));
                }
              });
            }

            List<ConfigModel> genderOptions = [];

            // Sử dụng Composite State để lấy configs
            if (state is MasterDataLoaded) {
              genderOptions = state.configsByGroup['SEX'] ?? [];
              debugPrint('SEX config loaded: ${genderOptions.length} options');
            } else if (state is MasterDataError && state.type == 'config') {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DropdownButtonFormField<String>(
                    value: null,
                    decoration: InputDecoration(
                      hintText: 'Lỗi tải dữ liệu',
                      prefixIcon: Icon(
                        TablerIcons.gender_male,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    items: [],
                    onChanged: null,
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'Không thể tải danh sách giới tính',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: AppColors.error),
                  ),
                ],
              );
            }

            // Show loading indicator if still loading
            if (state is MasterDataLoading && state.type == 'config') {
              debugPrint('Loading SEX config...');
              return const Center(child: CircularProgressIndicator());
            }

            // If no gender options loaded yet, show loading
            if (genderOptions.isEmpty) {
              debugPrint('No gender options available, showing loading...');
              return const Center(child: CircularProgressIndicator());
            }

            debugPrint(
              'Rendering gender options: ${genderOptions.length}, current gender: ${_formData['gender']}',
            );

            return DropdownButtonFormField<String>(
              value: _getGenderValue(_formData['gender'], genderOptions),
              decoration: InputDecoration(
                hintText: 'Chọn giới tính',
                prefixIcon: Icon(
                  TablerIcons.gender_male,
                  color: AppColors.textSecondary,
                ),
              ),
              items: genderOptions.map((config) {
                return DropdownMenuItem<String>(
                  value: config.value,
                  child: Text(config.label ?? ''),
                );
              }).toList(),
              onChanged: (String? value) {
                setState(() {
                  // Tìm ConfigModel tương ứng và lưu vào _formData
                  final selectedConfig = genderOptions.firstWhere(
                    (config) => config.value == value,
                    orElse: () => ConfigModel(),
                  );
                  _formData['gender'] = selectedConfig;
                });
              },
            );
          },
        ),
      ],
    );
  }

  Widget _workExperienceUpdate() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Kinh nghiệm làm việc',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            // Load WORK_EXPERIENCE config if not loaded yet - SYNC with LoadConfigEvent pattern
            if (state is MasterDataInitial && !_workExperienceOptionsLoaded) {
              _workExperienceOptionsLoaded = true;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  context.read<MasterDataBloc>().add(LoadConfigEvent(ConfigTypes.WORK_EXPERIENCE));
                }
              });
            }

            List<ConfigModel> workExperienceOptions = [];

            // Sử dụng Composite State để lấy configs
            if (state is MasterDataLoaded) {
              workExperienceOptions = state.configsByGroup[ConfigTypes.WORK_EXPERIENCE] ?? [];
            } else if (state is MasterDataError && state.type == 'config') {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DropdownButtonFormField<String>(
                    value: null,
                    decoration: InputDecoration(
                      hintText: 'Lỗi tải dữ liệu',
                      prefixIcon: Icon(
                        TablerIcons.clock,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    items: [],
                    onChanged: null,
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'Không thể tải danh sách kinh nghiệm làm việc',
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: AppColors.error),
                  ),
                ],
              );
            }

            // Show loading indicator if still loading
            if (state is MasterDataLoading && state.type == 'config') {
              return const Center(child: CircularProgressIndicator());
            }

            // If no work experience options loaded yet, show loading
            if (workExperienceOptions.isEmpty) {
              return const Center(child: CircularProgressIndicator());
            }
            return DropdownButtonFormField<String>(
              value: _getWorkExperienceValue(_formData['workExperience'], workExperienceOptions),
              decoration: InputDecoration(
                hintText: 'Chọn kinh nghiệm làm việc',
                prefixIcon: Icon(
                  TablerIcons.clock,
                  color: AppColors.textSecondary,
                ),
              ),
              items: workExperienceOptions.map((config) {
                return DropdownMenuItem<String>(
                  value: config.code, // Use code for work experience
                  child: Text(config.label ?? ''),
                );
              }).toList(),
              onChanged: (String? value) {
                setState(() {
                  // Tìm ConfigModel tương ứng và lưu vào _formData
                  final selectedConfig = workExperienceOptions.firstWhere(
                    (config) => config.code == value, // Use code for comparison
                    orElse: () => ConfigModel(),
                  );
                  _formData['workExperience'] = selectedConfig;
                });
              },
            );
          },
        ),
      ],
    );
  }

  Widget _customerTagUpdate() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trạng thái khách hàng',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            // Load CUST_STATUS if not loaded yet - SYNC with LoadConfigEvent pattern
            if (state is MasterDataInitial && !_statusOptionsLoaded) {
              _statusOptionsLoaded = true;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  debugPrint('Loading CUST_STATUS config...');
                  context.read<MasterDataBloc>().add(
                    const LoadConfigEvent('CUST_STATUS'),
                  );
                }
              });
            }

            List<ConfigModel> statusOptions = [];

            // Sử dụng Composite State để lấy configs
            if (state is MasterDataLoaded) {
              statusOptions = state.configsByGroup['CUST_STATUS'] ?? [];
              _statusOptions = statusOptions; // Cache for later use
              debugPrint('CUST_STATUS loaded: ${statusOptions.length} options');
            } else if (state is MasterDataError && state.type == 'config') {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      border: Border.all(color: AppColors.error),
                    ),
                    child: Row(
                      children: [
                        Icon(TablerIcons.alert_circle, color: AppColors.error),
                        SizedBox(width: AppDimensions.spacingM),
                        Expanded(
                          child: Text(
                            'Không thể tải danh sách trạng thái khách hàng',
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(color: AppColors.error),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }

            // Show loading indicator if still loading
            if (state is MasterDataLoading && state.type == 'config') {
              debugPrint('Loading CUST_STATUS...');
              return const Center(child: CircularProgressIndicator());
            }

            // If no status options loaded yet, show loading
            if (statusOptions.isEmpty && _statusOptions.isEmpty) {
              debugPrint('No status options available, showing loading...');
              return const Center(child: CircularProgressIndicator());
            }

            // Use cached options if current state doesn't have them
            final optionsToUse = statusOptions.isNotEmpty
                ? statusOptions
                : _statusOptions;

            debugPrint(
              'Rendering status options: ${optionsToUse.length}, current status: ${_formData['status']}',
            );

            return Column(
              children: optionsToUse.map((status) {
                String statusLabel = status.label ?? '';
                bool isSelected = false;
                final currentStatus = _formData['status'];

                if (currentStatus != null) {
                  if (currentStatus is ConfigModel) {
                    // If status is ConfigModel, compare with code or label
                    isSelected =
                        currentStatus.code == status.code ||
                        currentStatus.label == statusLabel;
                  } else {
                    // If status is string, compare directly
                    isSelected = currentStatus.toString() == statusLabel;
                  }
                }
                final statusColor = _getStatusColor(status);
                final statusIcon = _statusIcons[statusLabel] ?? TablerIcons.user;

                return Container(
                  margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        // Store the ConfigModel to maintain full status information
                        _formData['status'] = status;
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.all(AppDimensions.paddingM),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? statusColor.withValues(alpha: 0.1)
                            : Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        border: Border.all(
                          color: isSelected ? statusColor : AppColors.borderLight,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(AppDimensions.paddingS),
                            decoration: BoxDecoration(
                              color: statusColor.withValues(alpha: 0.2),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              statusIcon,
                              color: statusColor,
                              size: AppDimensions.iconM,
                            ),
                          ),
                          SizedBox(width: AppDimensions.spacingM),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  statusLabel,
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: isSelected ? statusColor : null,
                                      ),
                                ),
                                Text(
                                  _getStatusDescription(status),
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(color: AppColors.textSecondary),
                                ),
                              ],
                            ),
                          ),
                          if (isSelected)
                            Icon(TablerIcons.check, color: statusColor),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _addressInfoUpdate() {
    List<ProvinceModel> provinces = [];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tỉnh/thành phố',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) {
            // Chỉ rebuild khi provinces được load hoặc có lỗi
            return current is MasterDataLoaded ||
                current is MasterDataError && current.type == 'provinces' ||
                current is MasterDataLoading && current.type == 'provinces' ||
                current is MasterDataInitial;
          },
          builder: (context, state) {
            // Cập nhật danh sách provinces khi state thay đổi

            // Load provinces if not loaded yet - SYNC with LoadConfigEvent pattern
            if (state is MasterDataInitial && !_provincesLoaded) {
              _provincesLoaded = true;
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  context.read<MasterDataBloc>().add(
                    const LoadProvincesEvent(),
                  );
                }
              });
            }

            // Sử dụng Composite State để lấy provinces
            if (state is MasterDataLoaded) {
              provinces = state.provinces;

              // Gọi API wards lần đầu nếu province có data và chưa load wards
              if (!_initialWardsLoaded && _formData['province'] != null) {
                final province = _formData['province'];
                String? provinceId;

                // Extract provinceId từ ProvinceModel hoặc String
                if (province is ProvinceModel) {
                  provinceId = province.id;
                } else if (province != null) {
                  provinceId = province.toString();
                }

                // Load wards lần đầu nếu có provinceId hợp lệ
                if (provinceId != null && provinceId.isNotEmpty) {
                  _initialWardsLoaded = true; // Mark as loaded
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) {
                      debugPrint(
                        'Initial load wards for province: $provinceId',
                      );
                      context.read<MasterDataBloc>().add(
                        LoadWardsEvent(provinceId!),
                      );
                    }
                  });
                }
              }
            } else if (state is MasterDataError && state.type == 'provinces') {
              return Text(state.message);
            }

            return DropdownButtonFormField<String>(
              value: _getProvinceValue(_formData['province'], provinces),
              decoration: InputDecoration(
                hintText:
                    (state is MasterDataLoading && state.type == 'provinces')
                    ? 'Đang tải...'
                    : 'Chọn tỉnh/thành phố',
                prefixIcon: Icon(
                  TablerIcons.building_skyscraper,
                  color: AppColors.textSecondary,
                ),
              ),
              items: provinces.map((province) {
                return DropdownMenuItem<String>(
                  value: province.id,
                  child: Text(province.name),
                );
              }).toList(),
              onChanged: (provinceId) {
                setState(() {
                  // Tìm ProvinceModel tương ứng và lưu vào _formData
                  final selectedProvince = provinces.firstWhere(
                    (province) => province.id == provinceId,
                    orElse: () => ProvinceModel(id: '', gsoCode: '', name: ''),
                  );
                  _formData['province'] = selectedProvince;
                  // Reset ward khi thay đổi province
                  _formData['ward'] = null;
                  // Reset flag để cho phép load wards mới
                  _initialWardsLoaded = false;
                });
                BlocProvider.of<MasterDataBloc>(
                  context,
                ).add(LoadWardsEvent(provinceId ?? ''));
              },
            );
          },
        ),

        SizedBox(height: AppDimensions.spacingM),
        // 9. Phường/xã
        Text(
          'Phường/xã',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            List<WardModel> wards = [];

            // Sử dụng Composite State để lấy wards
            if (state is MasterDataLoaded) {
              final province = _formData['province'];
              String? provinceId;

              // Extract provinceId từ ProvinceModel hoặc String
              if (province is ProvinceModel) {
                provinceId = province.id;
              } else if (province != null) {
                provinceId = province.toString();
              }

              // Chỉ load wards nếu có provinceId hợp lệ
              if (provinceId != null && provinceId.isNotEmpty) {
                wards = state.wardsByProvince[provinceId] ?? [];
              }
            }

            return DropdownButtonFormField<String>(
              value: _getWardValue(_formData['ward'], wards),
              decoration: InputDecoration(
                hintText: (state is MasterDataLoading && state.type == 'wards')
                    ? 'Đang tải...'
                    : 'Chọn phường/xã',
                prefixIcon: Icon(
                  TablerIcons.building,
                  color: AppColors.textSecondary,
                ),
              ),
              items: wards.map((ward) {
                return DropdownMenuItem<String>(
                  value: ward.id,
                  child: Text(ward.name ?? ''),
                );
              }).toList(),
              onChanged: (wardId) {
                setState(() {
                  // Tìm WardModel tương ứng và lưu vào _formData
                  final selectedWard = wards.firstWhere(
                    (ward) => ward.id == wardId,
                    orElse: () => WardModel(id: '', name: ''),
                  );
                  _formData['ward'] = selectedWard;
                });
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),
      ],
    );
  }
}

class _SectionTitle extends StatelessWidget {
  final String title;

  const _SectionTitle(this.title);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppDimensions.paddingS),
      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: isDark
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
        ),
      ),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
      ),
    );
  }
}
