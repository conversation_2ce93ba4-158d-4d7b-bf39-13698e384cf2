import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../widgets/index.dart';
import '../blocs/customer_form_bloc.dart';
import '../blocs/customer_form_event.dart';
import '../blocs/customer_form_state.dart';
import '../services/customer_service.dart';
import '../../auth/blocs/master_data_bloc.dart';
import 'steps/basic_info_step.dart';
import 'steps/address_info_step.dart';
import 'steps/career_info_step.dart';
import 'steps/classification_step.dart';
import '../../../../shared/models/qr_models.dart';

class AddCustomerScreen extends StatefulWidget {
  final Map<String, dynamic>? initialData;
  final CccdQrData? cccdQrData;
  
  const AddCustomerScreen({
    super.key,
    this.initialData,
    this.cccdQrData,
  });

  @override
  State<AddCustomerScreen> createState() => _AddCustomerScreenState();
}

class _AddCustomerScreenState extends State<AddCustomerScreen> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CustomerFormBloc(
            customerService: CustomerService(),
          ),
        ),
        BlocProvider(
          create: (context) => MasterDataBloc(),
        ),
      ],
      child: _AddCustomerScreenContent(
        initialData: widget.initialData,
        cccdQrData: widget.cccdQrData,
      ),
    );
  }
}

class _AddCustomerScreenContent extends StatefulWidget {
  final Map<String, dynamic>? initialData;
  final CccdQrData? cccdQrData;
  
  const _AddCustomerScreenContent({
    this.initialData,
    this.cccdQrData,
  });

  @override
  State<_AddCustomerScreenContent> createState() => _AddCustomerScreenContentState();
}

class _AddCustomerScreenContentState extends State<_AddCustomerScreenContent> {
  final PageController _pageController = PageController();
  int _lastStep = 0; // Theo dõi step cuối cùng để tránh infinite loop
  int _currentStep = 0; // Lưu trữ currentStep để sử dụng khi state không có
  int _totalSteps = 4; // Lưu trữ totalSteps để sử dụng khi state không có

  // Step validation
  final Map<int, GlobalKey<FormState>> _formKeys = {
    0: GlobalKey<FormState>(),
    1: GlobalKey<FormState>(),
    2: GlobalKey<FormState>(),
    3: GlobalKey<FormState>(),
  };

  @override
  void initState() {
    super.initState();
    // Initialize bloc after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CustomerFormBloc>().add(CustomerFormInitialized(
        initialData: widget.initialData,
        cccdQrData: widget.cccdQrData,
      ));
    });
  }

  bool _isStepValid(int step) {
    final formKey = _formKeys[step];
    if (formKey?.currentState != null) {
      return formKey!.currentState!.validate();
    }
    return true;
  }

  void _nextStep() {
    // Ẩn bàn phím trước khi chuyển step
    FocusScope.of(context).unfocus();
    
    final bloc = context.read<CustomerFormBloc>();
    final state = bloc.state;
    
    if (state is CustomerFormReady) {
      final currentStep = state.currentStep;
      if (_isStepValid(currentStep)) {
        // Chuyển step với validation
        bloc.add(CustomerFormNextStep(currentStep, state.formData));
      }
    }
  }

  void _previousStep() {
    // Ẩn bàn phím trước khi chuyển step
    FocusScope.of(context).unfocus();
    
    final bloc = context.read<CustomerFormBloc>();
    final state = bloc.state;
    
    if (state is CustomerFormReady && state.currentStep > 0) {
      bloc.add(CustomerFormPreviousStep(state.currentStep));
    }
  }



  void _onExit() {
    // Check if form has data
    final bloc = context.read<CustomerFormBloc>();
    final state = bloc.state;
    
    bool hasData = false;
    if (state is CustomerFormReady) {
      final formData = state.formData;
      hasData = (formData['fullName']?.toString().isNotEmpty == true) ||
                (formData['phoneNumber']?.toString().isNotEmpty == true);
    }

    if (hasData) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Thoát không lưu?'),
          content: const Text(
            'Bạn có thông tin chưa được lưu. Bạn có chắc chắn muốn thoát không?'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Exit screen
              },
              style: TextButton.styleFrom(
                foregroundColor: AppColors.error,
              ),
              child: const Text('Thoát'),
            ),
          ],
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  String _getStepTitle(int currentStep) {
    switch (currentStep) {
      case 0: return 'Thông tin cơ bản';
      case 1: return 'Địa chỉ & Liên hệ';
      case 2: return 'Thông tin nghề nghiệp';
      case 3: return 'Phân loại khách hàng';
      default: return 'Thêm khách hàng mới';
    }
  }

  String _getActionButtonText(int currentStep, int totalSteps) {
    return currentStep == totalSteps - 1 ? 'Lưu khách hàng' : 'Tiếp tục';
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<CustomerFormBloc, CustomerFormState>(
      listener: (context, state) {
        if (state is CustomerFormValidationFailure) {
          // Show validation errors
          final firstError = state.validationErrors.values.firstOrNull?.firstOrNull;
          if (firstError != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Vui lòng kiểm tra lại thông tin: $firstError'),
                backgroundColor: AppColors.error,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        } else if (state is CustomerFormSuccess) {
          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    TablerIcons.check,
                    color: Colors.white,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Text('Đã thêm khách hàng thành công'),
                ],
              ),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Navigate back with success result
          Navigator.of(context).pop(true);
        } else if (state is CustomerFormFailure) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(
                    TablerIcons.alert_circle,
                    color: Colors.white,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(child: Text(state.error)),
                ],
              ),
              backgroundColor: AppColors.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else if (state is CustomerFormReady) {
          // Cập nhật currentStep và totalSteps từ state
          _currentStep = state.currentStep;
          _totalSteps = state.totalSteps;
          
          // Cập nhật PageView khi step thay đổi
          if (_lastStep != state.currentStep) {
            _lastStep = state.currentStep;
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_pageController.hasClients && _pageController.page?.round() != state.currentStep) {
                _pageController.animateToPage(
                  state.currentStep,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              }
            });
          }
        }
      },
      child: BlocBuilder<CustomerFormBloc, CustomerFormState>(
        builder: (context, state) {
          if (state is CustomerFormLoading) {
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          }

          // Get form data from appropriate state
          Map<String, dynamic> formData = {};
          
          if (state is CustomerFormReady) {
            formData = state.formData;
            // currentStep và totalSteps đã được cập nhật trong listener
          } else if (state is CustomerFormValidationFailure) {
            formData = state.formData;
            // Sử dụng currentStep và totalSteps đã lưu trữ
          } else if (state is CustomerFormSubmitting) {
            formData = state.formData;
            // Sử dụng currentStep và totalSteps đã lưu trữ
          } else if (state is CustomerFormFailure) {
            formData = state.formData;
            // Sử dụng currentStep và totalSteps đã lưu trữ
          }

          // Determine if we should show loading overlay
          final bool isLoading = state is CustomerFormSubmitting;

          return Stack(
            children: [
              // Main Scaffold
              Scaffold(
                appBar: AppNavHeaderExtension.forScreen(
                  title: 'Thêm khách hàng mới',
                  onBack: _onExit,
                ),
                body: AbsorbPointer(
                  absorbing: isLoading,
                  child: Opacity(
                    opacity: isLoading ? 0.6 : 1.0,
                    child: GestureDetector(
                      onTap: () {
                        // Ẩn bàn phím khi tap ra ngoài
                        FocusScope.of(context).unfocus();
                      },
                      child: Column(
                        children: [
                          // Progress Header
                          AddCustomerProgressHeader(
                            currentStep: _currentStep,
                            totalSteps: _totalSteps,
                            stepTitle: _getStepTitle(_currentStep),
                          ),
                          
                          // Form Content
                          Expanded(
                            child: PageView(
                              controller: _pageController,
                              onPageChanged: (index) {
                                // Chỉ trigger event khi user swipe, không phải khi bloc cập nhật
                                if (_lastStep != index) {
                                  context.read<CustomerFormBloc>().add(CustomerFormGoToStep(index));
                                }
                              },
                              children: [
                                BasicInfoStep(
                                  formKey: _formKeys[0]!,
                                  customerData: formData,
                                  cccdQrData: widget.cccdQrData,
                                ),
                                AddressInfoStep(
                                  formKey: _formKeys[1]!,
                                  customerData: formData,
                                ),
                                CareerInfoStep(
                                  formKey: _formKeys[2]!,
                                  customerData: formData,
                                ),
                                ClassificationStep(
                                  formKey: _formKeys[3]!,
                                  customerData: formData,
                                ),
                              ],
                            ),
                          ),
                          
                          // Navigation Bar
                          StepNavigationBar(
                            currentStep: _currentStep,
                            totalSteps: _totalSteps,
                            actionButtonText: _getActionButtonText(_currentStep, _totalSteps),
                            onPrevious: _currentStep > 0 ? _previousStep : null,
                            onNext: _nextStep,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Loading overlay covering entire Scaffold
              if (isLoading)
                Container(
                  color: Colors.black.withValues(alpha: 0.3),
                  child: const Center(
                    child: Card(
                      child: Padding(
                        padding: EdgeInsets.all(24.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text(
                              'Đang thêm khách hàng...',
                              style: TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
} 