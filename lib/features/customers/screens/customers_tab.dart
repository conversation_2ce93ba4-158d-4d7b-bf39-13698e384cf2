import 'dart:async';

import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../shared/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../../../shared/constants/config_types.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/models/region_model.dart';
import '../../../shared/models/branch_model.dart';
import '../../../shared/models/customer_tag_model.dart';
import '../../auth/blocs/master_data_bloc.dart';
import '../../employees/blocs/employee_bloc.dart';
import '../../employees/blocs/employee_event.dart';
import '../../employees/blocs/employee_state.dart';
import '../../employees/models/employee_model.dart';
import '../widgets/index.dart';
import '../blocs/customer_list_bloc.dart';
import '../blocs/customer_list_event.dart';
import '../blocs/customer_list_state.dart';

import 'customer_detail_screen.dart';
import 'add_customer_screen.dart';

class CustomersTab extends StatefulWidget {
  const CustomersTab({super.key});

  @override
  State<CustomersTab> createState() => _CustomersTabState();
}

class _CustomersTabState extends State<CustomersTab> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => MasterDataBloc()),
        BlocProvider(create: (context) => EmployeeBloc()),
      ],
      child: const _CustomersTabContent(),
    );
  }
}

class _CustomersTabContent extends StatefulWidget {
  const _CustomersTabContent();

  @override
  State<_CustomersTabContent> createState() => _CustomersTabContentState();
}

class _CustomersTabContentState extends State<_CustomersTabContent>
    with TickerProviderStateMixin {
  int _selectedFilter = 0;
  DateTimeRange? _dateRange;
  String? _selectedStatus;
  List<RegionModel> _selectedRegions = [];
  List<BranchModel> _selectedBusinessUnits = [];
  EmployeeModel? _selectedStaff;
  List<String> _selectedTags = [];
  String _searchText = '';
  
  // Search debounce timer
  Timer? _searchDebounceTimer;
  static const Duration _searchDebounceDelay = Duration(milliseconds: 500);
  
  // Scroll controller for infinite scroll
  late ScrollController _scrollController;
  
  // Animation controllers
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // Master data
  List<ConfigModel> _customerStatuses = [];
  List<RegionModel> _regions = [];
  List<BranchModel> _branches = [];
  List<CustomerTagModel> _customerTags = [];
  bool _masterDataLoaded = false;
  bool _branchesLoading = false;
  
  // Employee data - chỉ load khi search
  List<EmployeeModel> _employees = [];
  String _employeeSearchTerm = '';
  bool _isSearchingEmployees = false;
  
  // Stream để notify modal về branches updates
  final StreamController<List<BranchModel>> _branchesStreamController = StreamController<List<BranchModel>>.broadcast();
  // Stream để notify modal về employee search updates
  final StreamController<List<EmployeeModel>> _employeeSearchStreamController = StreamController<List<EmployeeModel>>.broadcast();
  
  // Flag to prevent duplicate load more calls
  bool _isLoadingMore = false;
  
  // Debounce timer for scroll events
  Timer? _scrollDebounceTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeScrollController();
    // Load master data và khởi tạo customer list sau khi widget được build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadMasterData();
        context.read<CustomerListBloc>().add(const CustomerListInitialized());
        // Load customer summary with no filters initially
        context.read<CustomerListBloc>().add(const CustomerSummaryRequested());
      }
    });
  }

  void _initializeAnimations() {
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );
    _shimmerController.repeat();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
  }

  void _initializeScrollController() {
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // Debounce scroll events to prevent too many triggers
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      // Check if user scrolled to near the bottom and not already loading
      if (!_isLoadingMore && 
          _scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
        // Trigger load more when user is 200px from bottom
        _onLoadMore();
      }
    });
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _scrollDebounceTimer?.cancel();
    _scrollController.dispose();
    _shimmerController.dispose();
    _fadeController.dispose();
    _branchesStreamController.close();
    _employeeSearchStreamController.close();
    super.dispose();
  }

  List<String> get _allStatuses => _customerStatuses.map((status) => status.label ?? '').where((label) => label.isNotEmpty).toList();

  List<EmployeeModel> get _allStaff => _employees;

  /// Get current employee search results for modal
  List<EmployeeModel> get _currentEmployeeSearchResults => _employeeSearchTerm.isEmpty ? _employees : _employees.where((emp) => 
    emp.fullName?.toLowerCase().contains(_employeeSearchTerm.toLowerCase()) == true ||
    emp.username?.toLowerCase().contains(_employeeSearchTerm.toLowerCase()) == true ||
    emp.email?.toLowerCase().contains(_employeeSearchTerm.toLowerCase()) == true
  ).toList();

  bool get _hasActiveFilters => _dateRange != null || _selectedStatus != null || _selectedRegions.isNotEmpty || _selectedBusinessUnits.isNotEmpty || _selectedStaff != null || _selectedFilter != 0;

  /// Load master data từ API
  void _loadMasterData() {
    // Load customer statuses
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.CUSTOMER_STATUS));
    // Load regions
    context.read<MasterDataBloc>().add(const LoadRegionsEvent());
    // Load customer tags
    context.read<MasterDataBloc>().add(LoadCustomerTagsEvent());
    // Load ALL branches ngay từ đầu (không cần đợi chọn regions)
    // Pass empty list để load all branches
    context.read<MasterDataBloc>().add(const LoadBranchesByRegionsEvent([]));
  }

  // Đã loại bỏ _loadEmployees() - không cần thiết vì chỉ search khi cần

  /// Search employees real-time - chỉ call API khi search
  void _searchEmployees(String searchTerm) {
    setState(() {
      _employeeSearchTerm = searchTerm;
      _isSearchingEmployees = true;
    });
    
    // If search term is empty, clear results
    if (searchTerm.isEmpty) {
      setState(() {
        _employees = [];
        _isSearchingEmployees = false;
      });
      _employeeSearchStreamController.add([]);
             } else {
           // Call API với search term
           context.read<EmployeeBloc>().add(SearchEmployees(searchTerm));
         }
  }

  /// Load branches cho các regions đã chọn hoặc load all branches
  void _loadBranchesForRegions(List<String> regionIds) {
    if (regionIds.isEmpty) {
      // Nếu không có region nào được chọn, load ALL branches
      setState(() {
        _branchesLoading = true;
      });
      // Load all branches (API sẽ trả về tất cả branches)
      context.read<MasterDataBloc>().add(const LoadBranchesByRegionsEvent([]));
      return;
    }

    // Sử dụng trực tiếp region IDs đã được truyền
    setState(() {
      _branchesLoading = true;
    });
    context.read<MasterDataBloc>().add(LoadBranchesByRegionsEvent(regionIds));
  }

         void _showModernFilterModal() async {
         // Kiểm tra master data đã load chưa
         if (!_masterDataLoaded) {
           ScaffoldMessenger.of(context).showSnackBar(
             SnackBar(
               content: Text('Đang tải dữ liệu...'),
               duration: Duration(seconds: 2),
             ),
           );
           return;
         }
         
         // Reset loading state nếu có branches rồi
         if (_branches.isNotEmpty && _branchesLoading) {
           setState(() {
             _branchesLoading = false;
           });
         }
         
         // ALWAYS emit current branches state để modal biết
         _branchesStreamController.add(_branches);

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
                         return ModernCustomerFilterModal(
               initialDateRange: _dateRange,
               initialStatus: _selectedStatus,
               initialRegions: _selectedRegions.isEmpty ? null : _selectedRegions,
               initialBusinessUnits: _selectedBusinessUnits.isEmpty ? null : _selectedBusinessUnits,
               initialStaff: _selectedStaff,
               initialTags: _selectedTags.isEmpty ? null : _selectedTags,
               availableStatuses: _customerStatuses,
               availableRegions: _regions,
               availableBusinessUnits: _branches,
                             availableStaff: _allStaff,
              availableCustomerTags: _customerTags,
              branchesLoading: _branchesLoading,
              branchesStream: _branchesStreamController.stream,
              employeeSearchStream: _employeeSearchStreamController.stream,
              onEmployeeSearch: _searchEmployees,
              isSearchingEmployees: _isSearchingEmployees,
                             onRegionsChanged: (regions) {
                // Extract region IDs từ RegionModel objects
                final regionIds = regions.map((region) => region.id).toList();
                
                // Update selected regions ONLY if this is not from auto-mapping
                // (Modal sẽ handle việc update _selectedRegions internally)
                setState(() {
                  _selectedRegions = regions;
                });
                
                // Load branches cho regions đã chọn để filter
                // So sánh region IDs thay vì object references
                final currentRegionIds = _selectedRegions.map((r) => r.id).toSet();
                final newRegionIds = regionIds.toSet();
                
                // So sánh size trước, nếu khác size thì chắc chắn khác nhau
                if (_branches.isEmpty || 
                    currentRegionIds.length != newRegionIds.length ||
                    !currentRegionIds.containsAll(newRegionIds)) {
                  _loadBranchesForRegions(regionIds);
                }
              },
              onApply: (dateRange, status, regions, businessUnits, staff, tags) {
                if (!mounted) return;
                setState(() {
                  _dateRange = dateRange;
                  _selectedStatus = status;
                  _selectedRegions = regions ?? [];
                  _selectedBusinessUnits = businessUnits ?? [];
                  _selectedStaff = staff;
                  _selectedTags = tags ?? [];
                  // Cập nhật selectedFilter nếu có status được chọn
                  if (status != null) {
                    // Tìm status label từ status code để cập nhật UI
                    final statusModel = _customerStatuses.firstWhere(
                      (s) => s.code == status,
                      orElse: () => ConfigModel(),
                    );
                    final statusLabel = statusModel.label ?? status;
                    final allFilters = ['Tất cả', ..._allStatuses];
                    final index = allFilters.indexOf(statusLabel);
                    _selectedFilter = index >= 0 ? index : 0;
                  } else {
                    _selectedFilter = 0; // Reset về "Tất cả" nếu không có status
                  }
                });
                
                // Apply combined search và filters (includes current search text)
                _applySearchAndFilters();
              },
            );
          },
        );
      },
    );
  }

  void _clearAllFilters() {
    // Cancel any pending search
    _searchDebounceTimer?.cancel();
    
    setState(() {
      _selectedFilter = 0;
      _dateRange = null;
      _selectedStatus = null;
      _selectedRegions.clear();
      _selectedBusinessUnits.clear();
      _selectedStaff = null;
      _selectedTags.clear();
      _searchText = '';
    });
    // Apply cleared state (will load all data)
    _applySearchAndFilters();
  }

  void _clearFiltersOnly() {
    // Clear only filters, keep search text
    setState(() {
      _selectedFilter = 0;
      _dateRange = null;
      _selectedStatus = null;
      _selectedRegions.clear();
      _selectedBusinessUnits.clear();
      _selectedStaff = null;
      _selectedTags.clear();
      // Keep _searchText unchanged
    });
    // Apply with current search text but no filters
    _applySearchAndFilters();
  }

  void _onSearchChanged(String text) {
    // Cancel previous timer first
    _searchDebounceTimer?.cancel();
    
    // Update local state immediately without setState to avoid rebuild
    _searchText = text;
    
    // Schedule search with debounce - this will trigger BlocBuilder rebuild
    // but our search field is outside BlocBuilder so keyboard stays
    _searchDebounceTimer = Timer(_searchDebounceDelay, () {
      if (!mounted) return;
      
      // Apply search với current filters (combined approach)
      _applySearchAndFilters();
    });
  }

  void _onFilterChanged(String filterStatus) {
    final allFilters = ['Tất cả', ..._allStatuses];
    final index = allFilters.indexOf(filterStatus);
    setState(() => _selectedFilter = index >= 0 ? index : 0);
    
    if (index > 0) {
      // Tìm status code từ status label để truyền lên API
      final statusModel = _customerStatuses.firstWhere(
        (s) => s.label == filterStatus,
        orElse: () => ConfigModel(),
      );
      final statusCode = statusModel.code ?? filterStatus;
      
      // Cập nhật _selectedStatus với code để modal hiển thị đúng
      setState(() {
        _selectedStatus = statusCode;
      });
    } else {
      // Reset status khi chọn "Tất cả"
      setState(() {
        _selectedStatus = null;
      });
    }
    
    // Apply combined search và filters
    _applySearchAndFilters();
  }

  /// Apply search text với current filters simultaneously
  void _applySearchAndFilters() {
    // If no search and no filters, load all data
    if (_searchText.isEmpty && 
        _selectedStatus == null && 
        _dateRange == null && 
        _selectedRegions.isEmpty && 
        _selectedBusinessUnits.isEmpty && 
        _selectedStaff == null && 
        _selectedTags.isEmpty) {
      context.read<CustomerListBloc>().add(const CustomerListInitialized());
      // Also load summary with no filters (show all data summary)
      context.read<CustomerListBloc>().add(const CustomerSummaryRequested());
      return;
    }

    // Extract IDs for API
    final regionIds = _selectedRegions.map((r) => r.id).toList();
    final branchIds = _selectedBusinessUnits.map((b) => b.id).toList();
    final staffIds = _selectedStaff?.cifNo != null ? [_selectedStaff!.cifNo!] : null;
    final location = _selectedRegions.map((r) => r.name).join(', ');

    // Combine search với all current filters
    context.read<CustomerListBloc>().add(CustomerListFiltered(
      status: _selectedStatus,
      location: location.isEmpty ? null : location,
      fromDate: _dateRange?.start,
      toDate: _dateRange?.end,
      regionIds: regionIds.isEmpty ? null : regionIds,
      branchIds: branchIds.isEmpty ? null : branchIds,
      staffIds: staffIds,
      customerTagIds: _selectedTags.isEmpty ? null : _selectedTags,
      searchQuery: _searchText.isEmpty ? null : _searchText, // ← COMBINED!
    ));
    
    // Also load customer summary with same filters
    context.read<CustomerListBloc>().add(CustomerSummaryRequested(
      status: _selectedStatus,
      location: location.isEmpty ? null : location,
      fromDate: _dateRange?.start,
      toDate: _dateRange?.end,
      regionIds: regionIds.isEmpty ? null : regionIds,
      branchIds: branchIds.isEmpty ? null : branchIds,
      staffIds: staffIds,
      customerTagIds: _selectedTags.isEmpty ? null : _selectedTags,
      searchQuery: _searchText.isEmpty ? null : _searchText,
    ));
  }

  Future<void> _onRefresh() async {
    context.read<CustomerListBloc>().add(const CustomerListRefreshed());
    // Also refresh summary data with current filters
    _refreshSummaryWithCurrentFilters();
    // Không refresh employees - chỉ load khi search
  }

  /// Refresh summary với current filters
  void _refreshSummaryWithCurrentFilters() {
    // Extract current filters
    final regionIds = _selectedRegions.map((r) => r.id).toList();
    final branchIds = _selectedBusinessUnits.map((b) => b.id).toList();
    final staffIds = _selectedStaff?.cifNo != null ? [_selectedStaff!.cifNo!] : null;
    final location = _selectedRegions.map((r) => r.name).join(', ');

    // Load summary with current filters or empty if no filters
    context.read<CustomerListBloc>().add(CustomerSummaryRequested(
      status: _selectedStatus,
      location: location.isEmpty ? null : location,
      fromDate: _dateRange?.start,
      toDate: _dateRange?.end,
      regionIds: regionIds.isEmpty ? null : regionIds,
      branchIds: branchIds.isEmpty ? null : branchIds,
      staffIds: staffIds,
      customerTagIds: _selectedTags.isEmpty ? null : _selectedTags,
      searchQuery: _searchText.isEmpty ? null : _searchText,
    ));
  }

  void _onLoadMore() {
    // Prevent duplicate calls
    if (_isLoadingMore) return;
    
    // Check if we can load more from current state
    final currentState = context.read<CustomerListBloc>().state;
    if (currentState is CustomerListSuccess && !currentState.hasMore) {
      return; // No more data to load
    }
    
    _isLoadingMore = true;
    context.read<CustomerListBloc>().add(const CustomerListLoadMore(pLimit: 10));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<MasterDataBloc, MasterDataState>(
          listener: (context, state) {
            if (state is MasterDataLoaded) {
              // Update customer statuses từ configsByGroup
              final customerStatuses = state.configsByGroup[ConfigTypes.CUSTOMER_STATUS] ?? [];
              setState(() {
                _customerStatuses = customerStatuses;
                _regions = state.regions;
                _customerTags = state.customerTags;
                // Check if basic master data is loaded (statuses + regions)
                _masterDataLoaded = _customerStatuses.isNotEmpty && _regions.isNotEmpty;
              });
              
              // Kiểm tra xem có branches không để update
              if (state.branches.isNotEmpty) {
                setState(() {
                  _branches = state.branches;
                  _branchesLoading = false;
                });
                
                // Emit branches mới qua stream - ALWAYS emit để modal update
                _branchesStreamController.add(_branches);
              } else {
                // Nếu API trả về empty, cũng emit để modal biết
                setState(() {
                  _branchesLoading = false;
                });
                _branchesStreamController.add([]);
              }
            } else if (state is MasterDataLoading && state.type == 'branches') {
              setState(() {
                _branchesLoading = true;
              });
              // KHÔNG emit empty list khi loading để tránh confuse modal
              // Modal sẽ dựa vào widget.branchesLoading để hiển thị loading state
            }
          },
        ),
                BlocListener<EmployeeBloc, EmployeeState>(
          listener: (context, state) {
            if (state is EmployeesSearchLoaded) {
              setState(() {
                _employees = state.employees;
                _isSearchingEmployees = false;
              });

              // Emit search results to modal
              _employeeSearchStreamController.add(_currentEmployeeSearchResults);
            } else if (state is EmployeeError) {
              setState(() {
                _isSearchingEmployees = false;
              });
              // Handle employee loading error
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Lỗi tìm kiếm nhân viên: ${state.message}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
        BlocListener<CustomerListBloc, CustomerListState>(
          listener: (context, state) {
            // Reset loading more flag when load more completes
            if (state is CustomerListSuccess || state is CustomerListFailure) {
              _isLoadingMore = false;
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: AppNavHeaderExtension.forTab(
          title: 'Khách hàng',
          actions: [
            Container(
              margin: const EdgeInsets.only(right: AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: Icon(
                  TablerIcons.user_plus,
                  color: Colors.white,
                  size: AppDimensions.iconM,
                ),
                tooltip: 'Thêm khách hàng mới',
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final bloc = context.read<CustomerListBloc>();
                  final result = await navigator.push(
                    MaterialPageRoute(
                      builder: (context) => const AddCustomerScreen(),
                    ),
                  );
                  if (result == true && mounted) {
                    // Refresh list after adding new customer successfully
                    bloc.add(const CustomerListInitialized());
                  }
                },
              ),
            ),
          ],
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: CustomScrollView(
        controller: _scrollController, // Add scroll controller for infinite scroll
        slivers: [
          // Customer Summary with targeted BlocBuilder
          SliverToBoxAdapter(
            child: _buildCustomerSummary(),
          ),

          // Quick Actions - Static
          const SliverToBoxAdapter(
            child: CustomerQuickActions(),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingL),
          ),

          // Search Bar - Static (outside BlocBuilder - preserves keyboard)
          SliverToBoxAdapter(
            child: EnhancedCustomerSearch(
              key: const ValueKey('persistent_search'),
              searchText: _searchText,
              onSearchChanged: _onSearchChanged,
              onAdvancedFilter: _showModernFilterModal,
              hasActiveFilters: _hasActiveFilters,
            ),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingM),
          ),

          // Filter Bar - Static (outside BlocBuilder)
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
              child: CustomerFilterBar(
                key: const ValueKey('persistent_filter'),
                selectedFilter: _selectedFilter,
                onFilter: _onFilterChanged,
                filters: ['Tất cả', ..._allStatuses],
              ),
            ),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingM),
          ),

          // Customer List with targeted BlocBuilder
          _buildCustomerListSection(),

          // Error state handled inside Customer List BlocBuilder
          _buildErrorStateSection(),

          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: AppDimensions.spacingXL),
          ),
        ],
      ),
    );
  }

  /// Targeted BlocBuilder for CustomerSummarySection only - ONLY handles summary states
  Widget _buildCustomerSummary() {
    return BlocBuilder<CustomerListBloc, CustomerListState>(
      buildWhen: (previous, current) {
        // ONLY rebuild for summary-specific states - completely isolate from customer list states
        return current is CustomerSummaryLoading ||
               current is CustomerSummarySuccess ||
               current is CustomerSummaryFailure;
      },
      builder: (context, state) {
        // Handle ONLY customer summary states - no fallback to customer list states
        if (state is CustomerSummaryLoading) {
          return CustomerSummarySection(
            isLoading: true,
            totalCount: 0,
            activeCount: 0,
            prospectCount: 0,
            completedCount: 0,
            totalRevenue: '0 VND',
          );
        } else if (state is CustomerSummarySuccess) {
          final summaryData = state.summaryData;

          // Trigger fade animation when data loads
          if (_fadeAnimation.value == 0.0) {
            _fadeController.forward();
          }

          return AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: CustomerSummarySection(
                  isLoading: false,
                  totalCount: summaryData.totalCustomers,
                  activeCount: summaryData.caringCustomers,
                  prospectCount: summaryData.potentialCustomers,
                  completedCount: summaryData.transactedCustomers,
                  totalRevenue: _formatCurrency(summaryData.totalRevenue),
                ),
              );
            },
          );
        } else if (state is CustomerSummaryFailure) {
          // Log error for debugging
          debugPrint('CustomerSummaryFailure: ${state.error}');

          // Show error state with zero data
          return CustomerSummarySection(
            isLoading: false,
            totalCount: 0,
            activeCount: 0,
            prospectCount: 0,
            completedCount: 0,
            totalRevenue: '0 VND',
          );
        }

        // Default state - show loading (initial state)
        return CustomerSummarySection(
          isLoading: true,
          totalCount: 0,
          activeCount: 0,
          prospectCount: 0,
          completedCount: 0,
          totalRevenue: '0 VND',
        );
      },
    );
  }

  /// Targeted BlocBuilder for Customer List section only
  Widget _buildCustomerListSection() {
    return BlocBuilder<CustomerListBloc, CustomerListState>(
      buildWhen: (previous, current) {
        // SKIP summary states - only handle customer list states
        if (current is CustomerSummaryLoading ||
            current is CustomerSummarySuccess ||
            current is CustomerSummaryFailure) {
          return false;
        }

        // Always rebuild for different state types (Loading -> Success, etc.)
        if (previous.runtimeType != current.runtimeType) {
          return true;
        }
        
        // For CustomerListSuccess states, rebuild if customer data or pagination changes
        if (current is CustomerListSuccess && previous is CustomerListSuccess) {
          return current.customers.length != previous.customers.length ||
                 current.hasMore != previous.hasMore ||
                 current.customers != previous.customers; // Check actual data changes
        }
        
        // For CustomerListRefreshing states, rebuild if customer data changes
        if (current is CustomerListRefreshing && previous is CustomerListRefreshing) {
          return current.customers.length != previous.customers.length ||
                 current.customers != previous.customers;
        }

        // For CustomerListLoadingMore states, rebuild if customer data changes
        if (current is CustomerListLoadingMore && previous is CustomerListLoadingMore) {
          return current.customers.length != previous.customers.length ||
                 current.customers != previous.customers;
        }
        
        return true;
      },
      builder: (context, state) {
        if (state is CustomerListLoading) {
          // Show skeleton for loading
          return SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildCustomerCardSkeleton(),
              childCount: 5,
            ),
          );
        } else if (state is CustomerListSuccess) {
          final customers = state.customers;
          
          if (customers.isEmpty) {
            return SliverFillRemaining(
              child: EnhancedCustomerEmptyState(
                searchText: _searchText.isNotEmpty ? _searchText : null,
                hasActiveFilters: _hasActiveFilters,
                onClearFilters: _searchText.isNotEmpty ? _clearFiltersOnly : _clearAllFilters,
                onAddCustomer: () async {
                  final navigator = Navigator.of(context);
                  final bloc = context.read<CustomerListBloc>();
                  final result = await navigator.push(
                    MaterialPageRoute(
                      builder: (context) => const AddCustomerScreen(),
                    ),
                  );
                  if (result == true && mounted) {
                    bloc.add(const CustomerListInitialized());
                  }
                },
                onImportContacts: () {
                  // TODO: Navigate to import contacts
                },
              ),
            );
          }
          
          return SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                // Add small loading indicator at the end if hasMore
                if (index == customers.length) {
                  if (state.hasMore) {
                    return Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Center(
                        child: SizedBox(
                          height: 24,
                          width: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: AppColors.kienlongSkyBlue,
                          ),
                        ),
                      ),
                    );
                  }
                  return const SizedBox(height: AppDimensions.spacingXL);
                }
                
                final customer = customers[index];
                return AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: CustomerCard(
                        customer: customer,
                        onTap: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => CustomerDetailScreen(
                                customerId: customer.id,
                              ),
                            ),
                          );
                        },
                      ),
                    );
                  },
                );
              },
              childCount: customers.length + (state.hasMore ? 1 : 1), // +1 for load more or spacing
            ),
          );
        } else if (state is CustomerListRefreshing) {
          // When refreshing, show loading skeleton for customer list
          // but keep summary with current data
          return SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildCustomerCardSkeleton(),
              childCount: 5, // Show 5 skeleton items while refreshing
            ),
          );
        } else if (state is CustomerListLoadingMore) {
          // When loading more, show current customers + loading indicator at bottom
          final customers = state.customers;
          
          return SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                // Show existing customers
                if (index < customers.length) {
                  final customer = customers[index];
                  return AnimatedBuilder(
                    animation: _fadeAnimation,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: CustomerCard(
                          customer: customer,
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => CustomerDetailScreen(
                                  customerId: customer.id,
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  );
                }
                
                // Show small loading indicator at the bottom
                return Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Center(
                    child: SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppColors.kienlongSkyBlue,
                      ),
                    ),
                  ),
                );
              },
              childCount: customers.length + 1, // +1 for loading indicator
            ),
          );
        } else {
          // Error state - show empty state
          return SliverFillRemaining(
            child: EnhancedCustomerEmptyState(
              searchText: _searchText.isNotEmpty ? _searchText : null,
              hasActiveFilters: _hasActiveFilters,
              onClearFilters: _clearAllFilters,
              onAddCustomer: () async {
                final navigator = Navigator.of(context);
                final bloc = context.read<CustomerListBloc>();
                final result = await navigator.push(
                  MaterialPageRoute(
                    builder: (context) => const AddCustomerScreen(),
                  ),
                );
                if (result == true && mounted) {
                  bloc.add(const CustomerListInitialized());
                }
              },
              onImportContacts: () {
                // TODO: Navigate to import contacts
              },
            ),
          );
        }
      },
    );
  }
  
  /// Targeted BlocBuilder for Error State
  Widget _buildErrorStateSection() {
    return BlocBuilder<CustomerListBloc, CustomerListState>(
      buildWhen: (previous, current) {
        // Only rebuild when entering/leaving customer list error state
        return current is CustomerListFailure || previous is CustomerListFailure;
      },
      builder: (context, state) {
        if (state is CustomerListFailure) {
          return SliverFillRemaining(
            child: _buildErrorState(state),
          );
        }
        return const SliverToBoxAdapter(child: SizedBox.shrink());
      },
    );
  }

  Widget _buildErrorState(CustomerListFailure state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            TablerIcons.alert_circle,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            'Có lỗi xảy ra',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            state.error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingL),
          ElevatedButton(
            onPressed: () {
              context.read<CustomerListBloc>().add(const CustomerListInitialized());
            },
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }



  // Skeleton widgets
  Widget _buildCustomerCardSkeleton() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          height: 80,
          margin: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.spacingS,
          ),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: _buildShimmerEffect(),
        );
      },
    );
  }

  Widget _buildShimmerEffect() {
    final shimmerValue = _shimmerAnimation.value;
    final shimmerColor = Colors.grey.withValues(alpha: 0.3);

    return ClipRRect(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(shimmerValue - 1, 0),
            end: Alignment(shimmerValue, 0),
            colors: [
              Colors.transparent,
              shimmerColor,
              Colors.transparent,
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
      ),
    );
  }



  String _formatCurrency(double amount) {
    if (amount >= 1000000000) {
      return '${(amount / 1000000000).toStringAsFixed(1)}B VND';
    } else if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M VND';
    }
    return '${amount.toStringAsFixed(0)} VND';
  }
} 