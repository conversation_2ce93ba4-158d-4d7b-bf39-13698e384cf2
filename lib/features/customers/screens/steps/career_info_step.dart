import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../../auth/blocs/master_data_bloc.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/constants/config_types.dart';

class CareerInfoStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> customerData;

  const CareerInfoStep({
    super.key,
    required this.formKey,
    required this.customerData,
  });

  @override
  State<CareerInfoStep> createState() => _CareerInfoStepState();
}

class _CareerInfoStepState extends State<CareerInfoStep> {
  late TextEditingController _occupationController;
  late TextEditingController _workplaceController;
  late TextEditingController _monthlyIncomeController;

  List<ConfigModel> _workExperienceOptions = [];

  final List<String> _occupationSuggestions = [
    'Nhân viên văn phòng',
    'Giáo viên',
    'Bác sĩ',
    'Kỹ sư',
    'Kinh doanh',
    'Quản lý',
    'Freelancer',
    'Doanh nhân',
    'Nghỉ hưu',
    'Sinh viên',
    'Khác',
  ];

  @override
  void initState() {
    super.initState();
    _occupationController = TextEditingController(text: widget.customerData['occupation']);
    _workplaceController = TextEditingController(text: widget.customerData['workplace']);
    _monthlyIncomeController = TextEditingController(text: widget.customerData['monthlyIncome']);

    _occupationController.addListener(() {
      widget.customerData['occupation'] = _occupationController.text;
    });
    _workplaceController.addListener(() {
      widget.customerData['workplace'] = _workplaceController.text;
    });
    _monthlyIncomeController.addListener(() {
      widget.customerData['monthlyIncome'] = _monthlyIncomeController.text;
    });

    // Load work experience options when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MasterDataBloc>().add(LoadConfigEvent(ConfigTypes.WORK_EXPERIENCE));
    });
  }

  @override
  void dispose() {
    _occupationController.dispose();
    _workplaceController.dispose();
    _monthlyIncomeController.dispose();
    super.dispose();
  }

  void _selectOccupation(String occupation) {
    _occupationController.text = occupation;
    widget.customerData['occupation'] = occupation;
  }

  String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    
    // Remove all non-digits
    String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    // Handle large numbers safely without parsing to int
    // This avoids int overflow issues with very large numbers
    String formatted = cleanValue.replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
    
    return formatted;
  }

  void _onIncomeChanged(String value) {
    String cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    String formatted = _formatCurrency(cleanValue);
    
    if (formatted != value) {
      _monthlyIncomeController.value = TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: formatted.length),
      );
    }
    
    widget.customerData['monthlyIncome'] = cleanValue;
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.briefcase,
                    color: AppColors.kienlongSkyBlue,
                    size: AppDimensions.iconM,
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      'Thông tin nghề nghiệp giúp đánh giá khả năng tài chính của khách hàng.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongSkyBlue,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Occupation Field
            Text(
              'Nghề nghiệp/Công việc',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _occupationController,
              decoration: InputDecoration(
                hintText: 'Nhập nghề nghiệp hiện tại',
                prefixIcon: Icon(
                  TablerIcons.user_check,
                  color: AppColors.textSecondary,
                ),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập nghề nghiệp hiện tại';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingM),

            // Occupation Quick Select
            Text(
              'Hoặc chọn nhanh:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Wrap(
              spacing: AppDimensions.spacingS,
              runSpacing: AppDimensions.spacingS,
              children: _occupationSuggestions.map((occupation) {
                return InkWell(
                  onTap: () => _selectOccupation(occupation),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      border: Border.all(
                        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                      ),
                    ),
                    child: Text(
                      occupation,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.kienlongSkyBlue,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Workplace Field
            Text(
              'Nơi làm việc',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _workplaceController,
              decoration: InputDecoration(
                hintText: 'Tên công ty/tổ chức (không bắt buộc)',
                prefixIcon: Icon(
                  TablerIcons.building,
                  color: AppColors.textSecondary,
                ),
              ),
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Monthly Income Field
            Text(
              'Thu nhập hàng tháng',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _monthlyIncomeController,
              decoration: InputDecoration(
                hintText: 'Nhập thu nhập hàng tháng',
                suffixText: 'VND',
                suffixStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChanged: _onIncomeChanged,
            ),

            SizedBox(height: AppDimensions.spacingM),

            // Income Range Helper
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.warning.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        TablerIcons.info_circle,
                        color: AppColors.warning,
                        size: AppDimensions.iconS,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Text(
                        'Mức thu nhập thường gặp:',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    '• Dưới 10 triệu: Thu nhập thấp\n'
                    '• 10-30 triệu: Thu nhập trung bình\n'
                    '• 30-50 triệu: Thu nhập khá\n'
                    '• Trên 50 triệu: Thu nhập cao',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.warning,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Work Experience Field
            Text(
              'Kinh nghiệm làm việc',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            BlocBuilder<MasterDataBloc, MasterDataState>(
              builder: (context, state) {
                // Check new composite state first
                if (state is MasterDataLoaded) {
                  final configs = state.configsByGroup[ConfigTypes.WORK_EXPERIENCE];
                  if (configs != null) {
                    _workExperienceOptions = configs;
                  }
                } 
                // Fallback to legacy state for backward compatibility
                else if (state is ConfigLoaded && state.groupCode == ConfigTypes.WORK_EXPERIENCE) {
                  _workExperienceOptions = state.configs;
                }

                return DropdownButtonFormField<String>(
                  value: (widget.customerData['workExperience']?.toString().isEmpty ?? true) ? null : widget.customerData['workExperience'],
                  decoration: InputDecoration(
                    hintText: state is MasterDataLoading ? 'Đang tải...' : 'Chọn số năm kinh nghiệm',
                    prefixIcon: Icon(
                      TablerIcons.clock,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  items: _workExperienceOptions.map((ConfigModel config) {
                    return DropdownMenuItem<String>(
                      value: config.code, // Lưu code thay vì label
                      child: Text(config.label ?? ''),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    setState(() {
                      widget.customerData['workExperience'] = value ?? '';
                    });
                  },
                );
              },
            ),

            SizedBox(height: AppDimensions.spacingXL),
          ],
        ),
      ),
    );
  }
} 