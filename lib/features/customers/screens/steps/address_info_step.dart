import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../../auth/blocs/master_data_bloc.dart';
import '../../../../shared/models/province_model.dart';
import '../../../../shared/models/ward_model.dart';

class AddressInfoStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> customerData;

  const AddressInfoStep({
    super.key,
    required this.formKey,
    required this.customerData,
  });

  @override
  State<AddressInfoStep> createState() => _AddressInfoStepState();
}

class _AddressInfoStepState extends State<AddressInfoStep> {
  late TextEditingController _permanentAddressController;
  late TextEditingController _currentAddressController;
  List<ProvinceModel> _provinces = []; // Lưu trữ danh sách provinces

  @override
  void initState() {
    super.initState();
    _permanentAddressController = TextEditingController(
      text: widget.customerData['permanentAddress'],
    );
    _currentAddressController = TextEditingController(
      text: widget.customerData['currentAddress'],
    );

    _permanentAddressController.addListener(() {
      widget.customerData['permanentAddress'] =
          _permanentAddressController.text;
    });
    _currentAddressController.addListener(() {
      widget.customerData['currentAddress'] = _currentAddressController.text;
    });

    // Load provinces when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MasterDataBloc>().add(const LoadProvincesEvent());
    });
  }

  @override
  void dispose() {
    _permanentAddressController.dispose();
    _currentAddressController.dispose();
    super.dispose();
  }

  void _onProvinceChanged(String? provinceId) {
    debugPrint('_onProvinceChanged called with: $provinceId');

    setState(() {
      widget.customerData['province'] = provinceId ?? '';
      widget.customerData['ward'] = null; // Reset ward khi đổi tỉnh
    });

    debugPrint(
      'After setState - province: ${widget.customerData['province']}, ward: ${widget.customerData['ward']}',
    );

    // Load wards for selected province
    if (provinceId != null && provinceId.isNotEmpty) {
      debugPrint('Loading wards for province: $provinceId');
      context.read<MasterDataBloc>().add(LoadWardsEvent(provinceId));
    }
  }

  void _onWardChanged(String? wardId) {
    setState(() {
      widget.customerData['ward'] = wardId;
    });
  }

  void _onSameAddressChanged(bool? value) {
    setState(() {
      widget.customerData['sameAddress'] = value ?? false;
      if (value == true) {
        _currentAddressController.text = _permanentAddressController.text;
        widget.customerData['currentAddress'] =
            widget.customerData['permanentAddress'];
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.map_pin,
                    color: AppColors.kienlongSkyBlue,
                    size: AppDimensions.iconM,
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      'Nhập thông tin địa chỉ liên hệ của khách hàng.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongSkyBlue,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Province Dropdown
            Text(
              'Tỉnh/Thành phố',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: AppDimensions.spacingS),
            BlocBuilder<MasterDataBloc, MasterDataState>(
              buildWhen: (previous, current) {
                // Chỉ rebuild khi provinces được load hoặc có lỗi
                return current is MasterDataLoaded ||
                    current is ProvincesLoaded ||
                    current is MasterDataError && current.type == 'provinces' ||
                    current is MasterDataLoading && current.type == 'provinces';
              },
              builder: (context, state) {
                // Cập nhật danh sách provinces khi state thay đổi
                if (state is MasterDataLoaded) {
                  _provinces = state.provinces;
                } else if (state is ProvincesLoaded) {
                  _provinces = state.provinces;
                }

                // Debug: In ra giá trị để kiểm tra
                debugPrint(
                  'Province dropdown - customerData[province]: ${widget.customerData['province']}',
                );
                debugPrint(
                  'Province dropdown - _provinces count: ${_provinces.length}',
                );
                debugPrint(
                  'Province dropdown - state type: ${state.runtimeType}',
                );

                if (state is MasterDataError && state.type == 'provinces') {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DropdownButtonFormField<String>(
                        value: null,
                        decoration: InputDecoration(
                          hintText: 'Lỗi tải dữ liệu',
                          prefixIcon: Icon(
                            TablerIcons.building_skyscraper,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        items: [],
                        onChanged: null,
                      ),
                      SizedBox(height: AppDimensions.spacingS),
                      Text(
                        'Không thể tải danh sách tỉnh/thành',
                        style: Theme.of(
                          context,
                        ).textTheme.bodySmall?.copyWith(color: AppColors.error),
                      ),
                    ],
                  );
                }

                return DropdownButtonFormField<String>(
                  value:
                      widget.customerData['province']?.toString().isNotEmpty ==
                          true
                      ? widget.customerData['province']
                      : null,
                  decoration: InputDecoration(
                    hintText:
                        (state is MasterDataLoading &&
                            state.type == 'provinces')
                        ? 'Đang tải...'
                        : 'Chọn tỉnh/thành phố',
                    prefixIcon: Icon(
                      TablerIcons.building_skyscraper,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  items: _provinces.map((province) {
                    return DropdownMenuItem<String>(
                      value: province.id,
                      child: Text(province.name),
                    );
                  }).toList(),
                  onChanged: _onProvinceChanged,
                  validator: null,
                );
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Ward Dropdown
            Text(
              'Phường/Xã',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: AppDimensions.spacingS),
            BlocBuilder<MasterDataBloc, MasterDataState>(
              builder: (context, state) {
                List<WardModel> wards = [];
                bool isLoadingWards = false;
                bool hasError = false;

                // Debug: In ra giá trị để kiểm tra
                debugPrint(
                  'Ward dropdown - customerData[province]: ${widget.customerData['province']}',
                );
                debugPrint(
                  'Ward dropdown - customerData[ward]: ${widget.customerData['ward']}',
                );
                debugPrint('Ward dropdown - state type: ${state.runtimeType}');

                // Check new composite state first
                if (state is MasterDataLoaded) {
                  final wardsForProvince =
                      state.wardsByProvince[widget.customerData['province']];
                  if (wardsForProvince != null) {
                    wards = wardsForProvince;
                    debugPrint(
                      'Ward dropdown - MasterDataLoaded: ${wards.length} wards for province ${widget.customerData['province']}',
                    );
                  }
                }
                // Fallback to legacy state for backward compatibility
                else if (state is WardsLoaded &&
                    state.provinceId == widget.customerData['province']) {
                  wards = state.wards;
                  debugPrint(
                    'Ward dropdown - WardsLoaded: ${wards.length} wards for province ${state.provinceId}',
                  );
                } else if (state is MasterDataLoading &&
                    state.type == 'wards') {
                  isLoadingWards = true;
                  debugPrint('Ward dropdown - Loading wards...');
                } else if (state is MasterDataError && state.type == 'wards') {
                  hasError = true;
                  debugPrint('Ward dropdown - Error loading wards');
                } else if (state is ProvincesLoaded) {
                  debugPrint('Ward dropdown - ProvincesLoaded, no wards yet');
                } else {
                  debugPrint(
                    'Ward dropdown - Other state: ${state.runtimeType}',
                  );
                }

                if (hasError) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DropdownButtonFormField<String>(
                        value: null,
                        decoration: InputDecoration(
                          hintText: 'Lỗi tải dữ liệu',
                          prefixIcon: Icon(
                            TablerIcons.building,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        items: [],
                        onChanged: null,
                      ),
                      SizedBox(height: AppDimensions.spacingS),
                      Text(
                        'Không thể tải danh sách phường/xã',
                        style: Theme.of(
                          context,
                        ).textTheme.bodySmall?.copyWith(color: AppColors.error),
                      ),
                    ],
                  );
                }

                return DropdownButtonFormField<String>(
                  value:
                      widget.customerData['ward']?.toString().isNotEmpty == true
                      ? widget.customerData['ward']
                      : null,
                  decoration: InputDecoration(
                    hintText: isLoadingWards ? 'Đang tải...' : 'Chọn phường/xã',
                    prefixIcon: Icon(
                      TablerIcons.building,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  items: wards.map((ward) {
                    return DropdownMenuItem<String>(
                      value: ward.id,
                      child: Text(ward.name ?? ''),
                    );
                  }).toList(),
                  onChanged:
                      widget.customerData['province']?.toString().isNotEmpty ==
                          true
                      ? _onWardChanged
                      : null,
                  // Ward không bắt buộc
                  validator: null,
                );
              },
            ),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'Địa chỉ thường trú',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _permanentAddressController,
              decoration: InputDecoration(
                hintText: 'Số nhà, tên đường',
                prefixIcon: Icon(
                  TablerIcons.home,
                  color: AppColors.textSecondary,
                ),
              ),
              maxLines: 2,
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Same Address Checkbox
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: CheckboxListTile(
                title: Text(
                  'Địa chỉ hiện tại giống thường trú',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                value: widget.customerData['sameAddress'],
                onChanged: _onSameAddressChanged,
                activeColor: AppColors.kienlongOrange,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
              ),
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Current Address (if different)
            if (!widget.customerData['sameAddress']) ...[
              Text(
                'Địa chỉ hiện tại',
                style: Theme.of(
                  context,
                ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
              ),
              SizedBox(height: AppDimensions.spacingS),
              TextFormField(
                controller: _currentAddressController,
                decoration: InputDecoration(
                  hintText: 'Nhập địa chỉ hiện tại (nếu khác thường trú)',
                  prefixIcon: Icon(
                    TablerIcons.map_pin,
                    color: AppColors.textSecondary,
                  ),
                ),
                maxLines: 2,
              ),
            ],

            SizedBox(height: AppDimensions.spacingXL),
          ],
        ),
      ),
    );
  }
}
