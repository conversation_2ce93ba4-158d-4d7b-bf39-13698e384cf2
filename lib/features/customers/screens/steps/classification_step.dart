import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../../auth/blocs/master_data_bloc.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/models/customer_tag_model.dart';
import '../../../../shared/utils/color_utils.dart';
import '../../../../shared/constants/config_types.dart';

class ClassificationStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> customerData;

  const ClassificationStep({
    super.key,
    required this.formKey,
    required this.customerData,
  });

  @override
  State<ClassificationStep> createState() => _ClassificationStepState();
}

class _ClassificationStepState extends State<ClassificationStep> {
  late TextEditingController _notesController;

  List<ConfigModel> _statusOptions = [];
  List<ConfigModel> _sourceOptions = [];
  List<CustomerTagModel> _availableTags = [];

     Map<String, Color> get _tagColors => {
     'VIP': AppColors.error,
     'Premium': AppColors.kienlongOrange,
     'Mới': AppColors.success,
     'Ưu tiên': AppColors.warning,
     'Quan trọng': AppColors.kienlongSkyBlue,
     'Tiềm năng cao': AppColors.kienlongDarkBlue,
     'Cần theo dõi': AppColors.textSecondary,
     'Nóng': AppColors.error,
   };

  Map<String, IconData> get _statusIcons => {
    'Tiềm năng': TablerIcons.eye,
    'Đang chăm sóc': TablerIcons.heart,
    'Đã giao dịch': TablerIcons.check,
  };

  /// Get status color from ConfigModel value field using ColorUtils
  Color _getStatusColor(ConfigModel status) {
    // Use color from API value field if available
    if (status.value != null && status.value!.isNotEmpty) {
      return ColorUtils.hexToColor(status.value!);
    }
    
    // Fallback to predefined colors based on status label
    final fallbackColors = {
      'Tiềm năng': AppColors.warning,
      'Đang chăm sóc': AppColors.kienlongSkyBlue,
      'Đã giao dịch': AppColors.success,
    };
    
    return fallbackColors[status.label ?? ''] ?? AppColors.kienlongDarkBlue;
  }

  @override
  void initState() {
    super.initState();
    _notesController = TextEditingController(text: widget.customerData['notes']);

    _notesController.addListener(() {
      widget.customerData['notes'] = _notesController.text;
    });

    // Initialize with default values if empty
    if (widget.customerData['status'] == null || widget.customerData['status'].toString().isEmpty) {
      widget.customerData['status'] = 'POTENTIAL';
    }
    if (widget.customerData['tags'] == null || widget.customerData['tags'] is! List) {
      widget.customerData['tags'] = <String>[];
    }

    // Load master data when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MasterDataBloc>().add(LoadConfigEvent(ConfigTypes.CUSTOMER_STATUS));
      context.read<MasterDataBloc>().add(LoadConfigEvent(ConfigTypes.CUSTOMER_SOURCE));
      context.read<MasterDataBloc>().add(LoadCustomerTagsEvent());
    });
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  void _toggleTag(String tagId) {
    setState(() {
      final tagsData = widget.customerData['tags'];
      List<String> currentTags = tagsData is List ? List<String>.from(tagsData) : <String>[];
      if (currentTags.contains(tagId)) {
        currentTags.remove(tagId);
      } else {
        currentTags.add(tagId);
      }
      widget.customerData['tags'] = currentTags;
    });
  }

  bool _isTagSelected(String tagId) {
    final tagsData = widget.customerData['tags'];
    List<String> currentTags = tagsData is List ? List<String>.from(tagsData) : <String>[];
    return currentTags.contains(tagId);
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.tags,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconM,
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      'Phân loại khách hàng để quản lý và chăm sóc hiệu quả.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongOrange,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Customer Status
            Text(
              'Trạng thái khách hàng *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),
            
            BlocBuilder<MasterDataBloc, MasterDataState>(
              builder: (context, state) {
                // Check new composite state first
                if (state is MasterDataLoaded) {
                  final configs = state.configsByGroup[ConfigTypes.CUSTOMER_STATUS];
                  if (configs != null) {
                    _statusOptions = configs;
                    // Tự động chọn item đầu tiên ngay sau khi API load data về
                    if ((widget.customerData['status'] == null || widget.customerData['status'].toString().isEmpty) && configs.isNotEmpty) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        setState(() {
                          widget.customerData['status'] = configs.first.code;
                        });
                      });
                    }
                  }
                } 
                // Fallback to legacy state for backward compatibility
                else if (state is ConfigLoaded && state.groupCode == ConfigTypes.CUSTOMER_STATUS) {
                  _statusOptions = state.configs;
                  // Tự động chọn item đầu tiên ngay sau khi API load data về
                  if ((widget.customerData['status'] == null || widget.customerData['status'].toString().isEmpty) && state.configs.isNotEmpty) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      setState(() {
                        widget.customerData['status'] = state.configs.first.code;
                      });
                    });
                  }
                }

                return Column(
                  children: _statusOptions.map((status) {
                    final statusLabel = status.label ?? '';
                    final statusCode = status.code ?? '';
                    final isSelected = widget.customerData['status'] == statusCode;
                    final statusColor = _getStatusColor(status);
                    final statusIcon = _statusIcons[statusLabel] ?? TablerIcons.user;
                    return Container(
                      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            widget.customerData['status'] = statusCode; // Lưu code thay vì label
                          });
                        },
                        child: Container(
                          padding: EdgeInsets.all(AppDimensions.paddingM),
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? statusColor.withValues(alpha: 0.1)
                                : Theme.of(context).cardColor,
                            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                            border: Border.all(
                              color: isSelected 
                                  ? statusColor
                                  : AppColors.borderLight,
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(AppDimensions.paddingS),
                                decoration: BoxDecoration(
                                  color: statusColor.withValues(alpha: 0.2),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  statusIcon,
                                  color: statusColor,
                                  size: AppDimensions.iconM,
                                ),
                              ),
                              SizedBox(width: AppDimensions.spacingM),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      statusLabel,
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w600,
                                        color: isSelected ? statusColor : null,
                                      ),
                                    ),
                                    Text(
                                      _getStatusDescription(statusLabel),
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  TablerIcons.check,
                                  color: statusColor,
                                ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                );
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Customer Source
            Text(
              'Nguồn khách hàng *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            BlocBuilder<MasterDataBloc, MasterDataState>(
              builder: (context, state) {
                // Check new composite state first
                if (state is MasterDataLoaded) {
                  final configs = state.configsByGroup[ConfigTypes.CUSTOMER_SOURCE];
                  if (configs != null) {
                    _sourceOptions = configs;
                  }
                } 
                // Fallback to legacy state for backward compatibility
                else if (state is ConfigLoaded && state.groupCode == ConfigTypes.CUSTOMER_SOURCE) {
                  _sourceOptions = state.configs;
                }

                return DropdownButtonFormField<String>(
                  value: (widget.customerData['source']?.toString().isEmpty ?? true) ? null : widget.customerData['source'],
                  decoration: InputDecoration(
                    hintText: state is MasterDataLoading ? 'Đang tải...' : 'Khách hàng đến từ đâu?',
                    prefixIcon: Icon(
                      TablerIcons.users,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  items: _sourceOptions.map((ConfigModel config) {
                    return DropdownMenuItem<String>(
                      value: config.code, // Lưu code thay vì label
                      child: Text(config.label ?? ''),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    setState(() {
                      widget.customerData['source'] = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng chọn nguồn khách hàng';
                    }
                    return null;
                  },
                );
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Tags Section
            Text(
              'Tags (nhãn phân loại)',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Chọn các nhãn để phân loại khách hàng:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),

            BlocBuilder<MasterDataBloc, MasterDataState>(
              builder: (context, state) {
                // Check new composite state first
                if (state is MasterDataLoaded) {
                  _availableTags = state.customerTags;
                } 
                // Fallback to legacy state for backward compatibility
                else if (state is CustomerTagsLoaded) {
                  _availableTags = state.customerTags;
                }

                return Wrap(
                  spacing: AppDimensions.spacingS,
                  runSpacing: AppDimensions.spacingS,
                  children: _availableTags.map((tag) {
                    final isSelected = _isTagSelected(tag.id ?? '');
                    final color = _getTagColor(tag);
                    
                    return InkWell(
                      onTap: () => _toggleTag(tag.id ?? ''), // Lưu tag.id thay vì tag.name
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 200),
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingM,
                          vertical: AppDimensions.paddingS,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? color.withValues(alpha: 0.2)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                          border: Border.all(
                            color: color,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isSelected)
                              Icon(
                                TablerIcons.check,
                                size: AppDimensions.iconS,
                                color: color,
                              ),
                            if (isSelected) SizedBox(width: AppDimensions.spacingXS),
                            Text(
                              tag.name ?? '',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: color,
                                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                );
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Notes Field
            Text(
              'Ghi chú',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                hintText: 'Thông tin bổ sung về khách hàng (không bắt buộc)',
                prefixIcon: Icon(
                  TablerIcons.notes,
                  color: AppColors.textSecondary,
                ),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              maxLength: 500,
            ),

            SizedBox(height: AppDimensions.spacingXL),

            // Summary Card
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                                             Icon(
                         TablerIcons.circle_check,
                         color: AppColors.success,
                         size: AppDimensions.iconM,
                       ),
                      SizedBox(width: AppDimensions.spacingM),
                      Text(
                        'Sẵn sàng lưu khách hàng',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'Bạn đã hoàn thành tất cả thông tin cần thiết. Nhấn "Lưu khách hàng" để thêm vào danh sách.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: AppDimensions.spacingXL),
          ],
        ),
      ),
    );
  }

  String _getStatusDescription(String status) {
    switch (status) {
      case 'Tiềm năng':
        return 'Khách hàng có khả năng sử dụng dịch vụ';
      case 'Đang chăm sóc':
        return 'Đang tư vấn và theo dõi nhu cầu';
      case 'Đã giao dịch':
        return 'Đã sử dụng sản phẩm/dịch vụ';
      default:
        return '';
    }
  }

  /// Get color for tag - prioritize API color, fallback to predefined colors
  Color _getTagColor(CustomerTagModel tag) {
    // Use color from API if available
    if (tag.color != null) {
      return ColorUtils.hexToColor(tag.color!);
    }
    
    // Fallback to predefined colors based on tag name
    return _tagColors[tag.name ?? ''] ?? AppColors.kienlongDarkBlue;
  }
} 