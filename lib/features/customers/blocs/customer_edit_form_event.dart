import 'package:equatable/equatable.dart';
import '../models/customer_model.dart';

abstract class CustomerEditFormEvent extends Equatable {
  const CustomerEditFormEvent();
  @override
  List<Object?> get props => [];
}

class CustomerEditFormInitialized extends CustomerEditFormEvent {
  final CustomerModel customer;
  const CustomerEditFormInitialized(this.customer);
  @override
  List<Object?> get props => [customer];
}

class CustomerEditFormDataUpdated extends CustomerEditFormEvent {
  final Map<String, dynamic> formData;
  const CustomerEditFormDataUpdated(this.formData);
  @override
  List<Object?> get props => [formData];
}

class CustomerEditFormValidated extends CustomerEditFormEvent {
  final Map<String, dynamic> formData;
  const CustomerEditFormValidated(this.formData);
  @override
  List<Object?> get props => [formData];
}

class CustomerEditFormSubmitted extends CustomerEditFormEvent {
  final String customerId;
  final Map<String, dynamic> request;
  const CustomerEditFormSubmitted(this.customerId, this.request);
  @override
  List<Object?> get props => [customerId, request];
}

