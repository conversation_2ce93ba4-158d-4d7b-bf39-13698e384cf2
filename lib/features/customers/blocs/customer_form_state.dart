import 'package:equatable/equatable.dart';
import '../services/customer_service.dart';

/// States cho CustomerFormBloc
abstract class CustomerFormState extends Equatable {
  const CustomerFormState();

  @override
  List<Object?> get props => [];
}

/// State khởi tạo
class CustomerFormInitial extends CustomerFormState {
  const CustomerFormInitial();
}

/// State đang load
class CustomerFormLoading extends CustomerFormState {
  const CustomerFormLoading();
}

/// State form đã sẵn sàng
class CustomerFormReady extends CustomerFormState {
  final Map<String, dynamic> formData;
  final int currentStep;
  final int totalSteps;
  final Map<String, List<String>> validationErrors;
  final bool isStepValid;

  const CustomerFormReady({
    required this.formData,
    required this.currentStep,
    this.totalSteps = 4,
    this.validationErrors = const {},
    this.isStepValid = true,
  });

  @override
  List<Object?> get props => [formData, currentStep, totalSteps, validationErrors, isStepValid];

  CustomerFormReady copyWith({
    Map<String, dynamic>? formData,
    int? currentStep,
    int? totalSteps,
    Map<String, List<String>>? validationErrors,
    bool? isStepValid,
  }) {
    return CustomerFormReady(
      formData: formData ?? this.formData,
      currentStep: currentStep ?? this.currentStep,
      totalSteps: totalSteps ?? this.totalSteps,
      validationErrors: validationErrors ?? this.validationErrors,
      isStepValid: isStepValid ?? this.isStepValid,
    );
  }
}

/// State đang submit
class CustomerFormSubmitting extends CustomerFormState {
  final Map<String, dynamic> formData;

  const CustomerFormSubmitting(this.formData);

  @override
  List<Object?> get props => [formData];
}

/// State submit thành công
class CustomerFormSuccess extends CustomerFormState {
  final bool success;

  const CustomerFormSuccess(this.success);

  @override
  List<Object?> get props => [success];
}

/// State submit thất bại
class CustomerFormFailure extends CustomerFormState {
  final String error;
  final CustomerExceptionType? errorType;
  final Map<String, dynamic> formData;

  const CustomerFormFailure({
    required this.error,
    this.errorType,
    required this.formData,
  });

  @override
  List<Object?> get props => [error, errorType, formData];
}

/// State validation thất bại
class CustomerFormValidationFailure extends CustomerFormState {
  final Map<String, List<String>> validationErrors;
  final Map<String, dynamic> formData;

  const CustomerFormValidationFailure({
    required this.validationErrors,
    required this.formData,
  });

  @override
  List<Object?> get props => [validationErrors, formData];
} 