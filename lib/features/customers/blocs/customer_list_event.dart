import 'package:equatable/equatable.dart';

/// Events cho CustomerListBloc
abstract class CustomerListEvent extends Equatable {
  const CustomerListEvent();

  @override
  List<Object?> get props => [];
}

/// Event khởi tạo danh sách
class CustomerListInitialized extends CustomerListEvent {
  const CustomerListInitialized();
}

/// Event refresh danh sách - chỉ reload data hiện tại
class CustomerListRefreshed extends CustomerListEvent {
  const CustomerListRefreshed();
}

/// Event load more customers - sử dụng current filters từ state
class CustomerListLoadMore extends CustomerListEvent {
  final int pLimit;

  const CustomerListLoadMore({this.pLimit = 10});

  @override
  List<Object?> get props => [pLimit];
}

/// Event filter customers
class CustomerListFiltered extends CustomerListEvent {
  final String? status;
  final String? location;
  final DateTime? fromDate;
  final DateTime? toDate;
  final List<String>? regionIds;
  final List<String>? branchIds;
  final List<String>? staffIds;
  final List<String>? customerTagIds;
  final String? searchQuery;

  const CustomerListFiltered({
    this.status,
    this.location,
    this.fromDate,
    this.toDate,
    this.regionIds,
    this.branchIds,
    this.staffIds,
    this.customerTagIds,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
    status, 
    location, 
    fromDate, 
    toDate, 
    regionIds, 
    branchIds, 
    staffIds, 
    customerTagIds,
    searchQuery,
  ];
}

/// Event search customers
class CustomerListSearched extends CustomerListEvent {
  final String searchQuery;

  const CustomerListSearched(this.searchQuery);

  @override
  List<Object?> get props => [searchQuery];
}

/// Event load customer summary
class CustomerSummaryRequested extends CustomerListEvent {
  final String? status;
  final String? location;
  final DateTime? fromDate;
  final DateTime? toDate;
  final List<String>? regionIds;
  final List<String>? branchIds;
  final List<String>? staffIds;
  final List<String>? customerTagIds;
  final String? searchQuery;

  const CustomerSummaryRequested({
    this.status,
    this.location,
    this.fromDate,
    this.toDate,
    this.regionIds,
    this.branchIds,
    this.staffIds,
    this.customerTagIds,
    this.searchQuery,
  });

  @override
  List<Object?> get props => [
    status, 
    location, 
    fromDate, 
    toDate, 
    regionIds, 
    branchIds, 
    staffIds, 
    customerTagIds,
    searchQuery,
  ];
} 