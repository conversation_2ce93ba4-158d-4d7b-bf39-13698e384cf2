import 'package:flutter_bloc/flutter_bloc.dart';
import '../services/customer_service.dart';
import '../models/customer_model.dart';
import '../models/update_customer_request.dart';
import 'customer_edit_form_event.dart';
import 'customer_edit_form_state.dart';

class CustomerEditFormBloc extends Bloc<CustomerEditFormEvent, CustomerEditFormState> {
  final CustomerService _customerService;

  CustomerEditFormBloc({required CustomerService customerService})
      : _customerService = customerService,
        super(const CustomerEditFormInitial()) {
    on<CustomerEditFormInitialized>(_onInitialized);
    on<CustomerEditFormDataUpdated>(_onDataUpdated);
    on<CustomerEditFormValidated>(_onValidated);
    on<CustomerEditFormSubmitted>(_onSubmitted);
  }

  Future<void> _onInitialized(
    CustomerEditFormInitialized event,
    Emitter<CustomerEditFormState> emit,
  ) async {
    emit(const CustomerEditFormLoading());
    try {
      final formData = _prefillFromCustomer(event.customer);
      emit(CustomerEditFormReady(formData: formData));
    } catch (e) {
      emit(CustomerEditFormFailure(
        error: 'Không thể khởi tạo form chỉnh sửa: $e',
        formData: const {},
      ));
    }
  }

  Future<void> _onDataUpdated(
    CustomerEditFormDataUpdated event,
    Emitter<CustomerEditFormState> emit,
  ) async {
    if (state is CustomerEditFormReady) {
      final current = state as CustomerEditFormReady;
      emit(current.copyWith(formData: event.formData));
    }
  }

  Future<void> _onValidated(
    CustomerEditFormValidated event,
    Emitter<CustomerEditFormState> emit,
  ) async {
    try {
      final errors = _validateFormData(event.formData);
      if (errors.isNotEmpty) {
        emit(CustomerEditFormValidationFailure(
          validationErrors: errors,
          formData: event.formData,
        ));
      } else {
        if (state is CustomerEditFormReady) {
          final current = state as CustomerEditFormReady;
          emit(current.copyWith(
            formData: event.formData,
            validationErrors: const {},
          ));
        }
      }
    } catch (e) {
      emit(CustomerEditFormFailure(
        error: 'Lỗi validation: $e',
        errorType: CustomerExceptionType.validationError,
        formData: event.formData,
      ));
    }
  }

  Future<void> _onSubmitted(
    CustomerEditFormSubmitted event,
    Emitter<CustomerEditFormState> emit,
  ) async {
    // Convert form data to UpdateCustomerRequest
    final updateRequest = _convertFormDataToUpdateRequest(event.request);
    
    emit(CustomerEditFormSubmitting(updateRequest.toJson()));
    try {
      await _customerService.updateCustomer(event.customerId, updateRequest);
      // Chỉ cần biết API call thành công
      emit(CustomerEditFormSuccess(null));
    } on CustomerException catch (e) {
      if (e.type == CustomerExceptionType.validationError) {
        emit(CustomerEditFormValidationFailure(
          validationErrors: _mapValidationMessage(e.message),
          formData: event.request,
        ));
      } else {
        emit(CustomerEditFormFailure(
          error: e.message,
          errorType: e.type,
          formData: event.request,
        ));
      }
          } catch (e) {
        emit(CustomerEditFormFailure(
          error: 'Lỗi không xác định: $e',
          errorType: CustomerExceptionType.unknown,
          formData: event.request,
        ));
      }
  }

  Map<String, dynamic> _prefillFromCustomer(CustomerModel customer) {
    return {
      'name': customer.fullName,
      'phone': customer.phoneNumber ?? '',
      'email': customer.email ?? '',
      'idNumber': customer.idCardNumber ?? '',
      'gender':customer.gender,
      'birthDate': customer.birthDate,
      'permanentAddress': customer.permanentAddress ?? '',
      'province': customer.province,
      'district': customer.district ?? '',
      'ward': customer.ward,
      'currentAddress': customer.currentAddress ?? '',
      'currentProvince': customer.province?.id ?? customer.province?.name ?? '', // For current address
      'currentWard': customer.ward?.id ?? customer.ward?.name ?? '', // For current address
      'sameAddress': customer.sameAddress,
      'occupation': customer.occupation ?? '',
      'status': customer.status,
      'workplace': customer.workplace ?? '',
      'monthlyIncome': customer.monthlyIncome ?? '',
      'workExperience': customer.workExperience?.code ?? '',
      'cifNumber': customer.cifNumber ?? '',
      'source': customer.source,
      'tags': customer.tags.map((tag) => tag.id).whereType<String>().toList(),
      'notes': customer.notes ?? '',
      'revenue': customer.revenue ?? '',
      'location': customer.location?.code ?? customer.location?.label ?? '',
    };
  }

  Map<String, List<String>> _validateFormData(Map<String, dynamic> formData) {
    final errors = <String, List<String>>{};
    if (formData['name'] == null || formData['name'].toString().trim().isEmpty) {
      errors['name'] = ['Họ tên không được để trống'];
    } else if (formData['name'].toString().length < 2) {
      errors['name'] = ['Họ tên phải có ít nhất 2 ký tự'];
    }
    final phone = formData['phone']?.toString();
    if (phone != null && phone.isNotEmpty) {
      final digits = phone.replaceAll(RegExp(r'[^\d]'), '');
      final valid = (digits.startsWith('84') && digits.length == 11) ||
          (digits.startsWith('0') && (digits.length == 10 || digits.length == 11)) ||
          (digits.length == 9) ||
          (digits.length == 10 && !digits.startsWith('0'));
      if (!valid) {
        errors['phone'] = ['Số điện thoại không đúng định dạng'];
      }
    }
    final email = formData['email']?.toString();
    if (email != null && email.isNotEmpty) {
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}\$');
      if (!emailRegex.hasMatch(email)) {
        errors['email'] = ['Email không đúng định dạng'];
      }
    }
    if (formData['permanentAddress'] == null || formData['permanentAddress'].toString().trim().isEmpty) {
      errors['permanentAddress'] = ['Địa chỉ thường trú không được để trống'];
    }
    if (formData['province'] == null || formData['province'].toString().trim().isEmpty) {
      errors['province'] = ['Tỉnh/thành phố không được để trống'];
    }
    final idNum = formData['idNumber']?.toString();
    if (idNum != null && idNum.isNotEmpty) {
      final idRegex = RegExp(r'^[0-9]{12}\$');
      if (!idRegex.hasMatch(idNum)) {
        errors['idNumber'] = ['Số CMND/CCCD không đúng định dạng (12 chữ số)'];
      }
    }
    return errors;
  }

  Map<String, List<String>> _mapValidationMessage(String message) {
    final errors = <String, List<String>>{};
    if (message.contains('Họ tên')) errors['name'] = ['Họ tên không được để trống'];
    if (message.contains('Số điện thoại')) errors['phone'] = ['Số điện thoại không đúng định dạng'];
    if (message.contains('Email')) errors['email'] = ['Email không đúng định dạng'];
    if (message.contains('Địa chỉ thường trú')) errors['permanentAddress'] = ['Địa chỉ thường trú không được để trống'];
    if (message.contains('Tỉnh/thành phố')) errors['province'] = ['Tỉnh/thành phố không được để trống'];
    if (message.contains('CMND') || message.contains('CCCD')) errors['idNumber'] = ['Số CMND/CCCD không đúng định dạng (12 chữ số)'];
    return errors;
  }

  /// Convert form data to UpdateCustomerRequest using service
  UpdateCustomerRequest _convertFormDataToUpdateRequest(Map<String, dynamic> formData) {
    // Service đã xử lý toàn bộ việc transform data, bao gồm cả gender mapping
    return _customerService.convertFormDataToUpdateRequest(formData);
  }
}

