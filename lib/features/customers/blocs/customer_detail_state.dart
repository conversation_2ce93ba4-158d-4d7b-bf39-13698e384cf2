import '../models/customer_model.dart';

abstract class CustomerDetailState {}

class CustomerDetailInitial extends CustomerDetailState {}

class CustomerDetailLoading extends CustomerDetailState {}

class CustomerDetailSuccess extends CustomerDetailState {
  final CustomerModel customer;
  CustomerDetailSuccess(this.customer);
}

class CustomerDetailFailure extends CustomerDetailState {
  final String error;
  CustomerDetailFailure(this.error);
} 