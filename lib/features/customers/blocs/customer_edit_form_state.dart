import 'package:equatable/equatable.dart';
import '../models/customer_model.dart';
import '../services/customer_service.dart';

abstract class CustomerEditFormState extends Equatable {
  const CustomerEditFormState();
  @override
  List<Object?> get props => [];
}

class CustomerEditFormInitial extends CustomerEditFormState {
  const CustomerEditFormInitial();
}

class CustomerEditFormLoading extends CustomerEditFormState {
  const CustomerEditFormLoading();
}

class CustomerEditFormReady extends CustomerEditFormState {
  final Map<String, dynamic> formData;
  final Map<String, List<String>> validationErrors;
  const CustomerEditFormReady({
    required this.formData,
    this.validationErrors = const {},
  });
  @override
  List<Object?> get props => [formData, validationErrors];
  CustomerEditFormReady copyWith({
    Map<String, dynamic>? formData,
    Map<String, List<String>>? validationErrors,
  }) {
    return CustomerEditFormReady(
      formData: formData ?? this.formData,
      validationErrors: validationErrors ?? this.validationErrors,
    );
  }
}

class CustomerEditFormSubmitting extends CustomerEditFormState {
  final Map<String, dynamic> formData;
  const CustomerEditFormSubmitting(this.formData);
  @override
  List<Object?> get props => [formData];
}

class CustomerEditFormSuccess extends CustomerEditFormState {
  final CustomerModel? customer;
  const CustomerEditFormSuccess(this.customer);
  @override
  List<Object?> get props => [customer];
}

class CustomerEditFormFailure extends CustomerEditFormState {
  final String error;
  final CustomerExceptionType? errorType;
  final Map<String, dynamic> formData;
  const CustomerEditFormFailure({
    required this.error,
    this.errorType,
    required this.formData,
  });
  @override
  List<Object?> get props => [error, errorType, formData];
}

class CustomerEditFormValidationFailure extends CustomerEditFormState {
  final Map<String, List<String>> validationErrors;
  final Map<String, dynamic> formData;
  const CustomerEditFormValidationFailure({
    required this.validationErrors,
    required this.formData,
  });
  @override
  List<Object?> get props => [validationErrors, formData];
}

