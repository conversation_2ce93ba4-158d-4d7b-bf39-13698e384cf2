import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import '../services/customer_service.dart';
import 'customer_detail_event.dart';
import 'customer_detail_state.dart';

// Bloc
class CustomerDetailBloc extends Bloc<CustomerDetailEvent, CustomerDetailState> {
  final CustomerService _customerService = CustomerService();
  final IAppLogger _logger = getIt.get<IAppLogger>();

  CustomerDetailBloc() : super(CustomerDetailInitial()) {
    on<CustomerDetailInitialized>(_onInitialized);
    on<CustomerDetailRefreshed>(_onRefreshed);
  }

  Future<void> _onInitialized(
    CustomerDetailInitialized event,
    Emitter<CustomerDetailState> emit,
  ) async {
    emit(CustomerDetailLoading());
    try {
      final response = await _customerService.getCustomerDetail(event.customerId);
      
      if (response.isSuccess && response.hasData) {
        emit(CustomerDetailSuccess(response.data!));
      } else {
        emit(CustomerDetailFailure(response.message));
      }
    } catch (e) {
      _logger.e('Error initializing customer detail: $e');
      emit(CustomerDetailFailure(e.toString()));
    }
  }

  Future<void> _onRefreshed(
    CustomerDetailRefreshed event,
    Emitter<CustomerDetailState> emit,
  ) async {
    emit(CustomerDetailLoading());
    try {
      final response = await _customerService.getCustomerDetail(event.customerId);
      
      if (response.isSuccess && response.hasData) {
        emit(CustomerDetailSuccess(response.data!));
      } else {
        emit(CustomerDetailFailure(response.message));
      }
    } catch (e) {
      _logger.e('Error refreshing customer detail: $e');
      emit(CustomerDetailFailure(e.toString()));
    }
  }
} 