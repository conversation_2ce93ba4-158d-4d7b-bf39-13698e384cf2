import 'package:flutter_bloc/flutter_bloc.dart';
import '../services/customer_service.dart';
import '../models/customer_list_request.dart';
import '../models/customer_summary_request.dart';
import 'customer_list_event.dart';
import 'customer_list_state.dart';

/// Bloc để quản lý customer list
class CustomerListBloc extends Bloc<CustomerListEvent, CustomerListState> {
  final CustomerService _customerService;

  CustomerListBloc({
    required CustomerService customerService,
  })  : _customerService = customerService,
        super(const CustomerListInitial()) {
    on<CustomerListInitialized>(_onInitialized);
    on<CustomerListRefreshed>(_onRefreshed);
    on<CustomerListLoadMore>(_onLoadMore);
    on<CustomerListFiltered>(_onFiltered);
    on<CustomerListSearched>(_onSearched);
    on<CustomerSummaryRequested>(_onSummaryRequested);
  }

  /// Khởi tạo danh sách
  Future<void> _onInitialized(
    CustomerListInitialized event,
    Emitter<CustomerListState> emit,
  ) async {
    emit(const CustomerListLoading());

    try {
      final request = const CustomerListRequest(pLimit: 10);

      final response = await _customerService.getCustomers(request);
      
      if (response.isSuccess && response.hasData) {
        final customerListData = response.data!;
        emit(CustomerListSuccess(
          customers: customerListData.customers,
          totalCount: customerListData.totalCount,
          currentPage: 1, // First page for initial load
          hasMore: customerListData.customers.length >= request.pLimit && customerListData.customers.length < customerListData.totalCount,
          // No filters for initial load - all nulls
        ));
      } else {
        emit(CustomerListFailure(
          error: response.message,
          errorType: CustomerExceptionType.apiError,
        ));
      }
    } on CustomerException catch (e) {
      emit(CustomerListFailure(
        error: e.message,
        errorType: e.type,
      ));
    } catch (e) {
      emit(CustomerListFailure(
        error: 'Lỗi không xác định: $e',
        errorType: CustomerExceptionType.unknown,
      ));
    }
  }

  /// Refresh danh sách
  Future<void> _onRefreshed(
    CustomerListRefreshed event,
    Emitter<CustomerListState> emit,
  ) async {
    if (state is CustomerListSuccess) {
      final currentState = state as CustomerListSuccess;
      emit(CustomerListRefreshing(customers: currentState.customers));
    } else {
      emit(const CustomerListLoading());
    }

    try {
      final request = const CustomerListRequest(pLimit: 10);

      final response = await _customerService.getCustomers(request);
      
      if (response.isSuccess && response.hasData) {
        final customerListData = response.data!;
        emit(CustomerListSuccess(
          customers: customerListData.customers,
          totalCount: customerListData.totalCount,
          currentPage: 1, // First page for refresh
          hasMore: customerListData.customers.length >= request.pLimit && customerListData.customers.length < customerListData.totalCount,
        ));
      } else {
        emit(CustomerListFailure(
          error: response.message,
          errorType: CustomerExceptionType.apiError,
        ));
      }
    } on CustomerException catch (e) {
      emit(CustomerListFailure(
        error: e.message,
        errorType: e.type,
      ));
    } catch (e) {
      emit(CustomerListFailure(
        error: 'Lỗi không xác định: $e',
        errorType: CustomerExceptionType.unknown,
      ));
    }
  }

  /// Load more customers - sử dụng current filters từ state
  Future<void> _onLoadMore(
    CustomerListLoadMore event,
    Emitter<CustomerListState> emit,
  ) async {
    if (state is CustomerListSuccess) {
      final currentState = state as CustomerListSuccess;
      
      if (!currentState.hasMore) return;

      emit(CustomerListLoadingMore(customers: currentState.customers));

      try {
        // Format dates for API if available
        String? formattedFromDate;
        String? formattedToDate;
        
        if (currentState.currentFromDate != null) {
          formattedFromDate = currentState.currentFromDate!.toIso8601String().split('T')[0];
        }
        
        if (currentState.currentToDate != null) {
          formattedToDate = currentState.currentToDate!.toIso8601String().split('T')[0];
        }
        
        // Use current filters/search from state for load more
        final request = CustomerListRequest(
          pLimit: event.pLimit,
          pOffset: currentState.customers.length, // Use current length as offset
          pKeysearch: currentState.currentSearchQuery?.isEmpty == true ? null : currentState.currentSearchQuery,
          pStatus: currentState.currentStatus?.isEmpty == true ? null : currentState.currentStatus,
          pCreatedFrom: formattedFromDate,
          pCreatedTo: formattedToDate,
          pRegionIds: currentState.currentRegionIds?.isEmpty == true ? null : currentState.currentRegionIds,
          pBranchIds: currentState.currentBranchIds?.isEmpty == true ? null : currentState.currentBranchIds,
          pManagerCif: currentState.currentStaffIds?.isEmpty == true ? null : currentState.currentStaffIds?.first,
          pCustomerTagIds: currentState.currentCustomerTagIds?.isEmpty == true ? null : currentState.currentCustomerTagIds,
        );

        final response = await _customerService.getCustomers(request);
        
        if (response.isSuccess && response.hasData) {
          final customerListData = response.data!;
          final allCustomers = [...currentState.customers, ...customerListData.customers];
          
          // Calculate correct hasMore based on total accumulated data
          final hasMore = allCustomers.length < customerListData.totalCount;
          
          emit(CustomerListSuccess(
            customers: allCustomers,
            totalCount: customerListData.totalCount,
            currentPage: (allCustomers.length / event.pLimit).floor() + 1,
            hasMore: hasMore,
            // Preserve current filters for next load more
            currentStatus: currentState.currentStatus,
            currentLocation: currentState.currentLocation,
            currentFromDate: currentState.currentFromDate,
            currentToDate: currentState.currentToDate,
            currentRegionIds: currentState.currentRegionIds,
            currentBranchIds: currentState.currentBranchIds,
            currentStaffIds: currentState.currentStaffIds,
            currentCustomerTagIds: currentState.currentCustomerTagIds,
            currentSearchQuery: currentState.currentSearchQuery,
          ));
        } else {
          emit(CustomerListFailure(
            error: response.message,
            errorType: CustomerExceptionType.apiError,
          ));
        }
      } on CustomerException catch (e) {
        emit(CustomerListFailure(
          error: e.message,
          errorType: e.type,
        ));
      } catch (e) {
        emit(CustomerListFailure(
          error: 'Lỗi không xác định: $e',
          errorType: CustomerExceptionType.unknown,
        ));
      }
    }
  }

  /// Filter customers
  Future<void> _onFiltered(
    CustomerListFiltered event,
    Emitter<CustomerListState> emit,
  ) async {
    emit(const CustomerListLoading());

    try {
      // Format dates for API if available
      String? formattedFromDate;
      String? formattedToDate;
      
      if (event.fromDate != null) {
        formattedFromDate = event.fromDate!.toIso8601String().split('T')[0]; // YYYY-MM-DD
      }
      
      if (event.toDate != null) {
        formattedToDate = event.toDate!.toIso8601String().split('T')[0]; // YYYY-MM-DD
      }
      
      // Create request with filter parameters
      final request = CustomerListRequest(
        pKeysearch: event.searchQuery?.isEmpty == true ? null : event.searchQuery,
        pStatus: event.status?.isEmpty == true ? null : event.status,
        pCreatedFrom: formattedFromDate,
        pCreatedTo: formattedToDate,
        pRegionIds: event.regionIds?.isEmpty == true ? null : event.regionIds,
        pBranchIds: event.branchIds?.isEmpty == true ? null : event.branchIds,
        pManagerCif: event.staffIds?.isEmpty == true ? null : event.staffIds?.first,
        pCustomerTagIds: event.customerTagIds?.isEmpty == true ? null : event.customerTagIds,
        pLimit: 10,
        pOffset: 0,
      );

      final response = await _customerService.getCustomers(request);
      
      if (response.isSuccess && response.hasData) {
        final customerListData = response.data!;
        emit(CustomerListSuccess(
          customers: customerListData.customers,
          totalCount: customerListData.totalCount,
          currentPage: 1, // First page for filter
          hasMore: customerListData.customers.length >= request.pLimit && customerListData.customers.length < customerListData.totalCount,
          // Save current filters for load more
          currentStatus: event.status,
          currentLocation: event.location,
          currentFromDate: event.fromDate,
          currentToDate: event.toDate,
          currentRegionIds: event.regionIds,
          currentBranchIds: event.branchIds,
          currentStaffIds: event.staffIds,
          currentCustomerTagIds: event.customerTagIds,
          currentSearchQuery: event.searchQuery,
        ));
      } else {
        emit(CustomerListFailure(
          error: response.message,
          errorType: CustomerExceptionType.apiError,
        ));
      }
    } on CustomerException catch (e) {
      emit(CustomerListFailure(
        error: e.message,
        errorType: e.type,
      ));
    } catch (e) {
      emit(CustomerListFailure(
        error: 'Lỗi không xác định: $e',
        errorType: CustomerExceptionType.unknown,
      ));
    }
  }

  /// Search customers - sử dụng API search
  Future<void> _onSearched(
    CustomerListSearched event,
    Emitter<CustomerListState> emit,
  ) async {
    emit(const CustomerListLoading());

    try {
      // Use API search instead of local filtering
      final request = CustomerListRequest(
        pKeysearch: event.searchQuery.isEmpty ? null : event.searchQuery,
        pLimit: 10,
        pOffset: 0,
      );

      final response = await _customerService.getCustomers(request);
      
      if (response.isSuccess && response.hasData) {
        final customerListData = response.data!;
        emit(CustomerListSuccess(
          customers: customerListData.customers,
          totalCount: customerListData.totalCount,
          currentPage: 1, // First page for search
          hasMore: customerListData.customers.length >= request.pLimit && customerListData.customers.length < customerListData.totalCount,
          // Save current search query for load more
          currentSearchQuery: event.searchQuery,
        ));
      } else {
        emit(CustomerListFailure(
          error: response.message,
          errorType: CustomerExceptionType.apiError,
        ));
      }
    } on CustomerException catch (e) {
      emit(CustomerListFailure(
        error: e.message,
        errorType: e.type,
      ));
    } catch (e) {
      emit(CustomerListFailure(
        error: 'Lỗi không xác định: $e',
        errorType: CustomerExceptionType.unknown,
      ));
    }
  }

  /// Get customer summary
  Future<void> _onSummaryRequested(
    CustomerSummaryRequested event,
    Emitter<CustomerListState> emit,
  ) async {
    emit(const CustomerSummaryLoading());

    try {
      // Format dates for API if available
      String? formattedFromDate;
      String? formattedToDate;
      
      if (event.fromDate != null) {
        formattedFromDate = event.fromDate!.toIso8601String().split('T')[0]; // YYYY-MM-DD
      }
      
      if (event.toDate != null) {
        formattedToDate = event.toDate!.toIso8601String().split('T')[0]; // YYYY-MM-DD
      }
      
      // Create request with filter parameters
      final request = CustomerSummaryRequest(
        pKeysearch: event.searchQuery?.isEmpty == true ? null : event.searchQuery,
        pStatus: event.status?.isEmpty == true ? null : event.status,
        pCreatedFrom: formattedFromDate,
        pCreatedTo: formattedToDate,
        pRegionIds: event.regionIds?.isEmpty == true ? null : event.regionIds,
        pBranchIds: event.branchIds?.isEmpty == true ? null : event.branchIds,
        pManagerCif: event.staffIds?.isEmpty == true ? null : event.staffIds?.first,
        pCustomerTagIds: event.customerTagIds?.isEmpty == true ? null : event.customerTagIds,
      );

      final response = await _customerService.getCustomerSummary(request);
      
      if (response.isSuccess && response.data != null) {
        emit(CustomerSummarySuccess(summaryData: response.data!));
      } else {
        emit(CustomerSummaryFailure(
          error: response.message,
          errorType: CustomerExceptionType.apiError,
        ));
      }
    } on CustomerException catch (e) {
      emit(CustomerSummaryFailure(
        error: e.message,
        errorType: e.type,
      ));
    } catch (e) {
      emit(CustomerSummaryFailure(
        error: 'Lỗi không xác định: $e',
        errorType: CustomerExceptionType.unknown,
      ));
    }
  }
} 