import 'package:equatable/equatable.dart';
import '../models/customer_model.dart';
import '../models/customer_summary_data.dart';
import '../services/customer_service.dart';

/// States cho CustomerListBloc
abstract class CustomerListState extends Equatable {
  const CustomerListState();

  @override
  List<Object?> get props => [];
}

/// State khởi tạo
class CustomerListInitial extends CustomerListState {
  const CustomerListInitial();
}

/// State đang load
class CustomerListLoading extends CustomerListState {
  const CustomerListLoading();
}

/// State đang refresh
class CustomerListRefreshing extends CustomerListState {
  final List<CustomerModel> customers;

  const CustomerListRefreshing({required this.customers});

  @override
  List<Object?> get props => [customers];
}

/// State đang load more
class CustomerListLoadingMore extends CustomerListState {
  final List<CustomerModel> customers;

  const CustomerListLoadingMore({required this.customers});

  @override
  List<Object?> get props => [customers];
}

/// State thành công
class CustomerListSuccess extends CustomerListState {
  final List<CustomerModel> customers;
  final int totalCount;
  final int currentPage;
  final bool hasMore;
  
  // Current filters/search parameters for load more
  final String? currentStatus;
  final String? currentLocation;
  final DateTime? currentFromDate;
  final DateTime? currentToDate;
  final List<String>? currentRegionIds;
  final List<String>? currentBranchIds;
  final List<String>? currentStaffIds;
  final List<String>? currentCustomerTagIds;
  final String? currentSearchQuery;

  const CustomerListSuccess({
    required this.customers,
    required this.totalCount,
    required this.currentPage,
    required this.hasMore,
    this.currentStatus,
    this.currentLocation,
    this.currentFromDate,
    this.currentToDate,
    this.currentRegionIds,
    this.currentBranchIds,
    this.currentStaffIds,
    this.currentCustomerTagIds,
    this.currentSearchQuery,
  });

  @override
  List<Object?> get props => [
    customers, 
    totalCount, 
    currentPage, 
    hasMore,
    currentStatus,
    currentLocation,
    currentFromDate,
    currentToDate,
    currentRegionIds,
    currentBranchIds,
    currentStaffIds,
    currentCustomerTagIds,
    currentSearchQuery,
  ];

  CustomerListSuccess copyWith({
    List<CustomerModel>? customers,
    int? totalCount,
    int? currentPage,
    bool? hasMore,
    String? currentStatus,
    String? currentLocation,
    DateTime? currentFromDate,
    DateTime? currentToDate,
    List<String>? currentRegionIds,
    List<String>? currentBranchIds,
    List<String>? currentStaffIds,
    List<String>? currentCustomerTagIds,
    String? currentSearchQuery,
  }) {
    return CustomerListSuccess(
      customers: customers ?? this.customers,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
      hasMore: hasMore ?? this.hasMore,
      currentStatus: currentStatus ?? this.currentStatus,
      currentLocation: currentLocation ?? this.currentLocation,
      currentFromDate: currentFromDate ?? this.currentFromDate,
      currentToDate: currentToDate ?? this.currentToDate,
      currentRegionIds: currentRegionIds ?? this.currentRegionIds,
      currentBranchIds: currentBranchIds ?? this.currentBranchIds,
      currentStaffIds: currentStaffIds ?? this.currentStaffIds,
      currentCustomerTagIds: currentCustomerTagIds ?? this.currentCustomerTagIds,
      currentSearchQuery: currentSearchQuery ?? this.currentSearchQuery,
    );
  }
}

/// State đang load customer summary
class CustomerSummaryLoading extends CustomerListState {
  const CustomerSummaryLoading();
}

/// State customer summary thành công
class CustomerSummarySuccess extends CustomerListState {
  final CustomerSummaryData summaryData;

  const CustomerSummarySuccess({required this.summaryData});

  @override
  List<Object?> get props => [summaryData];
}

/// State customer summary thất bại
class CustomerSummaryFailure extends CustomerListState {
  final String error;
  final CustomerExceptionType? errorType;

  const CustomerSummaryFailure({
    required this.error,
    this.errorType,
  });

  @override
  List<Object?> get props => [error, errorType];
}

/// State thất bại
class CustomerListFailure extends CustomerListState {
  final String error;
  final CustomerExceptionType? errorType;

  const CustomerListFailure({
    required this.error,
    this.errorType,
  });

  @override
  List<Object?> get props => [error, errorType];
} 