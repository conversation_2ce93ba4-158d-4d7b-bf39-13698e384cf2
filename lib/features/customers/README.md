# Customer Feature

## Tổng quan

Module Customer đư<PERSON><PERSON> thiết kế theo kiến trúc feature-based với các thành phần chính:

- **Models**: Đ<PERSON>nh nghĩa cấu trúc dữ liệu
- **Services**: Xử lý business logic và API calls
- **Blocs**: Quản lý state và business logic
- **Screens**: UI components
- **Widgets**: Reusable UI components

## Cấu trúc thư mục

```
lib/features/customers/
├── models/
│   ├── customer_model.dart              # Model chính cho Customer
│   ├── customer_model.g.dart            # Generated JSON serialization
│   ├── customer_response.dart           # Response cho single customer
│   ├── customer_list_response.dart      # Response cho customer list
│   ├── customer_list_request.dart       # Request cho customer list
│   ├── create_customer_request.dart     # Request tạo customer
│   └── index.dart                       # Barrel exports
├── services/
│   ├── customer_service.dart            # Service xử lý customer operations
│   └── index.dart                       # Barrel exports
├── blocs/
│   ├── customer_form_bloc.dart          # Bloc cho customer form
│   ├── customer_form_event.dart         # Events cho customer form
│   ├── customer_form_state.dart         # States cho customer form
│   ├── customer_list_bloc.dart          # Bloc cho customer list
│   ├── customer_list_event.dart         # Events cho customer list
│   ├── customer_list_state.dart         # States cho customer list
│   └── index.dart                       # Barrel exports
├── screens/
│   ├── customers_tab.dart               # Main customer list screen
│   ├── add_customer_screen.dart         # Add customer screen
│   └── customer_detail_screen.dart      # Customer detail screen
├── widgets/
│   └── ...                              # Reusable widgets
└── index.dart                           # Main barrel export
```

## Models

### CustomerModel
Model chính cho Customer với các trường:
- Basic info: id, fullName, phoneNumber, email, gender, birthDate
- Address info: permanentAddress, currentAddress, province, district, ward
- Career info: occupation, workplace, monthlyIncome, workExperience
- Classification: status, source, tags, notes, revenue, location
- Timestamps: createdAt, updatedAt, lastContactAt
- Metadata: metadata, createdBy, updatedBy

### Extensions
- `CustomerModelDisplay`: Extension để hiển thị lên UI với các methods:
  - `displayName`, `displayPhone`, `displayEmail`
  - `displayStatus`, `displayRevenue`, `displayLocation`
  - `displayTags`, `displayLastUpdate`, `displayCreatedAt`
  - `toDisplayMap()`: Chuyển đổi thành Map cho UI

## Services

### CustomerService
Service singleton để xử lý customer operations:

#### Methods
- `createCustomer(CreateCustomerRequest)`: Tạo customer mới
- `getCustomers(CustomerListRequest)`: Lấy danh sách customers
- Validation methods: `_validateCreateRequest`, `_isValidVietnamesePhone`, `_isValidEmail`, `_isValidIdCardNumber`

#### API Endpoints
- `POST /api/customers`: Tạo customer mới
- `POST /api/customers`: Lấy danh sách customers (với params)

## Blocs

### CustomerFormBloc
Quản lý state cho customer form:
- **Events**: Initialized, DataUpdated, Validated, Submitted, Reset
- **States**: Initial, Loading, Ready, Submitting, Success, Failure, ValidationFailure

### CustomerListBloc
Quản lý state cho customer list:
- **Events**: Initialized, Loaded, Refreshed, LoadMore, Filtered, Searched
- **States**: Initial, Loading, Refreshing, LoadingMore, Success, Failure

## Screens

### CustomersTab
Main screen hiển thị danh sách customers với:
- Customer summary statistics
- Search và filter functionality
- Customer list với pagination
- Empty state handling
- Error handling

## Luồng hoạt động

### Tạo Customer
1. User nhập thông tin customer trong `AddCustomerScreen`
2. `CustomerFormBloc` validate và submit data
3. `CustomerService.createCustomer()` gọi API
4. Response được xử lý và hiển thị kết quả

### Xem danh sách Customer
1. `CustomersTab` khởi tạo `CustomerListBloc`
2. `CustomerListBloc` gọi `CustomerService.getCustomers()`
3. API response được parse thành `CustomerListResponse`
4. UI hiển thị danh sách customers với pagination
5. User có thể search, filter, load more

## Error Handling

- **Validation errors**: Hiển thị lỗi validation cho từng field
- **API errors**: Hiển thị thông báo lỗi và retry button
- **Network errors**: Hiển thị thông báo lỗi mạng
- **Unknown errors**: Hiển thị thông báo lỗi chung

## Pagination

Customer list hỗ trợ pagination với:
- Page size: 20 items per page
- Load more functionality
- Infinite scroll (có thể implement thêm)

## Search & Filter

- **Search**: Tìm kiếm theo tên, số điện thoại, email
- **Status filter**: Lọc theo trạng thái (Tiềm năng, Đang chăm sóc, Đã giao dịch, Đã chốt)
- **Location filter**: Lọc theo địa điểm (HCM, HN, ĐN, Khác)
- **Date range filter**: Lọc theo khoảng thời gian
- **Advanced filters**: Modal filter với nhiều options

## Dependencies

- `flutter_bloc`: State management
- `equatable`: Value equality
- `json_annotation`: JSON serialization
- `kiloba_biz/shared/services/api`: API service
- `kiloba_biz/shared/utils/app_logger`: Logging
- `kiloba_biz/core/theme`: Theme và styling 