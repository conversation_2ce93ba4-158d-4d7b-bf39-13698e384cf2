import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/utils/app_logger.dart';

/// Repository để lưu form data tạm thời cho customer
class CustomerFormRepository {
  static final CustomerFormRepository _instance = CustomerFormRepository._internal();
  factory CustomerFormRepository() => _instance;
  CustomerFormRepository._internal();

  SharedPreferences? _prefs;
  final AppLogger _logger = AppLogger();

  // Storage keys
  static const String _formDataKey = 'customer_form_data';
  static const String _formProgressKey = 'customer_form_progress';
  static const String _validationErrorsKey = 'customer_validation_errors';
  static const String _lastSavedAtKey = 'customer_form_last_saved';

  /// Initialize SharedPreferences
  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Lưu form data đang nhập
  Future<void> saveFormData(Map<String, dynamic> formData) async {
    try {
      await _initPrefs();

      final dataToSave = {
        'formData': formData,
        'lastSavedAt': DateTime.now().toIso8601String(),
      };

      await _prefs!.setString(_formDataKey, jsonEncode(dataToSave));

      _logger.i('Saved customer form data');
    } catch (e) {
      _logger.e('Error saving customer form data: $e');
      rethrow;
    }
  }

  /// Lấy form data đã lưu
  Future<Map<String, dynamic>?> getFormData() async {
    try {
      await _initPrefs();

      final jsonString = _prefs!.getString(_formDataKey);
      if (jsonString == null) return null;

      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      return data['formData'] as Map<String, dynamic>;
    } catch (e) {
      _logger.e('Error loading customer form data: $e');
      return null;
    }
  }

  /// Xóa form data sau khi submit thành công
  Future<void> clearFormData() async {
    try {
      await _initPrefs();

      await Future.wait([
        _prefs!.remove(_formDataKey),
        _prefs!.remove(_formProgressKey),
        _prefs!.remove(_validationErrorsKey),
        _prefs!.remove(_lastSavedAtKey),
      ]);

      _logger.i('Cleared customer form data');
    } catch (e) {
      _logger.e('Error clearing customer form data: $e');
      rethrow;
    }
  }

  /// Lưu form step progress
  Future<void> saveFormProgress(int currentStep) async {
    try {
      await _initPrefs();
      await _prefs!.setInt(_formProgressKey, currentStep);

      _logger.i('Saved customer form progress: step $currentStep');
    } catch (e) {
      _logger.e('Error saving customer form progress: $e');
      rethrow;
    }
  }

  /// Lấy form step progress
  Future<int> getFormProgress() async {
    try {
      await _initPrefs();
      return _prefs!.getInt(_formProgressKey) ?? 0;
    } catch (e) {
      _logger.e('Error loading customer form progress: $e');
      return 0;
    }
  }

  /// Lưu validation errors
  Future<void> saveValidationErrors(Map<String, List<String>> errors) async {
    try {
      await _initPrefs();

      final errorsData = errors.map(
        (key, value) => MapEntry(key, value),
      );

      await _prefs!.setString(_validationErrorsKey, jsonEncode(errorsData));

      _logger.i('Saved customer validation errors');
    } catch (e) {
      _logger.e('Error saving customer validation errors: $e');
      rethrow;
    }
  }

  /// Lấy validation errors
  Future<Map<String, List<String>>> getValidationErrors() async {
    try {
      await _initPrefs();

      final jsonString = _prefs!.getString(_validationErrorsKey);
      if (jsonString == null) return {};

      final errorsData = jsonDecode(jsonString) as Map<String, dynamic>;
      return errorsData.map(
        (key, value) => MapEntry(key, List<String>.from(value as List)),
      );
    } catch (e) {
      _logger.e('Error loading customer validation errors: $e');
      return {};
    }
  }

  /// Kiểm tra có form data đã lưu không
  Future<bool> hasSavedFormData() async {
    try {
      await _initPrefs();
      return _prefs!.containsKey(_formDataKey);
    } catch (e) {
      _logger.e('Error checking saved form data: $e');
      return false;
    }
  }

  /// Lấy thời gian lưu cuối cùng
  Future<DateTime?> getLastSavedAt() async {
    try {
      await _initPrefs();

      final jsonString = _prefs!.getString(_formDataKey);
      if (jsonString == null) return null;

      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      final lastSavedAtString = data['lastSavedAt'] as String?;
      
      if (lastSavedAtString != null) {
        return DateTime.parse(lastSavedAtString);
      }
      
      return null;
    } catch (e) {
      _logger.e('Error getting last saved at: $e');
      return null;
    }
  }

  /// Kiểm tra form data có còn valid không (24 giờ)
  Future<bool> isFormDataValid() async {
    try {
      final lastSavedAt = await getLastSavedAt();
      if (lastSavedAt == null) return false;

      final now = DateTime.now();
      final difference = now.difference(lastSavedAt);

      return difference.inHours < 24;
    } catch (e) {
      _logger.e('Error checking form data validity: $e');
      return false;
    }
  }
} 