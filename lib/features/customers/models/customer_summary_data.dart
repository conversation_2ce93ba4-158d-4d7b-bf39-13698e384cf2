/// Model cho customer summary data từ API get_customer_summary
class CustomerSummaryData {
  final double totalRevenue;
  final int totalCustomers;
  final int caringCustomers;
  final int potentialCustomers;
  final int transactedCustomers;

  const CustomerSummaryData({
    required this.totalRevenue,
    required this.totalCustomers,
    required this.caringCustomers,
    required this.potentialCustomers,
    required this.transactedCustomers,
  });

  factory CustomerSummaryData.fromJson(Map<String, dynamic> json) {
    return CustomerSummaryData(
      totalRevenue: (json['total_revenue'] ?? 0).toDouble(),
      totalCustomers: json['total_customers'] ?? 0,
      caringCustomers: json['caring_customers'] ?? 0,
      potentialCustomers: json['potential_customers'] ?? 0,
      transactedCustomers: json['transacted_customers'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_revenue': totalRevenue,
      'total_customers': totalCustomers,
      'caring_customers': caringCustomers,
      'potential_customers': potentialCustomers,
      'transacted_customers': transactedCustomers,
    };
  }

  /// Create empty summary data for initial state
  static const empty = CustomerSummaryData(
    totalRevenue: 0,
    totalCustomers: 0,
    caringCustomers: 0,
    potentialCustomers: 0,
    transactedCustomers: 0,
  );

  @override
  String toString() {
    return 'CustomerSummaryData{'
        'totalRevenue: $totalRevenue, '
        'totalCustomers: $totalCustomers, '
        'caringCustomers: $caringCustomers, '
        'potentialCustomers: $potentialCustomers, '
        'transactedCustomers: $transactedCustomers'
        '}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerSummaryData &&
          runtimeType == other.runtimeType &&
          totalRevenue == other.totalRevenue &&
          totalCustomers == other.totalCustomers &&
          caringCustomers == other.caringCustomers &&
          potentialCustomers == other.potentialCustomers &&
          transactedCustomers == other.transactedCustomers;

  @override
  int get hashCode =>
      totalRevenue.hashCode ^
      totalCustomers.hashCode ^
      caringCustomers.hashCode ^
      potentialCustomers.hashCode ^
      transactedCustomers.hashCode;
}
