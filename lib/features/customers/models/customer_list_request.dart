import 'package:json_annotation/json_annotation.dart';

part 'customer_list_request.g.dart';

@JsonSerializable()
class CustomerListRequest {
  @Json<PERSON>ey(name: 'p_keysearch')
  final String? pKeysearch;
  
  @Json<PERSON>ey(name: 'p_status')
  final String? pStatus;
  
  @Json<PERSON><PERSON>(name: 'p_created_from')
  final String? pCreatedFrom;
  
  @<PERSON>son<PERSON><PERSON>(name: 'p_created_to')
  final String? pCreatedTo;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'p_branch_ids')
  final List<String>? pBranchIds;
  
  @<PERSON><PERSON><PERSON>ey(name: 'p_region_ids')
  final List<String>? pRegionIds;
  
  @Json<PERSON>ey(name: 'p_manager_cif')
  final String? pManagerCif;
  
  @<PERSON>son<PERSON>ey(name: 'p_customer_tag_ids')
  final List<String>? pCustomerTagIds;
  
  @Json<PERSON>ey(name: 'p_limit')
  final int pLimit;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'p_offset')
  final int pOffset;

  const CustomerListRequest({
    this.pKeysearch,
    this.pStatus,
    this.pCreatedFrom,
    this.pCreatedTo,
    this.pBranchIds,
    this.pRegionIds,
    this.pManagerCif,
    this.pCustomerTagIds,
    this.pLimit = 10,
    this.pOffset = 0,
  });

  factory CustomerListRequest.fromJson(Map<String, dynamic> json) =>
      _$CustomerListRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerListRequestToJson(this);

  @override
  String toString() {
    return 'CustomerListRequest(pKeysearch: $pKeysearch, pStatus: $pStatus, pLimit: $pLimit, pOffset: $pOffset)';
  }

  /// Tạo request với pagination
  CustomerListRequest copyWith({
    String? pKeysearch,
    String? pStatus,
    String? pCreatedFrom,
    String? pCreatedTo,
    List<String>? pBranchIds,
    List<String>? pRegionIds,
    String? pManagerCif,
    List<String>? pCustomerTagIds,
    int? pLimit,
    int? pOffset,
  }) {
    return CustomerListRequest(
      pKeysearch: pKeysearch ?? this.pKeysearch,
      pStatus: pStatus ?? this.pStatus,
      pCreatedFrom: pCreatedFrom ?? this.pCreatedFrom,
      pCreatedTo: pCreatedTo ?? this.pCreatedTo,
      pBranchIds: pBranchIds ?? this.pBranchIds,
      pRegionIds: pRegionIds ?? this.pRegionIds,
      pManagerCif: pManagerCif ?? this.pManagerCif,
      pCustomerTagIds: pCustomerTagIds ?? this.pCustomerTagIds,
      pLimit: pLimit ?? this.pLimit,
      pOffset: pOffset ?? this.pOffset,
    );
  }

  /// Tạo request cho trang tiếp theo
  CustomerListRequest nextPage() {
    return copyWith(pOffset: pOffset + pLimit);
  }

  /// Tạo request cho trang trước
  CustomerListRequest previousPage() {
    final newOffset = pOffset - pLimit;
    return copyWith(pOffset: newOffset < 0 ? 0 : newOffset);
  }

  /// Kiểm tra có thể chuyển trang trước không
  bool get canGoPrevious => pOffset > 0;

  /// Kiểm tra có thể chuyển trang tiếp không
  bool get canGoNext => pOffset >= 0; // Luôn true vì không biết total count
} 