// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_list_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerListRequest _$CustomerListRequestFromJson(Map<String, dynamic> json) =>
    CustomerListRequest(
      pKeysearch: json['p_keysearch'] as String?,
      pStatus: json['p_status'] as String?,
      pCreatedFrom: json['p_created_from'] as String?,
      pCreatedTo: json['p_created_to'] as String?,
      pBranchIds: (json['p_branch_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      pRegionIds: (json['p_region_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      pManagerCif: json['p_manager_cif'] as String?,
      pCustomerTagIds: (json['p_customer_tag_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      pLimit: (json['p_limit'] as num?)?.toInt() ?? 10,
      pOffset: (json['p_offset'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$CustomerListRequestToJson(
  CustomerListRequest instance,
) => <String, dynamic>{
  'p_keysearch': instance.pKeysearch,
  'p_status': instance.pStatus,
  'p_created_from': instance.pCreatedFrom,
  'p_created_to': instance.pCreatedTo,
  'p_branch_ids': instance.pBranchIds,
  'p_region_ids': instance.pRegionIds,
  'p_manager_cif': instance.pManagerCif,
  'p_customer_tag_ids': instance.pCustomerTagIds,
  'p_limit': instance.pLimit,
  'p_offset': instance.pOffset,
};
