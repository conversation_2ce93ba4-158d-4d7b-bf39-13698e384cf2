import 'package:json_annotation/json_annotation.dart';
import '../../../shared/models/customer_tag_model.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/ward_model.dart';
import '../../../shared/models/branch_model.dart';
import '../../../shared/models/assigned_manager_model.dart';

part 'customer_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class CustomerModel {
  final String id;
  final String fullName;
  final String? phoneNumber;
  final String? email;
  final ConfigModel? gender;
  final DateTime? birthDate;
  
  // ID Card Information (Thông tin CMND/CCCD)
  final String? idCardNumber; // Số GTTT
  final String? idCardType; // Loại giấy tờ
  final DateTime? idCardIssueDate; // Ngày cấp
  final String? idCardIssuePlace; // Nơi cấp
  final DateTime? idCardExpiryDate; // Ngày hết hạn
  
  // Address information (Thông tin địa chỉ)
  final String? permanentAddress; // Địa chỉ thường trú
  final String? currentAddress; // Địa chỉ hiện tại
  final ProvinceModel? province; // Tỉnh/Thành phố
  final String? district; // Quận/Huyện
  final WardModel? ward; // Phường/Xã
  final bool sameAddress; // Địa chỉ giống nhau
  
  // Career information (Thông tin nghề nghiệp)
  final String? occupation; // Nghề nghiệp
  final String? workplace; // Nơi làm việc
  final String? monthlyIncome; // Thu nhập hàng tháng
  final ConfigModel? workExperience; // Kinh nghiệm làm việc
  
  // Customer Profile (Hồ sơ khách hàng)
  final String? cifNumber; // Số CIF
  final String? customerSource; // Nguồn khách hàng
  final DateTime? profileCreatedAt; // Ngày tạo hồ sơ
  final DateTime? profileUpdatedAt; // Ngày cập nhật hồ sơ
  
  // Classification
  final ConfigModel? status; // Object {code,label,value}
  final ConfigModel? source;
  final List<CustomerTagModel> tags;
  final String? notes;
  final String? revenue;
  final ConfigModel? location; // Khu vực (province/region)
  final BranchModel? branch; // Chi nhánh
  final AssignedManagerModel? assignedManager; // Quản lý được phân công
  
  // Timestamps
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? lastContactAt;
  
  // Metadata
  final Map<String, dynamic>? metadata;
  final String? createdBy;
  final String? updatedBy;

  const CustomerModel({
    required this.id,
    required this.fullName,
    this.phoneNumber,
    this.email,
    this.gender,
    this.birthDate,
    this.idCardNumber,
    this.idCardType,
    this.idCardIssueDate,
    this.idCardIssuePlace,
    this.idCardExpiryDate,
    this.permanentAddress,
    this.currentAddress,
    this.province,
    this.district,
    this.ward,
    this.sameAddress = false,
    this.occupation,
    this.workplace,
    this.monthlyIncome,
    this.workExperience,
    this.cifNumber,
    this.customerSource,
    this.profileCreatedAt,
    this.profileUpdatedAt,
    this.status,
    this.source,
    this.tags = const <CustomerTagModel>[],
    this.notes,
    this.revenue,
    this.location,
    this.branch,
    this.assignedManager,
    this.createdAt,
    this.updatedAt,
    this.lastContactAt,
    this.metadata,
    this.createdBy,
    this.updatedBy,
  });

  factory CustomerModel.fromJson(Map<String, dynamic> json) =>
      _$CustomerModelFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerModelToJson(this);

  CustomerModel copyWith({
    String? id,
    String? fullName,
    String? phoneNumber,
    String? email,
    ConfigModel? gender,
    DateTime? birthDate,
    String? idCardNumber,
    String? idCardType,
    DateTime? idCardIssueDate,
    String? idCardIssuePlace,
    DateTime? idCardExpiryDate,
    String? permanentAddress,
    String? currentAddress,
    ProvinceModel? province,
    String? district,
    WardModel? ward,
    bool? sameAddress,
    String? occupation,
    String? workplace,
    String? monthlyIncome,
    ConfigModel? workExperience,
    String? cifNumber,
    String? customerSource,
    DateTime? profileCreatedAt,
    DateTime? profileUpdatedAt,
    ConfigModel? status,
    ConfigModel? source,
    List<CustomerTagModel>? tags,
    String? notes,
    String? revenue,
    ConfigModel? location,
    BranchModel? branch,
    AssignedManagerModel? assignedManager,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastContactAt,
    Map<String, dynamic>? metadata,
    String? createdBy,
    String? updatedBy,
  }) {
    return CustomerModel(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      idCardNumber: idCardNumber ?? this.idCardNumber,
      idCardType: idCardType ?? this.idCardType,
      idCardIssueDate: idCardIssueDate ?? this.idCardIssueDate,
      idCardIssuePlace: idCardIssuePlace ?? this.idCardIssuePlace,
      idCardExpiryDate: idCardExpiryDate ?? this.idCardExpiryDate,
      permanentAddress: permanentAddress ?? this.permanentAddress,
      currentAddress: currentAddress ?? this.currentAddress,
      province: province ?? this.province,
      district: district ?? this.district,
      ward: ward ?? this.ward,
      sameAddress: sameAddress ?? this.sameAddress,
      occupation: occupation ?? this.occupation,
      workplace: workplace ?? this.workplace,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
      workExperience: workExperience ?? this.workExperience,
      cifNumber: cifNumber ?? this.cifNumber,
      customerSource: customerSource ?? this.customerSource,
      profileCreatedAt: profileCreatedAt ?? this.profileCreatedAt,
      profileUpdatedAt: profileUpdatedAt ?? this.profileUpdatedAt,
      status: status ?? this.status,
      source: source ?? this.source,
      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
      revenue: revenue ?? this.revenue,
      location: location ?? this.location,
      branch: branch ?? this.branch,
      assignedManager: assignedManager ?? this.assignedManager,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastContactAt: lastContactAt ?? this.lastContactAt,
      metadata: metadata ?? this.metadata,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  @override
  String toString() {
    return 'CustomerModel(id: $id, fullName: $fullName, phoneNumber: $phoneNumber, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
} 