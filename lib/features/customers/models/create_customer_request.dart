import 'package:json_annotation/json_annotation.dart';

part 'create_customer_request.g.dart';

@JsonSerializable()
class CreateCustomerRequest {
  final String fullName;
  final String? phoneNumber;
  final String? email;
  final String? sex; // Using MALE/FEMALE values
  final String? dob; // Using string format "YYYY-MM-DD"
  final String? idNo; // ID card number
  final String? idType; // ID card type
  
  // Address information
  final String? permanentAddress;
  final String? currentAddress;
  final String? provinceId; // Province ID
  final String? wardsId; // Ward ID
  
  // Career information
  final String? occupation;
  final String? workplace;
  final String? monthlyIncome; // Numeric value
  final String? workExperience; // Using values like "ONE_TO_3_YEARS"
  
  // Classification
  final String? status; // Using values like "CARING"
  final String? source; // Using values like "REFERRAL"
  final List<String> tagIds; // Using actual tag IDs
  final String? noteContent; // Notes content

  const CreateCustomerRequest({
    required this.fullName,
    this.phoneNumber,
    this.email,
    this.sex,
    this.dob,
    this.idNo,
    this.idType,
    this.permanentAddress,
    this.currentAddress,
    this.provinceId,
    this.wardsId,
    this.occupation,
    this.workplace,
    this.monthlyIncome,
    this.workExperience,
    this.status,
    this.source,
    this.tagIds = const <String>[],
    this.noteContent,
  });

  factory CreateCustomerRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateCustomerRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CreateCustomerRequestToJson(this);

  CreateCustomerRequest copyWith({
    String? fullName,
    String? phoneNumber,
    String? email,
    String? sex,
    String? dob,
    String? idNo,
    String? idType,
    String? permanentAddress,
    String? currentAddress,
    String? provinceId,
    String? wardsId,
    String? occupation,
    String? workplace,
    String? monthlyIncome,
    String? workExperience,
    String? status,
    String? source,
    List<String>? tagIds,
    String? noteContent,
  }) {
    return CreateCustomerRequest(
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      sex: sex ?? this.sex,
      dob: dob ?? this.dob,
      idNo: idNo ?? this.idNo,
      idType: idType ?? this.idType,
      permanentAddress: permanentAddress ?? this.permanentAddress,
      currentAddress: currentAddress ?? this.currentAddress,
      provinceId: provinceId ?? this.provinceId,
      wardsId: wardsId ?? this.wardsId,
      occupation: occupation ?? this.occupation,
      workplace: workplace ?? this.workplace,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
      workExperience: workExperience ?? this.workExperience,
      status: status ?? this.status,
      source: source ?? this.source,
      tagIds: tagIds ?? this.tagIds,
      noteContent: noteContent ?? this.noteContent,
    );
  }

  @override
  String toString() {
    return 'CreateCustomerRequest(fullName: $fullName, phoneNumber: $phoneNumber, status: $status, sex: $sex)';
  }
} 