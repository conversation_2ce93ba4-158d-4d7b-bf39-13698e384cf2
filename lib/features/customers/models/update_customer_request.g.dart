// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'update_customer_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UpdateCustomerRequest _$UpdateCustomerRequestFromJson(
  Map<String, dynamic> json,
) => UpdateCustomerRequest(
  fullName: json['fullName'] as String,
  phoneNumber: json['phoneNumber'] as String?,
  status: json['status'] as String?,
  source: json['source'] as String?,
  occupation: json['occupation'] as String?,
  idType: json['idType'] as String?,
  idNo: json['idNo'] as String?,
  cifNo: json['cifNo'] as String?,
  sex: json['sex'] as String?,
  dob: json['dob'] as String?,
  email: json['email'] as String?,
  provinceId: json['provinceId'] as String?,
  wardsId: json['wardsId'] as String?,
  permanentAddress: json['permanentAddress'] as String?,
  currentAddress: json['currentAddress'] as String?,
  workplace: json['workplace'] as String?,
  monthlyIncome: json['monthlyIncome'] as String?,
  workExperience: json['workExperience'] as String?,
  tagIds:
      (json['tagIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const <String>[],
);

Map<String, dynamic> _$UpdateCustomerRequestToJson(
  UpdateCustomerRequest instance,
) => <String, dynamic>{
  'fullName': instance.fullName,
  'phoneNumber': instance.phoneNumber,
  'status': instance.status,
  'source': instance.source,
  'occupation': instance.occupation,
  'idType': instance.idType,
  'idNo': instance.idNo,
  'cifNo': instance.cifNo,
  'sex': instance.sex,
  'dob': instance.dob,
  'email': instance.email,
  'provinceId': instance.provinceId,
  'wardsId': instance.wardsId,
  'permanentAddress': instance.permanentAddress,
  'currentAddress': instance.currentAddress,
  'workplace': instance.workplace,
  'monthlyIncome': instance.monthlyIncome,
  'workExperience': instance.workExperience,
  'tagIds': instance.tagIds,
};
