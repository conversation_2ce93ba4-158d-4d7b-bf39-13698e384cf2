import 'package:json_annotation/json_annotation.dart';
import 'customer_model.dart';

part 'customer_list_data.g.dart';

/// Data model cho CustomerListResponse
@JsonSerializable(fieldRename: FieldRename.snake)
class CustomerListData {
  final List<CustomerModel> customers;
  final int totalCount;
  final int limit;
  final int offset;

  const CustomerListData({
    required this.customers,
    required this.totalCount,
    required this.limit, 
    required this.offset,
  });

  factory CustomerListData.fromJson(Map<String, dynamic> json) =>
      _$CustomerListDataFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerListDataToJson(this);

  /// Utility getters
  int get count => customers.length;
  bool get isEmpty => customers.isEmpty;
  bool get isNotEmpty => customers.isNotEmpty;
  int get page => (offset / limit).floor() + 1;
  bool get canLoadMore => offset + count < totalCount;
  int get totalPages => (totalCount / limit).ceil();

  @override
  String toString() {
    return 'CustomerListData(customers: ${customers.length} items, '
           'totalCount: $totalCount, page: $page/$totalPages)';
  }
}
