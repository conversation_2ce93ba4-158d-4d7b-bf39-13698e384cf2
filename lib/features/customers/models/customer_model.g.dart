// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerModel _$CustomerModelFromJson(Map<String, dynamic> json) =>
    CustomerModel(
      id: json['id'] as String,
      fullName: json['full_name'] as String,
      phoneNumber: json['phone_number'] as String?,
      email: json['email'] as String?,
      gender: json['gender'] == null
          ? null
          : ConfigModel.fromJson(json['gender'] as Map<String, dynamic>),
      birthDate: json['birth_date'] == null
          ? null
          : DateTime.parse(json['birth_date'] as String),
      idCardNumber: json['id_card_number'] as String?,
      idCardType: json['id_card_type'] as String?,
      idCardIssueDate: json['id_card_issue_date'] == null
          ? null
          : DateTime.parse(json['id_card_issue_date'] as String),
      idCardIssuePlace: json['id_card_issue_place'] as String?,
      idCardExpiryDate: json['id_card_expiry_date'] == null
          ? null
          : DateTime.parse(json['id_card_expiry_date'] as String),
      permanentAddress: json['permanent_address'] as String?,
      currentAddress: json['current_address'] as String?,
      province: json['province'] == null
          ? null
          : ProvinceModel.fromJson(json['province'] as Map<String, dynamic>),
      district: json['district'] as String?,
      ward: json['ward'] == null
          ? null
          : WardModel.fromJson(json['ward'] as Map<String, dynamic>),
      sameAddress: json['same_address'] as bool? ?? false,
      occupation: json['occupation'] as String?,
      workplace: json['workplace'] as String?,
      monthlyIncome: json['monthly_income'] as String?,
      workExperience: json['work_experience'] == null
          ? null
          : ConfigModel.fromJson(
              json['work_experience'] as Map<String, dynamic>,
            ),
      cifNumber: json['cif_number'] as String?,
      customerSource: json['customer_source'] as String?,
      profileCreatedAt: json['profile_created_at'] == null
          ? null
          : DateTime.parse(json['profile_created_at'] as String),
      profileUpdatedAt: json['profile_updated_at'] == null
          ? null
          : DateTime.parse(json['profile_updated_at'] as String),
      status: json['status'] == null
          ? null
          : ConfigModel.fromJson(json['status'] as Map<String, dynamic>),
      source: json['source'] == null
          ? null
          : ConfigModel.fromJson(json['source'] as Map<String, dynamic>),
      tags:
          (json['tags'] as List<dynamic>?)
              ?.map((e) => CustomerTagModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const <CustomerTagModel>[],
      notes: json['notes'] as String?,
      revenue: json['revenue'] as String?,
      location: json['location'] == null
          ? null
          : ConfigModel.fromJson(json['location'] as Map<String, dynamic>),
      branch: json['branch'] == null
          ? null
          : BranchModel.fromJson(json['branch'] as Map<String, dynamic>),
      assignedManager: json['assigned_manager'] == null
          ? null
          : AssignedManagerModel.fromJson(
              json['assigned_manager'] as Map<String, dynamic>,
            ),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      lastContactAt: json['last_contact_at'] == null
          ? null
          : DateTime.parse(json['last_contact_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdBy: json['created_by'] as String?,
      updatedBy: json['updated_by'] as String?,
    );

Map<String, dynamic> _$CustomerModelToJson(CustomerModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'full_name': instance.fullName,
      'phone_number': instance.phoneNumber,
      'email': instance.email,
      'gender': instance.gender,
      'birth_date': instance.birthDate?.toIso8601String(),
      'id_card_number': instance.idCardNumber,
      'id_card_type': instance.idCardType,
      'id_card_issue_date': instance.idCardIssueDate?.toIso8601String(),
      'id_card_issue_place': instance.idCardIssuePlace,
      'id_card_expiry_date': instance.idCardExpiryDate?.toIso8601String(),
      'permanent_address': instance.permanentAddress,
      'current_address': instance.currentAddress,
      'province': instance.province,
      'district': instance.district,
      'ward': instance.ward,
      'same_address': instance.sameAddress,
      'occupation': instance.occupation,
      'workplace': instance.workplace,
      'monthly_income': instance.monthlyIncome,
      'work_experience': instance.workExperience,
      'cif_number': instance.cifNumber,
      'customer_source': instance.customerSource,
      'profile_created_at': instance.profileCreatedAt?.toIso8601String(),
      'profile_updated_at': instance.profileUpdatedAt?.toIso8601String(),
      'status': instance.status,
      'source': instance.source,
      'tags': instance.tags,
      'notes': instance.notes,
      'revenue': instance.revenue,
      'location': instance.location,
      'branch': instance.branch,
      'assigned_manager': instance.assignedManager,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'last_contact_at': instance.lastContactAt?.toIso8601String(),
      'metadata': instance.metadata,
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };
