import 'package:json_annotation/json_annotation.dart';

part 'update_customer_request.g.dart';

@JsonSerializable()
class UpdateCustomerRequest {
  final String fullName;
  final String? phoneNumber;
  final String? status;
  final String? source;
  final String? occupation;
  final String? idType;
  final String? idNo;
  final String? cifNo;
  final String? sex;
  final String? dob; // Format: "1990-05-15"
  final String? email;
  final String? provinceId;
  final String? wardsId;
  final String? permanentAddress;
  final String? currentAddress;
  final String? workplace;
  final String? monthlyIncome;
  final String? workExperience;
  final List<String> tagIds;

  const UpdateCustomerRequest({
    required this.fullName,
    this.phoneNumber,
    this.status,
    this.source,
    this.occupation,
    this.idType,
    this.idNo,
    this.cifNo,
    this.sex,
    this.dob,
    this.email,
    this.provinceId,
    this.wardsId,
    this.permanentAddress,
    this.currentAddress,
    this.workplace,
    this.monthlyIncome,
    this.workExperience,
    this.tagIds = const <String>[],
  });

  factory UpdateCustomerRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateCustomerRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateCustomerRequestToJson(this);

  UpdateCustomerRequest copyWith({
    String? fullName,
    String? phoneNumber,
    String? status,
    String? source,
    String? occupation,
    String? idType,
    String? idNo,
    String? cifNo,
    String? sex,
    String? dob,
    String? email,
    String? provinceId,
    String? wardsId,
    String? permanentAddress,
    String? currentAddress,
    String? workplace,
    String? monthlyIncome,
    String? workExperience,
    List<String>? tagIds,
  }) {
    return UpdateCustomerRequest(
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      status: status ?? this.status,
      source: source ?? this.source,
      occupation: occupation ?? this.occupation,
      idType: idType ?? this.idType,
      idNo: idNo ?? this.idNo,
      cifNo: cifNo ?? this.cifNo,
      sex: sex ?? this.sex,
      dob: dob ?? this.dob,
      email: email ?? this.email,
      provinceId: provinceId ?? this.provinceId,
      wardsId: wardsId ?? this.wardsId,
      permanentAddress: permanentAddress ?? this.permanentAddress,
      currentAddress: currentAddress ?? this.currentAddress,
      workplace: workplace ?? this.workplace,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
      workExperience: workExperience ?? this.workExperience,
      tagIds: tagIds ?? this.tagIds,
    );
  }

  @override
  String toString() {
    return 'UpdateCustomerRequest(fullName: $fullName, phoneNumber: $phoneNumber, status: $status)';
  }
}
