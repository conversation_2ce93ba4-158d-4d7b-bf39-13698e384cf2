/// Model cho request parameters của API get_customer_summary
class CustomerSummaryRequest {
  final String? pKeysearch;
  final String? pStatus;
  final String? pCreatedFrom;
  final String? pCreatedTo;
  final List<String>? pBranchIds;
  final List<String>? pRegionIds;
  final String? pManagerCif;
  final List<String>? pCustomerTagIds;

  const CustomerSummaryRequest({
    this.pKeysearch,
    this.pStatus,
    this.pCreatedFrom,
    this.pCreatedTo,
    this.pBranchIds,
    this.pRegionIds,
    this.pManagerCif,
    this.pCustomerTagIds,
  });

  factory CustomerSummaryRequest.fromJson(Map<String, dynamic> json) {
    return CustomerSummaryRequest(
      pKeysearch: json['p_keysearch']?.toString(),
      pStatus: json['p_status']?.toString(),
      pCreatedFrom: json['p_created_from']?.toString(),
      pCreatedTo: json['p_created_to']?.toString(),
      pBranchIds: json['p_branch_ids'] != null 
          ? List<String>.from(json['p_branch_ids'])
          : null,
      pRegionIds: json['p_region_ids'] != null
          ? List<String>.from(json['p_region_ids'])
          : null,
      pManagerCif: json['p_manager_cif']?.toString(),
      pCustomerTagIds: json['p_customer_tag_ids'] != null
          ? List<String>.from(json['p_customer_tag_ids'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    
    if (pKeysearch != null && pKeysearch!.isNotEmpty) {
      data['p_keysearch'] = pKeysearch;
    }
    if (pStatus != null && pStatus!.isNotEmpty) {
      data['p_status'] = pStatus;
    }
    if (pCreatedFrom != null && pCreatedFrom!.isNotEmpty) {
      data['p_created_from'] = pCreatedFrom;
    }
    if (pCreatedTo != null && pCreatedTo!.isNotEmpty) {
      data['p_created_to'] = pCreatedTo;
    }
    if (pBranchIds != null && pBranchIds!.isNotEmpty) {
      data['p_branch_ids'] = pBranchIds;
    }
    if (pRegionIds != null && pRegionIds!.isNotEmpty) {
      data['p_region_ids'] = pRegionIds;
    }
    if (pManagerCif != null && pManagerCif!.isNotEmpty) {
      data['p_manager_cif'] = pManagerCif;
    }
    if (pCustomerTagIds != null && pCustomerTagIds!.isNotEmpty) {
      data['p_customer_tag_ids'] = pCustomerTagIds;
    }
    
    return data;
  }

  @override
  String toString() {
    return 'CustomerSummaryRequest{'
        'pKeysearch: $pKeysearch, '
        'pStatus: $pStatus, '
        'pCreatedFrom: $pCreatedFrom, '
        'pCreatedTo: $pCreatedTo, '
        'pBranchIds: $pBranchIds, '
        'pRegionIds: $pRegionIds, '
        'pManagerCif: $pManagerCif, '
        'pCustomerTagIds: $pCustomerTagIds'
        '}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerSummaryRequest &&
          runtimeType == other.runtimeType &&
          pKeysearch == other.pKeysearch &&
          pStatus == other.pStatus &&
          pCreatedFrom == other.pCreatedFrom &&
          pCreatedTo == other.pCreatedTo &&
          _listEquals(pBranchIds, other.pBranchIds) &&
          _listEquals(pRegionIds, other.pRegionIds) &&
          pManagerCif == other.pManagerCif &&
          _listEquals(pCustomerTagIds, other.pCustomerTagIds);

  @override
  int get hashCode =>
      pKeysearch.hashCode ^
      pStatus.hashCode ^
      pCreatedFrom.hashCode ^
      pCreatedTo.hashCode ^
      pBranchIds.hashCode ^
      pRegionIds.hashCode ^
      pManagerCif.hashCode ^
      pCustomerTagIds.hashCode;

  static bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
