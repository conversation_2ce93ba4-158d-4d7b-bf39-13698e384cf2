// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'create_customer_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CreateCustomerRequest _$CreateCustomerRequestFromJson(
  Map<String, dynamic> json,
) => CreateCustomerRequest(
  fullName: json['fullName'] as String,
  phoneNumber: json['phoneNumber'] as String?,
  email: json['email'] as String?,
  sex: json['sex'] as String?,
  dob: json['dob'] as String?,
  idNo: json['idNo'] as String?,
  idType: json['idType'] as String?,
  permanentAddress: json['permanentAddress'] as String?,
  currentAddress: json['currentAddress'] as String?,
  provinceId: json['provinceId'] as String?,
  wardsId: json['wardsId'] as String?,
  occupation: json['occupation'] as String?,
  workplace: json['workplace'] as String?,
  monthlyIncome: json['monthlyIncome'] as String?,
  workExperience: json['workExperience'] as String?,
  status: json['status'] as String?,
  source: json['source'] as String?,
  tagIds:
      (json['tagIds'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const <String>[],
  noteContent: json['noteContent'] as String?,
);

Map<String, dynamic> _$CreateCustomerRequestToJson(
  CreateCustomerRequest instance,
) => <String, dynamic>{
  'fullName': instance.fullName,
  'phoneNumber': instance.phoneNumber,
  'email': instance.email,
  'sex': instance.sex,
  'dob': instance.dob,
  'idNo': instance.idNo,
  'idType': instance.idType,
  'permanentAddress': instance.permanentAddress,
  'currentAddress': instance.currentAddress,
  'provinceId': instance.provinceId,
  'wardsId': instance.wardsId,
  'occupation': instance.occupation,
  'workplace': instance.workplace,
  'monthlyIncome': instance.monthlyIncome,
  'workExperience': instance.workExperience,
  'status': instance.status,
  'source': instance.source,
  'tagIds': instance.tagIds,
  'noteContent': instance.noteContent,
};
