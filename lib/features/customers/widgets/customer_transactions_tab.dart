import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/customer_model.dart';

class CustomerTransactionsTab extends StatelessWidget {
  final CustomerModel customer;

  const CustomerTransactionsTab({
    super.key,
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    // Mock transaction data - in real app would come from API
    final transactions = [
      {
        'id': 'TX001',
        'type': 'Vay tiêu dùng',
        'amount': '50,000,000',
        'status': 'Đã phê duyệt',
        'date': '2024-01-15',
        'description': 'Vay mua xe máy',
        'interestRate': '12%/năm',
        'term': '24 tháng',
      },
      {
        'id': 'TX002',
        'type': 'Thẻ tín dụng',
        'amount': '20,000,000',
        'status': '<PERSON>ang xử lý',
        'date': '2024-01-10',
        'description': 'Thẻ tín dụng Visa Gold',
        'interestRate': '24%/năm',
        'term': 'Không thời hạn',
      },
      {
        'id': 'TX003',
        'type': 'Tiền gửi tiết kiệm',
        'amount': '100,000,000',
        'status': 'Hoạt động',
        'date': '2024-01-05',
        'description': 'Gửi tiết kiệm có kỳ hạn',
        'interestRate': '6.5%/năm',
        'term': '12 tháng',
      },
    ];

    if (transactions.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      children: [
        // Create Transaction Button
        Container(
          width: double.infinity,
          margin: EdgeInsets.all(AppDimensions.paddingM),
          child: ElevatedButton.icon(
            onPressed: () {
              _showCreateTransactionModal(context);
            },
            icon: const Icon(TablerIcons.plus),
            label: const Text('Tạo giao dịch mới'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),

        // Transactions List
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
            itemCount: transactions.length,
            itemBuilder: (context, index) {
              final transaction = transactions[index];
              
              return Container(
                margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.shadowLight,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: InkWell(
                  onTap: () {
                    _showTransactionDetail(context, transaction);
                  },
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  child: Padding(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.all(AppDimensions.paddingS),
                              decoration: BoxDecoration(
                                color: _getTransactionColor(transaction['type'] as String).withValues(alpha: 0.1),
                                shape: BoxShape.circle,
                              ),
                                                             child: Icon(
                                 _getTransactionIcon(transaction['type'] as String),
                                 color: _getTransactionColor(transaction['type'] as String),
                                 size: AppDimensions.iconM,
                               ),
                            ),
                            SizedBox(width: AppDimensions.spacingM),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    transaction['type'] as String,
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    'ID: ${transaction['id']}',
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: AppDimensions.paddingS,
                                vertical: AppDimensions.paddingXS,
                              ),
                                                             decoration: BoxDecoration(
                                 color: _getStatusColor(transaction['status'] as String).withValues(alpha: 0.1),
                                 borderRadius: BorderRadius.circular(12),
                                 border: Border.all(
                                   color: _getStatusColor(transaction['status'] as String).withValues(alpha: 0.3),
                                   width: 1,
                                 ),
                               ),
                               child: Text(
                                 transaction['status'] as String,
                                 style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                   color: _getStatusColor(transaction['status'] as String),
                                   fontWeight: FontWeight.w600,
                                 ),
                               ),
                            ),
                          ],
                        ),
                        
                        SizedBox(height: AppDimensions.spacingM),
                        
                        // Amount
                        Row(
                          children: [
                            Icon(
                              TablerIcons.currency_dong,
                              size: AppDimensions.iconS,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(width: AppDimensions.spacingS),
                            Text(
                              'Số tiền: ',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                                                         Text(
                               '${transaction['amount']} VND',
                               style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                 fontWeight: FontWeight.w600,
                                 color: _getTransactionColor(transaction['type'] as String),
                               ),
                             ),
                          ],
                        ),
                        
                        SizedBox(height: AppDimensions.spacingS),
                        
                        // Description
                        Text(
                          transaction['description'] as String,
                          style: Theme.of(context).textTheme.bodyMedium,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        SizedBox(height: AppDimensions.spacingM),
                        
                        // Details
                        Row(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    TablerIcons.percentage,
                                    size: AppDimensions.iconS,
                                    color: AppColors.textTertiary,
                                  ),
                                  SizedBox(width: AppDimensions.spacingXS),
                                  Text(
                                    transaction['interestRate'] as String,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textTertiary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Row(
                                children: [
                                  Icon(
                                    TablerIcons.clock,
                                    size: AppDimensions.iconS,
                                    color: AppColors.textTertiary,
                                  ),
                                  SizedBox(width: AppDimensions.spacingXS),
                                  Text(
                                    transaction['term'] as String,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textTertiary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              transaction['date'] as String,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textTertiary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.neutral200,
                shape: BoxShape.circle,
              ),
              child: Icon(
                TablerIcons.file_text,
                size: 40,
                color: AppColors.neutral500,
              ),
            ),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'Chưa có giao dịch nào',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Tạo giao dịch đầu tiên cho khách hàng này',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                _showCreateTransactionModal(context);
              },
              icon: const Icon(TablerIcons.plus),
              label: const Text('Tạo giao dịch đầu tiên'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateTransactionModal(BuildContext context) {
    // TODO: Show modal to create new transaction
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Chức năng tạo giao dịch đang phát triển...')),
    );
  }

  void _showTransactionDetail(BuildContext context, Map<String, dynamic> transaction) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.7,
          minChildSize: 0.5,
          maxChildSize: 0.9,
          builder: (_, scrollController) {
            return Container(
              margin: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              ),
              child: Column(
                children: [
                  // Handle
                  Container(
                    margin: EdgeInsets.only(top: AppDimensions.paddingS),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.neutral300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  
                  // Content
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      padding: EdgeInsets.all(AppDimensions.paddingL),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(AppDimensions.paddingM),
                                decoration: BoxDecoration(
                                  color: _getTransactionColor(transaction['type']).withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  _getTransactionIcon(transaction['type']),
                                  color: _getTransactionColor(transaction['type']),
                                  size: AppDimensions.iconL,
                                ),
                              ),
                              SizedBox(width: AppDimensions.spacingM),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      transaction['type'],
                                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      'ID: ${transaction['id']}',
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          
                          SizedBox(height: AppDimensions.spacingL),
                          
                          // Status
                          Container(
                            padding: EdgeInsets.all(AppDimensions.paddingM),
                            decoration: BoxDecoration(
                              color: _getStatusColor(transaction['status']).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                              border: Border.all(
                                color: _getStatusColor(transaction['status']).withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  _getStatusIcon(transaction['status']),
                                  color: _getStatusColor(transaction['status']),
                                  size: AppDimensions.iconM,
                                ),
                                SizedBox(width: AppDimensions.spacingM),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Trạng thái',
                                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                      Text(
                                        transaction['status'],
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          fontWeight: FontWeight.w600,
                                          color: _getStatusColor(transaction['status']),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          SizedBox(height: AppDimensions.spacingL),
                          
                          // Details Grid
                          _buildDetailRow(
                            context: context,
                            icon: TablerIcons.currency_dong,
                            label: 'Số tiền',
                            value: '${transaction['amount']} VND',
                          ),
                          _buildDetailRow(
                            context: context,
                            icon: TablerIcons.percentage,
                            label: 'Lãi suất',
                            value: transaction['interestRate'],
                          ),
                          _buildDetailRow(
                            context: context,
                            icon: TablerIcons.clock,
                            label: 'Thời hạn',
                            value: transaction['term'],
                          ),
                          _buildDetailRow(
                            context: context,
                            icon: TablerIcons.calendar,
                            label: 'Ngày tạo',
                            value: transaction['date'],
                          ),
                          _buildDetailRow(
                            context: context,
                            icon: TablerIcons.file_text,
                            label: 'Mô tả',
                            value: transaction['description'],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildDetailRow({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.neutral200,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: AppDimensions.iconS,
              color: AppColors.neutral600,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingXS),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'Vay tiêu dùng':
      case 'Vay thế chấp':
        return TablerIcons.cash;
      case 'Thẻ tín dụng':
        return TablerIcons.credit_card;
      case 'Tiền gửi tiết kiệm':
        return TablerIcons.pig;
      case 'Bảo hiểm':
        return TablerIcons.shield;
      default:
        return TablerIcons.file_text;
    }
  }

  Color _getTransactionColor(String type) {
    switch (type) {
      case 'Vay tiêu dùng':
      case 'Vay thế chấp':
        return AppColors.kienlongOrange;
      case 'Thẻ tín dụng':
        return AppColors.kienlongSkyBlue;
      case 'Tiền gửi tiết kiệm':
        return AppColors.success;
      case 'Bảo hiểm':
        return AppColors.info;
      default:
        return AppColors.neutral500;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Đã phê duyệt':
      case 'Hoạt động':
        return AppColors.success;
      case 'Đang xử lý':
      case 'Chờ duyệt':
        return AppColors.warning;
      case 'Từ chối':
      case 'Đã hủy':
        return AppColors.error;
      case 'Hoàn thành':
        return AppColors.info;
      default:
        return AppColors.neutral500;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Đã phê duyệt':
      case 'Hoạt động':
        return TablerIcons.circle_check;
      case 'Đang xử lý':
      case 'Chờ duyệt':
        return TablerIcons.clock;
      case 'Từ chối':
      case 'Đã hủy':
        return TablerIcons.circle_x;
      case 'Hoàn thành':
        return TablerIcons.circle_check;
      default:
        return TablerIcons.circle;
    }
  }
} 