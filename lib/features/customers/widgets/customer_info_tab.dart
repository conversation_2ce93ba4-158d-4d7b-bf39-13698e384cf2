import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';
import '../blocs/customer_detail_bloc.dart';
import '../blocs/customer_detail_event.dart';
import '../blocs/customer_detail_state.dart';
import '../models/customer_model.dart';

class CustomerInfoTab extends StatefulWidget {
  final CustomerModel customer;

  const CustomerInfoTab({super.key, required this.customer});

  @override
  State<CustomerInfoTab> createState() => _CustomerInfoTabState();
}

class _CustomerInfoTabState extends State<CustomerInfoTab>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Widget _buildInfoCard({
    required BuildContext context,
    required String title,
    required List<Widget> children,
  }) {
    return AnimatedBuilder(
      animation: _slideAnimation,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: Container(
            margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(color: AppColors.borderLight, width: 1),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Card Header
                Container(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  decoration: BoxDecoration(
                    color: AppColors.kienlongOrange.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(AppDimensions.radiusM),
                      topRight: Radius.circular(AppDimensions.radiusM),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.kienlongOrange,
                            ),
                      ),
                    ],
                  ),
                ),

                // Card Content
                Padding(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: children,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _buildFullAddress(String? address, String? province, String? ward) {
    final parts = <String>[];
    
    // Thêm địa chỉ chính
    if (address != null && address.isNotEmpty) {
      parts.add(address);
    }
    
    // Thêm phường/xã
    if (ward != null && ward.isNotEmpty) {
      parts.add(ward);
    }
    
    // Thêm tỉnh/thành phố
    if (province != null && province.isNotEmpty) {
      parts.add(province);
    }
    
    // Nếu không có thông tin nào, trả về 'Chưa xác định'
    if (parts.isEmpty) {
      return 'Chưa xác định';
    }
    
    // Nối các phần địa chỉ bằng dấu phẩy
    return parts.join(', ');
  }

  /// Format số tiền theo định dạng tiền tệ Việt Nam
  String _formatCurrency(String? amount) {
    if (amount == null || amount.isEmpty) {
      return 'Chưa xác định';
    }
    
    try {
      // Chuyển đổi string thành double
      final doubleValue = double.tryParse(amount);
      if (doubleValue == null) {
        return 'Chưa xác định';
      }
      
      // Format theo định dạng tiền tệ Việt Nam
      final formatter = NumberFormat.currency(
        locale: 'vi_VN',
        symbol: 'VND',
        decimalDigits: 0,
      );
      
      return formatter.format(doubleValue);
    } catch (e) {
      return 'Chưa xác định';
    }
  }

  Widget _buildInfoRow({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    Color? iconColor,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: (iconColor ?? AppColors.neutral500).withValues(
                  alpha: 0.1,
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: AppDimensions.iconS,
                color: iconColor ?? AppColors.neutral500,
              ),
            ),
            SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null)
              Icon(
                TablerIcons.chevron_right,
                size: AppDimensions.iconS,
                color: AppColors.textSecondary,
              ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CustomerDetailBloc, CustomerDetailState>(
      builder: (context, state) {
        if (state is CustomerDetailLoading) {
          return _buildLoadingState();
        } else if (state is CustomerDetailFailure) {
          return _buildErrorState(state);
        } else if (state is CustomerDetailSuccess) {
          return _buildSuccessState(state);
        } else {
          return _buildLoadingState();
        }
      },
    );
  }

  Widget _buildLoadingState() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              children: List.generate(3, (index) => _buildSkeletonCard()),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(CustomerDetailFailure state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(TablerIcons.alert_circle, size: 64, color: Colors.grey[400]),
          const SizedBox(height: AppDimensions.spacingM),
          Text('Có lỗi xảy ra', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            state.error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingL),
          ElevatedButton(
            onPressed: () {
              context.read<CustomerDetailBloc>().add(
                CustomerDetailRefreshed(widget.customer.id),
              );
            },
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessState(CustomerDetailSuccess state) {
    final customer = state.customer;

    // Trigger animations when data loads
    if (_fadeAnimation.value == 0.0) {
      _fadeController.forward();
      _slideController.forward();
    }

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              children: [
                // Personal Information Card (Tab thông tin cá nhân)
                _buildInfoCard(
                  context: context,
                  title: 'Thông tin cá nhân',
                  children: [
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.id,
                      label: 'Số GTTT',
                      value: customer.idCardNumber ?? 'Chưa xác định',
                      iconColor: AppColors.warning,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.map_pin,
                      label: 'Địa chỉ thường trú',
                      value: _buildFullAddress(
                        customer.permanentAddress,
                        customer.province?.name,
                        customer.ward?.name,
                      ),
                      iconColor: AppColors.info,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.mail,
                      label: 'Email',
                      value: customer.email ?? 'Chưa xác định',
                      iconColor: AppColors.kienlongSkyBlue,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.home,
                      label: 'Địa chỉ hiện tại',
                      value: customer.currentAddress ?? 'Chưa xác định',
                      iconColor: AppColors.success,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.gender_male,
                      label: 'Giới tính',
                      value: customer.gender?.label ?? 'Chưa xác định',
                      iconColor: AppColors.info,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.calendar,
                      label: 'Ngày sinh',
                      value: customer.birthDate != null
                          ? '${customer.birthDate!.day}/${customer.birthDate!.month}/${customer.birthDate!.year}'
                          : 'Chưa xác định',
                      iconColor: AppColors.success,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.building,
                      label: 'Nơi làm việc',
                      value: customer.workplace ?? 'Chưa xác định',
                      iconColor: AppColors.kienlongSkyBlue,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.clock,
                      label: 'Kinh nghiệm làm việc',
                      value: customer.workExperience?.label ?? 'Chưa xác định',
                      iconColor: AppColors.info,
                    ),
                  ],
                ),

                // Customer Profile Card (Hồ sơ khách hàng)
                _buildInfoCard(
                  context: context,
                  title: 'Hồ sơ khách hàng',
                  children: [
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.credit_card,
                      label: 'Số CIF',
                      value: customer.cifNumber ?? 'Chưa xác định',
                      iconColor: AppColors.kienlongOrange,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.briefcase,
                      label: 'Nghề nghiệp',
                      value: customer.occupation ?? 'Chưa xác định',
                      iconColor: AppColors.kienlongOrange,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.users,
                      label: 'Nguồn khách hàng',
                      value: customer.source?.label ?? 'Chưa xác định',
                      iconColor: AppColors.info,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.currency_dong,
                      label: 'Thu nhập hàng tháng',
                      value: _formatCurrency(customer.monthlyIncome),
                      iconColor: AppColors.success,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.calendar_plus,
                      label: 'Ngày tạo hồ sơ',
                      value: customer.createdAt != null
                          ? DateFormat('dd/MM/yyyy').format(customer.createdAt!)
                          : 'Chưa xác định',
                      iconColor: AppColors.success,
                    ),
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.calendar_time,
                      label: 'Ngày cập nhật',
                      value: customer.updatedAt != null
                          ? DateFormat('dd/MM/yyyy').format(customer.updatedAt!)
                          : 'Chưa xác định',
                      iconColor: AppColors.info,
                    ),
                    // Assigned Manager
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.user_check,
                      label: 'Cán bộ phụ trách',
                      value: customer.assignedManager?.fullName?.isNotEmpty == true
                          ? customer.assignedManager?.fullName ?? ''
                          : 'Chưa phân công',
                      iconColor: AppColors.kienlongOrange,
                    ),
                    // Branch
                    _buildInfoRow(
                      context: context,
                      icon: TablerIcons.building,
                      label: 'Chi nhánh',
                      value: (customer.branch?.name.isNotEmpty == true)
                          ? customer.branch?.name ?? 'Chưa xác định'
                          : 'Chưa xác định',
                      iconColor: AppColors.kienlongSkyBlue,
                    ),
                  ],
                ),

                // Additional Notes Card (if any)
                if (customer.notes != null && customer.notes!.isNotEmpty)
                  _buildInfoCard(
                    context: context,
                    title: 'Ghi chú',
                    children: [
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(AppDimensions.paddingM),
                        decoration: BoxDecoration(
                          color: AppColors.neutral100,
                          borderRadius: BorderRadius.circular(
                            AppDimensions.radiusS,
                          ),
                          border: Border.all(
                            color: AppColors.borderLight,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          customer.notes!,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSkeletonCard() {
    return Container(
      height: 120,
      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.2),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                children: List.generate(
                  3,
                  (index) => Container(
                    height: 20,
                    margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
