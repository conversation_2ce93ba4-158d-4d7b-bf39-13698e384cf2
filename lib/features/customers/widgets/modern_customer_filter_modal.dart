import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/models/region_model.dart';
import '../../../shared/models/branch_model.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/models/customer_tag_model.dart';
import '../../../shared/utils/color_utils.dart';
import '../../employees/models/employee_model.dart';
import 'staff_search_button.dart';

class ModernCustomerFilterModal extends StatefulWidget {
  final DateTimeRange? initialDateRange;
  final String? initialStatus;
  final List<RegionModel>? initialRegions;
  final List<BranchModel>? initialBusinessUnits;
  final EmployeeModel? initialStaff;
  final List<String>? initialTags;
  final List<ConfigModel> availableStatuses;
  final List<RegionModel> availableRegions;
  final List<BranchModel> availableBusinessUnits;
  final List<EmployeeModel> availableStaff;
  final List<CustomerTagModel> availableCustomerTags;
  final Function(DateTimeRange?, String?, List<RegionModel>?, List<BranchModel>?, EmployeeModel?, List<String>?) onApply;
  final Function(List<RegionModel>)? onRegionsChanged;
  final bool branchesLoading;
  final Stream<List<BranchModel>>? branchesStream;
  final Stream<List<EmployeeModel>>? employeeSearchStream;
  final Function(String)? onEmployeeSearch;
  final bool isSearchingEmployees;

  const ModernCustomerFilterModal({
    super.key,
    this.initialDateRange,
    this.initialStatus,
    this.initialRegions,
    this.initialBusinessUnits,
    this.initialStaff,
    this.initialTags,
    required this.availableStatuses,
    required this.availableRegions,
    required this.availableBusinessUnits,
    required this.availableStaff,
    required this.availableCustomerTags,
    required this.onApply,
    this.onRegionsChanged,
    this.branchesLoading = false,
    this.branchesStream,
    this.employeeSearchStream,
    this.onEmployeeSearch,
    this.isSearchingEmployees = false,
  });

  @override
  State<ModernCustomerFilterModal> createState() => _ModernCustomerFilterModalState();
}

class _ModernCustomerFilterModalState extends State<ModernCustomerFilterModal> {
  DateTimeRange? _selectedDateRange;
  String? _selectedStatus;
  List<RegionModel> _selectedRegions = [];
  List<BranchModel> _selectedBusinessUnits = [];
  EmployeeModel? _selectedStaff;
  final List<String> _selectedTags = [];
  
  // Track which quick date is selected (simple approach)
  String? _selectedQuickDate;
  
  // Dynamic branches từ stream
  List<BranchModel> _dynamicBranches = [];
  
  // Status colors từ API - sử dụng widget.availableStatuses thay thế
  
  // Removed: GlobalKey<_StaffSearchInputState> _staffSearchKey - no longer needed

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange;
    _selectedStatus = widget.initialStatus;
    _selectedRegions = widget.initialRegions ?? [];
    _selectedBusinessUnits = widget.initialBusinessUnits ?? [];
    _selectedStaff = widget.initialStaff;
    _selectedTags.addAll(widget.initialTags ?? []);
    

    
    // Khởi tạo branches ban đầu - bao gồm cả widget.availableBusinessUnits
    _dynamicBranches = widget.availableBusinessUnits;
    
    // Listen stream để cập nhật branches (khi parent load all branches)
    widget.branchesStream?.listen((branches) {
      if (mounted) {
        setState(() {
          if (branches.isNotEmpty) {
            // SMART MERGE: Combine API response với selected branches để preserve selection
            final Set<String> allBranchIds = branches.map((branch) => branch.id).toSet();
            final List<BranchModel> missingSelectedBranches = _selectedBusinessUnits
                .where((selected) => !allBranchIds.contains(selected.id))
                .toList();
            
            // Merge: API branches + missing selected branches
            final List<BranchModel> mergedBranches = [
              ...branches,
              ...missingSelectedBranches,
            ];
            
            _dynamicBranches = mergedBranches;
          } else {
            // Nếu nhận empty list, CHỈ update nếu không có branches nào trước đó
            // Tránh clear branches khi parent đang loading
            if (_dynamicBranches.isEmpty) {
              _dynamicBranches = branches;
            }
          }
        });
      }
    });
    
    // Trigger load all branches nếu chưa có branches và parent không đang loading
    // Hoặc nếu parent không đang loading nhưng cũng chưa có data
    if (widget.onRegionsChanged != null && 
        widget.availableBusinessUnits.isEmpty && 
        !widget.branchesLoading) {
      // Pass empty list để parent load all branches
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onRegionsChanged?.call([]);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppDimensions.paddingS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
         Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.filter,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Text(
                    'Lọc nâng cao khách hàng',
                    style: AppTypography.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Spacer(),
                  Expanded(child: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      TablerIcons.x,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),)
                  
                ],
              ),
            ),
          
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick date filters
                  _buildQuickDateFilters(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Custom date range
                  _buildDateRangeSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Status filter
                  _buildStatusSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Region filter
                  _buildRegionSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Business Unit filter
                  _buildBusinessUnitSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Staff filter với 3 options
                  _buildStaffSection(),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Quick filters
                  _buildQuickFilters(),
                  const SizedBox(height: AppDimensions.spacingXL),
                ],
              ),
            ),
          ),
          
          // Bottom actions
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildQuickDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ngày thêm khách hàng',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: [
            _buildQuickDateChip('Hôm nay', () => _setQuickDate(0)),
            _buildQuickDateChip('7 ngày', () => _setQuickDate(7)),
            _buildQuickDateChip('Tháng này', () => _setQuickDate(30)),
            _buildQuickDateChip('3 tháng', () => _setQuickDate(90)),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickDateChip(String label, VoidCallback onTap) {
    // Check if this chip is currently selected
    final isSelected = _isQuickDateSelected(label);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: isSelected 
            ? AppColors.kienlongSkyBlue.withValues(alpha: 0.3)
            : AppColors.kienlongSkyBlue.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected 
              ? AppColors.kienlongSkyBlue
              : AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Text(
          label,
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: isSelected 
              ? AppColors.kienlongSkyBlue
              : AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
            fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Khoảng thời gian tùy chọn',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        GestureDetector(
          onTap: _selectDateRange,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.calendar,
                  color: AppColors.kienlongOrange,
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    _selectedDateRange == null
                        ? 'Chọn khoảng ngày'
                        : '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: _selectedDateRange == null
                          ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                if (_selectedDateRange != null)
                  GestureDetector(
                    onTap: () => setState(() => _selectedDateRange = null),
                    child: Icon(
                      TablerIcons.x,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      size: AppDimensions.iconS,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trạng thái khách hàng',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: [
            // Thêm item "Tất cả"
            GestureDetector(
              onTap: () => setState(() {
                _selectedStatus = null;
              }),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: _selectedStatus == null
                      ? AppColors.kienlongOrange.withValues(alpha: 0.15)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: _selectedStatus == null
                        ? AppColors.kienlongOrange
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    width: _selectedStatus == null ? 2 : 1,
                  ),
                ),
                child: Text(
                  'Tất cả',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: _selectedStatus == null
                        ? AppColors.kienlongOrange
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight: _selectedStatus == null ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
            // Các status items
            ...widget.availableStatuses.map((status) {
              final isSelected = _selectedStatus == status.code;
              final color = _getStatusColor(status.label ?? '');
              
              return GestureDetector(
                onTap: () => setState(() {
                  _selectedStatus = isSelected ? null : status.code;
                }),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                    vertical: AppDimensions.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? color.withValues(alpha: 0.15)
                        : Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    border: Border.all(
                      color: isSelected
                          ? color
                          : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Text(
                    status.label ?? '',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: isSelected
                          ? color
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildRegionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Khu vực',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Text(
          'Có thể chọn nhiều khu vực',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        GestureDetector(
          onTap: () => _showRegionSelectionModal(),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: _selectedRegions.isNotEmpty 
                  ? AppColors.info
                  : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                width: _selectedRegions.isNotEmpty ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.map_pin,
                  color: _selectedRegions.isNotEmpty 
                    ? AppColors.info 
                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    _selectedRegions.isEmpty
                        ? 'Chọn khu vực'
                        : _selectedRegions.length == 1
                            ? _selectedRegions.first.name
                            : '${_selectedRegions.length} khu vực đã chọn',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: _selectedRegions.isEmpty
                          ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                          : AppColors.info,
                      fontWeight: _selectedRegions.isNotEmpty ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
                Icon(
                  TablerIcons.chevron_down,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: AppDimensions.iconS,
                ),
              ],
            ),
          ),
        ),
        if (_selectedRegions.isNotEmpty) ...[
          const SizedBox(height: AppDimensions.spacingS),
        Wrap(
            spacing: AppDimensions.spacingXS,
            runSpacing: AppDimensions.spacingXS,
            children: _selectedRegions.map((region) => Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    region.name,
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: AppColors.info,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingXS),
                  GestureDetector(
              onTap: () => setState(() {
                      _selectedRegions.remove(region);
                      
                      // Remove all branches thuộc region này
                      _selectedBusinessUnits.removeWhere((unit) => unit.regionId == region.id);
                      
                      // Load branches cho remaining regions hoặc all nếu no regions
                      if (_selectedRegions.isEmpty) {
                        widget.onRegionsChanged?.call([]);
                      } else {
                        widget.onRegionsChanged?.call(_selectedRegions);
                      }
                    }),
                    child: Icon(
                      TablerIcons.x,
                      size: 12,
                      color: AppColors.info,
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildBusinessUnitSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Đơn vị kinh doanh',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Text(
          'Có thể chọn nhiều đơn vị, khu vực sẽ được tự động áp dụng',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        if (widget.branchesLoading)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: AppDimensions.iconS,
                  height: AppDimensions.iconS,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Đang tải đơn vị kinh doanh...',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          )
        else if (_dynamicBranches.isEmpty && !widget.branchesLoading)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                  Icon(
                    TablerIcons.info_circle,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    size: AppDimensions.iconS,
                  ),
                const SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Chưa có đơn vị kinh doanh nào',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          )
        else
          GestureDetector(
            onTap: () => _showBusinessUnitSelectionModal(),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: _selectedBusinessUnits.isNotEmpty 
                    ? AppColors.success
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  width: _selectedBusinessUnits.isNotEmpty ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.building,
                    color: _selectedBusinessUnits.isNotEmpty 
                      ? AppColors.success 
                      : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                child: Text(
                      _selectedBusinessUnits.isEmpty
                          ? 'Chọn đơn vị kinh doanh'
                          : _selectedBusinessUnits.length == 1
                              ? _selectedBusinessUnits.first.name
                              : '${_selectedBusinessUnits.length} đơn vị đã chọn',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: _selectedBusinessUnits.isEmpty
                            ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)
                            : AppColors.success,
                        fontWeight: _selectedBusinessUnits.isNotEmpty ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
                  Icon(
                    TablerIcons.chevron_down,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    size: AppDimensions.iconS,
                  ),
                ],
              ),
            ),
          ),
        if (_selectedBusinessUnits.isNotEmpty) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Wrap(
            spacing: AppDimensions.spacingXS,
            runSpacing: AppDimensions.spacingXS,
            children: _selectedBusinessUnits.map((unit) => Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    unit.name,
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingXS),
                                    GestureDetector(
              onTap: () => setState(() {
                      _selectedBusinessUnits.remove(unit);
                      
                      // Không cần cleanup regions vì regions độc lập với branches
                      // _cleanupUnusedRegions(); // Removed - regions are independent
                    }),
                    child: Icon(
                      TablerIcons.x,
                      size: 12,
                      color: AppColors.success,
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildStaffSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cán bộ phụ trách',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Text(
          'Nhấn để chọn cán bộ từ danh sách',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        
        // Sử dụng StaffSearchButton thay cho _StaffSearchInput
        StaffSearchButton(
          availableStaff: widget.availableStaff,
          selectedStaff: _selectedStaff,
          onStaffSelected: (staff) {
            setState(() {
              _selectedStaff = staff;
            });
          },
          onSearchChanged: widget.onEmployeeSearch,
          isSearching: widget.isSearchingEmployees,
          employeeSearchStream: widget.employeeSearchStream,
        ),
      ],
    );
  }

  Widget _buildQuickFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Lọc nhanh',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Text(
          'Sử dụng tags để lọc nhanh khách hàng:',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        if (widget.availableCustomerTags.isEmpty)
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  TablerIcons.info_circle,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: AppDimensions.iconS,
                ),
                const SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Chưa có tags nào',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          )
        else
          Wrap(
            spacing: AppDimensions.spacingS,
            runSpacing: AppDimensions.spacingS,
            children: widget.availableCustomerTags.map((tag) {
              final isSelected = _isTagSelected(tag.id ?? '');
              final color = _getTagColor(tag);
              final icon = _getTagIcon(tag.name ?? '');
              
              return InkWell(
                onTap: () => _toggleTag(tag.id ?? ''),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                    vertical: AppDimensions.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? color.withValues(alpha: 0.2)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                    border: Border.all(
                      color: color,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected)
                        Icon(
                          TablerIcons.check,
                          size: AppDimensions.iconS,
                          color: color,
                        ),
                      if (isSelected) const SizedBox(width: AppDimensions.spacingXS),
                      Icon(icon, color: color, size: AppDimensions.iconS),
                      const SizedBox(width: AppDimensions.spacingXS),
                      Text(
                        tag.name ?? '',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: color,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }



  Widget _buildBottomActions() {
    return Container(
      padding: EdgeInsets.only(
        left: AppDimensions.paddingL,
        right: AppDimensions.paddingL,
        top: AppDimensions.paddingM,
        bottom: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom + AppDimensions.paddingL,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _selectedDateRange = null;
                  _selectedStatus = null;
                  _selectedRegions.clear();
                  _selectedBusinessUnits.clear();
                  _selectedStaff = null;
                  _selectedTags.clear();
                  _selectedQuickDate = null; // Clear quick date selection
                });
                
                // Staff reset is handled by setState above - no need for manual reset
                
                // Khi clear all filters, chỉ load all branches nếu chưa có branches
                // Tránh trigger loading state không cần thiết
                if (_dynamicBranches.isEmpty) {
                  widget.onRegionsChanged?.call([]);
                }
                
                // Call onApply with null values to clear filters
                widget.onApply(null, null, null, null, null, null);
                Navigator.pop(context);
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                side: BorderSide(color: AppColors.kienlongOrange),
              ),
              child: Text(
                'Xóa lọc',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            flex: 2,
            child: ElevatedButton(
                            onPressed: () {
                // Staff is now EmployeeModel
                final selectedStaff = _selectedStaff;
                
                // Tags are already List<String>
                final tagIds = _selectedTags.isNotEmpty ? _selectedTags : null;
                
                widget.onApply(
                  _selectedDateRange, 
                  _selectedStatus, 
                  _selectedRegions.isNotEmpty ? _selectedRegions : null,
                  _selectedBusinessUnits.isNotEmpty ? _selectedBusinessUnits : null,
                  selectedStaff,
                  tagIds,
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              ),
              child: Text(
                'Áp dụng',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _setQuickDate(int days) {
    final now = DateTime.now();
    setState(() {
      if (days == 0) {
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, now.month, now.day),
          end: DateTime(now.year, now.month, now.day),
        );
        _selectedQuickDate = 'Hôm nay';
      } else if (days == 7) {
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days)),
          end: now,
        );
        _selectedQuickDate = '7 ngày';
      } else if (days == 30) {
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days)),
          end: now,
        );
        _selectedQuickDate = 'Tháng này';
      } else if (days == 90) {
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days)),
          end: now,
        );
        _selectedQuickDate = '3 tháng';
      }
    });
  }

  /// Check if a quick date chip is currently selected (simple approach)
  bool _isQuickDateSelected(String label) {
    return _selectedQuickDate == label;
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(DateTime.now().year - 2),
      lastDate: DateTime(DateTime.now().year + 1),
      initialDateRange: _selectedDateRange,
    );
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
        _selectedQuickDate = null; // Clear quick date selection
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get status color from ConfigModel value field using ColorUtils
  Color _getStatusColor(String statusLabel) {
    // Tìm status option từ widget.availableStatuses (master data)
    final statusOption = widget.availableStatuses.firstWhere(
      (option) => option.label == statusLabel,
      orElse: () => ConfigModel(),
    );
    
    // Use color from API value field if available (như trong classification_step.dart)
    if (statusOption.value != null && statusOption.value!.isNotEmpty) {
      return ColorUtils.hexToColor(statusOption.value!);
    }
    
    // Fallback to predefined colors based on status label
    final fallbackColors = {
      'Đang chăm sóc': AppColors.warning,
      'Tiềm năng': AppColors.info,
      'Đã giao dịch': AppColors.success,
      'Đã chốt': AppColors.kienlongOrange,
    };
    
    return fallbackColors[statusLabel] ?? AppColors.textSecondary;
  }

  /// Get color for tag - prioritize API color, fallback to predefined colors
  Color _getTagColor(CustomerTagModel tag) {
    // Use color from API if available
    if (tag.color != null) {
      return ColorUtils.hexToColor(tag.color!);
    }
    
    // Fallback to predefined colors based on tag name
    final fallbackColors = {
      'VIP': AppColors.error,
      'Premium': AppColors.kienlongOrange,
      'Mới': AppColors.success,
      'Ưu tiên': AppColors.warning,
      'Quan trọng': AppColors.kienlongSkyBlue,
      'Tiềm năng cao': AppColors.kienlongDarkBlue,
      'Cần theo dõi': AppColors.textSecondary,
      'Nóng': AppColors.error,
    };
    
    return fallbackColors[tag.name ?? ''] ?? AppColors.kienlongDarkBlue;
  }

  /// Get icon for tag based on tag name
  IconData _getTagIcon(String tagName) {
    final iconMap = {
      'VIP': TablerIcons.crown,
      'Premium': TablerIcons.star,
      'Mới': TablerIcons.sparkles,
      'Ưu tiên': TablerIcons.flag,
      'Quan trọng': TablerIcons.alert_circle,
      'Tiềm năng cao': TablerIcons.trending_up,
      'Cần theo dõi': TablerIcons.eye,
      'Nóng': TablerIcons.flame,
    };
    
    return iconMap[tagName] ?? TablerIcons.tag;
  }

  /// Toggle tag selection
  void _toggleTag(String tagId) {
    setState(() {
      if (_selectedTags.contains(tagId)) {
        _selectedTags.remove(tagId);
      } else {
        _selectedTags.add(tagId);
      }
    });
  }

  /// Check if tag is selected
  bool _isTagSelected(String tagId) {
    return _selectedTags.contains(tagId);
  }

  void _showRegionSelectionModal() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.3,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
            return _MultiSelectModal<RegionModel>(
              title: 'Chọn khu vực',
              items: widget.availableRegions,
              selectedItems: _selectedRegions,
              color: AppColors.info,
              icon: TablerIcons.map_pin,
              itemToString: (RegionModel region) => region.name,
              onSelectionChanged: (selectedItems) {
                setState(() {
                  // Track which regions were removed
                  final previousRegionIds = _selectedRegions.map((r) => r.id).toSet();
                  final newRegionIds = selectedItems.map((r) => r.id).toSet();
                  final removedRegionIds = previousRegionIds.difference(newRegionIds);
                  
                  _selectedRegions = selectedItems;
                  
                  // Remove branches thuộc các regions đã bị remove
                  if (removedRegionIds.isNotEmpty) {
                    _selectedBusinessUnits.removeWhere((unit) => 
                      unit.regionId != null && removedRegionIds.contains(unit.regionId)
                    );
                  }
                });
                
                // Load branches cho selected regions hoặc all nếu no regions
                if (_selectedRegions.isEmpty) {
                  widget.onRegionsChanged?.call([]);
                } else {
                  widget.onRegionsChanged?.call(_selectedRegions);
                }
              },
              scrollController: scrollController,
            );
          },
        );
      },
    );
  }

  void _showBusinessUnitSelectionModal() {
    // Merge selected branches với available branches để ensure proper highlighting
    final Set<String> availableBranchIds = _dynamicBranches.map((b) => b.id).toSet();
    final List<BranchModel> missingSelectedBranches = _selectedBusinessUnits
        .where((selected) => !availableBranchIds.contains(selected.id))
        .toList();
    
    final List<BranchModel> completeItemsList = [
      ..._dynamicBranches,
      ...missingSelectedBranches,
    ];
    

    
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.3,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
            return _MultiSelectModal<BranchModel>(
              title: 'Chọn đơn vị kinh doanh',
              items: completeItemsList, // ← Use complete list để ensure highlighting
              selectedItems: _selectedBusinessUnits,
              color: AppColors.success,
              icon: TablerIcons.building,
              itemToString: (BranchModel branch) => branch.name,
              onSelectionChanged: (selectedItems) {
                setState(() {
                  _selectedBusinessUnits = selectedItems;
                  
                  // Auto-map regions từ selected branches để cải thiện UX (một chiều)
                  // Nhưng không cleanup regions khi remove branches (regions độc lập)
                  if (selectedItems.isNotEmpty) {
                    _autoMapRegionsFromBranches(selectedItems);
                  }
                  // Không cleanup regions khi clear branches vì regions là filter độc lập
                });
              },
              scrollController: scrollController,
            );
          },
        );
      },
    );
  }

  /// Auto-map regions từ selected branches (ONE-WAY only)
  /// Chỉ map từ branches → regions để cải thiện UX
  /// Không reverse cleanup (regions vẫn độc lập)
  void _autoMapRegionsFromBranches(List<BranchModel> selectedBranches) {
    if (selectedBranches.isEmpty) {
      // Không cleanup regions khi clear branches - regions là filter độc lập
      return;
    }
    
    // Lấy tất cả regionIds từ selected branches
    final Set<String> branchRegionIds = {};
    for (final branch in selectedBranches) {
      if (branch.regionId != null && branch.regionId!.isNotEmpty) {
        branchRegionIds.add(branch.regionId!);
      }
    }
    
    // Merge với existing selected regions (không replace toàn bộ)
    final Set<String> existingRegionIds = _selectedRegions.map((r) => r.id).toSet();
    final Set<String> allRegionIds = {...existingRegionIds, ...branchRegionIds};
    
    // Map regionIds thành RegionModel objects
    final List<RegionModel> newSelectedRegions = [];
    for (final regionId in allRegionIds) {
      final region = widget.availableRegions.where((r) => r.id == regionId).firstOrNull;
      if (region != null) {
        newSelectedRegions.add(region);
      }
    }
    
    // Update selected regions (merge, không replace)
    final previousRegionIds = _selectedRegions.map((r) => r.id).toSet();
    final newRegionIds = newSelectedRegions.map((r) => r.id).toSet();
    
    _selectedRegions = newSelectedRegions;
    
    // Trigger reload branches nếu regions thay đổi
    if (!setEquals(previousRegionIds, newRegionIds)) {
      widget.onRegionsChanged?.call(_selectedRegions);
    }
  }

  /// Clean up regions that no longer have any selected business units - DISABLED
  /// Regions và branches là 2 filter độc lập, không cần cleanup
  // void _cleanupUnusedRegions() {
  //   // Function disabled - regions are independent of branches
  // }


}

// Removed _StaffSearchInput class - replaced with StaffSearchButton

class _MultiSelectModal<T> extends StatefulWidget {
  final String title;
  final List<T> items;
  final List<T> selectedItems;
  final Color color;
  final IconData icon;
  final Function(List<T>) onSelectionChanged;
  final ScrollController scrollController;
  final String Function(T) itemToString;

  const _MultiSelectModal({
    required this.title,
    required this.items,
    required this.selectedItems,
    required this.color,
    required this.icon,
    required this.onSelectionChanged,
    required this.itemToString,
    required this.scrollController,
  });

  @override
  State<_MultiSelectModal<T>> createState() => _MultiSelectModalState<T>();
}

class _MultiSelectModalState<T> extends State<_MultiSelectModal<T>> {
  late List<T> _tempSelectedItems;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tempSelectedItems = List<T>.from(widget.selectedItems);
  }

  List<T> get _filteredItems {
    if (_searchQuery.isEmpty) {
      return widget.items;
    }
    return widget.items.where((item) => 
      widget.itemToString(item).toLowerCase().contains(_searchQuery.toLowerCase())
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppDimensions.paddingS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Row(
                children: [
                  Icon(
                    widget.icon,
                    color: widget.color,
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      widget.title,
                      style: AppTypography.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (_tempSelectedItems.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: widget.color.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(
                          color: widget.color.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        '${_tempSelectedItems.length}',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: widget.color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  const SizedBox(width: AppDimensions.spacingS),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      TablerIcons.x,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Search bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: InputDecoration(
                hintText: 'Tìm kiếm...',
                prefixIcon: Icon(TablerIcons.search, color: widget.color),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: widget.color, width: 2),
                ),
              ),
            ),
          ),

          const SizedBox(height: AppDimensions.spacingM),

          // Items list
          Expanded(
            child: ListView.builder(
              controller: widget.scrollController,
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              itemCount: _filteredItems.length,
              itemBuilder: (context, index) {
                final item = _filteredItems[index];
                // Fix: Use ID comparison instead of object equality for BranchModel
                final isSelected = T == BranchModel 
                  ? _tempSelectedItems.any((selected) => 
                      (selected as BranchModel).id == (item as BranchModel).id)
                  : T == RegionModel
                    ? _tempSelectedItems.any((selected) => 
                        (selected as RegionModel).id == (item as RegionModel).id)
                    : _tempSelectedItems.contains(item);
                
                return Container(
                  margin: const EdgeInsets.only(bottom: AppDimensions.spacingS),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            // Remove by ID comparison for BranchModel/RegionModel
                            if (T == BranchModel) {
                              _tempSelectedItems.removeWhere((selected) => 
                                (selected as BranchModel).id == (item as BranchModel).id);
                            } else if (T == RegionModel) {
                              _tempSelectedItems.removeWhere((selected) => 
                                (selected as RegionModel).id == (item as RegionModel).id);
                            } else {
                            _tempSelectedItems.remove(item);
                            }
                          } else {
                            _tempSelectedItems.add(item);
                          }
                        });
                      },
                      child: Container(
                        padding: const EdgeInsets.all(AppDimensions.paddingM),
                        decoration: BoxDecoration(
                          color: isSelected 
                            ? widget.color.withValues(alpha: 0.15)
                            : Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                          border: Border.all(
                            color: isSelected
                              ? widget.color
                              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                widget.itemToString(item),
                                style: AppTypography.textTheme.bodyMedium?.copyWith(
                                  color: isSelected
                                    ? widget.color
                                    : Theme.of(context).colorScheme.onSurface,
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                                ),
                              ),
                            ),
                            if (isSelected)
                              Icon(
                                TablerIcons.check,
                                color: widget.color,
                                size: AppDimensions.iconS,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Bottom actions
          Container(
            padding: EdgeInsets.only(
              left: AppDimensions.paddingL,
              right: AppDimensions.paddingL,
              top: AppDimensions.paddingM,
              bottom: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom + AppDimensions.paddingL,
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _tempSelectedItems.clear();
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                      side: BorderSide(color: widget.color),
                    ),
                    child: Text(
                      'Xóa tất cả',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: widget.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onSelectionChanged(_tempSelectedItems);
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: widget.color,
                      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                    ),
                    child: Text(
                      'Áp dụng',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

 