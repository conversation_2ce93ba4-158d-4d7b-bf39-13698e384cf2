import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class StepNavigationBar extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final String actionButtonText;
  final VoidCallback? onPrevious;
  final VoidCallback onNext;

  const StepNavigationBar({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.actionButtonText,
    this.onPrevious,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: AppColors.borderLight,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Safe<PERSON><PERSON>(
        child: Row(
          children: [
            // Previous Button
            if (onPrevious != null) ...[
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: onPrevious,
                  icon: Icon(
                    TablerIcons.chevron_left,
                    size: AppDimensions.iconS,
                  ),
                  label: const Text('Quay lại'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.textSecondary,
                    side: BorderSide(color: AppColors.borderLight),
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingM,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
            ],
            
            // Next/Save Button
            Expanded(
              flex: onPrevious != null ? 1 : 1,
              child: ElevatedButton.icon(
                onPressed: onNext,
                icon: Icon(
                  currentStep == totalSteps - 1 
                      ? TablerIcons.device_floppy
                      : TablerIcons.chevron_right,
                  size: AppDimensions.iconS,
                ),
                label: Text(actionButtonText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongOrange,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.paddingM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                  elevation: 2,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 