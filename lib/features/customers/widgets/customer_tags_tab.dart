import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/customer_model.dart';

class CustomerTagsTab extends StatefulWidget {
  final CustomerModel customer;

  const CustomerTagsTab({
    super.key,
    required this.customer,
  });

  @override
  State<CustomerTagsTab> createState() => _CustomerTagsTabState();
}

class _CustomerTagsTabState extends State<CustomerTagsTab> {
  late List<String> _currentTags;
  String? _selectedStatus;

  final List<String> _availableTags = [
    'VIP', 'Premium', 'Mới', 'Tiềm năng cao', 'Quan tâm vay', 
    'Quan tâm gửi', 'Quan tâm thẻ', '<PERSON>h<PERSON>ch cũ', 'Giới thiệu'
  ];

  final List<String> _availableStatuses = [
    'Tiềm năng', '<PERSON>ang chăm sóc', 'Đ<PERSON> giao dịch', 'Đã chốt', 'Tạm dừng'
  ];

  @override
  void initState() {
    super.initState();
    _currentTags = List<String>.from(widget.customer.tags);
    _selectedStatus = widget.customer.status?.label;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Tags Section
          _buildCurrentTagsSection(),
          
          SizedBox(height: AppDimensions.spacingL),
          
          // Add Tags Section
          _buildAddTagsSection(),
          
          SizedBox(height: AppDimensions.spacingL),
          
          // Status Management Section
          _buildStatusSection(),
          
          SizedBox(height: AppDimensions.spacingL),
          
          // Status History Section
          _buildStatusHistorySection(),
        ],
      ),
    );
  }

  Widget _buildCurrentTagsSection() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.tags,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Tags hiện tại',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.kienlongOrange,
                ),
              ),
              const Spacer(),
              Text(
                '${_currentTags.length} tags',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          if (_currentTags.isEmpty)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppDimensions.paddingL),
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.borderLight,
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    TablerIcons.tag_off,
                    size: 32,
                    color: AppColors.neutral500,
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    'Chưa có tag nào',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    'Thêm tags để phân loại khách hàng',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textTertiary,
                    ),
                  ),
                ],
              ),
            )
          else
            Wrap(
              spacing: AppDimensions.spacingS,
              runSpacing: AppDimensions.spacingS,
              children: _currentTags.map((tag) {
                return Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                    vertical: AppDimensions.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: _getTagColor(tag).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getTagColor(tag),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        tag,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: _getTagColor(tag),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      InkWell(
                        onTap: () => _removeTag(tag),
                        borderRadius: BorderRadius.circular(10),
                        child: Icon(
                          TablerIcons.x,
                          size: 16,
                          color: _getTagColor(tag),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildAddTagsSection() {
    final availableToAdd = _availableTags
        .where((tag) => !_currentTags.contains(tag))
        .toList();
    
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.plus,
                color: AppColors.kienlongSkyBlue,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Thêm tags',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.kienlongSkyBlue,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          if (availableToAdd.isEmpty)
            Text(
              'Đã sử dụng tất cả tags có sẵn',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            )
          else
            Wrap(
              spacing: AppDimensions.spacingS,
              runSpacing: AppDimensions.spacingS,
              children: availableToAdd.map((tag) {
                return InkWell(
                  onTap: () => _addTag(tag),
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.neutral100,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.borderLight,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          TablerIcons.plus,
                          size: 14,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(width: AppDimensions.spacingXS),
                        Text(
                          tag,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Custom Tag Input
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Tạo tag tùy chỉnh...',
                    prefixIcon: Icon(
                      TablerIcons.tag,
                      color: AppColors.textSecondary,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      borderSide: BorderSide(color: AppColors.borderLight),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      borderSide: BorderSide(color: AppColors.kienlongOrange),
                    ),
                  ),
                  onSubmitted: (value) {
                    if (value.trim().isNotEmpty && !_currentTags.contains(value.trim())) {
                      _addTag(value.trim());
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSection() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.status_change,
                color: AppColors.info,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Quản lý trạng thái',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.info,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Current Status
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: _getStatusColor(_selectedStatus ?? '').withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: _getStatusColor(_selectedStatus ?? ''),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(_selectedStatus ?? ''),
                  color: _getStatusColor(_selectedStatus ?? ''),
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Trạng thái hiện tại',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        _selectedStatus ?? 'Chưa xác định',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: _getStatusColor(_selectedStatus ?? ''),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Change Status
          Text(
            'Thay đổi trạng thái:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          
          SizedBox(height: AppDimensions.spacingS),
          
          Wrap(
            spacing: AppDimensions.spacingS,
            runSpacing: AppDimensions.spacingS,
            children: _availableStatuses.map((status) {
              final isSelected = status == _selectedStatus;
              return InkWell(
                onTap: () => _changeStatus(status),
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                    vertical: AppDimensions.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? _getStatusColor(status)
                        : _getStatusColor(status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getStatusColor(status),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    status,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Colors.white
                          : _getStatusColor(status),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusHistorySection() {
    // Mock status history data
    final statusHistory = [
      {
        'status': 'Đang chăm sóc',
        'date': '2024-01-15',
        'staff': 'Nguyễn Văn A',
        'note': 'Khách hàng quan tâm đến sản phẩm vay',
      },
      {
        'status': 'Tiềm năng',
        'date': '2024-01-10',
        'staff': 'Nguyễn Văn A',
        'note': 'Khách hàng mới được thêm vào hệ thống',
      },
    ];

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
                 boxShadow: [
           BoxShadow(
             color: AppColors.shadowLight,
             blurRadius: 8,
             offset: const Offset(0, 2),
           ),
         ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.history,
                color: AppColors.neutral600,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Lịch sử trạng thái',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.neutral600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          ...statusHistory.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isLast = index == statusHistory.length - 1;
            
            return Container(
              margin: EdgeInsets.only(bottom: isLast ? 0 : AppDimensions.spacingM),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Timeline indicator
                  Column(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: _getStatusColor(item['status'] as String).withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: _getStatusColor(item['status'] as String),
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          _getStatusIcon(item['status'] as String),
                          color: _getStatusColor(item['status'] as String),
                          size: 12,
                        ),
                      ),
                      if (!isLast)
                        Container(
                          width: 2,
                          height: 40,
                          color: AppColors.borderLight,
                        ),
                    ],
                  ),
                  
                  SizedBox(width: AppDimensions.spacingM),
                  
                  // Content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['status'] as String,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: _getStatusColor(item['status'] as String),
                          ),
                        ),
                        Text(
                          item['note'] as String,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        SizedBox(height: AppDimensions.spacingXS),
                        Row(
                          children: [
                            Text(
                              item['date'] as String,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textTertiary,
                              ),
                            ),
                            SizedBox(width: AppDimensions.spacingS),
                            Text(
                              '• ${item['staff']}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textTertiary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  void _addTag(String tag) {
    setState(() {
      if (!_currentTags.contains(tag)) {
        _currentTags.add(tag);
      }
    });
    // TODO: Save to backend
  }

  void _removeTag(String tag) {
    setState(() {
      _currentTags.remove(tag);
    });
    // TODO: Save to backend
  }

  void _changeStatus(String status) {
    setState(() {
      _selectedStatus = status;
    });
    // TODO: Save to backend and add to history
  }

  Color _getTagColor(String tag) {
    switch (tag) {
      case 'VIP':
        return const Color(0xFFFFD700); // Gold
      case 'Premium':
        return AppColors.kienlongSkyBlue;
      case 'Mới':
        return AppColors.kienlongOrange;
      case 'Tiềm năng cao':
        return AppColors.success;
      case 'Quan tâm vay':
      case 'Quan tâm gửi':
      case 'Quan tâm thẻ':
        return AppColors.info;
      default:
        return AppColors.neutral600;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Đã chốt':
        return AppColors.success;
      case 'Đang chăm sóc':
        return AppColors.kienlongOrange;
      case 'Tiềm năng':
        return AppColors.warning;
      case 'Đã giao dịch':
        return AppColors.info;
      case 'Tạm dừng':
        return AppColors.error;
      default:
        return AppColors.neutral500;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Đã chốt':
        return TablerIcons.circle_check;
      case 'Đang chăm sóc':
        return TablerIcons.heart;
      case 'Tiềm năng':
        return TablerIcons.eye;
      case 'Đã giao dịch':
        return TablerIcons.cash;
      case 'Tạm dừng':
        return TablerIcons.player_pause;
      default:
        return TablerIcons.circle;
    }
  }
} 