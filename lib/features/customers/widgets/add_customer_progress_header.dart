import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

class AddCustomerProgressHeader extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final String stepTitle;

  const AddCustomerProgressHeader({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress bar
          Row(
            children: [
              Text(
                'Bước ${currentStep + 1}/$totalSteps',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '${((currentStep + 1) / totalSteps * 100).round()}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingS),
          
          // Progress indicator
          Container(
            height: 6,
            decoration: BoxDecoration(
              color: AppColors.neutral200,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                final progress = (currentStep + 1) / totalSteps;
                return Stack(
                  children: [
                    // Background
                    Container(
                      width: constraints.maxWidth,
                      decoration: BoxDecoration(
                        color: AppColors.neutral200,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                    ),
                    // Progress fill
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: constraints.maxWidth * progress,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.kienlongOrange,
                            AppColors.kienlongOrange.withValues(alpha: 0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Step title
          Text(
            stepTitle,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          
          SizedBox(height: AppDimensions.spacingXS),
          
          // Step dots indicator
          Row(
            children: List.generate(totalSteps, (index) {
              final isCompleted = index < currentStep;
              final isCurrent = index == currentStep;
              
              return Container(
                margin: EdgeInsets.only(
                  right: index < totalSteps - 1 ? AppDimensions.spacingS : 0,
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: isCurrent ? 24 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: isCompleted || isCurrent
                        ? AppColors.kienlongOrange
                        : AppColors.neutral300,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
} 