import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerDetailActions extends StatelessWidget {
  final VoidCallback onCall;
  final VoidCallback onMessage;
  final VoidCallback onAddNote;
  final VoidCallback onEdit;

  const CustomerDetailActions({
    super.key,
    required this.onCall,
    required this.onMessage,
    required this.onAddNote,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final actions = [
      {
        'icon': TablerIcons.phone,
        'label': 'Gọi điện',
        'color': AppColors.kienlongOrange,
        'onTap': onCall,
      },
      {
        'icon': TablerIcons.message_circle,
        'label': 'Nhắn tin',
        'color': AppColors.kienlongSkyBlue,
        'onTap': onMessage,
      },
      {
        'icon': TablerIcons.note,
        'label': '<PERSON><PERSON> chú',
        'color': AppColors.neutral600,
        'onTap': onAddNote,
      },
      {
        'icon': TablerIcons.edit,
        'label': 'Chỉnh sửa',
        'color': AppColors.neutral600,
        'onTap': onEdit,
      },
    ];

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: actions.map((action) {
          return Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingXS),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Action Button
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: action['onTap'] as VoidCallback,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                      child: Container(
                        width: AppDimensions.quickActionButtonSize,
                        height: AppDimensions.quickActionButtonSize,
                        decoration: BoxDecoration(
                          color: (action['color'] as Color).withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: (action['color'] as Color).withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          action['icon'] as IconData,
                          color: action['color'] as Color,
                          size: AppDimensions.iconM,
                        ),
                      ),
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Action Label
                  Text(
                    action['label'] as String,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
} 