import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class CustomerSummarySection extends StatefulWidget {
  final int? totalCount;
  final int? activeCount;
  final int? prospectCount;
  final int? completedCount;
  final String? totalRevenue;
  final bool isLoading;

  const CustomerSummarySection({
    super.key,
    this.totalCount,
    this.activeCount,
    this.prospectCount,
    this.completedCount,
    this.totalRevenue,
    this.isLoading = false,
  });

  @override
  State<CustomerSummarySection> createState() => _CustomerSummarySectionState();
}

class _CustomerSummarySectionState extends State<CustomerSummarySection>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );
    // Luôn start animation khi widget được tạo với isLoading = true
    _updateAnimationState();
  }

  void _updateAnimationState() {
    if (widget.isLoading) {
      if (!_shimmerController.isAnimating) {
        _shimmerController.repeat();
      }
    } else {
      if (_shimmerController.isAnimating) {
        _shimmerController.stop();
      }
    }
  }

  @override
  void didUpdateWidget(CustomerSummarySection oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      _updateAnimationState();
    }
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Luôn giữ khung cố định bên ngoài với chiều cao cố định
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingM),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      // Đảm bảo chiều cao cố định để tránh layout shift
      constraints: const BoxConstraints(minHeight: 250),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.kienlongSkyBlue,
            AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
            AppColors.kienlongOrange.withValues(alpha: 0.3),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
            spreadRadius: 2,
          ),
        ],
      ),
      child: widget.isLoading 
          ? _buildLoadingContent()
          : _buildActualContent(context),
    );
  }

  Widget _buildLoadingContent() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Stack(
          children: [
            // Shimmer effect overlay
            Positioned.fill(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment(_shimmerAnimation.value - 1, 0),
                      end: Alignment(_shimmerAnimation.value, 0),
                      colors: [
                        Colors.transparent,
                        Colors.white.withValues(alpha: 0.15),
                        Colors.transparent,
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                ),
              ),
            ),
            
            // Skeleton content - đảm bảo cùng layout structure với content thực tế
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header skeleton - tương đương với header thực tế
                Row(
                  children: [
                    Container(
                      width: AppDimensions.iconM,
                      height: AppDimensions.iconM,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: AppDimensions.spacingS),
                    Container(
                      width: 150,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacingM),
                
                // Revenue label skeleton - tương đương với "Tổng doanh số tháng"
                Container(
                  width: 120,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                // Revenue value skeleton - tương đương với giá trị doanh số
                Container(
                  width: 180,
                  height: 28,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: AppDimensions.spacingL),
                
                // Stats grid skeleton - tương đương với 4 stat cards
                Row(
                  children: List.generate(4, (index) => [
                    Expanded(child: _buildStatCardSkeleton()),
                    if (index < 3) const SizedBox(width: AppDimensions.spacingS),
                  ]).expand((x) => x).toList(),
                ),
                
                // Thêm spacing cuối để đảm bảo chiều cao tương đương
                const SizedBox(height: AppDimensions.spacingS),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCardSkeleton() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Icon skeleton - tương đương với icon thực tế
          Container(
            width: AppDimensions.iconM,
            height: AppDimensions.iconM,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: AppDimensions.spacingXS),
          // Count skeleton - tương đương với số liệu thống kê
          Container(
            width: 40,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 2),
          // Label skeleton - tương đương với label thống kê
          Container(
            width: 60,
            height: 14,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActualContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              TablerIcons.users,
              color: Colors.white,
              size: AppDimensions.iconM,
            ),
            const SizedBox(width: AppDimensions.spacingS),
            Text(
              'Tổng quan khách hàng',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        
        // Total revenue
        Text(
          'Tổng doanh số tháng',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Colors.white.withValues(alpha: 0.9),
          ),
        ),
        Text(
          _formatDisplayRevenue(widget.totalRevenue),
          style: AppTypography.textTheme.headlineMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingL),
        
        // Stats grid
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                'Tổng số',
                _formatDisplayCount(widget.totalCount),
                TablerIcons.users,
                Colors.white,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: _buildStatCard(
                context,
                'Chăm sóc',
                _formatDisplayCount(widget.activeCount),
                TablerIcons.heart_handshake,
                AppColors.warning,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: _buildStatCard(
                context,
                'Tiềm năng',
                _formatDisplayCount(widget.prospectCount),
                TablerIcons.target,
                AppColors.info,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: _buildStatCard(
                context,
                'Đã GD',
                _formatDisplayCount(widget.completedCount),
                TablerIcons.circle_check,
                AppColors.success,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String count,
    IconData icon,
    Color iconColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: iconColor,
            size: AppDimensions.iconM,
          ),
          const SizedBox(height: AppDimensions.spacingXS),
          Text(
            count,
            style: AppTypography.textTheme.titleSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Format revenue display với fallback cho null values  
  String _formatDisplayRevenue(String? revenue) {
    if (revenue == null || revenue.isEmpty) {
      return '0 VND';
    }
    return revenue;
  }

  /// Format count display với fallback cho null values
  String _formatDisplayCount(int? count) {
    if (count == null) {
      return '0';
    }
    
    // Format với thousand separator nếu số lớn
    if (count >= 1000) {
      if (count >= 1000000) {
        double millions = count / 1000000.0;
        return '${millions.toStringAsFixed(1)}M';
      } else {
        double thousands = count / 1000.0;
        return '${thousands.toStringAsFixed(1)}K';
      }
    }
    
    return count.toString();
  }
} 