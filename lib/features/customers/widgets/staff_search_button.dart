import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../employees/models/employee_model.dart';

/// Consolidated Staff Search components - <PERSON>ton, Modal, and Overlay in one file
/// This eliminates the need for multiple files and simplifies maintenance

// =============================================================================
// MAIN BUTTON COMPONENT
// =============================================================================

/// Simple button để trigger Staff Search Modal
class StaffSearchButton extends StatefulWidget {
  final List<EmployeeModel> availableStaff;
  final EmployeeModel? selectedStaff;
  final Function(EmployeeModel?) onStaffSelected;
  final Function(String)? onSearchChanged;
  final bool isSearching;
  final Stream<List<EmployeeModel>>? employeeSearchStream;

  const StaffSearchButton({
    super.key,
    required this.availableStaff,
    required this.selectedStaff,
    required this.onStaffSelected,
    this.onSearchChanged,
    this.isSearching = false,
    this.employeeSearchStream,
  });

  @override
  State<StaffSearchButton> createState() => _StaffSearchButtonState();
}

class _StaffSearchButtonState extends State<StaffSearchButton> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final hasSelection = widget.selectedStaff != null;
    final selectedStaffInfo = widget.selectedStaff;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => _openStaffSearchModal(context),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: hasSelection 
                  ? AppColors.kienlongOrange.withValues(alpha: 0.5)
                  : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                width: hasSelection ? 1.5 : 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.search,
                  color: hasSelection 
                    ? AppColors.kienlongOrange 
                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: hasSelection && selectedStaffInfo?.fullName != null
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              selectedStaffInfo!.fullName!,
                              style: AppTypography.textTheme.bodyMedium?.copyWith(
                                color: AppColors.kienlongOrange,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            if (selectedStaffInfo.username != null && selectedStaffInfo.username!.isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                selectedStaffInfo.username!,
                                style: AppTypography.textTheme.bodySmall?.copyWith(
                                  color: AppColors.kienlongOrange.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ],
                        )
                      : Text(
                          'Chọn cán bộ phụ trách',
                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                ),
                if (hasSelection)
                  GestureDetector(
                    onTap: () => widget.onStaffSelected(null),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        TablerIcons.x,
                        color: AppColors.kienlongOrange.withValues(alpha: 0.7),
                        size: AppDimensions.iconS,
                      ),
                    ),
                  )
                else
                  Icon(
                    TablerIcons.chevron_down,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    size: AppDimensions.iconS,
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _openStaffSearchModal(BuildContext context) {
    showStaffSearchModal(
      context: context,
      availableStaff: [],
      onStaffSelected: widget.onStaffSelected,
      onSearchChanged: widget.onSearchChanged,
      isSearching: widget.isSearching,
      employeeSearchStream: widget.employeeSearchStream,
    );
  }
}

// =============================================================================
// STAFF SEARCH MODAL COMPONENT
// =============================================================================

/// Modal riêng cho Staff Search với responsive design
class StaffSearchModal extends StatefulWidget {
  final List<EmployeeModel> availableStaff;
  final EmployeeModel? selectedStaff;
  final Function(EmployeeModel?) onStaffSelected;
  final Function(String)? onSearchChanged;
  final bool isSearching;
  final Stream<List<EmployeeModel>>? employeeSearchStream;

  const StaffSearchModal({
    super.key,
    required this.availableStaff,
    required this.selectedStaff,
    required this.onStaffSelected,
    this.onSearchChanged,
    this.isSearching = false,
    this.employeeSearchStream,
  });

  @override
  State<StaffSearchModal> createState() => _StaffSearchModalState();
}

class _StaffSearchModalState extends State<StaffSearchModal> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  Timer? _debounceTimer;
  List<EmployeeModel> _searchResults = [];
  List<EmployeeModel> _availableStaff = [];
  
  static const int _maxResults = 50;

  @override
  void initState() {
    super.initState();
    // Clear everything - no pre-filled data
    _searchController.clear();
    _availableStaff.clear();
    _searchResults.clear();
    
    // Listen to search stream để populate data khi user search
    if (widget.employeeSearchStream != null) {
      widget.employeeSearchStream!.listen((employees) {
        if (mounted) {
          setState(() {
            _availableStaff = employees;
            _performSearch(_searchController.text);
          });
        }
      });
    }

    // Auto focus để user bắt đầu search
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        _performSearch(query);
        widget.onSearchChanged?.call(query);
      }
    });
  }

  void _performSearch(String query) {
    // Nếu query rỗng, clear results để hiển thị empty state
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
      });
      return;
    }

    // Chỉ search khi có data từ stream
    final results = _availableStaff
        .where((staff) => 
          staff.fullName?.toLowerCase().contains(query.toLowerCase()) == true ||
          staff.username?.toLowerCase().contains(query.toLowerCase()) == true ||
          staff.email?.toLowerCase().contains(query.toLowerCase()) == true
        )
        .take(_maxResults)
        .toList();

    setState(() {
      _searchResults = results;
    });
  }

  void _selectStaff(EmployeeModel staff) {
    widget.onStaffSelected(staff);
    Navigator.pop(context, staff);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppDimensions.paddingS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.users,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Chọn cán bộ phụ trách',
                      style: AppTypography.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      TablerIcons.x,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Search bar - Fixed at top
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
            child: TextField(
              controller: _searchController,
              focusNode: _focusNode,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Nhập tên, username hoặc email cán bộ...',
                prefixIcon: widget.isSearching
                    ? SizedBox(
                        width: AppDimensions.iconM,
                        height: AppDimensions.iconM,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppColors.kienlongOrange,
                        ),
                      )
                    : Icon(
                        TablerIcons.search, 
                        color: AppColors.kienlongOrange,
                      ),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(TablerIcons.x, color: AppColors.kienlongOrange.withValues(alpha: 0.7)),
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(
                    color: AppColors.kienlongOrange,
                    width: 2,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: AppDimensions.spacingM),

          // Results count
          if (_searchResults.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.info_circle,
                    size: AppDimensions.iconS,
                    color: AppColors.kienlongOrange,
                  ),
                  const SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    '${_searchResults.length} kết quả${_searchResults.length >= _maxResults ? ' (hiển thị $_maxResults đầu tiên)' : ''}',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: AppDimensions.spacingS),

          // Scrollable Results list với keyboard handling
          Expanded(
            child: _searchResults.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
                    itemCount: _searchResults.length,
                    itemBuilder: (context, index) {
                      final staff = _searchResults[index];
                      
                      return Container(
                        margin: const EdgeInsets.only(bottom: AppDimensions.spacingS),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                            onTap: () => _selectStaff(staff),
                            child: Container(
                              padding: const EdgeInsets.all(AppDimensions.paddingM),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  // Avatar
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      TablerIcons.user,
                                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: AppDimensions.spacingM),
                                  
                                  // Staff info
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          staff.fullName ?? 'Unknown',
                                          style: AppTypography.textTheme.bodyLarge?.copyWith(
                                            color: Theme.of(context).colorScheme.onSurface,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                        const SizedBox(height: AppDimensions.spacingXS),
                                        Text(
                                          staff.username ?? 'N/A',
                                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                          ),
                                        ),
                                        if (staff.email != null && staff.email!.isNotEmpty) ...[
                                          const SizedBox(height: AppDimensions.spacingXS),
                                          Text(
                                            staff.email!,
                                            style: AppTypography.textTheme.bodySmall?.copyWith(
                                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                  
                                  // Arrow icon indicating selection action
                                  Icon(
                                    TablerIcons.chevron_right,
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
                                    size: AppDimensions.iconS,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
          ),

          // Bottom padding để tránh keyboard
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom + AppDimensions.paddingL),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _searchController.text.isEmpty ? TablerIcons.search : TablerIcons.search_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            _searchController.text.isEmpty 
              ? 'Nhập tên để tìm kiếm cán bộ'
              : 'Không tìm thấy cán bộ nào',
            style: AppTypography.textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          if (_searchController.text.isEmpty) ...[
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Gõ để bắt đầu tìm kiếm',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                fontStyle: FontStyle.italic,
              ),
            ),
          ] else ...[
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Thử tìm với từ khóa khác',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

// =============================================================================
// STAFF SEARCH OVERLAY COMPONENT  
// =============================================================================

/// Overlay solution cho Staff Search với smart positioning
class StaffSearchOverlay extends StatefulWidget {
  final List<EmployeeModel> availableStaff;
  final EmployeeModel? selectedStaff;
  final Function(EmployeeModel?) onStaffSelected;
  final Function(String)? onSearchChanged;
  final bool isSearching;
  final Stream<List<EmployeeModel>>? employeeSearchStream;
  final String? placeholder;

  const StaffSearchOverlay({
    super.key,
    required this.availableStaff,
    required this.selectedStaff,
    required this.onStaffSelected,
    this.onSearchChanged,
    this.isSearching = false,
    this.employeeSearchStream,
    this.placeholder,
  });

  @override
  State<StaffSearchOverlay> createState() => _StaffSearchOverlayState();
}

class _StaffSearchOverlayState extends State<StaffSearchOverlay> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final GlobalKey _textFieldKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  List<EmployeeModel> _searchResults = [];
  List<EmployeeModel> _availableStaff = [];
  Timer? _debounceTimer;
  
  static const int _maxOverlayResults = 6;

  @override
  void initState() {
    super.initState();
    _availableStaff = List.from(widget.availableStaff);
    
    if (widget.selectedStaff != null) {
      _searchController.text = widget.selectedStaff!.fullName ?? '';
    }
    
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _showOverlay();
      } else {
        _hideOverlay();
      }
    });

    if (widget.employeeSearchStream != null) {
      widget.employeeSearchStream!.listen((employees) {
        if (mounted) {
          setState(() {
            _availableStaff = employees;
            _performSearch(_searchController.text);
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _hideOverlay();
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        _performSearch(query);
        widget.onSearchChanged?.call(query);
        _updateOverlay();
      }
    });
  }

  void _performSearch(String query) {
    if (query.isEmpty) {
      setState(() {
        _searchResults = _availableStaff.take(_maxOverlayResults).toList();
      });
      return;
    }

    final results = _availableStaff
        .where((staff) => 
          staff.fullName?.toLowerCase().contains(query.toLowerCase()) == true ||
          staff.username?.toLowerCase().contains(query.toLowerCase()) == true ||
          staff.email?.toLowerCase().contains(query.toLowerCase()) == true
        )
        .take(_maxOverlayResults)
        .toList();

    setState(() {
      _searchResults = results;
    });
  }

  void _showOverlay() {
    _performSearch(_searchController.text);
    
    if (_overlayEntry != null) return;
    
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  OverlayEntry _createOverlayEntry() {
    return OverlayEntry(
      builder: (context) => Positioned(
        width: _getTextFieldWidth(),
        child: CompositedTransformFollower(
          link: _getLayerLink(),
          showWhenUnlinked: false,
          offset: Offset(0.0, _getTextFieldHeight() + 4),
          child: Material(
            elevation: 4.0,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            child: Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: _searchResults.isEmpty 
                ? _buildOverlayEmptyState()
                : ListView.builder(
                    padding: EdgeInsets.zero,
                    shrinkWrap: true,
                    itemCount: _searchResults.length,
                    itemBuilder: (context, index) {
                      final staff = _searchResults[index];
                      final isSelected = widget.selectedStaff?.id == staff.id;
                      
                      return InkWell(
                        onTap: () => _selectStaff(staff),
                        child: Container(
                          padding: const EdgeInsets.all(AppDimensions.paddingS),
                          decoration: BoxDecoration(
                            color: isSelected 
                              ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                              : Colors.transparent,
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: 32,
                                height: 32,
                                decoration: BoxDecoration(
                                  color: isSelected 
                                    ? AppColors.kienlongOrange.withValues(alpha: 0.2)
                                    : Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  TablerIcons.user,
                                  color: isSelected 
                                    ? AppColors.kienlongOrange 
                                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                  size: 16,
                                ),
                              ),
                              const SizedBox(width: AppDimensions.spacingS),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      staff.fullName ?? 'Unknown',
                                      style: AppTypography.textTheme.bodySmall?.copyWith(
                                        color: isSelected
                                          ? AppColors.kienlongOrange
                                          : Theme.of(context).colorScheme.onSurface,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    if (staff.username != null) ...[
                                      Text(
                                        staff.username!,
                                        style: AppTypography.textTheme.bodySmall?.copyWith(
                                          color: isSelected
                                            ? AppColors.kienlongOrange.withValues(alpha: 0.7)
                                            : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                          fontSize: 11,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                              if (isSelected)
                                Icon(
                                  TablerIcons.check,
                                  color: AppColors.kienlongOrange,
                                  size: AppDimensions.iconXS,
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverlayEmptyState() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Center(
        child: Text(
          _searchController.text.isEmpty 
            ? 'Nhập tên để tìm kiếm'
            : 'Không tìm thấy cán bộ',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            fontStyle: FontStyle.italic,
          ),
        ),
      ),
    );
  }

  void _selectStaff(EmployeeModel staff) {
    _searchController.text = staff.fullName ?? '';
    widget.onStaffSelected(staff);
    _focusNode.unfocus();
  }

  double _getTextFieldWidth() {
    final RenderBox? renderBox = 
        _textFieldKey.currentContext?.findRenderObject() as RenderBox?;
    return renderBox?.size.width ?? 200;
  }

  double _getTextFieldHeight() {
    final RenderBox? renderBox = 
        _textFieldKey.currentContext?.findRenderObject() as RenderBox?;
    return renderBox?.size.height ?? 48;
  }

  LayerLink _getLayerLink() {
    // This would need to be properly implemented with CompositedTransformTarget
    return LayerLink();
  }

  @override
  Widget build(BuildContext context) {
    final hasSelection = widget.selectedStaff != null;
    
    return CompositedTransformTarget(
      link: _getLayerLink(),
      child: TextField(
        key: _textFieldKey,
        controller: _searchController,
        focusNode: _focusNode,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: widget.placeholder ?? 'Chọn cán bộ phụ trách',
          prefixIcon: widget.isSearching
              ? SizedBox(
                  width: AppDimensions.iconM,
                  height: AppDimensions.iconM,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: AppColors.kienlongOrange,
                  ),
                )
              : Icon(
                  hasSelection ? TablerIcons.user_check : TablerIcons.search,
                  color: hasSelection 
                    ? AppColors.kienlongOrange 
                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
          suffixIcon: hasSelection 
            ? IconButton(
                icon: Icon(TablerIcons.x, color: AppColors.kienlongOrange.withValues(alpha: 0.7)),
                onPressed: () {
                  _searchController.clear();
                  widget.onStaffSelected(null);
                  _performSearch('');
                },
              )
            : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            borderSide: BorderSide(
              color: hasSelection 
                ? AppColors.kienlongOrange.withValues(alpha: 0.5)
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            borderSide: BorderSide(
              color: AppColors.kienlongOrange,
              width: 2,
            ),
          ),
        ),
      ),
    );
  }
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

/// Helper function để show Staff Search Modal
/// Always starts clean - no pre-selected staff or pre-loaded data
Future<EmployeeModel?> showStaffSearchModal({
  required BuildContext context,
  required List<EmployeeModel> availableStaff, // Kept for interface compatibility but ignored
  Function(EmployeeModel?)? onStaffSelected,
  Function(String)? onSearchChanged,
  bool isSearching = false,
  Stream<List<EmployeeModel>>? employeeSearchStream,
}) {
  return showModalBottomSheet<EmployeeModel>(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (_, controller) {
          return StaffSearchModal(
            availableStaff: [], // Always start empty
            selectedStaff: null, // No pre-selection
            onStaffSelected: onStaffSelected ?? (_) {},
            onSearchChanged: onSearchChanged,
            isSearching: isSearching,
            employeeSearchStream: employeeSearchStream,
          );
        },
      );
    },
  );
}