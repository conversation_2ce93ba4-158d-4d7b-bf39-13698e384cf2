import 'package:flutter/material.dart';

class CustomerFilterBar extends StatelessWidget {
  final void Function(String)? onFilter;
  final int selectedFilter;
  final List<String> filters;
  
  const CustomerFilterBar({
    this.onFilter,
    this.selectedFilter = 0,
    this.filters = const [
      '<PERSON><PERSON><PERSON> cả',
      '<PERSON><PERSON> chăm sóc',
      '<PERSON><PERSON><PERSON><PERSON> năng',
      '<PERSON><PERSON> giao dịch',
      '<PERSON><PERSON> chốt',
    ],
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final isSelected = index == selectedFilter;
          return ChoiceChip(
            label: Text(filters[index]),
            selected: isSelected,
            onSelected: (_) => onFilter?.call(filters[index]),
            selectedColor: Theme.of(context).colorScheme.primary,
            labelStyle: TextStyle(
              color: isSelected
                  ? Colors.white
                  : Theme.of(context).colorScheme.onSurface,
            ),
          );
        },
      ),
    );
  }
}

 