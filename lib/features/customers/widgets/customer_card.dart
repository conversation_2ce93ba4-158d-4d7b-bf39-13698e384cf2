import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/theme/index.dart';
import '../models/customer_model.dart';
import '../../../shared/utils/color_utils.dart';

class CustomerCard extends StatelessWidget {
  final CustomerModel customer;
  final VoidCallback? onTap;

  const CustomerCard({
    super.key,
    required this.customer,
    this.onTap,
  });

  Color _getStatusColorByLabel(String status) {
    switch (status) {
      case 'Đang chăm sóc':
        return AppColors.warning;
      case 'Tiềm năng':
        return AppColors.info;
      case 'Đã giao dịch':
        return AppColors.success;
      case 'Đã chốt':
        return AppColors.kienlongOrange;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Đang chăm sóc':
        return TablerIcons.heart_handshake;
      case 'Tiềm năng':
        return TablerIcons.target;
      case 'Đã giao dịch':
        return TablerIcons.circle_check;
      case 'Đã chốt':
        return TablerIcons.trophy;
      default:
        return TablerIcons.user;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Chưa có ngày';
    return '${date.day}/${date.month}/${date.year}';
  }



  @override
  Widget build(BuildContext context) {
    final statusLabel = customer.status?.label ?? '';
    final statusHex = customer.status?.value;
    final statusColor = (statusHex != null && statusHex.isNotEmpty)
        ? ColorUtils.hexToColor(statusHex)
        : _getStatusColorByLabel(statusLabel);
    final tags = customer.tags;

    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            children: [
              // Header row - Avatar, Customer Info, Status & Revenue
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Avatar
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusM,
                      ),
                    ),
                    child: Icon(
                      TablerIcons.user,
                      color: statusColor,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingM),

                  // Customer info - Use Flexible to prevent overflow
                  Flexible(
                    flex: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Customer name - Allow multiple lines if needed
                        Text(
                          customer.fullName,
                          style: AppTypography.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppDimensions.spacingS),

                        // Tags row - Wrap to prevent overflow
                        if (tags.isNotEmpty) ...[
                          Wrap(
                            spacing: AppDimensions.spacingXS,
                            runSpacing: 2,
                            children: tags
                                .take(2)
                                .map(
                                  (tag) => Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: AppDimensions.paddingXS,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: tag.name == 'VIP'
                                          ? AppColors.kienlongOrange.withValues(
                                              alpha: 0.15,
                                            )
                                          : AppColors.kienlongSkyBlue
                                                .withValues(alpha: 0.15),
                                      borderRadius: BorderRadius.circular(
                                        AppDimensions.radiusXS,
                                      ),
                                    ),
                                    child: Text(
                                      tag.name ?? '',
                                      style: AppTypography.textTheme.bodySmall
                                          ?.copyWith(
                                            color: tag.name == 'VIP'
                                                ? AppColors.kienlongOrange
                                                : AppColors.kienlongSkyBlue,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 10,
                                          ),
                                    ),
                                  ),
                                )
                                .toList(),
                          ),
                          const SizedBox(height: AppDimensions.spacingXS),
                        ],

                        Row(
                          children: [
                            // Phone number
                            Expanded(
                              child: Text(
                                  customer.phoneNumber??'',
                                style: AppTypography.textTheme.bodyMedium
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withValues(alpha: 0.7),
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppDimensions.paddingS,
                                vertical: AppDimensions.paddingXS,
                              ),
                              decoration: BoxDecoration(
                                color: statusColor.withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(
                                  AppDimensions.radiusS,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _getStatusIcon(statusLabel),
                                    size: AppDimensions.iconXS,
                                    color: statusColor,
                                  ),
                                  const SizedBox(
                                    width: AppDimensions.spacingXS,
                                  ),
                                  Text(
                                    statusLabel,
                                    style: AppTypography.textTheme.bodySmall
                                        ?.copyWith(
                                          color: statusColor,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        // Assigned Manager
                        if (customer.assignedManager?.fullName?.isNotEmpty == true) ...[
                          const SizedBox(height: AppDimensions.spacingS),
                          Row(
                            children: [
                              Icon(
                                TablerIcons.user_check,
                                size: AppDimensions.iconXS,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                              const SizedBox(width: AppDimensions.spacingXS),
                              Expanded(
                                child: Text(
                                  'CBPT: ${customer.assignedManager?.fullName ?? ''}',
                                  style: AppTypography.textTheme.bodySmall
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],

                        // Branch
                        if (customer.branch?.name != null && customer.branch!.name.isNotEmpty) ...[
                          const SizedBox(height: AppDimensions.spacingS),
                          Row(
                            children: [
                              Icon(
                                TablerIcons.building,
                                size: AppDimensions.iconXS,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                              const SizedBox(width: AppDimensions.spacingXS),
                              Expanded(
                                child: Text(
                                  customer.branch?.name ?? '',
                                  style: AppTypography.textTheme.bodySmall
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),

                  const SizedBox(width: AppDimensions.spacingS),

                  // Status & Revenue column
                  // Column(
                  //   crossAxisAlignment: CrossAxisAlignment.end,
                  //   children: [
                  //     if (revenue != '0') ...[
                  //       Text(
                  //         revenue,
                  //         style: AppTypography.textTheme.titleMedium?.copyWith(
                  //           fontWeight: FontWeight.bold,
                  //           color: AppColors.success,
                  //         ),
                  //         textAlign: TextAlign.right,
                  //       ),
                  //       const SizedBox(height: AppDimensions.spacingXS),
                  //     ],

                  //   ],
                  // ),
                ],
              ),

              const SizedBox(height: AppDimensions.spacingS),

              // Footer with last update and actions
              Column(
                children: [
                  // Last update row
                  Row(
                    children: [
                      Icon(
                        TablerIcons.clock,
                        size: AppDimensions.iconS,
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      const SizedBox(width: AppDimensions.spacingXS),
                      Expanded(
                        child: Text(
                          'Ngày tạo: ${_formatDate(customer.createdAt)}',
                          style: AppTypography.textTheme.bodySmall?.copyWith(
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.6),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spacingM),

                  // Action buttons row
                  Row(
                    children: [
                        _buildActionButton(
                          context,
                          icon: TablerIcons.phone,
                          label: 'Gọi',
                          onTap: ()=> makeCall(customer.phoneNumber, context),
                          color: AppColors.success,
                        ),
                        const SizedBox(width: AppDimensions.spacingS),
                      _buildActionButton(
                        context,
                        icon: TablerIcons.message,
                        label: 'Tin nhắn',
                        onTap: ()=> sendMessage(customer.phoneNumber, context),
                        color: AppColors.info,
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingS,
          vertical: AppDimensions.paddingXS,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: AppDimensions.iconS, color: color),
            const SizedBox(width: AppDimensions.spacingXS),
            Flexible(
              child: Text(
                label,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  // TODO: tối ưu sau
  void makeCall(String? phoneNumber, BuildContext context ) async {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không có số điện thoại để gọi'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final uri = Uri(scheme: 'tel', path: phoneNumber);

    try {
      // Thử launch với tel:// scheme và external application mode
      try {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        return; // Thành công, thoát hàm
      } catch (e) {
        debugPrint('Launch with tel:// failed: $e');
      }

      // Nếu tel:// thất bại, thử với tel: scheme
      try {
        final fallbackUri = Uri.parse('tel:$phoneNumber');
        await launchUrl(
          fallbackUri,
          mode: LaunchMode.externalApplication,
        );
        return; // Thành công, thoát hàm
      } catch (fallbackError) {
        // Ignore fallback error
      }

      // Nếu cả hai đều thất bại, thử với canLaunchUrl để debug
      try {
        final canLaunch = await canLaunchUrl(uri);

        if (canLaunch) {
          await launchUrl(uri);
          return; // Thành công, thoát hàm
        }
      } catch (checkError) {
        // Ignore check error
      }

      // Nếu tất cả đều thất bại, throw exception
      throw Exception(
        'Không thể mở ứng dụng gọi điện trên thiết bị này. Hãy thử gọi trực tiếp: $phoneNumber',
      );
    } catch (e) {

      String errorMessage = 'Lỗi khi gọi điện';
      if (e.toString().contains('canLaunchUrl returned false')) {
        errorMessage =
        'Thiết bị này không hỗ trợ gọi điện. Hãy gọi trực tiếp: $phoneNumber';
      } else if (e.toString().contains('Không thể mở ứng dụng gọi điện')) {
        errorMessage =
        'Không thể mở ứng dụng gọi điện. Hãy gọi trực tiếp: $phoneNumber';
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Copy số',
              textColor: Colors.white,
              onPressed: () {
                debugPrint('Copy phone number: $phoneNumber');
              },
            ),
          ),
        );
      }
    }
  }

  // TODO: tối ưu sau
  void sendMessage(String? phoneNumber, BuildContext context ) async {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Không có số điện thoại để nhắn tin'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Debug log
    debugPrint('Attempting to send SMS to: $phoneNumber');

    try {
      // Thử launch với sms:// scheme và external application mode
      try {
        final uri = Uri(scheme: 'sms', path: phoneNumber);
        debugPrint('SMS URI created: $uri');
        final result = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        debugPrint('Launch with sms:// successful: $result');
        return; // Thành công, thoát hàm
      } catch (e) {
        debugPrint('Launch with sms:// failed: $e');
      }

      // Nếu sms:// thất bại, thử với sms: scheme
      try {
        final fallbackUri = Uri.parse('sms:$phoneNumber');
        debugPrint('Trying SMS fallback URI: $fallbackUri');
        final result = await launchUrl(
          fallbackUri,
          mode: LaunchMode.externalApplication,
        );
        debugPrint('Fallback launch with sms: successful: $result');
        return; // Thành công, thoát hàm
      } catch (fallbackError) {
        debugPrint('Fallback launch with sms: failed: $fallbackError');
      }

      // Nếu cả hai đều thất bại, throw exception
      throw Exception(
        'Không thể mở ứng dụng tin nhắn trên thiết bị này. Hãy thử nhắn tin trực tiếp: $phoneNumber',
      );
    } catch (e) {
      debugPrint('Error in _sendMessage: $e');

      String errorMessage = 'Lỗi khi nhắn tin';
      if (e.toString().contains('Không thể mở ứng dụng tin nhắn')) {
        errorMessage =
        'Không thể mở ứng dụng tin nhắn. Hãy nhắn tin trực tiếp: $phoneNumber';
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Copy số',
              textColor: Colors.white,
              onPressed: () {
                // TODO: Copy phone number to clipboard
                debugPrint('Copy phone number: $phoneNumber');
              },
            ),
          ),
        );
      }
    }
  }

}
