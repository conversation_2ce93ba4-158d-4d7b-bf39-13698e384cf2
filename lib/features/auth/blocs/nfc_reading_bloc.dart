import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:dmrtd/dmrtd.dart';
import 'dart:typed_data';
import '../../../shared/models/cccd_extraction_model.dart';
import '../services/mrz_scanner_service.dart';
import '../../../core/nfc/index.dart';
import '../../../shared/utils/app_logger.dart';

// Events
abstract class NfcReadingEvent extends Equatable {
  const NfcReadingEvent();

  @override
  List<Object?> get props => [];
}

class StartNfcReadingEvent extends NfcReadingEvent {
  final CccdExtractionModel? cccdExtractionResult;
  final MrzScanResult? mrzScanResult;

  const StartNfcReadingEvent({
    this.cccdExtractionResult,
    this.mrzScanResult,
  });

  @override
  List<Object?> get props => [cccdExtractionResult, mrzScanResult];
}

class RetryNfcReadingEvent extends NfcReadingEvent {
  final CccdExtractionModel? cccdExtractionResult;
  final MrzScanResult? mrzScanResult;

  const RetryNfcReadingEvent({
    this.cccdExtractionResult,
    this.mrzScanResult,
  });

  @override
  List<Object?> get props => [cccdExtractionResult, mrzScanResult];
}

class ResetNfcReadingEvent extends NfcReadingEvent {
  const ResetNfcReadingEvent();
}

// States
abstract class NfcReadingState extends Equatable {
  const NfcReadingState();

  @override
  List<Object?> get props => [];
}

class NfcReadingInitial extends NfcReadingState {
  const NfcReadingInitial();
}

class NfcReadingLoading extends NfcReadingState {
  final String status;

  const NfcReadingLoading(this.status);

  @override
  List<Object?> get props => [status];
}

class NfcReadingSuccess extends NfcReadingState {
  final NfcDataModel nfcData;
  final BacCredentials usedCredentials;

  const NfcReadingSuccess({
    required this.nfcData,
    required this.usedCredentials,
  });

  @override
  List<Object?> get props => [nfcData, usedCredentials];
}

class NfcReadingError extends NfcReadingState {
  final String message;
  final String? errorDetails;

  const NfcReadingError({
    required this.message,
    this.errorDetails,
  });

  @override
  List<Object?> get props => [message, errorDetails];
}

// BAC Credentials model
class BacCredentials {
  final String documentNumber;
  final DateTime dateOfBirth;
  final DateTime dateOfExpiry;
  final String source; // 'mrz' or 'cccd_extraction'

  const BacCredentials({
    required this.documentNumber,
    required this.dateOfBirth,
    required this.dateOfExpiry,
    required this.source,
  });

  List<Object?> get props => [documentNumber, dateOfBirth, dateOfExpiry, source];
}

// Bloc
class NfcReadingBloc extends Bloc<NfcReadingEvent, NfcReadingState> {
  final NfcProvider _nfcProvider;
  final NfcDataParserService _nfcParser;
  final NfcDataRepository _nfcRepository;
  final AppLogger _logger;
  final MrzScannerService _mrzParser;

  NfcReadingBloc({
    required NfcProvider nfcProvider,
    required NfcDataParserService nfcParser,
    required NfcDataRepository nfcRepository,
    required AppLogger logger,
  })  : _nfcProvider = nfcProvider,
        _nfcParser = nfcParser,
        _nfcRepository = nfcRepository,
        _logger = logger,
        _mrzParser = MrzScannerService(),
        super(const NfcReadingInitial()) {
    on<StartNfcReadingEvent>(_onStartNfcReading);
    on<RetryNfcReadingEvent>(_onRetryNfcReading);
    on<ResetNfcReadingEvent>(_onResetNfcReading);
  }

  Future<void> _onStartNfcReading(
    StartNfcReadingEvent event,
    Emitter<NfcReadingState> emit,
  ) async {
    try {
      emit(const NfcReadingLoading('Đang khởi tạo kết nối NFC...'));

      // Get BAC credentials from previous steps
      final bacCredentials = _getBacCredentials(event.cccdExtractionResult, event.mrzScanResult);
      if (bacCredentials == null) {
        emit(const NfcReadingError(
          message: 'Không thể lấy thông tin BAC. Vui lòng hoàn thành bước trước.',
        ));
        return;
      }

      await _logger.i('Using BAC credentials from ${bacCredentials.source}: ${bacCredentials.documentNumber},${bacCredentials.dateOfBirth},${bacCredentials.dateOfExpiry}');

      // Check NFC availability
      final nfcStatus = await NfcProvider.nfcStatus;
      if (nfcStatus != NfcStatus.enabled) {
        emit(const NfcReadingError(
          message: 'NFC không khả dụng. Vui lòng bật NFC trên thiết bị.',
        ));
        return;
      }

      emit(const NfcReadingLoading('Đang kết nối với thẻ...'));

      // Connect to NFC tag
      await _nfcProvider.connect(
        iosAlertMessage: "Đặt CCCD sát vào mặt sau điện thoại",
      );

      emit(const NfcReadingLoading('Đang thiết lập phiên làm việc...'));

      // Update iOS alert message for session setup
      await _nfcProvider.setIosAlertMessage("Đang thiết lập phiên làm việc...");

      // Create passport instance
      final passport = Passport(_nfcProvider);

      // Create BAC key with credentials from previous steps
      final bacKey = DBAKey(
        bacCredentials.documentNumber,
        bacCredentials.dateOfBirth,
        bacCredentials.dateOfExpiry,
      );

      // Start BAC session
      await passport.startSession(bacKey);

      emit(const NfcReadingLoading('Đang đọc thông tin EF.COM...'));

      // Update iOS alert message for reading EF.COM
      await _nfcProvider.setIosAlertMessage("Đang đọc thông tin EF.COM...");

      // Read EF.COM to get available data groups
      final com = await passport.readEfCOM();
      await _logger.i(
        'EF.COM read successfully. Available DGs: ${com.dgTags.map((tag) => '0x${tag.value.toRadixString(16).toUpperCase()}').join(', ')}',
      );

      emit(const NfcReadingLoading('Đang đọc và parse các nhóm dữ liệu...'));

      // Update iOS alert message for reading data groups
      await _nfcProvider.setIosAlertMessage("Đang đọc các nhóm dữ liệu...");

      // Collect raw DG data for parsing
      final Map<String, dynamic> rawDgData = {};

      // Read all available data groups
      final dgReadMethods = [
        (EfDG1.TAG, () => passport.readEfDG1(), 'dg1'),
        (EfDG2.TAG, () => passport.readEfDG2(), 'dg2'),
        (EfDG5.TAG, () => passport.readEfDG5(), 'dg5'),
        (EfDG6.TAG, () => passport.readEfDG6(), 'dg6'),
        (EfDG7.TAG, () => passport.readEfDG7(), 'dg7'),
        (EfDG8.TAG, () => passport.readEfDG8(), 'dg8'),
        (EfDG9.TAG, () => passport.readEfDG9(), 'dg9'),
        (EfDG10.TAG, () => passport.readEfDG10(), 'dg10'),
        (EfDG11.TAG, () => passport.readEfDG11(), 'dg11'),
        (EfDG12.TAG, () => passport.readEfDG12(), 'dg12'),
        (EfDG13.TAG, () => passport.readEfDG13(), 'dg13'),
        (EfDG14.TAG, () => passport.readEfDG14(), 'dg14'),
        (EfDG15.TAG, () => passport.readEfDG15(), 'dg15'),
        (EfDG16.TAG, () => passport.readEfDG16(), 'dg16'),
      ];

      for (final (tag, readMethod, dgName) in dgReadMethods) {
        if (com.dgTags.contains(tag)) {
          try {
            final dg = await readMethod();
            rawDgData[dgName] = dg;
            await _logger.i('$dgName read successfully. Length: ${dg.toBytes().length} bytes');

            // Perform Active Authentication for DG15
            if (dgName == 'dg15') {
              emit(const NfcReadingLoading('Đang xác thực Active Authentication...'));
              // Update iOS alert message for Active Authentication
              await _nfcProvider.setIosAlertMessage("Đang xác thực Active Authentication...");
              try {
                final aaSig = await passport.activeAuthenticate(Uint8List(8));
                rawDgData['aaSignature'] = aaSig;
                await _logger.i('Active Authentication completed. Signature length: ${aaSig.length} bytes');
              } catch (e) {
                await _logger.w('Active Authentication failed: $e');
              }
            }
          } catch (e) {
            await _logger.e('Error reading $dgName: $e');
          }
        }
      }

      // Read EF.SOD
      try {
        final sod = await passport.readEfSOD();
        rawDgData['sod'] = sod;
        await _logger.i('EF.SOD read successfully. Length: ${sod.toBytes().length} bytes');
      } catch (e) {
        await _logger.e('Error reading EF.SOD: $e');
      }

      emit(const NfcReadingLoading('Đang parse và xử lý dữ liệu...'));

      // Update iOS alert message for parsing data
      await _nfcProvider.setIosAlertMessage("Đang xử lý dữ liệu...");

      // Parse all NFC data
      try {
        final parsedData = await _nfcParser.parseAllData(rawDgData);
        final validatedData = await _nfcRepository.validateAndEnrich(parsedData);

        await _logger.i('NFC data parsed successfully: ${validatedData.fullName ?? 'Unknown'}');

        emit(NfcReadingSuccess(
          nfcData: validatedData,
          usedCredentials: bacCredentials,
        ));

      } catch (e) {
        await _logger.e('Error parsing NFC data: $e');
        
        // Fallback to basic MRZ data if parsing fails
        if (rawDgData['dg1'] != null) {
          try {
            final dg1 = rawDgData['dg1'] as EfDG1;
            final mrz = dg1.mrz;
            
            // Create fallback NFC data
            final fallbackData = NfcDataModel(
              mrzInfo: MrzInfo.fromMrz(mrz),
              parsedAt: DateTime.now(),
            );

            emit(NfcReadingSuccess(
              nfcData: fallbackData,
              usedCredentials: bacCredentials,
            ));
            
            await _logger.w('Fallback to basic MRZ data due to parsing error');
            return;
          } catch (fallbackError) {
            await _logger.e('Fallback MRZ parsing also failed: $fallbackError');
          }
        }

        emit(NfcReadingError(
          message: 'Lỗi parse dữ liệu NFC',
          errorDetails: e.toString(),
        ));
      }

    } catch (e) {
      await _logger.e('NFC reading failed: $e');
      
      // Update iOS alert message for error
      try {
        await _nfcProvider.setIosAlertMessage("Lỗi đọc NFC");
      } catch (alertError) {
        await _logger.e('Error updating iOS alert message: $alertError');
      }
      
      final errorMessage = _getErrorMessage(e.toString());
      emit(NfcReadingError(
        message: errorMessage,
        errorDetails: e.toString(),
      ));
    } finally {
      try {
        // Always disconnect with completion message
        await _nfcProvider.disconnect(
          iosAlertMessage: "Hoàn thành quét NFC",
        );
      } catch (e) {
        await _logger.e('Error disconnecting NFC: $e');
      }
    }
  }

  Future<void> _onRetryNfcReading(
    RetryNfcReadingEvent event,
    Emitter<NfcReadingState> emit,
  ) async {
    // Same logic as start
    await _onStartNfcReading(StartNfcReadingEvent(
      cccdExtractionResult: event.cccdExtractionResult,
      mrzScanResult: event.mrzScanResult,
    ), emit);
  }

  void _onResetNfcReading(
    ResetNfcReadingEvent event,
    Emitter<NfcReadingState> emit,
  ) {
    emit(const NfcReadingInitial());
  }

  BacCredentials? _getBacCredentials(
    CccdExtractionModel? cccdExtractionResult,
    MrzScanResult? mrzScanResult,
  ) {
    _logger.i('Getting BAC credentials...');
    _logger.i('CCCD Extraction Result: ${cccdExtractionResult?.idNumber ?? 'null'}');
    _logger.i('MRZ Scan Result: ${mrzScanResult?.isSuccess ?? 'null'} (${mrzScanResult?.rawLines.length ?? 0} lines)');
    _logger.i('MRZ ParsedData Result: ${mrzScanResult?.parsedData ?? 'null'}');

    // Priority 1: Use MRZ scan result if available and parsed successfully
    if (mrzScanResult != null && mrzScanResult.isSuccess && mrzScanResult.parsedData != null) {
      final mrz = mrzScanResult.parsedData!;
      final documentNumber = _extractLast9Digits(mrz.documentNumber);
      _logger.i('Using MRZ parsed data for BAC: ${mrz.documentNumber} -> $documentNumber (last 9 digits)');
      return BacCredentials(
        documentNumber: documentNumber,
        dateOfBirth: mrz.birthDate,
        dateOfExpiry: mrz.expiryDate,
        source: 'mrz',
      );
    }

    // Priority 2: Use CCCD extraction result if available
    if (cccdExtractionResult != null) {
      final documentNumber = _extractLast9Digits(cccdExtractionResult.idNumber);
      _logger.i('Using CCCD extraction data for BAC: ${cccdExtractionResult.idNumber} -> $documentNumber (last 9 digits)');
      // Parse dates from CCCD extraction result
      try {
        final dob = _parseDateFromString(cccdExtractionResult.dateOfBirth);
        final doe = _parseDateFromString(cccdExtractionResult.expiryDate);
        
        return BacCredentials(
          documentNumber: documentNumber,
          dateOfBirth: dob,
          dateOfExpiry: doe,
          source: 'cccd_extraction',
        );
      } catch (e) {
        _logger.e('Error parsing dates from CCCD extraction: $e');
      }
    }

    // Priority 3: Use MRZ raw lines if available (fallback)
    if (mrzScanResult != null && mrzScanResult.hasRawLines) {
      _logger.i('Using MRZ raw lines for BAC fallback: ${mrzScanResult.rawLines.length} lines');
      try {
        // Try to parse MRZ from raw lines
        final parsedMrz = _mrzParser.extractAndParseMrz(mrzScanResult.rawLines.join('\n'));
        if (parsedMrz.isSuccess && parsedMrz.parsedData != null) {
          final mrz = parsedMrz.parsedData!;
          final documentNumber = _extractLast9Digits(mrz.documentNumber);
          _logger.i('Using MRZ raw lines parsed data for BAC: ${mrz.documentNumber} -> $documentNumber (last 9 digits)');
          return BacCredentials(
            documentNumber: documentNumber,
            dateOfBirth: mrz.birthDate,
            dateOfExpiry: mrz.expiryDate,
            source: 'mrz_raw_lines',
          );
        }
      } catch (e) {
        _logger.e('Error parsing MRZ from raw lines: $e');
      }
    }

    _logger.e('No BAC credentials found from any source');
    return null;
  }

  /// Extract last 9 digits from document number for BAC
  String _extractLast9Digits(String documentNumber) {
    // Remove all non-digit characters
    final digitsOnly = documentNumber.replaceAll(RegExp(r'[^0-9]'), '');
    
    // Take last 9 digits
    if (digitsOnly.length >= 9) {
      return digitsOnly.substring(digitsOnly.length - 9);
    } else {
      // If less than 9 digits, pad with zeros at the beginning
      return digitsOnly.padLeft(9, '0');
    }
  }

  DateTime _parseDateFromString(String dateStr) {
    // Handle different date formats: DD/MM/YYYY, YYYY-MM-DD, etc.
    if (dateStr.contains('/')) {
      final parts = dateStr.split('/');
      return DateTime(
        int.parse(parts[2]), // year
        int.parse(parts[1]), // month
        int.parse(parts[0]), // day
      );
    } else if (dateStr.contains('-')) {
      return DateTime.parse(dateStr);
    } else {
      throw FormatException('Unsupported date format: $dateStr');
    }
  }

  String _getErrorMessage(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('security status not satisfied')) {
      return 'Thông tin BAC không chính xác. Vui lòng kiểm tra lại.';
    } else if (errorLower.contains('timeout')) {
      return 'Hết thời gian chờ. Vui lòng thử lại.';
    } else if (errorLower.contains('tag was lost')) {
      return 'Mất kết nối với thẻ. Vui lòng giữ thẻ ổn định.';
    } else if (errorLower.contains('nfc không khả dụng')) {
      return 'NFC không khả dụng. Vui lòng bật NFC trên thiết bị.';
    } else if (errorLower.contains('invalidated by user')) {
      return 'Người dùng đã hủy thao tác.';
    } else {
      return 'Lỗi đọc NFC: ${error.split(':').last.trim()}';
    }
  }
} 