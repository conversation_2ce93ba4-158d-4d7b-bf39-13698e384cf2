import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/services/backend_url_manager.dart';
import '../../../shared/constants/api_endpoints.dart';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/services/api/api_service.dart';

// Events
abstract class BackendConfigEvent extends Equatable {
  const BackendConfigEvent();

  @override
  List<Object?> get props => [];
}

class LoadSavedBackendUrl extends BackendConfigEvent {}

class TestBackendConnection extends BackendConfigEvent {
  final String url;

  const TestBackendConnection(this.url);

  @override
  List<Object?> get props => [url];
}

class SaveBackendUrl extends BackendConfigEvent {
  final String url;

  const SaveBackendUrl(this.url);

  @override
  List<Object?> get props => [url];
}

class ResetBackendUrl extends BackendConfigEvent {}

// States
abstract class BackendConfigState extends Equatable {
  final String? selectedUrl;
  final String defaultUrl;
  final HealthCheckResult? lastTestResult;

  const BackendConfigState({
    this.selectedUrl,
    required this.defaultUrl,
    this.lastTestResult,
  });

  @override
  List<Object?> get props => [selectedUrl, defaultUrl, lastTestResult];
}

class BackendConfigInitial extends BackendConfigState {
  const BackendConfigInitial({
    super.selectedUrl,
    required super.defaultUrl,
    super.lastTestResult,
  });
}

class BackendConfigLoading extends BackendConfigState {
  const BackendConfigLoading({
    super.selectedUrl,
    required super.defaultUrl,
    super.lastTestResult,
  });
}

class BackendConfigSuccess extends BackendConfigState {
  const BackendConfigSuccess({
    required super.selectedUrl,
    required super.defaultUrl,
    required super.lastTestResult,
  });
}

class BackendConfigFailure extends BackendConfigState {
  final String error;

  const BackendConfigFailure({
    super.selectedUrl,
    required super.defaultUrl,
    required this.error,
    super.lastTestResult,
  });

  @override
  List<Object?> get props => [...super.props, error];
}

// Bloc
class BackendConfigBloc extends Bloc<BackendConfigEvent, BackendConfigState> {
  final AppLogger _logger = AppLogger();

  BackendConfigBloc()
    : super(
        const BackendConfigInitial(defaultUrl: ApiEndpoints.devBackendApiUrl),
      ) {
    on<LoadSavedBackendUrl>(_onLoadSavedBackendUrl);
    on<TestBackendConnection>(_onTestBackendConnection);
    on<SaveBackendUrl>(_onSaveBackendUrl);
    on<ResetBackendUrl>(_onResetBackendUrl);
  }

  Future<void> _onLoadSavedBackendUrl(
    LoadSavedBackendUrl event,
    Emitter<BackendConfigState> emit,
  ) async {
    try {
      _logger.d('🔄 Loading saved backend URL...');
      final savedUrl = await BackendUrlManager.getSelectedUrl();

      // Sử dụng saved URL hoặc mặc định devBackendApiUrl
      final selectedUrl = savedUrl.isNotEmpty
          ? savedUrl
          : ApiEndpoints.devBackendApiUrl;

      emit(
        BackendConfigInitial(
          selectedUrl: selectedUrl,
          defaultUrl: ApiEndpoints.devBackendApiUrl,
        ),
      );

      _logger.i(
        '✅ Loaded backend URL: $selectedUrl (saved: ${savedUrl.isNotEmpty})',
      );
    } catch (e) {
      _logger.e('❌ Error loading saved backend URL: $e');
      emit(
        BackendConfigFailure(
          selectedUrl: state.selectedUrl,
          defaultUrl: ApiEndpoints.devBackendApiUrl,
          error: 'Failed to load saved URL: $e',
          lastTestResult: state.lastTestResult,
        ),
      );
    }
  }

  Future<void> _onTestBackendConnection(
    TestBackendConnection event,
    Emitter<BackendConfigState> emit,
  ) async {
    try {
      _logger.i('🔍 Testing connection to: ${event.url}');

      emit(
        BackendConfigLoading(
          selectedUrl: state.selectedUrl,
          defaultUrl: state.defaultUrl,
          lastTestResult: state.lastTestResult,
        ),
      );

      final result = await BackendUrlManager.testConnection(event.url);

      if (result.isSuccess) {
        _logger.i('✅ Connection test successful');
        emit(
          BackendConfigSuccess(
            selectedUrl: event.url,
            defaultUrl: state.defaultUrl,
            lastTestResult: result,
          ),
        );
      } else {
        _logger.w('⚠️ Connection test failed: ${result.error}');
        emit(
          BackendConfigFailure(
            selectedUrl: state.selectedUrl,
            defaultUrl: state.defaultUrl,
            error: result.error?.isNotEmpty == true
                ? result.error!
                : 'Unknown error',
            lastTestResult: state.lastTestResult,
          ),
        );
      }
    } catch (e) {
      _logger.e('❌ Error testing connection: $e');
      emit(
        BackendConfigFailure(
          selectedUrl: state.selectedUrl,
          defaultUrl: state.defaultUrl,
          error: 'Connection test failed: $e',
          lastTestResult: state.lastTestResult,
        ),
      );
    }
  }

  Future<void> _onSaveBackendUrl(
    SaveBackendUrl event,
    Emitter<BackendConfigState> emit,
  ) async {
    try {
      _logger.i('💾 Saving backend URL: ${event.url}');

      emit(
        BackendConfigLoading(
          selectedUrl: state.selectedUrl,
          defaultUrl: state.defaultUrl,
          lastTestResult: state.lastTestResult,
        ),
      );

      await BackendUrlManager.setSelectedUrl(event.url);

      // Cập nhật ApiService với URL mới
      await ApiService().updateBackendUrlFromPreferences();

      emit(
        BackendConfigSuccess(
          selectedUrl: event.url,
          defaultUrl: state.defaultUrl,
          lastTestResult: state.lastTestResult,
        ),
      );

      _logger.i('✅ Backend URL saved and ApiService updated successfully');
    } catch (e) {
      _logger.e('❌ Error saving backend URL: $e');
      emit(
        BackendConfigFailure(
          selectedUrl: state.selectedUrl,
          defaultUrl: state.defaultUrl,
          error: 'Failed to save URL: $e',
          lastTestResult: state.lastTestResult,
        ),
      );
    }
  }

  Future<void> _onResetBackendUrl(
    ResetBackendUrl event,
    Emitter<BackendConfigState> emit,
  ) async {
    try {
      _logger.i('🔄 Resetting backend URL to default');

      emit(
        BackendConfigLoading(
          selectedUrl: state.selectedUrl,
          defaultUrl: state.defaultUrl,
          lastTestResult: state.lastTestResult,
        ),
      );

      await BackendUrlManager.resetToDefault();

      // Cập nhật ApiService với URL mặc định
      await ApiService().updateBackendUrlFromPreferences();

      emit(
        const BackendConfigInitial(
          selectedUrl: null,
          defaultUrl: ApiEndpoints.devBackendApiUrl,
        ),
      );

      _logger.i('✅ Backend URL reset and ApiService updated successfully');
    } catch (e) {
      _logger.e('❌ Error resetting backend URL: $e');
      emit(
        BackendConfigFailure(
          selectedUrl: state.selectedUrl,
          defaultUrl: state.defaultUrl,
          error: 'Failed to reset URL: $e',
          lastTestResult: state.lastTestResult,
        ),
      );
    }
  }
}
