import 'package:equatable/equatable.dart';

/// Base class cho tất cả Login events
abstract class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object?> get props => [];
}

/// Event khi user submit form đăng nhập
class LoginSubmitted extends LoginEvent {
  final String username;
  final String password;
  final bool rememberMe;

  const LoginSubmitted({
    required this.username,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [username, password, rememberMe];
}

/// Event khi user toggle password visibility
class LoginPasswordVisibilityToggled extends LoginEvent {
  const LoginPasswordVisibilityToggled();
}

/// Event khi user toggle remember me checkbox
class LoginRememberMeToggled extends LoginEvent {
  final bool rememberMe;

  const LoginRememberMeToggled({required this.rememberMe});

  @override
  List<Object?> get props => [rememberMe];
}

/// Event để clear error messages
class LoginErrorCleared extends LoginEvent {
  const LoginErrorCleared();
}

/// Event để initialize login screen
class LoginInitialized extends LoginEvent {
  const LoginInitialized();
} 