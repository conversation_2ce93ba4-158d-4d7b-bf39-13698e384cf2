import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import '../services/auth_service.dart';
import 'login_event.dart';
import 'login_state.dart';

/// Bloc quản lý logic đăng nhập
class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final AuthService _authService;
  final Logger _logger = Logger();

  LoginBloc({AuthService? authService})
      : _authService = authService ?? AuthService(),
        super(const LoginInitial()) {
    // Đăng ký event handlers
    on<LoginInitialized>(_onLoginInitialized);
    on<LoginSubmitted>(_onLoginSubmitted);
    on<LoginPasswordVisibilityToggled>(_onPasswordVisibilityToggled);
    on<LoginRememberMeToggled>(_onRememberMeToggled);
    on<LoginErrorCleared>(_onErrorCleared);
  }

  /// Initialize login screen
  Future<void> _onLoginInitialized(
    LoginInitialized event,
    Emitter<LoginState> emit,
  ) async {
    try {
      // Initialize AuthService nếu chưa được initialize
      await _authService.initialize();
      
      // Emit initial state
      emit(const LoginInitial());
      
      _logger.i('Login screen initialized');
    } catch (e) {
      _logger.e('Login initialization error: $e');
      emit(LoginFailure(
        error: 'Không thể khởi tạo màn hình đăng nhập',
      ));
    }
  }

  /// Xử lý khi user submit form đăng nhập
  Future<void> _onLoginSubmitted(
    LoginSubmitted event,
    Emitter<LoginState> emit,
  ) async {
    // Lấy current state để preserve UI settings
    final currentPasswordVisible = _getCurrentPasswordVisibility();
    final currentRememberMe = _getCurrentRememberMe();

    // Emit loading state
    emit(LoginLoading(
      isPasswordVisible: currentPasswordVisible,
      rememberMe: currentRememberMe,
    ));

    try {
      _logger.i('Attempting login for user: ${event.username}');

      // Gọi AuthService để đăng nhập
      final success = await _authService.loginWithRememberMe(
        event.username,
        event.password,
        event.rememberMe,
      );

      if (success) {
        _logger.i('Login successful for user: ${event.username}');
        
        // Wait a bit to ensure AuthService has emitted state
        await Future.delayed(const Duration(milliseconds: 100));
        
        emit(const LoginSuccess());
      } else {
        // Lấy error message từ AuthService
        final errorMessage = _authService.authError ?? 
            'Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin.';
        
        _logger.w('Login failed for user: ${event.username}, error: $errorMessage');
        
        emit(LoginFailure(
          error: errorMessage,
          isPasswordVisible: currentPasswordVisible,
          rememberMe: currentRememberMe,
        ));
      }
    } catch (e) {
      _logger.e('Login error for user: ${event.username}, exception: $e');
      
      emit(LoginFailure(
        error: 'Có lỗi xảy ra. Vui lòng thử lại.',
        isPasswordVisible: currentPasswordVisible,
        rememberMe: currentRememberMe,
      ));
    }
  }

  /// Xử lý toggle password visibility
  void _onPasswordVisibilityToggled(
    LoginPasswordVisibilityToggled event,
    Emitter<LoginState> emit,
  ) {
    final currentPasswordVisible = _getCurrentPasswordVisibility();
    final currentRememberMe = _getCurrentRememberMe();
    final currentError = _getCurrentError();

    emit(LoginStateChanged(
      isPasswordVisible: !currentPasswordVisible,
      rememberMe: currentRememberMe,
      error: currentError,
    ));
  }

  /// Xử lý toggle remember me
  void _onRememberMeToggled(
    LoginRememberMeToggled event,
    Emitter<LoginState> emit,
  ) {
    final currentPasswordVisible = _getCurrentPasswordVisibility();
    final currentError = _getCurrentError();

    emit(LoginStateChanged(
      isPasswordVisible: currentPasswordVisible,
      rememberMe: event.rememberMe,
      error: currentError,
    ));
  }

  /// Xử lý clear error
  void _onErrorCleared(
    LoginErrorCleared event,
    Emitter<LoginState> emit,
  ) {
    final currentPasswordVisible = _getCurrentPasswordVisibility();
    final currentRememberMe = _getCurrentRememberMe();

    emit(LoginStateChanged(
      isPasswordVisible: currentPasswordVisible,
      rememberMe: currentRememberMe,
      error: null,
    ));
  }

  /// Helper methods để lấy current state values
  bool _getCurrentPasswordVisibility() {
    final currentState = state;
    if (currentState is LoginInitial) return currentState.isPasswordVisible;
    if (currentState is LoginLoading) return currentState.isPasswordVisible;
    if (currentState is LoginFailure) return currentState.isPasswordVisible;
    if (currentState is LoginStateChanged) return currentState.isPasswordVisible;
    return false;
  }

  bool _getCurrentRememberMe() {
    final currentState = state;
    if (currentState is LoginInitial) return currentState.rememberMe;
    if (currentState is LoginLoading) return currentState.rememberMe;
    if (currentState is LoginFailure) return currentState.rememberMe;
    if (currentState is LoginStateChanged) return currentState.rememberMe;
    return false;
  }

  String? _getCurrentError() {
    final currentState = state;
    if (currentState is LoginFailure) return currentState.error;
    if (currentState is LoginStateChanged) return currentState.error;
    return null;
  }

  /// Check if currently loading
  bool get isLoading => state is LoginLoading;

  /// Get current error message
  String? get errorMessage => _getCurrentError();

  /// Dispose resources
  @override
  Future<void> close() {
    _logger.i('LoginBloc disposed');
    return super.close();
  }
} 