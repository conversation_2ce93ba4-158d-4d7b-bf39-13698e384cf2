import 'package:equatable/equatable.dart';

/// Base class cho tất cả Login states
abstract class LoginState extends Equatable {
  const LoginState();

  @override
  List<Object?> get props => [];
}

/// State ban đầu của login screen
class LoginInitial extends LoginState {
  final bool isPasswordVisible;
  final bool rememberMe;

  const LoginInitial({
    this.isPasswordVisible = false,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [isPasswordVisible, rememberMe];
}

/// State khi đang loading (đang đăng nhập)
class LoginLoading extends LoginState {
  final bool isPasswordVisible;
  final bool rememberMe;

  const LoginLoading({
    this.isPasswordVisible = false,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [isPasswordVisible, rememberMe];
}

/// State khi đăng nhập thành công
class LoginSuccess extends LoginState {
  const LoginSuccess();
}

/// State khi đăng nhập thất bại
class LoginFailure extends LoginState {
  final String error;
  final bool isPasswordVisible;
  final bool rememberMe;

  const LoginFailure({
    required this.error,
    this.isPasswordVisible = false,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [error, isPasswordVisible, rememberMe];
}

/// State khi có thay đổi UI (password visibility, remember me)
class LoginStateChanged extends LoginState {
  final bool isPasswordVisible;
  final bool rememberMe;
  final String? error;

  const LoginStateChanged({
    required this.isPasswordVisible,
    required this.rememberMe,
    this.error,
  });

  @override
  List<Object?> get props => [isPasswordVisible, rememberMe, error];
} 