import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/cccd_extraction_model.dart';
import '../../../shared/services/cccd_extraction_service.dart';

// Events
abstract class CccdExtractionEvent extends Equatable {
  const CccdExtractionEvent();

  @override
  List<Object?> get props => [];
}

class ExtractCccdEvent extends CccdExtractionEvent {
  final String frontImagePath;
  final String backImagePath;

  const ExtractCccdEvent({
    required this.frontImagePath,
    required this.backImagePath,
  });

  @override
  List<Object?> get props => [frontImagePath, backImagePath];
}

class ReextractCccdEvent extends CccdExtractionEvent {
  final String frontImagePath;
  final String backImagePath;

  const ReextractCccdEvent({
    required this.frontImagePath,
    required this.backImagePath,
  });

  @override
  List<Object?> get props => [frontImagePath, backImagePath];
}

class ResetCccdExtractionEvent extends CccdExtractionEvent {
  const ResetCccdExtractionEvent();
}

// States
abstract class CccdExtractionState extends Equatable {
  const CccdExtractionState();

  @override
  List<Object?> get props => [];
}

class CccdExtractionInitial extends CccdExtractionState {
  const CccdExtractionInitial();
}

class CccdExtractionLoading extends CccdExtractionState {
  const CccdExtractionLoading();
}

class CccdExtractionSuccess extends CccdExtractionState {
  final CccdExtractionModel result;

  const CccdExtractionSuccess(this.result);

  @override
  List<Object?> get props => [result];
}

class CccdExtractionError extends CccdExtractionState {
  final String message;
  final CccdExtractionException? exception;

  const CccdExtractionError({
    required this.message,
    this.exception,
  });

  @override
  List<Object?> get props => [message, exception];
}

// Bloc
class CccdExtractionBloc extends Bloc<CccdExtractionEvent, CccdExtractionState> {
  final CccdExtractionService _cccdExtractionService;

  CccdExtractionBloc({
    required CccdExtractionService cccdExtractionService,
  })  : _cccdExtractionService = cccdExtractionService,
        super(const CccdExtractionInitial()) {
    on<ExtractCccdEvent>(_onExtractCccd);
    on<ReextractCccdEvent>(_onReextractCccd);
    on<ResetCccdExtractionEvent>(_onResetCccdExtraction);
  }

  Future<void> _onExtractCccd(
    ExtractCccdEvent event,
    Emitter<CccdExtractionState> emit,
  ) async {
    try {
      emit(const CccdExtractionLoading());

      final result = await _cccdExtractionService.extractCccdFromFiles(
        frontImagePath: event.frontImagePath,
        backImagePath: event.backImagePath,
      );

      emit(CccdExtractionSuccess(result));
    } on CccdExtractionException catch (e) {
      emit(CccdExtractionError(
        message: 'Lỗi trích xuất CCCD: ${e.message}',
        exception: e,
      ));
    } catch (e) {
      emit(CccdExtractionError(
        message: 'Có lỗi xảy ra khi trích xuất CCCD',
        exception: null,
      ));
    }
  }

  Future<void> _onReextractCccd(
    ReextractCccdEvent event,
    Emitter<CccdExtractionState> emit,
  ) async {
    try {
      emit(const CccdExtractionLoading());

      final result = await _cccdExtractionService.extractCccdFromFiles(
        frontImagePath: event.frontImagePath,
        backImagePath: event.backImagePath,
      );

      emit(CccdExtractionSuccess(result));
    } on CccdExtractionException catch (e) {
      emit(CccdExtractionError(
        message: 'Lỗi trích xuất lại CCCD: ${e.message}',
        exception: e,
      ));
    } catch (e) {
      emit(CccdExtractionError(
        message: 'Có lỗi xảy ra khi trích xuất lại CCCD',
        exception: null,
      ));
    }
  }

  void _onResetCccdExtraction(
    ResetCccdExtractionEvent event,
    Emitter<CccdExtractionState> emit,
  ) {
    emit(const CccdExtractionInitial());
  }
} 