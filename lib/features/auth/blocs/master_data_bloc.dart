import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/services/master_data_service.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/branch_model.dart';
import '../../../shared/models/ward_model.dart';
import '../../../shared/models/position_model.dart';
import '../../../shared/models/region_model.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/models/customer_tag_model.dart';
import '../../../shared/models/bank_account_model.dart';
import '../../../shared/models/collateral_category_model.dart';

// Events
abstract class MasterDataEvent extends Equatable {
  const MasterDataEvent();

  @override
  List<Object?> get props => [];
}

class LoadProvincesEvent extends MasterDataEvent {
  final String? search;
  const LoadProvincesEvent({this.search});

  @override
  List<Object?> get props => [search];
}

class LoadBranchesEvent extends MasterDataEvent {
  final String provinceId;
  final String? search;
  const LoadBranchesEvent(this.provinceId, {this.search});

  @override
  List<Object?> get props => [provinceId, search];
}

class LoadBranchesByRegionsEvent extends MasterDataEvent {
  final List<String> regionIds;
  final String? search;
  const LoadBranchesByRegionsEvent(this.regionIds, {this.search});

  @override
  List<Object?> get props => [regionIds, search];
}

class LoadWardsEvent extends MasterDataEvent {
  final String provinceId;
  final String? search;
  const LoadWardsEvent(this.provinceId, {this.search});

  @override
  List<Object?> get props => [provinceId, search];
}

class LoadPositionsEvent extends MasterDataEvent {
  final String? search;
  const LoadPositionsEvent({this.search});

  @override
  List<Object?> get props => [search];
}

class LoadRegionsEvent extends MasterDataEvent {
  final String? search;
  const LoadRegionsEvent({this.search});

  @override
  List<Object?> get props => [search];
}

class LoadConfigEvent extends MasterDataEvent {
  final String groupCode;
  const LoadConfigEvent(this.groupCode);

  @override
  List<Object?> get props => [groupCode];
}

class LoadCustomerTagsEvent extends MasterDataEvent {}

class LoadBankAccountsEvent extends MasterDataEvent {
  final String idCardNo;
  const LoadBankAccountsEvent(this.idCardNo);

  @override
  List<Object?> get props => [idCardNo];
}

class LoadCollateralCategoriesEvent extends MasterDataEvent {}

class RefreshMasterDataEvent extends MasterDataEvent {}

class ClearMasterDataEvent extends MasterDataEvent {}

// States
abstract class MasterDataState extends Equatable {
  const MasterDataState();

  @override
  List<Object?> get props => [];
}

class MasterDataInitial extends MasterDataState {}

class MasterDataLoading extends MasterDataState {
  final String message;
  final String? type; // Thêm type để phân biệt loại loading
  const MasterDataLoading(this.message, {this.type});

  @override
  List<Object?> get props => [message, type];
}

/// Composite State chứa tất cả data đã load, không bị mất khi load data mới
class MasterDataLoaded extends MasterDataState {
  final List<ProvinceModel> provinces;
  final Map<String, List<WardModel>> wardsByProvince; // Key: provinceId, Value: list wards
  final Map<String, List<ConfigModel>> configsByGroup; // Key: groupCode, Value: list configs
  final List<BranchModel> branches;
  final List<PositionModel> positions;
  final List<RegionModel> regions;
  final List<CustomerTagModel> customerTags;
  final List<BankAccountModel> bankAccounts; // Tương tự customerTags
  final List<CollateralCategoryModel> collateralCategories;

  const MasterDataLoaded({
    this.provinces = const [],
    this.wardsByProvince = const {},
    this.configsByGroup = const {},
    this.branches = const [],
    this.positions = const [],
    this.regions = const [],
    this.customerTags = const [],
    this.bankAccounts = const [],
    this.collateralCategories = const [],
  });

  /// Copy with method để update từng phần data
  MasterDataLoaded copyWith({
    List<ProvinceModel>? provinces,
    Map<String, List<WardModel>>? wardsByProvince,
    Map<String, List<ConfigModel>>? configsByGroup,
    List<BranchModel>? branches,
    List<PositionModel>? positions,
    List<RegionModel>? regions,
    List<CustomerTagModel>? customerTags,
    List<BankAccountModel>? bankAccounts,
    List<CollateralCategoryModel>? collateralCategories,
  }) {
    return MasterDataLoaded(
      provinces: provinces ?? this.provinces,
      wardsByProvince: wardsByProvince ?? this.wardsByProvince,
      configsByGroup: configsByGroup ?? this.configsByGroup,
      branches: branches ?? this.branches,
      positions: positions ?? this.positions,
      regions: regions ?? this.regions,
      customerTags: customerTags ?? this.customerTags,
      bankAccounts: bankAccounts ?? this.bankAccounts,
      collateralCategories: collateralCategories ?? this.collateralCategories,
    );
  }

  @override
  List<Object?> get props => [
        provinces,
        wardsByProvince,
        configsByGroup,
        branches,
        positions,
        regions,
        customerTags,
        bankAccounts,
        collateralCategories,
      ];
}

// Legacy states - giữ lại để backward compatibility
class ProvincesLoaded extends MasterDataState {
  final List<ProvinceModel> provinces;
  const ProvincesLoaded(this.provinces);

  @override
  List<Object?> get props => [provinces];
}

class BranchesLoaded extends MasterDataState {
  final List<BranchModel> branches;
  final String provinceId;
  const BranchesLoaded(this.branches, this.provinceId);

  @override
  List<Object?> get props => [branches, provinceId];
}

class BranchesByRegionsLoaded extends MasterDataState {
  final List<BranchModel> branches;
  final List<String> regionIds;
  const BranchesByRegionsLoaded(this.branches, this.regionIds);

  @override
  List<Object?> get props => [branches, regionIds];
}

class WardsLoaded extends MasterDataState {
  final List<WardModel> wards;
  final String provinceId;
  const WardsLoaded(this.wards, this.provinceId);

  @override
  List<Object?> get props => [wards, provinceId];
}

class PositionsLoaded extends MasterDataState {
  final List<PositionModel> positions;
  const PositionsLoaded(this.positions);

  @override
  List<Object?> get props => [positions];
}

class RegionsLoaded extends MasterDataState {
  final List<RegionModel> regions;
  const RegionsLoaded(this.regions);

  @override
  List<Object?> get props => [regions];
}

class ConfigLoaded extends MasterDataState {
  final List<ConfigModel> configs;
  final String groupCode;
  const ConfigLoaded(this.configs, this.groupCode);

  @override
  List<Object?> get props => [configs, groupCode];
}

class CustomerTagsLoaded extends MasterDataState {
  final List<CustomerTagModel> customerTags;
  const CustomerTagsLoaded(this.customerTags);

  @override
  List<Object?> get props => [customerTags];
}

class BankAccountsLoaded extends MasterDataState {
  final List<BankAccountModel> bankAccounts;
  final String idCardNo;
  const BankAccountsLoaded(this.bankAccounts, this.idCardNo);

  @override
  List<Object?> get props => [bankAccounts, idCardNo];
}

class CollateralCategoriesLoaded extends MasterDataState {
  final List<CollateralCategoryModel> collateralCategories;
  const CollateralCategoriesLoaded(this.collateralCategories);

  @override
  List<Object?> get props => [collateralCategories];
}

class MasterDataError extends MasterDataState {
  final String message;
  final String? type;
  const MasterDataError(this.message, {this.type});

  @override
  List<Object?> get props => [message, type];
}

/// BLoC để quản lý master data (provinces, branches, positions, regions)
class MasterDataBloc extends Bloc<MasterDataEvent, MasterDataState> {
  final MasterDataService _masterDataService = MasterDataService();
  final AppLogger _logger = AppLogger();

  // Internal state để maintain data
  MasterDataLoaded _currentState = const MasterDataLoaded();

  MasterDataBloc() : super(MasterDataInitial()) {
    on<LoadProvincesEvent>(_onLoadProvinces);
    on<LoadBranchesEvent>(_onLoadBranches);
    on<LoadBranchesByRegionsEvent>(_onLoadBranchesByRegions);
    on<LoadWardsEvent>(_onLoadWards);
    on<LoadPositionsEvent>(_onLoadPositions);
    on<LoadRegionsEvent>(_onLoadRegions);
    on<LoadConfigEvent>(_onLoadConfig);
    on<LoadCustomerTagsEvent>(_onLoadCustomerTags);
    on<LoadBankAccountsEvent>(_onLoadBankAccounts);
    on<LoadCollateralCategoriesEvent>(_onLoadCollateralCategories);
    on<RefreshMasterDataEvent>(_onRefreshMasterData);
    on<ClearMasterDataEvent>(_onClearMasterData);
  }

  /// Load provinces
  Future<void> _onLoadProvinces(
    LoadProvincesEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      await _logger.i('=== START: Loading provinces ===');
      await _logger.i('Search parameter: ${event.search}');
      
      emit(const MasterDataLoading('Đang tải danh sách tỉnh/thành...', type: 'provinces'));
      
      await _logger.i('Calling MasterDataService.getProvinces()...');
      final provinces = await _masterDataService.getProvinces(search: event.search);
      
      await _logger.i('Provinces loaded successfully: ${provinces.length} items');
      await _logger.i('First province: ${provinces.isNotEmpty ? provinces.first.toJson() : 'No provinces'}');
      await _logger.i('=== END: Loading provinces ===');
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(provinces: provinces);
      emit(_currentState);

    } catch (e) {
      await _logger.i('=== ERROR: Loading provinces ===');
      await _logger.i('Error type: ${e.runtimeType}');
      await _logger.i('Error message: $e');
      await _logger.i('Stack trace: ${StackTrace.current}');
      await _logger.i('=== END ERROR ===');
      
      emit(MasterDataError(
        'Không thể tải danh sách tỉnh/thành: $e',
        type: 'provinces',
      ));
    }
  }

  /// Load branches for province
  Future<void> _onLoadBranches(
    LoadBranchesEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách chi nhánh...', type: 'branches'));
      await _logger.i('Loading branches for province: ${event.provinceId}');

      final branches = await _masterDataService.getBranches(
        provinceId: event.provinceId,
        search: event.search,
      );
      
      await _logger.i('Branches loaded successfully: ${branches.length} items');
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(branches: branches);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading branches: $e');
      emit(MasterDataError(
        'Không thể tải danh sách chi nhánh: $e',
        type: 'branches',
      ));
    }
  }

  /// Load branches by multiple regions
  Future<void> _onLoadBranchesByRegions(
    LoadBranchesByRegionsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách chi nhánh theo khu vực...', type: 'branches'));
      await _logger.i('Loading branches for regions: ${event.regionIds}');

      final branches = await _masterDataService.getBranches(
        regionIds: event.regionIds,
        search: event.search,
      );
      
      await _logger.i('Branches loaded successfully: ${branches.length} items');
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(branches: branches);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading branches by regions: $e');
      emit(MasterDataError(
        'Không thể tải danh sách chi nhánh theo khu vực: $e',
        type: 'branches',
      ));
    }
  }

  /// Load wards for province
  Future<void> _onLoadWards(
    LoadWardsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách phường/xã...', type: 'wards'));
      await _logger.i('Loading wards for province: ${event.provinceId}');

      final wards = await _masterDataService.getWards(
        provinceId: event.provinceId,
        search: event.search,
      );
      
      await _logger.i('Wards loaded successfully: ${wards.length} items');
      
      // Update internal state và emit composite state
      final updatedWardsByProvince = Map<String, List<WardModel>>.from(_currentState.wardsByProvince);
      updatedWardsByProvince[event.provinceId] = wards;
      
      await _logger.i('Storing wards for province: ${event.provinceId}, count: ${wards.length}');
      await _logger.i('Updated wardsByProvince keys: ${updatedWardsByProvince.keys.toList()}');
      
      _currentState = _currentState.copyWith(wardsByProvince: updatedWardsByProvince);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading wards: $e');
      emit(MasterDataError(
        'Không thể tải danh sách phường/xã: $e',
        type: 'wards',
      ));
    }
  }

  /// Load positions
  Future<void> _onLoadPositions(
    LoadPositionsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách chức vụ...', type: 'positions'));
      await _logger.i('Loading positions with search: ${event.search}');

      final positions = await _masterDataService.getPositions(search: event.search);
      
      await _logger.i('Positions loaded successfully: ${positions.length} items');
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(positions: positions);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading positions: $e');
      emit(MasterDataError(
        'Không thể tải danh sách chức vụ: $e',
        type: 'positions',
      ));
    }
  }

  /// Load regions
  Future<void> _onLoadRegions(
    LoadRegionsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách vùng miền...', type: 'regions'));
      await _logger.i('Loading regions with search: ${event.search}');

      final regions = await _masterDataService.getRegions(search: event.search);
      
      await _logger.i('Regions loaded successfully: ${regions.length} items');
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(regions: regions);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading regions: $e');
      emit(MasterDataError(
        'Không thể tải danh sách vùng miền: $e',
        type: 'regions',
      ));
    }
  }

  /// Load config theo group code
  Future<void> _onLoadConfig(
    LoadConfigEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách cấu hình...', type: 'config'));
      await _logger.i('Loading config with group code: ${event.groupCode}');

      final configs = await _masterDataService.getConfig(event.groupCode);
      
      await _logger.i('Configs loaded successfully: ${configs.length} items');
      
      // Update internal state và emit composite state
      final updatedConfigsByGroup = Map<String, List<ConfigModel>>.from(_currentState.configsByGroup);
      updatedConfigsByGroup[event.groupCode] = configs;
      
      _currentState = _currentState.copyWith(configsByGroup: updatedConfigsByGroup);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading config: $e');
      emit(MasterDataError(
        'Không thể tải danh sách cấu hình: $e',
        type: 'config',
      ));
    }
  }

  /// Load customer tags
  Future<void> _onLoadCustomerTags(
    LoadCustomerTagsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách tags...', type: 'customer_tag'));
      await _logger.i('Loading customer tags');

      final customerTags = await _masterDataService.getCustomerTags();
      
      await _logger.i('Customer tags loaded successfully: ${customerTags.length} items');
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(customerTags: customerTags);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading customer tags: $e');
      emit(MasterDataError(
        'Không thể tải danh sách tags: $e',
        type: 'customer_tag',
      ));
    }
  }

  /// Load bank accounts
  Future<void> _onLoadBankAccounts(
    LoadBankAccountsEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      emit(const MasterDataLoading('Đang tải danh sách tài khoản...', type: 'bank_accounts'));
      await _logger.i('Loading bank accounts for ID card: ${event.idCardNo}');

      final bankAccounts = await _masterDataService.getBankAccounts(event.idCardNo);
      
      await _logger.i('Bank accounts loaded successfully: ${bankAccounts.length} items');
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(bankAccounts: bankAccounts);
      emit(_currentState);

    } catch (e) {
      await _logger.i('Error loading bank accounts: $e');
      emit(MasterDataError(
        'Không thể tải danh sách tài khoản: $e',
        type: 'bank_accounts',
      ));
    }
  }

  /// Load collateral categories
  Future<void> _onLoadCollateralCategories(
    LoadCollateralCategoriesEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      await _logger.i('=== START: Loading collateral categories in bloc ===');
      emit(const MasterDataLoading('Đang tải danh sách loại tài sản đảm bảo...', type: 'collateral_categories'));

      final collateralCategories = await _masterDataService.getCollateralCategories();
      
      await _logger.i('Collateral categories loaded successfully: ${collateralCategories.length} items');
      for (int i = 0; i < collateralCategories.length; i++) {
        await _logger.i('Bloc Category $i: ${collateralCategories[i].toJson()}');
      }
      
      // Update internal state và emit composite state
      _currentState = _currentState.copyWith(collateralCategories: collateralCategories);
      await _logger.i('Current state collateral categories: ${_currentState.collateralCategories.length} items');
      emit(_currentState);
      await _logger.i('=== END: Loading collateral categories in bloc ===');

    } catch (e) {
      await _logger.i('Error loading collateral categories: $e');
      emit(MasterDataError(
        'Không thể tải danh sách loại tài sản đảm bảo: $e',
        type: 'collateral_categories',
      ));
    }
  }

  /// Refresh master data
  Future<void> _onRefreshMasterData(
    RefreshMasterDataEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      await _logger.i('Refreshing master data');
      
      // Clear cache
      _masterDataService.clearCache();
      
      // Reset internal state
      _currentState = const MasterDataLoaded();
      
      // Emit initial state để UI có thể reload
      emit(MasterDataInitial());
      
    } catch (e) {
      await _logger.i('Error refreshing master data: $e');
      emit(MasterDataError('Không thể làm mới dữ liệu: $e'));
    }
  }

  /// Clear master data
  Future<void> _onClearMasterData(
    ClearMasterDataEvent event,
    Emitter<MasterDataState> emit,
  ) async {
    try {
      await _logger.i('Clearing master data');
      _masterDataService.clearCache();
      
      // Reset internal state
      _currentState = const MasterDataLoaded();
      emit(MasterDataInitial());
    } catch (e) {
      await _logger.i('Error clearing master data: $e');
    }
  }

  @override
  Future<void> close() {
    _logger.i('MasterDataBloc disposed');
    return super.close();
  }
} 