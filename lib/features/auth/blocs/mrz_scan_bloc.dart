import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../services/mrz_scanner_service.dart';
import '../../../shared/models/cccd_extraction_model.dart';

// Events
abstract class MrzScanEvent extends Equatable {
  const MrzScanEvent();

  @override
  List<Object?> get props => [];
}

class ScanMrzFromImageEvent extends MrzScanEvent {
  final String imagePath;
  final CccdExtractionModel? cccdExtractionResult;

  const ScanMrzFromImageEvent({
    required this.imagePath,
    this.cccdExtractionResult,
  });

  @override
  List<Object?> get props => [imagePath, cccdExtractionResult];
}

class RescanMrzEvent extends MrzScanEvent {
  final String imagePath;
  final CccdExtractionModel? cccdExtractionResult;

  const RescanMrzEvent({
    required this.imagePath,
    this.cccdExtractionResult,
  });

  @override
  List<Object?> get props => [imagePath, cccdExtractionResult];
}

class ResetMrzScanEvent extends MrzScanEvent {
  const ResetMrzScanEvent();
}

// States
abstract class MrzScanState extends Equatable {
  const MrzScanState();

  @override
  List<Object?> get props => [];
}

class MrzScanInitial extends MrzScanState {
  const MrzScanInitial();
}

class MrzScanLoading extends MrzScanState {
  const MrzScanLoading();
}

class MrzScanSuccess extends MrzScanState {
  final MrzScanResult result;
  final bool usedFallback;

  const MrzScanSuccess({
    required this.result,
    this.usedFallback = false,
  });

  @override
  List<Object?> get props => [result, usedFallback];
}

class MrzScanError extends MrzScanState {
  final String message;

  const MrzScanError(this.message);

  @override
  List<Object?> get props => [message];
}

// Bloc
class MrzScanBloc extends Bloc<MrzScanEvent, MrzScanState> {
  final MrzScannerService _mrzScannerService;

  MrzScanBloc({
    required MrzScannerService mrzScannerService,
  })  : _mrzScannerService = mrzScannerService,
        super(const MrzScanInitial()) {
    on<ScanMrzFromImageEvent>(_onScanMrzFromImage);
    on<RescanMrzEvent>(_onRescanMrz);
    on<ResetMrzScanEvent>(_onResetMrzScan);
  }

  Future<void> _onScanMrzFromImage(
    ScanMrzFromImageEvent event,
    Emitter<MrzScanState> emit,
  ) async {
    try {
      emit(const MrzScanLoading());

      // Thử scan MRZ từ ảnh trước
      MrzScanResult? scanResult;
      try {
        scanResult = await _mrzScannerService.scanMrzFromImage(event.imagePath);
      } catch (e) {
        // Scan từ ảnh thất bại, tiếp tục với fallback
      }

      // Kiểm tra kết quả scan từ ảnh
      if (scanResult != null && scanResult.hasRawLines) {
        // Nếu scan được dữ liệu và parser thành công
        if (scanResult.isSuccess) {
          emit(MrzScanSuccess(result: scanResult, usedFallback: false));
          return;
        }
        // Nếu scan được dữ liệu nhưng parser thất bại, tiếp tục với fallback
      }

      // Fallback: Sử dụng MRZ lines từ CCCD extraction result
      if (event.cccdExtractionResult != null && 
          event.cccdExtractionResult!.mrzLines.isNotEmpty) {
        
        // Thử parser lại với dữ liệu từ CCCD extraction
        try {
          final parsedData = _mrzScannerService.extractAndParseMrz(
            event.cccdExtractionResult!.mrzLines.join('\n')
          );
          
          if (parsedData.isSuccess) {
            // Parser thành công với dữ liệu fallback
            final successResult = MrzScanResult(
              rawLines: event.cccdExtractionResult!.mrzLines,
              parsedData: parsedData.parsedData,
            );
            emit(MrzScanSuccess(result: successResult, usedFallback: true));
            return;
          }
        } catch (e) {
          // Parser thất bại với dữ liệu fallback
        }

        // Parser thất bại với dữ liệu fallback, nhưng vẫn hiển thị raw lines
        // vì đây đã là fallback rồi
        final fallbackResult = MrzScanResult(
          rawLines: event.cccdExtractionResult!.mrzLines,
          parsedData: null,
        );
        emit(MrzScanSuccess(result: fallbackResult, usedFallback: true));
        return;
      }

      // Không có dữ liệu fallback và parser từ ảnh thất bại
      // Emit error để không hiển thị raw lines từ ảnh
      emit(const MrzScanError('Không thể đọc được mã MRZ từ ảnh hoặc dữ liệu CCCD'));
      
    } catch (e) {
      emit(MrzScanError('Có lỗi xảy ra khi quét MRZ: $e'));
    }
  }

  Future<void> _onRescanMrz(
    RescanMrzEvent event,
    Emitter<MrzScanState> emit,
  ) async {
    try {
      emit(const MrzScanLoading());

      // Thử scan MRZ từ ảnh trước
      MrzScanResult? scanResult;
      try {
        scanResult = await _mrzScannerService.scanMrzFromImage(event.imagePath);
      } catch (e) {
        // Scan từ ảnh thất bại, tiếp tục với fallback
      }

      // Kiểm tra kết quả scan từ ảnh
      if (scanResult != null && scanResult.hasRawLines) {
        // Nếu scan được dữ liệu và parser thành công
        if (scanResult.isSuccess) {
          emit(MrzScanSuccess(result: scanResult, usedFallback: false));
          return;
        }
        // Nếu scan được dữ liệu nhưng parser thất bại, tiếp tục với fallback
      }

      // Fallback: Sử dụng MRZ lines từ CCCD extraction result
      if (event.cccdExtractionResult != null && 
          event.cccdExtractionResult!.mrzLines.isNotEmpty) {
        
        // Thử parser lại với dữ liệu từ CCCD extraction
        try {
          final parsedData = _mrzScannerService.extractAndParseMrz(
            event.cccdExtractionResult!.mrzLines.join('\n')
          );
          
          if (parsedData.isSuccess) {
            // Parser thành công với dữ liệu fallback
            final successResult = MrzScanResult(
              rawLines: event.cccdExtractionResult!.mrzLines,
              parsedData: parsedData.parsedData,
            );
            emit(MrzScanSuccess(result: successResult, usedFallback: true));
            return;
          }
        } catch (e) {
          // Parser thất bại với dữ liệu fallback
        }

        // Parser thất bại với dữ liệu fallback, nhưng vẫn hiển thị raw lines
        // vì đây đã là fallback rồi
        final fallbackResult = MrzScanResult(
          rawLines: event.cccdExtractionResult!.mrzLines,
          parsedData: null,
        );
        emit(MrzScanSuccess(result: fallbackResult, usedFallback: true));
        return;
      }

      // Không có dữ liệu fallback và parser từ ảnh thất bại
      // Emit error để không hiển thị raw lines từ ảnh
      emit(const MrzScanError('Không thể đọc được mã MRZ từ ảnh hoặc dữ liệu CCCD'));
      
    } catch (e) {
      emit(MrzScanError('Có lỗi xảy ra khi quét lại MRZ: $e'));
    }
  }

  void _onResetMrzScan(
    ResetMrzScanEvent event,
    Emitter<MrzScanState> emit,
  ) {
    emit(const MrzScanInitial());
  }
} 