import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import '../models/registration_model.dart';
import '../models/registration_response_model.dart';
import '../models/check_registration_response_model.dart';
import '../../../shared/services/api/api_service.dart';

/// Service để xử lý đăng ký user mới
class RegistrationService {
  static final RegistrationService _instance = RegistrationService._internal();
  factory RegistrationService() => _instance;
  RegistrationService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // API endpoints
  static const String _insertRegisterEndpoint = '/rest/rpc/insert_register';
  static const String _checkRegisterEndpoint =
      '/rest/rpc/check_register_by_id_card';

  // Constants theo PostgreSQL function
  static const List<String> _validRegisterTypes = [
    'COLLABORATOR',
    'BANK_OFFICER',
  ];
  static const List<String> _validIdCardTypes = ['CHIP_ID', 'PASSPORT'];
  static const List<String> _validDataSourceTypes = [
    'APP_SALE',
    'WEB_SALE',
    'KPLUS',
  ];

  /// Đăng ký user mới
  Future<RegistrationResponseModel> registerUser(
    RegistrationModel registration,
  ) async {
    try {
      _logger.i('Registering new user: ${registration.fullName}');

      // Validate registration data trước khi gửi
      final validationErrors = getValidationErrors(registration);
      if (validationErrors.isNotEmpty) {
        throw RegistrationException(
          message: 'Dữ liệu không hợp lệ: ${validationErrors.join(', ')}',
          type: RegistrationExceptionType.validationError,
        );
      }

      // Tạo data theo thứ tự parameters của PostgreSQL function
      final requestData = <String, dynamic>{
        'p_full_name': registration.fullName,
        'p_id_card_type': registration.idCardType,
        'p_id_card_no': registration.idCardNo,
        'p_issue_date': registration.issueDate,
        'p_issue_place': registration.issuePlace,
        'p_permanent_address': registration.permanentAddress,
        'p_phone_number': registration.phoneNumber,
        'p_email': registration.email,
        'p_register_type': registration.registerType,
        'p_province_id': registration.provinceId,
        'p_branch_id': registration.branchId,
      };

      // Chỉ thêm optional parameters nếu không null
      if (registration.expiryDate != null &&
          registration.expiryDate!.isNotEmpty) {
        requestData['p_expiry_date'] = registration.expiryDate;
      }
      if (registration.positionId != null &&
          registration.positionId!.isNotEmpty) {
        requestData['p_position_id'] = registration.positionId;
      }
      if (registration.referrerCode != null &&
          registration.referrerCode!.isNotEmpty) {
        requestData['p_referrer_code'] = registration.referrerCode;
      }
      if (registration.frontCardDocumentId != null &&
          registration.frontCardDocumentId!.isNotEmpty) {
        requestData['p_front_card_document_id'] =
            registration.frontCardDocumentId;
      }
      if (registration.backCardDocumentId != null &&
          registration.backCardDocumentId!.isNotEmpty) {
        requestData['p_back_card_document_id'] =
            registration.backCardDocumentId;
      }
      if (registration.dataSource.isNotEmpty) {
        requestData['p_data_source'] = registration.dataSource;
      }
      if (registration.metadata != null) {
        requestData['p_metadata'] = registration.metadata;
      }

      _logger.d('Registration request data: $requestData');

      final response = await _apiService.post(
        _insertRegisterEndpoint,
        data: requestData,
      );

      // Response từ PostgreSQL function có format: {success, message, code, data}
      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;

        _logger.i('Raw response data: $responseData');

        if (responseData['success'] == true) {
          final registrationData = responseData['data'] as Map<String, dynamic>;
          _logger.i('Registration data from response: $registrationData');
          final registrationResponse = RegistrationResponseModel.fromJson(
            registrationData,
          );

          // Log action information
          final action = registrationResponse.action;
          if (action != null) {
            _logger.i(
              'User registration ${action.toLowerCase()}: ${registrationResponse.registerId}',
            );
          } else {
            _logger.i(
              'User registered successfully: ${registrationResponse.registerId}',
            );
          }

          return registrationResponse;
        } else {
          // Handle specific error codes từ PostgreSQL function
          final errorCode = responseData['code'] as String?;
          final errorMessage = responseData['error'] as String?;

          RegistrationExceptionType exceptionType;
          switch (errorCode) {
            case 'ID_CARD_ALREADY_EXISTS':
              exceptionType = RegistrationExceptionType.idCardAlreadyExists;
              break;
            case 'PROVINCE_NOT_FOUND':
            case 'BRANCH_NOT_FOUND':
            case 'POSITION_NOT_FOUND':
            case 'FRONT_DOCUMENT_NOT_FOUND':
            case 'BACK_DOCUMENT_NOT_FOUND':
              exceptionType = RegistrationExceptionType.notFound;
              break;
            case 'VALIDATION_ERROR':
              exceptionType = RegistrationExceptionType.validationError;
              break;
            default:
              exceptionType = RegistrationExceptionType.apiError;
          }

          throw RegistrationException(
            message: errorMessage ?? 'Unknown error',
            code: errorCode,
            type: exceptionType,
          );
        }
      } else {
        throw RegistrationException(
          message: 'Invalid response format for registration',
          type: RegistrationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when registering user: ${e.message}');
      throw RegistrationException(
        message: 'Không thể đăng ký user: ${e.message}',
        type: RegistrationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when registering user: $e');
      throw RegistrationException(
        message: 'Lỗi không xác định khi đăng ký user',
        type: RegistrationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Kiểm tra xem đã tồn tại bản ghi đăng ký hay chưa theo số CMND/CCCD
  Future<CheckRegistrationResponseModel> checkRegistrationByIdCard(
    String idCardNo,
  ) async {
    try {
      _logger.i('Checking registration for ID card: $idCardNo');

      // Validate ID card number format
      if (idCardNo.isEmpty) {
        throw RegistrationException(
          message: 'Số CMND/CCCD không được để trống',
          type: RegistrationExceptionType.validationError,
        );
      }

      if (!_isValidIdCardNumber(idCardNo)) {
        throw RegistrationException(
          message: 'Số CMND/CCCD không đúng định dạng (12 chữ số)',
          type: RegistrationExceptionType.validationError,
        );
      }

      final requestData = <String, dynamic>{'p_id_card_no': idCardNo};

      _logger.d('Check registration request data: $requestData');

      final response = await _apiService.post(
        _checkRegisterEndpoint,
        data: requestData,
      );

      // Response từ PostgreSQL function có format: {success, message, data}
      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;

        _logger.i('Raw check registration response: $responseData');

        if (responseData['success'] == true) {
          // Bản ghi tồn tại
          final checkResponse = CheckRegistrationResponseModel.fromJson(
            responseData,
          );
          _logger.i(
            'Registration found for ID card: $idCardNo, Status: ${checkResponse.status}',
          );
          return checkResponse;
        } else {
          // Bản ghi không tồn tại
          _logger.i('No registration found for ID card: $idCardNo');
          return CheckRegistrationResponseModel.notFound();
        }
      } else {
        throw RegistrationException(
          message: 'Invalid response format for check registration',
          type: RegistrationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when checking registration: ${e.message}');
      throw RegistrationException(
        message: 'Không thể kiểm tra đăng ký: ${e.message}',
        type: RegistrationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when checking registration: $e');
      throw RegistrationException(
        message: 'Lỗi không xác định khi kiểm tra đăng ký',
        type: RegistrationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Đăng ký collaborator
  Future<RegistrationResponseModel> registerCollaborator({
    required String fullName,
    required String idCardType,
    required String idCardNo,
    required String issueDate,
    required String issuePlace,
    required String permanentAddress,
    required String phoneNumber,
    required String email,
    required String provinceId,
    required String branchId,
    required String expiryDate,
    String? positionId,
    String? referrerCode,
    String? frontCardDocumentId,
    String? backCardDocumentId,
    Map<String, dynamic>? metadata,
  }) async {
    final registration = RegistrationModel(
      fullName: fullName,
      idCardType: idCardType,
      idCardNo: idCardNo,
      issueDate: issueDate,
      issuePlace: issuePlace,
      permanentAddress: permanentAddress,
      phoneNumber: phoneNumber,
      email: email,
      registerType: 'COLLABORATOR',
      provinceId: provinceId,
      branchId: branchId,
      expiryDate: expiryDate,
      positionId: positionId,
      referrerCode: referrerCode,
      frontCardDocumentId: frontCardDocumentId,
      backCardDocumentId: backCardDocumentId,
      dataSource: 'APP_SALE',
      metadata: metadata,
    );

    return await registerUser(registration);
  }

  /// Đăng ký bank officer
  Future<RegistrationResponseModel> registerBankOfficer({
    required String fullName,
    required String idCardType,
    required String idCardNo,
    required String issueDate,
    required String issuePlace,
    required String permanentAddress,
    required String phoneNumber,
    required String email,
    required String provinceId,
    required String branchId,
    required String expiryDate,
    String? positionId,
    String? referrerCode,
    String? frontCardDocumentId,
    String? backCardDocumentId,
    Map<String, dynamic>? metadata,
  }) async {
    final registration = RegistrationModel(
      fullName: fullName,
      idCardType: idCardType,
      idCardNo: idCardNo,
      issueDate: issueDate,
      issuePlace: issuePlace,
      permanentAddress: permanentAddress,
      phoneNumber: phoneNumber,
      email: email,
      registerType: 'BANK_OFFICER',
      provinceId: provinceId,
      branchId: branchId,
      expiryDate: expiryDate,
      positionId: positionId,
      referrerCode: referrerCode,
      frontCardDocumentId: frontCardDocumentId,
      backCardDocumentId: backCardDocumentId,
      dataSource: 'APP_SALE',
      metadata: metadata,
    );

    return await registerUser(registration);
  }

  /// Validate registration data theo rules của PostgreSQL function
  bool validateRegistrationData(RegistrationModel registration) {
    // Validate required fields
    if (registration.fullName.isEmpty) return false;
    if (registration.idCardNo.isEmpty) return false;
    if (registration.phoneNumber.isEmpty) return false;
    if (registration.email.isEmpty) return false;
    if (registration.provinceId.isEmpty) return false;
    if (registration.branchId.isEmpty) return false;
    if (registration.issueDate.isEmpty) return false;
    if (registration.issuePlace.isEmpty) return false;
    if (registration.permanentAddress.isEmpty) return false;

    // Validate register type
    if (!_validRegisterTypes.contains(registration.registerType)) return false;

    // Validate ID card type
    if (!_validIdCardTypes.contains(registration.idCardType)) return false;

    // Validate data source
    if (!_validDataSourceTypes.contains(registration.dataSource)) return false;

    // Validate email format
    if (!_isValidEmail(registration.email)) return false;

    // Validate phone number format (Vietnamese)
    if (!_isValidVietnamesePhone(registration.phoneNumber)) return false;

    // Validate ID card number format
    if (!_isValidIdCardNumber(registration.idCardNo)) return false;

    // Validate date format (YYYY-MM-DD)
    if (!_isValidDate(registration.issueDate)) return false;
    if (registration.expiryDate != null &&
        registration.expiryDate!.isNotEmpty) {
      if (!_isValidDate(registration.expiryDate!)) return false;
    }

    return true;
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// Validate Vietnamese phone number
  bool _isValidVietnamesePhone(String phone) {
    // Remove all non-digit characters first
    final digits = phone.replaceAll(RegExp(r'[^\d]'), '');

    _logger.d(
      'Phone validation - Original: "$phone", Digits only: "$digits", Length: ${digits.length}',
    );

    // Check if it's a valid Vietnamese phone number
    if (digits.startsWith('84') && digits.length == 11) {
      _logger.d('Phone validation - Valid +84 format');
      return true; // +84xxxxxxxxx format
    } else if (digits.startsWith('0') && digits.length == 11) {
      _logger.d('Phone validation - Valid 0xxxxxxxxxx format (11 digits)');
      return true; // 0xxxxxxxxxx format (11 digits including leading 0)
    } else if (digits.startsWith('0') && digits.length == 10) {
      _logger.d('Phone validation - Valid 0xxxxxxxxx format (10 digits)');
      return true; // 0xxxxxxxxx format (10 digits including leading 0)
    } else if (digits.length == 9) {
      _logger.d('Phone validation - Valid 9-digit format');
      return true; // xxxxxxxxx format (auto-add 0)
    } else if (digits.length == 10 && !digits.startsWith('0')) {
      _logger.d('Phone validation - Valid 10-digit format (no leading 0)');
      return true; // xxxxxxxxxx format (might be valid)
    }

    _logger.d(
      'Phone validation - Invalid format: digits="$digits", length=${digits.length}',
    );
    return false;
  }

  /// Validate ID card number format
  bool _isValidIdCardNumber(String idCardNo) {
    // Vietnamese ID card format: 12 digits
    final idCardRegex = RegExp(r'^[0-9]{12}$');
    return idCardRegex.hasMatch(idCardNo);
  }

  /// Validate date format (YYYY-MM-DD)
  bool _isValidDate(String date) {
    try {
      DateTime.parse(date);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get validation errors theo rules của PostgreSQL function
  List<String> getValidationErrors(RegistrationModel registration) {
    final errors = <String>[];

    if (registration.fullName.isEmpty) {
      errors.add('Họ tên không được để trống');
    }

    if (registration.idCardType.isEmpty) {
      errors.add('Loại giấy tờ không được để trống');
    } else if (!_validIdCardTypes.contains(registration.idCardType)) {
      errors.add('Loại giấy tờ phải là CHIP_ID hoặc PASSPORT');
    }

    if (registration.idCardNo.isEmpty) {
      errors.add('Số CMND/CCCD không được để trống');
    } else if (!_isValidIdCardNumber(registration.idCardNo)) {
      errors.add('Số CMND/CCCD không đúng định dạng (12 chữ số)');
    }

    if (registration.issueDate.isEmpty) {
      errors.add('Ngày cấp không được để trống');
    } else if (!_isValidDate(registration.issueDate)) {
      errors.add('Ngày cấp không đúng định dạng (YYYY-MM-DD)');
    }

    if (registration.issuePlace.isEmpty) {
      errors.add('Nơi cấp không được để trống');
    }

    if (registration.permanentAddress.isEmpty) {
      errors.add('Địa chỉ thường trú không được để trống');
    }

    if (registration.phoneNumber.isEmpty) {
      errors.add('Số điện thoại không được để trống');
    } else if (!_isValidVietnamesePhone(registration.phoneNumber)) {
      errors.add('Số điện thoại không đúng định dạng (cần 9-10 chữ số)');
    }

    if (registration.email.isEmpty) {
      errors.add('Email không được để trống');
    } else if (!_isValidEmail(registration.email)) {
      errors.add('Email không đúng định dạng');
    }

    if (registration.registerType.isEmpty) {
      errors.add('Loại đăng ký không được để trống');
    } else if (!_validRegisterTypes.contains(registration.registerType)) {
      errors.add('Loại đăng ký phải là COLLABORATOR hoặc BANK_OFFICER');
    }

    if (registration.provinceId.isEmpty) {
      errors.add('Tỉnh/thành phố không được để trống');
    }

    if (registration.branchId.isEmpty) {
      errors.add('Chi nhánh không được để trống');
    }

    if (registration.expiryDate != null &&
        registration.expiryDate!.isNotEmpty &&
        !_isValidDate(registration.expiryDate!)) {
      errors.add('Ngày hết hạn không đúng định dạng (YYYY-MM-DD)');
    }

    if (!_validDataSourceTypes.contains(registration.dataSource)) {
      errors.add('Nguồn dữ liệu không hợp lệ');
    }

    return errors;
  }

  /// Kiểm tra tính khả dụng của registration API
  Future<bool> checkRegistrationApiAvailability() async {
    try {
      // Thử tạo một registration test nhỏ
      final testRegistration = RegistrationModel(
        fullName: 'Test User',
        idCardType: 'CHIP_ID',
        idCardNo: '************',
        issueDate: '2020-01-01',
        issuePlace: 'Test Place',
        permanentAddress: 'Test Address',
        phoneNumber: '0123456789',
        email: '<EMAIL>',
        registerType: 'COLLABORATOR',
        provinceId: 'test-province-id',
        branchId: 'test-branch-id',
        expiryDate: '2025-01-01',
        dataSource: 'APP_SALE',
      );

      await registerUser(testRegistration);
      return true;
    } catch (e) {
      _logger.w('Registration API not available: $e');
      return false;
    }
  }

  /// Format phone number cho display
  String formatPhoneNumber(String phone) {
    if (phone.startsWith('+84')) {
      return '0${phone.substring(3)}';
    }
    return phone;
  }

  /// Format ID card number cho display
  String formatIdCardNumber(String idCardNo) {
    if (idCardNo.length == 12) {
      return '${idCardNo.substring(0, 3)} ${idCardNo.substring(3, 6)} ${idCardNo.substring(6, 9)} ${idCardNo.substring(9)}';
    }
    return idCardNo;
  }

  /// Get valid register types
  List<String> getValidRegisterTypes() =>
      List.unmodifiable(_validRegisterTypes);

  /// Get valid ID card types
  List<String> getValidIdCardTypes() => List.unmodifiable(_validIdCardTypes);

  /// Get valid data source types
  List<String> getValidDataSourceTypes() =>
      List.unmodifiable(_validDataSourceTypes);

  /// Kiểm tra xem registration đã được tạo mới hay cập nhật
  bool isRegistrationCreated(RegistrationResponseModel response) {
    return response.action == 'CREATED';
  }

  /// Kiểm tra xem registration đã được cập nhật
  bool isRegistrationUpdated(RegistrationResponseModel response) {
    return response.action == 'UPDATED';
  }

  /// Lấy thông tin action dưới dạng text tiếng Việt
  String getActionDescription(RegistrationResponseModel response) {
    switch (response.action) {
      case 'CREATED':
        return 'đã được tạo mới';
      case 'UPDATED':
        return 'đã được cập nhật';
      default:
        return 'đã được xử lý';
    }
  }

  /// Kiểm tra xem registration có tồn tại hay không
  bool isRegistrationExists(CheckRegistrationResponseModel response) {
    return response.exists;
  }

  /// Lấy trạng thái registration dưới dạng text tiếng Việt
  String getRegistrationStatusDescription(
    CheckRegistrationResponseModel response,
  ) {
    if (!response.exists) {
      return 'Chưa đăng ký';
    }

    switch (response.status?.toUpperCase()) {
      case 'PENDING':
        return 'Chờ xử lý';
      case 'APPROVED':
        return 'Đã duyệt';
      case 'REJECTED':
        return 'Đã từ chối';
      case 'ACTIVE':
        return 'Đang hoạt động';
      case 'INACTIVE':
        return 'Không hoạt động';
      default:
        return 'Không xác định';
    }
  }

  /// Kiểm tra xem registration có thể được cập nhật hay không
  bool canUpdateRegistration(CheckRegistrationResponseModel response) {
    if (!response.exists) {
      return false; // Không tồn tại thì không thể cập nhật
    }

    // Chỉ cho phép cập nhật khi status là PENDING hoặc REJECTED
    final status = response.status?.toUpperCase();
    return status == 'PENDING' || status == 'REJECTED';
  }

  /// Kiểm tra xem registration có thể được xóa hay không
  bool canDeleteRegistration(CheckRegistrationResponseModel response) {
    if (!response.exists) {
      return false; // Không tồn tại thì không thể xóa
    }

    // Chỉ cho phép xóa khi status là PENDING
    final status = response.status?.toUpperCase();
    return status == 'PENDING';
  }
}

/// Custom exception cho Registration service
class RegistrationException implements Exception {
  final String message;
  final RegistrationExceptionType type;
  final String? code;
  final Object? originalException;

  const RegistrationException({
    required this.message,
    required this.type,
    this.code,
    this.originalException,
  });

  @override
  String toString() =>
      'RegistrationException: $message (Type: $type${code != null ? ', Code: $code' : ''})';
}

/// Loại lỗi Registration
enum RegistrationExceptionType {
  notFound,
  apiError,
  invalidResponse,
  validationError,
  idCardAlreadyExists,
  unauthorized,
  unknown,
}
