import '../../../shared/utils/app_logger.dart';
import '../models/local_registration_data.dart';
import 'registration_storage_service.dart';
import 'registration_service.dart';

/// Service để kiểm tra trạng thái approval của registration
class RegistrationApprovalService {
  static final RegistrationApprovalService _instance =
      RegistrationApprovalService._internal();
  factory RegistrationApprovalService() => _instance;
  RegistrationApprovalService._internal();

  final AppLogger _logger = AppLogger();
  final RegistrationStorageService _storageService =
      RegistrationStorageService();
  final RegistrationService _registrationService = RegistrationService();

  /// Kiểm tra trạng thái approval theo ID card number
  Future<ApprovalStatusResult> checkApprovalStatusByIdCard(
    String idCardNo,
  ) async {
    try {
      _logger.i('Checking approval status for ID card: $idCardNo');

      // Sử dụng RegistrationService để kiểm tra trạng thái theo ID card
      final checkResponse = await _registrationService
          .checkRegistrationByIdCard(idCardNo);

      if (checkResponse.exists) {
        final approvalStatus = checkResponse.status ?? 'PENDING';
        final isApproved = approvalStatus == 'APPROVED';
        final approvedAt = checkResponse.createdAt;
        final registerId = checkResponse.id;

        // Cập nhật trạng thái trong local storage nếu có registration local
        final localRegistration = await _storageService
            .getLatestCompletedRegistration();
        if (localRegistration != null &&
            localRegistration.registerId == registerId &&
            registerId != null) {
          await _storageService.updateApprovalStatus(
            registerId: registerId,
            approvalStatus: approvalStatus,
            approvalMessage: _getApprovalMessage(approvalStatus),
            isApproved: isApproved,
          );
        }

        _logger.i('Approval status checked: $idCardNo -> $approvalStatus');

        return ApprovalStatusResult(
          registerId: registerId ?? '',
          idCardNo: idCardNo,
          status: approvalStatus,
          message: _getApprovalMessage(approvalStatus),
          isApproved: isApproved,
          approvedAt: approvedAt,
        );
      } else {
        // Registration không tồn tại trên server
        _logger.i('No registration found for ID card: $idCardNo');

        return ApprovalStatusResult(
          registerId: null,
          idCardNo: idCardNo,
          status: 'NOT_FOUND',
          message: 'Chưa có đăng ký cho số CMND/CCCD này',
          isApproved: false,
        );
      }
    } catch (e) {
      _logger.e('Error checking approval status: $e');

      return ApprovalStatusResult(
        registerId: null,
        idCardNo: idCardNo,
        status: 'ERROR',
        message: 'Không thể kiểm tra trạng thái',
        isApproved: false,
        error: e.toString(),
      );
    }
  }

  /// Kiểm tra trạng thái approval cho registration gần nhất trong local storage
  Future<ApprovalStatusResult?> checkLatestRegistrationStatus() async {
    try {
      _logger.i('checkLatestRegistrationStatus: Starting...');

      final latestRegistration = await _storageService
          .getLatestCompletedRegistration();

      if (latestRegistration == null) {
        _logger.d('No completed registration found in local storage');
        return null;
      }

      _logger.i('Found latest registration: ${latestRegistration.registerId}');
      _logger.i(
        'Registration data keys: ${latestRegistration.registrationData.keys.toList()}',
      );

      // Lấy ID card number từ registration data
      final idCardNo = _extractIdCardNumber(latestRegistration);
      _logger.i('Extracted ID card number: $idCardNo');

      if (idCardNo == null) {
        _logger.w('ID card number not found in registration data');
        _logger.w('NFC data: ${latestRegistration.nfcData}');
        _logger.w(
          'Registration data nfcData: ${latestRegistration.registrationData['nfcData']}',
        );
        return null;
      }

      // Kiểm tra xem có cần kiểm tra lại không
      _logger.i(
        'Needs approval check: ${latestRegistration.needsApprovalCheck}',
      );
      if (!latestRegistration.needsApprovalCheck) {
        _logger.d('No need to check approval status yet');
        return ApprovalStatusResult(
          registerId: latestRegistration.registerId,
          idCardNo: idCardNo,
          status: latestRegistration.approvalStatus ?? 'PENDING',
          message: latestRegistration.approvalMessage,
          isApproved: latestRegistration.isApproved ?? false,
          approvedAt: latestRegistration.approvedAt,
        );
      }

      // Kiểm tra trạng thái theo ID card number
      _logger.i('Calling checkApprovalStatusByIdCard with ID: $idCardNo');
      return await checkApprovalStatusByIdCard(idCardNo);
    } catch (e) {
      _logger.e('Error checking latest registration status: $e');
      return null;
    }
  }

  /// Lấy thông tin registration gần nhất từ local storage
  Future<LocalRegistrationData?> getLatestRegistration() async {
    try {
      return await _storageService.getLatestCompletedRegistration();
    } catch (e) {
      _logger.e('Error getting latest registration: $e');
      return null;
    }
  }

  /// Kiểm tra xem có registration nào đã hoàn thành trong local storage chưa
  Future<bool> hasCompletedRegistration() async {
    try {
      final latestRegistration = await _storageService
          .getLatestCompletedRegistration();
      return latestRegistration != null && latestRegistration.isCompleted;
    } catch (e) {
      _logger.e('Error checking completed registration: $e');
      return false;
    }
  }

  /// Kiểm tra trạng thái approval cho một ID card number cụ thể
  /// (có thể được gọi từ bên ngoài khi user nhập ID card number)
  Future<ApprovalStatusResult> checkStatusForIdCard(String idCardNo) async {
    try {
      _logger.i('Checking status for ID card: $idCardNo');
      return await checkApprovalStatusByIdCard(idCardNo);
    } catch (e) {
      _logger.e('Error checking status for ID card: $e');
      return ApprovalStatusResult(
        registerId: null,
        idCardNo: idCardNo,
        status: 'ERROR',
        message: 'Không thể kiểm tra trạng thái',
        isApproved: false,
        error: e.toString(),
      );
    }
  }

  /// Trích xuất ID card number từ registration data
  String? _extractIdCardNumber(LocalRegistrationData registration) {
    _logger.d('_extractIdCardNumber: Starting extraction...');
    
    // Thử lấy từ NFC data trước
    _logger.d('Checking registration.nfcData: ${registration.nfcData}');
    if (registration.nfcData != null &&
        registration.nfcData!['idNumber'] != null) {
      final idNumber = registration.nfcData!['idNumber'].toString();
      _logger.d('Found ID number from registration.nfcData: $idNumber');
      return idNumber;
    }

    // Thử lấy từ registration data
    _logger.d('Checking registration.registrationData[nfcData]');
    if (registration.registrationData['nfcData'] != null) {
      final nfcData =
          registration.registrationData['nfcData'] as Map<String, dynamic>?;
      _logger.d('NFC data from registration data: $nfcData');
      if (nfcData != null && nfcData['idNumber'] != null) {
        final idNumber = nfcData['idNumber'].toString();
        _logger.d('Found ID number from registration data: $idNumber');
        return idNumber;
      }
    }

    _logger.w('No ID card number found in registration data');
    return null;
  }

  /// Lấy message tương ứng với trạng thái approval
  String _getApprovalMessage(String status) {
    switch (status.toUpperCase()) {
      case 'APPROVED':
        return 'Đăng ký đã được phê duyệt thành công';
      case 'REJECTED':
        return 'Đăng ký đã bị từ chối';
      case 'PENDING':
        return 'Đăng ký đang chờ phê duyệt';
      case 'ACTIVE':
        return 'Tài khoản đang hoạt động';
      case 'INACTIVE':
        return 'Tài khoản đã bị vô hiệu hóa';
      case 'NOT_FOUND':
        return 'Chưa có đăng ký cho số CMND/CCCD này';
      default:
        return 'Trạng thái không xác định';
    }
  }
}

/// Model cho kết quả kiểm tra trạng thái approval
class ApprovalStatusResult {
  final String? registerId; // Có thể null nếu không tìm thấy
  final String idCardNo; // ID card number được kiểm tra
  final String status; // PENDING, APPROVED, REJECTED, ERROR, NOT_FOUND
  final String? message;
  final bool isApproved;
  final DateTime? approvedAt;
  final String? error;

  const ApprovalStatusResult({
    this.registerId,
    required this.idCardNo,
    required this.status,
    this.message,
    required this.isApproved,
    this.approvedAt,
    this.error,
  });

  /// Kiểm tra xem có lỗi không
  bool get hasError => error != null || status == 'ERROR';

  /// Kiểm tra xem có tìm thấy registration không
  bool get exists => status != 'NOT_FOUND' && !hasError;

  /// Lấy thông tin hiển thị cho user
  String get displayStatus {
    if (hasError) return 'Lỗi kiểm tra trạng thái';

    switch (status) {
      case 'APPROVED':
        return 'Đã được phê duyệt';
      case 'REJECTED':
        return 'Đã bị từ chối';
      case 'NOT_FOUND':
        return 'Chưa đăng ký';
      case 'PENDING':
      default:
        return 'Đang chờ phê duyệt';
    }
  }

  /// Lấy icon tương ứng với trạng thái
  String get statusIcon {
    switch (status) {
      case 'APPROVED':
        return '✅';
      case 'REJECTED':
        return '❌';
      case 'ERROR':
        return '⚠️';
      case 'NOT_FOUND':
        return '🔍';
      case 'PENDING':
      default:
        return '⏳';
    }
  }

  /// Lấy màu tương ứng với trạng thái
  String get statusColor {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      case 'ERROR':
        return 'warning';
      case 'NOT_FOUND':
        return 'neutral';
      case 'PENDING':
      default:
        return 'info';
    }
  }

  /// Lấy thông tin ngày tạo/duyệt để hiển thị
  String? get displayDate {
    if (approvedAt != null) {
      return '${approvedAt!.day}/${approvedAt!.month}/${approvedAt!.year}';
    }
    return null;
  }
}
