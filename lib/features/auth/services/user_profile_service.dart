import 'package:kiloba_biz/shared/services/api/api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/models/index.dart';
import '../models/user_profile.dart';
import '../../../shared/services/service_locator.dart';

/// Service để lấy thông tin user profile từ data API
class UserProfileService {
  static final UserProfileService _instance = UserProfileService._internal();
  factory UserProfileService() => _instance;
  UserProfileService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // Data API endpoint
  static const String _getCurrentUserProfileEndpoint =
      '/rest/rpc/get_current_user_profile';

  /// Lấy thông tin profile của user hiện tại từ data API
  Future<BaseResponse<UserProfile>> getCurrentUserProfile() async {
    try {
      _logger.i('Fetching current user profile from data API');

      // Gọi data API với authentication (JWT token sẽ được tự động thêm)
      final response = await _apiService.post(_getCurrentUserProfileEndpoint);

      _logger.i('API response received: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = response.data;

        if (responseData is Map<String, dynamic>) {
          // Sử dụng BaseResponse.fromJson với custom fromJsonT function
          return BaseResponse.fromJson(
            responseData,
            (data) => _parseUserProfileFromData(data),
          );
        } else {
          throw UserProfileException(
            message: 'Response data không đúng định dạng',
            type: UserProfileExceptionType.invalidResponse,
          );
        }
      } else {
        throw UserProfileException(
          message: 'HTTP error: ${response.statusCode}',
          type: UserProfileExceptionType.apiError,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching user profile: ${e.message}');
      throw UserProfileException(
        message: 'Không thể lấy thông tin profile: ${e.message}',
        type: UserProfileExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching user profile: $e');
      throw UserProfileException(
        message: 'Lỗi không xác định khi lấy thông tin profile',
        type: UserProfileExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Helper function để parse UserProfile từ API response data
  UserProfile _parseUserProfileFromData(Object? data) {
    if (data == null) {
      throw UserProfileException(
        message: 'Không có dữ liệu user profile',
        type: UserProfileExceptionType.notFound,
      );
    }

    try {
      // Xử lý case response là array (compatibility với code cũ)
      if (data is List && data.isNotEmpty) {
        final userData = data.first as Map<String, dynamic>;
        return UserProfile.fromJson(userData);
      }
      
      // Xử lý case response là single object
      if (data is Map<String, dynamic>) {
        return UserProfile.fromJson(data);
      }

      throw UserProfileException(
        message: 'Data không đúng định dạng',
        type: UserProfileExceptionType.invalidResponse,
      );
    } catch (e) {
      _logger.e('Error parsing user profile: $e');
      throw UserProfileException(
        message: 'Lỗi parse dữ liệu user profile: $e',
        type: UserProfileExceptionType.invalidResponse,
      );
    }
  }

  /// Kiểm tra tính khả dụng của data API
  Future<bool> checkDataApiAvailability() async {
    try {
      final response = await getCurrentUserProfile();
      return response.isSuccess;
    } catch (e) {
      _logger.w('Data API not available for user profile: $e');
      return false;
    }
  }

  /// Refresh thông tin user profile
  Future<UserProfile?> refreshUserProfile() async {
    try {
      final response = await getCurrentUserProfile();
      if (response.isSuccess && response.hasData) {
        return response.data;
      }
      return null;
    } catch (e) {
      _logger.w('Failed to refresh user profile: $e');
      return null;
    }
  }
}

/// Custom exception cho User Profile service
class UserProfileException implements Exception {
  final String message;
  final UserProfileExceptionType type;
  final Object? originalException;

  const UserProfileException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'UserProfileException: $message (Type: $type)';
}

/// Loại lỗi User Profile
enum UserProfileExceptionType { 
  notFound, 
  apiError, 
  invalidResponse, 
  unauthorized, 
  unknown 
}
