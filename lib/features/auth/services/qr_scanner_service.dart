import 'dart:io';
import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import '../../../shared/services/camera_permission_service.dart';
import '../../../shared/utils/app_logger.dart';

/// Enhanced QR Scanner Service với camera support và lifecycle management
/// Kết hợp camera real-time scanning và image-based scanning
class QrScannerService {
  static final QrScannerService _instance = QrScannerService._internal();
  factory QrScannerService() => _instance;
  QrScannerService._internal();

  final AppLogger _logger = AppLogger();
  final CameraPermissionService _permissionService = CameraPermissionService();
  final BarcodeScanner _barcodeScanner = BarcodeScanner(
    formats: [
      BarcodeFormat.qrCode,
      BarcodeFormat.code128,
      BarcodeFormat.code39,
      BarcodeFormat.ean13,
      BarcodeFormat.ean8,
      BarcodeFormat.aztec,
      BarcodeFormat.dataMatrix,
      BarcodeFormat.pdf417,
    ],
  );

  // Camera management
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;
  bool _isInitialized = false;
  bool _isScanning = false;
  
  // Throttling để tránh xử lý quá nhiều frames
  DateTime? _lastProcessTime;
  static const Duration _processingInterval = Duration(milliseconds: 200); // Giảm từ 500ms xuống 200ms

  /// Đọc QR code từ ảnh
  /// 
  /// [imagePath] - Đường dẫn đến file ảnh
  /// [qrRegion] - Vùng ảnh chứa QR code (tùy chọn)
  /// 
  /// Returns danh sách các QR code được tìm thấy
  Future<List<QrScanResult>> scanQrFromImage(
    String imagePath, {
    QrRegion? qrRegion,
  }) async {
    try {
      debugPrint('=== QR Scanner: Starting QR scan ===');
      debugPrint('Image path: $imagePath');

      // Đọc file ảnh
      final imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        debugPrint('ERROR: Image file does not exist');
        return [];
      }

      final imageBytes = await imageFile.readAsBytes();
      debugPrint('Image bytes size: ${imageBytes.length} bytes');

      // Decode ảnh
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        debugPrint('ERROR: Failed to decode image');
        return [];
      }

      debugPrint('Image size: ${image.width}x${image.height}');

      // Nếu có vùng QR được chỉ định, crop ảnh
      img.Image? croppedImage;
      if (qrRegion != null) {
        croppedImage = _cropImageToRegion(image, qrRegion);
        debugPrint('Cropped image size: ${croppedImage.width}x${croppedImage.height}');
      }

      // Scan QR code - nếu có crop thì save temp file, không thì dùng file gốc
      debugPrint('Scanning for QR codes...');
      String? tempFilePath;
      String imagePathToProcess;
      
      if (croppedImage != null) {
        // Save cropped image to temp file
        tempFilePath = await _saveTempCroppedImage(croppedImage);
        imagePathToProcess = tempFilePath;
      } else {
        // Dùng file gốc
        imagePathToProcess = imagePath;
      }

      final List<Barcode> barcodes = await _barcodeScanner.processImage(InputImage.fromFilePath(imagePathToProcess));

      debugPrint('Found ${barcodes.length} barcodes');

      // Lọc và chuyển đổi kết quả
      final qrResults = <QrScanResult>[];
      for (final barcode in barcodes) {
        if (barcode.rawValue != null) {
          qrResults.add(QrScanResult(
            value: barcode.rawValue!,
            format: barcode.format,
            boundingBox: barcode.boundingBox,
            cornerPoints: barcode.cornerPoints.map((point) => Offset(point.x.toDouble(), point.y.toDouble())).toList(),
          ));
        }
      }

      debugPrint('Found ${qrResults.length} QR codes');
      debugPrint('=== QR Scanner: Scan completed ===');

      // Cleanup temp file nếu có
      if (tempFilePath != null) {
        try {
          await File(tempFilePath).delete();
        } catch (e) {
          debugPrint('Warning: Failed to delete temp file: $e');
        }
      }

      return qrResults;
    } catch (e) {
      debugPrint('ERROR: QR scan error: $e');
      return [];
    }
  }

  /// Crop ảnh theo vùng được chỉ định
  img.Image _cropImageToRegion(img.Image image, QrRegion region) {
    final x = (region.left * image.width).toInt();
    final y = (region.top * image.height).toInt();
    final width = (region.width * image.width).toInt();
    final height = (region.height * image.height).toInt();

    debugPrint('Cropping region: x=$x, y=$y, width=$width, height=$height');

    return img.copyCrop(
      image,
      x: x,
      y: y,
      width: width,
      height: height,
    );
  }

  /// Save cropped image to temp file cho ML Kit
  Future<String> _saveTempCroppedImage(img.Image croppedImage) async {
    final tempDir = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final tempPath = '${tempDir.path}/qr_crop_temp_$timestamp.png';
    
    final pngBytes = img.encodePng(croppedImage);
    await File(tempPath).writeAsBytes(pngBytes);
    
    debugPrint('Temp cropped image saved: $tempPath');
    return tempPath;
  }

  /// Đọc QR code từ phần trên bên phải của ảnh CCCD mặt trước
  /// 
  /// [imagePath] - Đường dẫn đến ảnh CCCD mặt trước
  /// 
  /// Returns danh sách QR code được tìm thấy
  Future<List<QrScanResult>> scanQrFromCccdFront(String imagePath) async {
    // Danh sách các vùng để thử scan QR code
    final qrRegions = [
      // Vùng 1: Góc trên bên phải (vùng chính)
      const QrRegion(
        left: 0.70, // 70% từ trái sang phải
        top: 0.02,  // 2% từ trên xuống
        width: 0.30, // 30% chiều rộng
        height: 0.20, // 20% chiều cao
      ),
      // Vùng 2: Rộng hơn nếu vùng 1 không tìm thấy
      const QrRegion(
        left: 0.65, // 65% từ trái sang phải
        top: 0.0,   // 0% từ trên xuống
        width: 0.35, // 35% chiều rộng
        height: 0.25, // 25% chiều cao
      ),
      // Vùng 3: Thử toàn bộ nửa trên bên phải
      const QrRegion(
        left: 0.50, // 50% từ trái sang phải
        top: 0.0,   // 0% từ trên xuống
        width: 0.50, // 50% chiều rộng
        height: 0.30, // 30% chiều cao
      ),
    ];

         // Thử từng vùng cho đến khi tìm thấy QR code
     for (int i = 0; i < qrRegions.length; i++) {
       debugPrint('=== Trying QR region ${i + 1} ===');
       final results = await scanQrFromImage(imagePath, qrRegion: qrRegions[i]);
       
       if (results.isNotEmpty) {
         debugPrint('Found ${results.length} QR code(s) in region ${i + 1}');
         return results;
       }
       debugPrint('No QR code found in region ${i + 1}');
     }

     debugPrint('No QR code found in any region');
     return [];
  }

  /// Initialize camera system
  Future<QrScannerInitResult> initializeCamera() async {
    try {
      await _logger.d('Initializing camera system for QR scanning');

      // Kiểm tra camera permission
      final hasPermission = await _permissionService.canUseCamera();
      if (!hasPermission) {
        await _logger.w('Camera permission not granted');
        return QrScannerInitResult.permissionDenied;
      }

      // Lấy danh sách cameras
      _cameras = await availableCameras();
      if (_cameras == null || _cameras!.isEmpty) {
        await _logger.e('No cameras available');
        return QrScannerInitResult.noCameraAvailable;
      }

      await _logger.d('Found ${_cameras!.length} cameras');

      // Tìm camera sau (thường tốt hơn cho scanning)
      CameraDescription? backCamera;
      for (final camera in _cameras!) {
        if (camera.lensDirection == CameraLensDirection.back) {
          backCamera = camera;
          break;
        }
      }

      // Nếu không có camera sau, dùng camera đầu tiên
      final selectedCamera = backCamera ?? _cameras!.first;
      await _logger.d('Selected camera: ${selectedCamera.name}');

      // Khởi tạo camera controller
      final imageFormatGroup = Platform.isAndroid 
          ? ImageFormatGroup.nv21 
          : ImageFormatGroup.bgra8888;
      
      await _logger.d('Initializing camera with format: $imageFormatGroup');
      
      _cameraController = CameraController(
        selectedCamera,
        ResolutionPreset.high, // Tăng resolution để có chất lượng tốt hơn cho QR detection
        enableAudio: false,
        imageFormatGroup: imageFormatGroup,
      );

      await _cameraController!.initialize();
      _isInitialized = true;

      await _logger.i('Camera initialized successfully');
      return QrScannerInitResult.success;

    } catch (e) {
      await _logger.e('Error initializing camera: $e');
      return QrScannerInitResult.error;
    }
  }

  /// Dispose camera resources
  Future<void> disposeCamera() async {
    try {
      await _logger.d('Disposing camera resources');
      
      _isScanning = false;
      
      if (_cameraController != null) {
        await _cameraController!.dispose();
        _cameraController = null;
      }
      
      _isInitialized = false;
      await _logger.d('Camera resources disposed');
    } catch (e) {
      await _logger.e('Error disposing camera: $e');
    }
  }

  /// Pause camera (for lifecycle management)
  Future<void> pauseCamera() async {
    try {
      if (_cameraController != null && _cameraController!.value.isInitialized) {
        await _logger.d('Pausing camera');
        _isScanning = false;
        // Camera controller sẽ tự động pause khi app vào background
      }
    } catch (e) {
      await _logger.e('Error pausing camera: $e');
    }
  }

  /// Resume camera (for lifecycle management)
  Future<void> resumeCamera() async {
    try {
      if (_cameraController != null && _cameraController!.value.isInitialized) {
        await _logger.d('Resuming camera');
        // Camera controller sẽ tự động resume khi app về foreground
      }
    } catch (e) {
      await _logger.e('Error resuming camera: $e');
    }
  }

  /// Start continuous QR scanning from camera stream
  Future<void> startContinuousScanning({
    required Function(List<QrScanResult>) onQrDetected,
    Duration scanInterval = const Duration(milliseconds: 500),
  }) async {
    if (!_isInitialized || _cameraController == null) {
      await _logger.w('Camera not initialized for continuous scanning');
      return;
    }

    if (_isScanning) {
      await _logger.w('Scanning already in progress');
      return;
    }

    try {
      _isScanning = true;
      await _logger.d('Starting continuous QR scanning');
      debugPrint('Camera stream starting...');

      await _cameraController!.startImageStream((CameraImage image) async {
        debugPrint('Received camera image: ${image.width}x${image.height}');
        if (!_isScanning) return;

        // Throttling: Chỉ xử lý mỗi 200ms
        final now = DateTime.now();
        if (_lastProcessTime != null && 
            now.difference(_lastProcessTime!) < _processingInterval) {
          debugPrint('Skipping frame due to throttling');
          return;
        }
        _lastProcessTime = now;
        debugPrint('Processing frame at ${now.millisecondsSinceEpoch}');

        try {
          // Convert CameraImage to InputImage
          final inputImage = _convertCameraImageToInputImage(image);
          if (inputImage == null) {
            debugPrint('Failed to convert camera image to InputImage');
            return;
          }

          // Scan for barcodes
          final barcodes = await _barcodeScanner.processImage(inputImage);
          
          // Debug: Log số lượng barcodes tìm được
          if (barcodes.isNotEmpty) {
            debugPrint('Found ${barcodes.length} barcodes in image');
            for (final barcode in barcodes) {
              debugPrint('Barcode format: ${barcode.format}, value: ${barcode.rawValue}');
            }
          } else {
            debugPrint('No barcodes found in image');
          }
          
          // Thêm debug cho barcode scanner
          debugPrint('Barcode scanner processed image successfully');
          
          if (barcodes.isNotEmpty) {
            final results = barcodes
                .where((barcode) => barcode.rawValue != null)
                .map((barcode) => QrScanResult(
                      value: barcode.rawValue!,
                      format: barcode.format,
                      boundingBox: barcode.boundingBox,
                      cornerPoints: barcode.cornerPoints
                          .map((point) => Offset(point.x.toDouble(), point.y.toDouble()))
                          .toList(),
                    ))
                .toList();

            if (results.isNotEmpty) {
              await _logger.d('Found ${results.length} QR codes in camera stream');
              onQrDetected(results);
            }
          }
        } catch (e) {
          // Chỉ log error mỗi 2 giây để tránh spam
          if (_lastProcessTime == null || 
              now.difference(_lastProcessTime!) > const Duration(seconds: 2)) {
            debugPrint('Error processing camera image: $e');
          }
        }
      });

    } catch (e) {
      await _logger.e('Error starting continuous scanning: $e');
      _isScanning = false;
    }
  }

  /// Stop continuous scanning
  Future<void> stopContinuousScanning() async {
    try {
      if (_isScanning && _cameraController != null) {
        await _logger.d('Stopping continuous QR scanning');
        await _cameraController!.stopImageStream();
        _isScanning = false;
      }
    } catch (e) {
      await _logger.e('Error stopping continuous scanning: $e');
    }
  }

  /// Capture and scan single image from camera
  Future<List<QrScanResult>> captureAndScan() async {
    if (!_isInitialized || _cameraController == null) {
      await _logger.w('Camera not initialized for capture');
      return [];
    }

    try {
      await _logger.d('Capturing image for QR scanning');

      // Capture image
      final XFile imageFile = await _cameraController!.takePicture();
      debugPrint('Captured image path: ${imageFile.path}');
      
      // Scan QR codes from captured image
      final results = await scanQrFromImage(imageFile.path);
      debugPrint('Capture scan found ${results.length} results');
      
      // Clean up captured image
      try {
        await File(imageFile.path).delete();
      } catch (e) {
        await _logger.w('Failed to delete captured image: $e');
      }

      return results;
    } catch (e) {
      await _logger.e('Error capturing and scanning: $e');
      return [];
    }
  }

  /// Convert CameraImage to InputImage for ML Kit
  InputImage? _convertCameraImageToInputImage(CameraImage image) {
    try {
      // Lấy thông tin camera
      final camera = _cameraController?.description;
      if (camera == null) return null;

      // Xác định rotation dựa trên orientation và platform
      InputImageRotation rotation;
      if (Platform.isIOS) {
        rotation = InputImageRotationValue.fromRawValue(camera.sensorOrientation) ?? InputImageRotation.rotation0deg;
      } else {
        // Android: Xử lý rotation phức tạp hơn
        rotation = _getRotationCorrection(camera.sensorOrientation);
      }

      // Xác định format được hỗ trợ
      InputImageFormat? format;
      
      if (Platform.isAndroid) {
        // Android: Chỉ hỗ trợ NV21 và YV12
        if (image.format.group == ImageFormatGroup.nv21) {
          format = InputImageFormat.nv21;
        } else if (image.format.group == ImageFormatGroup.yuv420) {
          format = InputImageFormat.yuv420;
        } else {
          debugPrint('Unsupported Android image format: ${image.format.group}');
          debugPrint('Available formats: ${image.format.group}');
          return null;
        }
      } else {
        // iOS: Hỗ trợ BGRA8888
        if (image.format.group == ImageFormatGroup.bgra8888) {
          format = InputImageFormat.bgra8888;
        } else {
          debugPrint('Unsupported iOS image format: ${image.format.group}');
          return null;
        }
      }

      // format đã được xác định ở trên, không cần kiểm tra null

      // Debug: Log image info
      debugPrint('Image size: ${image.width}x${image.height}');
      debugPrint('Image format: ${image.format.group}');
      debugPrint('Planes count: ${image.planes.length}');
      debugPrint('Selected format: $format');
      debugPrint('Rotation: $rotation');
      
      // Tạo InputImage với format đã xác định
      final inputImage = InputImage.fromBytes(
        bytes: _concatenatePlanes(image.planes),
        metadata: InputImageMetadata(
          size: Size(image.width.toDouble(), image.height.toDouble()),
          rotation: rotation,
          format: format,
          bytesPerRow: image.planes[0].bytesPerRow,
        ),
      );

      debugPrint('InputImage created successfully');
      return inputImage;
    } catch (e) {
      debugPrint('Error converting camera image: $e');
      return null;
    }
  }

  /// Get rotation correction for Android
  InputImageRotation _getRotationCorrection(int sensorOrientation) {
    switch (sensorOrientation) {
      case 90:
        return InputImageRotation.rotation90deg;
      case 180:
        return InputImageRotation.rotation180deg;
      case 270:
        return InputImageRotation.rotation270deg;
      default:
        return InputImageRotation.rotation0deg;
    }
  }

  /// Concatenate image planes for ML Kit
  Uint8List _concatenatePlanes(List<Plane> planes) {
    final WriteBuffer allBytes = WriteBuffer();
    for (final Plane plane in planes) {
      allBytes.putUint8List(plane.bytes);
    }
    return allBytes.done().buffer.asUint8List();
  }

  /// Dispose resources
  Future<void> dispose() async {
    await stopContinuousScanning();
    await disposeCamera();
    _barcodeScanner.close();
  }

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isScanning => _isScanning;
  CameraController? get cameraController => _cameraController;
}

/// Kết quả scan QR code
class QrScanResult {
  final String value;
  final BarcodeFormat format;
  final Rect? boundingBox;
  final List<Offset>? cornerPoints;

  QrScanResult({
    required this.value,
    required this.format,
    this.boundingBox,
    this.cornerPoints,
  });

  @override
  String toString() {
    return 'QrScanResult(value: $value, format: $format)';
  }
}

/// Vùng ảnh để crop (tọa độ tương đối 0-1)
class QrRegion {
  final double left;
  final double top;
  final double width;
  final double height;

  const QrRegion({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });

  @override
  String toString() {
    return 'QrRegion(left: $left, top: $top, width: $width, height: $height)';
  }
}

/// Kết quả khởi tạo QR scanner
enum QrScannerInitResult {
  success,
  permissionDenied,
  noCameraAvailable,
  error,
}

/// Extension cho QrScannerInitResult
extension QrScannerInitResultExtension on QrScannerInitResult {
  String get message {
    switch (this) {
      case QrScannerInitResult.success:
        return 'Camera đã sẵn sàng để quét QR';
      case QrScannerInitResult.permissionDenied:
        return 'Cần quyền truy cập camera để quét QR';
      case QrScannerInitResult.noCameraAvailable:
        return 'Không tìm thấy camera trên thiết bị';
      case QrScannerInitResult.error:
        return 'Có lỗi xảy ra khi khởi tạo camera';
    }
  }

  bool get isSuccess => this == QrScannerInitResult.success;
  bool get isError => this != QrScannerInitResult.success;
} 