import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../shared/utils/app_logger.dart';
import '../models/local_registration_data.dart';
import '../constants/registration_storage_keys.dart';

/// Service để quản lý local storage cho registration data
class RegistrationStorageService {
  static final RegistrationStorageService _instance =
      RegistrationStorageService._internal();
  factory RegistrationStorageService() => _instance;
  RegistrationStorageService._internal();

  SharedPreferences? _prefs;
  final AppLogger _logger = AppLogger();

  /// Initialize SharedPreferences
  Future<void> _initPrefs() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Lưu dữ liệu registration hiện tại
  Future<void> saveCurrentRegistration({
    required Map<String, dynamic> registrationData,
    required int currentStep,
    String? sessionId,
  }) async {
    try {
      await _initPrefs();

      final localData = LocalRegistrationData(
        registrationData: registrationData,
        currentStep: currentStep,
        lastSavedAt: DateTime.now(),
        sessionId: sessionId ?? _generateSessionId(),
      );

      await _prefs!.setString(
        RegistrationStorageKeys.currentRegistration,
        jsonEncode(localData.toJson()),
      );

      _logger.i('Saved registration progress at step $currentStep');
    } catch (e) {
      _logger.e('Error saving registration progress: $e');
      rethrow;
    }
  }

  /// Lấy dữ liệu registration hiện tại
  Future<LocalRegistrationData?> getCurrentRegistration() async {
    try {
      await _initPrefs();

      final jsonString = _prefs!.getString(
        RegistrationStorageKeys.currentRegistration,
      );

      _logger.i(
        'Getting current registration: ${jsonString != null ? 'EXISTS' : 'NULL'}',
      );

      if (jsonString == null) return null;

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      final data = LocalRegistrationData.fromJson(json);

      _logger.i(
        'Current registration loaded: step ${data.currentStep}, completed: ${data.isCompleted}',
      );

      return data;
    } catch (e) {
      _logger.e('Error loading current registration: $e');
      return null;
    }
  }

  /// Lưu registration đã hoàn thành với server response
  Future<void> saveCompletedRegistrationWithServerData({
    required Map<String, dynamic> registrationData,
    required int currentStep,
    required String registerId,
    required Map<String, String> documentIds,
    required Map<String, dynamic> serverResponse,
    String? sessionId,
  }) async {
    try {
      await _initPrefs();

      final completedData = LocalRegistrationData(
        registrationData: registrationData,
        currentStep: currentStep,
        lastSavedAt: DateTime.now(),
        sessionId: sessionId ?? _generateSessionId(),
        isCompleted: true,
        registerId: registerId,
        completedAt: DateTime.now(),
        documentIds: documentIds,
        serverResponse: serverResponse,
        serverStatus: serverResponse['status'] ?? 'SUCCESS',
        serverMessage:
            serverResponse['message'] ?? 'Registration completed successfully',
        approvalStatus: 'PENDING', // Mặc định là pending
        lastCheckedAt: DateTime.now(),
      );

      // Lấy danh sách completed registrations
      final completedList = await getCompletedRegistrations();

      // Thêm registration mới
      completedList.add(completedData);

      // Lưu lại
      await _prefs!.setString(
        RegistrationStorageKeys.completedRegistrations,
        jsonEncode(completedList.map((r) => r.toJson()).toList()),
      );

      // KHÔNG xóa current registration để user có thể xem lại hoặc chỉnh sửa
      // await _prefs!.remove(RegistrationStorageKeys.currentRegistration);

      _logger.i('Saved completed registration with server data: $registerId');
    } catch (e) {
      _logger.e('Error saving completed registration: $e');
      rethrow;
    }
  }

  /// Lưu registration đã hoàn thành (legacy method)
  Future<void> saveCompletedRegistration(
    LocalRegistrationData registration,
  ) async {
    try {
      await _initPrefs();

      // Lấy danh sách completed registrations
      final completedList = await getCompletedRegistrations();

      // Thêm registration mới
      completedList.add(registration);

      // Lưu lại
      await _prefs!.setString(
        RegistrationStorageKeys.completedRegistrations,
        jsonEncode(completedList.map((r) => r.toJson()).toList()),
      );

      // KHÔNG xóa current registration để user có thể xem lại hoặc chỉnh sửa
      // await _prefs!.remove(RegistrationStorageKeys.currentRegistration);

      _logger.i('Saved completed registration: ${registration.registerId}');
    } catch (e) {
      _logger.e('Error saving completed registration: $e');
      rethrow;
    }
  }

  /// Lấy danh sách registrations đã hoàn thành
  Future<List<LocalRegistrationData>> getCompletedRegistrations() async {
    try {
      await _initPrefs();

      final jsonString = _prefs!.getString(
        RegistrationStorageKeys.completedRegistrations,
      );
      if (jsonString == null) return [];

      final jsonList = jsonDecode(jsonString) as List;
      return jsonList
          .map(
            (json) =>
                LocalRegistrationData.fromJson(json as Map<String, dynamic>),
          )
          .toList();
    } catch (e) {
      _logger.e('Error loading completed registrations: $e');
      return [];
    }
  }

  /// Lấy registration đã hoàn thành gần nhất
  Future<LocalRegistrationData?> getLatestCompletedRegistration() async {
    try {
      final completedList = await getCompletedRegistrations();
      if (completedList.isEmpty) return null;

      // Sắp xếp theo thời gian hoàn thành, lấy cái mới nhất
      completedList.sort(
        (a, b) => (b.completedAt ?? DateTime(1900)).compareTo(
          a.completedAt ?? DateTime(1900),
        ),
      );

      return completedList.first;
    } catch (e) {
      _logger.e('Error getting latest completed registration: $e');
      return null;
    }
  }

  /// Cập nhật trạng thái approval của registration
  Future<void> updateApprovalStatus({
    required String registerId,
    required String approvalStatus,
    String? approvalMessage,
    bool? isApproved,
  }) async {
    try {
      await _initPrefs();

      final completedList = await getCompletedRegistrations();
      final index = completedList.indexWhere((r) => r.registerId == registerId);

      if (index != -1) {
        final updatedRegistration = completedList[index].copyWith(
          approvalStatus: approvalStatus,
          approvalMessage: approvalMessage,
          isApproved: isApproved,
          approvedAt: isApproved == true ? DateTime.now() : null,
          lastCheckedAt: DateTime.now(),
        );

        completedList[index] = updatedRegistration;

        await _prefs!.setString(
          RegistrationStorageKeys.completedRegistrations,
          jsonEncode(completedList.map((r) => r.toJson()).toList()),
        );

        _logger.i(
          'Updated approval status for registration: $registerId -> $approvalStatus',
        );
      }
    } catch (e) {
      _logger.e('Error updating approval status: $e');
      rethrow;
    }
  }

  /// Xóa registration hiện tại
  Future<void> clearCurrentRegistration() async {
    try {
      await _initPrefs();
      _logger.i('About to clear current registration');
      await _prefs!.remove(RegistrationStorageKeys.currentRegistration);
      _logger.i('Cleared current registration successfully');
    } catch (e) {
      _logger.e('Error clearing current registration: $e');
    }
  }

  /// Xóa tất cả dữ liệu registration (khi user đăng nhập thành công)
  Future<void> clearAllRegistrationData() async {
    try {
      await _initPrefs();
      await _prefs!.remove(RegistrationStorageKeys.currentRegistration);
      await _prefs!.remove(RegistrationStorageKeys.completedRegistrations);
      await _prefs!.remove(RegistrationStorageKeys.uploadedFiles);
      _logger.i('Cleared all registration data');
    } catch (e) {
      _logger.e('Error clearing all registration data: $e');
    }
  }

  /// Xóa current registration và bắt đầu đăng ký mới
  Future<void> startNewRegistration() async {
    try {
      await _initPrefs();
      _logger.i('Starting new registration - clearing current data');
      await _prefs!.remove(RegistrationStorageKeys.currentRegistration);
      await _prefs!.remove(RegistrationStorageKeys.uploadedFiles);
      _logger.i('Started new registration successfully');
    } catch (e) {
      _logger.e('Error starting new registration: $e');
    }
  }

  /// Lưu uploaded files info
  Future<void> saveUploadedFiles(Map<String, String> filePaths) async {
    try {
      await _initPrefs();
      await _prefs!.setString(
        RegistrationStorageKeys.uploadedFiles,
        jsonEncode(filePaths),
      );
    } catch (e) {
      _logger.e('Error saving uploaded files: $e');
    }
  }

  /// Lấy uploaded files info
  Future<Map<String, String>> getUploadedFiles() async {
    try {
      await _initPrefs();

      final jsonString = _prefs!.getString(
        RegistrationStorageKeys.uploadedFiles,
      );
      if (jsonString == null) return {};

      final json = jsonDecode(jsonString) as Map<String, dynamic>;
      return Map<String, String>.from(json);
    } catch (e) {
      _logger.e('Error loading uploaded files: $e');
      return {};
    }
  }

  /// Lưu master data cache
  Future<void> saveMasterDataCache({
    required String key,
    required List<Map<String, dynamic>> data,
  }) async {
    try {
      await _initPrefs();
      await _prefs!.setString(key, jsonEncode(data));
      await _prefs!.setString(
        RegistrationStorageKeys.cacheTimestamp,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      _logger.e('Error saving master data cache: $e');
    }
  }

  /// Lấy master data cache
  Future<List<Map<String, dynamic>>> getMasterDataCache(String key) async {
    try {
      await _initPrefs();

      final jsonString = _prefs!.getString(key);
      if (jsonString == null) return [];

      final jsonList = jsonDecode(jsonString) as List;
      return jsonList.cast<Map<String, dynamic>>();
    } catch (e) {
      _logger.e('Error loading master data cache: $e');
      return [];
    }
  }

  /// Kiểm tra cache có còn valid không (24 giờ)
  Future<bool> isCacheValid() async {
    try {
      await _initPrefs();

      final timestampString = _prefs!.getString(
        RegistrationStorageKeys.cacheTimestamp,
      );
      if (timestampString == null) return false;

      final timestamp = DateTime.parse(timestampString);
      final now = DateTime.now();
      final difference = now.difference(timestamp);

      return difference.inHours < 24;
    } catch (e) {
      _logger.e('Error checking cache validity: $e');
      return false;
    }
  }

  /// Generate session ID
  String _generateSessionId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
