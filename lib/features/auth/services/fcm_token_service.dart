import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:uuid/uuid.dart';
import '../models/fcm_token_response.dart';
import '../../../shared/services/api/api_service.dart';
import '../../../../shared/services/firebase_service.dart';
import '../../../../shared/services/device_info_service.dart';

/// Service để quản lý FCM token thông qua PostgREST API với 2 functions mới
class FcmTokenService {
  static final FcmTokenService _instance = FcmTokenService._internal();
  factory FcmTokenService() => _instance;
  FcmTokenService._internal();

  // Use getter để đảm bảo lấy service singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();
  
  FirebaseService get _firebaseService => FirebaseService();
  DeviceInfoService get _deviceInfoService => DeviceInfoService();
  
  // PostgREST endpoints
  static const String _upsertTokenEndpoint = '/rest/rpc/upsert_fcm_token';
  static const String _deactivateTokenEndpoint =
      '/rest/rpc/deactivate_fcm_token';

  // Cached device info
  String? _cachedDeviceId;
  String? _cachedInstallationId;

  /// Upsert FCM token lên server (tạo mới hoặc cập nhật)
  Future<FcmTokenUpsertResult> upsertFcmToken({String? userId}) async {
    try {
      _logger.i('Upserting FCM token to server');

      // Get current FCM token from Firebase
      final fcmToken = await _firebaseService.getToken();
      if (fcmToken == null) {
        _logger.w('No FCM token available from Firebase');
        return FcmTokenUpsertResult.failure(
          'Không thể lấy FCM token từ Firebase',
        );
      }

      _logger.d('FCM token obtained: ${fcmToken.substring(0, 20)}...');

      // Prepare device information
      final request = await _buildUpsertRequest(fcmToken, userId);
      _logger.d('Request data: $request');

      // Call upsert function
      final response = await _apiService.post(
        _upsertTokenEndpoint,
        data: request.toJson(),
      );

      // Parse response (PostgREST function returns JSON object directly)
      if (response.data is Map<String, dynamic>) {
        final tokenResponse = FcmTokenUpsertResponse.fromJson(response.data);

        if (tokenResponse.success) {
          _logger.i(
            'FCM token upserted successfully: ${tokenResponse.message}',
          );
          _logger.d(
            'Token details: Action=${tokenResponse.action}, ID=${tokenResponse.tokenId}',
          );
          return FcmTokenUpsertResult.success(tokenResponse);
        } else {
          _logger.w('FCM token upsert failed: ${tokenResponse.message}');
          return FcmTokenUpsertResult.failure(tokenResponse.message);
        }
      } else {
        _logger.e(
          'Invalid response format from server. Expected JSON object, got: ${response.data.runtimeType}',
        );
        return FcmTokenUpsertResult.failure(
          'Định dạng phản hồi không hợp lệ từ server',
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error upserting FCM token: ${e.message}');
      return FcmTokenUpsertResult.failure('Lỗi API: ${e.message}');
    } catch (e) {
      _logger.e('Unknown error upserting FCM token: $e');
      return FcmTokenUpsertResult.failure(
        'Lỗi không xác định khi upsert FCM token',
      );
    }
  }

  /// Deactivate FCM token khi logout
  Future<FcmTokenDeactivateResult> deactivateFcmToken() async {
    try {
      _logger.i('Deactivating FCM token');

      // Get device ID
      final deviceId = await _getDeviceId();

      // Call deactivate function
      final response = await _apiService.post(
        _deactivateTokenEndpoint,
        data: {'p_device_id': deviceId},
      );

      // Parse response (PostgREST function returns JSON object directly)
      if (response.data is Map<String, dynamic>) {
        final tokenResponse = FcmTokenDeactivateResponse.fromJson(
          response.data,
        );

        if (tokenResponse.success) {
          _logger.i(
            'FCM token deactivated successfully: ${tokenResponse.message}',
          );
          return FcmTokenDeactivateResult.success(tokenResponse);
        } else {
          _logger.w('FCM token deactivation failed: ${tokenResponse.message}');
          return FcmTokenDeactivateResult.failure(tokenResponse.message);
        }
      } else {
        _logger.e(
          'Invalid response format from server. Expected JSON object, got: ${response.data.runtimeType}',
        );
        return FcmTokenDeactivateResult.failure(
          'Định dạng phản hồi không hợp lệ từ server',
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error deactivating FCM token: ${e.message}');
      return FcmTokenDeactivateResult.failure('Lỗi API: ${e.message}');
    } catch (e) {
      _logger.e('Unknown error deactivating FCM token: $e');
      return FcmTokenDeactivateResult.failure(
        'Lỗi không xác định khi deactivate FCM token',
      );
    }
  }

  /// Build upsert request với device information
  Future<FcmTokenUpsertRequest> _buildUpsertRequest(
    String fcmToken,
    String? userId,
  ) async {
    try {
      // Get device information
      final deviceId = await _getDeviceId();
      final platform = _getPlatform();
      final appVersion = await _getAppVersion();
      final osVersion = await _getOSVersion();
      final deviceModel = await _getDeviceModel();
      final deviceName = await _getDeviceName();
      final installationId = await _getInstallationId();

      return FcmTokenUpsertRequest(
        deviceId: deviceId,
        fcmToken: fcmToken,
        platform: platform,
        userId: userId,
        appVersion: appVersion,
        osVersion: osVersion,
        deviceModel: deviceModel,
        deviceName: deviceName,
        installationId: installationId,
      );
    } catch (e) {
      _logger.e('Error building upsert request: $e');
      rethrow;
    }
  }

  /// Get device ID từ DeviceInfoService (cached)
  Future<String> _getDeviceId() async {
    if (_cachedDeviceId != null) return _cachedDeviceId!;

    // Get device headers từ DeviceInfoService
    final headers = await _deviceInfoService.getDeviceHeaders();
    _cachedDeviceId =
        headers['X-Device-ID'] ??
        'unknown_device_${DateTime.now().millisecondsSinceEpoch}';
    return _cachedDeviceId!;
  }

  /// Get platform
  String _getPlatform() {
    if (Platform.isAndroid) return DevicePlatform.android.value;
    if (Platform.isIOS) return DevicePlatform.ios.value;
    if (kIsWeb) return DevicePlatform.web.value;
    return 'UNKNOWN';
  }

  /// Get app version
  Future<String?> _getAppVersion() async {
    final headers = await _deviceInfoService.getAppHeaders();
    return headers['X-App-Version'];
  }

  /// Get OS version
  Future<String?> _getOSVersion() async {
    final headers = await _deviceInfoService.getDeviceHeaders();
    return headers['X-Device-OS-Version'];
  }

  /// Get device model
  Future<String?> _getDeviceModel() async {
    final headers = await _deviceInfoService.getDeviceHeaders();
    return headers['X-Device-Model'];
  }

  /// Get device name (customizable)
  Future<String?> _getDeviceName() async {
    final headers = await _deviceInfoService.getDeviceHeaders();
    final model = headers['X-Device-Model'];
    final brand = headers['X-Device-Brand'];

    if (model != null && brand != null) {
      return '$brand $model';
    }
    return model ?? brand;
  }

  /// Get installation ID (UUID cho mỗi app install)
  Future<String> _getInstallationId() async {
    if (_cachedInstallationId != null) return _cachedInstallationId!;

    // Generate UUID for this installation
    _cachedInstallationId = const Uuid().v4();
    return _cachedInstallationId!;
  }

  /// Thiết lập listener cho token refresh tự động
  void setupTokenRefreshListener() {
    _firebaseService.onTokenRefresh = (String newToken) async {
      _logger.i('FCM token refreshed, updating to server automatically');
      final result = await upsertFcmToken();
      if (result.isSuccess) {
        _logger.i('Auto FCM token update successful');
      } else {
        _logger.w('Auto FCM token update failed: ${result.message}');
      }
    };

    _logger.i('FCM token refresh listener setup completed');
  }

  /// Cập nhật token sau khi đăng nhập thành công
  Future<void> updateTokenAfterLogin({required String userId}) async {
    try {
      // Wait một chút để JWT token được set trong ApiService
      await Future.delayed(const Duration(seconds: 1));

      _logger.i('Updating FCM token after successful login for user: $userId');
      final result = await upsertFcmToken(userId: userId);

      if (result.isSuccess) {
        _logger.i('Post-login FCM token update successful');
      } else {
        _logger.w('Post-login FCM token update failed: ${result.message}');
      }
    } catch (e) {
      _logger.e('Error updating FCM token after login: $e');
    }
  }

  /// Deactivate token sau khi đăng xuất
  Future<void> deactivateTokenAfterLogout() async {
    try {
      _logger.i('Deactivating FCM token after logout');
      final result = await deactivateFcmToken();

      if (result.isSuccess) {
        _logger.i('Post-logout FCM token deactivation successful');
      } else {
        _logger.w(
          'Post-logout FCM token deactivation failed: ${result.message}',
        );
      }
    } catch (e) {
      _logger.e('Error deactivating FCM token after logout: $e');
    }
  }

  /// Kiểm tra tính khả dụng của FCM token service
  Future<bool> checkServiceAvailability() async {
    try {
      final result = await upsertFcmToken();
      return result.isSuccess;
    } catch (e) {
      _logger.w('FCM token service not available: $e');
      return false;
    }
  }

  /// Force refresh FCM token và cập nhật lên server
  Future<FcmTokenUpsertResult> forceRefreshToken({String? userId}) async {
    try {
      _logger.i('Force refreshing FCM token');

      // Delete existing token to force Firebase to generate new one
      // Note: This will trigger onTokenRefresh callback automatically
      await _firebaseService.getToken();

      // Update with current/new token
      return await upsertFcmToken(userId: userId);
    } catch (e) {
      _logger.e('Error force refreshing FCM token: $e');
      return FcmTokenUpsertResult.failure('Lỗi khi force refresh FCM token');
    }
  }
}
