import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/constants/api_endpoints.dart';
import '../blocs/backend_config_bloc.dart';

/// Widget cho phép chọn backend URL trong development mode
class BackendUrlSelector extends StatefulWidget {
  const BackendUrlSelector({super.key});

  @override
  State<BackendUrlSelector> createState() => _BackendUrlSelectorState();
}

class _BackendUrlSelectorState extends State<BackendUrlSelector> {
  final _customUrlController = TextEditingController();
  String? _selectedUrl;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _loadSavedUrl();
  }

  @override
  void dispose() {
    _customUrlController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedUrl() async {
    // URL sẽ được load thông qua bloc
  }

  @override
  Widget build(BuildContext context) {
    // Chỉ hiển thị trong development mode
    if (kReleaseMode) return const SizedBox.shrink();

    return BlocProvider(
      create: (context) => BackendConfigBloc()..add(LoadSavedBackendUrl()),
      child: BlocBuilder<BackendConfigBloc, BackendConfigState>(
        builder: (context, state) {
          // Cập nhật selected URL từ state
          if (state.selectedUrl != null && _selectedUrl != state.selectedUrl) {
            _selectedUrl = state.selectedUrl;
            // Only update controller if user is not currently typing
            if (_customUrlController.text.isEmpty) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _customUrlController.text = state.selectedUrl!;
              });
            }
          }

          return Container(
            margin: EdgeInsets.only(top: AppDimensions.spacingL),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                // Header - Collapsible
                _buildHeader(context, state),

                // Content - Expandable
                if (_isExpanded) _buildContent(context, state),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader(BuildContext context, BackendConfigState state) {
    return InkWell(
      onTap: () => setState(() => _isExpanded = !_isExpanded),
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Row(
          children: [
            Icon(
              TablerIcons.settings,
              color: AppColors.kienlongOrange,
              size: 20,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: Text(
                'Backend Configuration (Dev Mode)',
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.kienlongOrange,
                ),
              ),
            ),
            _buildStatusIndicator(state),
            SizedBox(width: AppDimensions.spacingS),
            Icon(
              _isExpanded ? TablerIcons.chevron_up : TablerIcons.chevron_down,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(BackendConfigState state) {
    if (state is BackendConfigLoading) {
      return SizedBox(
        width: 12,
        height: 12,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.kienlongOrange),
        ),
      );
    }

    if (state is BackendConfigSuccess) {
      return Icon(TablerIcons.check, color: Colors.green, size: 16);
    }

    if (state is BackendConfigFailure) {
      return Icon(TablerIcons.alert_circle, color: Colors.red, size: 16);
    }

    return Icon(
      TablerIcons.help_circle,
      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
      size: 16,
    );
  }

  Widget _buildContent(BuildContext context, BackendConfigState state) {
    return Padding(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // URL Selection
          _buildUrlSelection(context, state),

          SizedBox(height: AppDimensions.spacingM),

          // Custom URL Input
          _buildCustomUrlInput(context, state),

          SizedBox(height: AppDimensions.spacingM),

          // Action Buttons
          _buildActionButtons(context, state),

          // Test Result Info
          if (state is BackendConfigSuccess &&
              state.lastTestResult != null) ...[
            SizedBox(height: AppDimensions.spacingM),
            _buildTestResultInfo(state),
          ],

          // Error Message
          if (state is BackendConfigFailure) ...[
            SizedBox(height: AppDimensions.spacingM),
            _buildErrorMessage(state),
          ],
        ],
      ),
    );
  }

  Widget _buildUrlSelection(BuildContext context, BackendConfigState state) {
    final availableUrls = [
      {'name': 'Development', 'url': ApiEndpoints.devBackendApiUrl},
      {'name': 'Local', 'url': ApiEndpoints.localBackendApiUrl},
      {'name': 'Staging', 'url': ApiEndpoints.stagingBackendApiUrl},
      {'name': 'Custom', 'url': ApiEndpoints.customBackendApiUrl},
    ];

    // Check if the current selected URL matches any predefined URL
    final matchingUrl = availableUrls.firstWhere(
      (url) => url['url'] == state.selectedUrl,
      orElse: () => {'name': 'Custom', 'url': ''},
    );

    // Only set dropdown value if it matches a predefined URL
    final dropdownValue = matchingUrl['url']!.isNotEmpty
        ? state.selectedUrl
        : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Backend URL:',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        DropdownButtonFormField<String>(
          value: dropdownValue,
          decoration: InputDecoration(
            hintText: dropdownValue == null
                ? 'Select URL or enter custom'
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
          ),
          items: availableUrls.map((url) {
            return DropdownMenuItem(
              value: url['url'],
              child: Tooltip(
                message: url['url'] as String,
                child: Text(
                  url['name'] as String,
                  style: AppTypography.textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedUrl = value;
                _customUrlController.text = value;
              });
              // Trigger bloc event để cập nhật state
              context.read<BackendConfigBloc>().add(SaveBackendUrl(value));
            }
          },
        ),
      ],
    );
  }

  Widget _buildCustomUrlInput(BuildContext context, BackendConfigState state) {
    // Only update controller if it's empty and state has a URL
    if (_customUrlController.text.isEmpty && state.selectedUrl != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _customUrlController.text = state.selectedUrl!;
      });
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom URL:',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          controller: _customUrlController,
          decoration: InputDecoration(
            hintText: 'Enter custom backend URL...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            suffixIcon: IconButton(
              icon: Icon(TablerIcons.refresh),
              onPressed: () {
                setState(() {
                  _selectedUrl = _customUrlController.text;
                });
                // Trigger bloc event to update state
                if (_customUrlController.text.trim().isNotEmpty) {
                  context.read<BackendConfigBloc>().add(
                    SaveBackendUrl(_customUrlController.text),
                  );
                }
              },
            ),
          ),
          onChanged: (value) {
            setState(() {
              _selectedUrl = value;
            });
            // Don't trigger bloc event immediately on every keystroke
            // Let user finish typing first
          },
          onEditingComplete: () {
            // Trigger bloc event when user finishes editing
            if (_customUrlController.text.trim().isNotEmpty) {
              context.read<BackendConfigBloc>().add(
                SaveBackendUrl(_customUrlController.text),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, BackendConfigState state) {
    final isLoading = state is BackendConfigLoading;
    // Prioritize URL from text box over state
    final currentUrl = _customUrlController.text.trim().isNotEmpty
        ? _customUrlController.text.trim()
        : (state.selectedUrl ?? _selectedUrl);
    final hasValidUrl = currentUrl?.trim().isNotEmpty == true;

    return Column(
      children: [
        // Test Connection Button - Full width
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: isLoading || !hasValidUrl
                ? null
                : () {
                    if (currentUrl != null) {
                      // Update local state first
                      setState(() {
                        _selectedUrl = currentUrl;
                      });
                      // Then test connection
                      context.read<BackendConfigBloc>().add(
                        TestBackendConnection(currentUrl),
                      );
                    }
                  },
            icon: Icon(TablerIcons.plug),
            label: Text('Test Connection'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongSkyBlue,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        SizedBox(height: AppDimensions.spacingM),

        // Save and Reset Buttons - Row
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: isLoading || !hasValidUrl
                    ? null
                    : () {
                        if (currentUrl != null) {
                          // Update local state first
                          setState(() {
                            _selectedUrl = currentUrl;
                          });
                          // Then save
                          context.read<BackendConfigBloc>().add(
                            SaveBackendUrl(currentUrl),
                          );
                        }
                      },
                icon: Icon(TablerIcons.device_floppy),
                label: Text('Save'),
              ),
            ),
            SizedBox(width: AppDimensions.spacingS),
            OutlinedButton.icon(
              onPressed: isLoading
                  ? null
                  : () {
                      context.read<BackendConfigBloc>().add(ResetBackendUrl());
                      _customUrlController.clear();
                      setState(() {
                        _selectedUrl = null;
                      });
                    },
              icon: Icon(TablerIcons.refresh),
              label: Text('Reset'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTestResultInfo(BackendConfigSuccess state) {
    final result = state.lastTestResult!;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(TablerIcons.check, color: Colors.green, size: 16),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Connection Successful',
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingS),
          if (result.database != null)
            Text(
              'Database: ${result.database}',
              style: AppTypography.textTheme.bodySmall,
            ),
          if (result.schema != null)
            Text(
              'Schema: ${result.schema}',
              style: AppTypography.textTheme.bodySmall,
            ),
          if (result.timestamp != null)
            Text(
              'Timestamp: ${result.timestamp}',
              style: AppTypography.textTheme.bodySmall,
            ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage(BackendConfigFailure state) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(TablerIcons.alert_circle, color: Colors.red, size: 16),
          SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Text(
              state.error,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
