import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'dart:io';
import 'dart:ui' as ui;

import '../../../main.dart';
import '../../../core/theme/index.dart';
import '../../../shared/utils/index.dart';
import '../../../shared/widgets/index.dart';

class CccdCameraScreen extends StatefulWidget {
  final String photoType; // 'front' or 'back'
  final Function(String imagePath) onPhotoTaken;

  const CccdCameraScreen({
    super.key,
    required this.photoType,
    required this.onPhotoTaken,
  });

  @override
  State<CccdCameraScreen> createState() => _CccdCameraScreenState();
}

class _CccdCameraScreenState extends State<CccdCameraScreen>
    with WidgetsBindingObserver {
  CameraController? _cameraController;
  bool _isCameraInitialized = false;
  bool _isPermissionGranted = false;
  bool _isLoading = true;
  String? _errorMessage;
  bool _isCapturing = false;
  String? _capturedImagePath;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    final CameraController? cameraController = _cameraController;

    // App state changed before we got the chance to initialize.
    if (cameraController == null || !cameraController.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      cameraController.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _onNewCameraSelected(cameraController.description);
    }
  }

  Future<void> _onNewCameraSelected(CameraDescription cameraDescription) async {
    if (_cameraController != null) {
      await _cameraController!.dispose();
    }

    final CameraController cameraController = CameraController(
      cameraDescription,
      ResolutionPreset.veryHigh, // Use highest resolution
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    _cameraController = cameraController;

    try {
      await cameraController.initialize();

      // Set auto focus for sharp images
      await cameraController.setFocusMode(FocusMode.auto);
      await cameraController.setExposureMode(ExposureMode.auto);

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isCameraInitialized = false;
          _errorMessage = 'Lỗi khởi tạo camera: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _initializeCamera() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Check if cameras are available
      if (cameras.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Không tìm thấy camera trên thiết bị';
        });
        return;
      }

      // Use the highest resolution back camera (main camera)
      final backCamera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        backCamera,
        ResolutionPreset.veryHigh, // Use highest resolution
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.jpeg,
      );

      // Camera plugin will automatically request permission when initialize() is called
      await _cameraController!.initialize();

      // Set auto focus for sharp images
      await _cameraController!.setFocusMode(FocusMode.auto);
      await _cameraController!.setExposureMode(ExposureMode.auto);

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
          _isPermissionGranted = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      String errorMessage = 'Lỗi khởi tạo camera: ${e.toString()}';

      if (e is CameraException) {
        switch (e.code) {
          case 'CameraAccessDenied':
            errorMessage =
                'Quyền truy cập camera bị từ chối. Vui lòng cấp quyền camera trong Cài đặt.';
            setState(() {
              _isPermissionGranted = false;
            });
            break;
          case 'CameraAccessDeniedWithoutPrompt':
            errorMessage =
                'Quyền camera bị từ chối vĩnh viễn. Vui lòng mở Cài đặt > Riêng tư & Bảo mật > Camera và bật quyền cho ứng dụng này.';
            setState(() {
              _isPermissionGranted = false;
            });
            break;
          case 'CameraAccessRestricted':
            errorMessage = 'Truy cập camera bị hạn chế bởi hệ thống';
            setState(() {
              _isPermissionGranted = false;
            });
            break;
          case 'AudioAccessDenied':
            errorMessage = 'Quyền truy cập microphone bị từ chối';
            break;
          default:
            errorMessage = 'Lỗi camera: ${e.description ?? e.code}';
            break;
        }
      }

      setState(() {
        _isLoading = false;
        _errorMessage = errorMessage;
      });
    }
  }

  Future<void> _capturePhoto() async {
    if (_cameraController == null ||
        !_cameraController!.value.isInitialized ||
        _isCapturing) {
      return;
    }

    setState(() {
      _isCapturing = true;
    });

    try {
      final XFile image = await _cameraController!.takePicture();
      
      // Hiển thị ảnh đã chụp thay vì camera live
      setState(() {
        _capturedImagePath = image.path;
        _isCapturing = false;
        _isProcessing = true;
      });

      // Xử lý crop ảnh trong background
      final croppedPaths = await _cropImageToOverlay(image.path);

      widget.onPhotoTaken(image.path);
      if (mounted) {
        Navigator.of(context).pop(croppedPaths);
      }
    } catch (e) {
      if (mounted) {
        debugPrint('Error: $e');
        CustomSnackBar.show(
          context,
          message: 'Lỗi chụp ảnh: ${e.toString()}',
          type: SnackBarType.error,
        );
        setState(() {
          _isCapturing = false;
          _isProcessing = false;
          _capturedImagePath = null;
        });
      }
    }
  }

  Future<void> _requestPermission() async {
    // Camera plugin will handle permission request automatically
    _initializeCamera();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        title: Text(
          widget.photoType == 'front'
              ? 'Chụp mặt trước CCCD'
              : 'Chụp mặt sau CCCD',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            if (mounted && Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
          },
          icon: Icon(
            TablerIcons.arrow_left,
            color: Colors.white,
            size: AppDimensions.iconM,
          ),
        ),
      ),
      body: _buildBody(isDarkMode),
    );
  }

  Widget _buildBody(bool isDarkMode) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (!_isPermissionGranted) {
      return _buildPermissionDeniedState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (!_isCameraInitialized || _cameraController == null) {
      return _buildLoadingState();
    }

    // Hiển thị ảnh đã chụp khi đang xử lý
    if (_isProcessing && _capturedImagePath != null) {
      return _buildProcessingState();
    }

    return Stack(
      children: [
        // Camera Preview with tap-to-focus - Only in body area
        Positioned.fill(
          child: GestureDetector(
            onTapUp: (TapUpDetails details) => _onTapToFocus(details),
            child: CameraPreview(_cameraController!),
          ),
        ),

        // Overlay with guide frame - Only in body area
        Positioned.fill(
          child: _buildOverlay(),
        ),

        // Instructions below overlay frame
        _buildInstructionsPositioned(),

        // Capture button at bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: _buildCaptureControls(),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              AppColors.kienlongSkyBlue,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),
          Text(
            'Đang khởi tạo camera...',
            style: AppTypography.textTheme.bodyLarge?.copyWith(
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionDeniedState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(TablerIcons.camera_off, size: 80, color: AppColors.error),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'Cần quyền truy cập camera',
              style: AppTypography.textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Để chụp ảnh CCCD, ứng dụng cần quyền truy cập camera của bạn.',
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingXL),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _requestPermission,
                icon: Icon(TablerIcons.camera),
                label: Text('Cấp quyền camera'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongSkyBlue,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.paddingM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(TablerIcons.alert_circle, size: 80, color: AppColors.error),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'Lỗi camera',
              style: AppTypography.textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              _errorMessage!,
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingXL),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _initializeCamera,
                icon: Icon(TablerIcons.refresh),
                label: Text('Thử lại'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongSkyBlue,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.paddingM,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessingState() {
    return Stack(
      children: [
        // Hiển thị ảnh đã chụp
        Positioned.fill(
          child: Image.file(
            File(_capturedImagePath!),
            fit: BoxFit.cover,
          ),
        ),

        // Overlay tối để text dễ đọc
        Positioned.fill(
          child: Container(
            color: Colors.black.withValues(alpha: 0.3),
          ),
        ),

        // Progress indicator và text
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppColors.kienlongSkyBlue,
                ),
                strokeWidth: 3,
              ),
              SizedBox(height: AppDimensions.spacingL),
              Text(
                'Đang xử lý ảnh...',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingM),
              Text(
                'Vui lòng chờ trong giây lát',
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),


      ],
    );
  }

  Widget _buildOverlay() {
    final screenSize = MediaQuery.of(context).size;
    final overlayWidth = screenSize.width - 40; // 20px margin on each side
    final overlayHeight =
        overlayWidth *
        0.63; // ID card ratio (86 x 54 mm = 1.59:1, so height = width / 1.59 ≈ 0.63)

    // Position the overlay at center of body area (correct coordinate system)
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;
    final appBarHeight = kToolbarHeight;
    
    // Calculate actual displayable height and body area
    final displayableHeight = screenSize.height - statusBarHeight - bottomSafeArea;
    final bodyHeight = displayableHeight - appBarHeight;
    
    final overlayLeft = (screenSize.width - overlayWidth) / 2;
    final overlayTop = (bodyHeight - overlayHeight) / 2 - appBarHeight;

    debugPrint('Overlay position (body area center-based):');
    debugPrint('  - Displayable height: ${displayableHeight}px');
    debugPrint('  - Body height: ${bodyHeight}px');
    debugPrint('  - Overlay size: ${overlayWidth}x$overlayHeight');
    debugPrint('  - Overlay left: $overlayLeft');
    debugPrint('  - Overlay top: $overlayTop');

    debugPrint('=== Debug Screen Info ===');
    debugPrint('Screen size: ${screenSize.width}x${screenSize.height}');
    debugPrint('MediaQuery padding: ${MediaQuery.of(context).padding}');
    debugPrint('MediaQuery viewInsets: ${MediaQuery.of(context).viewInsets}');
    debugPrint('MediaQuery viewPadding: ${MediaQuery.of(context).viewPadding}');
    debugPrint('AppBar height: $kToolbarHeight');
    debugPrint('Status bar height: ${MediaQuery.of(context).padding.top}');
    debugPrint('Bottom safe area: ${MediaQuery.of(context).padding.bottom}');

    return Stack(
      children: [
        // Custom overlay with transparent cutout
        Positioned.fill(
          child: CustomPaint(
            painter: OverlayPainter(
              cutoutRect: Rect.fromLTWH(overlayLeft, overlayTop, overlayWidth, overlayHeight),
            ),
          ),
        ),
        
        // Border frame for the scanning area
        Positioned(
          top: overlayTop,
          left: overlayLeft,
          child: Container(
            width: overlayWidth,
            height: overlayHeight,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.kienlongSkyBlue, width: 3),
            ),
            child: Stack(
              children: [
                // Corner guides
                ...List.generate(4, (index) {
                  return Positioned(
                    top: index < 2 ? 10 : null,
                    bottom: index >= 2 ? 10 : null,
                    left: index % 2 == 0 ? 10 : null,
                    right: index % 2 == 1 ? 10 : null,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        border: Border(
                          top: index < 2
                              ? BorderSide(
                                  color: AppColors.kienlongSkyBlue,
                                  width: 3,
                                )
                              : BorderSide.none,
                          bottom: index >= 2
                              ? BorderSide(
                                  color: AppColors.kienlongSkyBlue,
                                  width: 3,
                                )
                              : BorderSide.none,
                          left: index % 2 == 0
                              ? BorderSide(
                                  color: AppColors.kienlongSkyBlue,
                                  width: 3,
                                )
                              : BorderSide.none,
                          right: index % 2 == 1
                              ? BorderSide(
                                  color: AppColors.kienlongSkyBlue,
                                  width: 3,
                                )
                              : BorderSide.none,
                        ),
                      ),
                    ),
                  );
                }),

                // Center text
                Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                    child: Text(
                      widget.photoType == 'front'
                          ? 'MẶT TRƯỚC CCCD'
                          : 'MẶT SAU CCCD',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongSkyBlue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInstructions() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.info_circle,
                color: AppColors.kienlongSkyBlue,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  'Hướng dẫn chụp ảnh',
                  style: AppTypography.textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),
          _buildInstructionItem('Đặt CCCD vào khung hướng dẫn'),
          _buildInstructionItem('Đảm bảo ánh sáng đủ, tránh phản quang'),
          _buildInstructionItem('Giữ thiết bị ổn định khi chụp'),
          _buildInstructionItem('Thông tin trên CCCD phải rõ nét'),
        ],
      ),
    );
  }

  Widget _buildInstructionItem(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: EdgeInsets.only(top: 8),
            decoration: BoxDecoration(
              color: AppColors.kienlongSkyBlue,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Text(
              text,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCaptureControls() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: [
            Colors.black.withValues(alpha: 0.8),
            Colors.black.withValues(alpha: 0.4),
            Colors.transparent,
          ],
        ),
      ),
      child: SafeArea(
        top: false,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Flash toggle (if available)
            IconButton(
              onPressed: _toggleFlash,
              icon: Icon(
                _cameraController?.value.flashMode == FlashMode.torch
                    ? TablerIcons.bulb
                    : TablerIcons.bulb_off,
                color: Colors.white,
                size: AppDimensions.iconL,
              ),
            ),

            // Capture button
            GestureDetector(
              onTap: _isCapturing ? null : _capturePhoto,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isCapturing
                      ? AppColors.kienlongSkyBlue.withValues(alpha: 0.5)
                      : AppColors.kienlongSkyBlue,
                  border: Border.all(color: Colors.white, width: 4),
                ),
                child: _isCapturing
                    ? Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Icon(
                        TablerIcons.camera,
                        color: Colors.white,
                        size: AppDimensions.iconL,
                      ),
              ),
            ),

            // Gallery button (placeholder)
            IconButton(
              onPressed: () {
                // TODO: Implement gallery picker if needed
              },
              icon: Icon(
                TablerIcons.photo,
                color: Colors.white.withValues(alpha: 0.5),
                size: AppDimensions.iconL,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleFlash() async {
    if (_cameraController == null) return;

    try {
      final currentFlashMode = _cameraController!.value.flashMode;
      final newFlashMode = currentFlashMode == FlashMode.torch
          ? FlashMode.off
          : FlashMode.torch;

      await _cameraController!.setFlashMode(newFlashMode);
      setState(() {});
    } catch (e) {
      // Flash not supported or error
    }
  }

  Future<void> _onTapToFocus(TapUpDetails details) async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      final RenderBox renderBox = context.findRenderObject() as RenderBox;
      final Offset localPosition = renderBox.globalToLocal(
        details.globalPosition,
      );
      final double x = localPosition.dx / renderBox.size.width;
      final double y = localPosition.dy / renderBox.size.height;

      await _cameraController!.setFocusPoint(Offset(x, y));
      await _cameraController!.setExposurePoint(Offset(x, y));

      // Optionally show focus indicator
      // You can add a visual indicator here if needed
    } catch (e) {
      // Focus not supported or error
    }
  }



  Widget _buildInstructionsPositioned() {
    final screenSize = MediaQuery.of(context).size;
    final overlayWidth = screenSize.width - 40;
    final overlayHeight = overlayWidth * 0.63;
    
    // Use same coordinate system as _buildOverlay
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;
    final appBarHeight = kToolbarHeight;
    final displayableHeight = screenSize.height - statusBarHeight - bottomSafeArea;
    final bodyHeight = displayableHeight - appBarHeight;
    final overlayTop = (bodyHeight - overlayHeight) / 2 - appBarHeight;

    // Position instructions below the overlay frame with some spacing
    final instructionsTop = overlayTop + overlayHeight + 20;

    return Positioned(
      top: instructionsTop,
      left: 0,
      right: 0,
      child: _buildInstructions(),
    );
  }

  Future<Map<String, String>> _cropImageToOverlay(String imagePath) async {
    try {
      debugPrint('=== CCCD Camera: Starting image crop ===');
      debugPrint('Original image path: $imagePath');

      final screenSize = MediaQuery.of(context).size;
      debugPrint('Screen size: ${screenSize.width}x${screenSize.height}');

      // Calculate overlay position based on body area (correct coordinate system)
      final overlayWidth = screenSize.width - 40; // 20px margin on each side
      final overlayHeight = overlayWidth * 0.63; // ID card ratio
      
      // Use same coordinate system as _buildOverlay
      final statusBarHeight = MediaQuery.of(context).padding.top;
      final bottomSafeArea = MediaQuery.of(context).padding.bottom;
      final appBarHeight = kToolbarHeight;
      final displayableHeight = screenSize.height - statusBarHeight - bottomSafeArea;
      final bodyHeight = displayableHeight - appBarHeight;
      
      final overlayLeft = (screenSize.width - overlayWidth) / 2;
      // Calculate aspect ratio adjustment
      final aspectRatioAdjustment = ImageCropUtils.calculateAspectRatioAdjustment(
        screenSize: screenSize,
        statusBarHeight: statusBarHeight,
        bottomSafeArea: bottomSafeArea,
      );
      
      final overlayTop = (bodyHeight - overlayHeight) / 2 - appBarHeight - aspectRatioAdjustment;
      
      debugPrint('=== CROP CALCULATION DEBUG ===');
      debugPrint('Overlay position (body area center-based):');
      debugPrint('  - Displayable height: ${displayableHeight}px');
      debugPrint('  - Body height: ${bodyHeight}px');
      debugPrint('  - Overlay size: ${overlayWidth}x$overlayHeight');
      debugPrint('  - Overlay left: $overlayLeft');
      debugPrint('  - Overlay top: $overlayTop');
      
      // Calculate both formulas for comparison
      final formula1 = (bodyHeight - overlayHeight) / 2 - appBarHeight;
      final formula2 = (bodyHeight - overlayHeight) / 2 - appBarHeight - (statusBarHeight - bottomSafeArea) / 2;
      final adjustment = (statusBarHeight - bottomSafeArea) / 2;
      
      // Calculate aspect ratio adjustment
      final screenAspectRatio = screenSize.height / screenSize.width;
      final expectedImageAspectRatio = 16.0 / 9.0; // Expected camera aspect ratio
      final aspectRatioDifference = (screenAspectRatio - expectedImageAspectRatio).abs();
      final manualAspectRatioAdjustment = aspectRatioDifference > 0.1 ? (statusBarHeight - bottomSafeArea) / 2 : 0.0;
      
      debugPrint('Formula comparison:');
      debugPrint('  - Formula 1 (UI): $formula1');
      debugPrint('  - Formula 2 (Crop): $formula2');
      debugPrint('  - Adjustment: $adjustment');
      debugPrint('  - Difference: ${formula1 - formula2}');
      debugPrint('Aspect ratio analysis:');
      debugPrint('  - Screen aspect ratio: ${screenAspectRatio.toStringAsFixed(3)}');
      debugPrint('  - Expected image aspect ratio: ${expectedImageAspectRatio.toStringAsFixed(3)}');
      debugPrint('  - Aspect ratio difference: ${aspectRatioDifference.toStringAsFixed(3)}');
      debugPrint('  - Aspect ratio adjustment needed: ${aspectRatioDifference > 0.1}');
      debugPrint('  - Manual adjustment: $manualAspectRatioAdjustment');

      // Get camera rotation
      final cameraRotation = ImageCropUtils.getCameraRotation(
        _cameraController!.description,
      );
      debugPrint('Camera rotation: $cameraRotation degrees');

      // Create overlay rectangle
      final overlayRect = ImageCropUtils.getOverlayRect(
        screenSize: screenSize,
        overlayLeft: overlayLeft,
        overlayTop: overlayTop,
        overlayWidth: overlayWidth,
        overlayHeight: overlayHeight,
      );
      debugPrint(
        'Overlay rectangle created: ${overlayRect.left}, ${overlayRect.top}, ${overlayRect.width}x${overlayRect.height}',
      );

      // Calculate body area for crop calculation (using body area since overlay is centered on body area)
      final bodyArea = ui.Rect.fromLTWH(0, appBarHeight, screenSize.width, bodyHeight);
      
      // Crop image using the utility
      debugPrint('Calling ImageCropUtils.cropImageToOverlay...');
      final croppedPath = await ImageCropUtils.cropImageToOverlay(
        imagePath: imagePath,
        overlayRect: overlayRect,
        screenSize: screenSize,
        cameraRotation: cameraRotation,
        bodyArea: bodyArea,
      );
      debugPrint('Crop completed, result paths: $croppedPath');
      debugPrint('=== CCCD Camera: Image crop completed ===');

      return croppedPath;
    } catch (e) {
      debugPrint('ERROR: Crop error in CCCD Camera: $e');
      return {
        'resized': imagePath,
        'original': imagePath,
      };
    }
  }
}

/// Custom painter to draw overlay with transparent cutout
class OverlayPainter extends CustomPainter {
  final Rect cutoutRect;

  OverlayPainter({
    required this.cutoutRect,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black.withValues(alpha: 0.5)
      ..style = PaintingStyle.fill;

    // Draw 4 rectangles around the cutout area to create the overlay effect
    // Top rectangle
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, cutoutRect.top),
      paint,
    );

    // Bottom rectangle
    canvas.drawRect(
      Rect.fromLTWH(0, cutoutRect.bottom, size.width, size.height - cutoutRect.bottom),
      paint,
    );

    // Left rectangle
    canvas.drawRect(
      Rect.fromLTWH(0, cutoutRect.top, cutoutRect.left, cutoutRect.height),
      paint,
    );

    // Right rectangle
    canvas.drawRect(
      Rect.fromLTWH(cutoutRect.right, cutoutRect.top, size.width - cutoutRect.right, cutoutRect.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
