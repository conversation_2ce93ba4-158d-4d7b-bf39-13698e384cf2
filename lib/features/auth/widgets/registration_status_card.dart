import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/utils/app_logger.dart';
import '../models/local_registration_data.dart';
import '../services/registration_approval_service.dart';
import '../services/registration_storage_service.dart';

/// Widget hiển thị thông tin đăng ký trên màn hình login
class RegistrationStatusCard extends StatefulWidget {
  final VoidCallback? onRegisterAgain;
  final VoidCallback? onViewDetails;
  final VoidCallback? onLoginWithRegistration;

  const RegistrationStatusCard({
    super.key,
    this.onRegisterAgain,
    this.onViewDetails,
    this.onLoginWithRegistration,
  });

  @override
  State<RegistrationStatusCard> createState() => _RegistrationStatusCardState();
}

class _RegistrationStatusCardState extends State<RegistrationStatusCard> {
  final RegistrationApprovalService _approvalService =
      RegistrationApprovalService();
  final RegistrationStorageService _storageService =
      RegistrationStorageService();
  final AppLogger _logger = AppLogger();

  LocalRegistrationData? _registration;
  ApprovalStatusResult? _statusResult;
  bool _isLoading = true;
  bool _isCheckingStatus = false;

  @override
  void initState() {
    super.initState();
    _loadRegistrationData();
  }

  /// Load dữ liệu registration và kiểm tra trạng thái
  Future<void> _loadRegistrationData() async {
    try {
      setState(() => _isLoading = true);
      _logger.i('Loading registration data...');

      // Lấy registration gần nhất từ local storage
      final registration = await _storageService
          .getLatestCompletedRegistration();

      _logger.i(
        'Registration data loaded: ${registration != null ? 'YES' : 'NO'}',
      );
      if (registration != null) {
        _logger.i('Registration ID: ${registration.registerId}');
        _logger.i('Is completed: ${registration.isCompleted}');
      }

      if (registration == null || !registration.isCompleted) {
        _logger.i('No completed registration found, hiding card');
        setState(() {
          _registration = null;
          _statusResult = null;
          _isLoading = false;
        });
        return;
      }

      setState(() => _registration = registration);

      // Kiểm tra trạng thái approval
      await _checkApprovalStatus();
    } catch (e) {
      _logger.e('Error loading registration data: $e');
      setState(() => _isLoading = false);
    }
  }

  /// Kiểm tra trạng thái approval
  Future<void> _checkApprovalStatus() async {
    try {
      setState(() => _isCheckingStatus = true);
      _logger.i('Starting approval status check...');

      final statusResult = await _approvalService
          .checkLatestRegistrationStatus();

      _logger.i('Approval status check completed: ${statusResult?.status}');
      if (statusResult != null) {
        _logger.i('Status: ${statusResult.status}');
        _logger.i('Message: ${statusResult.message}');
        _logger.i('Is approved: ${statusResult.isApproved}');
        _logger.i('Register ID: ${statusResult.registerId}');
      }

      setState(() {
        _statusResult = statusResult;
        _isLoading = false;
        _isCheckingStatus = false;
      });
    } catch (e) {
      _logger.e('Error checking approval status: $e');
      setState(() {
        _isLoading = false;
        _isCheckingStatus = false;
      });
    }
  }

  /// Refresh trạng thái
  Future<void> _refreshStatus() async {
    await _checkApprovalStatus();
  }

  /// Clear current registration data và navigate to register screen
  Future<void> _clearCurrentAndNavigateToRegister() async {
    try {
      // Restore dữ liệu từ completed registration vào current registration để user có thể chỉnh sửa
      if (_registration != null) {
        _logger.i(
          'Restoring completed registration data to current registration for editing',
        );

        await _storageService.saveCurrentRegistration(
          registrationData: _registration!.registrationData,
          currentStep: _registration!.currentStep,
        );

        _logger.i('Restored registration data successfully');
      }

      // Navigate to register screen
      if (widget.onRegisterAgain != null) {
        widget.onRegisterAgain!();
      }
    } catch (e) {
      _logger.e('Error restoring registration data: $e');
    }
  }

  /// Xóa dữ liệu registration (khi user đăng nhập thành công)
  /// Method này sẽ được gọi từ bên ngoài khi user đăng nhập thành công
  Future<void> clearRegistrationData() async {
    try {
      await _storageService.clearAllRegistrationData();
      setState(() {
        _registration = null;
        _statusResult = null;
      });
    } catch (e) {
      _logger.e('Error clearing registration data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Không hiển thị nếu không có dữ liệu
    if (_isLoading || _registration == null) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(isDarkMode),

          // Content
          _buildContent(isDarkMode),

          // Actions
          _buildActions(isDarkMode),
        ],
      ),
    );
  }

  /// Build header với icon và title
  Widget _buildHeader(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDark
            : AppColors.backgroundPrimary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusL),
          topRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              TablerIcons.file_description,
              color: AppColors.info,
              size: 20,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Thông tin đăng ký của bạn',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                color: isDarkMode
                    ? AppColors.neutral100
                    : AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          if (_isCheckingStatus)
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.info),
              ),
            )
          else
            IconButton(
              onPressed: _refreshStatus,
              icon: Icon(
                TablerIcons.refresh,
                size: 16,
                color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            ),
        ],
      ),
    );
  }

  /// Build content với thông tin chi tiết
  Widget _buildContent(bool isDarkMode) {
    return Padding(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Registration ID
          if (_registration?.registerId != null) ...[
            _buildInfoRow(
              'Mã đăng ký',
              _registration!.registerId!,
              TablerIcons.hash,
              isDarkMode,
            ),
            SizedBox(height: AppDimensions.spacingM),
          ],

          // Registration date
          // Họ và tên
          if (_registration?.registrationData['fullName'] != null) ...[
            _buildInfoRow(
              'Họ và tên',
              _registration!.registrationData['fullName'].toString(),
              TablerIcons.user,
              isDarkMode,
            ),
            SizedBox(height: AppDimensions.spacingS),
          ],

          // Số điện thoại
          if (_registration?.registrationData['phone'] != null) ...[
            _buildInfoRow(
              'Số điện thoại',
              _registration!.registrationData['phone'].toString(),
              TablerIcons.phone,
              isDarkMode,
            ),
            SizedBox(height: AppDimensions.spacingS),
          ],

          // ID Card number (masked)
          if (_registration?.nfcData != null) ...[
            _buildInfoRow(
              'Số CMND/CCCD',
              _maskIdCardNumber(
                _registration!.nfcData!['idNumber']?.toString() ?? '',
              ),
              TablerIcons.id,
              isDarkMode,
            ),
            SizedBox(height: AppDimensions.spacingM),
          ],

          if (_registration?.completedAt != null) ...[
            _buildInfoRow(
              'Ngày đăng ký',
              _formatDate(_registration!.completedAt!),
              TablerIcons.calendar,
              isDarkMode,
            ),
            SizedBox(height: AppDimensions.spacingM),
          ],

          // Status
          _buildStatusSection(isDarkMode),
        ],
      ),
    );
  }

  /// Build status section
  Widget _buildStatusSection(bool isDarkMode) {
    final status = _statusResult?.status ?? 'PENDING';
    final statusColor = _getStatusColor(status);
    final statusIcon = _getStatusIcon(status);
    final statusText = _statusResult?.displayStatus ?? 'Đang chờ phê duyệt';

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: statusColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 20),
          SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Text(
              statusText,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: statusColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build info row
  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon,
    bool isDarkMode,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
        ),
        SizedBox(width: AppDimensions.spacingS),
        Text(
          '$label: ',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode ? AppColors.neutral100 : AppColors.textPrimary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// Build actions
  Widget _buildActions(bool isDarkMode) {
    final status = _statusResult?.status ?? 'PENDING';

    // Nếu đã được duyệt (APPROVED), không hiển thị actions
    if (status == 'APPROVED') {
      return const SizedBox.shrink();
    }

    // Nếu chưa được duyệt, hiển thị nút "Đăng ký lại"
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDark
            : AppColors.backgroundPrimary,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.radiusL),
          bottomRight: Radius.circular(AppDimensions.radiusL),
        ),
        border: Border(
          top: BorderSide(
            color: isDarkMode
                ? AppColors.borderDark.withValues(alpha: 0.3)
                : AppColors.borderLight,
            width: 1,
          ),
        ),
      ),
      child: SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: _clearCurrentAndNavigateToRegister,
          icon: Icon(TablerIcons.edit, size: 16),
          label: Text('Đăng ký lại'),
          style: OutlinedButton.styleFrom(
            foregroundColor: isDarkMode
                ? AppColors.neutral400
                : AppColors.neutral600,
            side: BorderSide(
              color: isDarkMode
                  ? AppColors.borderDark.withValues(alpha: 0.3)
                  : AppColors.borderLight,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
          ),
        ),
      ),
    );
  }

  /// Get status color
  Color _getStatusColor(String status) {
    switch (status) {
      case 'APPROVED':
        return AppColors.success;
      case 'REJECTED':
        return AppColors.error;
      case 'NOT_FOUND':
        return AppColors.neutral500;
      case 'PENDING':
      default:
        return AppColors.info;
    }
  }

  /// Get status icon
  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'APPROVED':
        return TablerIcons.check;
      case 'REJECTED':
        return TablerIcons.x;
      case 'NOT_FOUND':
        return TablerIcons.search;
      case 'PENDING':
      default:
        return TablerIcons.clock;
    }
  }

  /// Format date
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Mask ID card number for security
  String _maskIdCardNumber(String idCardNo) {
    if (idCardNo.length < 8) return idCardNo;
    return '${idCardNo.substring(0, 3)} *** *** ${idCardNo.substring(idCardNo.length - 3)}';
  }
}
