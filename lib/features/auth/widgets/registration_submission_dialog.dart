import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../blocs/registration_submission_bloc.dart';
import '../services/registration_submission_service.dart';

class RegistrationSubmissionDialog extends StatelessWidget {
  final Map<String, dynamic> registrationData;
  final VoidCallback? onSuccess;
  final VoidCallback? onError;

  const RegistrationSubmissionDialog({
    super.key,
    required this.registrationData,
    this.onSuccess,
    this.onError,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RegistrationSubmissionBloc(),
      child: BlocListener<RegistrationSubmissionBloc, RegistrationSubmissionState>(
        listener: (context, state) {
          if (state is RegistrationSubmissionCompleted) {
            onSuccess?.call();
            // Không tự động đóng dialog, để user bấm nút "<PERSON><PERSON><PERSON> thành"
          } else if (state is RegistrationSubmissionFailed) {
            onError?.call();
            // <PERSON>hông tự động đóng dialog, để user bấm nút "<PERSON>hử lại" hoặc "Đóng"
          }
        },
        child:
            BlocBuilder<
              RegistrationSubmissionBloc,
              RegistrationSubmissionState
            >(
              builder: (context, state) {
                return Dialog(
                  backgroundColor: Theme.of(
                    context,
                  ).dialogTheme.backgroundColor,
                  surfaceTintColor: Colors.transparent,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                  ),
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: 400,
                      maxHeight: MediaQuery.of(context).size.height * 0.8,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildHeader(context, state),
                        Flexible(
                          child: SingleChildScrollView(
                            child: Padding(
                              padding: EdgeInsets.all(AppDimensions.paddingL),
                              child: _buildContent(context, state),
                            ),
                          ),
                        ),
                        _buildActions(context, state),
                      ],
                    ),
                  ),
                );
              },
            ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, RegistrationSubmissionState state) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    IconData icon;
    Color iconColor;
    String title;

    if (state is RegistrationSubmissionInProgress) {
      icon = TablerIcons.loader_2;
      iconColor = AppColors.info;
      title = 'Đang xử lý đăng ký';
    } else if (state is RegistrationSubmissionCompleted) {
      icon = TablerIcons.check;
      iconColor = AppColors.success;
      title = 'Đăng ký thành công';
    } else if (state is RegistrationSubmissionFailed) {
      icon = TablerIcons.x;
      iconColor = AppColors.error;
      title = 'Đăng ký thất bại';
    } else {
      icon = TablerIcons.send;
      iconColor = AppColors.kienlongOrange;
      title = 'Xác nhận đăng ký';
    }

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusL),
          topRight: Radius.circular(AppDimensions.radiusL),
        ),
        border: Border(
          bottom: BorderSide(
            color: isDarkMode
                ? AppColors.borderDark.withValues(alpha: 0.3)
                : AppColors.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: iconColor, size: 24),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          if (state is RegistrationSubmissionInProgress)
            IconButton(
              onPressed: () {
                context.read<RegistrationSubmissionBloc>().add(
                  CancelSubmissionEvent(),
                );
                Navigator.of(context).pop();
              },
              icon: Icon(TablerIcons.x, size: 20),
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
        ],
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    RegistrationSubmissionState state,
  ) {
    if (state is RegistrationSubmissionInitial) {
      return _buildConfirmationContent(context);
    } else if (state is RegistrationSubmissionInProgress) {
      return _buildProgressContent(context, state.progress);
    } else if (state is RegistrationSubmissionCompleted) {
      return _buildSuccessContent(context, state);
    } else if (state is RegistrationSubmissionFailed) {
      return _buildErrorContent(context, state);
    }

    return SizedBox.shrink();
  }

  Widget _buildConfirmationContent(BuildContext context) {
    // Debug: Log registration data
    // TODO: Remove debug logs after testing
    // print('🔍 Registration data in dialog: $registrationData');
    // print('🔍 fullName: ${registrationData['fullName']}');
    // print('🔍 phone: ${registrationData['phone']}');
    // print('🔍 email: ${registrationData['email']}');
    // print('🔍 province: ${registrationData['province']}');
    // print('🔍 branch: ${registrationData['branch']}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Xác nhận thông tin đăng ký',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: AppDimensions.spacingM),
        Text(
          'Vui lòng kiểm tra lại thông tin trước khi gửi đăng ký:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        SizedBox(height: AppDimensions.spacingL),
        _buildInfoItem(
          context,
          'Họ và tên',
          registrationData['fullName'] ?? 'Chưa có (sẽ được lấy từ NFC)',
        ),
        _buildInfoItem(
          context,
          'Số điện thoại',
          registrationData['phone'] ?? '',
        ),
        _buildInfoItem(context, 'Email', registrationData['email'] ?? ''),
        _buildInfoItem(
          context,
          'Tỉnh/Thành phố',
          registrationData['province'] ?? '',
        ),
        _buildInfoItem(context, 'Chi nhánh', registrationData['branch'] ?? ''),
        SizedBox(height: AppDimensions.spacingL),
        Container(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: AppColors.warning.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.warning.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(TablerIcons.info_circle, color: AppColors.warning, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  'Quá trình đăng ký sẽ mất vài phút. Vui lòng không đóng ứng dụng.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.warning,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressContent(
    BuildContext context,
    SubmissionProgress progress,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      children: [
        // Progress indicator
        SizedBox(
          width: 80,
          height: 80,
          child: CircularProgressIndicator(
            value: progress.progress,
            strokeWidth: 8,
            backgroundColor: isDarkMode
                ? AppColors.neutral700
                : AppColors.neutral200,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.kienlongOrange),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingL),

        // Progress text
        Text(
          '${(progress.progress * 100).toInt()}%',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.kienlongOrange,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),

        Text(
          progress.message,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: AppDimensions.spacingL),

        // Step indicator
        _buildStepIndicator(context, progress.currentStep),
      ],
    );
  }

  Widget _buildStepIndicator(BuildContext context, SubmissionStep step) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    final steps = [
      SubmissionStep.validating,
      SubmissionStep.uploadingFiles,
      SubmissionStep.creatingDocuments,
      SubmissionStep.submittingRegistration,
      SubmissionStep.updatingDocuments,
    ];

    final stepNames = [
      'Kiểm tra dữ liệu',
      'Upload files',
      'Tạo documents',
      'Gửi đăng ký',
      'Cập nhật thông tin',
    ];

    return Column(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final stepType = entry.value;
        final stepName = stepNames[index];
        final isCompleted = steps.indexOf(step) > index;
        final isCurrent = step == stepType;

        return Container(
          margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
          child: Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isCompleted
                      ? AppColors.success
                      : isCurrent
                      ? AppColors.kienlongOrange
                      : isDarkMode
                      ? AppColors.neutral600
                      : AppColors.neutral300,
                  shape: BoxShape.circle,
                ),
                child: isCompleted
                    ? Icon(TablerIcons.check, size: 16, color: Colors.white)
                    : isCurrent
                    ? SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : null,
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Text(
                  stepName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: isCurrent ? FontWeight.w600 : FontWeight.w400,
                    color: isCompleted
                        ? AppColors.success
                        : isCurrent
                        ? AppColors.kienlongOrange
                        : Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSuccessContent(
    BuildContext context,
    RegistrationSubmissionCompleted state,
  ) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.success.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(TablerIcons.check, size: 40, color: AppColors.success),
        ),
        SizedBox(height: AppDimensions.spacingL),

        Text(
          'Đăng ký thành công!',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.success,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppDimensions.spacingM),

        Text(
          'Mã đăng ký: ${state.registrationId}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppDimensions.spacingS),

        Text(
          'Chúng tôi sẽ liên hệ với bạn trong thời gian sớm nhất.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildErrorContent(
    BuildContext context,
    RegistrationSubmissionFailed state,
  ) {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.error.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(TablerIcons.x, size: 40, color: AppColors.error),
        ),
        SizedBox(height: AppDimensions.spacingL),

        Text(
          'Đăng ký thất bại',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.error,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: AppDimensions.spacingM),

        Container(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: AppColors.error.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: AppColors.error.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            _getShortErrorMessage(state.errorMessage),
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.error),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(height: AppDimensions.spacingL),

        Text(
          'Vui lòng thử lại hoặc liên hệ hỗ trợ nếu vấn đề vẫn tiếp tục.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.5)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.6),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(
    BuildContext context,
    RegistrationSubmissionState state,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.radiusL),
          bottomRight: Radius.circular(AppDimensions.radiusL),
        ),
        border: Border(
          top: BorderSide(
            color: isDarkMode
                ? AppColors.borderDark.withValues(alpha: 0.3)
                : AppColors.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          if (state is RegistrationSubmissionInitial) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                  side: BorderSide(
                    color: isDarkMode
                        ? AppColors.borderDark.withValues(alpha: 0.3)
                        : AppColors.borderLight,
                  ),
                ),
                child: Text('Hủy'),
              ),
            ),
            SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  context.read<RegistrationSubmissionBloc>().add(
                    SubmitRegistrationEvent(registrationData),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongOrange,
                  foregroundColor: Colors.white,
                ),
                child: Text('Xác nhận'),
              ),
            ),
          ] else if (state is RegistrationSubmissionCompleted) ...[
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // Đóng dialog trước
                  Navigator.of(context).pop(state);

                  // Navigate trực tiếp về login screen
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    Navigator.of(context).pushReplacementNamed('/login');
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.success,
                  foregroundColor: Colors.white,
                ),
                child: Text('Hoàn thành'),
              ),
            ),
          ] else if (state is RegistrationSubmissionFailed) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                  side: BorderSide(
                    color: isDarkMode
                        ? AppColors.borderDark.withValues(alpha: 0.3)
                        : AppColors.borderLight,
                  ),
                ),
                child: Text('Đóng'),
              ),
            ),
            SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  context.read<RegistrationSubmissionBloc>().add(
                    RetrySubmissionEvent(registrationData),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongOrange,
                  foregroundColor: Colors.white,
                ),
                child: Text('Thử lại'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Show dialog
  static Future<RegistrationSubmissionState?> show(
    BuildContext context, {
    required Map<String, dynamic> registrationData,
    VoidCallback? onSuccess,
    VoidCallback? onError,
  }) {
    return showDialog<RegistrationSubmissionState>(
      context: context,
      barrierDismissible: false,
      builder: (context) => RegistrationSubmissionDialog(
        registrationData: registrationData,
        onSuccess: onSuccess,
        onError: onError,
      ),
    );
  }

  /// Lấy message lỗi ngắn gọn
  String _getShortErrorMessage(String errorMessage) {
    if (errorMessage.contains('network')) {
      return 'Lỗi kết nối mạng. Vui lòng kiểm tra lại.';
    } else if (errorMessage.contains('timeout')) {
      return 'Hệ thống đang bận. Vui lòng thử lại.';
    } else if (errorMessage.contains('validation')) {
      return 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại.';
    } else if (errorMessage.contains('server')) {
      return 'Lỗi hệ thống. Vui lòng thử lại sau.';
    } else {
      // Giới hạn độ dài message
      if (errorMessage.length > 100) {
        return '${errorMessage.substring(0, 97)}...';
      }
      return errorMessage;
    }
  }
}
