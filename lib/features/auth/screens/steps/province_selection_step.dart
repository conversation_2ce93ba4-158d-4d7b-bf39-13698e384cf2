import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../../../shared/models/province_model.dart';

class ProvinceSelectionStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;
  final List<ProvinceModel> provinces;
  final bool isLoading;
  final Function(ProvinceModel)? onProvinceSelected;

  const ProvinceSelectionStep({
    super.key,
    required this.formKey,
    required this.registrationData,
    this.provinces = const [],
    this.isLoading = false,
    this.onProvinceSelected,
  });

  @override
  State<ProvinceSelectionStep> createState() => _ProvinceSelectionStepState();
}

class _ProvinceSelectionStepState extends State<ProvinceSelectionStep> {
  final TextEditingController _searchController = TextEditingController();
  List<ProvinceModel> _filteredProvinces = [];
  ProvinceModel? _selectedProvince;

  @override
  void initState() {
    super.initState();
    _filteredProvinces = List.from(widget.provinces);
    
    // Set selected province from registration data
    final selectedProvinceName = widget.registrationData['province'];
    if (selectedProvinceName != null && widget.provinces.isNotEmpty) {
      try {
        _selectedProvince = widget.provinces.firstWhere(
          (province) => province.name == selectedProvinceName,
        );
      } catch (e) {
        // Province not found in current list, will be set when provinces load
        _selectedProvince = null;
      }
    }

    _searchController.addListener(_filterProvinces);
  }

  @override
  void didUpdateWidget(ProvinceSelectionStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update filtered provinces when provinces list changes
    if (oldWidget.provinces != widget.provinces) {
      _filteredProvinces = List.from(widget.provinces);
      _filterProvinces();
      
      // Try to set selected province if it was not set before
      if (_selectedProvince == null && widget.provinces.isNotEmpty) {
        final selectedProvinceName = widget.registrationData['province'];
        if (selectedProvinceName != null) {
          try {
            _selectedProvince = widget.provinces.firstWhere(
              (province) => province.name == selectedProvinceName,
            );
          } catch (e) {
            // Province not found, keep as null
          }
        }
      }
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterProvinces() {
    final searchTerm = _searchController.text.toLowerCase();
    
    if (searchTerm.isEmpty) {
      setState(() {
        _filteredProvinces = List.from(widget.provinces);
      });
    } else {
      setState(() {
        _filteredProvinces = widget.provinces
            .where((province) =>
                province.name.toLowerCase().contains(searchTerm) ||
                province.gsoCode.toLowerCase().contains(searchTerm))
            .toList();
      });
    }
  }

  void _selectProvince(ProvinceModel province) {
    setState(() {
      _selectedProvince = province;
    });

    // Update registration data
    widget.registrationData['province'] = province.name;
    widget.registrationData['provinceCode'] = province.id;

    // Call callback if provided
    widget.onProvinceSelected?.call(province);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            _buildIntroduction(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Search Field
            _buildSearchField(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Provinces List
            _buildProvincesList(isDarkMode),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroduction(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.map_pin,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Chọn tỉnh/thành phố',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Vui lòng chọn tỉnh/thành phố nơi bạn sinh sống và làm việc. '
            'Thông tin này sẽ được sử dụng để xác định chi nhánh gần nhất.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField(bool isDarkMode) {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: 'Tìm kiếm tỉnh/thành phố...',
        prefixIcon: Icon(
          TablerIcons.search,
          color: AppColors.textSecondary,
        ),
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: Icon(
                  TablerIcons.x,
                  color: AppColors.textSecondary,
                ),
                onPressed: () {
                  _searchController.clear();
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        filled: true,
        fillColor: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
      ),
    );
  }

  Widget _buildProvincesList(bool isDarkMode) {
    if (widget.isLoading) {
      return Center(
        child: Column(
          children: [
            CircularProgressIndicator(),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Đang tải danh sách tỉnh/thành...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    if (widget.provinces.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              TablerIcons.alert_circle,
              size: AppDimensions.iconL,
              color: AppColors.warning,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Không thể tải danh sách tỉnh/thành',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            ElevatedButton(
              onPressed: () {
                // TODO: Retry loading provinces
              },
              child: Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (_filteredProvinces.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              TablerIcons.search_off,
              size: AppDimensions.iconL,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Không tìm thấy tỉnh/thành phù hợp',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Danh sách tỉnh/thành phố (${_filteredProvinces.length})',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingM),
        ..._filteredProvinces.map((province) => _buildProvinceItem(
          province,
          isDarkMode,
        )),
      ],
    );
  }

  Widget _buildProvinceItem(ProvinceModel province, bool isDarkMode) {
    final isSelected = _selectedProvince?.id == province.id;

    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.kienlongOrange.withValues(alpha: 0.1)
            : isDarkMode
                ? AppColors.backgroundDarkSecondary
                : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isSelected
              ? AppColors.kienlongOrange
              : isDarkMode
                  ? AppColors.borderDark.withValues(alpha: 0.3)
                  : AppColors.borderLight,
        ),
      ),
      child: ListTile(
        leading: Icon(
          isSelected ? TablerIcons.map_pin_filled : TablerIcons.map_pin,
          color: isSelected
              ? AppColors.kienlongOrange
              : AppColors.textSecondary,
        ),
        title: Text(
          province.name,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected
                ? AppColors.kienlongOrange
                : null,
          ),
        ),
        subtitle: Text(
          'Mã: ${province.gsoCode}',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        trailing: isSelected
            ? Icon(
                TablerIcons.check,
                color: AppColors.kienlongOrange,
              )
            : null,
        onTap: () => _selectProvince(province),
      ),
    );
  }
}
