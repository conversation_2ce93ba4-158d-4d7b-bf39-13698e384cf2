import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';
import '../../../../../shared/widgets/index.dart';
import '../../../services/mrz_scanner_service.dart';

class MrzResultSection extends StatelessWidget {
  final MrzScanResult mrzScanResult;
  final bool isDarkMode;
  final VoidCallback onRescan;

  const MrzResultSection({
    super.key,
    required this.mrzScanResult,
    required this.isDarkMode,
    required this.onRescan,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.kienlongSkyBlue.withValues(alpha: 0.1)
            : AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                TablerIcons.scan,
                color: AppColors.kienlongSkyBlue,
                size: 20,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                mrzScanResult.isSuccess
                    ? 'Dữ liệu MRZ đã parse'
                    : 'Mã MRZ đã đọc được',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: mrzScanResult.isSuccess
                      ? AppColors.success
                      : AppColors.kienlongSkyBlue,
                ),
              ),
              Spacer(),
              IconButton(
                onPressed: onRescan,
                icon: Icon(TablerIcons.refresh, size: 16),
                color: mrzScanResult.isSuccess
                    ? AppColors.success
                    : AppColors.kienlongSkyBlue,
                tooltip: 'Quét lại',
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Hiển thị dữ liệu đã parse nếu thành công, hoặc raw lines nếu thất bại
          if (mrzScanResult.isSuccess) ...[
            // Parsed data grid
            GridView.count(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: AppDimensions.spacingM,
              mainAxisSpacing: AppDimensions.spacingM,
              childAspectRatio: 3,
              children: [
                _buildDataItem('Loại giấy tờ', mrzScanResult.parsedData!.documentType, isDarkMode),
                _buildDataItem('Quốc gia', mrzScanResult.parsedData!.countryCode, isDarkMode),
                _buildDataItem('Họ', mrzScanResult.parsedData!.surnames, isDarkMode),
                _buildDataItem('Tên', mrzScanResult.parsedData!.givenNames, isDarkMode),
                _buildDataItem('Số giấy tờ', mrzScanResult.parsedData!.documentNumber, isDarkMode),
                _buildDataItem(
                  'Quốc tịch',
                  mrzScanResult.parsedData!.nationalityCountryCode,
                  isDarkMode,
                ),
                _buildDataItem(
                  'Ngày sinh',
                  _formatDate(mrzScanResult.parsedData!.birthDate),
                  isDarkMode,
                ),
                _buildDataItem('Giới tính', _formatSex(mrzScanResult.parsedData!.sex), isDarkMode),
                _buildDataItem(
                  'Hạn sử dụng',
                  _formatDate(mrzScanResult.parsedData!.expiryDate),
                  isDarkMode,
                ),
                _buildDataItem('Mã cá nhân', mrzScanResult.parsedData!.personalNumber, isDarkMode),
                if (mrzScanResult.parsedData!.personalNumber2 != null)
                  _buildDataItem(
                    'Mã cá nhân 2',
                    mrzScanResult.parsedData!.personalNumber2!,
                    isDarkMode,
                  ),
              ],
            ),
            SizedBox(height: AppDimensions.spacingM),
          ] else ...[
            // Hiển thị raw MRZ lines khi parse thất bại
            ...mrzScanResult.rawLines.asMap().entries.map((entry) {
              final index = entry.key;
              final line = entry.value;
              return Container(
                margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? AppColors.neutral800.withValues(alpha: 0.5)
                      : AppColors.neutral100,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  border: Border.all(
                    color: isDarkMode
                        ? AppColors.borderDark.withValues(alpha: 0.3)
                        : AppColors.borderLight,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Line label
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingS,
                            vertical: AppDimensions.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.kienlongSkyBlue.withValues(
                              alpha: 0.2,
                            ),
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusS,
                            ),
                          ),
                          child: Text(
                            'Dòng ${index + 1}',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: AppColors.kienlongSkyBlue,
                            ),
                          ),
                        ),
                        Spacer(),
                        // Copy button
                        IconButton(
                          onPressed: () => _copyMrzLine(context, line),
                          icon: Icon(TablerIcons.copy, size: 14),
                          color: AppColors.kienlongSkyBlue,
                          tooltip: 'Copy dòng ${index + 1}',
                          padding: EdgeInsets.zero,
                          constraints: BoxConstraints(
                            minWidth: 24,
                            minHeight: 24,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: AppDimensions.spacingS),

                    // MRZ line content
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(AppDimensions.paddingS),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? AppColors.neutral900.withValues(alpha: 0.8)
                            : AppColors.neutral50,
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusS,
                        ),
                        border: Border.all(
                          color: isDarkMode
                              ? AppColors.borderDark.withValues(alpha: 0.2)
                              : AppColors.borderLight.withValues(alpha: 0.5),
                        ),
                      ),
                      child: SelectableText(
                        line,
                        style: TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                          letterSpacing: 1.2,
                          height: 1.4,
                          color: isDarkMode
                              ? AppColors.neutral100
                              : AppColors.neutral900,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
            SizedBox(height: AppDimensions.spacingM),
          ],

          // Info text
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.info.withValues(alpha: 0.1)
                  : AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(color: AppColors.info.withValues(alpha: 0.2)),
            ),
            child: Row(
              children: [
                Icon(TablerIcons.info_circle, size: 16, color: AppColors.info),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    mrzScanResult.isSuccess
                        ? 'Đã parse thành công dữ liệu MRZ. Bạn có thể xem thông tin chi tiết bên trên.'
                        : 'Không thể parse dữ liệu MRZ. Đây là mã raw, bạn có thể copy từng dòng để kiểm tra.',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: isDarkMode
                          ? AppColors.neutral200
                          : AppColors.neutral700,
                      fontSize: 11,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataItem(String label, String value, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.5)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 2),
          Text(
              value,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: isDarkMode ? AppColors.neutral100 : AppColors.neutral900,
                fontSize: 11,
                fontWeight: FontWeight.w600,
                height: 1.3,
              ), 
            ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatSex(dynamic sex) {
    if (sex == null) return 'Không xác định';
    
    switch (sex.toString()) {
      case 'Sex.male':
        return 'Nam';
      case 'Sex.female':
        return 'Nữ';
      case 'Sex.none':
        return 'Không xác định';
      default:
        return 'Không xác định';
    }
  }

  /// Copy MRZ line vào clipboard
  void _copyMrzLine(BuildContext context, String line) {
    Clipboard.setData(ClipboardData(text: line));
    CustomSnackBar.show(
      context,
      message: 'Đã copy mã MRZ vào clipboard',
      type: SnackBarType.success,
      duration: Duration(seconds: 2),
    );
  }
} 