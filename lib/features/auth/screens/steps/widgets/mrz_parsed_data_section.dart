import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';

class MrzParsedDataSection extends StatelessWidget {
  final dynamic mrzData;
  final bool isDarkMode;

  const MrzParsedDataSection({
    super.key,
    required this.mrzData,
    required this.isDarkMode,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.success.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(TablerIcons.check, color: AppColors.success, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Thông tin đã parse từ MRZ',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.success,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Parsed data grid
          GridView.count(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: AppDimensions.spacingM,
            mainAxisSpacing: AppDimensions.spacingM,
            childAspectRatio: 3,
            children: [
              _buildDataItem('Loại giấy tờ', mrzData.documentType, isDarkMode),
              _buildDataItem('Quốc gia', mrzData.countryCode, isDarkMode),
              _buildDataItem('Họ', mrzData.surnames, isDarkMode),
              _buildDataItem('Tên', mrzData.givenNames, isDarkMode),
              _buildDataItem('Số giấy tờ', mrzData.documentNumber, isDarkMode),
              _buildDataItem(
                'Quốc tịch',
                mrzData.nationalityCountryCode,
                isDarkMode,
              ),
              _buildDataItem(
                'Ngày sinh',
                _formatDate(mrzData.birthDate),
                isDarkMode,
              ),
              _buildDataItem('Giới tính', _formatSex(mrzData.sex), isDarkMode),
              _buildDataItem(
                'Hạn sử dụng',
                _formatDate(mrzData.expiryDate),
                isDarkMode,
              ),
              _buildDataItem('Mã cá nhân', mrzData.personalNumber, isDarkMode),
              if (mrzData.personalNumber2 != null)
                _buildDataItem(
                  'Mã cá nhân 2',
                  mrzData.personalNumber2!,
                  isDarkMode,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDataItem(String label, String value, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.5)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 2),
          Text(
            value,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral100 : AppColors.neutral900,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatSex(dynamic sex) {
    if (sex == null) return 'Không xác định';
    
    switch (sex.toString()) {
      case 'Sex.male':
        return 'Nam';
      case 'Sex.female':
        return 'Nữ';
      case 'Sex.none':
        return 'Không xác định';
      default:
        return 'Không xác định';
    }
  }
} 