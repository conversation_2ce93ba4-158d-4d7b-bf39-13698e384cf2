import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';

class IdPhotoGuidelines extends StatelessWidget {
  const IdPhotoGuidelines({super.key});

  @override
  Widget build(BuildContext context) {
    final guidelines = [
      'Đặt CCCD trên nền phẳng, có ánh sáng tốt',
      'Đ<PERSON>m bảo toàn bộ thẻ nằm trong khung chụp',
      '<PERSON>r<PERSON><PERSON> chụp bị mờ, nghiêng hoặc có bóng',
      'Thông tin trên thẻ phải rõ ràng, dễ đọc',
    ];

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(TablerIcons.info_circle, color: AppColors.warning, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Hướng dẫn chụp ảnh',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          ...guidelines.map(
            (guideline) => Padding(
              padding: EdgeInsets.only(bottom: AppDimensions.spacingS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.warning,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      guideline,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 