import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';

class IdPhotoPlaceholder extends StatelessWidget {
  final String photoType;

  const IdPhotoPlaceholder({
    super.key,
    required this.photoType,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 80,
          height: 50,
          decoration: BoxDecoration(
            border: Border.all(
              color: AppColors.kienlongOrange.withValues(alpha: 0.5),
              width: 2,
              style: BorderStyle.solid,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                TablerIcons.id,
                color: AppColors.kienlongOrange.withValues(alpha: 0.5),
                size: 20,
              ),
              SizedBox(height: 2),
              Text(
                photoType == 'front' ? 'MẶT TRƯỚC' : 'MẶT SAU',
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.5),
                  fontSize: 8,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: AppDimensions.spacingM),
        Text(
          'Nhấn "Chụp ảnh" để bắt đầu',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        Text(
          'Đặt CCCD trong khung chấm để có ảnh rõ nét',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
} 