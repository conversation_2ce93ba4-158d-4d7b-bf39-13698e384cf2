import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:google_mlkit_barcode_scanning/google_mlkit_barcode_scanning.dart';
import '../../../../../core/theme/index.dart';

import '../../../services/qr_scanner_service.dart';

class QrResultSection extends StatelessWidget {
  final List<QrScanResult> qrScanResults;
  final bool isDarkMode;
  final VoidCallback onRescan;

  const QrResultSection({
    super.key,
    required this.qrScanResults,
    required this.isDarkMode,
    required this.onRescan,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                TablerIcons.qrcode,
                color: AppColors.kienlongSkyBlue,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  'Kết quả đọc mã QR',
                  style: AppTypography.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              IconButton(
                onPressed: onRescan,
                icon: Icon(
                  TablerIcons.refresh,
                  color: AppColors.kienlongSkyBlue,
                  size: AppDimensions.iconS,
                ),
                tooltip: 'Quét lại',
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),

          // QR Results
          ...qrScanResults.asMap().entries.map((entry) {
            final index = entry.key;
            final qrResult = entry.value;
            return _buildQrResultItem(context, qrResult, index + 1, isDarkMode);
          }),
        ],
      ),
    );
  }

  Widget _buildQrResultItem(BuildContext context, QrScanResult qrResult, int index, bool isDarkMode) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
      padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // QR Index
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingXS,
                  vertical: 2,
                ),
                decoration: BoxDecoration(
                  color: AppColors.kienlongSkyBlue,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusXS),
                ),
                child: Text(
                  'QR $index',
                  style: AppTypography.textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingS),
                              Text(
                  'Format: ${_getFormatName(qrResult.format)}',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingXS),

          // QR Value
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusXS),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: SelectableText(
              qrResult.value,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontFamily: 'monospace',
              ),
            ),
          ),

          // Bounding Box Info (if available)
          if (qrResult.boundingBox != null) ...[
            SizedBox(height: AppDimensions.spacingXS),
            Text(
              'Vị trí: ${qrResult.boundingBox!.left.toInt()}, ${qrResult.boundingBox!.top.toInt()} - ${qrResult.boundingBox!.width.toInt()}x${qrResult.boundingBox!.height.toInt()}',
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _getFormatName(BarcodeFormat format) {
    switch (format) {
      case BarcodeFormat.qrCode:
        return 'QR Code';
      case BarcodeFormat.dataMatrix:
        return 'Data Matrix';
      case BarcodeFormat.aztec:
        return 'Aztec';
      case BarcodeFormat.pdf417:
        return 'PDF417';
      case BarcodeFormat.code128:
        return 'Code 128';
      case BarcodeFormat.code39:
        return 'Code 39';
      case BarcodeFormat.ean13:
        return 'EAN-13';
      case BarcodeFormat.ean8:
        return 'EAN-8';

      case BarcodeFormat.codabar:
        return 'Codabar';
      case BarcodeFormat.itf:
        return 'ITF';
      case BarcodeFormat.all:
        return 'All';
      default:
        return 'Unknown';
    }
  }
} 