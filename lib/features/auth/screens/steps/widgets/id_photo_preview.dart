import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';
import '../../../../../shared/widgets/index.dart';

class IdPhotoPreview extends StatelessWidget {
  final String photo;
  final String photoType;

  const IdPhotoPreview({
    super.key,
    required this.photo,
    required this.photoType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM - 2),
        child: Stack(
          children: [
            // Display actual image with tap to view full screen
            GestureDetector(
              onTap: () {
                FullScreenImageViewer.show(
                  context,
                  imagePath: photo,
                  heroTag: 'id_photo_$photo',
                  title:
                      'CCCD ${photoType == 'front' ? 'mặt trước' : 'mặt sau'}',
                );
              },
              child: Hero(
                tag: 'id_photo_$photo',
                child: Image.file(
                  File(photo),
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback for invalid image paths
                    return Container(
                      decoration: BoxDecoration(
                        color: AppColors.success.withValues(alpha: 0.1),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              TablerIcons.photo,
                              size: 48,
                              color: AppColors.success,
                            ),
                            SizedBox(height: AppDimensions.spacingS),
                            Text(
                              'Ảnh đã được chụp',
                              style: AppTypography.textTheme.bodyMedium
                                  ?.copyWith(
                                    color: AppColors.success,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            // Success indicator overlay
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: AppColors.success,
                  shape: BoxShape.circle,
                ),
                child: Icon(TablerIcons.check, size: 16, color: Colors.white),
              ),
            ),
            // Tap indicator overlay
            Positioned(
              bottom: 8,
              left: 8,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingS,
                  vertical: AppDimensions.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(TablerIcons.eye, size: 12, color: Colors.white),
                    SizedBox(width: AppDimensions.spacingXS),
                    Text(
                      'Nhấn để xem',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 