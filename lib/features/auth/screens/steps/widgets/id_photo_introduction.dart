import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';

class IdPhotoIntroduction extends StatelessWidget {
  const IdPhotoIntroduction({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(TablerIcons.camera, color: AppColors.kienlongSkyBlue, size: 24),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Chụp ảnh CCCD để xác minh danh tính. <PERSON><PERSON><PERSON> đảm bảo ảnh rõ nét và đầy đủ thông tin.',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 