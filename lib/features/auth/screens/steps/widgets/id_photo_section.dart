import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';
import 'id_photo_preview.dart';
import 'id_photo_placeholder.dart';

class IdPhotoSection extends StatelessWidget {
  final String title;
  final String description;
  final String photoType;
  final String? photo;
  final bool isDarkMode;
  final bool hasDocumentId;
  final VoidCallback onTakePhoto;
  final VoidCallback onRetakePhoto;

  const IdPhotoSection({
    super.key,
    required this.title,
    required this.description,
    required this.photoType,
    required this.photo,
    required this.isDarkMode,
    required this.hasDocumentId,
    required this.onTakePhoto,
    required this.onRetakePhoto,
  });

  @override
  Widget build(BuildContext context) {
    final hasPhoto = photo != null;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: hasPhoto
              ? AppColors.success.withValues(alpha: 0.3)
              : isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: hasPhoto ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                hasPhoto ? TablerIcons.check : TablerIcons.camera,
                color: hasPhoto ? AppColors.success : AppColors.kienlongOrange,
                size: 24,
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTypography.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: hasPhoto
                            ? AppColors.success
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      description,
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Photo Preview or Placeholder
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.neutral800.withValues(alpha: 0.3)
                  : AppColors.neutral100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: isDarkMode
                    ? AppColors.borderDark.withValues(alpha: 0.3)
                    : AppColors.borderLight,
                width: 1,
                style: BorderStyle.solid,
              ),
            ),
            child: hasPhoto
                ? IdPhotoPreview(photo: photo!, photoType: photoType)
                : IdPhotoPlaceholder(photoType: photoType),
          ),

          SizedBox(height: AppDimensions.spacingM),



          // Action Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => hasPhoto ? onRetakePhoto() : onTakePhoto(),
              icon: Icon(
                hasPhoto ? TablerIcons.refresh : TablerIcons.camera,
                size: AppDimensions.iconS,
              ),
              label: Text(hasPhoto ? 'Chụp lại' : 'Chụp ảnh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: hasPhoto
                    ? AppColors.info
                    : AppColors.kienlongSkyBlue,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
} 