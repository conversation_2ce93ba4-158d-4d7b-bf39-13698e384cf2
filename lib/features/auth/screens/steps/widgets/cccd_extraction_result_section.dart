import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../../core/theme/index.dart';
import '../../../../../shared/models/cccd_extraction_model.dart';

class CccdExtractionResultSection extends StatelessWidget {
  final CccdExtractionModel cccdExtractionResult;
  final bool isDarkMode;
  final VoidCallback onReextract;

  const CccdExtractionResultSection({
    super.key,
    required this.cccdExtractionResult,
    required this.isDarkMode,
    required this.onReextract,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.kienlongOrange.withValues(alpha: 0.1)
            : AppColors.kienlongOrange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongOrange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                TablerIcons.id,
                color: AppColors.kienlongOrange,
                size: 20,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Thông tin CCCD đã trích xuất',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.kienlongOrange,
                ),
              ),
              Spacer(),
              IconButton(
                onPressed: onReextract,
                icon: Icon(
                  TablerIcons.refresh,
                  size: 16,
                  color: AppColors.kienlongOrange,
                ),
                tooltip: 'Trích xuất lại',
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Confidence indicator
          Container(
            margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.neutral800.withValues(alpha: 0.5)
                  : AppColors.neutral100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: isDarkMode
                    ? AppColors.borderDark.withValues(alpha: 0.3)
                    : AppColors.borderLight,
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.check,
                  color: cccdExtractionResult.confidence >= 0.8
                      ? AppColors.success
                      : cccdExtractionResult.confidence >= 0.6
                          ? AppColors.warning
                          : AppColors.error,
                  size: 16,
                ),
                SizedBox(width: AppDimensions.spacingXS),
                Text(
                  'Độ tin cậy: ${(cccdExtractionResult.confidence * 100).toStringAsFixed(1)}%',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: cccdExtractionResult.confidence >= 0.8
                        ? AppColors.success
                        : cccdExtractionResult.confidence >= 0.6
                            ? AppColors.warning
                            : AppColors.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Expiry status
          if (cccdExtractionResult.isExpired || cccdExtractionResult.daysUntilExpiry <= 180)
            Container(
              margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: cccdExtractionResult.isExpired
                    ? AppColors.error.withValues(alpha: 0.1)
                    : AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: cccdExtractionResult.isExpired
                      ? AppColors.error
                      : AppColors.warning,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    cccdExtractionResult.isExpired
                        ? TablerIcons.alert_triangle
                        : TablerIcons.clock,
                    color: cccdExtractionResult.isExpired
                        ? AppColors.error
                        : AppColors.warning,
                    size: 16,
                  ),
                  SizedBox(width: AppDimensions.spacingXS),
                  Expanded(
                    child: Text(
                      cccdExtractionResult.isExpired
                          ? 'CCCD đã hết hạn'
                          : 'CCCD sắp hết hạn (${cccdExtractionResult.daysUntilExpiry} ngày)',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: cccdExtractionResult.isExpired
                            ? AppColors.error
                            : AppColors.warning,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Extracted information grid
          Column(
            children: [
              // Grid for short fields (2 columns)
              GridView.count(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: AppDimensions.spacingM,
                mainAxisSpacing: AppDimensions.spacingM,
                childAspectRatio: 3,
                children: [
                  _buildDataItem('Số CCCD', cccdExtractionResult.idNumber, isDarkMode),
                  _buildDataItem('Họ tên', cccdExtractionResult.fullName, isDarkMode),
                  _buildDataItem('Ngày sinh', cccdExtractionResult.dateOfBirth, isDarkMode),
                  _buildDataItem('Giới tính', cccdExtractionResult.gender, isDarkMode),
                  _buildDataItem('Quốc tịch', cccdExtractionResult.nationality, isDarkMode),
                  _buildDataItem('Nơi cấp', cccdExtractionResult.issuePlace, isDarkMode),
                  _buildDataItem('Ngày cấp', cccdExtractionResult.issueDate, isDarkMode),
                  _buildDataItem('Ngày hết hạn', cccdExtractionResult.expiryDate, isDarkMode),
                ],
              ),
              
              // Full width fields for long content
              if (cccdExtractionResult.placeOfOrigin.isNotEmpty)
                  SizedBox(height: AppDimensions.spacingM),
                _buildFullWidthDataItem('Quê quán', cccdExtractionResult.placeOfOrigin, isDarkMode),
              if (cccdExtractionResult.placeOfResidence.isNotEmpty) ...[
                if (cccdExtractionResult.placeOfOrigin.isNotEmpty)
                  SizedBox(height: AppDimensions.spacingM),
                _buildFullWidthDataItem('Địa chỉ', cccdExtractionResult.placeOfResidence, isDarkMode),
              ],
            ],
          ),

          // Warnings
          if (cccdExtractionResult.warnings.isNotEmpty) ...[
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.warning,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        TablerIcons.alert_triangle,
                        color: AppColors.warning,
                        size: 16,
                      ),
                      SizedBox(width: AppDimensions.spacingXS),
                      Text(
                        'Cảnh báo',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: AppColors.warning,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingXS),
                  ...cccdExtractionResult.warnings.map((warning) => Padding(
                    padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
                    child: Text(
                      '• $warning',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.warning,
                      ),
                    ),
                  )),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDataItem(String label, String value, bool isDarkMode) {
    if (value.isEmpty) return SizedBox.shrink();
    
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.5)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: isDarkMode ? AppColors.neutral100 : AppColors.neutral900,
                fontSize: 11,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 2,
              softWrap: true,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullWidthDataItem(String label, String value, bool isDarkMode) {
    if (value.isEmpty) return SizedBox.shrink();
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.neutral800.withValues(alpha: 0.5)
            : AppColors.neutral100,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral400 : AppColors.neutral600,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 4),
          Text(
            value,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode ? AppColors.neutral100 : AppColors.neutral900,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
            maxLines: null,
            softWrap: true,
          ),
        ],
      ),
    );
  }
} 