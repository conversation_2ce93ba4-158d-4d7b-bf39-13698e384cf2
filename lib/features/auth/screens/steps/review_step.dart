import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

class ReviewStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const ReviewStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<ReviewStep> createState() => _ReviewStepState();
}

class _ReviewStepState extends State<ReviewStep> {
  bool _confirmAccuracy = false;
  bool _agreeToSubmit = false;

  @override
  void initState() {
    super.initState();
    // Initialize checkbox states from registration data
    _confirmAccuracy = widget.registrationData['confirmAccuracy'] ?? false;
    _agreeToSubmit = widget.registrationData['agreeToSubmit'] ?? false;
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: widget.form<PERSON><PERSON>,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            _buildIntroduction(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Basic Information Section
            _buildInformationSection(
              title: 'Thông tin cơ bản',
              icon: TablerIcons.user,
              items: [
                {
                  'label': 'Email',
                  'value': widget.registrationData['email'] ?? '',
                },
                {
                  'label': 'Số điện thoại',
                  'value': widget.registrationData['phone'] ?? '',
                },
                {
                  'label': 'Vai trò',
                  'value': widget.registrationData['role'] ?? '',
                },
                if (widget.registrationData['referralCode']?.isNotEmpty == true)
                  {
                    'label': 'Mã người giới thiệu',
                    'value': widget.registrationData['referralCode'],
                  },
              ],
              isDarkMode: isDarkMode,
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Location Information Section
            _buildInformationSection(
              title: 'Thông tin vị trí',
              icon: TablerIcons.map_pin,
              items: [
                {
                  'label': 'Tỉnh/Thành phố',
                  'value': widget.registrationData['province'] ?? '',
                },
                {
                  'label': 'Chi nhánh',
                  'value': widget.registrationData['branch'] ?? '',
                },
              ],
              isDarkMode: isDarkMode,
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Identity Verification Section
            _buildVerificationSection(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // NFC Information Section (if available)
            if (widget.registrationData['nfcData'] != null) ...[
              _buildNfcDataSection(isDarkMode),
              SizedBox(height: AppDimensions.spacingL),
            ],
            
            // CCCD Extraction Information Section (if available and NFC skipped)
            if (widget.registrationData['cccdExtractionData'] != null && 
                widget.registrationData['nfcSkipped'] == true) ...[
              _buildCccdExtractionDataSection(isDarkMode),
              SizedBox(height: AppDimensions.spacingL),
            ],

            // Confirmation Checkboxes
            _buildConfirmationSection(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Important Notice
            _buildImportantNotice(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroduction(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            TablerIcons.clipboard_check,
            color: AppColors.kienlongSkyBlue,
            size: 24,
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Vui lòng kiểm tra lại toàn bộ thông tin trước khi hoàn tất đăng ký. Đảm bảo tất cả thông tin chính xác và đầy đủ.',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInformationSection({
    required String title,
    required IconData icon,
    required List<Map<String, String>> items,
    required bool isDarkMode,
  }) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(icon, color: AppColors.kienlongOrange, size: 20),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Text(
                title,
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Information Items
          ...items.map(
            (item) => Container(
              margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 120,
                    child: Text(
                      '${item['label']}:',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      item['value'] ?? '',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationSection(bool isDarkMode) {
    final hasPhotos =
        widget.registrationData['frontIdPhoto'] != null &&
        widget.registrationData['backIdPhoto'] != null;
    final hasNfc = widget.registrationData['nfcCompleted'] == true;
    final hasNfcSkipped = widget.registrationData['nfcSkipped'] == true;
    final hasCccdData = widget.registrationData['cccdExtractionData'] != null;
    
    // NFC is considered complete if either completed or skipped with CCCD data
    final nfcStatus = hasNfc || (hasNfcSkipped && hasCccdData);

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  TablerIcons.shield_check,
                  color: AppColors.success,
                  size: 20,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Text(
                'Xác minh danh tính',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Photos Status
          _buildVerificationItem(
            label: 'Ảnh CCCD',
            isCompleted: hasPhotos,
            description: hasPhotos
                ? 'Đã chụp ảnh mặt trước và mặt sau'
                : 'Chưa hoàn thành',
          ),

          SizedBox(height: AppDimensions.spacingS),

          // NFC Status
          _buildVerificationItem(
            label: 'Quét NFC',
            isCompleted: nfcStatus,
            description: hasNfc
                ? 'Đã đọc thông tin từ chip CCCD'
                : hasNfcSkipped && hasCccdData
                    ? 'Đã bỏ qua - sử dụng thông tin từ CCCD extraction'
                    : 'Chưa hoàn thành',
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationItem({
    required String label,
    required bool isCompleted,
    required String description,
  }) {
    return Row(
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: isCompleted ? AppColors.success : AppColors.warning,
            shape: BoxShape.circle,
          ),
          child: Icon(
            isCompleted ? TablerIcons.check : TablerIcons.clock,
            color: Colors.white,
            size: 14,
          ),
        ),
        SizedBox(width: AppDimensions.spacingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              Text(
                description,
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: isCompleted ? AppColors.success : AppColors.warning,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNfcDataSection(bool isDarkMode) {
    final nfcData = widget.registrationData['nfcData'] as Map<String, dynamic>;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  TablerIcons.nfc,
                  color: AppColors.success,
                  size: 20,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Text(
                'Thông tin từ CCCD (NFC)',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.success,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // NFC Data Display
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...nfcData.entries.map(
                  (entry) => Container(
                    margin: EdgeInsets.only(bottom: AppDimensions.spacingXS),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 120,
                          child: Text(
                            '${_getNfcFieldLabel(entry.key)}:',
                            style: AppTypography.textTheme.bodySmall?.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withValues(alpha: 0.7),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            entry.value.toString(),
                            style: AppTypography.textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCccdExtractionDataSection(bool isDarkMode) {
    final cccdData = widget.registrationData['cccdExtractionData'] as Map<String, dynamic>;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  TablerIcons.camera,
                  color: AppColors.warning,
                  size: 20,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Text(
                'Thông tin từ CCCD (AI Extraction)',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Notice about skipped NFC
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: AppColors.warning.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.info_circle,
                  color: AppColors.warning,
                  size: 16,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    'Thông tin được trích xuất từ ảnh CCCD. Bạn có thể cập nhật thông tin NFC sau trong phần Tài khoản.',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: AppDimensions.spacingM),

          // CCCD Data Display
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCccdDataItem('Họ và tên', cccdData['fullName'] ?? ''),
                _buildCccdDataItem('Số CCCD', cccdData['idNumber'] ?? ''),
                _buildCccdDataItem('Ngày sinh', cccdData['dateOfBirth'] ?? ''),
                _buildCccdDataItem('Giới tính', cccdData['gender'] ?? ''),
                _buildCccdDataItem('Ngày cấp', cccdData['issueDate'] ?? ''),
                _buildCccdDataItem('Nơi cấp', cccdData['issuePlace'] ?? ''),
                _buildCccdDataItem('Ngày hết hạn', cccdData['expiryDate'] ?? ''),
                _buildCccdDataItem('Quê quán', cccdData['placeOfOrigin'] ?? ''),
                _buildCccdDataItem('Địa chỉ', cccdData['placeOfResidence'] ?? ''),
                _buildCccdDataItem('Quốc tịch', cccdData['nationality'] ?? ''),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCccdDataItem(String label, String value) {
    if (value.isEmpty) return const SizedBox.shrink();
    
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmationSection(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Row(
            children: [
              Icon(
                TablerIcons.shield_check,
                color: AppColors.kienlongOrange,
                size: 24,
              ),
              SizedBox(width: AppDimensions.spacingM),
              Text(
                'Xác nhận thông tin',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Accuracy Confirmation
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: Checkbox(
                  value: _confirmAccuracy,
                  onChanged: (value) {
                    setState(() {
                      _confirmAccuracy = value ?? false;
                      widget.registrationData['confirmAccuracy'] =
                          _confirmAccuracy;
                    });
                  },
                  activeColor: AppColors.kienlongOrange,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  'Tôi xác nhận rằng tất cả thông tin trên là chính xác và đầy đủ',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Submit Agreement
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: Checkbox(
                  value: _agreeToSubmit,
                  onChanged: (value) {
                    setState(() {
                      _agreeToSubmit = value ?? false;
                      widget.registrationData['agreeToSubmit'] = _agreeToSubmit;
                    });
                  },
                  activeColor: AppColors.kienlongOrange,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  'Tôi đồng ý gửi đơn đăng ký và hiểu rằng thông tin sẽ được xem xét bởi bộ phận nhân sự',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ),
            ],
          ),

          // Validation Messages
          if (!_confirmAccuracy || !_agreeToSubmit) ...[
            SizedBox(height: AppDimensions.spacingM),
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.warning.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.warning.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.alert_triangle,
                    color: AppColors.warning,
                    size: 16,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Vui lòng xác nhận tất cả thông tin để tiếp tục',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.warning,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImportantNotice(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(TablerIcons.info_circle, color: AppColors.info, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Thông báo quan trọng',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.info,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          Text(
            '• Đơn đăng ký sẽ được xem xét trong vòng 24-48 giờ làm việc\n'
            '• Bạn sẽ nhận được thông báo qua email về kết quả xét duyệt\n'
            '• Trong trường hợp cần bổ sung thông tin, chúng tôi sẽ liên hệ trực tiếp\n'
            '• Tài khoản sẽ được kích hoạt sau khi xét duyệt thành công',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.8),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  String _getNfcFieldLabel(String key) {
    switch (key) {
      case 'fullName':
        return 'Họ và tên';
      case 'idNumber':
        return 'Số CCCD';
      case 'dateOfBirth':
        return 'Ngày sinh';
      case 'placeOfBirth':
        return 'Nơi sinh';
      case 'address':
        return 'Địa chỉ';
      case 'issueDate':
        return 'Ngày cấp';
      case 'expiryDate':
        return 'Ngày hết hạn';
      default:
        return key;
    }
  }

  bool get canSubmit => _confirmAccuracy && _agreeToSubmit;
}
