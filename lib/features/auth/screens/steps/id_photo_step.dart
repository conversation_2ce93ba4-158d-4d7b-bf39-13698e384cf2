import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../../../shared/widgets/index.dart';
import '../../../../shared/models/cccd_extraction_model.dart';
import '../../../../shared/services/cccd_extraction_service.dart';
import '../../blocs/index.dart';
import '../../widgets/cccd_camera_screen.dart';
import 'widgets/cccd_extraction_result_section.dart';
import 'widgets/cccd_extraction_indicator.dart';
import 'widgets/mrz_result_section.dart';
import 'widgets/mrz_scanning_indicator.dart';
import 'widgets/id_photo_introduction.dart';
import 'widgets/id_photo_guidelines.dart';
import 'widgets/id_photo_section.dart';
import 'widgets/id_photo_completion_status.dart';

class IdPhotoStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const IdPhotoStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<IdPhotoStep> createState() => _IdPhotoStepState();
}

class _IdPhotoStepState extends State<IdPhotoStep> {
  // Photo paths
  String? get _frontPhoto => widget.registrationData['frontIdPhoto'];
  String? get _backPhoto => widget.registrationData['backIdPhoto'];
  bool get _hasBothPhotos => _frontPhoto != null && _backPhoto != null;

  // Document IDs for tracking
  String? _frontPhotoDocumentId;
  String? _backPhotoDocumentId;

  @override
  void initState() {
    super.initState();
    _initializePhotos();
  }

  void _initializePhotos() {
    // Initialize photo document IDs if photos exist
    if (_frontPhoto != null) {
      _frontPhotoDocumentId = _frontPhoto;
    }
    if (_backPhoto != null) {
      _backPhotoDocumentId = _backPhoto;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _safeNavigatorPop() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  Future<void> _takePhoto(BuildContext context, String photoType) async {
    try {
      final Map<String, String>? result = await Navigator.of(context)
          .push<Map<String, String>>(
            MaterialPageRoute(
              builder: (context) => CccdCameraScreen(
                photoType: photoType,
                onPhotoTaken: (imagePath) => imagePath,
              ),
            ),
          );

      if (result != null) {
        // Sử dụng ảnh resized để lưu trữ (tối ưu storage)
        final imagePath = result['resized'] ?? result['original']!;

        // Cập nhật ảnh mới
        setState(() {
          widget.registrationData['${photoType}IdPhoto'] = imagePath;
        });

        // Reset logic thông minh dựa trên loại ảnh
        
        if (photoType == 'front') {
          // Chụp lại ảnh mặt trước: reset CCCD extraction vì dữ liệu sẽ thay đổi
          if (context.mounted) {
            context.read<CccdExtractionBloc>().add(
              const ResetCccdExtractionEvent(),
            );
          }
          // Không reset MRZ scan vì ảnh mặt sau không thay đổi
        } else if (photoType == 'back') {
          // Chụp lại ảnh mặt sau: reset cả CCCD extraction và MRZ scan
          if (context.mounted) {
            context.read<CccdExtractionBloc>().add(
              const ResetCccdExtractionEvent(),
            );
            context.read<MrzScanBloc>().add(const ResetMrzScanEvent());
          }
        }
        

        // Tự động scan MRZ nếu là ảnh mặt sau (dùng ảnh resized)
        if (photoType == 'back') {
          _autoScanMrzFromBackPhoto(imagePath);
        }

        // Tự động extract CCCD nếu có đủ ảnh mặt trước và mặt sau
        if (_hasBothPhotos) {
          _autoExtractCccdInfo();
        }

        if (context.mounted) {
          CustomSnackBar.show(
            context,
            message:
                'Đã chụp ảnh ${photoType == 'front' ? 'mặt trước' : 'mặt sau'} thành công',
            type: SnackBarType.success,
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        CustomSnackBar.show(
          context,
          message: 'Có lỗi khi chụp ảnh. Vui lòng thử lại.',
          type: SnackBarType.error,
        );
      }
    }
  }

  void _retakePhoto(BuildContext context, String photoType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Chụp lại ảnh?',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'Bạn có muốn chụp lại ảnh ${photoType == 'front' ? 'mặt trước' : 'mặt sau'} CCCD không?',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _safeNavigatorPop,
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            child: Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              _safeNavigatorPop();
              _takePhoto(context, photoType);
            },
            style: TextButton.styleFrom(
              foregroundColor: AppColors.kienlongOrange,
            ),
            child: Text('Chụp lại'),
          ),
        ],
      ),
    );
  }

  /// Tự động scan MRZ từ ảnh mặt sau
  void _autoScanMrzFromBackPhoto(String imagePath) {
    // Lấy CCCD extraction result hiện tại để fallback
    final cccdState = context.read<CccdExtractionBloc>().state;
    CccdExtractionModel? cccdResult;
    if (cccdState is CccdExtractionSuccess) {
      cccdResult = cccdState.result;
    }

    context.read<MrzScanBloc>().add(
      ScanMrzFromImageEvent(
        imagePath: imagePath,
        cccdExtractionResult: cccdResult,
      ),
    );
  }

  /// Tự động extract thông tin CCCD
  Future<void> _autoExtractCccdInfo() async {
    if (!_hasBothPhotos) return;

    context.read<CccdExtractionBloc>().add(
      ExtractCccdEvent(
        frontImagePath: _frontPhoto!,
        backImagePath: _backPhoto!,
      ),
    );
  }

  /// Quét lại MRZ từ ảnh mặt sau
  void _rescanMrz() {
    if (_backPhoto == null) return;

    // Lấy CCCD extraction result hiện tại để fallback
    final cccdState = context.read<CccdExtractionBloc>().state;
    CccdExtractionModel? cccdResult;
    if (cccdState is CccdExtractionSuccess) {
      cccdResult = cccdState.result;
    }

    context.read<MrzScanBloc>().add(
      RescanMrzEvent(imagePath: _backPhoto!, cccdExtractionResult: cccdResult),
    );
  }

  /// Extract lại thông tin CCCD
  void _reextractCccd() {
    if (!_hasBothPhotos) return;

    context.read<CccdExtractionBloc>().add(
      ReextractCccdEvent(
        frontImagePath: _frontPhoto!,
        backImagePath: _backPhoto!,
      ),
    );
  }

  /// Hiển thị dialog lỗi khi không đọc được MRZ
  void _showMrzErrorDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            Icon(TablerIcons.x, color: AppColors.error),
            SizedBox(width: AppDimensions.spacingS),
            Text(
              'Không đọc được mã MRZ',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        content: Text(
          'Không thể đọc được mã MRZ từ ảnh CCCD. Có thể do:\n\n'
          '• Ảnh không rõ nét\n'
          '• Vùng mã MRZ bị che khuất\n'
          '• Góc chụp không phù hợp\n\n'
          'Bạn có thể thử chụp lại ảnh mặt sau.',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _safeNavigatorPop,
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            child: Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              _safeNavigatorPop();
              _retakePhoto(context, 'back');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: Text('Chụp lại'),
          ),
        ],
      ),
    );
  }

  /// Hiển thị dialog lỗi khi không extract được CCCD
  void _showCccdExtractionErrorDialog(CccdExtractionException? exception) {
    String title = 'Không thể trích xuất thông tin CCCD';
    String content =
        'Không thể trích xuất thông tin từ ảnh CCCD. Có thể do:\n\n'
        '• Ảnh không rõ nét\n'
        '• CCCD bị che khuất một phần\n'
        '• Góc chụp không phù hợp\n'
        '• CCCD đã hết hạn hoặc sắp hết hạn\n\n'
        'Bạn có thể thử chụp lại ảnh CCCD.';

    if (exception != null) {
      switch (exception.type) {
        case CccdExtractionExceptionType.poorImageQuality:
          title = 'Chất lượng ảnh kém';
          content =
              'Chất lượng ảnh CCCD không đủ tốt để trích xuất thông tin. '
              'Vui lòng chụp lại với ánh sáng tốt hơn và đảm bảo ảnh rõ nét.';
          break;
        case CccdExtractionExceptionType.cccdExpired:
          title = 'CCCD đã hết hạn';
          content =
              'CCCD đã hết hạn. Vui lòng sử dụng CCCD còn hạn để đăng ký.';
          break;
        case CccdExtractionExceptionType.cccdExpiringSoon:
          title = 'CCCD sắp hết hạn';
          content =
              'CCCD sắp hết hạn. Vui lòng gia hạn CCCD trước khi đăng ký.';
          break;
        case CccdExtractionExceptionType.extractionFailed:
          title = 'Trích xuất thất bại';
          content =
              'Không thể trích xuất thông tin từ CCCD. Vui lòng thử lại hoặc chụp lại ảnh.';
          break;
        default:
          title = 'Lỗi trích xuất';
          content =
              'Có lỗi xảy ra khi trích xuất thông tin: ${exception.message}';
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            Icon(TablerIcons.x, color: AppColors.error),
            SizedBox(width: AppDimensions.spacingS),
            Text(
              title,
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        content: Text(
          content,
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _safeNavigatorPop,
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            child: Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              _safeNavigatorPop();
              _retakePhoto(context, 'front');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: Text('Chụp lại'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return MultiBlocListener(
      listeners: [
        BlocListener<CccdExtractionBloc, CccdExtractionState>(
          listener: (context, state) {
            if (state is CccdExtractionSuccess) {
              // Lưu kết quả vào registration data
              widget.registrationData['cccdExtractionData'] = state.result
                  .toJson();

              CustomSnackBar.show(
                context,
                message: 'Đã trích xuất thông tin CCCD thành công',
                type: SnackBarType.success,
              );
            } else if (state is CccdExtractionError) {
              _showCccdExtractionErrorDialog(state.exception);
            }
          },
        ),
        BlocListener<MrzScanBloc, MrzScanState>(
          listener: (context, state) {
            if (state is MrzScanSuccess) {
              // Lưu MRZ data vào registration data
              final mrzDataModel = state.result.toSerializable();
              widget.registrationData['mrzData'] = mrzDataModel.toJson();

              final message = state.usedFallback
                  ? 'Đã sử dụng MRZ từ dữ liệu CCCD (${state.result.rawLines.length} dòng)'
                  : state.result.isSuccess
                  ? 'Đã đọc và parse thành công ${state.result.rawLines.length} dòng MRZ'
                  : 'Đã đọc được ${state.result.rawLines.length} dòng MRZ (parse thất bại)';

              CustomSnackBar.show(
                context,
                message: message,
                type: state.usedFallback
                    ? SnackBarType.info
                    : state.result.isSuccess
                    ? SnackBarType.success
                    : SnackBarType.warning,
              );
            } else if (state is MrzScanError) {
              _showMrzErrorDialog();
            }
          },
        ),
      ],
      child: Form(
        key: widget.formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Introduction
              IdPhotoIntroduction(),

              SizedBox(height: AppDimensions.spacingL),

              // ID Card Guidelines
              IdPhotoGuidelines(),

              SizedBox(height: AppDimensions.spacingL),

              // Front Photo Section
              IdPhotoSection(
                title: 'Ảnh mặt trước CCCD',
                description: 'Chụp ảnh mặt trước có thông tin cá nhân',
                photoType: 'front',
                photo: _frontPhoto,
                isDarkMode: isDarkMode,
                hasDocumentId: _frontPhotoDocumentId != null,
                onTakePhoto: () => _takePhoto(context, 'front'),
                onRetakePhoto: () => _retakePhoto(context, 'front'),
              ),

              SizedBox(height: AppDimensions.spacingL),

              // Back Photo Section
              IdPhotoSection(
                title: 'Ảnh mặt sau CCCD',
                description: 'Chụp ảnh mặt sau có chip NFC',
                photoType: 'back',
                photo: _backPhoto,
                isDarkMode: isDarkMode,
                hasDocumentId: _backPhotoDocumentId != null,
                onTakePhoto: () => _takePhoto(context, 'back'),
                onRetakePhoto: () => _retakePhoto(context, 'back'),
              ),

              if (_hasBothPhotos) ...[
                SizedBox(height: AppDimensions.spacingL),
                IdPhotoCompletionStatus(),
              ],

              // Hiển thị kết quả CCCD extraction nếu có
              BlocBuilder<CccdExtractionBloc, CccdExtractionState>(
                builder: (context, state) {
                  if (state is CccdExtractionSuccess) {
                    return Column(
                      children: [
                        SizedBox(height: AppDimensions.spacingL),
                        CccdExtractionResultSection(
                          cccdExtractionResult: state.result,
                          isDarkMode: isDarkMode,
                          onReextract: _reextractCccd,
                        ),
                      ],
                    );
                  } else if (state is CccdExtractionLoading) {
                    return Column(
                      children: [
                        SizedBox(height: AppDimensions.spacingL),
                        CccdExtractionIndicator(isDarkMode: isDarkMode),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Hiển thị kết quả MRZ nếu có
              BlocBuilder<MrzScanBloc, MrzScanState>(
                builder: (context, state) {
                  if (state is MrzScanSuccess) {
                    return Column(
                      children: [
                        SizedBox(height: AppDimensions.spacingL),
                        MrzResultSection(
                          mrzScanResult: state.result,
                          isDarkMode: isDarkMode,
                          onRescan: _rescanMrz,
                        ),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Hiển thị loading khi đang scan MRZ
              BlocBuilder<MrzScanBloc, MrzScanState>(
                builder: (context, state) {
                  if (state is MrzScanLoading) {
                    return Column(
                      children: [
                        SizedBox(height: AppDimensions.spacingL),
                        MrzScanningIndicator(isDarkMode: isDarkMode),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              SizedBox(height: AppDimensions.spacingL),
            ],
          ),
        ),
      ),
    );
  }
}
