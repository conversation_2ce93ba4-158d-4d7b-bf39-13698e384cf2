import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

class BasicInfoStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const BasicInfoStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<BasicInfoStep> createState() => _BasicInfoStepState();
}

class _BasicInfoStepState extends State<BasicInfoStep> {
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _referralController;

  final List<String> _roles = ['Cộng tác viên', 'Nhân viên ngân hàng'];

  String? get _selectedRole =>
      widget.registrationData['role']?.isNotEmpty == true
      ? widget.registrationData['role']
      : null;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController(
      text: widget.registrationData['email'] ?? '',
    );
    _phoneController = TextEditingController(
      text: widget.registrationData['phone'] ?? '',
    );
    _referralController = TextEditingController(
      text: widget.registrationData['referralCode'] ?? '',
    );

    // Listen to changes
    _emailController.addListener(() {
      widget.registrationData['email'] = _emailController.text;
    });
    _phoneController.addListener(() {
      widget.registrationData['phone'] = _phoneController.text;
    });
    _referralController.addListener(() {
      widget.registrationData['referralCode'] = _referralController.text;
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    _referralController.dispose();
    super.dispose();
  }

  void _setSelectedRole(String? role) {
    setState(() {
      widget.registrationData['role'] = role ?? '';
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            _buildIntroduction(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Email Field
            _buildEmailField(),

            SizedBox(height: AppDimensions.spacingL),

            // Phone Field
            _buildPhoneField(),

            SizedBox(height: AppDimensions.spacingL),

            // Role Selection
            _buildRoleSelection(isDarkMode),

            SizedBox(height: AppDimensions.spacingL),

            // Referral Code Field
            _buildReferralCodeField(),

            SizedBox(height: AppDimensions.spacingL),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroduction(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            TablerIcons.info_circle,
            color: AppColors.kienlongSkyBlue,
            size: 24,
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Điền thông tin cơ bản để tạo tài khoản. Thông tin này sẽ được sử dụng để xác minh danh tính của bạn.',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email',
          style: AppTypography.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          decoration: InputDecoration(
            hintText: 'Nhập địa chỉ email',
            prefixIcon: Icon(
              TablerIcons.mail,
              color: AppColors.kienlongSkyBlue,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: AppColors.kienlongSkyBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập email';
            }
            if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value.trim())) {
              return 'Email không hợp lệ';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Số điện thoại',
          style: AppTypography.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        TextFormField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          textInputAction: TextInputAction.next,
          decoration: InputDecoration(
            hintText: 'Nhập số điện thoại',
            prefixIcon: Icon(
              TablerIcons.phone,
              color: AppColors.kienlongSkyBlue,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: AppColors.kienlongSkyBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập số điện thoại';
            }
            if (!RegExp(r'^[0-9]{10,11}$').hasMatch(value.trim())) {
              return 'Số điện thoại không hợp lệ (10-11 số)';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildRoleSelection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Vai trò',
          style: AppTypography.textTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),

        // Role selection description
        Container(
          margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
          child: Text(
            'Chọn vai trò phù hợp với vị trí công việc của bạn',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ),

        // Radio button options
        Column(
          children: _roles.map((String role) {
            final isSelected = _selectedRole == role;
            return GestureDetector(
              onTap: () => _setSelectedRole(role),
              child: Container(
                margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.kienlongOrange
                        : isDarkMode
                        ? AppColors.borderDark.withValues(alpha: 0.3)
                        : AppColors.borderLight,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Row(
                  children: [
                    // Custom radio icon
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? AppColors.kienlongOrange
                              : Theme.of(
                                  context,
                                ).colorScheme.outline.withValues(alpha: 0.5),
                          width: 2,
                        ),
                        color: isSelected
                            ? AppColors.kienlongOrange
                            : Colors.transparent,
                      ),
                      child: isSelected
                          ? Center(
                              child: Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.white,
                                ),
                              ),
                            )
                          : null,
                    ),

                    SizedBox(width: AppDimensions.spacingM),

                    // Role icon
                    Icon(
                      role == 'Nhân viên ngân hàng'
                          ? TablerIcons.briefcase
                          : TablerIcons.user_circle,
                      color: isSelected
                          ? AppColors.kienlongOrange
                          : Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.6),
                      size: 24,
                    ),

                    SizedBox(width: AppDimensions.spacingM),

                    // Role text and description
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            role,
                            style: AppTypography.textTheme.bodyLarge?.copyWith(
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              color: isSelected
                                  ? AppColors.kienlongOrange
                                  : Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          SizedBox(height: 2),
                          Text(
                            role == 'Nhân viên ngân hàng'
                                ? 'Nhân viên chính thức của ngân hàng'
                                : 'Cộng tác viên bán hàng',
                            style: AppTypography.textTheme.bodySmall?.copyWith(
                              color: Theme.of(
                                context,
                              ).colorScheme.onSurface.withValues(alpha: 0.6),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),

        // Validation message
        if (_selectedRole == null) ...[
          SizedBox(height: AppDimensions.spacingS),
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: AppColors.warning.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.alert_triangle,
                  color: AppColors.warning,
                  size: 16,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    'Vui lòng chọn vai trò',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: AppColors.warning,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildReferralCodeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Mã người giới thiệu',
              style: AppTypography.textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(width: AppDimensions.spacingS),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Text(
                'Tùy chọn',
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: AppColors.info,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: AppDimensions.spacingS),

        Text(
          'Nhập mã người giới thiệu nếu có (không bắt buộc)',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),

        SizedBox(height: AppDimensions.spacingS),

        TextFormField(
          controller: _referralController,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.done,
          textCapitalization: TextCapitalization.characters,
          decoration: InputDecoration(
            hintText: 'Nhập mã người giới thiệu',
            prefixIcon: Icon(
              TablerIcons.user_plus,
              color: AppColors.kienlongSkyBlue,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: AppColors.kienlongSkyBlue,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
          ),
          validator: (value) {
            // Optional field, so only validate if provided
            if (value != null && value.trim().isNotEmpty) {
              if (value.trim().length < 3) {
                return 'Mã người giới thiệu phải có ít nhất 3 ký tự';
              }
            }
            return null;
          },
        ),
      ],
    );
  }
}
