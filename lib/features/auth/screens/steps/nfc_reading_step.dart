import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/theme/index.dart';
import '../../../../shared/utils/app_logger.dart';
import '../../../../shared/widgets/index.dart';
import 'package:dmrtd/dmrtd.dart';
// Import Core NFC Parser Services
import '../../../../core/nfc/index.dart';
// Import Blocs
import '../../blocs/index.dart';
// Import Models
import '../../../../shared/models/cccd_extraction_model.dart';
import '../../models/mrz_data_model.dart';
import '../../services/mrz_scanner_service.dart';

class NfcReadingStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> registrationData;

  const NfcReadingStep({
    super.key,
    required this.formKey,
    required this.registrationData,
  });

  @override
  State<NfcReadingStep> createState() => _NfcReadingStepState();
}

class _NfcReadingStepState extends State<NfcReadingStep>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scanController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scanAnimation;

  bool _isNfcAvailable = false;
  bool _hasExistingCccdData = false;

  // AppLogger instance
  final AppLogger _logger = AppLogger();

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scanController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _scanAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scanController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);

    // Initialize AppLogger
    _logger.initialize().catchError((error) {
      // Silently handle initialization error
      debugPrint('Failed to initialize AppLogger: $error');
    });

    // Check NFC availability
    _checkNfcAvailability();
    
    // Check if we have existing CCCD data
    _checkExistingCccdData();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scanController.dispose();
    super.dispose();
  }

  Future<void> _checkNfcAvailability() async {
    try {
      final nfcStatus = await NfcProvider.nfcStatus;
      setState(() {
        _isNfcAvailable = nfcStatus == NfcStatus.enabled;
      });
    } catch (e) {
      setState(() {
        _isNfcAvailable = false;
      });
    }
  }

  void _checkExistingCccdData() {
    final cccdExtractionData = widget.registrationData['cccdExtractionData'];
    setState(() {
      _hasExistingCccdData = cccdExtractionData != null;
    });
  }

  void _startNfcScan() {
    // Reset NFC skip status when user starts scanning
    if (widget.registrationData['nfcSkipped'] == true) {
      widget.registrationData['nfcSkipped'] = false;
      widget.registrationData['nfcCompleted'] = false;
      widget.registrationData['primaryDataSource'] = null;
      _logger.i('Reset NFC skip status - user is scanning again');
    }
    
    // Get data from previous steps
    final cccdExtractionData = widget.registrationData['cccdExtractionData'];
    final mrzData = widget.registrationData['mrzData'];
    
    // Debug: Log what we found
    debugPrint('NFC Step - CCCD Extraction Data: $cccdExtractionData');
    debugPrint('NFC Step - MRZ Data: $mrzData');
    
    // Convert back to models
    CccdExtractionModel? cccdExtractionResult;
    MrzScanResult? mrzScanResult;
    
    if (cccdExtractionData != null) {
      try {
        cccdExtractionResult = CccdExtractionModel.fromJson(cccdExtractionData);
        debugPrint('NFC Step - Successfully parsed CCCD data: ${cccdExtractionResult.idNumber}');
      } catch (e) {
        debugPrint('Error parsing CCCD extraction data: $e');
      }
    }
    
    if (mrzData != null) {
      try {
        final mrzDataModel = MrzDataModel.fromJson(mrzData);
        // Create MrzScanResult from MrzDataModel using the new method
        mrzScanResult = MrzScanResult.fromSerializable(mrzDataModel);
        debugPrint('NFC Step - Successfully parsed MRZ data: ${mrzDataModel.rawLines.length} lines, isSuccess: ${mrzScanResult.isSuccess}');
      } catch (e) {
        debugPrint('Error parsing MRZ data: $e');
      }
    }

    // Start NFC reading with Bloc
    context.read<NfcReadingBloc>().add(StartNfcReadingEvent(
      cccdExtractionResult: cccdExtractionResult,
      mrzScanResult: mrzScanResult,
    ));
  }

  void _retryNfcScan() {
    // Reset NFC skip status when user retries scanning
    if (widget.registrationData['nfcSkipped'] == true) {
      widget.registrationData['nfcSkipped'] = false;
      widget.registrationData['nfcCompleted'] = false;
      widget.registrationData['primaryDataSource'] = null;
      _logger.i('Reset NFC skip status - user is retrying scan');
    }
    
    // Get data from previous steps
    final cccdExtractionData = widget.registrationData['cccdExtractionData'];
    final mrzData = widget.registrationData['mrzData'];
    
    // Convert back to models
    CccdExtractionModel? cccdExtractionResult;
    MrzScanResult? mrzScanResult;
    
    if (cccdExtractionData != null) {
      try {
        cccdExtractionResult = CccdExtractionModel.fromJson(cccdExtractionData);
      } catch (e) {
        debugPrint('Error parsing CCCD extraction data: $e');
      }
    }
    
    if (mrzData != null) {
      try {
        final mrzDataModel = MrzDataModel.fromJson(mrzData);
        // Create MrzScanResult from MrzDataModel using the new method
        mrzScanResult = MrzScanResult.fromSerializable(mrzDataModel);
        debugPrint('NFC Step - Successfully parsed MRZ data: ${mrzDataModel.rawLines.length} lines, isSuccess: ${mrzScanResult.isSuccess}');
      } catch (e) {
        debugPrint('Error parsing MRZ data: $e');
      }
    }

    // Retry NFC reading with Bloc
    context.read<NfcReadingBloc>().add(RetryNfcReadingEvent(
      cccdExtractionResult: cccdExtractionResult,
      mrzScanResult: mrzScanResult,
    ));
  }



  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return BlocListener<NfcReadingBloc, NfcReadingState>(
      listener: (context, state) {
        if (state is NfcReadingSuccess) {
          // Save NFC data to registration data
          widget.registrationData['nfcData'] = {
            'fullName': state.nfcData.fullName,
            'idNumber': state.nfcData.documentNumber,
            'dateOfBirth': state.nfcData.dateOfBirth?.toString(),
            'placeOfBirth': state.nfcData.citizenInfo?.placeOfOrigin,
            'address': state.nfcData.citizenInfo?.placeOfResidence,
            'issueDate': state.nfcData.citizenInfo?.dateOfRelease,
            'expiryDate': state.nfcData.citizenInfo?.dateOfExpiry,
          };
          
          // Also save fullName directly to registrationData for easy access
          if (state.nfcData.fullName != null && state.nfcData.fullName!.isNotEmpty) {
            widget.registrationData['fullName'] = state.nfcData.fullName;
          }

          widget.registrationData['nfcCompleted'] = true;
          widget.registrationData['scanCompletedTime'] = DateTime.now().toIso8601String();

          CustomSnackBar.show(
            context,
            message: 'Đọc thông tin NFC thành công!',
            type: SnackBarType.success,
          );
        } else if (state is NfcReadingError) {
          CustomSnackBar.show(
            context,
            message: state.message,
            type: SnackBarType.error,
          );
        }
      },
      child: Form(
        key: widget.formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top content with padding
              Padding(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // NFC Status Indicator with Refresh Overlay
                    Stack(
                      children: [
                        _buildNfcStatusIndicator(isDarkMode),
                        Positioned(
                          top: AppDimensions.spacingS,
                          right: AppDimensions.spacingS,
                          child: Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: IconButton(
                              onPressed: _checkNfcAvailability,
                              icon: Icon(
                                TablerIcons.refresh,
                                color: AppColors.kienlongSkyBlue,
                                size: 18,
                              ),
                              tooltip: 'Làm mới trạng thái NFC',
                              padding: EdgeInsets.all(AppDimensions.spacingS),
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: AppDimensions.spacingL),

                    // Introduction
                    _buildIntroduction(isDarkMode),

                    SizedBox(height: AppDimensions.spacingL),

                    // NFC Instructions
                    _buildNfcInstructions(isDarkMode),
                  ],
                ),
              ),

              SizedBox(height: AppDimensions.spacingL),

              // NFC Scanner Area
              Padding(
                padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
                child: _buildNfcScannerArea(isDarkMode),
              ),

              // Bottom content with padding
              Padding(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: AppDimensions.spacingL),

                    // Skip confirmation status
                    if (widget.registrationData['nfcSkipped'] == true) ...[
                      SizedBox(height: AppDimensions.spacingM),
                      _buildSkipConfirmationStatus(isDarkMode),
                    ],

                    // Retry Button (when there's an error)
                    BlocBuilder<NfcReadingBloc, NfcReadingState>(
                      builder: (context, state) {
                        if (state is NfcReadingError) {
                          return Column(
                            children: [
                              SizedBox(height: AppDimensions.spacingM),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed: _retryNfcScan,
                                  icon: Icon(
                                    TablerIcons.refresh,
                                    size: AppDimensions.iconS,
                                  ),
                                  label: const Text('Thử lại'),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: AppColors.kienlongSkyBlue,
                                    side: BorderSide(color: AppColors.kienlongSkyBlue),
                                    padding: EdgeInsets.symmetric(
                                      vertical: AppDimensions.paddingM,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                        AppDimensions.radiusM,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),

                    // Completion Status
                    BlocBuilder<NfcReadingBloc, NfcReadingState>(
                      builder: (context, state) {
                        if (state is NfcReadingSuccess) {
                          return _buildCompletionStatus(isDarkMode, state);
                        }
                        return const SizedBox.shrink();
                      },
                    ),

                    SizedBox(height: AppDimensions.spacingL),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNfcStatusIndicator(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: _isNfcAvailable
            ? AppColors.success.withValues(alpha: 0.1)
            : AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: _isNfcAvailable
              ? AppColors.success.withValues(alpha: 0.3)
              : AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isNfcAvailable ? TablerIcons.nfc : TablerIcons.nfc_off,
            color: _isNfcAvailable ? AppColors.success : AppColors.warning,
            size: 24,
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isNfcAvailable ? 'NFC đã sẵn sàng' : 'NFC không khả dụng',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: _isNfcAvailable
                        ? AppColors.success
                        : AppColors.warning,
                  ),
                ),
                Text(
                  _isNfcAvailable
                      ? 'Thiết bị hỗ trợ đọc NFC'
                      : 'Vui lòng bật NFC trong cài đặt thiết bị',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIntroduction(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(TablerIcons.nfc, color: AppColors.kienlongSkyBlue, size: 24),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Quét chip NFC trên CCCD để xác minh và lấy thông tin chính xác từ thẻ.',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNfcInstructions(bool isDarkMode) {
    final instructions = [
      'Bật tính năng NFC trên điện thoại của bạn',
      'Đặt mặt sau CCCD (có chip NFC) sát vào mặt sau điện thoại',
      'Giữ thẻ ổn định trong quá trình quét (3-5 giây)',
      'Không di chuyển thẻ cho đến khi quét hoàn tất'
    ];

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(TablerIcons.info_circle, color: AppColors.info, size: 20),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Hướng dẫn quét NFC',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.info,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          ...instructions.asMap().entries.map(
            (entry) => Padding(
              padding: EdgeInsets.only(bottom: AppDimensions.spacingS),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppColors.info,
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${entry.key + 1}',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      entry.value,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNfcScannerArea(bool isDarkMode) {
    return BlocBuilder<NfcReadingBloc, NfcReadingState>(
      builder: (context, state) {
        final isCompleted = state is NfcReadingSuccess;
        final isScanning = state is NfcReadingLoading;
        final errorMessage = state is NfcReadingError ? state.message : null;
        final scanStatus = state is NfcReadingLoading ? state.status : 'Sẵn sàng quét NFC';
        final subScanStatus = state is NfcReadingLoading 
            ? 'Vui lòng giữ CCCD sát mặt sau điện thoại'
            : isCompleted 
                ? 'Thông tin CCCD đã được xác minh thành công'
                : _hasExistingCccdData 
                    ? 'Nhấn "Tiếp tục" để bỏ qua hoặc "Đọc NFC" để xác thực'
                    : 'Vui lòng đọc NFC để tiếp tục';

        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppDimensions.paddingL),
          decoration: BoxDecoration(
            color: isCompleted
                ? AppColors.success.withValues(alpha: 0.05)
                : isScanning
                ? AppColors.kienlongOrange.withValues(alpha: 0.05)
                : isDarkMode
                ? AppColors.neutral800.withValues(alpha: 0.3)
                : AppColors.neutral100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: isCompleted
                  ? AppColors.success.withValues(alpha: 0.3)
                  : isScanning
                  ? AppColors.kienlongOrange.withValues(alpha: 0.5)
                  : isDarkMode
                  ? AppColors.borderDark.withValues(alpha: 0.3)
                  : AppColors.borderLight,
              width: isCompleted || isScanning ? 2 : 1,
            ),
          ),
          child: Column(
            children: [
              // NFC Icon Animation
              AnimatedBuilder(
                animation: isScanning ? _pulseAnimation : _scanAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: isScanning ? _pulseAnimation.value : 1.0,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? AppColors.success.withValues(alpha: 0.1)
                            : isScanning
                            ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                            : AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isCompleted
                              ? AppColors.success
                              : isScanning
                              ? AppColors.kienlongOrange
                              : AppColors.kienlongSkyBlue,
                          width: 2,
                        ),
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Icon(
                            isCompleted ? TablerIcons.check : TablerIcons.nfc,
                            size: 48,
                            color: isCompleted
                                ? AppColors.success
                                : isScanning
                                ? AppColors.kienlongOrange
                                : AppColors.kienlongSkyBlue,
                          ),
                          if (isScanning) ...[
                            AnimatedBuilder(
                              animation: _scanAnimation,
                              builder: (context, child) {
                                return Container(
                                  width: 120 * _scanAnimation.value,
                                  height: 120 * _scanAnimation.value,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppColors.kienlongOrange.withValues(
                                        alpha: 1.0 - _scanAnimation.value,
                                      ),
                                      width: 2,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ],
                        ],
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: AppDimensions.spacingL),

              // Status Text
              Text(
                scanStatus,
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: isCompleted
                      ? AppColors.success
                      : isScanning
                      ? AppColors.kienlongOrange
                      : Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: AppDimensions.spacingS),

              Text(
                subScanStatus,
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),

              if (errorMessage != null) ...[
                SizedBox(height: AppDimensions.spacingM),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
                  padding: EdgeInsets.all(AppDimensions.paddingS),
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    border: Border.all(
                      color: AppColors.error.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            TablerIcons.alert_circle,
                            color: AppColors.error,
                            size: 16,
                          ),
                          SizedBox(width: AppDimensions.spacingS),
                          Expanded(
                            child: Text(
                              'Lỗi đọc NFC',
                              style: AppTypography.textTheme.bodyMedium?.copyWith(
                                color: AppColors.error,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: AppDimensions.spacingS),
                      Text(
                        errorMessage,
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: AppColors.error,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: AppDimensions.spacingS),
                      Text(
                        'Vui lòng kiểm tra:\n• NFC đã được bật trên thiết bị\n• Thẻ CCCD được đặt đúng vị trí\n• Thông tin BAC chính xác\n• Thử lại sau khi đảm bảo các điều kiện trên',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // Optional NFC scan button when CCCD data exists
              if (_hasExistingCccdData && !isCompleted && !isScanning) ...[
                SizedBox(height: AppDimensions.spacingL),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: _isNfcAvailable ? _startNfcScan : null,
                    icon: Icon(
                      TablerIcons.scan,
                      size: AppDimensions.iconS,
                    ),
                    label: Text(_isNfcAvailable 
                        ? 'Bắt đầu quét NFC ngay' 
                        : 'NFC không khả dụng'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppColors.kienlongSkyBlue,
                      side: BorderSide(color: AppColors.kienlongSkyBlue),
                      padding: EdgeInsets.symmetric(
                        vertical: AppDimensions.paddingM,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusM,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }



  Widget _buildCompletionStatus(bool isDarkMode, NfcReadingSuccess state) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.success.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.success,
                  shape: BoxShape.circle,
                ),
                child: Icon(TablerIcons.check, color: Colors.white, size: 24),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hoàn thành quét NFC',
                      style: AppTypography.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                    ),
                    Text(
                      'Đã đọc thành công thông tin từ CCCD',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // BAC Credentials Information
          SizedBox(
            width: double.infinity,
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Thông tin BAC sử dụng:',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.info,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  Text(
                    '• Số hộ chiếu: ${state.usedCredentials.documentNumber}',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '• Ngày sinh: ${state.usedCredentials.dateOfBirth.day.toString().padLeft(2, '0')}/${state.usedCredentials.dateOfBirth.month.toString().padLeft(2, '0')}/${state.usedCredentials.dateOfBirth.year}',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '• Ngày hết hạn: ${state.usedCredentials.dateOfExpiry.day.toString().padLeft(2, '0')}/${state.usedCredentials.dateOfExpiry.month.toString().padLeft(2, '0')}/${state.usedCredentials.dateOfExpiry.year}',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    '• Nguồn: ${state.usedCredentials.source}',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: AppColors.info,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Parsed NFC Data Display
          SizedBox(height: AppDimensions.spacingM),
          NfcDataDisplayWidget(
            nfcData: state.nfcData,
          ),
        ],
      ),
    );
  }

  Widget _buildSkipConfirmationStatus(bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.warning,
                  shape: BoxShape.circle,
                ),
                child: Icon(TablerIcons.clock, color: Colors.white, size: 24),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Đã bỏ qua đọc NFC',
                      style: AppTypography.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.warning,
                      ),
                    ),
                    Text(
                      'Sử dụng thông tin từ CCCD extraction',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: AppColors.info.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Nhắc nhở:',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.info,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingS),
                Text(
                  'Bạn có thể cập nhật thông tin NFC trong phần Tài khoản sau khi hoàn thành đăng ký để xác thực đầy đủ.',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
