import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../core/theme/index.dart';
import '../../../shared/services/navigation_service.dart';
import '../services/auth_service.dart';
import 'login_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _particleController;
  
  late Animation<double> _logoOpacity;
  late Animation<double> _logoScale;
  late Animation<double> _textOpacity;
  late Animation<double> _particleOpacity;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimationSequence();
  }

  void _initAnimations() {
    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Text animation controller
    _textController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Particle animation controller
    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Logo animations
    _logoOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeInOut,
    ));

    _logoScale = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Text animation
    _textOpacity = Tween<double>(
      begin: 0.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    // Particle animation
    _particleOpacity = Tween<double>(
      begin: 0.0,
      end: 0.3,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAnimationSequence() async {
    // Timeline: 0.3s → logo, 0.5s → particles, 1.0s → text
    await Future.delayed(const Duration(milliseconds: 300));
    _logoController.forward();
    
    await Future.delayed(const Duration(milliseconds: 200));
    _particleController.forward();
    
    await Future.delayed(const Duration(milliseconds: 500));
    _textController.forward();
    
    // Navigate to main screen after 2 seconds total
    await Future.delayed(const Duration(milliseconds: 1000));
    if (mounted) {
      _navigateToMainScreen();
    }
  }

  void _navigateToMainScreen() async {
    try {
      // Check current authentication state
      final authService = AuthService();
      final isAuthenticated = authService.isAuthenticated;
      
      if (isAuthenticated) {
        // User is already authenticated, go to home
        await NavigationService().navigateToHome(clearStack: true);
      } else {
        // User is not authenticated, go to login
        await NavigationService().navigateToLogin(clearStack: true);
      }
    } catch (e) {
      // Fallback to login screen on any error
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
      ),
      child: Scaffold(
      body: Container(
        decoration: _buildBackgroundDecoration(isDarkMode),
        child: Stack(
          children: [
            // Particles Background
            _buildParticlesBackground(),
            
            // Main Content
            _buildMainContent(context, isDarkMode),
            
            // Loading Indicator
            _buildLoadingIndicator(),
          ],
        ),
      ),
    ),
    );
  }

  BoxDecoration _buildBackgroundDecoration(bool isDarkMode) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: isDarkMode
            ? [
                Colors.black,
                AppColors.kienlongDarkBlue,
              ]
            : [
                AppColors.kienlongDarkBlue,
                AppColors.kienlongOrange,
              ],
        stops: const [0.0, 1.0],
      ),
    );
  }

  Widget _buildParticlesBackground() {
    return AnimatedBuilder(
      animation: _particleOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _particleOpacity.value,
          child: CustomPaint(
            painter: ParticlesPainter(),
            size: Size.infinite,
          ),
        );
      },
    );
  }

  Widget _buildMainContent(BuildContext context, bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo with animation
          AnimatedBuilder(
            animation: Listenable.merge([_logoOpacity, _logoScale]),
            builder: (context, child) {
              return Transform.scale(
                scale: _logoScale.value,
                child: Opacity(
                  opacity: _logoOpacity.value,
                  child: _buildLogo(),
                ),
              );
            },
          ),
          
          SizedBox(height: AppDimensions.spacingL),
          
          // Tagline with animation
          AnimatedBuilder(
            animation: _textOpacity,
            builder: (context, child) {
              return Opacity(
                opacity: _textOpacity.value,
                child: _buildTagline(context),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLogo() {
    return Container(
      width: 140,
      height: 140 * 36 / 180, // Maintain aspect ratio from SVG
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM)
      ),
      child: SvgPicture.asset(
        'assets/images/logos/logo.svg',
        colorFilter: const ColorFilter.mode(
          Colors.white,
          BlendMode.srcIn,
        ),
      ),
    );
  }

  Widget _buildTagline(BuildContext context) {
    return Text(
              'Ứng dụng nhân viên kinh doanh',
        style: AppTypography.textTheme.titleMedium?.copyWith(
          color: Colors.white.withValues(alpha: 0.9),
          fontWeight: FontWeight.w500,
          letterSpacing: 0.5,
        ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildLoadingIndicator() {
    return Positioned(
      bottom: AppDimensions.spacingXL * 2,
      left: 0,
      right: 0,
      child: Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom painter for particles effect
class ParticlesPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Generate random particles
    for (int i = 0; i < 50; i++) {
      final x = (i * 123.456) % size.width;
      final y = (i * 234.567) % size.height;
      final radius = (i % 3) + 1.0;
      
      canvas.drawCircle(Offset(x, y), radius, paint);
    }

    // Draw connecting lines between nearby particles
    final linePaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.05)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < 20; i++) {
      final x1 = (i * 123.456) % size.width;
      final y1 = (i * 234.567) % size.height;
      final x2 = ((i + 10) * 123.456) % size.width;
      final y2 = ((i + 10) * 234.567) % size.height;
      
      canvas.drawLine(Offset(x1, y1), Offset(x2, y2), linePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 