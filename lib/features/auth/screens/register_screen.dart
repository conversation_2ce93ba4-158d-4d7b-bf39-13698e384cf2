import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:dmrtd/dmrtd.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/branch_model.dart';
import '../../../shared/models/cccd_extraction_model.dart';
import '../widgets/register_progress_header.dart';
import '../widgets/register_navigation_bar.dart';
import '../services/registration_storage_service.dart';
import '../models/local_registration_data.dart';
import '../blocs/master_data_bloc.dart';
import '../blocs/registration_submission_bloc.dart';
import '../blocs/cccd_extraction_bloc.dart';
import '../blocs/mrz_scan_bloc.dart';
import '../blocs/nfc_reading_bloc.dart';
import '../widgets/registration_submission_dialog.dart';
import '../../../shared/services/cccd_extraction_service.dart';
import '../services/mrz_scanner_service.dart';
import '../../../core/nfc/index.dart';
import 'steps/introduction_step.dart';
import 'steps/basic_info_step.dart';
import 'steps/province_selection_step.dart';
import 'steps/branch_selection_step.dart';
import 'steps/id_photo_step.dart';
import 'steps/nfc_reading_step.dart';
import 'steps/review_step.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final PageController _pageController = PageController();
  int _currentStep = 0;
  final int _totalSteps = 7;

  // Storage service
  final RegistrationStorageService _storageService =
      RegistrationStorageService();
  final AppLogger _logger = AppLogger();
  Timer? _autoSaveTimer;

  // Master data
  List<ProvinceModel> _provinces = [];
  List<BranchModel> _branches = [];
  bool _isLoadingProvinces = false;
  bool _isLoadingBranches = false;
  bool _hasLoadedBranches = false;
  
  // Bloc instances
  late MasterDataBloc _masterDataBloc;

  // Registration data storage
  final Map<String, dynamic> _registrationData = {
    // Step 1 - Introduction
    'agreeToTerms': false,

    // Step 2 - Basic info
    'email': '',
    'phone': '',
    'role': '',
    'referralCode': '',

    // Step 3 - Province
    'province': '',
    'provinceCode': '',

    // Step 4 - Branch
    'branch': '',
    'branchCode': '',

    // Step 5 - ID Photos
    'frontIdPhoto': null,
    'backIdPhoto': null,

    // Step 6 - NFC
    'nfcData': null,
    'nfcCompleted': false,

    // Step 7 - Review
    'isSubmitting': false,
  };

  // Step validation
  final Map<int, GlobalKey<FormState>> _formKeys = {
    0: GlobalKey<FormState>(),
    1: GlobalKey<FormState>(),
    2: GlobalKey<FormState>(),
    3: GlobalKey<FormState>(),
    4: GlobalKey<FormState>(),
    5: GlobalKey<FormState>(),
    6: GlobalKey<FormState>(),
  };

  bool _isStepValid(int step) {
    _logger.d('_isStepValid($step) called');
    
    switch (step) {
      case 0: // Introduction step
        final agreeToTerms = _registrationData['agreeToTerms'] == true;
        _logger.d('  Step 0 (Introduction): agreeToTerms=$agreeToTerms');
        return agreeToTerms;
      case 1: // Basic info step
        final formKey = _formKeys[step];
        final isValid = formKey?.currentState?.validate() ?? false;
        _logger.d('  Step 1 (Basic info): formKey=${formKey != null}, isValid=$isValid');
        return isValid;
      case 2: // Province step
        final province = _registrationData['province'];
        final provinceValid = province != null && province.toString().trim().isNotEmpty;
        _logger.d('  Step 2 (Province): province="$province", isValid=$provinceValid');
        return provinceValid;
      case 3: // Branch step
        final branch = _registrationData['branch'];
        final branchValid = branch != null && branch.toString().trim().isNotEmpty;
        _logger.d('  Step 3 (Branch): branch="$branch", isValid=$branchValid');
        return branchValid;
      case 4: // ID Photos step
        final frontPhoto = _registrationData['frontIdPhoto'];
        final backPhoto = _registrationData['backIdPhoto'];
        final photosValid = frontPhoto != null && backPhoto != null;
        _logger.d('  Step 4 (ID Photos): frontPhoto=${frontPhoto != null}, backPhoto=${backPhoto != null}, isValid=$photosValid');
        return photosValid;
      case 5: // NFC step
        // Valid if NFC completed OR CCCD extraction data exists (can skip NFC)
        final nfcCompleted = _registrationData['nfcCompleted'] == true;
        final hasCccdData = _registrationData['cccdExtractionData'] != null;
        final nfcValid = nfcCompleted || hasCccdData;
        _logger.d('  Step 5 (NFC): nfcCompleted=$nfcCompleted, hasCccdData=$hasCccdData, isValid=$nfcValid');
        return nfcValid;
      case 6: // Review step
        // Check if review confirmations are complete
        final confirmAccuracy = _registrationData['confirmAccuracy'] == true;
        final agreeToSubmit = _registrationData['agreeToSubmit'] == true;
        final reviewValid = confirmAccuracy && agreeToSubmit;
        _logger.d('  Step 6 (Review): confirmAccuracy=$confirmAccuracy, agreeToSubmit=$agreeToSubmit, isValid=$reviewValid');
        return reviewValid;
      default:
        _logger.d('  Step $step: default case, returning true');
        return true;
    }
  }

  void _nextStep() {
    // Hide keyboard before transitioning
    FocusScope.of(context).unfocus();

    _logger.d(
      '_nextStep called - currentStep: $_currentStep, totalSteps: $_totalSteps',
    );
    _logger.d('_isStepValid($_currentStep): ${_isStepValid(_currentStep)}');

    // Special handling for NFC step
    _logger.d('NFC Step Check: currentStep=$_currentStep, isValid=${_isStepValid(_currentStep)}');
    if (_currentStep == 5) {
      final nfcCompleted = _registrationData['nfcCompleted'] == true;
      final hasNfcSkipped = _registrationData['nfcSkipped'] == true;
      final hasCccdData = _registrationData['cccdExtractionData'] != null;
      
      _logger.d('NFC Step Debug:');
      _logger.d('  - nfcCompleted: $nfcCompleted');
      _logger.d('  - hasNfcSkipped: $hasNfcSkipped');
      _logger.d('  - hasCccdData: $hasCccdData');
      _logger.d('  - cccdExtractionData: ${_registrationData['cccdExtractionData']}');
      _logger.d('  - Full registrationData keys: ${_registrationData.keys.toList()}');
      
      // If NFC not completed but has CCCD data and not yet skipped, show confirmation
      if (!nfcCompleted && !hasNfcSkipped && hasCccdData) {
        _logger.d('Showing NFC skip confirmation bottom sheet');
        _showSkipNfcConfirmationBottomSheet();
        return;
      } else {
        _logger.d('Skipping bottom sheet - conditions not met');
      }
    }

    // Check if step is valid for normal navigation
    if (_isStepValid(_currentStep)) {
      if (_currentStep < _totalSteps - 1) {
        _logger.d('Moving to next step');
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );

        // Auto-save khi chuyển step
        _saveOnDataChange();
      } else {
        _logger.i('Reached final step, calling _submitRegistration');
        _submitRegistration();
      }
    } else {
      _logger.w('Step validation failed, showing validation error');
      _showValidationError();
    }
  }

  void _previousStep() {
    // Hide keyboard before transitioning
    FocusScope.of(context).unfocus();

    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showValidationError() {
    String message = 'Vui lòng hoàn thành thông tin bước này';

    switch (_currentStep) {
      case 0:
        message = 'Vui lòng đồng ý với điều khoản sử dụng';
        break;
      case 1:
        message = 'Vui lòng điền đầy đủ thông tin cơ bản';
        break;
      case 2:
        message = 'Vui lòng chọn tỉnh/thành phố';
        break;
      case 3:
        message = 'Vui lòng chọn chi nhánh';
        break;
      case 4:
        message = 'Vui lòng chụp ảnh mặt trước và mặt sau CCCD';
        break;
      case 5:
        message = 'Vui lòng hoàn thành quét NFC hoặc chụp ảnh CCCD để trích xuất thông tin';
        break;
    }

    CustomSnackBar.show(
      context,
      message: message,
      type: SnackBarType.warning,
    );
  }

  void _showSkipNfcConfirmationBottomSheet() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        margin: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppColors.warning.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      TablerIcons.alert_triangle,
                      color: AppColors.warning,
                      size: 24,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Bỏ qua đọc NFC?',
                          style: AppTypography.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        Text(
                          'Xác nhận bỏ qua bước đọc NFC',
                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: AppDimensions.spacingL),
              
              // Content
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.info.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          TablerIcons.info_circle,
                          color: AppColors.info,
                          size: 20,
                        ),
                        SizedBox(width: AppDimensions.spacingS),
                        Text(
                          'Thông tin quan trọng',
                          style: AppTypography.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.info,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: AppDimensions.spacingM),
                    Text(
                      'Chúng tôi đã trích xuất thông tin từ ảnh CCCD. Bạn có thể:\n\n• Tiếp tục với thông tin đã trích xuất\n• Cập nhật thông tin NFC sau trong phần Tài khoản\n• Thông tin NFC sẽ giúp xác thực chính xác hơn',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: AppDimensions.spacingL),
              
              // Warning
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.warning.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      TablerIcons.clock,
                      color: AppColors.warning,
                      size: 20,
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Expanded(
                      child: Text(
                        'Bạn sẽ cần cập nhật thông tin NFC sau này để xác thực đầy đủ tài khoản.',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: AppColors.warning,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: AppDimensions.spacingXL),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.kienlongSkyBlue,
                        side: BorderSide(color: AppColors.kienlongSkyBlue),
                        padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        ),
                      ),
                      child: const Text('Đọc NFC ngay'),
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        _skipNfcAndProceed();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.kienlongOrange,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        ),
                      ),
                      child: const Text('Bỏ qua NFC'),
                    ),
                  ),
                ],
              ),
              
              SizedBox(height: AppDimensions.spacingM),
            ],
          ),
        ),
      ),
    );
  }

  void _skipNfcAndProceed() {
    // Mark NFC as skipped and use CCCD extraction data
    setState(() {
      _registrationData['nfcSkipped'] = true;
      _registrationData['nfcCompleted'] = false;
      _registrationData['skipCompletedTime'] = DateTime.now().toIso8601String();
    });
    
    // Copy CCCD extraction data to be used as primary data source
    final cccdExtractionData = _registrationData['cccdExtractionData'];
    if (cccdExtractionData != null) {
      try {
        final cccdModel = CccdExtractionModel.fromJson(cccdExtractionData);
        _registrationData['primaryDataSource'] = 'cccd_extraction';
        
        // Set fullName from CCCD data if not already set
        if (cccdModel.fullName.isNotEmpty) {
          _registrationData['fullName'] = cccdModel.fullName;
        }
        
        CustomSnackBar.show(
          context,
          message: 'Đã sử dụng thông tin từ CCCD extraction. Bạn có thể cập nhật NFC sau.',
          type: SnackBarType.info,
        );
        
        // Auto-save khi skip NFC
        _saveOnDataChange();
        
        // Move to next step
        setState(() {
          _currentStep++;
        });
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        
      } catch (e) {
        CustomSnackBar.show(
          context,
          message: 'Lỗi xử lý dữ liệu CCCD. Vui lòng thử lại.',
          type: SnackBarType.error,
        );
      }
    }
  }

  void _submitRegistration() async {
    _logger.i('_submitRegistration called');
    try {
      // Validate all required data
      _logger.d('Calling _validateAllSteps()');
      if (!_validateAllSteps()) {
        _logger.w('_validateAllSteps() returned false');
        _showErrorSnackBar('Vui lòng hoàn thành tất cả các bước bắt buộc');
        return;
      }

      // Show submission dialog
      final result = await RegistrationSubmissionDialog.show(
        context,
        registrationData: _registrationData,
        onSuccess: () {
          _logger.i('Registration submission completed successfully');
        },
        onError: () {
          _logger.e('Registration submission failed');
        },
      );

      if (result is RegistrationSubmissionCompleted) {
        // Lưu dữ liệu đăng ký hoàn thành với server response
        _logger.i('Saving completed registration data...');
        await _storageService.saveCompletedRegistrationWithServerData(
          registrationData: _registrationData,
          currentStep: _currentStep,
          registerId: result.registrationId,
          documentIds: result.documentIds,
          serverResponse: {
            'registrationId': result.registrationId,
            'documentIds': result.documentIds,
            'registrationData': result.registrationData,
            'status': 'SUCCESS',
            'message': 'Registration completed successfully',
          },
        );
        _logger.i('Completed registration data saved successfully');

        if (mounted) {
          CustomSnackBar.show(
            context,
            message: 'Đăng ký thành công! Mã: ${result.registrationId}',
            type: SnackBarType.success,
          );

          // Dialog đã tự handle navigation, không cần navigate thêm ở đây
          // Navigator.of(context).pop();
        }
      }
    } catch (e) {
      _logger.e('Error in registration submission: $e');
      _showErrorSnackBar('Có lỗi xảy ra: $e');
    }
  }

  bool _validateAllSteps() {
    _logger.d('=== START VALIDATION ===');
    _logger.d('Current registration data: $_registrationData');

    // Check required fields for each step
    final requiredFields = [
      'phone',
      'email',
      'province',
      'branch',
      'frontIdPhoto',
      'backIdPhoto',
    ];

    _logger.d('Checking required fields: $requiredFields');

    for (final field in requiredFields) {
      final value = _registrationData[field];
      _logger.d('Field: $field, Value: $value, Type: ${value.runtimeType}');

      if (value == null || value.toString().trim().isEmpty) {
        _logger.w('❌ Validation failed for field: $field, value: $value');
        return false;
      } else {
        _logger.d('✅ Field: $field - OK');
      }
    }

    // Check if photos are taken (document IDs will be created during submission)
    final frontPhoto = _registrationData['frontIdPhoto'];
    final backPhoto = _registrationData['backIdPhoto'];

    _logger.d('frontPhoto: $frontPhoto (${frontPhoto.runtimeType})');
    _logger.d('backPhoto: $backPhoto (${backPhoto.runtimeType})');

    if (frontPhoto == null || frontPhoto.toString().trim().isEmpty) {
      _logger.w(
        '❌ Validation failed: frontIdPhoto is required (value: $frontPhoto)',
      );
      return false;
    } else {
      _logger.d('✅ frontIdPhoto - OK');
    }

    if (backPhoto == null || backPhoto.toString().trim().isEmpty) {
      _logger.w(
        '❌ Validation failed: backIdPhoto is required (value: $backPhoto)',
      );
      return false;
    } else {
      _logger.d('✅ backIdPhoto - OK');
    }

    // Check NFC completion or CCCD extraction data
    final nfcCompleted = _registrationData['nfcCompleted'];
    final hasNfcSkipped = _registrationData['nfcSkipped'];
    final hasCccdData = _registrationData['cccdExtractionData'] != null;
    
    _logger.d('nfcCompleted: $nfcCompleted (${nfcCompleted.runtimeType})');
    _logger.d('hasNfcSkipped: $hasNfcSkipped (${hasNfcSkipped.runtimeType})');
    _logger.d('hasCccdData: $hasCccdData');
    
    // NFC is valid if completed OR skipped with CCCD data
    final nfcValid = nfcCompleted == true || (hasNfcSkipped == true && hasCccdData);
    
    if (!nfcValid) {
      _logger.w(
        '❌ Validation failed: NFC not completed and no CCCD extraction data (nfcCompleted: $nfcCompleted, hasNfcSkipped: $hasNfcSkipped, hasCccdData: $hasCccdData)',
      );
      return false;
    } else {
      _logger.d('✅ NFC validation - OK');
      // Get fullName from appropriate source
      final fullName = _registrationData['fullName'];
      final primaryDataSource = _registrationData['primaryDataSource'];
      _logger.d('fullName: $fullName, primaryDataSource: $primaryDataSource');
    }

    // Check additional required conditions
    _logger.d('Checking boolean conditions...');

    final agreeToTerms = _registrationData['agreeToTerms'];
    _logger.d('agreeToTerms: $agreeToTerms (${agreeToTerms.runtimeType})');
    if (agreeToTerms != true) {
      _logger.w(
        '❌ Validation failed: agreeToTerms not checked (value: $agreeToTerms)',
      );
      return false;
    } else {
      _logger.d('✅ agreeToTerms - OK');
    }

    // Remove duplicate NFC check
    _logger.d('NFC validation already checked above');

    final confirmAccuracy = _registrationData['confirmAccuracy'];
    _logger.d(
      'confirmAccuracy: $confirmAccuracy (${confirmAccuracy.runtimeType})',
    );
    if (confirmAccuracy != true) {
      _logger.w(
        '❌ Validation failed: confirmAccuracy not checked (value: $confirmAccuracy)',
      );
      return false;
    } else {
      _logger.d('✅ confirmAccuracy - OK');
    }

    final agreeToSubmit = _registrationData['agreeToSubmit'];
    _logger.d('agreeToSubmit: $agreeToSubmit (${agreeToSubmit.runtimeType})');
    if (agreeToSubmit != true) {
      _logger.w(
        '❌ Validation failed: agreeToSubmit not checked (value: $agreeToSubmit)',
      );
      return false;
    } else {
      _logger.d('✅ agreeToSubmit - OK');
    }

    _logger.i('🎉 All validation checks passed!');
    return true;
  }

  void _onExit() {
    // Check if form has data
    bool hasData =
        _registrationData['email'].toString().isNotEmpty ||
        _registrationData['phone'].toString().isNotEmpty ||
        _registrationData['agreeToTerms'] == true;

    if (hasData) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;

      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => AlertDialog(
          backgroundColor: isDarkMode
              ? AppColors.backgroundDarkSecondary
              : AppColors.backgroundPrimary,
          surfaceTintColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          title: Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? AppColors.warning.withValues(alpha: 0.2)
                      : AppColors.warning.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  TablerIcons.alert_triangle,
                  color: AppColors.warning,
                  size: 20,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Text(
                  'Thoát không lưu?',
                  style: AppTypography.textTheme.titleLarge?.copyWith(
                    color: isDarkMode
                        ? AppColors.neutral100
                        : AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bạn có thông tin chưa được lưu. Bạn có chắc chắn muốn thoát không?',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  color: isDarkMode
                      ? AppColors.neutral100
                      : AppColors.textPrimary,
                ),
              ),
              SizedBox(height: AppDimensions.spacingM),
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? AppColors.warning.withValues(alpha: 0.1)
                      : AppColors.warning.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: AppColors.warning.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      TablerIcons.info_circle,
                      color: AppColors.warning,
                      size: 16,
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Expanded(
                      child: Text(
                        'Dữ liệu sẽ bị mất nếu bạn thoát',
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: AppColors.warning,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: isDarkMode
                    ? AppColors.neutral400
                    : AppColors.neutral600,
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
              ),
              child: Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context).pop(); // Exit screen
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
              ),
              child: Text('Thoát'),
            ),
          ],
          actionsPadding: EdgeInsets.all(AppDimensions.paddingL),
        ),
      );
    } else {
      Navigator.of(context).pop();
    }
  }

  String get _stepTitle {
    switch (_currentStep) {
      case 0:
        return 'Chào mừng đến với Kiloba';
      case 1:
        return 'Thông tin cơ bản';
      case 2:
        return 'Chọn tỉnh/thành phố';
      case 3:
        return 'Chọn chi nhánh';
      case 4:
        return 'Chụp ảnh giấy tờ';
      case 5:
        return 'Quét thông tin NFC';
      case 6:
        return 'Xác nhận thông tin';
      default:
        return 'Đăng ký tài khoản';
    }
  }

  String get _actionButtonText {
    switch (_currentStep) {
      case 0:
        return 'Bắt đầu';
      case 6:
        return 'Hoàn tất đăng ký';
      default:
        return 'Tiếp tục';
    }
  }

  @override
  void initState() {
    super.initState();
    _masterDataBloc = MasterDataBloc();
    _loadSavedRegistration();
    _startAutoSave();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Load master data after BlocProvider is available
    if (_provinces.isEmpty && !_isLoadingProvinces) {
      // Delay to ensure BlocProvider is ready
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _provinces.isEmpty && !_isLoadingProvinces) {
          _loadMasterData();
        }
      });
    }
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _pageController.dispose();
    _masterDataBloc.close();
    super.dispose();
  }

  /// Load registration đã lưu
  Future<void> _loadSavedRegistration() async {
    try {
      _logger.i('Loading saved registration...');
      final savedRegistration = await _storageService.getCurrentRegistration();

      if (savedRegistration != null) {
        _logger.i(
          'Found saved registration: step ${savedRegistration.currentStep}, completed: ${savedRegistration.isCompleted}',
        );

        // Kiểm tra xem có phải là registration chưa hoàn thành không
        if (!savedRegistration.isCompleted) {
          _logger.i('Restoring incomplete registration data');

          // Restore data
          setState(() {
            _registrationData.clear();
            _registrationData.addAll(savedRegistration.registrationData);
            _currentStep = savedRegistration.currentStep;
          });

          // Load branches if province is selected
          final provinceCode =
              savedRegistration.registrationData['provinceCode'];
          if (provinceCode != null && provinceCode.toString().isNotEmpty) {
            _loadBranchesForProvince(provinceCode.toString());
          }

          // Move to saved step (only if PageController is attached)
          if (_pageController.hasClients) {
            _pageController.jumpToPage(_currentStep);
          }

          // Show resume dialog
          _showResumeDialog(savedRegistration);
        } else {
          _logger.i('Saved registration is completed, not restoring');
        }
      } else {
        _logger.i('No saved registration found');
      }
    } catch (e) {
      _logger.e('Error loading saved registration: $e');
    }
  }

  /// Start auto-save timer
  void _startAutoSave() {
    _autoSaveTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _autoSave();
    });
  }

  /// Auto-save registration data
  Future<void> _autoSave() async {
    try {
      await _storageService.saveCurrentRegistration(
        registrationData: _registrationData,
        currentStep: _currentStep,
      );
    } catch (e) {
      _logger.e('Auto-save failed: $e');
    }
  }

  /// Manual save khi user thay đổi data
  Future<void> _saveOnDataChange() async {
    try {
      await _storageService.saveCurrentRegistration(
        registrationData: _registrationData,
        currentStep: _currentStep,
      );
    } catch (e) {
      _logger.e('Manual save failed: $e');
    }
  }

  /// Show resume dialog
  void _showResumeDialog(LocalRegistrationData savedRegistration) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundPrimary,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(TablerIcons.clock, color: AppColors.info, size: 20),
            ),
            SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: Text(
                'Tiếp tục đăng ký?',
                style: AppTypography.textTheme.titleLarge?.copyWith(
                  color: isDarkMode
                      ? AppColors.neutral100
                      : AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bạn có dữ liệu đăng ký chưa hoàn thành từ ${_formatDateTime(savedRegistration.lastSavedAt)}.',
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: isDarkMode
                    ? AppColors.neutral100
                    : AppColors.textPrimary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Bạn có muốn tiếp tục không?',
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: isDarkMode
                    ? AppColors.neutral300
                    : AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? AppColors.info.withValues(alpha: 0.1)
                    : AppColors.info.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.info_circle,
                    color: AppColors.info,
                    size: 16,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Dữ liệu sẽ được khôi phục từ bước ${savedRegistration.currentStep + 1}',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.info,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startNewRegistration();
            },
            style: TextButton.styleFrom(
              foregroundColor: isDarkMode
                  ? AppColors.neutral400
                  : AppColors.neutral600,
            ),
            child: Text('Bắt đầu mới'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Data đã được restore trong _loadSavedRegistration
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: Text('Tiếp tục'),
          ),
        ],
        actionsPadding: EdgeInsets.all(AppDimensions.paddingL),
      ),
    );
  }

  /// Start new registration
  Future<void> _startNewRegistration() async {
    setState(() {
      _registrationData.clear();
      _currentStep = 0;
      _branches.clear();
      _isLoadingBranches = false;
      _hasLoadedBranches = false;
    });

    await _storageService.startNewRegistration();
    _pageController.jumpToPage(0);
  }

  /// Format datetime helper
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}';
  }

  /// Load master data
  Future<void> _loadMasterData() async {
    try {
      if (!mounted) return;

      _logger.d('_loadMasterData called');
      setState(() => _isLoadingProvinces = true);

      // Load provinces
      _logger.d('Using _masterDataBloc instance: ${_masterDataBloc.hashCode}');
      _logger.d('Adding LoadProvincesEvent to bloc');
      _masterDataBloc.add(const LoadProvincesEvent());
      _logger.d('LoadProvincesEvent added successfully');
    } catch (e) {
      _logger.e('Error loading master data: $e');
      if (mounted) {
        setState(() => _isLoadingProvinces = false);
      }
    }
  }

  /// Load branches for province
  Future<void> _loadBranchesForProvince(String provinceId) async {
    try {
      if (!mounted || provinceId.isEmpty) return;

      setState(() {
        _branches.clear(); // Clear previous branches
        _isLoadingBranches = true;
        _hasLoadedBranches = false; // Reset hasLoaded state
      });

      _masterDataBloc.add(LoadBranchesEvent(provinceId));
    } catch (e) {
      _logger.e('Error loading branches: $e');
      if (mounted) {
        setState(() => _isLoadingBranches = false);
      }
    }
  }

  /// Handle province selection
  void _onProvinceSelected(ProvinceModel province) {
    
    setState(() {
      _registrationData['province'] = province.name;
      _registrationData['provinceCode'] = province.id;
      _registrationData['branch'] = '';
      _registrationData['branchCode'] = '';
      _branches.clear(); // Clear previous branches
      _hasLoadedBranches = false; // Reset loaded state
    });

    // Load branches for selected province
    _loadBranchesForProvince(province.id);
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    CustomSnackBar.show(
      context,
      message: message,
      type: SnackBarType.error,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return MultiBlocProvider(
      providers: [
        BlocProvider<MasterDataBloc>.value(
          value: _masterDataBloc,
        ),
        BlocProvider(create: (context) => RegistrationSubmissionBloc()),
        BlocProvider(
          create: (context) => CccdExtractionBloc(
            cccdExtractionService: CccdExtractionService(),
          ),
        ),
        BlocProvider(
          create: (context) => MrzScanBloc(
            mrzScannerService: MrzScannerService(),
          ),
        ),
        BlocProvider(
          create: (context) => NfcReadingBloc(
            nfcProvider: NfcProvider(),
            nfcParser: NfcDataParserService(),
            nfcRepository: NfcDataRepository(),
            logger: AppLogger(),
          ),
        ),
      ],
            child: BlocListener<MasterDataBloc, MasterDataState>(
        listener: (context, state) {
          _logger.d('MasterDataBloc state changed: ${state.runtimeType}');
          
          if (state is MasterDataLoaded) {
            _logger.d('MasterDataLoaded state received');
            
            // Update provinces if available
            if (state.provinces.isNotEmpty) {
              _logger.d('Provinces updated: ${state.provinces.length} items');
              setState(() {
                _provinces = state.provinces;
                _isLoadingProvinces = false;
              });
            }
            
            // Update branches if available
            if (state.branches.isNotEmpty) {
              _logger.d('Branches updated: ${state.branches.length} items');
              setState(() {
                _branches = state.branches;
                _isLoadingBranches = false;
                _hasLoadedBranches = true;
              });
            }
            
          } else if (state is ProvincesLoaded) {
            // Legacy state handling for backward compatibility
            _logger.d('ProvincesLoaded state received: ${state.provinces.length} provinces');
            setState(() {
              _provinces = state.provinces;
              _isLoadingProvinces = false;
            });
            _logger.d('Provinces updated in UI: ${_provinces.length} items');
          } else if (state is BranchesLoaded) {
            // Legacy state handling for backward compatibility
            _logger.d('BranchesLoaded state received: ${state.branches.length} branches');
            setState(() {
              _branches = state.branches;
              _isLoadingBranches = false;
              _hasLoadedBranches = true;
            });
          } else if (state is MasterDataError) {
            _logger.e('MasterDataError state received: ${state.message}');
            setState(() {
              _isLoadingProvinces = false;
              _isLoadingBranches = false;
              // Don't set _hasLoadedBranches to true on error
            });
            _showErrorSnackBar(state.message);
          } else if (state is MasterDataLoading) {
            _logger.d('MasterDataLoading state received: ${state.message}');
            
            // Handle loading states based on type
            if (state.type == 'provinces') {
              setState(() {
                _isLoadingProvinces = true;
              });
            } else if (state.type == 'branches') {
              setState(() {
                _isLoadingBranches = true;
              });
            }
          } else {
            _logger.d('Other state received: ${state.runtimeType}');
          }
        },
        child: AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: isDarkMode
                ? Brightness.light
                : Brightness.dark,
            statusBarBrightness: isDarkMode ? Brightness.dark : Brightness.light,
          ),
          child: Scaffold(
            appBar: AppNavHeaderExtension.forScreen(
              title: 'Đăng ký tài khoản',
              onBack: _onExit,
            ),
            body: GestureDetector(
              onTap: () {
                // Hide keyboard when tapping outside
                FocusScope.of(context).unfocus();
              },
              child: Column(
                children: [
                  // Progress Header
                  RegisterProgressHeader(
                    currentStep: _currentStep,
                    totalSteps: _totalSteps,
                    stepTitle: _stepTitle,
                  ),

                  // Form Content
                  Expanded(
                    child: PageView(
                      controller: _pageController,
                      physics:
                          const NeverScrollableScrollPhysics(), // Disable swipe navigation
                      onPageChanged: (index) {
                        setState(() {
                          _currentStep = index;
                        });
                      },
                      children: [
                        IntroductionStep(
                          formKey: _formKeys[0]!,
                          registrationData: _registrationData,
                        ),
                        BasicInfoStep(
                          formKey: _formKeys[1]!,
                          registrationData: _registrationData,
                        ),
                        ProvinceSelectionStep(
                          formKey: _formKeys[2]!,
                          registrationData: _registrationData,
                          provinces: _provinces.isNotEmpty ? _provinces : [],
                          isLoading: _isLoadingProvinces,
                          onProvinceSelected: _onProvinceSelected,
                        ),
                        BranchSelectionStep(
                          formKey: _formKeys[3]!,
                          registrationData: _registrationData,
                          branches: _branches.isNotEmpty ? _branches : [],
                          isLoading: _isLoadingBranches,
                          hasLoaded:
                              !_isLoadingBranches && _branches.isNotEmpty ||
                              (!_isLoadingBranches &&
                                  _branches.isEmpty &&
                                  _hasLoadedBranches),
                        ),
                        IdPhotoStep(
                          formKey: _formKeys[4]!,
                          registrationData: _registrationData,
                        ),
                        NfcReadingStep(
                          formKey: _formKeys[5]!,
                          registrationData: _registrationData,
                        ),
                        ReviewStep(
                          formKey: _formKeys[6]!,
                          registrationData: _registrationData,
                        ),
                      ],
                    ),
                  ),

                  // Navigation Bar
                  BlocBuilder<CccdExtractionBloc, CccdExtractionState>(
                    builder: (context, cccdState) {
                      return BlocBuilder<MrzScanBloc, MrzScanState>(
                        builder: (context, mrzState) {
                          return BlocBuilder<NfcReadingBloc, NfcReadingState>(
                            builder: (context, nfcState) {
                              return RegisterNavigationBar(
                                currentStep: _currentStep,
                                totalSteps: _totalSteps,
                                actionButtonText: _actionButtonText,
                                onPrevious: _currentStep > 0 ? _previousStep : null,
                                onNext: _nextStep,
                                isLoading: _registrationData['isSubmitting'] == true,
                                isExtractingCccd: cccdState is CccdExtractionLoading || 
                                                mrzState is MrzScanLoading || 
                                                nfcState is NfcReadingLoading,
                              );
                            },
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
