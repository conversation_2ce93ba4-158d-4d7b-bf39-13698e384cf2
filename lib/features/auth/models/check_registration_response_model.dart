class CheckRegistrationResponseModel {
  final String? id;
  final String? status;
  final DateTime? createdAt;
  final bool exists;

  const CheckRegistrationResponseModel({
    this.id,
    this.status,
    this.createdAt,
    required this.exists,
  });

  factory CheckRegistrationResponseModel.fromJson(Map<String, dynamic> json) {
    final data = json['data'] as Map<String, dynamic>?;

    if (data != null) {
      return CheckRegistrationResponseModel(
        id: data['id'] as String?,
        status: data['status'] as String?,
        createdAt: data['created_at'] != null
            ? DateTime.parse(data['created_at'] as String)
            : null,
        exists: true,
      );
    } else {
      return const CheckRegistrationResponseModel(exists: false);
    }
  }

  factory CheckRegistrationResponseModel.notFound() {
    return const CheckRegistrationResponseModel(exists: false);
  }

  @override
  String toString() {
    return 'CheckRegistrationResponseModel(id: $id, status: $status, createdAt: $createdAt, exists: $exists)';
  }
}
