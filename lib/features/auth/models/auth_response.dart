/// Model cho Auth Response từ backend API theo spec
class AuthResponse {
  final String accessToken;
  final String refreshToken;
  final String tokenType; // "Bearer"
  final int expiresIn; // seconds
  final int refreshExpiresIn; // seconds
  final String scope;
  final String sessionState;
  final double issuedAt; // Unix timestamp
  final double expiresAt; // Unix timestamp
  final double refreshExpiresAt; // Unix timestamp
  final bool refreshTokenExpired; // Added from new response
  final bool accessTokenExpired; // Added from new response

  const AuthResponse({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
    required this.refreshExpiresIn,
    required this.scope,
    required this.sessionState,
    required this.issuedAt,
    required this.expiresAt,
    required this.refreshExpiresAt,
    this.refreshTokenExpired = false,
    this.accessTokenExpired = false,
  });

  /// Tạo instance từ JSON response
  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    // Handle new response structure with data wrapper
    Map<String, dynamic> data;
    if (json.containsKey('data') && json['data'] is Map<String, dynamic>) {
      // New response format with success/code/message/data wrapper
      data = json['data'];
    } else {
      // Old response format - direct data
      data = json;
    }
    
    final issuedAt = data['issued_at'] != null 
        ? (data['issued_at'] is String 
            ? DateTime.parse(data['issued_at']).millisecondsSinceEpoch / 1000.0
            : (data['issued_at'] is int 
                ? data['issued_at'].toDouble() 
                : data['issued_at'].toDouble()))
        : DateTime.now().millisecondsSinceEpoch / 1000.0;
    
    final expiresAt = data['expires_at'] != null
        ? (data['expires_at'] is String 
            ? DateTime.parse(data['expires_at']).millisecondsSinceEpoch / 1000.0
            : (data['expires_at'] is int 
                ? data['expires_at'].toDouble() 
                : data['expires_at'].toDouble()))
        : issuedAt + (data['expires_in'] ?? 300);
    
    final refreshExpiresAt = data['refresh_expires_at'] != null
        ? (data['refresh_expires_at'] is String 
            ? DateTime.parse(data['refresh_expires_at']).millisecondsSinceEpoch / 1000.0
            : (data['refresh_expires_at'] is int 
                ? data['refresh_expires_at'].toDouble() 
                : data['refresh_expires_at'].toDouble()))
        : issuedAt + (data['refresh_expires_in'] ?? 1800);

    return AuthResponse(
      accessToken: data['access_token'] ?? '',
      refreshToken: data['refresh_token'] ?? '',
      tokenType: data['token_type'] ?? 'Bearer',
      expiresIn: data['expires_in'] ?? 300,
      refreshExpiresIn: data['refresh_expires_in'] ?? 1800,
      scope: data['scope'] ?? 'openid profile email',
      sessionState: data['session_state'] ?? '',
      issuedAt: issuedAt,
      expiresAt: expiresAt,
      refreshExpiresAt: refreshExpiresAt,
      refreshTokenExpired: data['refreshTokenExpired'] ?? false,
      accessTokenExpired: data['accessTokenExpired'] ?? false,
    );
  }

  /// Convert thành Map
  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
      'refresh_expires_in': refreshExpiresIn,
      'scope': scope,
      'session_state': sessionState,
      'issued_at': issuedAt,
      'expires_at': expiresAt,
      'refresh_expires_at': refreshExpiresAt,
      'refreshTokenExpired': refreshTokenExpired,
      'accessTokenExpired': accessTokenExpired,
    };
  }

  /// Helper methods
  bool get isAccessTokenExpired {
    // Use backend provided status first, fallback to calculation
    if (accessTokenExpired) return true;
    final now = DateTime.now().millisecondsSinceEpoch / 1000.0;
    return now > expiresAt;
  }

  bool get isRefreshTokenExpired {
    // Use backend provided status first, fallback to calculation
    if (refreshTokenExpired) return true;
    final now = DateTime.now().millisecondsSinceEpoch / 1000.0;
    return now > refreshExpiresAt;
  }

  bool get shouldRefreshToken {
    // Refresh if expires in less than 2 minutes
    final now = DateTime.now().millisecondsSinceEpoch / 1000.0;
    final twoMinutesLater = now + 120; // 2 minutes in seconds
    return twoMinutesLater > expiresAt;
  }

  Duration get accessTokenTimeLeft {
    final now = DateTime.now().millisecondsSinceEpoch / 1000.0;
    if (expiresAt > now) {
      return Duration(seconds: (expiresAt - now).round());
    }
    return Duration.zero;
  }

  Duration get refreshTokenTimeLeft {
    final now = DateTime.now().millisecondsSinceEpoch / 1000.0;
    if (refreshExpiresAt > now) {
      return Duration(seconds: (refreshExpiresAt - now).round());
    }
    return Duration.zero;
  }

  /// Convert timestamp to DateTime
  DateTime get issuedAtDateTime => DateTime.fromMillisecondsSinceEpoch((issuedAt * 1000).round());
  DateTime get expiresAtDateTime => DateTime.fromMillisecondsSinceEpoch((expiresAt * 1000).round());
  DateTime get refreshExpiresAtDateTime => DateTime.fromMillisecondsSinceEpoch((refreshExpiresAt * 1000).round());

  /// Copy với token mới (dùng cho refresh)
  AuthResponse copyWith({
    String? accessToken,
    String? refreshToken,
    String? tokenType,
    int? expiresIn,
    int? refreshExpiresIn,
    String? scope,
    String? sessionState,
    double? issuedAt,
    double? expiresAt,
    double? refreshExpiresAt,
    bool? refreshTokenExpired,
    bool? accessTokenExpired,
  }) {
    return AuthResponse(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      tokenType: tokenType ?? this.tokenType,
      expiresIn: expiresIn ?? this.expiresIn,
      refreshExpiresIn: refreshExpiresIn ?? this.refreshExpiresIn,
      scope: scope ?? this.scope,
      sessionState: sessionState ?? this.sessionState,
      issuedAt: issuedAt ?? this.issuedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      refreshExpiresAt: refreshExpiresAt ?? this.refreshExpiresAt,
      refreshTokenExpired: refreshTokenExpired ?? this.refreshTokenExpired,
      accessTokenExpired: accessTokenExpired ?? this.accessTokenExpired,
    );
  }

  @override
  String toString() {
    return 'AuthResponse(tokenType: $tokenType, expiresIn: $expiresIn, scope: $scope)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthResponse &&
        other.accessToken == accessToken &&
        other.refreshToken == refreshToken &&
        other.sessionState == sessionState;
  }

  @override
  int get hashCode {
    return Object.hash(accessToken, refreshToken, sessionState);
  }
} 