import 'package:json_annotation/json_annotation.dart';

part 'registration_model.g.dart';

@JsonSerializable()
class RegistrationModel {
  // Thông tin cá nhân (required) - theo thứ tự của PostgreSQL function
  final String fullName;
  final String idCardType;
  final String idCardNo;
  final String issueDate;
  final String issuePlace;
  final String permanentAddress;
  final String phoneNumber;
  final String email;
  
  // Thông tin đăng ký (required)
  final String registerType;
  final String provinceId;
  final String branchId;
  
  // Thông tin cá nhân (optional)
  final String? expiryDate;
  final String? positionId;
  final String? referrerCode;
  
  // Tài li<PERSON> đ<PERSON> kèm (optional)
  final String? frontCardDocumentId;
  final String? backCardDocumentId;
  
  // Metadata (optional)
  final String dataSource;
  final Map<String, dynamic>? metadata;

  const RegistrationModel({
    required this.fullName,
    required this.idCardType,
    required this.idCardNo,
    required this.issueDate,
    required this.issuePlace,
    required this.permanentAddress,
    required this.phoneNumber,
    required this.email,
    required this.registerType,
    required this.provinceId,
    required this.branchId,
    this.expiryDate,
    this.positionId,
    this.referrerCode,
    this.frontCardDocumentId,
    this.backCardDocumentId,
    required this.dataSource,
    this.metadata,
  });

  factory RegistrationModel.fromJson(Map<String, dynamic> json) =>
      _$RegistrationModelFromJson(json);

  Map<String, dynamic> toJson() => _$RegistrationModelToJson(this);

  RegistrationModel copyWith({
    String? fullName,
    String? idCardType,
    String? idCardNo,
    String? issueDate,
    String? issuePlace,
    String? permanentAddress,
    String? phoneNumber,
    String? email,
    String? registerType,
    String? provinceId,
    String? branchId,
    String? expiryDate,
    String? positionId,
    String? referrerCode,
    String? frontCardDocumentId,
    String? backCardDocumentId,
    String? dataSource,
    Map<String, dynamic>? metadata,
  }) {
    return RegistrationModel(
      fullName: fullName ?? this.fullName,
      idCardType: idCardType ?? this.idCardType,
      idCardNo: idCardNo ?? this.idCardNo,
      issueDate: issueDate ?? this.issueDate,
      issuePlace: issuePlace ?? this.issuePlace,
      permanentAddress: permanentAddress ?? this.permanentAddress,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      registerType: registerType ?? this.registerType,
      provinceId: provinceId ?? this.provinceId,
      branchId: branchId ?? this.branchId,
      expiryDate: expiryDate ?? this.expiryDate,
      positionId: positionId ?? this.positionId,
      referrerCode: referrerCode ?? this.referrerCode,
      frontCardDocumentId: frontCardDocumentId ?? this.frontCardDocumentId,
      backCardDocumentId: backCardDocumentId ?? this.backCardDocumentId,
      dataSource: dataSource ?? this.dataSource,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'RegistrationModel(fullName: $fullName, idCardNo: $idCardNo, registerType: $registerType)';
  }
} 