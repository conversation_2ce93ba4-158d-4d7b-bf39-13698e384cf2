import 'package:json_annotation/json_annotation.dart';
import 'package:mrz_parser/mrz_parser.dart';
import '../../../../shared/utils/app_logger.dart';

part 'mrz_data_model.g.dart';

@JsonSerializable()
class MrzDataModel {
  final List<String> rawLines;
  final String? documentType;
  final String? countryCode;
  final String? surnames;
  final String? givenNames;
  final String? documentNumber;
  final String? nationalityCountryCode;
  final String? birthDate;
  final String? sex;
  final String? expiryDate;
  final String? personalNumber;
  final String? personalNumber2;

  const MrzDataModel({
    required this.rawLines,
    this.documentType,
    this.countryCode,
    this.surnames,
    this.givenNames,
    this.documentNumber,
    this.nationalityCountryCode,
    this.birthDate,
    this.sex,
    this.expiryDate,
    this.personalNumber,
    this.personalNumber2,
  });

  factory MrzDataModel.fromJson(Map<String, dynamic> json) =>
      _$MrzDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$MrzDataModelToJson(this);

  bool get isSuccess => documentType != null;
  bool get hasRawLines => rawLines.isNotEmpty;

  /// Reconstruct MRZResult from serialized data
  MRZResult? toMRZResult() {
    if (!isSuccess) return null;
    
    try {
      // Create MRZResult using the parsed data
      return _createMRZResultFromData();
    } catch (e) {
      appLogger.e('Error reconstructing MRZResult: $e');
      return null;
    }
  }

  /// Create MRZResult from parsed data
  MRZResult? _createMRZResultFromData() {
    try {
      // Use MRZParser to parse from raw lines if available
      if (hasRawLines) {
        final mrz = MRZParser.tryParse(rawLines);
        if (mrz != null) {
          return mrz;
        }
      }
      
      // If raw lines parsing fails, try to create from individual fields
      // This is a fallback approach
      return _createMRZResultFromFields();
    } catch (e) {
      appLogger.e('Error creating MRZResult from data: $e');
      return null;
    }
  }

  /// Create MRZResult from individual fields (fallback method)
  MRZResult? _createMRZResultFromFields() {
    // This is a simplified approach - in practice, you might need to
    // reconstruct the MRZ lines and parse them again
    if (rawLines.length >= 3) {
      // Try to parse from reconstructed raw lines
      return MRZParser.tryParse(rawLines);
    }
    return null;
  }

  @override
  String toString() {
    return 'MrzDataModel(documentType: $documentType, surnames: $surnames, givenNames: $givenNames)';
  }
} 