// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'registration_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegistrationModel _$RegistrationModelFromJson(Map<String, dynamic> json) =>
    RegistrationModel(
      fullName: json['fullName'] as String,
      idCardType: json['idCardType'] as String,
      idCardNo: json['idCardNo'] as String,
      issueDate: json['issueDate'] as String,
      issuePlace: json['issuePlace'] as String,
      permanentAddress: json['permanentAddress'] as String,
      phoneNumber: json['phoneNumber'] as String,
      email: json['email'] as String,
      registerType: json['registerType'] as String,
      provinceId: json['provinceId'] as String,
      branchId: json['branchId'] as String,
      expiryDate: json['expiryDate'] as String?,
      positionId: json['positionId'] as String?,
      referrerCode: json['referrerCode'] as String?,
      frontCardDocumentId: json['frontCardDocumentId'] as String?,
      backCardDocumentId: json['backCardDocumentId'] as String?,
      dataSource: json['dataSource'] as String,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$RegistrationModelToJson(RegistrationModel instance) =>
    <String, dynamic>{
      'fullName': instance.fullName,
      'idCardType': instance.idCardType,
      'idCardNo': instance.idCardNo,
      'issueDate': instance.issueDate,
      'issuePlace': instance.issuePlace,
      'permanentAddress': instance.permanentAddress,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'registerType': instance.registerType,
      'provinceId': instance.provinceId,
      'branchId': instance.branchId,
      'expiryDate': instance.expiryDate,
      'positionId': instance.positionId,
      'referrerCode': instance.referrerCode,
      'frontCardDocumentId': instance.frontCardDocumentId,
      'backCardDocumentId': instance.backCardDocumentId,
      'dataSource': instance.dataSource,
      'metadata': instance.metadata,
    };
