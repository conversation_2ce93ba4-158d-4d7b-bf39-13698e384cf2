

/// Response model cho function mobile_api.upsert_fcm_token
class FcmTokenUpsertResponse {
  final bool success;
  final String? action; // 'created' | 'updated'
  final String? tokenId; // UUID string
  final String message;
  final String? error;
  final String? errorCode;

  const FcmTokenUpsertResponse({
    required this.success,
    this.action,
    this.tokenId,
    required this.message,
    this.error,
    this.errorCode,
  });

  factory FcmTokenUpsertResponse.fromJson(Map<String, dynamic> json) {
    // PostgreSQL function trả về 'message' khi thành công, 'error' khi thất bại
    // <PERSON><PERSON> đảm bảo message luôn có giá trị, fallback từ error
    final success = json['success'] ?? false;
    final message = success 
        ? (json['message'] ?? 'Operation completed successfully')
        : (json['error'] ?? 'Unknown error occurred');
    
    return FcmTokenUpsertResponse(
      success: success,
      action: json['action'],
      tokenId: json['token_id']?.toString(),
      message: message,
      error: json['error'],
      errorCode: json['error_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'action': action,
      'token_id': tokenId,
      'message': message,
      'error': error,
      'error_code': errorCode,
    };
  }

  @override
  String toString() {
    return 'FcmTokenUpsertResponse(success: $success, action: $action, tokenId: $tokenId, message: $message, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is FcmTokenUpsertResponse &&
      other.success == success &&
      other.action == action &&
      other.tokenId == tokenId &&
      other.message == message &&
      other.error == error &&
      other.errorCode == errorCode;
  }

  @override
  int get hashCode {
    return success.hashCode ^
      action.hashCode ^
      tokenId.hashCode ^
      message.hashCode ^
      error.hashCode ^
      errorCode.hashCode;
  }
}

/// Response model cho function mobile_api.deactivate_fcm_token
class FcmTokenDeactivateResponse {
  final bool success;
  final String message;
  final String? error;
  final String? errorCode;

  const FcmTokenDeactivateResponse({
    required this.success,
    required this.message,
    this.error,
    this.errorCode,
  });

  factory FcmTokenDeactivateResponse.fromJson(Map<String, dynamic> json) {
    // PostgreSQL function trả về 'message' khi thành công, 'error' khi thất bại
    // Để đảm bảo message luôn có giá trị, fallback từ error
    final success = json['success'] ?? false;
    final message = success 
        ? (json['message'] ?? 'Deactivation completed successfully')
        : (json['error'] ?? 'Unknown error occurred');
    
    return FcmTokenDeactivateResponse(
      success: success,
      message: message,
      error: json['error'],
      errorCode: json['error_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'error': error,
      'error_code': errorCode,
    };
  }

  @override
  String toString() {
    return 'FcmTokenDeactivateResponse(success: $success, message: $message, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is FcmTokenDeactivateResponse &&
      other.success == success &&
      other.message == message &&
      other.error == error &&
      other.errorCode == errorCode;
  }

  @override
  int get hashCode {
    return success.hashCode ^
      message.hashCode ^
      error.hashCode ^
      errorCode.hashCode;
  }
}

/// Request model cho function mobile_api.upsert_fcm_token
class FcmTokenUpsertRequest {
  final String deviceId;
  final String fcmToken;
  final String platform; // 'ANDROID' | 'IOS' | 'WEB'
  final String? userId;
  final String? appVersion;
  final String? osVersion;
  final String? deviceModel;
  final String? deviceName;
  final String? installationId;

  const FcmTokenUpsertRequest({
    required this.deviceId,
    required this.fcmToken,
    required this.platform,
    this.userId,
    this.appVersion,
    this.osVersion,
    this.deviceModel,
    this.deviceName,
    this.installationId,
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'p_device_id': deviceId,
      'p_fcm_token': fcmToken,
      'p_platform': platform,
    };

    // Add optional parameters only if they are not null
    if (userId != null) json['p_user_id'] = userId;
    if (appVersion != null) json['p_app_version'] = appVersion;
    if (osVersion != null) json['p_os_version'] = osVersion;
    if (deviceModel != null) json['p_device_model'] = deviceModel;
    if (deviceName != null) json['p_device_name'] = deviceName;
    if (installationId != null) json['p_installation_id'] = installationId;

    return json;
  }

  @override
  String toString() {
    return 'FcmTokenUpsertRequest(deviceId: $deviceId, platform: $platform, userId: $userId, appVersion: $appVersion)';
  }
}

/// Enum cho platform
enum DevicePlatform {
  android('ANDROID'),
  ios('IOS'),
  web('WEB');

  const DevicePlatform(this.value);
  final String value;

  static DevicePlatform fromString(String platform) {
    switch (platform.toUpperCase()) {
      case 'ANDROID':
        return DevicePlatform.android;
      case 'IOS':
        return DevicePlatform.ios;
      case 'WEB':
        return DevicePlatform.web;
      default:
        throw ArgumentError('Unknown platform: $platform');
    }
  }
}



/// Result wrapper cho FCM token upsert operations
class FcmTokenUpsertResult {
  final bool isSuccess;
  final String message;
  final FcmTokenUpsertResponse? data;

  const FcmTokenUpsertResult._({
    required this.isSuccess,
    required this.message,
    this.data,
  });

  /// Tạo result thành công với data
  factory FcmTokenUpsertResult.success(FcmTokenUpsertResponse data) {
    return FcmTokenUpsertResult._(
      isSuccess: true,
      message: data.message,
      data: data,
    );
  }

  /// Tạo result thất bại với error message
  factory FcmTokenUpsertResult.failure(String message) {
    return FcmTokenUpsertResult._(
      isSuccess: false,
      message: message,
    );
  }

  @override
  String toString() {
    return 'FcmTokenUpsertResult(isSuccess: $isSuccess, message: $message, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is FcmTokenUpsertResult &&
      other.isSuccess == isSuccess &&
      other.message == message &&
      other.data == data;
  }

  @override
  int get hashCode => isSuccess.hashCode ^ message.hashCode ^ data.hashCode;
}

/// Result wrapper cho FCM token deactivate operations
class FcmTokenDeactivateResult {
  final bool isSuccess;
  final String message;
  final FcmTokenDeactivateResponse? data;

  const FcmTokenDeactivateResult._({
    required this.isSuccess,
    required this.message,
    this.data,
  });

  /// Tạo result thành công với data
  factory FcmTokenDeactivateResult.success(FcmTokenDeactivateResponse data) {
    return FcmTokenDeactivateResult._(
      isSuccess: true,
      message: data.message,
      data: data,
    );
  }

  /// Tạo result thất bại với error message
  factory FcmTokenDeactivateResult.failure(String message) {
    return FcmTokenDeactivateResult._(
      isSuccess: false,
      message: message,
    );
  }

  @override
  String toString() {
    return 'FcmTokenDeactivateResult(isSuccess: $isSuccess, message: $message, data: $data)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is FcmTokenDeactivateResult &&
      other.isSuccess == isSuccess &&
      other.message == message &&
      other.data == data;
  }

  @override
  int get hashCode => isSuccess.hashCode ^ message.hashCode ^ data.hashCode;
}

/// Exception cho FCM Token operations
class FcmTokenException implements Exception {
  final String message;
  final FcmTokenExceptionType type;
  final Object? originalException;

  const FcmTokenException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'FcmTokenException: $message (Type: $type)';
}

/// Loại lỗi FCM Token
enum FcmTokenExceptionType {
  firebaseError,
  apiError,
  networkError,
  tokenNotFound,
  authenticationError,
  serverError,
  unknown,
} 