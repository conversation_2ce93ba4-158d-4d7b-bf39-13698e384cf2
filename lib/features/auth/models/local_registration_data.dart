/// Model để quản lý dữ liệu registration đư<PERSON><PERSON> lưu local
class LocalRegistrationData {
  final Map<String, dynamic> registrationData;
  final int currentStep;
  final DateTime lastSavedAt;
  final String? sessionId;
  final bool isCompleted;
  final String? registerId;
  final DateTime? completedAt;
  final Map<String, String>? documentIds;
  final Map<String, dynamic>? nfcData;
  final List<String> uploadedFiles;

  // Thông tin server response
  final Map<String, dynamic>? serverResponse;
  final String? serverStatus;
  final String? serverMessage;

  // Thông tin phê duyệt
  final bool? isApproved;
  final DateTime? approvedAt;
  final String? approvalStatus; // PENDING, APPROVED, REJECTED
  final String? approvalMessage;
  final DateTime? lastCheckedAt;

  const LocalRegistrationData({
    required this.registrationData,
    required this.currentStep,
    required this.lastSavedAt,
    this.sessionId,
    this.isCompleted = false,
    this.registerId,
    this.completedAt,
    this.documentIds,
    this.nfcData,
    this.uploadedFiles = const [],
    this.serverResponse,
    this.serverStatus,
    this.serverMessage,
    this.isApproved,
    this.approvedAt,
    this.approvalStatus,
    this.approvalMessage,
    this.lastCheckedAt,
  });

  factory LocalRegistrationData.fromJson(Map<String, dynamic> json) {
    return LocalRegistrationData(
      registrationData: Map<String, dynamic>.from(
        json['registrationData'] ?? {},
      ),
      currentStep: json['currentStep'] ?? 0,
      lastSavedAt: DateTime.parse(json['lastSavedAt']),
      sessionId: json['sessionId'],
      isCompleted: json['isCompleted'] ?? false,
      registerId: json['registerId'],
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      documentIds: json['documentIds'] != null
          ? Map<String, String>.from(json['documentIds'])
          : null,
      nfcData: json['nfcData'],
      uploadedFiles: List<String>.from(json['uploadedFiles'] ?? []),
      serverResponse: json['serverResponse'],
      serverStatus: json['serverStatus'],
      serverMessage: json['serverMessage'],
      isApproved: json['isApproved'],
      approvedAt: json['approvedAt'] != null
          ? DateTime.parse(json['approvedAt'])
          : null,
      approvalStatus: json['approvalStatus'],
      approvalMessage: json['approvalMessage'],
      lastCheckedAt: json['lastCheckedAt'] != null
          ? DateTime.parse(json['lastCheckedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'registrationData': registrationData,
      'currentStep': currentStep,
      'lastSavedAt': lastSavedAt.toIso8601String(),
      'sessionId': sessionId,
      'isCompleted': isCompleted,
      'registerId': registerId,
      'completedAt': completedAt?.toIso8601String(),
      'documentIds': documentIds,
      'nfcData': nfcData,
      'uploadedFiles': uploadedFiles,
      'serverResponse': serverResponse,
      'serverStatus': serverStatus,
      'serverMessage': serverMessage,
      'isApproved': isApproved,
      'approvedAt': approvedAt?.toIso8601String(),
      'approvalStatus': approvalStatus,
      'approvalMessage': approvalMessage,
      'lastCheckedAt': lastCheckedAt?.toIso8601String(),
    };
  }

  LocalRegistrationData copyWith({
    Map<String, dynamic>? registrationData,
    int? currentStep,
    DateTime? lastSavedAt,
    String? sessionId,
    bool? isCompleted,
    String? registerId,
    DateTime? completedAt,
    Map<String, String>? documentIds,
    Map<String, dynamic>? nfcData,
    List<String>? uploadedFiles,
    Map<String, dynamic>? serverResponse,
    String? serverStatus,
    String? serverMessage,
    bool? isApproved,
    DateTime? approvedAt,
    String? approvalStatus,
    String? approvalMessage,
    DateTime? lastCheckedAt,
  }) {
    return LocalRegistrationData(
      registrationData: registrationData ?? this.registrationData,
      currentStep: currentStep ?? this.currentStep,
      lastSavedAt: lastSavedAt ?? this.lastSavedAt,
      sessionId: sessionId ?? this.sessionId,
      isCompleted: isCompleted ?? this.isCompleted,
      registerId: registerId ?? this.registerId,
      completedAt: completedAt ?? this.completedAt,
      documentIds: documentIds ?? this.documentIds,
      nfcData: nfcData ?? this.nfcData,
      uploadedFiles: uploadedFiles ?? this.uploadedFiles,
      serverResponse: serverResponse ?? this.serverResponse,
      serverStatus: serverStatus ?? this.serverStatus,
      serverMessage: serverMessage ?? this.serverMessage,
      isApproved: isApproved ?? this.isApproved,
      approvedAt: approvedAt ?? this.approvedAt,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      approvalMessage: approvalMessage ?? this.approvalMessage,
      lastCheckedAt: lastCheckedAt ?? this.lastCheckedAt,
    );
  }

  /// Kiểm tra xem registration có thể được sử dụng để đăng nhập không
  bool get canBeUsedForLogin =>
      isCompleted && registerId != null && approvalStatus == 'APPROVED';

  /// Kiểm tra xem có cần kiểm tra trạng thái approval không
  bool get needsApprovalCheck {
    if (!isCompleted || registerId == null) return false;
    if (lastCheckedAt == null) return true;

    // Kiểm tra lại sau 1 giờ
    final timeSinceLastCheck = DateTime.now().difference(lastCheckedAt!);
    return timeSinceLastCheck.inHours >= 1;
  }

  /// Lấy thông tin hiển thị cho user
  String get displayStatus {
    if (!isCompleted) return 'Chưa hoàn thành';

    switch (approvalStatus) {
      case 'APPROVED':
        return 'Đã được phê duyệt';
      case 'REJECTED':
        return 'Đã bị từ chối';
      case 'PENDING':
      default:
        return 'Đang chờ phê duyệt';
    }
  }

  @override
  String toString() {
    return 'LocalRegistrationData(currentStep: $currentStep, isCompleted: $isCompleted, registerId: $registerId, approvalStatus: $approvalStatus)';
  }
}
