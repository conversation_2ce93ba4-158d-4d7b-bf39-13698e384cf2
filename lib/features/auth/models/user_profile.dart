/// Model chứa thông tin chi tiết về user profile từ API
class UserProfile {
  final String id;
  final String username;
  final String email;
  final String displayName;
  final List<String>? roles;
  final String status;
  final String? avatar;
  
  // Person information
  final String? personId;
  final String? personCode;
  final String? personFullName;
  final String? personCifNo;
  final String? personPhoneNumber;
  final String? personIdCardNo;
  final String? personIssueDate;
  final String? personExpiryDate;
  final String? personIssuePlace;
  final String? personPermanentAddress;
  final String? personStatus;
  final String? personType;
  
  // Branch information
  final String? branchId;
  final String? branchCode;
  final String? branchName;
  final String? branchAddress;
  
  // Position information
  final String? positionId;
  final String? positionCode;
  final String? positionName;
  
  // Manager information
  final String? managerId;
  final String? managerFullName;

  const UserProfile({
    required this.id,
    required this.username,
    required this.email,
    required this.displayName,
    this.roles,
    required this.status,
    this.avatar,
    this.personId,
    this.personCode,
    this.personFullName,
    this.personCifNo,
    this.personPhoneNumber,
    this.personIdCardNo,
    this.personIssueDate,
    this.personExpiryDate,
    this.personIssuePlace,
    this.personPermanentAddress,
    this.personStatus,
    this.personType,
    this.branchId,
    this.branchCode,
    this.branchName,
    this.branchAddress,
    this.positionId,
    this.positionCode,
    this.positionName,
    this.managerId,
    this.managerFullName,
  });

  /// Tạo instance từ JSON response của API
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    // Parse roles - có thể là string hoặc array
    List<String>? roles;
    if (json['roles'] != null) {
      if (json['roles'] is String) {
        // Nếu roles là string, split theo dấu phẩy
        final rolesString = json['roles'] as String;
        roles = rolesString.split(',').map((role) => role.trim()).toList();
      } else if (json['roles'] is List) {
        // Nếu roles là array
        roles = List<String>.from(json['roles']);
      }
    }

    // Parse nested person object
    final person = json['person'] as Map<String, dynamic>?;
    
    // Parse nested branch object
    final branch = json['branch'] as Map<String, dynamic>?;
    
    // Parse nested position object
    final position = json['position'] as Map<String, dynamic>?;
    
    // Parse nested manager object
    final manager = json['manager'] as Map<String, dynamic>?;

    return UserProfile(
      id: json['id'] ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      displayName: json['display_name'] ?? '',
      roles: roles,
      status: json['status'] ?? '',
      avatar: json['avatar'],
      // Person information from nested person object
      personId: person?['id'],
      personCode: person?['code'],
      personFullName: person?['full_name'],
      personCifNo: person?['cif_no'],
      personPhoneNumber: person?['phone_number'],
      personIdCardNo: person?['id_card_no'],
      personIssueDate: person?['issue_date'],
      personExpiryDate: person?['expiry_date'],
      personIssuePlace: person?['issue_place'],
      personPermanentAddress: person?['permanent_address'],
      personStatus: person?['status'],
      personType: person?['type'],
      // Branch information from nested branch object
      branchId: branch?['id'],
      branchCode: branch?['code'],
      branchName: branch?['name'],
      branchAddress: branch?['address'],
      // Position information from nested position object
      positionId: position?['id'],
      positionCode: position?['code'],
      positionName: position?['name'],
      // Manager information from nested manager object
      managerId: manager?['id'],
      managerFullName: manager?['full_name'],
    );
  }

  /// Convert thành Map để lưu trữ
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'display_name': displayName,
      'roles': roles?.join(','), // Convert list to comma-separated string
      'status': status,
      'avatar': avatar,
      'person_id': personId,
      'person_code': personCode,
      'person_full_name': personFullName,
      'person_cif_no': personCifNo,
      'person_phone_number': personPhoneNumber,
      'person_id_card_no': personIdCardNo,
      'person_issue_date': personIssueDate,
      'person_expiry_date': personExpiryDate,
      'person_issue_place': personIssuePlace,
      'person_permanent_address': personPermanentAddress,
      'person_status': personStatus,
      'person_type': personType,
      'branch_id': branchId,
      'branch_code': branchCode,
      'branch_name': branchName,
      'branch_address': branchAddress,
      'position_id': positionId,
      'position_code': positionCode,
      'position_name': positionName,
      'manager_id': managerId,
      'manager_full_name': managerFullName,
    };
  }

  /// Copy with new values
  UserProfile copyWith({
    String? id,
    String? username,
    String? email,
    String? displayName,
    List<String>? roles,
    String? status,
    String? avatar,
    String? personId,
    String? personCode,
    String? personFullName,
    String? personCifNo,
    String? personPhoneNumber,
    String? personIdCardNo,
    String? personIssueDate,
    String? personExpiryDate,
    String? personIssuePlace,
    String? personPermanentAddress,
    String? personStatus,
    String? personType,
    String? branchId,
    String? branchCode,
    String? branchName,
    String? branchAddress,
    String? positionId,
    String? positionCode,
    String? positionName,
    String? managerId,
    String? managerFullName,
  }) {
    return UserProfile(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      roles: roles ?? this.roles,
      status: status ?? this.status,
      avatar: avatar ?? this.avatar,
      personId: personId ?? this.personId,
      personCode: personCode ?? this.personCode,
      personFullName: personFullName ?? this.personFullName,
      personCifNo: personCifNo ?? this.personCifNo,
      personPhoneNumber: personPhoneNumber ?? this.personPhoneNumber,
      personIdCardNo: personIdCardNo ?? this.personIdCardNo,
      personIssueDate: personIssueDate ?? this.personIssueDate,
      personExpiryDate: personExpiryDate ?? this.personExpiryDate,
      personIssuePlace: personIssuePlace ?? this.personIssuePlace,
      personPermanentAddress: personPermanentAddress ?? this.personPermanentAddress,
      personStatus: personStatus ?? this.personStatus,
      personType: personType ?? this.personType,
      branchId: branchId ?? this.branchId,
      branchCode: branchCode ?? this.branchCode,
      branchName: branchName ?? this.branchName,
      branchAddress: branchAddress ?? this.branchAddress,
      positionId: positionId ?? this.positionId,
      positionCode: positionCode ?? this.positionCode,
      positionName: positionName ?? this.positionName,
      managerId: managerId ?? this.managerId,
      managerFullName: managerFullName ?? this.managerFullName,
    );
  }

  @override
  String toString() {
    return 'UserProfile(id: $id, username: $username, email: $email, displayName: $displayName, status: $status, personFullName: $personFullName, branchName: $branchName, positionName: $positionName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile &&
        other.id == id &&
        other.username == username &&
        other.email == email;
  }

  @override
  int get hashCode {
    return Object.hash(id, username, email);
  }
} 