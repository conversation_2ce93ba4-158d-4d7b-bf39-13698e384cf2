// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'registration_response_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RegistrationResponseModel _$RegistrationResponseModelFromJson(
  Map<String, dynamic> json,
) => RegistrationResponseModel(
  registerId: json['register_id'] as String,
  registerType: json['register_type'] as String,
  fullName: json['full_name'] as String,
  idCardType: json['id_card_type'] as String,
  idCardNo: json['id_card_no'] as String,
  phoneNumber: json['phone_number'] as String,
  email: json['email'] as String,
  status: json['status'] as String,
  dataSource: json['data_source'] as String,
  provinceId: json['province_id'] as String,
  branchId: json['branch_id'] as String,
  positionId: json['position_id'] as String?,
  frontCardDocumentId: json['front_card_document_id'] as String?,
  backCardDocumentId: json['back_card_document_id'] as String?,
  action: json['action'] as String?,
  createdBy: json['created_by'] as String?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
);

Map<String, dynamic> _$RegistrationResponseModelToJson(
  RegistrationResponseModel instance,
) => <String, dynamic>{
  'register_id': instance.registerId,
  'register_type': instance.registerType,
  'full_name': instance.fullName,
  'id_card_type': instance.idCardType,
  'id_card_no': instance.idCardNo,
  'phone_number': instance.phoneNumber,
  'email': instance.email,
  'status': instance.status,
  'data_source': instance.dataSource,
  'province_id': instance.provinceId,
  'branch_id': instance.branchId,
  'position_id': instance.positionId,
  'front_card_document_id': instance.frontCardDocumentId,
  'back_card_document_id': instance.backCardDocumentId,
  'action': instance.action,
  'created_by': instance.createdBy,
  'created_at': instance.createdAt?.toIso8601String(),
};
