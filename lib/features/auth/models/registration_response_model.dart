import 'package:json_annotation/json_annotation.dart';

part 'registration_response_model.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class RegistrationResponseModel {
  final String registerId;
  final String registerType;
  final String fullName;
  final String idCardType;
  final String idCardNo;
  final String phoneNumber;
  final String email;
  final String status;
  final String dataSource;
  final String provinceId;
  final String branchId;
  final String? positionId;
  final String? frontCardDocumentId;
  final String? backCardDocumentId;
  final String? action;
  final String? createdBy;
  final DateTime? createdAt;

  const RegistrationResponseModel({
    required this.registerId,
    required this.registerType,
    required this.fullName,
    required this.idCardType,
    required this.idCardNo,
    required this.phoneNumber,
    required this.email,
    required this.status,
    required this.dataSource,
    required this.provinceId,
    required this.branchId,
    this.positionId,
    this.frontCardDocumentId,
    this.backCardDocumentId,
    this.action,
    this.createdBy,
    this.createdAt,
  });

  factory RegistrationResponseModel.fromJson(Map<String, dynamic> json) {
    // Handle response format từ PostgreSQL function
    // Function trả về: {success, message, code, data: {...}}
    // Chúng ta cần parse từ data field
    Map<String, dynamic> data;
    
    if (json.containsKey('data')) {
      // Response từ PostgreSQL function
      data = json['data'] as Map<String, dynamic>;
    } else {
      // Direct object response
      data = json;
    }
    
    return _$RegistrationResponseModelFromJson(data);
  }

  Map<String, dynamic> toJson() => _$RegistrationResponseModelToJson(this);

  RegistrationResponseModel copyWith({
    String? registerId,
    String? registerType,
    String? fullName,
    String? idCardType,
    String? idCardNo,
    String? phoneNumber,
    String? email,
    String? status,
    String? dataSource,
    String? provinceId,
    String? branchId,
    String? positionId,
    String? frontCardDocumentId,
    String? backCardDocumentId,
    String? action,
    String? createdBy,
    DateTime? createdAt,
  }) {
    return RegistrationResponseModel(
      registerId: registerId ?? this.registerId,
      registerType: registerType ?? this.registerType,
      fullName: fullName ?? this.fullName,
      idCardType: idCardType ?? this.idCardType,
      idCardNo: idCardNo ?? this.idCardNo,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      status: status ?? this.status,
      dataSource: dataSource ?? this.dataSource,
      provinceId: provinceId ?? this.provinceId,
      branchId: branchId ?? this.branchId,
      positionId: positionId ?? this.positionId,
      frontCardDocumentId: frontCardDocumentId ?? this.frontCardDocumentId,
      backCardDocumentId: backCardDocumentId ?? this.backCardDocumentId,
      action: action ?? this.action,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'RegistrationResponseModel(registerId: $registerId, fullName: $fullName, status: $status, action: $action)';
  }
} 