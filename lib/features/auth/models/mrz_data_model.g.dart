// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mrz_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MrzDataModel _$MrzDataModelFromJson(Map<String, dynamic> json) => MrzDataModel(
  rawLines: (json['rawLines'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  documentType: json['documentType'] as String?,
  countryCode: json['countryCode'] as String?,
  surnames: json['surnames'] as String?,
  givenNames: json['givenNames'] as String?,
  documentNumber: json['documentNumber'] as String?,
  nationalityCountryCode: json['nationalityCountryCode'] as String?,
  birthDate: json['birthDate'] as String?,
  sex: json['sex'] as String?,
  expiryDate: json['expiryDate'] as String?,
  personalNumber: json['personalNumber'] as String?,
  personalNumber2: json['personalNumber2'] as String?,
);

Map<String, dynamic> _$MrzDataModelToJson(MrzDataModel instance) =>
    <String, dynamic>{
      'rawLines': instance.rawLines,
      'documentType': instance.documentType,
      'countryCode': instance.countryCode,
      'surnames': instance.surnames,
      'givenNames': instance.givenNames,
      'documentNumber': instance.documentNumber,
      'nationalityCountryCode': instance.nationalityCountryCode,
      'birthDate': instance.birthDate,
      'sex': instance.sex,
      'expiryDate': instance.expiryDate,
      'personalNumber': instance.personalNumber,
      'personalNumber2': instance.personalNumber2,
    };
