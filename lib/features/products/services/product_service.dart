import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/api/api_service.dart';
import 'package:kiloba_biz/shared/models/index.dart';
import '../models/product_model.dart';

/// Service để quản lý products từ backend API
class ProductService {
  static final ProductService _instance = ProductService._internal();
  factory ProductService() => _instance;
  ProductService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // API endpoints
  static const String _getProductsEndpoint = '/rest/rpc/get_products';

  // Cache data
  BaseResponse<ProductsListData>? _productsCache;
  DateTime? _lastCacheTime;
  
  // Cache timeout trong phút
  static const int _cacheTimeoutMinutes = 10;

  /// Lấy danh sách tất cả sản phẩm
  Future<BaseResponse<ProductsListData>> getProducts() async {
    try {
      _logger.i('=== START: ProductService.getProducts ===');
      _logger.i('API endpoint: $_getProductsEndpoint');

      // Check cache
      if (_isCacheValid() && _productsCache != null) {
        _logger.i('Returning cached products');
        return _productsCache!;
      }

      final response = await _apiService.post(_getProductsEndpoint);

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response là object với format {success, code, message, data}
      if (response.data is Map<String, dynamic>) {
        final Map<String, dynamic> responseMap = response.data as Map<String, dynamic>;
        _logger.i('Response map keys: ${responseMap.keys.toList()}');
        
        // Sử dụng BaseResponse.fromJson với custom fromJsonT function
        final baseResponse = BaseResponse.fromJson(
          responseMap,
          (data) => _parseProductsListDataFromData(data),
        );
        
        _logger.i('API success: ${baseResponse.success}, code: ${baseResponse.code}, message: ${baseResponse.message}');
        
        if (baseResponse.success) {
          // Cache kết quả
          _productsCache = baseResponse;
          _updateCacheTime();
          
          _logger.i('Products parsed successfully: ${baseResponse.data?.products.length ?? 0} items');
          _logger.i('=== END: ProductService.getProducts ===');
          return baseResponse;
        } else {
          _logger.e('API returned success: false, message: ${baseResponse.message}');
          throw ProductException(
            message: baseResponse.message,
            type: ProductExceptionType.apiError,
            code: baseResponse.code,
          );
        }
      } else {
        _logger.e('Invalid response format - expected Map but got ${response.data.runtimeType}');
        throw ProductException(
          message: 'Invalid response format for products',
          type: ProductExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching products: ${e.message}');
      throw ProductException(
        message: 'Không thể lấy danh sách sản phẩm: ${e.message}',
        type: ProductExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching products: $e');
      throw ProductException(
        message: 'Lỗi không xác định khi lấy danh sách sản phẩm',
        type: ProductExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy sản phẩm theo ID
  Future<ProductModel?> getProductById(String productId) async {
    try {
      _logger.i('=== START: ProductService.getProductById ===');
      _logger.i('Product ID: $productId');

      // Tìm trong cache trước
      if (_productsCache != null && _productsCache!.data != null) {
        final product = _productsCache!.data!.products.firstWhere(
          (p) => p.id == productId,
          orElse: () => throw ProductException(
            message: 'Không tìm thấy sản phẩm với ID: $productId',
            type: ProductExceptionType.notFound,
          ),
        );
        
        _logger.i('Product found in cache: ${product.name}');
        _logger.i('=== END: ProductService.getProductById ===');
        return product;
      }

      // Nếu chưa có cache, fetch tất cả products
      final productsResponse = await getProducts();
      final productsData = productsResponse.data!;
      final product = productsData.products.firstWhere(
        (p) => p.id == productId,
        orElse: () => throw ProductException(
          message: 'Không tìm thấy sản phẩm với ID: $productId',
          type: ProductExceptionType.notFound,
        ),
      );

      _logger.i('Product found: ${product.name}');
      _logger.i('=== END: ProductService.getProductById ===');
      return product;
    } catch (e) {
      if (e is ProductException) {
        rethrow;
      }
      
      _logger.e('Unknown error when getting product by ID: $e');
      throw ProductException(
        message: 'Lỗi không xác định khi lấy sản phẩm theo ID',
        type: ProductExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy sản phẩm theo code
  Future<ProductModel?> getProductByCode(String productCode) async {
    try {
      _logger.i('=== START: ProductService.getProductByCode ===');
      _logger.i('Product Code: $productCode');

      // Tìm trong cache trước
      if (_productsCache != null && _productsCache!.data != null) {
        final product = _productsCache!.data!.products.firstWhere(
          (p) => p.code.toLowerCase() == productCode.toLowerCase(),
          orElse: () => throw ProductException(
            message: 'Không tìm thấy sản phẩm với code: $productCode',
            type: ProductExceptionType.notFound,
          ),
        );
        
        _logger.i('Product found in cache: ${product.name}');
        _logger.i('=== END: ProductService.getProductByCode ===');
        return product;
      }

      // Nếu chưa có cache, fetch tất cả products
      final productsResponse = await getProducts();
      final productsData = productsResponse.data!;
      final product = productsData.products.firstWhere(
        (p) => p.code.toLowerCase() == productCode.toLowerCase(),
        orElse: () => throw ProductException(
          message: 'Không tìm thấy sản phẩm với code: $productCode',
          type: ProductExceptionType.notFound,
        ),
      );

      _logger.i('Product found: ${product.name}');
      _logger.i('=== END: ProductService.getProductByCode ===');
      return product;
    } catch (e) {
      if (e is ProductException) {
        rethrow;
      }
      
      _logger.e('Unknown error when getting product by code: $e');
      throw ProductException(
        message: 'Lỗi không xác định khi lấy sản phẩm theo code',
        type: ProductExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy sản phẩm active
  Future<List<ProductModel>> getActiveProducts() async {
    try {
      _logger.i('=== START: ProductService.getActiveProducts ===');

      final productsResponse = await getProducts();
      final productsData = productsResponse.data!;
      final activeProducts = productsData.activeProducts;

      _logger.i('Active products found: ${activeProducts.length}');
      _logger.i('=== END: ProductService.getActiveProducts ===');
      return activeProducts;
    } catch (e) {
      if (e is ProductException) {
        rethrow;
      }
      
      _logger.e('Unknown error when getting active products: $e');
      throw ProductException(
        message: 'Lỗi không xác định khi lấy sản phẩm active',
        type: ProductExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy sản phẩm theo group
  Future<List<ProductModel>> getProductsByGroup(String group) async {
    try {
      _logger.i('=== START: ProductService.getProductsByGroup ===');
      _logger.i('Group: $group');

      final productsResponse = await getProducts();
      final productsData = productsResponse.data!;
      final groupProducts = productsData.getProductsByGroup(group);

      _logger.i('Products in group "$group": ${groupProducts.length}');
      _logger.i('=== END: ProductService.getProductsByGroup ===');
      return groupProducts;
    } catch (e) {
      if (e is ProductException) {
        rethrow;
      }
      
      _logger.e('Unknown error when getting products by group: $e');
      throw ProductException(
        message: 'Lỗi không xác định khi lấy sản phẩm theo nhóm',
        type: ProductExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy sản phẩm featured
  Future<List<ProductModel>> getFeaturedProducts() async {
    try {
      _logger.i('=== START: ProductService.getFeaturedProducts ===');

      final productsResponse = await getProducts();
      final productsData = productsResponse.data!;
      final featuredProducts = productsData.featuredProducts;

      _logger.i('Featured products found: ${featuredProducts.length}');
      _logger.i('=== END: ProductService.getFeaturedProducts ===');
      return featuredProducts;
    } catch (e) {
      if (e is ProductException) {
        rethrow;
      }
      
      _logger.e('Unknown error when getting featured products: $e');
      throw ProductException(
        message: 'Lỗi không xác định khi lấy sản phẩm nổi bật',
        type: ProductExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Tìm kiếm sản phẩm theo tên hoặc mô tả
  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      _logger.i('=== START: ProductService.searchProducts ===');
      _logger.i('Search query: $query');

      if (query.trim().isEmpty) {
        _logger.i('Empty query, returning all products');
        final productsResponse = await getProducts();
        final productsData = productsResponse.data!;
        return productsData.products;
      }

      final productsResponse = await getProducts();
      final productsData = productsResponse.data!;
      final searchQuery = query.toLowerCase().trim();
      
      final searchResults = productsData.products.where((product) {
        return product.name.toLowerCase().contains(searchQuery) ||
               product.description.toLowerCase().contains(searchQuery) ||
               product.code.toLowerCase().contains(searchQuery);
      }).toList();

      _logger.i('Search results found: ${searchResults.length}');
      _logger.i('=== END: ProductService.searchProducts ===');
      return searchResults;
    } catch (e) {
      if (e is ProductException) {
        rethrow;
      }
      
      _logger.e('Unknown error when searching products: $e');
      throw ProductException(
        message: 'Lỗi không xác định khi tìm kiếm sản phẩm',
        type: ProductExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy cache products nếu có
  BaseResponse<ProductsListData>? getCachedProducts() => _productsCache;

  /// Clear cache
  void clearCache() {
    _productsCache = null;
    _lastCacheTime = null;
    _logger.i('Products cache cleared');
  }

  /// Refresh products data
  Future<BaseResponse<ProductsListData>> refreshProducts() async {
    try {
      clearCache();
      return await getProducts();
    } catch (e) {
      _logger.w('Failed to refresh products: $e');
      rethrow;
    }
  }

  /// Kiểm tra tính khả dụng của products API
  Future<bool> checkProductsApiAvailability() async {
    try {
      final response = await getProducts();
      return response.isSuccess;
    } catch (e) {
      _logger.w('Products API not available: $e');
      return false;
    }
  }

  /// Validate product ID format
  bool isValidProductId(String productId) {
    return productId.trim().isNotEmpty;
  }

  /// Validate product code format
  bool isValidProductCode(String productCode) {
    return productCode.trim().isNotEmpty;
  }

  /// Format product name cho display
  String formatProductName(ProductModel product) {
    return product.displayName;
  }

  /// Format product description cho display
  String formatProductDescription(ProductModel product) {
    return product.displayDescription;
  }

  /// Get unique product groups
  Future<List<String>> getProductGroups() async {
    try {
      final productsResponse = await getProducts();
      final productsData = productsResponse.data!;
      final groups = productsData.products
          .map((product) => product.group)
          .where((group) => group.isNotEmpty)
          .toSet()
          .toList();
      
      groups.sort();
      return groups;
    } catch (e) {
      _logger.e('Error getting product groups: $e');
      return [];
    }
  }

  // Private helper methods

  /// Helper function để parse ProductsListData từ API response data
  ProductsListData _parseProductsListDataFromData(Object? data) {
    if (data == null) {
      _logger.w('No data in products response, returning empty data');
      return ProductsListData.empty;
    }

    if (data is! Map<String, dynamic>) {
      throw ProductException(
        message: 'Data không đúng định dạng Map<String, dynamic>',
        type: ProductExceptionType.invalidResponse,
      );
    }

    final dataMap = data;
    
    try {
      final productsJson = dataMap['products'] as List<dynamic>? ?? [];
      _logger.i('Products list length: ${productsJson.length}');
      
      final products = productsJson
          .map((productJson) => ProductModel.fromJson(productJson as Map<String, dynamic>))
          .toList();
      
      return ProductsListData(
        products: products,
        totalCount: products.length,
      );
    } catch (e) {
      _logger.e('Error parsing products data: $e');
      throw ProductException(
        message: 'Lỗi parse dữ liệu sản phẩm: $e',
        type: ProductExceptionType.invalidResponse,
      );
    }
  }

  /// Kiểm tra cache có hợp lệ không
  bool _isCacheValid() {
    if (_lastCacheTime == null) return false;
    
    final now = DateTime.now();
    final difference = now.difference(_lastCacheTime!);
    return difference.inMinutes < _cacheTimeoutMinutes;
  }

  /// Cập nhật thời gian cache
  void _updateCacheTime() {
    _lastCacheTime = DateTime.now();
  }


}

/// Custom exception cho Product service
class ProductException implements Exception {
  final String message;
  final ProductExceptionType type;
  final String? code;
  final Object? originalException;

  const ProductException({
    required this.message,
    required this.type,
    this.code,
    this.originalException,
  });

  @override
  String toString() => 'ProductException: $message (Type: $type${code != null ? ', Code: $code' : ''})';
}

/// Loại lỗi Product
enum ProductExceptionType {
  notFound,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
}
