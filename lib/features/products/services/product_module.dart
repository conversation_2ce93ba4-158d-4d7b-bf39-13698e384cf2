import '../../../shared/core/feature_module.dart';
import 'product_service.dart';

/// Product Feature Module
/// Organizes product-related services and provides public interface
class ProductModule extends FeatureModule with SingletonFeatureModule<ProductModule> {
  static ProductModule? _instance;
  bool _initialized = false;

  /// Singleton instance getter
  static ProductModule get instance {
    return _instance ??= ProductModule._internal();
  }

  ProductModule._internal();

  @override
  String get featureName => 'product';

  @override
  bool get isInitialized => _initialized;

  // Feature-scoped service getters (internal singletons)
  ProductService get productService => ProductService();

  @override
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      logInfo('Initializing product module...');
      
      // Product service không cần initialize đặc biệt
      // Chỉ cần đảm bảo service locator đã sẵn sàng
      
      _initialized = true;
      logInfo('Product module initialized successfully');
      
    } catch (e) {
      logError('Product module initialization failed', e);
      rethrow;
    }
  }

  @override
  void dispose() {
    try {
      logInfo('Disposing product module...');
          
      // Clear cache khi dispose
      productService.clearCache();
      
      _initialized = false;
      logInfo('Product module disposed');
      
    } catch (e) {
      logError('Product module disposal error', e);
    }
  }

  /// For testing - reset module state
  static void resetForTesting() {
    _instance?._initialized = false;
    _instance = null;
  }
}
