import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/models/index.dart';
import '../services/product_service.dart';
import '../models/product_model.dart';
import 'product_event.dart';
import 'product_state.dart';

/// Bloc để quản lý product state và xử lý events
class ProductBloc extends Bloc<ProductEvent, ProductState> {
  final ProductService _productService;
  final IAppLogger _logger;

  ProductBloc({
    ProductService? productService,
    IAppLogger? logger,
  }) : _productService = productService ?? ProductService(),
       _logger = logger ?? getIt.get<IAppLogger>(),
       super(ProductInitial()) {
    // Register event handlers
    on<LoadProducts>(_onLoadProducts);
    on<RefreshProducts>(_onRefreshProducts);
    on<SearchProducts>(_onSearchProducts);
    on<FilterProductsByGroup>(_onFilterProductsByGroup);
    on<SelectProduct>(_onSelectProduct);
    on<ClearProductSelection>(_onClearProductSelection);
    on<LoadProductDetail>(_onLoadProductDetail);
    on<ClearProductCache>(_onClearProductCache);
    on<RetryLoadProducts>(_onRetryLoadProducts);
    on<LoadActiveProducts>(_onLoadActiveProducts);
    on<LoadFeaturedProducts>(_onLoadFeaturedProducts);
    on<LoadProductGroups>(_onLoadProductGroups);
  }

  /// Load danh sách sản phẩm
  Future<void> _onLoadProducts(
    LoadProducts event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.LoadProducts ===');
      emit(ProductLoading());

      // Load products và groups concurrently
      final results = await Future.wait([
        _productService.getProducts(),
        _productService.getProductGroups(),
      ]);

      final productsResponse = results[0] as BaseResponse<ProductsListData>;
      final groups = results[1] as List<String>;

      if (productsResponse.isSuccess && productsResponse.data != null) {
        final products = productsResponse.data!.products;
        
        if (products.isEmpty) {
          emit(ProductEmpty());
        } else {
          emit(ProductLoaded(
            products: products,
            filteredProducts: products,
            productGroups: groups,
          ));
        }
      } else {
        emit(ProductError(
          message: productsResponse.message,
          type: ProductErrorType.server,
        ));
      }

      _logger.i('=== END: ProductBloc.LoadProducts ===');
    } catch (e) {
      _logger.e('Error loading products: $e');
      emit(ProductError(
        message: 'Không thể tải danh sách sản phẩm',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Refresh danh sách sản phẩm
  Future<void> _onRefreshProducts(
    RefreshProducts event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.RefreshProducts ===');
      
      // Emit loading state với isRefreshing = true
      if (state is ProductLoaded) {
        emit((state as ProductLoaded).copyWith(isRefreshing: true));
      } else {
        emit(ProductLoading(isRefreshing: true));
      }

      // Clear cache và reload
      _productService.clearCache();
      
      final results = await Future.wait([
        _productService.getProducts(),
        _productService.getProductGroups(),
      ]);

      final productsResponse = results[0] as BaseResponse<ProductsListData>;
      final groups = results[1] as List<String>;

      if (productsResponse.isSuccess && productsResponse.data != null) {
        final products = productsResponse.data!.products;
        
        if (products.isEmpty) {
          emit(ProductEmpty());
        } else {
          emit(ProductLoaded(
            products: products,
            filteredProducts: products,
            productGroups: groups,
            isRefreshing: false,
          ));
        }
      } else {
        emit(ProductError(
          message: productsResponse.message,
          type: ProductErrorType.server,
        ));
      }

      _logger.i('=== END: ProductBloc.RefreshProducts ===');
    } catch (e) {
      _logger.e('Error refreshing products: $e');
      emit(ProductError(
        message: 'Không thể làm mới danh sách sản phẩm',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Tìm kiếm sản phẩm
  Future<void> _onSearchProducts(
    SearchProducts event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.SearchProducts ===');
      _logger.i('Search query: ${event.query}');

      if (state is! ProductLoaded) {
        // Nếu chưa load products, load trước
        add(LoadProducts());
        return;
      }

      final currentState = state as ProductLoaded;
      
      if (event.query.trim().isEmpty) {
        // Clear search - hiển thị tất cả sản phẩm
        emit(currentState.copyWith(
          filteredProducts: currentState.products,
          searchQuery: '',
        ));
      } else {
        // Thực hiện search
        final searchResults = await _productService.searchProducts(event.query);
        
        emit(currentState.copyWith(
          filteredProducts: searchResults,
          searchQuery: event.query,
        ));
      }

      _logger.i('=== END: ProductBloc.SearchProducts ===');
    } catch (e) {
      _logger.e('Error searching products: $e');
      emit(ProductError(
        message: 'Không thể tìm kiếm sản phẩm',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Lọc sản phẩm theo nhóm
  Future<void> _onFilterProductsByGroup(
    FilterProductsByGroup event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.FilterProductsByGroup ===');
      _logger.i('Filter group: ${event.group}');

      if (state is! ProductLoaded) {
        // Nếu chưa load products, load trước
        add(LoadProducts());
        return;
      }

      final currentState = state as ProductLoaded;
      
      if (event.group == null || event.group!.trim().isEmpty) {
        // Clear filter - hiển thị tất cả sản phẩm
        emit(currentState.copyWith(
          filteredProducts: currentState.products,
          clearSelectedGroup: true,
        ));
      } else {
        // Thực hiện filter
        final filteredProducts = await _productService.getProductsByGroup(event.group!);
        
        emit(currentState.copyWith(
          filteredProducts: filteredProducts,
          selectedGroup: event.group,
        ));
      }

      _logger.i('=== END: ProductBloc.FilterProductsByGroup ===');
    } catch (e) {
      _logger.e('Error filtering products by group: $e');
      emit(ProductError(
        message: 'Không thể lọc sản phẩm theo nhóm',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Chọn sản phẩm
  Future<void> _onSelectProduct(
    SelectProduct event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.SelectProduct ===');
      _logger.i('Product ID: ${event.productId}');

      if (state is! ProductLoaded) {
        // Nếu chưa load products, load trước
        add(LoadProducts());
        return;
      }

      final currentState = state as ProductLoaded;
      
      // Tìm sản phẩm theo ID
      final product = await _productService.getProductById(event.productId);
      
      if (product != null) {
        emit(currentState.copyWith(selectedProduct: product));
        _logger.i('Product selected: ${product.name}');
      } else {
        emit(ProductError(
          message: 'Không tìm thấy sản phẩm',
          type: ProductErrorType.notFound,
        ));
      }

      _logger.i('=== END: ProductBloc.SelectProduct ===');
    } catch (e) {
      _logger.e('Error selecting product: $e');
      emit(ProductError(
        message: 'Không thể chọn sản phẩm',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Clear product selection
  void _onClearProductSelection(
    ClearProductSelection event,
    Emitter<ProductState> emit,
  ) {
    _logger.i('=== ProductBloc.ClearProductSelection ===');
    
    if (state is ProductLoaded) {
      final currentState = state as ProductLoaded;
      emit(currentState.copyWith(clearSelectedProduct: true));
    }
  }

  /// Load chi tiết sản phẩm
  Future<void> _onLoadProductDetail(
    LoadProductDetail event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.LoadProductDetail ===');
      _logger.i('Product ID: ${event.productId}');

      emit(ProductActionInProgress(
        actionType: 'loadDetail',
        productId: event.productId,
      ));

      final product = await _productService.getProductById(event.productId);
      
      if (product != null) {
        emit(ProductActionSuccess(
          actionType: 'loadDetail',
          productId: event.productId,
          message: 'Đã tải chi tiết sản phẩm',
        ));
      } else {
        emit(ProductError(
          message: 'Không tìm thấy sản phẩm',
          type: ProductErrorType.notFound,
        ));
      }

      _logger.i('=== END: ProductBloc.LoadProductDetail ===');
    } catch (e) {
      _logger.e('Error loading product detail: $e');
      emit(ProductError(
        message: 'Không thể tải chi tiết sản phẩm',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Clear cache
  void _onClearProductCache(
    ClearProductCache event,
    Emitter<ProductState> emit,
  ) {
    _logger.i('=== ProductBloc.ClearProductCache ===');
    _productService.clearCache();
    emit(ProductInitial());
  }

  /// Retry load products
  void _onRetryLoadProducts(
    RetryLoadProducts event,
    Emitter<ProductState> emit,
  ) {
    _logger.i('=== ProductBloc.RetryLoadProducts ===');
    add(LoadProducts());
  }

  /// Load active products
  Future<void> _onLoadActiveProducts(
    LoadActiveProducts event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.LoadActiveProducts ===');

      if (state is! ProductLoaded) {
        // Nếu chưa load products, load trước
        add(LoadProducts());
        return;
      }

      final currentState = state as ProductLoaded;
      final activeProducts = await _productService.getActiveProducts();
      
      emit(currentState.copyWith(filteredProducts: activeProducts));

      _logger.i('=== END: ProductBloc.LoadActiveProducts ===');
    } catch (e) {
      _logger.e('Error loading active products: $e');
      emit(ProductError(
        message: 'Không thể tải sản phẩm đang hoạt động',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Load featured products
  Future<void> _onLoadFeaturedProducts(
    LoadFeaturedProducts event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.LoadFeaturedProducts ===');

      if (state is! ProductLoaded) {
        // Nếu chưa load products, load trước
        add(LoadProducts());
        return;
      }

      final currentState = state as ProductLoaded;
      final featuredProducts = await _productService.getFeaturedProducts();
      
      emit(currentState.copyWith(filteredProducts: featuredProducts));

      _logger.i('=== END: ProductBloc.LoadFeaturedProducts ===');
    } catch (e) {
      _logger.e('Error loading featured products: $e');
      emit(ProductError(
        message: 'Không thể tải sản phẩm nổi bật',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Load product groups
  Future<void> _onLoadProductGroups(
    LoadProductGroups event,
    Emitter<ProductState> emit,
  ) async {
    try {
      _logger.i('=== START: ProductBloc.LoadProductGroups ===');

      final groups = await _productService.getProductGroups();
      
      if (state is ProductLoaded) {
        final currentState = state as ProductLoaded;
        emit(currentState.copyWith(productGroups: groups));
      }

      _logger.i('=== END: ProductBloc.LoadProductGroups ===');
    } catch (e) {
      _logger.e('Error loading product groups: $e');
      emit(ProductError(
        message: 'Không thể tải nhóm sản phẩm',
        type: _mapExceptionToErrorType(e),
        originalError: e,
      ));
    }
  }

  /// Map exception to error type
  ProductErrorType _mapExceptionToErrorType(Object error) {
    if (error is ProductException) {
      switch (error.type) {
        case ProductExceptionType.notFound:
          return ProductErrorType.notFound;
        case ProductExceptionType.unauthorized:
          return ProductErrorType.unauthorized;
        case ProductExceptionType.apiError:
          return ProductErrorType.server;
        case ProductExceptionType.invalidResponse:
          return ProductErrorType.server;
        case ProductExceptionType.unknown:
          return ProductErrorType.unknown;
      }
    }
    return ProductErrorType.unknown;
  }
}
