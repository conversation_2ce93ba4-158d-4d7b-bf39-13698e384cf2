import 'package:flutter/foundation.dart';
import '../models/product_model.dart';

/// Base class cho tất cả product states
@immutable
abstract class ProductState {}

/// Initial state khi vừa khởi tạo bloc
class ProductInitial extends ProductState {}

/// State đang loading data
class ProductLoading extends ProductState {
  final bool isRefreshing;

  ProductLoading({this.isRefreshing = false});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductLoading && other.isRefreshing == isRefreshing;
  }

  @override
  int get hashCode => isRefreshing.hashCode;

  @override
  String toString() => 'ProductLoading(isRefreshing: $isRefreshing)';
}

/// State đã load thành công data
class ProductLoaded extends ProductState {
  final List<ProductModel> products;
  final List<ProductModel> filteredProducts;
  final List<String> productGroups;
  final ProductModel? selectedProduct;
  final String? selectedGroup;
  final String searchQuery;
  final bool isRefreshing;
  final bool hasError;
  final String? errorMessage;
  
  ProductLoaded({
    required this.products,
    this.filteredProducts = const [],
    this.productGroups = const [],
    this.selectedProduct,
    this.selectedGroup,
    this.searchQuery = '',
    this.isRefreshing = false,
    this.hasError = false,
    this.errorMessage,
  });

  /// Copy with method để update state
  ProductLoaded copyWith({
    List<ProductModel>? products,
    List<ProductModel>? filteredProducts,
    List<String>? productGroups,
    ProductModel? selectedProduct,
    String? selectedGroup,
    String? searchQuery,
    bool? isRefreshing,
    bool? hasError,
    String? errorMessage,
    bool clearSelectedProduct = false,
    bool clearSelectedGroup = false,
  }) {
    return ProductLoaded(
      products: products ?? this.products,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      productGroups: productGroups ?? this.productGroups,
      selectedProduct: clearSelectedProduct ? null : (selectedProduct ?? this.selectedProduct),
      selectedGroup: clearSelectedGroup ? null : (selectedGroup ?? this.selectedGroup),
      searchQuery: searchQuery ?? this.searchQuery,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  /// Lấy danh sách sản phẩm active
  List<ProductModel> get activeProducts => products.where((p) => p.isActive).toList();

  /// Lấy danh sách sản phẩm featured
  List<ProductModel> get featuredProducts => products.where((p) => p.isFeatured).toList();

  /// Kiểm tra có sản phẩm nào được chọn không
  bool get hasSelectedProduct => selectedProduct != null;

  /// Kiểm tra có filter theo nhóm không
  bool get hasGroupFilter => selectedGroup != null && selectedGroup!.isNotEmpty;

  /// Kiểm tra có search query không
  bool get hasSearchQuery => searchQuery.isNotEmpty;

  /// Lấy danh sách sản phẩm hiển thị (đã filter và search)
  List<ProductModel> get displayProducts {
    if (filteredProducts.isNotEmpty) {
      return filteredProducts;
    }
    return products;
  }

  /// Số lượng sản phẩm
  int get totalProducts => products.length;

  /// Số lượng sản phẩm active
  int get activeProductsCount => activeProducts.length;

  /// Số lượng sản phẩm featured
  int get featuredProductsCount => featuredProducts.length;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductLoaded &&
        listEquals(other.products, products) &&
        listEquals(other.filteredProducts, filteredProducts) &&
        listEquals(other.productGroups, productGroups) &&
        other.selectedProduct == selectedProduct &&
        other.selectedGroup == selectedGroup &&
        other.searchQuery == searchQuery &&
        other.isRefreshing == isRefreshing &&
        other.hasError == hasError &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(
      Object.hashAll(products),
      Object.hashAll(filteredProducts),
      Object.hashAll(productGroups),
      selectedProduct,
      selectedGroup,
      searchQuery,
      isRefreshing,
      hasError,
      errorMessage,
    );
  }

  @override
  String toString() {
    return 'ProductLoaded('
        'products: ${products.length}, '
        'filteredProducts: ${filteredProducts.length}, '
        'productGroups: ${productGroups.length}, '
        'selectedProduct: ${selectedProduct?.name}, '
        'selectedGroup: $selectedGroup, '
        'searchQuery: $searchQuery, '
        'isRefreshing: $isRefreshing, '
        'hasError: $hasError, '
        'errorMessage: $errorMessage'
        ')';
  }
}

/// State khi có lỗi
class ProductError extends ProductState {
  final String message;
  final ProductErrorType type;
  final Object? originalError;

  ProductError({
    required this.message,
    required this.type,
    this.originalError,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductError &&
        other.message == message &&
        other.type == type &&
        other.originalError == originalError;
  }

  @override
  int get hashCode => Object.hash(message, type, originalError);

  @override
  String toString() => 'ProductError(message: $message, type: $type)';
}

/// Enum cho các loại lỗi product
enum ProductErrorType {
  network,
  server,
  unauthorized,
  notFound,
  unknown,
}

/// State cho empty/no data
class ProductEmpty extends ProductState {
  final String message;

  ProductEmpty({this.message = 'Không có sản phẩm nào'});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductEmpty && other.message == message;
  }

  @override
  int get hashCode => message.hashCode;

  @override
  String toString() => 'ProductEmpty(message: $message)';
}

/// State khi đang thực hiện action (select product, etc.)
class ProductActionInProgress extends ProductState {
  final String actionType;
  final String? productId;

  ProductActionInProgress({
    required this.actionType,
    this.productId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductActionInProgress &&
        other.actionType == actionType &&
        other.productId == productId;
  }

  @override
  int get hashCode => Object.hash(actionType, productId);

  @override
  String toString() => 'ProductActionInProgress(actionType: $actionType, productId: $productId)';
}

/// State khi action thành công
class ProductActionSuccess extends ProductState {
  final String actionType;
  final String? productId;
  final String message;

  ProductActionSuccess({
    required this.actionType,
    this.productId,
    required this.message,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductActionSuccess &&
        other.actionType == actionType &&
        other.productId == productId &&
        other.message == message;
  }

  @override
  int get hashCode => Object.hash(actionType, productId, message);

  @override
  String toString() => 'ProductActionSuccess(actionType: $actionType, productId: $productId, message: $message)';
}
