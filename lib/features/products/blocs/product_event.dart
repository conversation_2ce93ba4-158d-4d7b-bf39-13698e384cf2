import 'package:flutter/foundation.dart';

/// Base class cho tất cả product events
@immutable
abstract class ProductEvent {}

/// Event để load danh sách sản phẩm
class LoadProducts extends ProductEvent {}

/// Event để refresh danh sách sản phẩm (pull-to-refresh)
class RefreshProducts extends ProductEvent {}

/// Event để tìm kiếm sản phẩm
class SearchProducts extends ProductEvent {
  final String query;

  SearchProducts(this.query);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchProducts && other.query == query;
  }

  @override
  int get hashCode => query.hashCode;

  @override
  String toString() => 'SearchProducts(query: $query)';
}

/// Event để lọc sản phẩm theo nhóm
class FilterProductsByGroup extends ProductEvent {
  final String? group;

  FilterProductsByGroup(this.group);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FilterProductsByGroup && other.group == group;
  }

  @override
  int get hashCode => group.hashCode;

  @override
  String toString() => 'FilterProductsByGroup(group: $group)';
}

/// Event để chọn sản phẩm
class SelectProduct extends ProductEvent {
  final String productId;

  SelectProduct(this.productId);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SelectProduct && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;

  @override
  String toString() => 'SelectProduct(productId: $productId)';
}

/// Event để clear selection
class ClearProductSelection extends ProductEvent {}

/// Event để load chi tiết sản phẩm
class LoadProductDetail extends ProductEvent {
  final String productId;

  LoadProductDetail(this.productId);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoadProductDetail && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;

  @override
  String toString() => 'LoadProductDetail(productId: $productId)';
}

/// Event để clear cache
class ClearProductCache extends ProductEvent {}

/// Event để retry khi có lỗi
class RetryLoadProducts extends ProductEvent {}

/// Event để load sản phẩm active
class LoadActiveProducts extends ProductEvent {}

/// Event để load sản phẩm featured
class LoadFeaturedProducts extends ProductEvent {}

/// Event để load danh sách nhóm sản phẩm
class LoadProductGroups extends ProductEvent {}
