import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:kiloba_biz/shared/utils/color_utils.dart';

/// Model cho Product từ API response
class ProductModel extends Equatable {
  final String id;
  final String code;
  final String name;
  final String description;
  final String group;
  final String status;
  final ProductDisplayConfig? displayConfig;

  const ProductModel({
    required this.id,
    required this.code,
    required this.name,
    required this.description,
    required this.group,
    required this.status,
    this.displayConfig,
  });

  /// Factory constructor từ JSON
  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id']?.toString() ?? '',
      code: json['code']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      group: json['group']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      displayConfig: json['display_config'] != null
          ? ProductDisplayConfig.fromJson(json['display_config'] as Map<String, dynamic>)
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'name': name,
      'description': description,
      'group': group,
      'status': status,
      'display_config': displayConfig?.toJson(),
    };
  }

  /// Copy with method
  ProductModel copyWith({
    String? id,
    String? code,
    String? name,
    String? description,
    String? group,
    String? status,
    ProductDisplayConfig? displayConfig,
  }) {
    return ProductModel(
      id: id ?? this.id,
      code: code ?? this.code,
      name: name ?? this.name,
      description: description ?? this.description,
      group: group ?? this.group,
      status: status ?? this.status,
      displayConfig: displayConfig ?? this.displayConfig,
    );
  }

  /// Kiểm tra product có active không
  bool get isActive => status.toUpperCase() == 'ACTIVE';

  /// Kiểm tra product có inactive không
  bool get isInactive => status.toUpperCase() == 'INACTIVE';

  /// Get display name (ưu tiên name, fallback về code)
  String get displayName => name.isNotEmpty ? name : code;

  /// Get display description (fallback về empty string)
  String get displayDescription => description.isNotEmpty ? description : 'Không có mô tả';

  /// Check if product is featured
  bool get isFeatured {
    // Kiểm tra từ display config trước
    if (displayConfig?.isFeatured == true) {
      return true;
    }
    
    // Fallback: kiểm tra từ metadata hoặc logic khác
    return false;
  }

  /// Get product display color from displayConfig
  Color get displayColor {
    return displayConfig?.flutterColor ?? Colors.grey;
  }

  /// Check if product has custom color
  bool get hasCustomColor {
    return displayConfig?.hasColor == true;
  }

  /// Get display order for sorting
  int get displayOrder {
    return displayConfig?.displayOrder ?? 999;
  }

  @override
  List<Object?> get props => [
        id,
        code,
        name,
        description,
        group,
        status,
        displayConfig,
      ];

  @override
  String toString() {
    return 'ProductModel(id: $id, code: $code, name: $name, group: $group, status: $status)';
  }
}

/// Model cho display config của product
class ProductDisplayConfig extends Equatable {
  final String? icon;
  final String? color;
  final int? order;
  final bool? featured;
  final Map<String, dynamic>? metadata;

  const ProductDisplayConfig({
    this.icon,
    this.color,
    this.order,
    this.featured,
    this.metadata,
  });

  /// Factory constructor từ JSON
  factory ProductDisplayConfig.fromJson(Map<String, dynamic> json) {
    return ProductDisplayConfig(
      icon: json['icon']?.toString(),
      color: json['color']?.toString(),
      order: json['order'] is int ? json['order'] : int.tryParse(json['order']?.toString() ?? ''),
      featured: json['featured'] is bool ? json['featured'] : (json['featured']?.toString().toLowerCase() == 'true'),
      metadata: json['metadata'] is Map<String, dynamic> ? json['metadata'] : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'icon': icon,
      'color': color,
      'order': order,
      'featured': featured,
      'metadata': metadata,
    };
  }

  /// Copy with method
  ProductDisplayConfig copyWith({
    String? icon,
    String? color,
    int? order,
    bool? featured,
    Map<String, dynamic>? metadata,
  }) {
    return ProductDisplayConfig(
      icon: icon ?? this.icon,
      color: color ?? this.color,
      order: order ?? this.order,
      featured: featured ?? this.featured,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert color string to Flutter Color using ColorUtils
  Color get flutterColor {
    if (color == null || color!.isEmpty) {
      return Colors.grey; // Default color
    }
    return ColorUtils.hexToColor(color!);
  }

  /// Check if has valid color
  bool get hasColor => color != null && color!.isNotEmpty;

  /// Check if is featured
  bool get isFeatured => featured == true;

  /// Get display order (default to 999 if not set)
  int get displayOrder => order ?? 999;

  @override
  List<Object?> get props => [
        icon,
        color,
        order,
        featured,
        metadata,
      ];

  @override
  String toString() {
    return 'ProductDisplayConfig(icon: $icon, color: $color, order: $order, featured: $featured)';
  }
}

/// Model cho danh sách products response
class ProductsListData extends Equatable {
  final List<ProductModel> products;
  final int totalCount;

  const ProductsListData({
    required this.products,
    required this.totalCount,
  });

  /// Factory constructor từ JSON
  factory ProductsListData.fromJson(Map<String, dynamic> json) {
    final productsJson = json['products'] as List<dynamic>? ?? [];
    final products = productsJson
        .map((productJson) => ProductModel.fromJson(productJson as Map<String, dynamic>))
        .toList();

    return ProductsListData(
      products: products,
      totalCount: json['total_count'] as int? ?? products.length,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'products': products.map((product) => product.toJson()).toList(),
      'total_count': totalCount,
    };
  }

  /// Empty constructor
  static const ProductsListData empty = ProductsListData(
    products: [],
    totalCount: 0,
  );

  /// Kiểm tra có products không
  bool get hasProducts => products.isNotEmpty;

  /// Kiểm tra empty
  bool get isEmpty => products.isEmpty;

  /// Get active products only
  List<ProductModel> get activeProducts => products.where((product) => product.isActive).toList();

  /// Get products by group
  List<ProductModel> getProductsByGroup(String group) {
    return products.where((product) => product.group.toLowerCase() == group.toLowerCase()).toList();
  }

  /// Get featured products (nếu có display config)
  List<ProductModel> get featuredProducts {
    return products.where((product) => product.displayConfig?.featured == true).toList();
  }

  @override
  List<Object?> get props => [products, totalCount];

  @override
  String toString() {
    return 'ProductsListData(totalCount: $totalCount, productsCount: ${products.length})';
  }
}
