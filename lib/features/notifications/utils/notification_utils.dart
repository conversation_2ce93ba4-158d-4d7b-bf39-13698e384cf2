import 'package:flutter/material.dart';
import '../models/notification_type.dart';
import '../models/notification_priority.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/app_colors.dart';

/// Utilities cho notification parsing và formatting
class NotificationUtils {
  /// Parse notification type từ string
  static NotificationTypeApi parseNotificationType(String? type) {
    if (type == null) return NotificationTypeApi.general;

    switch (type.toUpperCase()) {
      case 'PROPOSAL':
        return NotificationTypeApi.proposal;
      case 'CUSTOMER':
        return NotificationTypeApi.customer;
      case 'DOCUMENT':
        return NotificationTypeApi.document;
      case 'APPROVAL':
        return NotificationTypeApi.approval;
      case 'REMINDER':
        return NotificationTypeApi.reminder;
      case 'MEETING':
        return NotificationTypeApi.meeting;
      case 'DEADLINE':
        return NotificationTypeApi.deadline;
      case 'BIRTHDAY':
        return NotificationTypeApi.birthday;
      case 'HOLIDAY':
        return NotificationTypeApi.holiday;
      case 'GENERAL':
        return NotificationTypeApi.general;
      case 'SYSTEM':
        return NotificationTypeApi.system;
      case 'URGENT':
        return NotificationTypeApi.urgent;
      case 'TRAINING':
        return NotificationTypeApi.training;
      case 'PERFORMANCE':
        return NotificationTypeApi.performance;
      case 'COMMISSION':
        return NotificationTypeApi.commission;
      default:
        return NotificationTypeApi.general;
    }
  }

  /// Parse notification priority từ string
  static NotificationPriorityApi parseNotificationPriority(String? priority) {
    if (priority == null) return NotificationPriorityApi.normal;

    switch (priority.toUpperCase()) {
      case 'LOW':
        return NotificationPriorityApi.low;
      case 'NORMAL':
        return NotificationPriorityApi.normal;
      case 'HIGH':
        return NotificationPriorityApi.high;
      case 'URGENT':
        return NotificationPriorityApi.urgent;
      default:
        return NotificationPriorityApi.normal;
    }
  }

  /// Parse DateTime từ string hoặc dynamic
  static DateTime? parseDateTime(dynamic dateTime) {
    if (dateTime == null) return null;

    if (dateTime is DateTime) return dateTime;

    if (dateTime is String) {
      try {
        return DateTime.parse(dateTime);
      } catch (e) {
        return null;
      }
    }

    return null;
  }

  /// Convert notification type enum to string for API
  static String notificationTypeToApiString(NotificationTypeApi type) {
    return type.name.toUpperCase();
  }

  /// Convert notification priority enum to string for API
  static String notificationPriorityToApiString(
    NotificationPriorityApi priority,
  ) {
    return priority.name.toUpperCase();
  }

  /// Format time ago string
  static String formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} ngày trước';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} giờ trước';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} phút trước';
    } else {
      return 'Vừa xong';
    }
  }

  /// Get Vietnamese display name for notification type
  static String getNotificationTypeDisplayName(NotificationTypeApi type) {
    switch (type) {
      case NotificationTypeApi.proposal:
        return 'Đề xuất';
      case NotificationTypeApi.customer:
        return 'Khách hàng';
      case NotificationTypeApi.document:
        return 'Tài liệu';
      case NotificationTypeApi.approval:
        return 'Phê duyệt';
      case NotificationTypeApi.reminder:
        return 'Nhắc nhở';
      case NotificationTypeApi.meeting:
        return 'Cuộc họp';
      case NotificationTypeApi.deadline:
        return 'Hạn chót';
      case NotificationTypeApi.birthday:
        return 'Sinh nhật';
      case NotificationTypeApi.holiday:
        return 'Lễ tết';
      case NotificationTypeApi.general:
        return 'Chung';
      case NotificationTypeApi.system:
        return 'Hệ thống';
      case NotificationTypeApi.urgent:
        return 'Khẩn cấp';
      case NotificationTypeApi.training:
        return 'Đào tạo';
      case NotificationTypeApi.performance:
        return 'Hiệu suất';
      case NotificationTypeApi.commission:
        return 'Hoa hồng';
    }
  }

  /// Get icon cho notification type
  static IconData getNotificationIcon(NotificationTypeApi notificationType) {
    switch (notificationType) {
      case NotificationTypeApi.proposal:
        return TablerIcons.presentation;
      case NotificationTypeApi.customer:
        return TablerIcons.user_circle;
      case NotificationTypeApi.document:
        return TablerIcons.file_description;
      case NotificationTypeApi.approval:
        return TablerIcons.shield_check;
      case NotificationTypeApi.reminder:
        return TablerIcons.alarm;
      case NotificationTypeApi.meeting:
        return TablerIcons.calendar_event;
      case NotificationTypeApi.deadline:
        return TablerIcons.hourglass;
      case NotificationTypeApi.birthday:
        return TablerIcons.gift;
      case NotificationTypeApi.holiday:
        return TablerIcons.beach;
      case NotificationTypeApi.general:
        return TablerIcons.bell;
      case NotificationTypeApi.system:
        return TablerIcons.server;
      case NotificationTypeApi.urgent:
        return TablerIcons.alert_circle;
      case NotificationTypeApi.training:
        return TablerIcons.certificate;
      case NotificationTypeApi.performance:
        return TablerIcons.chart_line;
      case NotificationTypeApi.commission:
        return TablerIcons.wallet;
    }
  }

  /// Get color cho notification type
  static Color getNotificationColor(NotificationTypeApi notificationType) {
    switch (notificationType) {
      case NotificationTypeApi.proposal:
        return AppColors.info;
      case NotificationTypeApi.customer:
        return AppColors.success;
      case NotificationTypeApi.document:
        return AppColors.kienlongOrange;
      case NotificationTypeApi.approval:
        return AppColors.success;
      case NotificationTypeApi.reminder:
        return AppColors.warning;
      case NotificationTypeApi.meeting:
        return AppColors.info;
      case NotificationTypeApi.deadline:
        return AppColors.error;
      case NotificationTypeApi.birthday:
        return Colors.pink;
      case NotificationTypeApi.holiday:
        return Colors.purple;
      case NotificationTypeApi.general:
        return AppColors.kienlongSkyBlue;
      case NotificationTypeApi.system:
        return Colors.grey;
      case NotificationTypeApi.urgent:
        return AppColors.error;
      case NotificationTypeApi.training:
        return Colors.green;
      case NotificationTypeApi.performance:
        return AppColors.kienlongOrange;
      case NotificationTypeApi.commission:
        return Colors.amber;
    }
  }

  /// Get Vietnamese display name for notification priority
  static String getNotificationPriorityDisplayName(
    NotificationPriorityApi priority,
  ) {
    switch (priority) {
      case NotificationPriorityApi.low:
        return 'Thấp';
      case NotificationPriorityApi.normal:
        return 'Bình thường';
      case NotificationPriorityApi.high:
        return 'Cao';
      case NotificationPriorityApi.urgent:
        return 'Khẩn cấp';
    }
  }

  /// Check if notification type is important
  static bool isImportantNotificationType(NotificationTypeApi type) {
    switch (type) {
      case NotificationTypeApi.urgent:
      case NotificationTypeApi.deadline:
      case NotificationTypeApi.approval:
        return true;
      default:
        return false;
    }
  }

  /// Check if notification priority is high
  static bool isHighPriority(NotificationPriorityApi priority) {
    return priority == NotificationPriorityApi.high ||
        priority == NotificationPriorityApi.urgent;
  }

  /// Get notification type category for grouping
  static String getNotificationTypeCategory(NotificationTypeApi type) {
    // Sử dụng notification type như category để tương thích với API mới
    return type.name.toUpperCase();
  }

  /// Parse notification type category từ string (for backward compatibility)
  static NotificationTypeApi parseNotificationTypeCategory(String? category) {
    // Sử dụng lại logic parseNotificationType
    return parseNotificationType(category);
  }
}
