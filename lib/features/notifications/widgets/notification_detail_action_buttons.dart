import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';

import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';

/// Action buttons widget cho notification detail bottom sheet
class NotificationDetailActionButtons extends StatelessWidget {
  final NotificationDetail notification;

  const NotificationDetailActionButtons({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(
              TablerIcons.devices,
              size: 20,
              color: AppColors.kienlongOrange,
            ),
            const SizedBox(width: AppDimensions.spacingS),
            Text(
              'Hành động',
              style: AppTypography.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppDimensions.spacingM),
        
        // Action buttons
        Row(
          children: [
            // Primary action button
            if (notification.isActionable && notification.actionUrl != null)
              Expanded(
                child: _buildPrimaryActionButton(context),
              ),
            
            if (notification.isActionable && notification.actionUrl != null)
              const SizedBox(width: AppDimensions.spacingM),
            
            // Secondary action buttons
            Expanded(
              child: _buildSecondaryActionButtons(context),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPrimaryActionButton(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () => _handlePrimaryAction(context),
      icon: Icon(_getActionIcon()),
      label: Text(_getActionText()),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.kienlongOrange,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.spacingL,
          vertical: AppDimensions.spacingM,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        elevation: 2,
      ),
    );
  }

  Widget _buildSecondaryActionButtons(BuildContext context) {
    return Column(
      children: [
        // Share button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _handleShare(context),
            icon: const Icon(TablerIcons.share, size: 16),
            label: const Text('Chia sẻ'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.info,
              side: BorderSide(color: AppColors.info),
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.spacingM,
                vertical: AppDimensions.spacingS,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getActionIcon() {
    if (notification.actionUrl == null) return TablerIcons.link;
    
    final uri = Uri.tryParse(notification.actionUrl!);
    if (uri == null) return TablerIcons.link;
    
    final pathSegments = uri.pathSegments;
    if (pathSegments.isEmpty) return TablerIcons.link;
    
    final module = pathSegments[0];
    final action = pathSegments.length > 1 ? pathSegments[1] : '';
    
    switch ('/$module/$action') {
      case '/attendance/check-in':
        return TablerIcons.login;
      case '/attendance/check-out':
        return TablerIcons.logout;
      case '/attendance/supplement':
        return TablerIcons.edit;
      case '/attendance/details':
        return TablerIcons.eye;
      case '/training/register':
        return TablerIcons.book;
      case '/training/continue':
        return TablerIcons.player_play;
      case '/training/certificate':
        return TablerIcons.certificate;
      case '/training/details':
        return TablerIcons.info_circle;
      case '/leave/apply':
        return TablerIcons.file_text;
      case '/leave/details':
        return TablerIcons.eye;
      case '/leave/respond':
        return TablerIcons.message;
      case '/overtime/request':
        return TablerIcons.clock;
      case '/overtime/respond':
        return TablerIcons.message;
      default:
        return TablerIcons.external_link;
    }
  }

  String _getActionText() {
    if (notification.actionText != null && notification.actionText!.isNotEmpty) {
      return notification.actionText!;
    }
    
    if (notification.actionUrl == null) return 'Xem chi tiết';
    
    final uri = Uri.tryParse(notification.actionUrl!);
    if (uri == null) return 'Xem chi tiết';
    
    final pathSegments = uri.pathSegments;
    if (pathSegments.isEmpty) return 'Xem chi tiết';
    
    final module = pathSegments[0];
    final action = pathSegments.length > 1 ? pathSegments[1] : '';
    
    switch ('/$module/$action') {
      case '/attendance/check-in':
        return 'Chấm công vào';
      case '/attendance/check-out':
        return 'Chấm công ra';
      case '/attendance/supplement':
        return 'Bổ sung chấm công';
      case '/attendance/details':
        return 'Xem chấm công';
      case '/training/register':
        return 'Đăng ký khóa học';
      case '/training/continue':
        return 'Tiếp tục học';
      case '/training/certificate':
        return 'Xem chứng chỉ';
      case '/training/details':
        return 'Chi tiết khóa học';
      case '/leave/apply':
        return 'Nộp đơn nghỉ';
      case '/leave/details':
        return 'Xem chi tiết';
      case '/leave/respond':
        return 'Phản hồi';
      case '/overtime/request':
        return 'Gửi yêu cầu';
      case '/overtime/respond':
        return 'Phản hồi';
      default:
        return 'Thực hiện hành động';
    }
  }

  void _handlePrimaryAction(BuildContext context) {
    if (notification.actionUrl == null) {
      _showSnackBar(context, 'Không có hành động nào khả dụng');
      return;
    }
    
    // TODO: Implement action execution
    debugPrint('Executing primary action: ${notification.actionUrl}');
    _showSnackBar(context, 'Đang thực hiện hành động...');
    
    // Close bottom sheet after action
    Navigator.of(context).pop();
  }



  void _handleShare(BuildContext context) {
    // TODO: Implement share functionality
    debugPrint('Sharing notification: ${notification.notificationId}');
    _showSnackBar(context, 'Đang chia sẻ thông báo...');
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.kienlongOrange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),
    );
  }
} 