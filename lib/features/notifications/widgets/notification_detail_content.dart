import 'package:flutter/material.dart';

import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';
import '../utils/notification_utils.dart';

/// Content widget cho notification detail bottom sheet
class NotificationDetailContent extends StatelessWidget {
  final NotificationDetail notification;

  const NotificationDetailContent({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Message content
        _buildMessageContent(context),
        
        // Image (if any)
        if (notification.imageUrl != null) ...[
          const SizedBox(height: AppDimensions.spacingL),
          _buildImageContent(context),
        ],
      ],
    );
  }

  Widget _buildMessageContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Content header
          Row(
            children: [
              Icon(
                _getNotificationIcon(),
                size: 20,
                color: _getNotificationColor(),
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Nội dung thông báo',
                style: AppTypography.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.spacingM),
          
          // Message text
          Text(
            notification.message,
            style: AppTypography.textTheme.bodyLarge?.copyWith(
              height: 1.6,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          
          // Short message (if different from main message)
          if (notification.shortMessage != null && 
              notification.shortMessage != notification.message) ...[
            const SizedBox(height: AppDimensions.spacingM),
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: AppColors.kienlongOrange,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      notification.shortMessage!,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongOrange,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImageContent(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
              child: ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Image.network(
            notification.imageUrl!,
            fit: BoxFit.cover,
            width: double.infinity,
            height: 200,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                height: 200,
                color: Theme.of(context).colorScheme.surface,
                child: Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                        : null,
                    color: AppColors.kienlongOrange,
                  ),
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) => Container(
              height: 200,
              color: Theme.of(context).colorScheme.surface,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: AppDimensions.spacingS),
                    Text(
                      'Không thể tải hình ảnh',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
    );
  }

  IconData _getNotificationIcon() {
    return NotificationUtils.getNotificationIcon(notification.notificationType);
  }

  Color _getNotificationColor() {
    return NotificationUtils.getNotificationColor(notification.notificationType);
  }
} 