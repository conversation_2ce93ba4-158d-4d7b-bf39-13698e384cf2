import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../blocs/notification_bloc.dart';
import '../blocs/notification_state.dart';
import '../../../shared/services/notification_dialog_service.dart';

/// Widget listener để lắng nghe NotificationBloc state và hiển thị dialog
/// Sử dụng trong MaterialApp hoặc widget tree để tự động hiển thị dialog
class NotificationDialogListener extends StatelessWidget {
  final Widget child;

  const NotificationDialogListener({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    debugPrint('🔧 NotificationDialogListener build called');
    return BlocListener<NotificationBloc, NotificationState>(
      listener: (context, state) {
        debugPrint('👂 NotificationDialogListener received state: ${state.runtimeType}');
        
        if (state is NotificationDetailDialogState) {
          debugPrint('🎯 Showing notification detail dialog for: ${state.notification.title}');
          debugPrint('🎯 NotificationDialogService.isSetup: ${NotificationDialogService().isSetup}');
          // Hiển thị dialog khi có state NotificationDetailDialogState
          NotificationDialogService().showNotificationDetailDialog(state.notification);
        } else {
          debugPrint('ℹ️ State is not NotificationDetailDialogState: ${state.runtimeType}');
        }
      },
      child: child,
    );
  }
} 