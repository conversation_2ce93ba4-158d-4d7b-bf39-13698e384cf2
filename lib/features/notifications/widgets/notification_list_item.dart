import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:kiloba_biz/features/notifications/models/notification_priority.dart';
import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';
import '../utils/notification_utils.dart';

/// Single notification item component
class NotificationListItem extends StatelessWidget {
  final NotificationDetail notification;
  final VoidCallback? onTap;

  const NotificationListItem({
    super.key,
    required this.notification,
    this.onTap, required Function() onMarkAsRead, required Function() onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.notificationId),
      direction: DismissDirection.endToStart,
      background: _buildDismissBackground(),
      onDismissed: (direction) {
        // Auto mark as read when dismissed if not already read
        if (!notification.isRead) {
          // TODO: Implement mark as read logic if needed
          debugPrint('Notification dismissed: ${notification.notificationId}');
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
        decoration: BoxDecoration(
          color: notification.isRead
              ? Theme.of(context).colorScheme.surface
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: notification.isRead
                ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)
                : AppColors.kienlongOrange.withValues(alpha: 0.3),
            width: notification.isRead ? 1 : 2,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildNotificationIcon(),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(child: _buildNotificationContent(context)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon() {
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.all(AppDimensions.paddingS),
          decoration: BoxDecoration(
            color: _getNotificationColor().withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          ),
          child: Icon(
            _getNotificationIcon(),
            color: _getNotificationColor(),
            size: AppDimensions.iconM,
          ),
        ),
        if (notification.isImportant)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: AppColors.error,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.white, width: 1),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNotificationContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                notification.title,
                style: AppTypography.textTheme.titleSmall?.copyWith(
                  fontWeight: notification.isRead
                      ? FontWeight.w500
                      : FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (!notification.isRead)
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: AppColors.kienlongOrange,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          notification.shortMessage ?? notification.message,
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Text(
              notification.timeAgo,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
              ),
            ),
            if (notification.isActionable &&
                notification.actionText != null) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  notification.actionText ?? '',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: AppColors.kienlongOrange,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
        if (_shouldShowPriorityBadge()) ...[
          const SizedBox(height: 4),
          _buildPriorityBadge(),
        ],
      ],
    );
  }


  Widget _buildDismissBackground() {
    return Container(
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.only(right: 20),
      decoration: BoxDecoration(
        color: notification.isRead ? Colors.red : AppColors.success,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Icon(
        notification.isRead ? TablerIcons.trash : TablerIcons.check,
        color: Colors.white,
      ),
    );
  }

  Widget _buildPriorityBadge() {
    if (notification.priority == NotificationPriorityApi.normal) {
      return const SizedBox.shrink();
    }

    Color badgeColor;
    String text;
    IconData icon;

    switch (notification.priority) {
      case NotificationPriorityApi.low:
        badgeColor = Colors.grey;
        text = 'Thấp';
        icon = TablerIcons.arrow_down;
        break;
      case NotificationPriorityApi.high:
        badgeColor = AppColors.warning;
        text = 'Cao';
        icon = TablerIcons.arrow_up;
        break;
      case NotificationPriorityApi.urgent:
        badgeColor = AppColors.error;
        text = 'Khẩn cấp';
        icon = TablerIcons.alert_triangle;
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: badgeColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 10, color: badgeColor),
          const SizedBox(width: 2),
          Text(
            text,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: badgeColor,
              fontWeight: FontWeight.w500,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  bool _shouldShowPriorityBadge() {
    return notification.priority != NotificationPriorityApi.normal;
  }

  Color _getNotificationColor() {
    return NotificationUtils.getNotificationColor(
      notification.notificationType,
    );
  }

  IconData _getNotificationIcon() {
    return NotificationUtils.getNotificationIcon(notification.notificationType);
  }
}

/// Compact notification list item for dense lists
class CompactNotificationListItem extends StatelessWidget {
  final NotificationDetail notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;

  const CompactNotificationListItem({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingS),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: notification.isRead
              ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)
              : AppColors.kienlongOrange.withValues(alpha: 0.3),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingS),
            child: Row(
              children: [
                Icon(
                  _getNotificationIcon(),
                  size: 16,
                  color: _getNotificationColor(),
                ),
                const SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        notification.title,
                        style: AppTypography.textTheme.bodyMedium?.copyWith(
                          fontWeight: notification.isRead
                              ? FontWeight.normal
                              : FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        notification.timeAgo,
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurface.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ),
                ),
                if (!notification.isRead) ...[
                  const SizedBox(width: AppDimensions.spacingS),
                  GestureDetector(
                    onTap: onMarkAsRead,
                    child: Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: AppColors.kienlongOrange,
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getNotificationColor() {
    return NotificationUtils.getNotificationColor(
      notification.notificationType,
    );
  }

  IconData _getNotificationIcon() {
    return NotificationUtils.getNotificationIcon(notification.notificationType);
  }
}
