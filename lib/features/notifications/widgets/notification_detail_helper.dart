import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../models/notification_detail.dart';
import '../blocs/notification_bloc.dart';
import 'notification_detail_bottom_sheet.dart';

/// Helper class để hiển thị notification detail bottom sheet
class NotificationDetailHelper {
  /// Hiển thị notification detail bottom sheet
  static void showNotificationDetail(
    BuildContext context,
    NotificationDetail notification,
  ) {
    final notificationBloc = context.read<NotificationBloc>();
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider.value(
        value: notificationBloc,
        child: NotificationDetailBottomSheet(
          notification: notification,
        ),
      ),
    );
  }

  /// Hiển thị notification detail bottom sheet với animation
  static void showNotificationDetailWithAnimation(
    BuildContext context,
    NotificationDetail notification, {
    Duration duration = const Duration(milliseconds: 300),
  }) {
    final notificationBloc = context.read<NotificationBloc>();
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider.value(
        value: notificationBloc,
        child: NotificationDetailBottomSheet(
          notification: notification,
        ),
      ),
    );
  }
} 