import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:shimmer/shimmer.dart';
import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';
import 'notification_list_item.dart';

/// Widget để hiển thị danh sách thông báo gần đây
class RecentNotificationsList extends StatelessWidget {
  final List<NotificationDetail> notifications;
  final bool isLoading;
  final Function(NotificationDetail)? onNotificationTap;
  final Function(String)? onMarkAsRead;
  final Function(String)? onDelete;
  final VoidCallback? onViewAll;
  final bool showViewAll;
  final String? emptyMessage;

  const RecentNotificationsList({
    super.key,
    required this.notifications,
    this.isLoading = false,
    this.onNotificationTap,
    this.onMarkAsRead,
    this.onDelete,
    this.onViewAll,
    this.showViewAll = true,
    this.emptyMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: AppDimensions.spacingM),
        if (isLoading)
          _buildLoadingList()
        else if (notifications.isEmpty)
          _buildEmptyState(context)
        else
          _buildNotificationsList(),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Thông báo gần đây',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        if (showViewAll && notifications.isNotEmpty)
          TextButton(
            onPressed: onViewAll,
            child: Text(
              'Xem tất cả',
              style: TextStyle(color: AppColors.kienlongOrange),
            ),
          ),
      ],
    );
  }

  Widget _buildNotificationsList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return NotificationListItem(
          notification: notification,
          onTap: () => onNotificationTap?.call(notification),
          onMarkAsRead: () => onMarkAsRead?.call(notification.notificationId),
          onDelete: () => onDelete?.call(notification.notificationId),
        );
      },
    );
  }

  Widget _buildLoadingList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 3,
      itemBuilder: (context, index) {
        return _buildLoadingItem(context);
      },
    );
  }

  Widget _buildLoadingItem(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 16,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 14,
                    width: 200,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 12,
                    width: 80,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingXL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(
            TablerIcons.bell_off,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            emptyMessage ?? 'Không có thông báo nào',
            style: AppTypography.textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'Thông báo mới sẽ xuất hiện ở đây',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Horizontal notification list for dashboard
class HorizontalNotificationsList extends StatelessWidget {
  final List<NotificationDetail> notifications;
  final bool isLoading;
  final Function(NotificationDetail)? onNotificationTap;
  final Function(String)? onMarkAsRead;
  final VoidCallback? onViewAll;

  const HorizontalNotificationsList({
    super.key,
    required this.notifications,
    this.isLoading = false,
    this.onNotificationTap,
    this.onMarkAsRead,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Thông báo gần đây',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (notifications.isNotEmpty)
              TextButton(
                onPressed: onViewAll,
                child: Text(
                  'Xem tất cả',
                  style: TextStyle(color: AppColors.kienlongOrange),
                ),
              ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        SizedBox(
          height: 100,
          child: isLoading ? _buildLoadingList() : _buildHorizontalList(context),
        ),
      ],
    );
  }

  Widget _buildHorizontalList(BuildContext context) {
    if (notifications.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: notifications.length,
      separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.spacingM),
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return SizedBox(
          width: 280,
          child: CompactNotificationListItem(
            notification: notification,
            onTap: () => onNotificationTap?.call(notification),
            onMarkAsRead: () => onMarkAsRead?.call(notification.notificationId),
          ),
        );
      },
    );
  }

  Widget _buildLoadingList() {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: 3,
      separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.spacingM),
      itemBuilder: (context, index) {
        return SizedBox(
          width: 280,
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            TablerIcons.bell_off,
            size: 32,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 8),
          Text(
            'Không có thông báo',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }
}

/// Infinite scroll notification list
class InfiniteNotificationsList extends StatefulWidget {
  final List<NotificationDetail> notifications;
  final bool isLoading;
  final bool hasMore;
  final Function(NotificationDetail)? onNotificationTap;
  final Function(String)? onMarkAsRead;
  final Function(String)? onDelete;
  final VoidCallback? onLoadMore;

  const InfiniteNotificationsList({
    super.key,
    required this.notifications,
    this.isLoading = false,
    this.hasMore = false,
    this.onNotificationTap,
    this.onMarkAsRead,
    this.onDelete,
    this.onLoadMore,
  });

  @override
  State<InfiniteNotificationsList> createState() => _InfiniteNotificationsListState();
}

class _InfiniteNotificationsListState extends State<InfiniteNotificationsList> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (widget.hasMore && !widget.isLoading) {
        widget.onLoadMore?.call();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      itemCount: widget.notifications.length + (widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= widget.notifications.length) {
          return _buildLoadingIndicator();
        }

        final notification = widget.notifications[index];
        return NotificationListItem(
          notification: notification,
          onTap: () => widget.onNotificationTap?.call(notification),
          onMarkAsRead: () => widget.onMarkAsRead?.call(notification.notificationId),
          onDelete: () => widget.onDelete?.call(notification.notificationId),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }
}