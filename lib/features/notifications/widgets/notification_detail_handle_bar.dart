import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

/// Handle bar widget cho notification detail bottom sheet
class NotificationDetailHandleBar extends StatelessWidget {
  const NotificationDetailHandleBar({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(top: AppDimensions.spacingM),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: theme.dividerColor,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }
} 