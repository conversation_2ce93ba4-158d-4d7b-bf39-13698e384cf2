import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';
import '../blocs/notification_bloc.dart';
import '../blocs/notification_event.dart';
import 'notification_detail_full_header.dart';
import 'notification_detail_content.dart';
import 'notification_detail_metadata_card.dart';
import 'notification_detail_action_buttons.dart';

/// Dialog chuyên nghiệp để xem chi tiết notification từ push notification
/// Sử dụng khi người dùng nhấn vào push notification có notification_id
class NotificationDetailDialog extends StatelessWidget {
  final NotificationDetail notification;

  const NotificationDetailDialog({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.95,
        ),
        decoration: BoxDecoration(
          color: theme.scaffoldBackgroundColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: _NotificationDetailDialogContent(notification: notification),
      ),
    );
  }
}

/// Content widget cho notification detail dialog
class _NotificationDetailDialogContent extends StatefulWidget {
  final NotificationDetail notification;

  const _NotificationDetailDialogContent({
    required this.notification,
  });

  @override
  State<_NotificationDetailDialogContent> createState() => _NotificationDetailDialogContentState();
}

class _NotificationDetailDialogContentState extends State<_NotificationDetailDialogContent> {
  @override
  void initState() {
    super.initState();
    // Tự động mark as read khi mở dialog nếu chưa đọc
    if (!widget.notification.isRead) {
      // Delay để tránh race condition với việc mở dialog
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          try {
            context.read<NotificationBloc>().add(
              MarkNotificationAsRead(widget.notification.notificationId),
            );
          } catch (e) {
            debugPrint('Error marking notification as read: $e');
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Header
        NotificationDetailFullHeader(notification: widget.notification),
        
        // Content
        Flexible(
          child: _buildContent(),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main content
          NotificationDetailContent(notification: widget.notification),
          
          const SizedBox(height: AppDimensions.spacingL),
          
          // Metadata card
          NotificationDetailMetadataCard(notification: widget.notification),
          
          const SizedBox(height: AppDimensions.spacingL),
          
          // Action buttons
          if (widget.notification.isActionable)
            NotificationDetailActionButtons(notification: widget.notification),
        ],
      ),
    );
  }
}

/// Helper function để hiển thị notification detail dialog
class NotificationDetailDialogHelper {
  /// Hiển thị dialog chi tiết thông báo
  static Future<void> showNotificationDetail(
    BuildContext context,
    NotificationDetail notification,
  ) async {
    debugPrint('🎭 showNotificationDetail called for: ${notification.title}');
    debugPrint('🎭 Context mounted: ${context.mounted}');
    
    try {
      await showDialog<void>(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          debugPrint('🎭 Building NotificationDetailDialog...');
          return NotificationDetailDialog(notification: notification);
        },
      );
      debugPrint('🎭 Dialog shown successfully');
    } catch (e) {
      debugPrint('❌ Error showing dialog: $e');
    }
  }
} 