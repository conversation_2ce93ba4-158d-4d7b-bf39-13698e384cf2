import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';

import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';
import '../models/notification_priority.dart';
import '../utils/notification_utils.dart';

/// Metadata card widget cho notification detail bottom sheet
class NotificationDetailMetadataCard extends StatelessWidget {
  final NotificationDetail notification;

  const NotificationDetailMetadataCard({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode 
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                TablerIcons.info_circle,
                size: 20,
                color: AppColors.info,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Thông tin chi tiết',
                style: AppTypography.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.spacingM),
          
          // Metadata items
          _buildMetadataItem(
            context,
            icon: TablerIcons.tag,
            label: 'Loại thông báo',
            value: NotificationUtils.getNotificationTypeDisplayName(notification.notificationType),
          ),
          
          _buildMetadataItem(
            context,
            icon: TablerIcons.flag,
            label: 'Độ ưu tiên',
            value: _getPriorityDisplayName(),
            valueColor: _getPriorityColor(),
          ),
          
          if (notification.senderName != null)
            _buildMetadataItem(
              context,
              icon: TablerIcons.user,
              label: 'Người gửi',
              value: notification.senderName!,
            ),
          
          _buildMetadataItem(
            context,
            icon: TablerIcons.clock,
            label: 'Thời gian tạo',
            value: _formatDateTime(notification.createdAt),
          ),
          
          if (notification.sentAt != null)
            _buildMetadataItem(
              context,
              icon: TablerIcons.send,
              label: 'Thời gian gửi',
              value: _formatDateTime(notification.sentAt!),
            ),
          
          if (notification.readAt != null)
            _buildMetadataItem(
              context,
              icon: TablerIcons.eye,
              label: 'Đã đọc lúc',
              value: _formatDateTime(notification.readAt!),
            ),
          
          if (notification.clickedAt != null)
            _buildMetadataItem(
              context,
              icon: TablerIcons.mouse,
              label: 'Đã click lúc',
              value: _formatDateTime(notification.clickedAt!),
            ),
          
          // Status indicators
          const SizedBox(height: AppDimensions.spacingM),
          _buildStatusIndicators(context),
        ],
      ),
    );
  }

  Widget _buildMetadataItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              icon,
              size: 14,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(width: AppDimensions.spacingS),
          
          // Content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: valueColor ?? theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicators(BuildContext context) {
    Theme.of(context);
    
    return Row(
      children: [
        // Read status
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.spacingS,
            vertical: AppDimensions.spacingXS,
          ),
          decoration: BoxDecoration(
            color: notification.isRead 
                ? AppColors.success.withValues(alpha: 0.1)
                : AppColors.warning.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            border: Border.all(
              color: notification.isRead 
                  ? AppColors.success.withValues(alpha: 0.3)
                  : AppColors.warning.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                notification.isRead ? TablerIcons.eye : TablerIcons.eye_off,
                size: 12,
                color: notification.isRead ? AppColors.success : AppColors.warning,
              ),
              const SizedBox(width: 4),
              Text(
                notification.isRead ? 'Đã đọc' : 'Chưa đọc',
                style: AppTypography.textTheme.labelSmall?.copyWith(
                  color: notification.isRead ? AppColors.success : AppColors.warning,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(width: AppDimensions.spacingS),
        
        // Click status
        if (notification.isActionable)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.spacingS,
              vertical: AppDimensions.spacingXS,
            ),
            decoration: BoxDecoration(
              color: notification.isClicked 
                  ? AppColors.info.withValues(alpha: 0.1)
                  : AppColors.neutral500.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: notification.isClicked 
                    ? AppColors.info.withValues(alpha: 0.3)
                    : AppColors.neutral500.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  notification.isClicked ? TablerIcons.mouse : TablerIcons.mouse_off,
                  size: 12,
                  color: notification.isClicked ? AppColors.info : AppColors.neutral500,
                ),
                const SizedBox(width: 4),
                Text(
                  notification.isClicked ? 'Đã click' : 'Chưa click',
                  style: AppTypography.textTheme.labelSmall?.copyWith(
                    color: notification.isClicked ? AppColors.info : AppColors.neutral500,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        
        const SizedBox(width: AppDimensions.spacingS),
        
        // Important indicator
        if (notification.isImportant)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.spacingS,
              vertical: AppDimensions.spacingXS,
            ),
            decoration: BoxDecoration(
              color: AppColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: AppColors.error.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  TablerIcons.star,
                  size: 12,
                  color: AppColors.error,
                ),
                const SizedBox(width: 4),
                Text(
                  'Quan trọng',
                  style: AppTypography.textTheme.labelSmall?.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  String _getPriorityDisplayName() {
    return NotificationUtils.getNotificationPriorityDisplayName(notification.priority);
  }

  Color _getPriorityColor() {
    switch (notification.priority) {
      case NotificationPriorityApi.high:
        return AppColors.error;
      case NotificationPriorityApi.normal:
        return AppColors.warning;
      case NotificationPriorityApi.low:
        return AppColors.info;
      case NotificationPriorityApi.urgent:
        return AppColors.error;
      }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
} 