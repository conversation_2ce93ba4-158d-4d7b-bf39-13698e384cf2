import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../blocs/notification_badge_cubit.dart';

/// Widget để hiển thị notification tab với badge count
/// Sử dụng trong bottom navigation bar
class BadgeNotificationTab extends StatelessWidget {
  final bool isSelected;
  final VoidCallback? onTap;
  final Color? selectedColor;
  final Color? unselectedColor;

  const BadgeNotificationTab({
    super.key,
    this.isSelected = false,
    this.onTap,
    this.selectedColor,
    this.unselectedColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveSelectedColor = selectedColor ?? AppColors.kienlongOrange;
    final effectiveUnselectedColor = unselectedColor ?? theme.colorScheme.onSurface.withValues(alpha: 0.6);

    return GestureDetector(
      onTap: onTap,
      child: BlocBuilder<NotificationBadgeCubit, int>(
        builder: (context, badgeCount) {
          return Badge(
            isLabelVisible: badgeCount > 0,
            label: Text(
              badgeCount > 99 ? '99+' : '$badgeCount',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: AppColors.error,
            child: Icon(
              TablerIcons.bell,
              color: isSelected ? effectiveSelectedColor : effectiveUnselectedColor,
              size: 24,
            ),
          );
        },
      ),
    );
  }
}

/// Simple tab item với badge cho bottom navigation
class NotificationTabItem extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback? onTap;

  const NotificationTabItem({
    super.key,
    required this.label,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          BadgeNotificationTab(
            isSelected: isSelected,
            onTap: onTap,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isSelected 
                  ? AppColors.kienlongOrange 
                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}

/// Badge widget có thể tái sử dụng
class NotificationBadgeWidget extends StatelessWidget {
  final Widget child;
  final bool showBadge;
  final String? badgeText;
  final Color? badgeColor;
  final double? badgeSize;

  const NotificationBadgeWidget({
    super.key,
    required this.child,
    this.showBadge = false,
    this.badgeText,
    this.badgeColor,
    this.badgeSize,
  });

  @override
  Widget build(BuildContext context) {
    if (!showBadge) return child;

    return Badge(
      isLabelVisible: showBadge,
      label: badgeText != null 
          ? Text(
              badgeText!,
              style: TextStyle(
                color: Colors.white,
                fontSize: badgeSize ?? 10,
                fontWeight: FontWeight.bold,
              ),
            )
          : null,
      backgroundColor: badgeColor ?? AppColors.error,
      child: child,
    );
  }
}

/// Provider widget để wrap app với NotificationBadgeCubit
class NotificationBadgeProvider extends StatelessWidget {
  final Widget child;

  const NotificationBadgeProvider({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NotificationBadgeCubit()..loadBadgeCount(),
      child: child,
    );
  }
}