import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:shimmer/shimmer.dart';
import '../../../core/theme/index.dart';
import '../models/notification_summary.dart';

/// Widget để hiển thị tóm tắt thống kê thông báo
class NotificationSummaryCard extends StatelessWidget {
  final NotificationSummary? summary;
  final bool isLoading;
  final VoidCallback? onTap;

  const NotificationSummaryCard({
    super.key,
    this.summary,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isDarkMode
                ? [
                    AppColors.backgroundDarkSecondary,
                    AppColors.backgroundDarkTertiary,
                  ]
                : [
                    AppColors.kienlongSkyBlue,
                    AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
                  ],
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          boxShadow: [
            BoxShadow(
              color: isDarkMode 
                  ? AppColors.shadowDark
                  : AppColors.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: isLoading ? _buildLoadingContent(context) : _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Thông báo hôm nay',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  color: isDarkMode ? AppColors.textOnPrimary : Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              _buildSummaryText(context),
              if (summary?.hasUnreadNotifications == true) ...[
                const SizedBox(height: 4),
                _buildUnreadIndicator(),
              ],
            ],
          ),
        ),
        _buildIconSection(context),
      ],
    );
  }

  Widget _buildSummaryText(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    if (summary == null) {
      return Text(
        'Không có dữ liệu',
        style: AppTypography.textTheme.bodyMedium?.copyWith(
          color: isDarkMode 
              ? AppColors.textOnPrimary.withValues(alpha: 0.9)
              : Colors.white.withValues(alpha: 0.9),
        ),
      );
    }

    final parts = <String>[];
    
    if (summary!.todayCount > 0) {
      parts.add('${summary!.todayCount} hôm nay');
    }
    
    if (summary!.unreadCount > 0) {
      parts.add('${summary!.unreadCount} chưa đọc');
    }
    
    if (parts.isEmpty) {
      parts.add('Tất cả đã đọc');
    }

    return Text(
      parts.join(' • '),
      style: AppTypography.textTheme.bodyMedium?.copyWith(
        color: isDarkMode 
            ? AppColors.textOnPrimary.withValues(alpha: 0.9)
            : Colors.white.withValues(alpha: 0.9),
      ),
    );
  }

  Widget _buildUnreadIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 8,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: AppColors.warning,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        'Có thông báo mới',
        style: AppTypography.textTheme.bodySmall?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildIconSection(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: isDarkMode 
            ? AppColors.textOnPrimary.withValues(alpha: 0.2)
            : Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: summary?.hasUnreadNotifications == true
          ? _buildAnimatedBell(context)
          : Icon(
              TablerIcons.bell,
              color: isDarkMode ? AppColors.textOnPrimary : Colors.white,
              size: 32,
            ),
    );
  }

  Widget _buildAnimatedBell(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween<double>(begin: 0, end: 1),
      builder: (context, value, child) {
        return Transform.rotate(
          angle: value * 0.1 * (value < 0.5 ? value * 2 : (1 - value) * 2),
          child: Icon(
            TablerIcons.bell_ringing,
            color: isDarkMode ? AppColors.textOnPrimary : Colors.white,
            size: 32,
          ),
        );
      },
    );
  }

  Widget _buildLoadingContent(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                baseColor: isDarkMode 
                    ? AppColors.backgroundDarkSecondary
                    : Colors.white.withValues(alpha: 0.3),
                highlightColor: isDarkMode 
                    ? AppColors.backgroundDarkTertiary
                    : Colors.white.withValues(alpha: 0.5),
                child: Container(
                  height: 20,
                  width: 140,
                  decoration: BoxDecoration(
                    color: isDarkMode 
                        ? AppColors.backgroundDarkSecondary
                        : Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Shimmer.fromColors(
                baseColor: isDarkMode 
                    ? AppColors.backgroundDarkSecondary
                    : Colors.white.withValues(alpha: 0.3),
                highlightColor: isDarkMode 
                    ? AppColors.backgroundDarkTertiary
                    : Colors.white.withValues(alpha: 0.5),
                child: Container(
                  height: 16,
                  width: 180,
                  decoration: BoxDecoration(
                    color: isDarkMode 
                        ? AppColors.backgroundDarkSecondary
                        : Colors.white,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: const Icon(
            TablerIcons.bell,
            color: Colors.white,
            size: 32,
          ),
        ),
      ],
    );
  }
}

/// Expanded summary card với additional metrics
class DetailedNotificationSummaryCard extends StatelessWidget {
  final NotificationSummary? summary;
  final bool isLoading;
  final VoidCallback? onTap;

  const DetailedNotificationSummaryCard({
    super.key,
    this.summary,
    this.isLoading = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isDarkMode
                ? [
                    AppColors.backgroundDarkSecondary,
                    AppColors.backgroundDarkTertiary,
                  ]
                : [
                    AppColors.kienlongSkyBlue,
                    AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
                  ],
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          boxShadow: [
            BoxShadow(
              color: isDarkMode 
                  ? AppColors.shadowDark
                  : AppColors.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: isLoading ? _buildLoadingContent(context) : _buildDetailedContent(context),
      ),
    );
  }

  Widget _buildDetailedContent(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Tổng quan thông báo',
                style: AppTypography.textTheme.titleLarge?.copyWith(
                  color: isDarkMode ? AppColors.textOnPrimary : Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Icon(
              TablerIcons.bell_ringing,
              color: isDarkMode ? AppColors.textOnPrimary : Colors.white,
              size: 28,
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingL),
        _buildMetricsRow(context),
      ],
    );
  }

  Widget _buildMetricsRow(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    if (summary == null) {
      return Text(
        'Không có dữ liệu',
        style: AppTypography.textTheme.bodyLarge?.copyWith(
          color: isDarkMode 
              ? AppColors.textOnPrimary.withValues(alpha: 0.9)
              : Colors.white.withValues(alpha: 0.9),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildMetricItem(
          'Tổng',
          '${summary!.totalNotifications}',
          TablerIcons.inbox,
          context,
        ),
        _buildMetricItem(
          'Chưa đọc',
          '${summary!.unreadCount}',
          TablerIcons.mail,
          context,
        ),
        _buildMetricItem(
          'Hôm nay',
          '${summary!.todayCount}',
          TablerIcons.calendar,
          context,
        ),
        _buildMetricItem(
          'Quan trọng',
          '${summary!.importantCount}',
          TablerIcons.star,
          context,
        ),
      ],
    );
  }

  Widget _buildMetricItem(String label, String value, IconData icon, BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Column(
      children: [
        Icon(
          icon,
          color: isDarkMode 
              ? AppColors.textOnPrimary.withValues(alpha: 0.8)
              : Colors.white.withValues(alpha: 0.8),
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppTypography.textTheme.headlineSmall?.copyWith(
            color: isDarkMode ? AppColors.textOnPrimary : Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: isDarkMode 
                ? AppColors.textOnPrimary.withValues(alpha: 0.8)
                : Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingContent(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Shimmer.fromColors(
          baseColor: isDarkMode 
              ? AppColors.backgroundDarkSecondary
              : Colors.white.withValues(alpha: 0.3),
          highlightColor: isDarkMode 
              ? AppColors.backgroundDarkTertiary
              : Colors.white.withValues(alpha: 0.5),
          child: Container(
            height: 24,
            width: 160,
            decoration: BoxDecoration(
              color: isDarkMode 
                  ? AppColors.backgroundDarkSecondary
                  : Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingL),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(
            4,
            (index) => Shimmer.fromColors(
              baseColor: isDarkMode 
                  ? AppColors.backgroundDarkSecondary
                  : Colors.white.withValues(alpha: 0.3),
              highlightColor: isDarkMode 
                  ? AppColors.backgroundDarkTertiary
                  : Colors.white.withValues(alpha: 0.5),
              child: Column(
                children: [
                  Container(
                    height: 20,
                    width: 20,
                    decoration: BoxDecoration(
                      color: isDarkMode 
                          ? AppColors.backgroundDarkSecondary
                          : Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    height: 20,
                    width: 30,
                    decoration: BoxDecoration(
                      color: isDarkMode 
                          ? AppColors.backgroundDarkSecondary
                          : Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Container(
                    height: 12,
                    width: 40,
                    decoration: BoxDecoration(
                      color: isDarkMode 
                          ? AppColors.backgroundDarkSecondary
                          : Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}