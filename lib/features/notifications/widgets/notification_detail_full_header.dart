import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';

import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';
import '../models/notification_priority.dart';
import '../utils/notification_utils.dart';

/// Full header widget cho notification detail bottom sheet
class NotificationDetailFullHeader extends StatelessWidget {
  final NotificationDetail notification;

  const NotificationDetailFullHeader({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDarkMode 
                ? AppColors.borderDark.withValues(alpha: 0.3)
                : AppColors.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Priority và actions
          Row(
            children: [
              _buildPriorityBadge(),
              const Spacer(),
              _buildActionButtons(context),
            ],
          ),
          
          const SizedBox(height: AppDimensions.spacingM),
          
          // Full title
          Text(
            notification.title,
            style: AppTypography.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: AppDimensions.spacingS),
          
          // Metadata row
          _buildMetadataRow(context),
        ],
      ),
    );
  }

  Widget _buildPriorityBadge() {
    Color badgeColor;
    IconData badgeIcon;
    String badgeText;
    
    switch (notification.priority) {
      case NotificationPriorityApi.high:
        badgeColor = AppColors.error;
        badgeIcon = TablerIcons.alert_triangle;
        badgeText = 'CAO';
        break;
      case NotificationPriorityApi.normal:
        badgeColor = AppColors.warning;
        badgeIcon = TablerIcons.info_circle;
        badgeText = 'BÌNH THƯỜNG';
        break;
      case NotificationPriorityApi.low:
        badgeColor = AppColors.info;
        badgeIcon = TablerIcons.bell;
        badgeText = 'THẤP';
        break;
      case NotificationPriorityApi.urgent:
        badgeColor = AppColors.error;
        badgeIcon = TablerIcons.alert_triangle;
        badgeText = 'KHẨN CẤP';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingS,
        vertical: AppDimensions.spacingXS,
      ),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: badgeColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            badgeIcon,
            size: 14,
            color: badgeColor,
          ),
          const SizedBox(width: 4),
          Text(
            badgeText,
            style: AppTypography.textTheme.labelSmall?.copyWith(
              color: badgeColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Share button
        IconButton(
          onPressed: () {
            // TODO: Implement share functionality
            debugPrint('Share notification: ${notification.notificationId}');
          },
          icon: const Icon(TablerIcons.share, size: 20),
          style: IconButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        
        // Close button
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(TablerIcons.x, size: 20),
          style: IconButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildMetadataRow(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        // Time
        Icon(
          TablerIcons.clock,
          size: 14,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        ),
        const SizedBox(width: 4),
        Text(
          _getTimeAgo(),
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        
        const SizedBox(width: AppDimensions.spacingM),
        
        // Type
        Icon(
          TablerIcons.tag,
          size: 14,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
        ),
        const SizedBox(width: 4),
        Text(
          NotificationUtils.getNotificationTypeDisplayName(notification.notificationType),
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        
        if (notification.senderName != null) ...[
          const SizedBox(width: AppDimensions.spacingM),
          
          // Sender
          Icon(
            TablerIcons.user,
            size: 14,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(width: 4),
          Text(
            notification.senderName!,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ],
    );
  }

  String _getTimeAgo() {
    return NotificationUtils.formatTimeAgo(notification.createdAt);
  }
} 