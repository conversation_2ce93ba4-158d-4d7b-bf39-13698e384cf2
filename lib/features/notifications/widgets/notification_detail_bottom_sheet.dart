import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/index.dart';
import '../models/notification_detail.dart';
import '../blocs/notification_bloc.dart';
import '../blocs/notification_event.dart';
import 'notification_detail_handle_bar.dart';
import 'notification_detail_full_header.dart';
import 'notification_detail_content.dart';
import 'notification_detail_metadata_card.dart';
import 'notification_detail_action_buttons.dart';

/// Bottom sheet chuyên nghiệp để xem chi tiết notification
/// <PERSON><PERSON> thể vuốt lên để mở full screen
class NotificationDetailBottomSheet extends StatelessWidget {
  final NotificationDetail notification;

  const NotificationDetailBottomSheet({
    super.key,
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusL),
          topRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: _calculateInitialSize(),
        minChildSize: 0.5,
        maxChildSize: 1.0,
        expand: false,
        builder: (context, scrollController) {
          return _NotificationDetailContent(
            notification: notification,
            scrollController: scrollController,
          );
        },
      ),
    );
  }

  /// Tính toán kích thước ban đầu dựa trên content
  double _calculateInitialSize() {
    final contentLength = notification.message.length;
    final hasImage = notification.imageUrl != null;
    final hasActions = notification.isActionable;
    
    if (contentLength < 100 && !hasImage && !hasActions) {
      return 0.6; // Small
    } else if (contentLength < 300) {
      return 0.8; // Medium
    } else {
      return 0.9; // Large
    }
  }
}

/// Content widget cho notification detail
class _NotificationDetailContent extends StatefulWidget {
  final NotificationDetail notification;
  final ScrollController scrollController;

  const _NotificationDetailContent({
    required this.notification,
    required this.scrollController,
  });

  @override
  State<_NotificationDetailContent> createState() => _NotificationDetailContentState();
}

class _NotificationDetailContentState extends State<_NotificationDetailContent> {

  @override
  void initState() {
    super.initState();
    // Tự động mark as read khi mở bottom sheet nếu chưa đọc
    if (!widget.notification.isRead) {
      // Delay để tránh race condition với việc mở bottom sheet
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          try {
            context.read<NotificationBloc>().add(
              MarkNotificationAsRead(widget.notification.notificationId),
            );
          } catch (e) {
            debugPrint('Error marking notification as read: $e');
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Handle bar
        const NotificationDetailHandleBar(),
        
        // Header
        NotificationDetailFullHeader(notification: widget.notification),
        
        // Content
        Expanded(
          child: _buildFullScreenContent(),
        ),
      ],
    );
  }



  Widget _buildFullScreenContent() {
    return SingleChildScrollView(
      controller: widget.scrollController,
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main content
          NotificationDetailContent(notification: widget.notification),
          
          const SizedBox(height: AppDimensions.spacingL),
          
          // Metadata card
          NotificationDetailMetadataCard(notification: widget.notification),
          
          const SizedBox(height: AppDimensions.spacingL),
          
          // Action buttons
          if (widget.notification.isActionable)
            NotificationDetailActionButtons(notification: widget.notification),
        ],
      ),
    );
  }
} 