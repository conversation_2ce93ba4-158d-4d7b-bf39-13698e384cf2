import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:shimmer/shimmer.dart';
import '../../../core/theme/index.dart';
import '../models/notification_category.dart';
import '../models/notification_type.dart';
import '../utils/notification_utils.dart';
import '../blocs/notification_bloc.dart';
import '../blocs/notification_state.dart';
import '../blocs/notification_event.dart';

/// Bottom sheet chuyên nghiệp để filter thông báo theo lo<PERSON>i
class NotificationFilterBottomSheet extends StatelessWidget {
  final List<NotificationCategory> categories;
  final NotificationTypeApi? selectedNotificationType;
  final bool isLoading;

  const NotificationFilterBottomSheet({
    super.key,
    required this.categories,
    this.selectedNotificationType,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusL),
          topRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return BlocConsumer<NotificationBloc, NotificationState>(
            listener: (context, state) {
              debugPrint(
                '🔍 BlocConsumer: State changed - ${state.runtimeType}',
              );
              if (state is NotificationLoaded) {
                debugPrint(
                  '🔍 BlocConsumer: Selected type = ${state.selectedNotificationType}',
                );
                // Nếu state đã thay đổi và không còn loading, đóng bottom sheet
                if (!state.isRefreshing) {
                  debugPrint('🔍 BlocConsumer: Closing bottom sheet');
                  Navigator.of(context).pop();
                }
              }
            },
            builder: (context, state) {
              final currentSelectedType = state is NotificationLoaded
                  ? state.selectedNotificationType
                  : selectedNotificationType;
              // Chỉ hiển thị loading khi load lần đầu, không hiển thị khi filter
              final currentIsLoading =
                  state is NotificationLoading || isLoading;
              final currentCategories = state is NotificationLoaded
                  ? state.categories
                  : categories;

              // Debug: In ra state hiện tại
              debugPrint('🔍 Filter BottomSheet - Current State:');
              debugPrint('  - State type: ${state.runtimeType}');
              debugPrint('  - Selected type: $currentSelectedType');
              debugPrint('  - Is loading: $currentIsLoading');
              debugPrint(
                '  - Is refreshing: ${state is NotificationLoaded ? (state).isRefreshing : false}',
              );
              debugPrint('  - Categories count: ${currentCategories.length}');

              return Column(
                children: [
                  // Handle bar
                  Container(
                    margin: const EdgeInsets.only(top: AppDimensions.spacingM),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: theme.dividerColor,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Header
                  _buildHeader(context, theme, currentSelectedType),

                  // Content
                  Expanded(
                    child: currentIsLoading
                        ? _buildLoadingContent(scrollController)
                        : _buildFilterContent(
                            context,
                            theme,
                            scrollController,
                            currentCategories,
                            currentSelectedType,
                          ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ThemeData theme,
    NotificationTypeApi? selectedType,
  ) {
    final hasActiveFilter = selectedType != null;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.brightness == Brightness.dark
                ? AppColors.borderDark.withValues(alpha: 0.3)
                : AppColors.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Filter icon
          Container(
            padding: const EdgeInsets.all(AppDimensions.spacingS),
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              TablerIcons.filter,
              color: AppColors.kienlongOrange,
              size: 20,
            ),
          ),

          const SizedBox(width: AppDimensions.spacingM),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Lọc thông báo',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (hasActiveFilter)
                  Text(
                    'Đang lọc: ${NotificationUtils.getNotificationTypeDisplayName(selectedType)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.kienlongOrange,
                    ),
                  ),
              ],
            ),
          ),

          // Clear filter button
          if (hasActiveFilter)
            TextButton.icon(
              onPressed: () {
                debugPrint('🔍 Clear filter button pressed');
                context.read<NotificationBloc>().add(
                  NotificationTypeFilterChanged(null),
                );
                // Không gọi Navigator.pop() ở đây, để BlocListener tự động đóng
              },
              icon: const Icon(TablerIcons.x, size: 16),
              label: const Text('Xóa bộ lọc'),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.kienlongOrange,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.spacingM,
                  vertical: AppDimensions.spacingS,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingContent(ScrollController scrollController) {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: 8,
      itemBuilder: (context, index) => _buildLoadingItem(context),
    );
  }

  Widget _buildLoadingItem(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Shimmer.fromColors(
      baseColor: isDarkMode 
          ? AppColors.backgroundDarkSecondary
          : AppColors.neutral200,
      highlightColor: isDarkMode 
          ? AppColors.backgroundDarkTertiary
          : AppColors.neutral100,
      child: Container(
        margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
        height: 72,
        decoration: BoxDecoration(
          color: isDarkMode 
              ? AppColors.backgroundDarkSecondary
              : AppColors.backgroundSecondary,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
      ),
    );
  }

  Widget _buildFilterContent(
    BuildContext context,
    ThemeData theme,
    ScrollController scrollController,
    List<NotificationCategory> categories,
    NotificationTypeApi? selectedType,
  ) {
    if (categories.isEmpty) {
      return _buildEmptyState(context, theme);
    }

    return ListView(
      controller: scrollController,
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      children: [
        // All notifications option
        _buildFilterOption(
          context: context,
          theme: theme,
          title: 'Tất cả thông báo',
          subtitle: 'Hiển thị tất cả các loại thông báo',
          icon: TablerIcons.inbox,
          count: categories.fold(0, (sum, cat) => sum + cat.count),
          unreadCount: categories.fold(0, (sum, cat) => sum + cat.unreadCount),
          isSelected: selectedType == null,
          onTap: () {
            debugPrint(
              '🔍 Tapping "Tất cả thông báo" - selectedType: $selectedType',
            );
            debugPrint('🔍 Will send: null');
            debugPrint('🔍 Current isSelected: ${selectedType == null}');
            _handleFilterChanged(context, null);
          },
        ),

        const SizedBox(height: AppDimensions.spacingL),

        // Section title
        Text(
          'Lọc theo loại thông báo',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),

        const SizedBox(height: AppDimensions.spacingM),

        // Categories list
        ...categories.map(
          (category) => _buildFilterOption(
            context: context,
            theme: theme,
            title: category.displayName,
            subtitle: _buildCategorySubtitle(category),
            icon: NotificationUtils.getNotificationIcon(
              category.notificationType,
            ),
            count: category.count,
            unreadCount: category.unreadCount,
            isSelected: selectedType == category.notificationType,
            onTap: () =>
                _handleFilterChanged(context, category.notificationType),
          ),
        ),
      ],
    );
  }

  void _handleFilterChanged(
    BuildContext context,
    NotificationTypeApi? notificationType,
  ) {
    debugPrint('🔍 Filter Changed: $notificationType');
    context.read<NotificationBloc>().add(
      NotificationTypeFilterChanged(notificationType),
    );
    // Không đóng bottom sheet ngay lập tức
    // Để BlocListener có thể lắng nghe state changes
  }

  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            TablerIcons.filter_off,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: AppDimensions.spacingL),
          Text(
            'Không có dữ liệu lọc',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'Dữ liệu thống kê chưa có sẵn',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterOption({
    required BuildContext context,
    required ThemeData theme,
    required String title,
    required String subtitle,
    required IconData icon,
    required int count,
    required int unreadCount,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: isSelected
            ? AppColors.kienlongOrange.withValues(alpha: 0.05)
            : theme.cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isSelected
              ? AppColors.kienlongOrange.withValues(alpha: 0.3)
              : isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              // Icon
              Container(
                padding: const EdgeInsets.all(AppDimensions.spacingM),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                      : theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  icon,
                  color: isSelected
                      ? AppColors.kienlongOrange
                      : theme.colorScheme.primary,
                  size: 24,
                ),
              ),

              const SizedBox(width: AppDimensions.spacingM),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: isSelected
                            ? FontWeight.w600
                            : FontWeight.w500,
                        color: isSelected ? AppColors.kienlongOrange : null,
                      ),
                    ),
                    const SizedBox(height: AppDimensions.spacingXS),
                    Text(
                      subtitle,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Counts
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Total count
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.spacingS,
                      vertical: AppDimensions.spacingXS,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                          : theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                    child: Text(
                      count.toString(),
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? AppColors.kienlongOrange : null,
                      ),
                    ),
                  ),

                  if (unreadCount > 0) ...[
                    const SizedBox(height: AppDimensions.spacingXS),
                    // Unread count badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.spacingS,
                        vertical: AppDimensions.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.error,
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusS,
                        ),
                      ),
                      child: Text(
                        '$unreadCount chưa đọc',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ],
              ),

              // Selection indicator
              if (isSelected) ...[
                const SizedBox(width: AppDimensions.spacingM),
                Icon(
                  TablerIcons.check,
                  color: AppColors.kienlongOrange,
                  size: 20,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _buildCategorySubtitle(NotificationCategory category) {
    if (category.count == 0) {
      return 'Chưa có thông báo';
    }

    final readCount = category.readCount;
    if (category.unreadCount > 0) {
      return '$readCount đã đọc • ${category.unreadCount} chưa đọc';
    }

    return '$readCount thông báo';
  }
}
