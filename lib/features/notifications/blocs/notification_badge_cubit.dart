import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import '../services/notification_service.dart';

/// Cubit để quản lý badge count cho notification tab
class NotificationBadgeCubit extends Cubit<int> {
  final NotificationService _notificationService;
  final IAppLogger _logger;

  NotificationBadgeCubit({
    NotificationService? notificationService,
    IAppLogger? logger,
  })  : _notificationService = notificationService ?? NotificationService(),
        _logger = logger ?? getIt.get<IAppLogger>(),
        super(0);

  /// Load badge count từ API
  Future<void> loadBadgeCount() async {
    try {
      _logger.i('=== START: NotificationBadgeCubit.loadBadgeCount ===');
      
      final summary = await _notificationService.getNotificationSummary();
      final badgeCount = summary?.unreadCount ?? 0;
      
      _logger.i('Badge count loaded: $badgeCount');
      emit(badgeCount);
      
      _logger.i('=== END: NotificationBadgeCubit.loadBadgeCount ===');
    } catch (e) {
      _logger.e('Error loading badge count: $e');
      // Emit 0 on error to avoid showing incorrect badge
      emit(0);
    }
  }

  /// Tăng badge count (khi nhận FCM notification)
  void incrementBadge() {
    final newCount = state + 1;
    _logger.i('Badge count incremented: $state -> $newCount');
    emit(newCount);
  }

  /// Giảm badge count (khi mark as read)
  void decrementBadge() {
    if (state > 0) {
      final newCount = state - 1;
      _logger.i('Badge count decremented: $state -> $newCount');
      emit(newCount);
    }
  }

  /// Clear badge count
  void clearBadge() {
    if (state > 0) {
      _logger.i('Badge count cleared: $state -> 0');
      emit(0);
    }
  }

  /// Set badge count cụ thể
  void setBadgeCount(int count) {
    if (count != state) {
      _logger.i('Badge count set: $state -> $count');
      emit(count >= 0 ? count : 0);
    }
  }

  /// Sync badge count với server
  Future<void> syncBadgeCount() async {
    await loadBadgeCount();
  }

  /// Get formatted badge text cho UI
  String get badgeText {
    if (state <= 0) return '';
    if (state > 99) return '99+';
    return state.toString();
  }

  /// Check có badge không
  bool get hasBadge => state > 0;

  @override
  void onChange(Change<int> change) {
    super.onChange(change);
    _logger.d('NotificationBadgeCubit state changed: ${change.currentState} -> ${change.nextState}');
  }

  @override
  Future<void> close() {
    _logger.i('NotificationBadgeCubit disposed');
    return super.close();
  }
}