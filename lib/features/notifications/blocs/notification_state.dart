import 'package:flutter/foundation.dart';
import 'package:kiloba_biz/features/notifications/models/notification_type.dart';
import '../models/notification_summary.dart';
import '../models/notification_category.dart';
import '../models/notification_detail.dart';

/// Base class cho tất cả notification states
@immutable
abstract class NotificationState {}

/// Initial state khi vừa khởi tạo bloc
class NotificationInitial extends NotificationState {}

/// State đang loading data
class NotificationLoading extends NotificationState {
  final bool isRefreshing;

  NotificationLoading({this.isRefreshing = false});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationLoading && other.isRefreshing == isRefreshing;
  }

  @override
  int get hashCode => isRefreshing.hashCode;

  @override
  String toString() => 'NotificationLoading(isRefreshing: $isRefreshing)';
}

/// State đã load thành công data
class NotificationLoaded extends NotificationState {
  final NotificationSummary? summary;
  final List<NotificationCategory> categories;
  final List<NotificationDetail> recentNotifications;
  final List<NotificationDetail> allNotifications; // Mới: tất cả notifications
  final bool isRefreshing;
  final bool hasMoreNotifications;
  final bool hasMoreData; // Mới: pagination support
  final bool isLoadingMore; // Mới: loading more indicator
  final NotificationTypeApi? selectedNotificationType;
  final int totalNotifications;
  
  NotificationLoaded({
    required this.summary,
    required this.categories,
    required this.recentNotifications,
    this.allNotifications = const [], // Mới: default empty
    this.isRefreshing = false,
    this.hasMoreNotifications = false,
    this.hasMoreData = false, // Mới: default false
    this.isLoadingMore = false, // Mới: default false
    this.totalNotifications = 0,
    this.selectedNotificationType,
  });

  /// Copy with method để update state
  NotificationLoaded copyWith({
    NotificationSummary? summary,
    List<NotificationCategory>? categories,
    List<NotificationDetail>? recentNotifications,
    List<NotificationDetail>? allNotifications,
    bool? isRefreshing,
    bool? hasMoreNotifications,
    bool? hasMoreData,
    bool? isLoadingMore,
    int? totalNotifications, 
    NotificationTypeApi? selectedNotificationType,
  }) {
    return NotificationLoaded(
      summary: summary ?? this.summary,
      categories: categories ?? this.categories,
      recentNotifications: recentNotifications ?? this.recentNotifications,
      allNotifications: allNotifications ?? this.allNotifications,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasMoreNotifications: hasMoreNotifications ?? this.hasMoreNotifications,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      totalNotifications: totalNotifications ?? this.totalNotifications,
      selectedNotificationType: selectedNotificationType ?? this.selectedNotificationType,
    );
  }

  /// Check có thông báo chưa đọc không
  bool get hasUnreadNotifications => summary?.hasUnreadNotifications ?? false;

  /// Số lượng thông báo chưa đọc
  int get unreadCount => summary?.unreadCount ?? 0;

  /// Số lượng thông báo hôm nay
  int get todayCount => summary?.todayCount ?? 0;

  /// Số lượng thông báo quan trọng
  int get importantCount => summary?.importantCount ?? 0;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationLoaded &&
        other.summary == summary &&
        listEquals(other.categories, categories) &&
        listEquals(other.recentNotifications, recentNotifications) &&
        listEquals(other.allNotifications, allNotifications) &&
        other.isRefreshing == isRefreshing &&
        other.hasMoreNotifications == hasMoreNotifications &&
        other.hasMoreData == hasMoreData &&
        other.isLoadingMore == isLoadingMore &&
        other.totalNotifications == totalNotifications &&
        other.selectedNotificationType == selectedNotificationType;
  }

  @override
  int get hashCode {
    return Object.hash(
      summary,
      Object.hashAll(categories),
      Object.hashAll(recentNotifications),
      Object.hashAll(allNotifications),
      isRefreshing,
      hasMoreNotifications,
      hasMoreData,
      isLoadingMore,
      totalNotifications,
      selectedNotificationType,
    );
  }

  @override
  String toString() {
    return 'NotificationLoaded('
        'summary: $summary, '
        'categories: ${categories.length}, '
        'recentNotifications: ${recentNotifications.length}, '
        'allNotifications: ${allNotifications.length}, '
        'isRefreshing: $isRefreshing, '
        'hasMoreNotifications: $hasMoreNotifications, '
        'hasMoreData: $hasMoreData, '
        'isLoadingMore: $isLoadingMore, '
        'totalNotifications: $totalNotifications, '
        'selectedNotificationType: $selectedNotificationType'
        ')';
  }
}

/// State khi có lỗi
class NotificationError extends NotificationState {
  final String message;
  final NotificationErrorType type;
  final Object? originalError;

  NotificationError({
    required this.message,
    required this.type,
    this.originalError,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationError &&
        other.message == message &&
        other.type == type &&
        other.originalError == originalError;
  }

  @override
  int get hashCode => Object.hash(message, type, originalError);

  @override
  String toString() => 'NotificationError(message: $message, type: $type)';
}

/// Enum cho các loại lỗi notification
enum NotificationErrorType {
  network,
  server,
  unauthorized,
  notFound,
  unknown,
}

/// State khi đang thực hiện action (mark as read, etc.)
class NotificationActionInProgress extends NotificationState {
  final String actionType;
  final String? notificationId;

  NotificationActionInProgress({
    required this.actionType,
    this.notificationId,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationActionInProgress &&
        other.actionType == actionType &&
        other.notificationId == notificationId;
  }

  @override
  int get hashCode => Object.hash(actionType, notificationId);

  @override
  String toString() => 'NotificationActionInProgress(actionType: $actionType, notificationId: $notificationId)';
}

/// State khi action thành công
class NotificationActionSuccess extends NotificationState {
  final String actionType;
  final String? notificationId;
  final String message;

  NotificationActionSuccess({
    required this.actionType,
    this.notificationId,
    required this.message,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationActionSuccess &&
        other.actionType == actionType &&
        other.notificationId == notificationId &&
        other.message == message;
  }

  @override
  int get hashCode => Object.hash(actionType, notificationId, message);

  @override
  String toString() => 'NotificationActionSuccess(actionType: $actionType, notificationId: $notificationId, message: $message)';
}

/// State cho empty/no data
class NotificationEmpty extends NotificationState {
  final String message;

  NotificationEmpty({this.message = 'Không có thông báo nào'});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationEmpty && other.message == message;
  }

  @override
  int get hashCode => message.hashCode;

  @override
  String toString() => 'NotificationEmpty(message: $message)';
}

/// State để hiển thị dialog chi tiết thông báo
class NotificationDetailDialogState extends NotificationState {
  final NotificationDetail notification;
  final bool isLoading;

  NotificationDetailDialogState({
    required this.notification,
    this.isLoading = false,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationDetailDialogState &&
        other.notification == notification &&
        other.isLoading == isLoading;
  }

  @override
  int get hashCode => Object.hash(notification, isLoading);

  @override
  String toString() => 'NotificationDetailDialogState(notification: ${notification.notificationId}, isLoading: $isLoading)';
}