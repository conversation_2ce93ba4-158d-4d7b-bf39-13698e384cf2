import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import '../services/notification_service.dart';
import '../models/notification_summary.dart';
import '../models/notification_category.dart';
import '../models/notification_detail.dart';
import 'notification_event.dart';
import 'notification_state.dart';

/// Bloc để quản lý notification state và xử lý events
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationService _notificationService;
  final IAppLogger _logger;

  NotificationBloc({
    NotificationService? notificationService,
    IAppLogger? logger,
  }) : _notificationService = notificationService ?? NotificationService(),
       _logger = logger ?? getIt.get<IAppLogger>(),
       super(NotificationInitial()) {
    // Register event handlers
    on<LoadNotificationData>(_onLoadNotificationData);
    on<LoadAllNotifications>(_onLoadAllNotifications);
    on<RefreshNotificationData>(_onRefreshNotificationData);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<MarkNotificationAsClicked>(_onMarkNotificationAsClicked);
    on<MarkAllNotificationsAsRead>(_onMarkAllNotificationsAsRead);
    on<NotificationTypeFilterChanged>(_onNotificationTypeFilterChanged);
    on<FCMNotificationReceived>(_onFCMNotificationReceived);
    on<LoadMoreNotifications>(_onLoadMoreNotifications);
    on<RetryLoadNotifications>(_onRetryLoadNotifications);
    on<ClearNotificationCache>(_onClearNotificationCache);
    on<NavigateToNotificationDetail>(_onNavigateToNotificationDetail);
    on<ShowNotificationDetailDialog>(_onShowNotificationDetailDialog);
  }

  /// Load initial notification data
  Future<void> _onLoadNotificationData(
    LoadNotificationData event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.LoadNotificationData ===');
      emit(NotificationLoading());

      // Load all data concurrently
      final results = await Future.wait([
        _notificationService.getNotificationSummary(),
        _notificationService.getNotificationCategories(),
        _notificationService.getRecentNotifications(limit: 10),
      ]);

      final summary = results[0] as NotificationSummary?;
      final categories = results[1] as List<NotificationCategory>;
      final recentNotifications = results[2] as List<NotificationDetail>;

      _logger.i('Notification data loaded successfully');
      _logger.i('Summary: ${summary?.totalNotifications ?? 0} total');
      _logger.i('Categories: ${categories.length} items');
      _logger.i('Recent: ${recentNotifications.length} items');

      emit(
        NotificationLoaded(
          summary: summary,
          categories: categories,
          recentNotifications: recentNotifications,
          totalNotifications: summary?.totalNotifications ?? 0,
        ),
      );

      _logger.i('=== END: NotificationBloc.LoadNotificationData ===');
    } on NotificationException catch (e) {
      _logger.e('NotificationException in LoadNotificationData: ${e.message}');
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      _logger.e('Unknown error in LoadNotificationData: $e');
      emit(
        NotificationError(
          message: 'Lỗi không xác định khi tải thông báo',
          type: NotificationErrorType.unknown,
          originalError: e,
        ),
      );
    }
  }

  /// Refresh notification data (pull-to-refresh)
  Future<void> _onRefreshNotificationData(
    RefreshNotificationData event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.RefreshNotificationData ===');

      // Clear cache first
      _notificationService.clearCache();

      // Emit refreshing state if currently loaded
      if (state is NotificationLoaded) {
        emit((state as NotificationLoaded).copyWith(isRefreshing: true));
      } else {
        emit(NotificationLoading(isRefreshing: true));
      }

      // Refresh all data
      final results = await Future.wait([
        _notificationService.getNotificationSummary(),
        _notificationService.getNotificationCategories(),
        _notificationService.getRecentNotifications(limit: 10),
      ]);

      final summary = results[0] as NotificationSummary?;
      final categories = results[1] as List<NotificationCategory>;
      final recentNotifications = results[2] as List<NotificationDetail>;

      _logger.i('Notification data refreshed successfully');

      emit(
        NotificationLoaded(
          summary: summary,
          categories: categories,
          recentNotifications: recentNotifications,
          isRefreshing: false,
          totalNotifications: summary?.totalNotifications ?? 0,
        ),
      );

      _logger.i('=== END: NotificationBloc.RefreshNotificationData ===');
    } on NotificationException catch (e) {
      _logger.e(
        'NotificationException in RefreshNotificationData: ${e.message}',
      );
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      _logger.e('Unknown error in RefreshNotificationData: $e');
      emit(
        NotificationError(
          message: 'Lỗi không xác định khi làm mới thông báo',
          type: NotificationErrorType.unknown,
          originalError: e,
        ),
      );
    }
  }

  /// Mark notification as read
  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.MarkNotificationAsRead ===');
      _logger.i('Notification ID: ${event.notificationId}');

      final success = await _notificationService.markNotificationAsRead(
        event.notificationId,
      );

      if (success) {
        _logger.i('Notification marked as read successfully');

        // Update local state if currently loaded
        if (state is NotificationLoaded) {
          final currentState = state as NotificationLoaded;

          // Update the notification in recent list
          final updatedNotifications = currentState.recentNotifications.map((
            notification,
          ) {
            if (notification.notificationId == event.notificationId) {
              return notification.markAsRead();
            }
            return notification;
          }).toList();

          // Update summary if available
          NotificationSummary? updatedSummary;
          if (currentState.summary != null) {
            final summary = currentState.summary!;
            updatedSummary = summary.copyWith(
              unreadCount: summary.unreadCount > 0
                  ? summary.unreadCount - 1
                  : 0,
              readCount: summary.readCount + 1,
            );
          }

                  final newState = currentState.copyWith(
          summary: updatedSummary,
          recentNotifications: updatedNotifications,
        );
        _logger.i('Emitting updated NotificationLoaded state');
        emit(newState);
      }
      // Không emit NotificationActionSuccess vì đã emit NotificationLoaded rồi
      // UI sẽ tự động cập nhật từ NotificationLoaded state
      } else {
        _logger.i('Notification was already read');
      }

      _logger.i('=== END: NotificationBloc.MarkNotificationAsRead ===');
    } on NotificationException catch (e) {
      _logger.e(
        'NotificationException in MarkNotificationAsRead: ${e.message}',
      );
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      _logger.e('Unknown error in MarkNotificationAsRead: $e');
      emit(
        NotificationError(
          message: 'Lỗi không xác định khi đánh dấu thông báo đã đọc',
          type: NotificationErrorType.unknown,
          originalError: e,
        ),
      );
    }
  }

  /// Mark notification as clicked
  Future<void> _onMarkNotificationAsClicked(
    MarkNotificationAsClicked event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.MarkNotificationAsClicked ===');
      _logger.i('Notification ID: ${event.notificationId}');

      final success = await _notificationService.markNotificationAsClicked(
        event.notificationId,
      );

      if (success) {
        _logger.i('Notification marked as clicked successfully');

        // Update local state if currently loaded
        if (state is NotificationLoaded) {
          final currentState = state as NotificationLoaded;

          // Update the notification in recent list
          final updatedNotifications = currentState.recentNotifications.map((
            notification,
          ) {
            if (notification.notificationId == event.notificationId) {
              return notification.markAsClicked();
            }
            return notification;
          }).toList();

          emit(
            currentState.copyWith(recentNotifications: updatedNotifications),
          );
        }
      }

      _logger.i('=== END: NotificationBloc.MarkNotificationAsClicked ===');
    } on NotificationException catch (e) {
      _logger.e(
        'NotificationException in MarkNotificationAsClicked: ${e.message}',
      );
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      _logger.e('Unknown error in MarkNotificationAsClicked: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> _onMarkAllNotificationsAsRead(
    MarkAllNotificationsAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.MarkAllNotificationsAsRead ===');
      _logger.i('Notification type: ${event.notificationType}');

      final count = await _notificationService.markAllNotificationsAsRead(
        notificationType: event.notificationType,
      );

      _logger.i('$count notifications marked as read');

      if (count > 0) {
        // Refresh data to get updated counts
        add(RefreshNotificationData());

        emit(
          NotificationActionSuccess(
            actionType: 'mark_all_as_read',
            message: 'Đã đánh dấu $count thông báo đã đọc',
          ),
        );
      }

      _logger.i('=== END: NotificationBloc.MarkAllNotificationsAsRead ===');
    } on NotificationException catch (e) {
      _logger.e(
        'NotificationException in MarkAllNotificationsAsRead: ${e.message}',
      );
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      _logger.e('Unknown error in MarkAllNotificationsAsRead: $e');
      emit(
        NotificationError(
          message: 'Lỗi không xác định khi đánh dấu tất cả thông báo đã đọc',
          type: NotificationErrorType.unknown,
          originalError: e,
        ),
      );
    }
  }

  /// Handle category filter change
  Future<void> _onNotificationTypeFilterChanged(
    NotificationTypeFilterChanged event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.CategoryFilterChanged ===');
      _logger.i('Selected category: ${event.notificationType}');
      debugPrint('🔍 Bloc: Filter event received - ${event.notificationType}');
      debugPrint('🔍 Bloc: Event type: ${event.runtimeType}');
      debugPrint(
        '🔍 Bloc: Event notificationType type: ${event.notificationType.runtimeType}',
      );

      if (state is NotificationLoaded) {
        final currentState = state as NotificationLoaded;

        // Emit loading state first
        final loadingState = NotificationLoaded(
          summary: currentState.summary,
          categories: currentState.categories,
          recentNotifications: currentState.recentNotifications,
          isRefreshing: true,
          hasMoreNotifications: currentState.hasMoreNotifications,
          totalNotifications: currentState.totalNotifications,
          selectedNotificationType: event.notificationType,
        );
        debugPrint(
          '🔍 Bloc: Emitting loading state with selectedType: ${loadingState.selectedNotificationType}',
        );
        debugPrint(
          '🔍 Bloc: Event notificationType: ${event.notificationType}',
        );
        emit(loadingState);

        // Load filtered notifications
        final filteredNotifications = await _notificationService
            .getNotificationsWithFilters(
              notificationType: event.notificationType,
              limit: 20,
            );

        _logger.i(
          'Filtered notifications loaded: ${filteredNotifications.length} items',
        );

        // Emit final state with filtered data
        final newState = NotificationLoaded(
          summary: currentState.summary,
          categories: currentState.categories,
          recentNotifications: filteredNotifications,
          isRefreshing: false,
          hasMoreNotifications: currentState.hasMoreNotifications,
          totalNotifications: currentState.totalNotifications,
          selectedNotificationType: event.notificationType,
        );
        debugPrint(
          '🔍 Bloc: Emitting new state with selectedType: ${newState.selectedNotificationType}',
        );
        debugPrint(
          '🔍 Bloc: Event notificationType in final state: ${event.notificationType}',
        );
        emit(newState);
      }

      _logger.i('=== END: NotificationBloc.CategoryFilterChanged ===');
    } on NotificationException catch (e) {
      _logger.e('NotificationException in CategoryFilterChanged: ${e.message}');
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      _logger.e('Unknown error in CategoryFilterChanged: $e');
    }
  }

  /// Handle FCM notification received
  Future<void> _onFCMNotificationReceived(
    FCMNotificationReceived event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.FCMNotificationReceived ===');
      _logger.i('FCM payload: ${event.payload}');

      // Auto refresh notification data when FCM received
      add(RefreshNotificationData());

      _logger.i('=== END: NotificationBloc.FCMNotificationReceived ===');
    } catch (e) {
      _logger.e('Error in FCMNotificationReceived: $e');
    }
  }

  /// Load all notifications with filter
  Future<void> _onLoadAllNotifications(
    LoadAllNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.LoadAllNotifications ===');
      _logger.i('Filter: ${event.filter}');

      emit(NotificationLoading());

      // Load all data concurrently
      final results = await Future.wait([
        _notificationService.getNotificationSummary(),
        _notificationService.getNotificationCategories(),
        _notificationService.getNotificationsWithFilters(
          notificationType: event.filter.notificationType,
          isRead: event.filter.isRead,
          isImportant: event.filter.isImportant,
          limit: event.filter.limit,
          offset: event.filter.offset,
        ),
      ]);

      final summary = results[0] as NotificationSummary?;
      final categories = results[1] as List<NotificationCategory>;
      final allNotifications = results[2] as List<NotificationDetail>;

      _logger.i('All notifications loaded successfully');
      _logger.i('Summary: ${summary?.totalNotifications ?? 0} total');
      _logger.i('Categories: ${categories.length} items');
      _logger.i('All notifications: ${allNotifications.length} items');

      emit(
        NotificationLoaded(
          summary: summary,
          categories: categories,
          recentNotifications: allNotifications.take(10).toList(),
          allNotifications: allNotifications,
          hasMoreData: allNotifications.length >= event.filter.limit,
          totalNotifications: summary?.totalNotifications ?? 0,
          selectedNotificationType: event.filter.notificationType,
        ),
      );

      _logger.i('=== END: NotificationBloc.LoadAllNotifications ===');
    } on NotificationException catch (e) {
      _logger.e('NotificationException in LoadAllNotifications: ${e.message}');
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      _logger.e('Unknown error in LoadAllNotifications: $e');
      emit(
        NotificationError(
          message: 'Lỗi không xác định khi tải thông báo',
          type: NotificationErrorType.unknown,
          originalError: e,
        ),
      );
    }
  }

  /// Load more notifications (pagination)
  Future<void> _onLoadMoreNotifications(
    LoadMoreNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('=== START: NotificationBloc.LoadMoreNotifications ===');
      _logger.i('Filter: ${event.filter}');

      if (state is NotificationLoaded) {
        final currentState = state as NotificationLoaded;
        
        // Set loading more state
        emit(currentState.copyWith(isLoadingMore: true));

        final moreNotifications = await _notificationService
            .getNotificationsWithFilters(
              notificationType: event.filter.notificationType,
              isRead: event.filter.isRead,
              isImportant: event.filter.isImportant,
              limit: event.filter.limit,
              offset: currentState.allNotifications.length,
            );

        final updatedAllNotifications = [
          ...currentState.allNotifications,
          ...moreNotifications,
        ];

        emit(
          currentState.copyWith(
            allNotifications: updatedAllNotifications,
            recentNotifications: updatedAllNotifications.take(10).toList(),
            hasMoreData: moreNotifications.length >= event.filter.limit,
            isLoadingMore: false,
          ),
        );
      }

      _logger.i('=== END: NotificationBloc.LoadMoreNotifications ===');
    } on NotificationException catch (e) {
      _logger.e('NotificationException in LoadMoreNotifications: ${e.message}');
      if (state is NotificationLoaded) {
        emit((state as NotificationLoaded).copyWith(isLoadingMore: false));
      }
    } catch (e) {
      _logger.e('Unknown error in LoadMoreNotifications: $e');
      if (state is NotificationLoaded) {
        emit((state as NotificationLoaded).copyWith(isLoadingMore: false));
      }
    }
  }

  /// Retry loading notifications
  Future<void> _onRetryLoadNotifications(
    RetryLoadNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    add(LoadNotificationData());
  }

  /// Clear notification cache
  Future<void> _onClearNotificationCache(
    ClearNotificationCache event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('Clearing notification cache');
      _notificationService.clearCache();
    } catch (e) {
      _logger.e('Error clearing notification cache: $e');
    }
  }

  /// Navigate to notification detail
  Future<void> _onNavigateToNotificationDetail(
    NavigateToNotificationDetail event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      _logger.i('Navigating to notification detail: ${event.notificationId}');

      // Mark as read and clicked
      add(MarkNotificationAsRead(event.notificationId));
      add(MarkNotificationAsClicked(event.notificationId));

      // TODO: Implement navigation logic
    } catch (e) {
      _logger.e('Error navigating to notification detail: $e');
    }
  }

  /// Show notification detail dialog from push notification
  Future<void> _onShowNotificationDetailDialog(
    ShowNotificationDetailDialog event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      debugPrint('🎯 _onShowNotificationDetailDialog called with ID: ${event.notificationId}');
      _logger.i('Showing notification detail dialog: ${event.notificationId}');

      // Get notification detail from API
      debugPrint('📡 Calling getNotificationById API...');
      final notifications = await _notificationService.getNotificationById(event.notificationId);
      debugPrint('📡 API response: ${notifications.length} notifications');
      
      if (notifications.isNotEmpty) {
        final notification = notifications.first;
        debugPrint('✅ Found notification: ${notification.title}');
        
        // Mark as read and clicked
        debugPrint('📝 Marking notification as read and clicked...');
        add(MarkNotificationAsRead(event.notificationId));
        add(MarkNotificationAsClicked(event.notificationId));
        
        // Emit state with notification detail for dialog
        debugPrint('📤 Emitting NotificationDetailDialogState...');
        emit(
          NotificationDetailDialogState(
            notification: notification,
            isLoading: false,
          ),
        );
        debugPrint('✅ NotificationDetailDialogState emitted successfully');
      } else {
        debugPrint('❌ No notifications found for ID: ${event.notificationId}');
        _logger.w('Notification not found: ${event.notificationId}');
        emit(
          NotificationError(
            message: 'Không tìm thấy thông báo',
            type: NotificationErrorType.notFound,
          ),
        );
      }
    } on NotificationException catch (e) {
      debugPrint('❌ NotificationException: ${e.message}');
      _logger.e('NotificationException in ShowNotificationDetailDialog: ${e.message}');
      emit(
        NotificationError(
          message: e.message,
          type: _mapNotificationExceptionType(e.type),
          originalError: e,
        ),
      );
    } catch (e) {
      debugPrint('❌ Unknown error: $e');
      _logger.e('Unknown error in ShowNotificationDetailDialog: $e');
      emit(
        NotificationError(
          message: 'Lỗi không xác định khi hiển thị thông báo',
          type: NotificationErrorType.unknown,
          originalError: e,
        ),
      );
    }
  }

  /// Map NotificationExceptionType to NotificationErrorType
  NotificationErrorType _mapNotificationExceptionType(
    NotificationExceptionType type,
  ) {
    switch (type) {
      case NotificationExceptionType.apiError:
        return NotificationErrorType.server;
      case NotificationExceptionType.unauthorized:
        return NotificationErrorType.unauthorized;
      case NotificationExceptionType.invalidResponse:
        return NotificationErrorType.server;
      case NotificationExceptionType.notFound:
        return NotificationErrorType.server;
      case NotificationExceptionType.unknown:
        return NotificationErrorType.unknown;
    }
  }

  @override
  Future<void> close() {
    _logger.i('NotificationBloc disposed');
    return super.close();
  }
}
