import 'package:flutter/foundation.dart';
import 'package:kiloba_biz/features/notifications/models/notification_type.dart';
import 'package:kiloba_biz/features/notifications/models/notification_filter_request.dart';

/// Base class cho tất cả notification events
@immutable
abstract class NotificationEvent {}

/// Event để load initial notification data
class LoadNotificationData extends NotificationEvent {}

/// Event để refresh notification data (pull-to-refresh)
class RefreshNotificationData extends NotificationEvent {}

/// Event để đánh dấu một thông báo đã đọc
class MarkNotificationAsRead extends NotificationEvent {
  final String notificationId;

  MarkNotificationAsRead(this.notificationId);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarkNotificationAsRead && other.notificationId == notificationId;
  }

  @override
  int get hashCode => notificationId.hashCode;

  @override
  String toString() => 'MarkNotificationAsRead(notificationId: $notificationId)';
}

/// Event để đánh dấu một thông báo đã click
class MarkNotificationAsClicked extends NotificationEvent {
  final String notificationId;

  MarkNotificationAsClicked(this.notificationId);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarkNotificationAsClicked && other.notificationId == notificationId;
  }

  @override
  int get hashCode => notificationId.hashCode;

  @override
  String toString() => 'MarkNotificationAsClicked(notificationId: $notificationId)';
}

/// Event để đánh dấu tất cả thông báo đã đọc
class MarkAllNotificationsAsRead extends NotificationEvent {
  final NotificationTypeApi? notificationType;

  MarkAllNotificationsAsRead({this.notificationType});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarkAllNotificationsAsRead && other.notificationType == notificationType;
  }

  @override
  int get hashCode => notificationType.hashCode;

  @override
  String toString() => 'MarkAllNotificationsAsRead(notificationType: $notificationType)';
}

/// Event khi thay đổi filter notification type
class NotificationTypeFilterChanged extends NotificationEvent {
  final NotificationTypeApi? notificationType;

  NotificationTypeFilterChanged(this.notificationType);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationTypeFilterChanged && other.notificationType == notificationType;
  }

  @override
  int get hashCode => notificationType.hashCode;

  @override
  String toString() => 'NotificationTypeFilterChanged(notificationType: $notificationType)';
}

/// Event khi nhận được FCM notification
class FCMNotificationReceived extends NotificationEvent {
  final Map<String, dynamic> payload;

  FCMNotificationReceived(this.payload);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FCMNotificationReceived && 
           mapEquals(other.payload, payload);
  }

  @override
  int get hashCode => payload.hashCode;

  @override
  String toString() => 'FCMNotificationReceived(payload: $payload)';
}

/// Event để load tất cả thông báo với filter
class LoadAllNotifications extends NotificationEvent {
  final NotificationFilterRequest filter;

  LoadAllNotifications(this.filter);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoadAllNotifications && other.filter == filter;
  }

  @override
  int get hashCode => filter.hashCode;

  @override
  String toString() => 'LoadAllNotifications(filter: $filter)';
}

/// Event để load thêm thông báo (pagination)
class LoadMoreNotifications extends NotificationEvent {
  final NotificationFilterRequest filter;

  LoadMoreNotifications(this.filter);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoadMoreNotifications && other.filter == filter;
  }

  @override
  int get hashCode => filter.hashCode;

  @override
  String toString() => 'LoadMoreNotifications(filter: $filter)';
}

/// Event để retry khi có lỗi
class RetryLoadNotifications extends NotificationEvent {}

/// Event để clear cache
class ClearNotificationCache extends NotificationEvent {}

/// Event để navigate tới notification detail
class NavigateToNotificationDetail extends NotificationEvent {
  final String notificationId;

  NavigateToNotificationDetail(this.notificationId);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NavigateToNotificationDetail && other.notificationId == notificationId;
  }

  @override
  int get hashCode => notificationId.hashCode;

  @override
  String toString() => 'NavigateToNotificationDetail(notificationId: $notificationId)';
}

/// Event để hiển thị dialog chi tiết thông báo từ push notification
class ShowNotificationDetailDialog extends NotificationEvent {
  final String notificationId;

  ShowNotificationDetailDialog(this.notificationId);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShowNotificationDetailDialog && other.notificationId == notificationId;
  }

  @override
  int get hashCode => notificationId.hashCode;

  @override
  String toString() => 'ShowNotificationDetailDialog(notificationId: $notificationId)';
}