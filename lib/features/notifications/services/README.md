# Notification Service Documentation

## Tổng quan

`NotificationService` cung cấp các API để quản lý thông báo từ backend. Service này được thiết kế theo pattern singleton và tuân thủ kiến trúc featured-based của dự án.

## API Functions

Service này implement tất cả 8 API functions từ backend documentation:

### 1. `getNotificationSummary()`
Lấy tóm tắt thống kê thông báo cho user hiện tại.

**Response:** `NotificationSummary?` hoặc `null` nếu không có JWT/user_id

```dart
final summary = await NotificationService().getNotificationSummary();
if (summary != null) {
  print('Tổng thông báo: ${summary.totalNotifications}');
  print('Chưa đọc: ${summary.unreadCount}');
  print('Hôm nay: ${summary.todayCount}');
}
```

### 2. `getNotificationCategories()`
L<PERSON>y thống kê thông báo theo category.

**Response:** `List<NotificationCategory>`

```dart
final categories = await NotificationService().getNotificationCategories();
for (final category in categories) {
  print('${category.displayName}: ${category.count} (${category.unreadCount} chưa đọc)');
}
```

### 3. `getRecentNotifications({int limit = 10})`
Lấy danh sách thông báo gần đây.

**Parameters:**
- `limit`: Số lượng thông báo tối đa (mặc định: 10)

**Response:** `List<NotificationDetail>`

```dart
final notifications = await NotificationService().getRecentNotifications(limit: 20);
for (final notif in notifications) {
  print('${notif.title} - ${notif.timeAgo}');
}
```

### 4. `getNotificationsWithFilters(...)`
Lấy danh sách thông báo với bộ lọc và hỗ trợ pagination.

**Parameters:**
- `category`: Lọc theo category (optional)
- `notificationType`: Lọc theo loại thông báo (optional)
- `isRead`: Lọc theo trạng thái đọc (optional)
- `isImportant`: Lọc theo thông báo quan trọng (optional)  
- `limit`: Số lượng tối đa (mặc định: 20)
- `offset`: Số lượng bỏ qua cho pagination (mặc định: 0)

**Response:** `List<NotificationDetail>`

```dart
final notifications = await NotificationService().getNotificationsWithFilters(
  category: 'PROPOSAL',
  isRead: false,
  isImportant: true,
  limit: 20,
  offset: 0,
);
```

### 5. `markNotificationAsRead(String notificationId)`
Đánh dấu thông báo đã đọc.

**Parameters:**
- `notificationId`: ID của thông báo

**Response:** `bool` - `true` nếu đánh dấu thành công, `false` nếu đã đọc trước đó

```dart
final success = await NotificationService().markNotificationAsRead('notif_123');
if (success) {
  print('Đánh dấu đã đọc thành công');
}
```

### 6. `markNotificationAsClicked(String notificationId)`
Đánh dấu thông báo đã click.

**Parameters:**
- `notificationId`: ID của thông báo

**Response:** `bool` - `true` nếu đánh dấu thành công, `false` nếu đã click trước đó

```dart
final success = await NotificationService().markNotificationAsClicked('notif_123');
```

### 7. `markAllNotificationsAsRead({String? category})`
Đánh dấu tất cả thông báo đã đọc.

**Parameters:**
- `category`: Chỉ đánh dấu thông báo trong category này (optional)

**Response:** `int` - Số lượng thông báo đã được đánh dấu

```dart
final count = await NotificationService().markAllNotificationsAsRead();
print('Đã đánh dấu $count thông báo');

// Hoặc chỉ mark category cụ thể
final proposalCount = await NotificationService().markAllNotificationsAsRead(
  category: 'PROPOSAL'
);
```

### 8. `getNewNotifications(DateTime lastCheckTime)`
Lấy thông báo mới từ thời điểm cuối cùng check.

**Parameters:**
- `lastCheckTime`: Thời điểm check cuối cùng

**Response:** `List<NotificationDetail>`

```dart
final lastCheck = DateTime.now().subtract(Duration(hours: 1));
final newNotifications = await NotificationService().getNewNotifications(lastCheck);
```

## Cache Management

Service có cache mechanism với timeout 5 phút để tối ưu performance:

```dart
// Lấy cached data (nếu có)
final cachedSummary = NotificationService().getCachedSummary();
final cachedCategories = NotificationService().getCachedCategories();
final cachedRecent = NotificationService().getCachedRecentNotifications();

// Clear cache
NotificationService().clearCache();

// Refresh tất cả data
await NotificationService().refreshAllNotificationData();
```

## Error Handling

Service sử dụng `NotificationException` với các loại lỗi:

```dart
try {
  final summary = await NotificationService().getNotificationSummary();
} on NotificationException catch (e) {
  switch (e.type) {
    case NotificationExceptionType.apiError:
      // Xử lý lỗi API
      break;
    case NotificationExceptionType.unauthorized:
      // Xử lý lỗi không có quyền
      break;
    case NotificationExceptionType.invalidResponse:
      // Xử lý lỗi response không hợp lệ
      break;
    default:
      // Xử lý lỗi khác
      break;
  }
}
```

## Models

### NotificationSummary
```dart
class NotificationSummary {
  final int totalNotifications;
  final int unreadCount;
  final int readCount;
  final int todayCount;
  final int importantCount;
  
  // Helper properties
  double get readPercentage;
  bool get hasUnreadNotifications;
  bool get hasImportantNotifications;
}
```

### NotificationCategory
```dart
class NotificationCategory {
  final String category;
  final int count;
  final int unreadCount;
  
  // Helper properties  
  String get displayName; // "Đề xuất", "Khách hàng", etc.
  String get iconName; // Material icon names
  bool get hasUnreadNotifications;
}
```

### NotificationDetail
```dart
class NotificationDetail {
  final String notificationId;
  final String title;
  final String message;
  final String? shortMessage;
  final NotificationTypeApi notificationType;
  final String category;
  final NotificationPriorityApi priority;
  final bool isImportant;
  final bool isActionable;
  final String? actionUrl;
  final String? actionText;
  final DateTime createdAt;
  final DateTime? readAt;
  final bool isRead;
  final bool isClicked;
  
  // Helper properties
  String get timeAgo; // "5 phút trước", "2 giờ trước"
  String get typeDisplayName; // "Đề xuất", "Khách hàng"
  String get displayActionText; // Text cho action button
  bool get isNew; // Trong 24h và chưa đọc
}
```

## Enums

### NotificationTypeApi
```dart
enum NotificationTypeApi {
  proposal,      // Đề xuất
  customer,      // Khách hàng  
  document,      // Tài liệu
  approval,      // Phê duyệt
  reminder,      // Nhắc nhở
  meeting,       // Cuộc họp
  deadline,      // Hạn chót
  birthday,      // Sinh nhật
  holiday,       // Ngày lễ
  general,       // Chung
  system,        // Hệ thống
  urgent,        // Khẩn cấp
  training,      // Đào tạo
  performance,   // Hiệu suất
  commission,    // Hoa hồng
}
```

### NotificationPriorityApi
```dart
enum NotificationPriorityApi {
  low,           // Thấp
  normal,        // Bình thường
  high,          // Cao
  urgent,        // Khẩn cấp
}
```

## Usage trong UI

```dart
class NotificationScreen extends StatefulWidget {
  @override
  _NotificationScreenState createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final _notificationService = NotificationService();
  
  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }
  
  Future<void> _loadNotifications() async {
    try {
      final summary = await _notificationService.getNotificationSummary();
      final categories = await _notificationService.getNotificationCategories();
      final recent = await _notificationService.getRecentNotifications();
      
      // Update UI
      setState(() {
        // Update state variables
      });
    } on NotificationException catch (e) {
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(e.message)),
      );
    }
  }
  
  Future<void> _onNotificationTap(NotificationDetail notification) async {
    // Mark as read and clicked
    await _notificationService.markNotificationAsRead(notification.notificationId);
    await _notificationService.markNotificationAsClicked(notification.notificationId);
    
    // Navigate to action URL if actionable
    if (notification.isActionable && notification.actionUrl != null) {
      // Navigate to the action URL
    }
  }
}
```

## Security Notes

- Tất cả API calls sử dụng JWT authentication từ backend
- User chỉ có thể truy cập thông báo của chính mình
- Service tự động handle trường hợp không có JWT (return empty/null)