import 'package:kiloba_biz/features/notifications/models/notification_type.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/api/api_service.dart';
import '../models/notification_summary.dart';
import '../models/notification_category.dart';
import '../models/notification_detail.dart';

/// Service để quản lý notifications từ backend API
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // API endpoints
  static const String _getSummaryEndpoint = '/rest/rpc/get_notification_summary';
  static const String _getCategoriesEndpoint = '/rest/rpc/get_notification_categories';
  static const String _getRecentEndpoint = '/rest/rpc/get_recent_notifications';
  static const String _getWithFiltersEndpoint = '/rest/rpc/get_notifications_with_filters';
  static const String _getNotificationByIdEndpoint = '/rest/rpc/get_notification_by_id';
  static const String _markAsReadEndpoint = '/rest/rpc/mark_notification_as_read';
  static const String _markAsClickedEndpoint = '/rest/rpc/mark_notification_as_clicked';
  static const String _markAllAsReadEndpoint = '/rest/rpc/mark_all_notifications_as_read';
  static const String _getNewEndpoint = '/rest/rpc/get_new_notifications';

  // Cache data
  NotificationSummary? _summaryCache;
  List<NotificationCategory>? _categoriesCache;
  List<NotificationDetail>? _recentCache;
  DateTime? _lastCacheTime;
  
  // Cache timeout trong phút
  static const int _cacheTimeoutMinutes = 5;

  /// Lấy thống kê tổng quan thông báo
  Future<NotificationSummary?> getNotificationSummary() async {
    try {
      _logger.i('=== START: NotificationService.getNotificationSummary ===');
      _logger.i('API endpoint: $_getSummaryEndpoint');

      // Check cache
      if (_isCacheValid() && _summaryCache != null) {
        _logger.i('Returning cached summary');
        return _summaryCache;
      }

      final response = await _apiService.post(_getSummaryEndpoint);

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response là array, có thể empty nếu không có JWT
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        if (dataList.isEmpty) {
          _logger.w('No notification summary data (possibly no JWT or user_id)');
          return null;
        }
        
        final summary = NotificationSummary.fromJson(dataList.first);
        
        // Cache kết quả
        _summaryCache = summary;
        _updateCacheTime();
        
        _logger.i('Notification summary parsed successfully');
        _logger.i('=== END: NotificationService.getNotificationSummary ===');
        return summary;
      } else {
        _logger.e('Invalid response format - expected List but got ${response.data.runtimeType}');
        throw NotificationException(
          message: 'Invalid response format for notification summary',
          type: NotificationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching notification summary: ${e.message}');
      throw NotificationException(
        message: 'Không thể lấy thống kê thông báo: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching notification summary: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi lấy thống kê thông báo',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy thống kê thông báo theo category
  Future<List<NotificationCategory>> getNotificationCategories() async {
    try {
      _logger.i('=== START: NotificationService.getNotificationCategories ===');
      _logger.i('API endpoint: $_getCategoriesEndpoint');

      // Check cache
      if (_isCacheValid() && _categoriesCache != null) {
        _logger.i('Returning cached categories');
        return _categoriesCache!;
      }

      final response = await _apiService.post(_getCategoriesEndpoint);

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        final categories = dataList.map((item) => NotificationCategory.fromJson(item)).toList();
        
        // Cache kết quả
        _categoriesCache = categories;
        _updateCacheTime();
        
        _logger.i('Notification categories parsed successfully: ${categories.length} items');
        _logger.i('=== END: NotificationService.getNotificationCategories ===');
        return categories;
      } else {
        _logger.e('Invalid response format - expected List but got ${response.data.runtimeType}');
        throw NotificationException(
          message: 'Invalid response format for notification categories',
          type: NotificationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching notification categories: ${e.message}');
      throw NotificationException(
        message: 'Không thể lấy danh mục thông báo: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching notification categories: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi lấy danh mục thông báo',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }


  /// Lấy danh sách thông báo gần đây
  Future<List<NotificationDetail>> getNotificationById(String notificationId) async {
    try {
      _logger.i('=== START: NotificationService.getNotificationById ===');
      _logger.i('Notification ID: $notificationId');
      _logger.i('API endpoint: $_getNotificationByIdEndpoint');

      final Map<String, dynamic> params = {
        'p_notification_id': notificationId,
      };

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _getNotificationByIdEndpoint,
        data: params,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        final notifications = dataList.map((item) => NotificationDetail.fromJson(item)).toList();
        
        _logger.i('Notification parsed successfully: ${notifications.length} items');
        _logger.i('=== END: NotificationService.getNotificationById ===');
        return notifications;
      } else {
        _logger.e('Invalid response format - expected List but got ${response.data.runtimeType}');
        throw NotificationException(
          message: 'Invalid response format for notification by id',
          type: NotificationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching notification by id: ${e.message}');
      throw NotificationException(
        message: 'Không thể lấy thông báo theo ID: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching notification by id: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi lấy thông báo theo ID',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách thông báo gần đây
  Future<List<NotificationDetail>> getRecentNotifications({int limit = 20}) async {
    try {
      _logger.i('=== START: NotificationService.getRecentNotifications ===');
      _logger.i('Limit: $limit');
      _logger.i('API endpoint: $_getRecentEndpoint');

      final Map<String, dynamic> params = {
        'p_limit': limit,
      };

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _getRecentEndpoint,
        data: params,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        final notifications = dataList.map((item) => NotificationDetail.fromJson(item)).toList();
        
        // Cache kết quả
        _recentCache = notifications;
        _updateCacheTime();
        
        _logger.i('Recent notifications parsed successfully: ${notifications.length} items');
        _logger.i('=== END: NotificationService.getRecentNotifications ===');
        return notifications;
      } else {
        _logger.e('Invalid response format - expected List but got ${response.data.runtimeType}');
        throw NotificationException(
          message: 'Invalid response format for recent notifications',
          type: NotificationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching recent notifications: ${e.message}');
      throw NotificationException(
        message: 'Không thể lấy thông báo gần đây: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching recent notifications: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi lấy thông báo gần đây',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách thông báo với bộ lọc
  Future<List<NotificationDetail>> getNotificationsWithFilters({
    NotificationTypeApi? notificationType,
    bool? isRead,
    bool? isImportant,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.i('=== START: NotificationService.getNotificationsWithFilters ===');
      _logger.i('Type: $notificationType');
      _logger.i('Is read: $isRead');
      _logger.i('Is important: $isImportant');
      _logger.i('Limit: $limit, Offset: $offset');
      _logger.i('API endpoint: $_getWithFiltersEndpoint');

      final Map<String, dynamic> params = {
        'p_limit': limit,
        'p_offset': offset,
      };

      if (notificationType != null) params['p_notification_type'] = notificationType.name.toUpperCase();
      if (isRead != null) params['p_is_read'] = isRead;
      if (isImportant != null) params['p_is_important'] = isImportant;

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _getWithFiltersEndpoint,
        data: params,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        final notifications = dataList.map((item) => NotificationDetail.fromJson(item)).toList();
        
        _logger.i('Filtered notifications parsed successfully: ${notifications.length} items');
        _logger.i('=== END: NotificationService.getNotificationsWithFilters ===');
        return notifications;
      } else {
        _logger.e('Invalid response format - expected List but got ${response.data.runtimeType}');
        throw NotificationException(
          message: 'Invalid response format for filtered notifications',
          type: NotificationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching filtered notifications: ${e.message}');
      throw NotificationException(
        message: 'Không thể lấy thông báo với bộ lọc: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching filtered notifications: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi lấy thông báo với bộ lọc',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Đánh dấu thông báo đã đọc
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      _logger.i('=== START: NotificationService.markNotificationAsRead ===');
      _logger.i('Notification ID: $notificationId');
      _logger.i('API endpoint: $_markAsReadEndpoint');

      final Map<String, dynamic> params = {
        'p_notification_id': notificationId,
      };

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _markAsReadEndpoint,
        data: params,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data: ${response.data}');

      // Response là boolean
      final bool result = response.data == true;
      
      if (result) {
        _invalidateCache(); // Invalidate cache khi có thay đổi
        _logger.i('Notification marked as read successfully');
      } else {
        _logger.i('Notification was already read or not found');
      }
      
      _logger.i('=== END: NotificationService.markNotificationAsRead ===');
      return result;
    } on ApiException catch (e) {
      _logger.e('API error when marking notification as read: ${e.message}');
      throw NotificationException(
        message: 'Không thể đánh dấu thông báo đã đọc: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when marking notification as read: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi đánh dấu thông báo đã đọc',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Đánh dấu thông báo đã click
  Future<bool> markNotificationAsClicked(String notificationId) async {
    try {
      _logger.i('=== START: NotificationService.markNotificationAsClicked ===');
      _logger.i('Notification ID: $notificationId');
      _logger.i('API endpoint: $_markAsClickedEndpoint');

      final Map<String, dynamic> params = {
        'p_notification_id': notificationId,
      };

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _markAsClickedEndpoint,
        data: params,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data: ${response.data}');

      // Response là boolean
      final bool result = response.data == true;
      
      if (result) {
        _invalidateCache(); // Invalidate cache khi có thay đổi
        _logger.i('Notification marked as clicked successfully');
      } else {
        _logger.i('Notification was already clicked or not found');
      }
      
      _logger.i('=== END: NotificationService.markNotificationAsClicked ===');
      return result;
    } on ApiException catch (e) {
      _logger.e('API error when marking notification as clicked: ${e.message}');
      throw NotificationException(
        message: 'Không thể đánh dấu thông báo đã click: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when marking notification as clicked: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi đánh dấu thông báo đã click',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Đánh dấu tất cả thông báo đã đọc
  Future<int> markAllNotificationsAsRead({NotificationTypeApi? notificationType}) async {
    try {
      _logger.i('=== START: NotificationService.markAllNotificationsAsRead ===');
      _logger.i('Notification type: $notificationType');
      _logger.i('API endpoint: $_markAllAsReadEndpoint');

      final Map<String, dynamic> params = {};
      if (notificationType != null) params['p_notification_type'] = notificationType.name.toUpperCase();

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _markAllAsReadEndpoint,
        data: params.isNotEmpty ? params : null,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data: ${response.data}');

      // Response là integer (số lượng thông báo đã được đánh dấu)
      final int result = (response.data as num).toInt();
      
      if (result > 0) {
        _invalidateCache(); // Invalidate cache khi có thay đổi
        _logger.i('$result notifications marked as read successfully');
      } else {
        _logger.i('No notifications were marked as read');
      }
      
      _logger.i('=== END: NotificationService.markAllNotificationsAsRead ===');
      return result;
    } on ApiException catch (e) {
      _logger.e('API error when marking all notifications as read: ${e.message}');
      throw NotificationException(
        message: 'Không thể đánh dấu tất cả thông báo đã đọc: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when marking all notifications as read: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi đánh dấu tất cả thông báo đã đọc',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy thông báo mới từ thời điểm cuối cùng check
  Future<List<NotificationDetail>> getNewNotifications(DateTime lastCheckTime) async {
    try {
      _logger.i('=== START: NotificationService.getNewNotifications ===');
      _logger.i('Last check time: $lastCheckTime');
      _logger.i('API endpoint: $_getNewEndpoint');

      final Map<String, dynamic> params = {
        'p_last_check_time': lastCheckTime.toIso8601String(),
      };

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _getNewEndpoint,
        data: params,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response là array
      if (response.data is List) {
        final List<dynamic> dataList = response.data as List;
        _logger.i('Data list length: ${dataList.length}');
        
        final notifications = dataList.map((item) => NotificationDetail.fromJson(item)).toList();
        
        _logger.i('New notifications parsed successfully: ${notifications.length} items');
        _logger.i('=== END: NotificationService.getNewNotifications ===');
        return notifications;
      } else {
        _logger.e('Invalid response format - expected List but got ${response.data.runtimeType}');
        throw NotificationException(
          message: 'Invalid response format for new notifications',
          type: NotificationExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when fetching new notifications: ${e.message}');
      throw NotificationException(
        message: 'Không thể lấy thông báo mới: ${e.message}',
        type: NotificationExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching new notifications: $e');
      throw NotificationException(
        message: 'Lỗi không xác định khi lấy thông báo mới',
        type: NotificationExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy cache summary nếu có
  NotificationSummary? getCachedSummary() => _summaryCache;

  /// Lấy cache categories nếu có
  List<NotificationCategory>? getCachedCategories() => _categoriesCache;

  /// Lấy cache recent notifications nếu có
  List<NotificationDetail>? getCachedRecentNotifications() => _recentCache;

  /// Clear cache
  void clearCache() {
    _summaryCache = null;
    _categoriesCache = null;
    _recentCache = null;
    _lastCacheTime = null;
    _logger.i('Notification cache cleared');
  }

  /// Refresh tất cả notification data
  Future<void> refreshAllNotificationData() async {
    try {
      await Future.wait([
        getNotificationSummary(),
        getNotificationCategories(),
        getRecentNotifications(),
      ]);
      _logger.i('All notification data refreshed successfully');
    } catch (e) {
      _logger.w('Failed to refresh all notification data: $e');
    }
  }

  /// Kiểm tra tính khả dụng của notification API
  Future<bool> checkNotificationApiAvailability() async {
    try {
      await getNotificationSummary();
      return true;
    } catch (e) {
      _logger.w('Notification API not available: $e');
      return false;
    }
  }

  // Private helper methods

  /// Kiểm tra cache có hợp lệ không
  bool _isCacheValid() {
    if (_lastCacheTime == null) return false;
    
    final now = DateTime.now();
    final difference = now.difference(_lastCacheTime!);
    return difference.inMinutes < _cacheTimeoutMinutes;
  }

  /// Cập nhật thời gian cache
  void _updateCacheTime() {
    _lastCacheTime = DateTime.now();
  }

  /// Invalidate cache
  void _invalidateCache() {
    _lastCacheTime = null;
  }
}

/// Custom exception cho Notification service
class NotificationException implements Exception {
  final String message;
  final NotificationExceptionType type;
  final Object? originalException;

  const NotificationException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'NotificationException: $message (Type: $type)';
}

/// Loại lỗi Notification
enum NotificationExceptionType {
  notFound,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
}
