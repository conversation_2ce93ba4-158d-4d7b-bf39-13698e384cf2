import 'package:flutter/foundation.dart';

/// Model cho tóm tắt thống kê thông báo
/// Response từ API: get_notification_summary()
@immutable
class NotificationSummary {
  final int totalNotifications;
  final int unreadCount;
  final int readCount;
  final int todayCount;
  final int importantCount;

  const NotificationSummary({
    required this.totalNotifications,
    required this.unreadCount,
    required this.readCount,
    required this.todayCount,
    required this.importantCount,
  });

  /// Tạo từ JSON response
  factory NotificationSummary.fromJson(Map<String, dynamic> json) {
    return NotificationSummary(
      totalNotifications: (json['total_notifications'] as num?)?.toInt() ?? 0,
      unreadCount: (json['unread_count'] as num?)?.toInt() ?? 0,
      readCount: (json['read_count'] as num?)?.toInt() ?? 0,
      todayCount: (json['today_count'] as num?)?.toInt() ?? 0,
      importantCount: (json['important_count'] as num?)?.toInt() ?? 0,
    );
  }

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'total_notifications': totalNotifications,
      'unread_count': unreadCount,
      'read_count': readCount,
      'today_count': todayCount,
      'important_count': importantCount,
    };
  }

  /// Copy with method
  NotificationSummary copyWith({
    int? totalNotifications,
    int? unreadCount,
    int? readCount,
    int? todayCount,
    int? importantCount,
  }) {
    return NotificationSummary(
      totalNotifications: totalNotifications ?? this.totalNotifications,
      unreadCount: unreadCount ?? this.unreadCount,
      readCount: readCount ?? this.readCount,
      todayCount: todayCount ?? this.todayCount,
      importantCount: importantCount ?? this.importantCount,
    );
  }

  /// Tỷ lệ đã đọc (0.0 - 1.0)
  double get readPercentage {
    if (totalNotifications == 0) return 0.0;
    return readCount / totalNotifications;
  }

  /// Tỷ lệ chưa đọc (0.0 - 1.0)
  double get unreadPercentage {
    if (totalNotifications == 0) return 0.0;
    return unreadCount / totalNotifications;
  }

  /// Có thông báo chưa đọc không
  bool get hasUnreadNotifications => unreadCount > 0;

  /// Có thông báo quan trọng không
  bool get hasImportantNotifications => importantCount > 0;

  /// Có thông báo mới hôm nay không
  bool get hasTodayNotifications => todayCount > 0;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationSummary &&
        other.totalNotifications == totalNotifications &&
        other.unreadCount == unreadCount &&
        other.readCount == readCount &&
        other.todayCount == todayCount &&
        other.importantCount == importantCount;
  }

  @override
  int get hashCode {
    return Object.hash(
      totalNotifications,
      unreadCount,
      readCount,
      todayCount,
      importantCount,
    );
  }

  @override
  String toString() {
    return 'NotificationSummary('
        'total: $totalNotifications, '
        'unread: $unreadCount, '
        'read: $readCount, '
        'today: $todayCount, '
        'important: $importantCount'
        ')';
  }
}