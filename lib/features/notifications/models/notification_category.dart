import 'package:flutter/foundation.dart';
import '../utils/notification_utils.dart';
import 'package:kiloba_biz/features/notifications/models/notification_type.dart';

/// Model cho thống kê thông báo theo category
/// Response từ API: get_notification_categories()
@immutable
class NotificationCategory {
  final NotificationTypeApi notificationType;
  final int count;
  final int unreadCount;

  const NotificationCategory({
    required this.notificationType,
    required this.count,
    required this.unreadCount,
  });

  /// Tạo từ JSON response
  factory NotificationCategory.fromJson(Map<String, dynamic> json) {
    return NotificationCategory(
      notificationType: NotificationUtils.parseNotificationType(json['notification_type'] as String?),
      count: (json['count'] as num?)?.toInt() ?? 0,
      unreadCount: (json['unread_count'] as num?)?.toInt() ?? 0,
    );
  }

  /// <PERSON>yển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'notification_type': notificationType,
      'count': count,
      'unread_count': unreadCount,
    };
  }

  /// Copy with method
  NotificationCategory copyWith({
    NotificationTypeApi? notificationType,
    int? count,
    int? unreadCount,
  }) {
    return NotificationCategory(
      notificationType: notificationType ?? this.notificationType,
      count: count ?? this.count,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }

  /// Số lượng đã đọc
  int get readCount => count - unreadCount;

  /// Tỷ lệ đã đọc (0.0 - 1.0)
  double get readPercentage {
    if (count == 0) return 0.0;
    return readCount / count;
  }

  /// Tỷ lệ chưa đọc (0.0 - 1.0)  
  double get unreadPercentage {
    if (count == 0) return 0.0;
    return unreadCount / count;
  }

  /// Có thông báo chưa đọc không
  bool get hasUnreadNotifications => unreadCount > 0;

  /// Tên hiển thị category
  String get displayName => NotificationUtils.getNotificationTypeDisplayName(notificationType);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationCategory &&
        other.notificationType == notificationType &&
        other.count == count &&
        other.unreadCount == unreadCount;
  }

  @override
  int get hashCode {
    return Object.hash(notificationType, count, unreadCount);
  }

  @override
  String toString() {
    return 'NotificationCategory('
        'notificationType: $notificationType, '
        'count: $count, '
        'unread: $unreadCount'
        ')';
  }
}