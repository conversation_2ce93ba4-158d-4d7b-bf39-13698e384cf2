import 'package:flutter/foundation.dart';
import 'package:kiloba_biz/features/notifications/models/notification_priority.dart';
import 'package:kiloba_biz/features/notifications/models/notification_type.dart';
import '../utils/notification_utils.dart';

/// Model cho thông tin chi tiết thông báo
/// Response từ API: get_recent_notifications(), get_notifications_with_filters(), get_new_notifications()
@immutable
class NotificationDetail {
  final String notificationId;
  final String title;
  final String message;
  final String? shortMessage;
  final NotificationTypeApi notificationType;
  final NotificationPriorityApi priority;
  final bool isImportant;
  final bool isActionable;
  final String? actionUrl;
  final String? actionText;
  final String? imageUrl;
  final String? senderName;
  final DateTime createdAt;
  final DateTime? sentAt;
  final DateTime? readAt;
  final DateTime? clickedAt;
  final bool isRead;
  final bool isClicked;
  final int? totalCount; // Từ get_notifications_with_filters

  const NotificationDetail({
    required this.notificationId,
    required this.title,
    required this.message,
    this.shortMessage,
    required this.notificationType,
    required this.priority,
    required this.isImportant,
    required this.isActionable,
    this.actionUrl,
    this.actionText,
    this.imageUrl,
    this.senderName,
    required this.createdAt,
    this.sentAt,
    this.readAt,
    this.clickedAt,
    required this.isRead,
    required this.isClicked,
    this.totalCount,
  });

  /// Tạo từ JSON response
  factory NotificationDetail.fromJson(Map<String, dynamic> json) {
    return NotificationDetail(
      notificationId: json['notification_id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      message: json['message'] as String? ?? '',
      shortMessage: json['short_message'] as String?,
      notificationType: NotificationUtils.parseNotificationType(json['notification_type'] as String?),
      priority: NotificationUtils.parseNotificationPriority(json['priority'] as String?),
      isImportant: json['is_important'] as bool? ?? false,
      isActionable: json['is_actionable'] as bool? ?? false,
      actionUrl: json['action_url'] as String?,
      actionText: json['action_text'] as String?,
      imageUrl: json['image_url'] as String?,
      senderName: json['sender_name'] as String?,
      createdAt: NotificationUtils.parseDateTime(json['created_at']) ?? DateTime.now(),
      sentAt: NotificationUtils.parseDateTime(json['sent_at']),
      readAt: NotificationUtils.parseDateTime(json['read_at']),
      clickedAt: NotificationUtils.parseDateTime(json['clicked_at']),
      isRead: json['is_read'] as bool? ?? false,
      isClicked: json['is_clicked'] as bool? ?? false,
      totalCount: (json['total_count'] as num?)?.toInt(),
    );
  }



  /// Chuyển thành JSON
  Map<String, dynamic> toJson() {
    return {
      'notification_id': notificationId,
      'title': title,
      'message': message,
      'short_message': shortMessage,
      'notification_type': notificationType.name.toUpperCase(),
      'priority': priority.name.toUpperCase(),
      'is_important': isImportant,
      'is_actionable': isActionable,
      'action_url': actionUrl,
      'action_text': actionText,
      'image_url': imageUrl,
      'sender_name': senderName,
      'created_at': createdAt.toIso8601String(),
      'sent_at': sentAt?.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
      'clicked_at': clickedAt?.toIso8601String(),
      'is_read': isRead,
      'is_clicked': isClicked,
      'total_count': totalCount,
    };
  }

  /// Copy with method
  NotificationDetail copyWith({
    String? notificationId,
    String? title,
    String? message,
    String? shortMessage,
    NotificationTypeApi? notificationType,
    NotificationPriorityApi? priority,
    bool? isImportant,
    bool? isActionable,
    String? actionUrl,
    String? actionText,
    String? imageUrl,
    String? senderName,
    DateTime? createdAt,
    DateTime? sentAt,
    DateTime? readAt,
    DateTime? clickedAt,
    bool? isRead,
    bool? isClicked,
    int? totalCount,
  }) {
    return NotificationDetail(
      notificationId: notificationId ?? this.notificationId,
      title: title ?? this.title,
      message: message ?? this.message,
      shortMessage: shortMessage ?? this.shortMessage,
      notificationType: notificationType ?? this.notificationType,
      priority: priority ?? this.priority,
      isImportant: isImportant ?? this.isImportant,
      isActionable: isActionable ?? this.isActionable,
      actionUrl: actionUrl ?? this.actionUrl,
      actionText: actionText ?? this.actionText,
      imageUrl: imageUrl ?? this.imageUrl,
      senderName: senderName ?? this.senderName,
      createdAt: createdAt ?? this.createdAt,
      sentAt: sentAt ?? this.sentAt,
      readAt: readAt ?? this.readAt,
      clickedAt: clickedAt ?? this.clickedAt,
      isRead: isRead ?? this.isRead,
      isClicked: isClicked ?? this.isClicked,
      totalCount: totalCount ?? this.totalCount,
    );
  }

  /// Đánh dấu đã đọc
  NotificationDetail markAsRead() {
    return copyWith(
      isRead: true,
      readAt: DateTime.now(),
    );
  }

  /// Đánh dấu đã click
  NotificationDetail markAsClicked() {
    return copyWith(
      isClicked: true,
      clickedAt: DateTime.now(),
    );
  }

  /// Kiểm tra thông báo có mới không (trong 24h)
  bool get isNew {
    final now = DateTime.now();
    final diff = now.difference(createdAt);
    return diff.inHours < 24 && !isRead;
  }

  /// Format thời gian tương đối
  String get timeAgo => NotificationUtils.formatTimeAgo(createdAt);

  /// Tên hiển thị cho notification type
  String get typeDisplayName => 
      NotificationUtils.getNotificationTypeDisplayName(notificationType);

  /// Tên hiển thị cho priority
  String get priorityDisplayName => 
      NotificationUtils.getNotificationPriorityDisplayName(priority);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationDetail && other.notificationId == notificationId;
  }

  @override
  int get hashCode => notificationId.hashCode;

  @override
  String toString() {
    return 'NotificationDetail('
        'id: $notificationId, '
        'title: $title, '
        'type: $notificationType, '
        'isRead: $isRead'
        ')';
  }
}