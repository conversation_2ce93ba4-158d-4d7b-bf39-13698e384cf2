import 'package:flutter/foundation.dart';
import 'package:kiloba_biz/features/notifications/models/notification_type.dart';
import 'package:kiloba_biz/features/notifications/utils/notification_utils.dart';

/// Model cho request filter thông báo
/// Dùng cho API: get_notifications_with_filters()
@immutable
class NotificationFilterRequest {
  final NotificationTypeApi? notificationType;
  final bool? isRead;
  final bool? isImportant;
  final int limit;
  final int offset;

  const NotificationFilterRequest({
    this.notificationType,
    this.isRead,
    this.isImportant,
    this.limit = 20,
    this.offset = 0,
  });

  /// Copy with method
  NotificationFilterRequest copyWith({
    NotificationTypeApi? notificationType,
    bool? isRead,
    bool? isImportant,
    int? limit,
    int? offset,
  }) {
    return NotificationFilterRequest(
      notificationType: notificationType ?? this.notificationType,
      isRead: isRead ?? this.isRead,
      isImportant: isImportant ?? this.isImportant,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  /// Clear all filters
  NotificationFilterRequest clear() {
    return const NotificationFilterRequest();
  }

  /// Kiểm tra có filter nào đang active không
  bool get hasActiveFilters {
    return notificationType != null ||
           isRead != null ||
           isImportant != null;
  }

  /// Chuyển thành Map để gửi API
  Map<String, dynamic> toApiParams() {
    final Map<String, dynamic> params = {
      'p_limit': limit,
      'p_offset': offset,
    };

    if (notificationType != null) params['p_notification_type'] = notificationType!.name.toUpperCase();
    if (isRead != null) params['p_is_read'] = isRead;
    if (isImportant != null) params['p_is_important'] = isImportant;

    return params;
  }

  /// Mô tả filter hiện tại
  String get description {
    final parts = <String>[];
    
    if (notificationType != null) {
      parts.add('Loại: ${NotificationUtils.getNotificationTypeDisplayName(notificationType!)}');
    }
    
    if (isRead != null) {
      parts.add(isRead! ? 'Đã đọc' : 'Chưa đọc');
    }
    
    if (isImportant != null && isImportant!) {
      parts.add('Quan trọng');
    }
    
    return parts.isEmpty ? 'Tất cả thông báo' : parts.join(' • ');
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationFilterRequest &&
        other.notificationType == notificationType &&
        other.isRead == isRead &&
        other.isImportant == isImportant &&
        other.limit == limit &&
        other.offset == offset;
  }

  @override
  int get hashCode {
    return Object.hash(
      notificationType,
      isRead,
      isImportant,
      limit,
      offset,
    );
  }

  @override
  String toString() {
    return 'NotificationFilterRequest('
        'type: $notificationType, '
        'isRead: $isRead, '
        'isImportant: $isImportant, '
        'limit: $limit, '
        'offset: $offset'
        ')';
  }
}