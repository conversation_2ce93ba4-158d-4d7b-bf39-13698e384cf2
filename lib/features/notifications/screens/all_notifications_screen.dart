import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:shimmer/shimmer.dart';

import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../blocs/notification_bloc.dart';
import '../blocs/notification_state.dart';
import '../blocs/notification_event.dart';
import '../models/notification_detail.dart';
import '../models/notification_filter_request.dart';
import '../widgets/notification_summary_card.dart';
import '../widgets/notification_list_item.dart';
import '../widgets/notification_filter_bottom_sheet.dart';
import '../widgets/notification_detail_helper.dart';

// 1. Thêm enum cho filter nhanh
enum NotificationQuickFilter {
  all,
  unread,
  important,
  today,
  thisWeek,
  thisMonth,
}

/// <PERSON><PERSON><PERSON> hình hiển thị tất cả thông báo với pagination và filtering
class AllNotificationsScreen extends StatefulWidget {
  const AllNotificationsScreen({super.key});

  @override
  State<AllNotificationsScreen> createState() => _AllNotificationsScreenState();
}

class _AllNotificationsScreenState extends State<AllNotificationsScreen> {
  final ScrollController _scrollController = ScrollController();
  NotificationFilterRequest _currentFilter = const NotificationFilterRequest();
  NotificationQuickFilter _selectedQuickFilter = NotificationQuickFilter.all;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationBloc>().add(LoadAllNotifications(_currentFilter));
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreNotifications();
    }
  }

  void _loadMoreNotifications() {
    final state = context.read<NotificationBloc>().state;
    if (state is NotificationLoaded && 
        state.hasMoreData && 
        !state.isLoadingMore && 
        !_isLoadingMore) {
      setState(() => _isLoadingMore = true);
      context.read<NotificationBloc>().add(LoadMoreNotifications(_currentFilter));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppNavHeaderExtension.forTab(
        title: 'Tất cả thông báo',
        actions: [
          // Filter Icon with Badge
          BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, state) {
              final hasActiveFilter = _currentFilter.hasActiveFilters;

              return Container(
                margin: const EdgeInsets.only(right: AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Stack(
                  children: [
                    IconButton(
                      icon: Icon(
                        TablerIcons.filter,
                        color: Colors.white,
                        size: AppDimensions.iconM,
                      ),
                      onPressed: () => _showFilterBottomSheet(context, state),
                    ),
                    if (hasActiveFilter)
                      Positioned(
                        right: 4,
                        top: 4,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: AppColors.kienlongOrange,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
          // Mark All As Read Icon
          BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, state) {
              final hasUnreadNotifications = state is NotificationLoaded && 
                  state.summary?.hasUnreadNotifications == true;
              
              return Container(
                margin: const EdgeInsets.only(right: AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Stack(
                  children: [
                    IconButton(
                      icon: Icon(
                        TablerIcons.eye_check,
                        color: hasUnreadNotifications 
                            ? Colors.white 
                            : Colors.white.withValues(alpha: 0.4),
                        size: AppDimensions.iconM,
                      ),
                      onPressed: hasUnreadNotifications 
                          ? () => _handleMarkAllAsRead(context)
                          : null,
                      tooltip: 'Đánh dấu tất cả đã đọc',
                    ),
                    if (hasUnreadNotifications)
                      Positioned(
                        right: 4,
                        top: 4,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: AppColors.kienlongOrange,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: BlocConsumer<NotificationBloc, NotificationState>(
        listener: (context, state) {
          if (state is NotificationLoaded) {
            setState(() => _isLoadingMore = false);
          }
          // Cập nhật state khi có thay đổi
          if (state is NotificationActionSuccess) {
            // Refresh lại data nếu cần
            if (state.actionType == 'mark_as_read' || state.actionType == 'mark_as_clicked') {
              // Không cần refresh vì state đã được cập nhật
            }
          }
        },
        builder: (context, state) {
          return RefreshIndicator(
            onRefresh: () async {
              context.read<NotificationBloc>().add(LoadAllNotifications(_currentFilter));
              await Future.delayed(const Duration(milliseconds: 500));
            },
            child: _buildBody(context, state),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, NotificationState state) {
    if (state is NotificationError) {
      return _buildErrorState(context, state);
    }

    if (state is NotificationLoading) {
      return _buildLoadingState(context);
    }

    if (state is NotificationLoaded) {
      return _buildContent(context, state);
    }

    return _buildLoadingState(context);
  }

  Widget _buildContent(BuildContext context, NotificationLoaded state) {
    // Áp dụng filter local nếu là filter theo ngày/tuần/tháng
    List<NotificationDetail> filteredNotifications = state.allNotifications;
    if (_selectedQuickFilter == NotificationQuickFilter.today) {
      final now = DateTime.now();
      filteredNotifications = filteredNotifications.where((n) {
        final created = n.createdAt;
        return created.year == now.year && created.month == now.month && created.day == now.day;
      }).toList();
    } else if (_selectedQuickFilter == NotificationQuickFilter.thisWeek) {
      final now = DateTime.now();
      final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
      final endOfWeek = startOfWeek.add(const Duration(days: 6));
      filteredNotifications = filteredNotifications.where((n) {
        final created = n.createdAt;
        return !created.isBefore(startOfWeek) && !created.isAfter(endOfWeek);
      }).toList();
    } else if (_selectedQuickFilter == NotificationQuickFilter.thisMonth) {
      final now = DateTime.now();
      filteredNotifications = filteredNotifications.where((n) {
        final created = n.createdAt;
        return created.year == now.year && created.month == now.month;
      }).toList();
    }
    // Các filter còn lại đã filter từ API
    if (_selectedQuickFilter == NotificationQuickFilter.unread) {
      filteredNotifications = filteredNotifications.where((n) => !n.isRead).toList();
    } else if (_selectedQuickFilter == NotificationQuickFilter.important) {
      filteredNotifications = filteredNotifications.where((n) => n.isImportant).toList();
    }

    final groupedNotifications = _groupNotificationsByDate(filteredNotifications);
    final hasMoreData = state.hasMoreData;

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        // Summary Card
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: NotificationSummaryCard(
              summary: state.summary,
              isLoading: state.isRefreshing,
              onTap: () {},
            ),
          ),
        ),
        // Quick Filter Chips
        SliverToBoxAdapter(
          child: _buildQuickFilterChips(context),
        ),
        // Notifications List
        if (groupedNotifications.isEmpty)
          SliverFillRemaining(
            child: _buildEmptyState(context),
          )
        else
          ...groupedNotifications.entries.map((entry) {
            return SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index == 0) {
                    return _buildDateHeader(entry.key, context);
                  }
                  final notification = entry.value[index - 1];
                  return NotificationListItem(
                    notification: notification,
                    onTap: () => _showNotificationDetail(notification), onMarkAsRead: () {  }, onDelete: () {  },
                  );
                },
                childCount: entry.value.length + 1,
              ),
            );
          }),
        // Load More Indicator
        if (hasMoreData)
          SliverToBoxAdapter(
            child: _buildLoadMoreIndicator(context),
          ),
      ],
    );
  }

  Widget _buildQuickFilterChips(BuildContext context) {
    final filters = [
      {'label': 'Tất cả', 'value': NotificationQuickFilter.all},
      {'label': 'Chưa đọc', 'value': NotificationQuickFilter.unread},
      {'label': 'Quan trọng', 'value': NotificationQuickFilter.important},
      {'label': 'Hôm nay', 'value': NotificationQuickFilter.today},
      {'label': 'Tuần này', 'value': NotificationQuickFilter.thisWeek},
      {'label': 'Tháng này', 'value': NotificationQuickFilter.thisMonth},
    ];
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    return SizedBox(
      height: 50,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
        itemCount: filters.length,
        separatorBuilder: (_, _) => const SizedBox(width: AppDimensions.spacingS),
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedQuickFilter == filter['value'];
          return FilterChip(
            label: Text(
              filter['label'] as String,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? Colors.white
                    : isDarkMode
                        ? AppColors.textOnPrimary
                        : AppColors.textPrimary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
            selected: isSelected,
            onSelected: (_) => _onQuickFilterSelected(filter['value'] as NotificationQuickFilter),
            backgroundColor: isDarkMode
                ? AppColors.backgroundDarkSecondary
                : AppColors.backgroundSecondary,
            selectedColor: AppColors.kienlongOrange,
            checkmarkColor: Colors.white,
            side: BorderSide(
              color: isSelected
                  ? AppColors.kienlongOrange
                  : isDarkMode
                      ? AppColors.borderDark.withValues(alpha: 0.3)
                      : AppColors.borderLight,
              width: 1,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDateHeader(String date, BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      child: Text(
        date,
        style: AppTypography.textTheme.titleSmall?.copyWith(
          color: isDarkMode 
              ? AppColors.textOnPrimary.withValues(alpha: 0.7)
              : AppColors.textSecondary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildLoadMoreIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Center(
        child: Column(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  isDarkMode 
                      ? AppColors.textOnPrimary
                      : AppColors.kienlongOrange,
                ),
              ),
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Đang tải thêm...',
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: isDarkMode 
                    ? AppColors.textOnPrimary.withValues(alpha: 0.7)
                    : AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            TablerIcons.bell_off,
            size: 64,
            color: isDarkMode 
                ? AppColors.textOnPrimary.withValues(alpha: 0.3)
                : AppColors.textTertiary,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            'Không có thông báo nào',
            style: AppTypography.textTheme.titleMedium?.copyWith(
              color: isDarkMode 
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            'Bạn đã xem tất cả thông báo hoặc chưa có thông báo mới',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode 
                  ? AppColors.textOnPrimary.withValues(alpha: 0.6)
                  : AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
          height: 72,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Shimmer.fromColors(
            baseColor: Theme.of(context).brightness == Brightness.dark
                ? AppColors.backgroundDarkSecondary
                : AppColors.neutral200,
            highlightColor: Theme.of(context).brightness == Brightness.dark
                ? AppColors.backgroundDarkTertiary
                : AppColors.neutral100,
            child: Container(
              margin: const EdgeInsets.all(AppDimensions.paddingM),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 16,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          height: 12,
                          width: 200,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(BuildContext context, NotificationError state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            TablerIcons.alert_circle,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          Text(
            'Có lỗi xảy ra',
            style: AppTypography.textTheme.titleMedium?.copyWith(
              color: AppColors.error,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            state.message,
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.spacingL),
          ElevatedButton(
            onPressed: () {
              context.read<NotificationBloc>().add(LoadAllNotifications(_currentFilter));
            },
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  void _onQuickFilterSelected(NotificationQuickFilter filter) {
    setState(() {
      _selectedQuickFilter = filter;
      // Chỉ gọi API nếu là filter all, unread, important
      if (filter == NotificationQuickFilter.all) {
        _currentFilter = const NotificationFilterRequest();
        context.read<NotificationBloc>().add(LoadAllNotifications(_currentFilter));
      } else if (filter == NotificationQuickFilter.unread) {
        _currentFilter = const NotificationFilterRequest(isRead: false);
        context.read<NotificationBloc>().add(LoadAllNotifications(_currentFilter));
      } else if (filter == NotificationQuickFilter.important) {
        _currentFilter = const NotificationFilterRequest(isImportant: true);
        context.read<NotificationBloc>().add(LoadAllNotifications(_currentFilter));
      }
      // Các filter còn lại filter local, không gọi API
    });
  }

  void _showFilterBottomSheet(BuildContext context, NotificationState state) {
    final notificationBloc = context.read<NotificationBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider.value(
        value: notificationBloc,
        child: NotificationFilterBottomSheet(
          categories: state is NotificationLoaded ? state.categories : [],
          selectedNotificationType: _currentFilter.notificationType,
          isLoading: state is NotificationLoading ||
              (state is NotificationLoaded && state.isRefreshing),
        ),
      ),
    );
  }

  void _showNotificationDetail(NotificationDetail notification) {
    // Tự động đánh dấu đã đọc nếu chưa đọc
    if (!notification.isRead) {
      context.read<NotificationBloc>().add(
        MarkNotificationAsRead(notification.notificationId),
      );
    }
    
    NotificationDetailHelper.showNotificationDetail(context, notification);
  }

  void _handleMarkAllAsRead(BuildContext context) async {
    final confirmed = await showConfirmationDialog(
      context,
      title: 'Đánh dấu tất cả đã đọc',
      content: 'Bạn có chắc chắn muốn đánh dấu tất cả thông báo là đã đọc?',
      confirmText: 'Đánh dấu',
      cancelText: 'Hủy',
      icon: TablerIcons.check,
      iconColor: AppColors.success,
      confirmButtonColor: AppColors.success,
    );

    if (confirmed == true) {
      if (context.mounted) {
        context.read<NotificationBloc>().add(
            MarkAllNotificationsAsRead(notificationType: null),
          );
      }
    }
  }

  Map<String, List<NotificationDetail>> _groupNotificationsByDate(
    List<NotificationDetail> notifications,
  ) {
    final grouped = <String, List<NotificationDetail>>{};
    
    for (final notification in notifications) {
      final dateKey = _formatDate(notification.createdAt);
      grouped.putIfAbsent(dateKey, () => []).add(notification);
    }
    
    return grouped;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final notificationDate = DateTime(date.year, date.month, date.day);
    
    if (notificationDate == today) {
      return 'Hôm nay';
    } else if (notificationDate == yesterday) {
      return 'Hôm qua';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
} 