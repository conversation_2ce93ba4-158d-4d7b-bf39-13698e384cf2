import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';

import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../blocs/notification_bloc.dart';
import '../blocs/notification_state.dart';
import '../blocs/notification_event.dart';
import '../widgets/notification_summary_card.dart';
import '../widgets/notification_list_item.dart';
import '../widgets/notification_filter_bottom_sheet.dart';
import '../widgets/notification_detail_helper.dart';
import 'all_notifications_screen.dart';

/// Notification tab với Bloc pattern và components
class NotificationsTab extends StatelessWidget {
  const NotificationsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NotificationBloc()..add(LoadNotificationData()),
      child: const _NotificationsTabContent(),
    );
  }
}

class _NotificationsTabContent extends StatelessWidget {
  const _NotificationsTabContent();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppNavHeaderExtension.forTab(
        title: 'Thông báo',
        actions: [
          // Filter Icon with Badge
          BlocBuilder<NotificationBloc, NotificationState>(
            builder: (context, state) {
              final hasActiveFilter =
                  state is NotificationLoaded &&
                  state.selectedNotificationType != null;

              return Container(
                margin: const EdgeInsets.only(right: AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Stack(
                  children: [
                    IconButton(
                      icon: Icon(
                        TablerIcons.filter,
                        color: Colors.white,
                        size: AppDimensions.iconM,
                      ),
                      onPressed: () => _showFilterBottomSheet(context, state),
                    ),
                    if (hasActiveFilter)
                      Positioned(
                        right: 4,
                        top: 4,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: AppColors.kienlongOrange,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: BlocBuilder<NotificationBloc, NotificationState>(
        builder: (context, state) {
          debugPrint('🔍 UI: State changed to ${state.runtimeType}');
          if (state is NotificationLoaded) {
            debugPrint('🔍 UI: NotificationLoaded with ${state.recentNotifications.length} notifications');
          }
          return RefreshIndicator(
            onRefresh: () async {
              context.read<NotificationBloc>().add(RefreshNotificationData());
              // Wait for refresh to complete
              await Future.delayed(const Duration(milliseconds: 500));
            },
            child: _buildBody(context, state),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, NotificationState state) {
    if (state is NotificationError) {
      return _buildErrorState(context, state);
    }

    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Summary Card
                _buildSummarySection(context, state),
                const SizedBox(height: AppDimensions.spacingM),

                // Recent Notifications Header
                _buildRecentNotificationsHeader(context, state)
              ],
            ),
          ),
        ),

        // Recent Notifications List
        _buildRecentNotificationsList(context, state),
      ],
    );
  }

  Widget _buildSummarySection(BuildContext context, NotificationState state) {
    if (state is NotificationLoaded) {
      return NotificationSummaryCard(
        summary: state.summary,
        isLoading: state.isRefreshing,
        onTap: () {
          // Navigate to detailed summary
        },
      );
    }

    return const NotificationSummaryCard(isLoading: true);
  }

  /// Hiển thị bottom sheet filter chuyên nghiệp
  void _showFilterBottomSheet(BuildContext context, NotificationState state) {
    final notificationBloc = context.read<NotificationBloc>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider.value(
        value: notificationBloc,
        child: NotificationFilterBottomSheet(
          categories: state is NotificationLoaded ? state.categories : [],
          selectedNotificationType: state is NotificationLoaded
              ? state.selectedNotificationType
              : null,
          isLoading:
              state is NotificationLoading ||
              (state is NotificationLoaded && state.isRefreshing),
        ),
      ),
    );
  }

  Widget _buildRecentNotificationsHeader(
    BuildContext context,
    NotificationState state,
  ) {
    if (state is NotificationLoaded) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Thông báo gần đây',
            style: AppTypography.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          if (state.recentNotifications.isNotEmpty)
            TextButton(
              onPressed: () {
                final notificationBloc = context.read<NotificationBloc>();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => BlocProvider.value(
                      value: notificationBloc,
                      child: const AllNotificationsScreen(),
                    ),
                  ),
                );
              },
              child: Text(
                'Xem tất cả',
                style: TextStyle(color: AppColors.kienlongOrange),
              ),
            ),
        ],
      );
    }

    return Text(
      'Thông báo gần đây',
      style: AppTypography.textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildRecentNotificationsList(
    BuildContext context,
    NotificationState state,
  ) {
    if (state is NotificationLoaded) {
      if (state.recentNotifications.isEmpty) {
        return SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
            ),
            child: _buildEmptyNotifications(),
          ),
        );
      }

      return SliverPadding(
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
        sliver: SliverList.builder(
          itemCount: state.recentNotifications.length,
          itemBuilder: (context, index) {
            final notification = state.recentNotifications[index];
            return NotificationListItem(
              notification: notification,
              onTap: () =>
                  _handleNotificationTap(context, notification.notificationId), onMarkAsRead: () {  }, onDelete: () {  },
              // Bỏ onMarkAsRead và onDelete vì đã có auto mark as read và swipe to delete
            );
          },
        ),
      );
    }

          return SliverPadding(
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
        sliver: SliverList.builder(
          itemCount: 3,
          itemBuilder: (context, index) => _buildLoadingNotificationItem(context),
        ),
      );
  }

  Widget _buildLoadingNotificationItem(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: isDarkMode 
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isDarkMode 
                  ? AppColors.backgroundDarkTertiary
                  : AppColors.neutral200,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 16,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: isDarkMode 
                        ? AppColors.backgroundDarkTertiary
                        : AppColors.neutral200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 14,
                  width: 200,
                  decoration: BoxDecoration(
                    color: isDarkMode 
                        ? AppColors.backgroundDarkTertiary
                        : AppColors.neutral200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  height: 12,
                  width: 80,
                  decoration: BoxDecoration(
                    color: isDarkMode 
                        ? AppColors.backgroundDarkTertiary
                        : AppColors.neutral200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyNotifications() {
    return Builder(
      builder: (context) => Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppDimensions.paddingXL),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          children: [
            Icon(
              TablerIcons.bell_off,
              size: 48,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            Text(
              'Không có thông báo nào',
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Thông báo mới sẽ xuất hiện ở đây',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.4),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, NotificationError state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(TablerIcons.alert_triangle, size: 64, color: AppColors.error),
            const SizedBox(height: AppDimensions.spacingL),
            Text(
              'Có lỗi xảy ra',
              style: AppTypography.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            Text(
              state.message,
              style: AppTypography.textTheme.bodyLarge?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                context.read<NotificationBloc>().add(RetryLoadNotifications());
              },
              icon: const Icon(TablerIcons.refresh),
              label: const Text('Thử lại'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleNotificationTap(BuildContext context, String notificationId) {
    // Tìm notification từ state
    final state = context.read<NotificationBloc>().state;
    if (state is NotificationLoaded) {
      final notification = state.recentNotifications.firstWhere(
        (n) => n.notificationId == notificationId,
        orElse: () => state.recentNotifications.first,
      );
      
      // Hiển thị bottom sheet thay vì navigation
      NotificationDetailHelper.showNotificationDetail(context, notification);
    }
  }
}
