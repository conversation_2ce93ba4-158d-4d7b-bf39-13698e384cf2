import 'dart:async';
import 'package:intl/intl.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:shimmer/shimmer.dart';
import '../../../shared/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';
import '../blocs/index.dart';
import '../models/proposal_list.dart';
import '../models/get_proposals_request.dart';
import '../models/transaction_models.dart';
import 'transaction_detail_screen.dart';
import 'create_transaction_screen.dart';
// Add imports for filter data
import '../../../features/auth/blocs/master_data_bloc.dart';
import '../../../features/products/blocs/product_bloc.dart';
import '../../../features/products/blocs/product_state.dart';
import '../../../features/products/blocs/product_event.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/employees/models/employee_model.dart';
import '../../../features/employees/blocs/employee_bloc.dart';
import '../../../features/employees/blocs/employee_state.dart';
import '../../../features/employees/blocs/employee_event.dart';
import '../../../shared/models/index.dart';

class TransactionsTab extends StatefulWidget {
  const TransactionsTab({super.key});

  @override
  State<TransactionsTab> createState() => _TransactionsTabState();
}

class _TransactionsTabState extends State<TransactionsTab> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => MasterDataBloc()),
        BlocProvider(create: (context) => ProductBloc()),
        BlocProvider(create: (context) => EmployeeBloc()),
      ],
      child: const _TransactionsTabContent(),
    );
  }
}

class _TransactionsTabContent extends StatefulWidget {
  const _TransactionsTabContent();

  @override
  State<_TransactionsTabContent> createState() =>
      _TransactionsTabContentState();
}

class _TransactionsTabContentState extends State<_TransactionsTabContent>
    with TickerProviderStateMixin {
  int _selectedFilter = 0;
  DateTimeRange? _dateRange;
  ProductModel? _selectedProduct;
  ConfigModel? _selectedStatus;
  List<RegionModel> _selectedRegions = [];
  List<BranchModel> _selectedBranches = [];
  EmployeeModel? _selectedEmployee;
  String _searchText = '';

  // Search debounce timer
  Timer? _searchDebounceTimer;
  static const Duration _searchDebounceDelay = Duration(milliseconds: 500);

  // Scroll controller for infinite scroll
  late ScrollController _scrollController;

  // Flag to prevent duplicate load more calls
  bool _isLoadingMore = false;

  // Debounce timer for scroll events
  Timer? _scrollDebounceTimer;

  // Applied filters (actual filters affecting data and UI)
  DateTimeRange? _appliedDateRange;
  ProductModel? _appliedProduct;
  ConfigModel? _appliedStatus;
  final List<RegionModel> _appliedRegions = [];
  final List<BranchModel> _appliedBranches = [];
  EmployeeModel? _appliedEmployee;

  // Master data
  List<ProductModel> _products = [];
  List<ConfigModel> _statuses = [];
  List<RegionModel> _regions = [];
  List<BranchModel> _branches = [];
  List<EmployeeModel> _employees = [];
  bool _masterDataLoaded = false;
  bool _branchesLoading = false;
  bool _isSearchingEmployees = false;
  String _employeeSearchTerm = '';

  // Stream để notify modal về branches và employees updates
  final StreamController<List<BranchModel>> _branchesStreamController =
      StreamController<List<BranchModel>>.broadcast();
  final StreamController<List<EmployeeModel>> _employeeSearchStreamController =
      StreamController<List<EmployeeModel>>.broadcast();

  @override
  void initState() {
    super.initState();
    _initializeScrollController();
    // Load initial transaction list and filter options when widget is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<TransactionBloc>().add(const LoadTransactionList());
        _loadMasterData();
      }
    });
  }

  void _initializeScrollController() {
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // Debounce scroll events to prevent too many triggers
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      // Check if user scrolled to near the bottom and not already loading
      if (!_isLoadingMore &&
          _scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200) {
        // Trigger load more when user is 200px from bottom
        _onLoadMore();
      }
    });
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _scrollDebounceTimer?.cancel();
    _scrollController.dispose();
    _branchesStreamController.close();
    _employeeSearchStreamController.close();
    super.dispose();
  }

  /// Load master data từ API
  void _loadMasterData() {
    // Load products
    context.read<ProductBloc>().add(LoadProducts());
    // Load general status by default
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent('PROPOSAL_GENERAL_STATUS'),
    );
    // Load regions
    context.read<MasterDataBloc>().add(const LoadRegionsEvent());
    // Load ALL branches ngay từ đầu (không cần đợi chọn regions)
    context.read<MasterDataBloc>().add(const LoadBranchesByRegionsEvent([]));
  }

  /// Search employees real-time - chỉ call API khi search
  void _searchEmployees(String searchTerm) {
    setState(() {
      _employeeSearchTerm = searchTerm;
      _isSearchingEmployees = true;
    });

    // If search term is empty, clear results
    if (searchTerm.isEmpty) {
      setState(() {
        _employees = [];
        _isSearchingEmployees = false;
      });
      _employeeSearchStreamController.add([]);
    } else {
      // Call API với search term
      context.read<EmployeeBloc>().add(SearchEmployees(searchTerm));
    }
  }

  /// Get current employee search results for modal
  List<EmployeeModel> get _currentEmployeeSearchResults =>
      _employeeSearchTerm.isEmpty
      ? _employees
      : _employees
            .where(
              (emp) =>
                  emp.fullName?.toLowerCase().contains(
                        _employeeSearchTerm.toLowerCase(),
                      ) ==
                      true ||
                  emp.username?.toLowerCase().contains(
                        _employeeSearchTerm.toLowerCase(),
                      ) ==
                      true ||
                  emp.email?.toLowerCase().contains(
                        _employeeSearchTerm.toLowerCase(),
                      ) ==
                      true,
            )
            .toList();

  /// Load branches cho các regions đã chọn hoặc load all branches
  void _loadBranchesForRegions(List<RegionModel> regions) {
    final regionIds = regions.map((r) => r.id).toList();

    if (regionIds.isEmpty) {
      // Nếu không có region nào được chọn, load ALL branches
      setState(() {
        _branchesLoading = true;
      });
      // Load all branches (API sẽ trả về tất cả branches)
      context.read<MasterDataBloc>().add(const LoadBranchesByRegionsEvent([]));
      return;
    }
    setState(() {
      _branchesLoading = true;
    });
    context.read<MasterDataBloc>().add(LoadBranchesByRegionsEvent(regionIds));
  }

  /// Load transactions with specific status
  void _loadTransactionsWithStatus(String statusCode) {
    // Skip if statusCode is empty
    if (statusCode.isEmpty) {
      _loadAllTransactions();
      return;
    }

    // Build GetProposalsRequest with status filter using applied filters
    final request = GetProposalsRequest(
      createdFrom: _appliedDateRange?.start,
      createdTo: _appliedDateRange?.end,
      productId: _appliedProduct?.id,
      status: statusCode, // Filter by selected status
      regionIds: _appliedRegions.isNotEmpty
          ? _appliedRegions.map((r) => r.id).toList()
          : null,
      branchIds: _appliedBranches.isNotEmpty
          ? _appliedBranches.map((b) => b.id).toList()
          : null,
      assignedEmployeeIds: _appliedEmployee != null
          ? [_appliedEmployee!.id!]
          : null,
      limit: 20,
      offset: 0,
    );

    // Call TransactionBloc to filter transactions by status
    context.read<TransactionBloc>().add(FilterTransactions(request: request));
  }

  /// Load all transactions (clear status filter)
  void _loadAllTransactions() {
    // Build GetProposalsRequest without status filter using applied filters
    final request = GetProposalsRequest(
      createdFrom: _appliedDateRange?.start,
      createdTo: _appliedDateRange?.end,
      productId: _appliedProduct?.id,
      // No status filter - load all
      regionIds: _appliedRegions.isNotEmpty
          ? _appliedRegions.map((r) => r.id).toList()
          : null,
      branchIds: _appliedBranches.isNotEmpty
          ? _appliedBranches.map((b) => b.id).toList()
          : null,
      assignedEmployeeIds: _appliedEmployee != null
          ? [_appliedEmployee!.id!]
          : null,
      limit: 20,
      offset: 0,
    );

    // Call TransactionBloc to load all transactions
    context.read<TransactionBloc>().add(FilterTransactions(request: request));
  }

  /// Build current filter request based on applied filters
  GetProposalsRequest _buildCurrentFilterRequest() {
    return GetProposalsRequest(
      createdFrom: _appliedDateRange?.start,
      createdTo: _appliedDateRange?.end,
      productId: _appliedProduct?.id,
      status: _appliedStatus?.code, // Use applied status
      regionIds: _appliedRegions.isNotEmpty
          ? _appliedRegions.map((r) => r.id).toList()
          : null,
      branchIds: _appliedBranches.isNotEmpty
          ? _appliedBranches.map((b) => b.id).toList()
          : null,
      assignedEmployeeIds: _appliedEmployee != null
          ? [_appliedEmployee!.id!]
          : null,
      limit: 20,
      offset: 0,
    );
  }

  /// Load statuses based on selected product
  void _loadStatusesForProduct(ProductModel? product) {
    if (product == null) {
      // Load general status if no product selected
      context.read<MasterDataBloc>().add(
        const LoadConfigEvent('PROPOSAL_GENERAL_STATUS'),
      );
    } else if (product.code == 'MANGO') {
      // Load Mango-specific statuses for trả góp product
      context.read<MasterDataBloc>().add(
        const LoadConfigEvent('PROPOSAL_MANGO_STATUS'),
      );
    } else {
      // Load general status for other products
      context.read<MasterDataBloc>().add(
        const LoadConfigEvent('PROPOSAL_GENERAL_STATUS'),
      );
    }
  }

  // Tính toán stats từ mock transactions (fake data cho summary)
  Map<String, dynamic> get _transactionStats {
    return {
      'total': 5,
      'success': 5,
      'pending': 5,
      'error': 5,
      'totalAmount': 5,
    };
  }

  bool get _hasActiveFilters =>
      _appliedDateRange != null ||
      _appliedProduct != null ||
      _appliedStatus != null ||
      _appliedRegions.isNotEmpty ||
      _appliedBranches.isNotEmpty;

  String _formatCurrency(double amount) {
    // Format với dấu phẩy ngăn cách hàng nghìn
    final formatter = NumberFormat('#,##0', 'vi_VN');
    return '${formatter.format(amount)} VND';
  }

  /// Reset preview filters to applied filters (when modal is dismissed without applying)
  void _resetPreviewFilters() {
    setState(() {
      _dateRange = _appliedDateRange;
      _selectedProduct = _appliedProduct;
      _selectedStatus = _appliedStatus;
      _selectedRegions.clear();
      _selectedRegions.addAll(_appliedRegions);
      _selectedBranches.clear();
      _selectedBranches.addAll(_appliedBranches);
      _selectedEmployee = _appliedEmployee;
    });
  }

  void _showModernFilterModal() async {
    // Kiểm tra master data đã load chưa
    if (!_masterDataLoaded) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đang tải dữ liệu...'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Reset loading state nếu có branches rồi
    if (_branches.isNotEmpty && _branchesLoading) {
      setState(() {
        _branchesLoading = false;
      });
    }

    // ALWAYS emit current branches state để modal biết
    _branchesStreamController.add(_branches);

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (modalContext) {
        return DraggableScrollableSheet(
          initialChildSize: 0.8,
          minChildSize: 0.5,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
            return ModernFilterModal(
              initialDateRange: _dateRange ?? _appliedDateRange,
              initialProduct: _selectedProduct ?? _appliedProduct,
              initialStatus: _selectedStatus ?? _appliedStatus,
              initialRegions: _selectedRegions.isNotEmpty
                  ? _selectedRegions
                  : _appliedRegions,
              initialBranches: _selectedBranches.isNotEmpty
                  ? _selectedBranches
                  : _appliedBranches,
              initialEmployee: _selectedEmployee ?? _appliedEmployee,
              availableProducts: _products,
              availableStatuses: _statuses,
              availableRegions: _regions,
              availableBranches: _branches,
              availableEmployees: _currentEmployeeSearchResults,
              isLoadingOptions: false, // Simplified loading logic
              branchesLoading: _branchesLoading,
              isSearchingEmployees: _isSearchingEmployees,
              masterDataBloc: context.read<MasterDataBloc>(),
              externalSelectedStatus: _appliedStatus,
              branchesStream: _branchesStreamController.stream,
              employeeSearchStream: _employeeSearchStreamController.stream,
              onEmployeeSearch: _searchEmployees,
              onRegionsChanged: _loadBranchesForRegions,
              onProductChanged: (product) {
                // Load appropriate statuses when product changes
                _loadStatusesForProduct(product);
              },
              onApply: (dateRange, product, status, regions, branches, employee) {
                setState(() {
                  // Update preview filters (for next modal open)
                  _dateRange = dateRange;
                  _selectedProduct = product;
                  _selectedEmployee = employee;
                  _selectedStatus = status;
                  _selectedRegions = regions ?? [];
                  _selectedBranches = branches ?? [];

                  // Update applied filters (actual filters affecting UI)
                  _appliedDateRange = dateRange;
                  _appliedProduct = product;
                  _appliedEmployee = employee;
                  _appliedStatus = status;
                  _appliedRegions.clear();
                  _appliedRegions.addAll(regions ?? []);
                  _appliedBranches.clear();
                  _appliedBranches.addAll(branches ?? []);

                  // Update _statuses from current MasterDataState to ensure correct statuses
                  final masterDataState = context.read<MasterDataBloc>().state;
                  if (masterDataState is MasterDataLoaded) {
                    _statuses = _getStatusesFromState(masterDataState);
                  }

                  // Update filter bar to reflect the selected status
                  if (status != null) {
                    // Filter out "Tất cả" from _statuses to match TransactionFilterBar logic
                    final filteredStatuses = _statuses.where((s) {
                      final label = s.label ?? s.code;
                      return label != null &&
                          label.isNotEmpty &&
                          label.toLowerCase() != 'tất cả';
                    }).toList();

                    final statusIndex = filteredStatuses.indexWhere(
                      (s) => s.code == status.code,
                    );
                    if (statusIndex >= 0) {
                      _selectedFilter =
                          statusIndex + 1; // +1 because "Tất cả" is at index 0
                    }
                  } else {
                    _selectedFilter = 0; // Reset to "Tất cả"
                  }
                });

                // Apply filter to get transactions
                _applyFilters();
              },
            );
          },
        );
      },
    ).then((_) {
      // Reset preview filters when modal is dismissed without applying
      _resetPreviewFilters();
    });
  }

  /// Extract statuses from MasterDataState - prioritize product-specific statuses
  List<ConfigModel> _getStatusesFromState(MasterDataState state) {
    if (state is MasterDataLoaded) {
      // Prioritize Mango statuses if available and product is Mango
      if (_selectedProduct?.code == 'MANGO' &&
          state.configsByGroup.containsKey('PROPOSAL_MANGO_STATUS') &&
          state.configsByGroup['PROPOSAL_MANGO_STATUS']!.isNotEmpty) {
        return state.configsByGroup['PROPOSAL_MANGO_STATUS']!;
      }

      // Fallback to general statuses
      if (state.configsByGroup.containsKey('PROPOSAL_GENERAL_STATUS') &&
          state.configsByGroup['PROPOSAL_GENERAL_STATUS']!.isNotEmpty) {
        return state.configsByGroup['PROPOSAL_GENERAL_STATUS']!;
      }

      // Legacy fallback (for backward compatibility)
      if (state.configsByGroup.containsKey('PROPOSAL_STATUS') &&
          state.configsByGroup['PROPOSAL_STATUS']!.isNotEmpty) {
        return state.configsByGroup['PROPOSAL_STATUS']!;
      }
    }
    return []; // Empty list if no statuses loaded
  }

  /// Apply filters and get transactions from API
  void _applyFilters() {
    // Build GetProposalsRequest from applied filter state
    final request = GetProposalsRequest(
      createdFrom: _appliedDateRange?.start,
      createdTo: _appliedDateRange?.end,
      productId: _appliedProduct?.id,
      status: _appliedStatus?.code,
      regionIds: _appliedRegions.isNotEmpty
          ? _appliedRegions.map((r) => r.id).toList()
          : null,
      branchIds: _appliedBranches.isNotEmpty
          ? _appliedBranches.map((b) => b.id).toList()
          : null,
      assignedEmployeeIds: _appliedEmployee != null
          ? [_appliedEmployee!.id!]
          : null,
      pKeySearch: _searchText.isNotEmpty ? _searchText : null,
      limit: 20,
      offset: 0,
    );

    // Call TransactionBloc to filter transactions
    context.read<TransactionBloc>().add(FilterTransactions(request: request));
  }

  void _onSearchChanged(String text) {
    // Cancel previous timer first
    _searchDebounceTimer?.cancel();

    // Update local state immediately without setState to avoid rebuild
    _searchText = text;

    // Schedule search with debounce - this will trigger BlocBuilder rebuild
    // but our search field is outside BlocBuilder so keyboard stays
    _searchDebounceTimer = Timer(_searchDebounceDelay, () {
      if (!mounted) return;

      // Apply search với current filters (combined approach)
      _applyFilters();
    });
  }

  void _clearAllFilters() {
    // Cancel any pending search
    _searchDebounceTimer?.cancel();

    setState(() {
      _selectedFilter = 0;
      _dateRange = null;
      _selectedProduct = null;
      _selectedStatus = null;
      _selectedEmployee = null;
      _selectedRegions.clear();
      _selectedBranches.clear();
      _searchText = '';

      // Clear applied filters
      _appliedDateRange = null;
      _appliedProduct = null;
      _appliedStatus = null;
      _appliedEmployee = null;
      _appliedRegions.clear();
      _appliedBranches.clear();
    });

    // Load all transactions without filters
    context.read<TransactionBloc>().add(const LoadTransactionList());
  }

  void _onLoadMore() {
    // Prevent duplicate calls
    if (_isLoadingMore) return;

    // Check if we can load more from current state
    final currentState = context.read<TransactionBloc>().state;
    if (currentState is TransactionLoaded && !currentState.hasMoreData) {
      return; // No more data to load
    }

    _isLoadingMore = true;
    context.read<TransactionBloc>().add(const LoadMoreTransactions(limit: 20));
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        // Listen to TransactionBloc for error and success states
        BlocListener<TransactionBloc, TransactionState>(
          listener: (context, state) {
            // Handle error states
            if (state is TransactionError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }

            // Handle success actions
            if (state is TransactionActionSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
            }

            // Reset loading more flag when load more completes
            if (state is TransactionLoaded || state is TransactionError) {
              _isLoadingMore = false;
            }
          },
        ),
        // Listen to EmployeeBloc for search results
        BlocListener<EmployeeBloc, EmployeeState>(
          listener: (context, state) {
            if (state is EmployeesSearchLoaded) {
              setState(() {
                _employees = state.employees;
                _isSearchingEmployees = false;
              });
              _employeeSearchStreamController.add(state.employees);
            } else if (state is EmployeeError) {
              setState(() {
                _employees = [];
                _isSearchingEmployees = false;
              });
              _employeeSearchStreamController.add([]);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Lỗi tìm kiếm nhân viên: ${state.message}'),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is EmployeeLoading) {
              setState(() {
                _isSearchingEmployees = true;
              });
            }
          },
        ),
        // Listen to MasterDataBloc
        BlocListener<MasterDataBloc, MasterDataState>(
          listener: (context, state) {
            if (state is MasterDataLoaded) {
              // Update statuses từ configsByGroup
              final statuses = _getStatusesFromState(state);
              setState(() {
                _statuses = statuses;
                _regions = state.regions;
                // Check if basic master data is loaded
                _masterDataLoaded = _statuses.isNotEmpty && _regions.isNotEmpty;
              });

              // Kiểm tra xem có branches không để update
              if (state.branches.isNotEmpty) {
                setState(() {
                  _branches = state.branches;
                  _branchesLoading = false;
                });

                // Emit branches mới qua stream - ALWAYS emit để modal update
                _branchesStreamController.add(_branches);
              } else {
                // Nếu API trả về empty, cũng emit để modal biết
                setState(() {
                  _branchesLoading = false;
                });
                _branchesStreamController.add([]);
              }
            } else if (state is MasterDataLoading && state.type == 'branches') {
              setState(() {
                _branchesLoading = true;
              });
              // KHÔNG emit empty list khi loading để tránh confuse modal
              // Modal sẽ dựa vào widget.branchesLoading để hiển thị loading state
            } else if (state is MasterDataError) {
              setState(() {
                _branchesLoading = false;
              });
              if (state.type == 'branches') {
                // Show user-friendly error message for branches
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Lỗi tải chi nhánh: ${state.message}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
        ),
        // Listen to ProductBloc
        BlocListener<ProductBloc, ProductState>(
          listener: (context, state) {
            if (state is ProductLoaded) {
              setState(() {
                _products = state.products;
                // Update master data loaded flag
                if (_statuses.isNotEmpty && _regions.isNotEmpty) {
                  _masterDataLoaded = true;
                }
              });
            }
          },
        ),
      ],
      child: Scaffold(
        appBar: AppNavHeaderExtension.forTab(
          title: 'Giao dịch',
          actions: [
            Container(
              margin: const EdgeInsets.only(right: AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: Icon(
                  TablerIcons.plus,
                  color: Colors.white,
                  size: AppDimensions.iconM,
                ),
                tooltip: 'Tạo giao dịch mới',
                onPressed: () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const CreateTransactionScreen(),
                    ),
                  );
                  if (mounted) {
                    // ignore: use_build_context_synchronously
                    context.read<TransactionBloc>().add(const RefreshTransactionList());
                  }
                },
              ),
            ),
          ],
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            context.read<TransactionBloc>().add(const RefreshTransactionList());
          },
          child: CustomScrollView(
            controller:
                _scrollController, // Add scroll controller for infinite scroll
            slivers: [
              // Transaction Summary - with targeted BlocBuilder
              SliverToBoxAdapter(child: _buildTransactionSummary()),

              // Quick Actions - static (no API needed)
              const SliverToBoxAdapter(child: QuickActionsGrid()),

              const SliverToBoxAdapter(
                child: SizedBox(height: AppDimensions.spacingL),
              ),

              // Enhanced Search Bar
              SliverToBoxAdapter(
                child: EnhancedSearchBar(
                  searchText: _searchText,
                  onSearchChanged: _onSearchChanged,
                  onAdvancedFilter: _showModernFilterModal,
                  hasActiveFilters: _hasActiveFilters,
                ),
              ),

              const SliverToBoxAdapter(
                child: SizedBox(height: AppDimensions.spacingM),
              ),

              // Filter Bar - static (using local data)
              SliverToBoxAdapter(
                child: Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                  ),
                  child: TransactionFilterBar(
                    selectedIndex: _selectedFilter,
                    availableStatuses: _statuses,
                    onChanged: (index) =>
                        setState(() => _selectedFilter = index),
                    onStatusSelected: (status) {
                      // Update selected status in modal and load transactions
                      if (status != null) {
                        // Update both preview and applied status for filter bar
                        setState(() {
                          _selectedStatus = status;
                          _appliedStatus = status;
                        });

                        // Load transactions with the selected status
                        _loadTransactionsWithStatus(status.code ?? '');
                      } else {
                        // Clear status and load all transactions
                        setState(() {
                          _selectedStatus = null;
                          _appliedStatus = null;
                        });
                        _loadAllTransactions();
                      }
                    },
                  ),
                ),
              ),

              const SliverToBoxAdapter(
                child: SizedBox(height: AppDimensions.spacingM),
              ),

              // Transaction List - with targeted BlocBuilder
              _buildTransactionList(),

              // Bottom spacing
              const SliverToBoxAdapter(
                child: SizedBox(height: AppDimensions.spacingXL),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Targeted BlocBuilder for Transaction Summary
  Widget _buildTransactionSummary() {
    return BlocBuilder<TransactionBloc, TransactionState>(
      buildWhen: (previous, current) {
        // Only rebuild when transaction data changes or loading states change
        return current is TransactionLoading ||
            current is TransactionLoaded ||
            current is TransactionError ||
            current is TransactionEmpty;
      },
      builder: (context, state) {
        if (state is TransactionLoading) {
          // Show shimmer loading summary
          return _buildTransactionSummaryShimmer();
        } else if (state is TransactionLoaded) {
          // Show shimmer if refreshing, otherwise show fake data for summary
          if (state.isRefreshing) {
            return _buildTransactionSummaryShimmer();
          }
          // Use fake data for summary since no API available yet
          final stats = _transactionStats;
          return TransactionSummarySection(
            totalCount: stats['total'] ,
            successCount: stats['success'],
            pendingCount: stats['pending'],
            errorCount: stats['error'],
            totalAmount: stats['totalAmount'].toString(),
          );
        } else if (state is TransactionError &&
            state.previousTransactions != null) {
          // Show previous data if available - use fake data for summary
          final stats = _transactionStats;
          return TransactionSummarySection(
            totalCount: stats['total'],
            successCount: stats['success'],
            pendingCount: stats['pending'],
            errorCount: stats['error'],
            totalAmount: stats['totalAmount'].toString(),
          );
        } else {
          // Default/empty state
          return TransactionSummarySection(
            totalCount: 0,
            successCount: 0,
            pendingCount: 0,
            errorCount: 0,
            totalAmount: '0 VND',
          );
        }
      },
    );
  }

  /// Targeted BlocBuilder for Transaction List
  Widget _buildTransactionList() {
    return BlocBuilder<TransactionBloc, TransactionState>(
      buildWhen: (previous, current) {
        // Skip summary-only updates, only rebuild for list changes
        if (previous.runtimeType != current.runtimeType) {
          return true;
        }

        // For loaded states, check if transaction data actually changed
        if (current is TransactionLoaded && previous is TransactionLoaded) {
          return current.transactions.length != previous.transactions.length ||
              current.hasMoreData != previous.hasMoreData ||
              current.isLoadingMore != previous.isLoadingMore ||
              current.transactions != previous.transactions;
        }

        return true;
      },
      builder: (context, state) {
        if (state is TransactionLoading && !state.isRefreshing) {
          // Show skeleton loading for initial load
          return SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) => _buildTransactionCardSkeleton(),
              childCount: 5,
            ),
          );
        } else if (state is TransactionLoaded) {
          final transactions = state.transactions;

          // Show shimmer if refreshing
          if (state.isRefreshing && transactions.isEmpty) {
            return SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) => _buildTransactionCardSkeleton(),
                childCount: 3, // Show 3 shimmer items while refreshing
              ),
            );
          }

          if (transactions.isEmpty) {
            return SliverFillRemaining(
              child: EnhancedEmptyState(
                searchText: _searchText.isNotEmpty ? _searchText : null,
                hasActiveFilters: _hasActiveFilters || _selectedFilter != 0,
                onClearFilters: _clearAllFilters,
                onCreateTransaction: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const CreateTransactionScreen(),
                    ),
                  );
                },
              ),
            );
          }

          return SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              // Add loading indicator at the end if hasMoreData
              if (index == transactions.length) {
                if (state.hasMoreData) {
                  return Padding(
                    padding: const EdgeInsets.all(AppDimensions.paddingM),
                    child: Center(
                      child: SizedBox(
                        height: 24,
                        width: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppColors.kienlongSkyBlue,
                        ),
                      ),
                    ),
                  );
                }
                return const SizedBox(height: AppDimensions.spacingXL);
              }

              final transaction = transactions[index];
              return _buildTransactionCardFromApi(transaction);
            }, childCount: transactions.length + (state.hasMoreData ? 1 : 1)),
          );
        } else if (state is TransactionError) {
          if (state.previousTransactions != null &&
              state.previousTransactions!.isNotEmpty) {
            // Show previous data with error indicator
            final transactions = state.previousTransactions!;
            return SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) =>
                    _buildTransactionCardFromApi(transactions[index]),
                childCount: transactions.length,
              ),
            );
          } else {
            // Show error state
            return SliverFillRemaining(child: _buildErrorState(state));
          }
        } else if (state is TransactionEmpty) {
          return SliverFillRemaining(
            child: EnhancedEmptyState(
              searchText: _searchText.isNotEmpty ? _searchText : null,
              hasActiveFilters: state.hasFilters,
              onClearFilters: _clearAllFilters,
              onCreateTransaction: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CreateTransactionScreen(),
                  ),
                );
              },
            ),
          );
        } else {
          // Initial/unknown state - show empty
          return const SliverToBoxAdapter(child: SizedBox.shrink());
        }
      },
    );
  }

  /// Build transaction card from API data
  Widget _buildTransactionCardFromApi(ProposalItem transaction) {
    return TransactionCard(
      customerName: transaction.borrowerName ?? 'N/A',
      product: transaction.productName ?? 'N/A',
      statusConfig: transaction.status,
      date:
          transaction.createdAt?.toString().split(' ')[0] ??
          'N/A', // Format date
      borrowerPhone: transaction.borrowerPhone,
      branchName: transaction.branch?.name,
      createdByName: transaction.createdBy?.fullName,
      createdAt: transaction.createdAt?.toString(),
      amount: _formatCurrency((transaction.loanAmount ?? 0).toDouble()),
      customerTag: null, // API doesn't have customer tag yet
      currentFlowStep: transaction.currentFlowStep,
      productDisplayConfig: transaction.product?.displayConfig,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => TransactionDetailScreen(
              transaction: {
                'id': transaction.id,
                'customerName': transaction.borrowerName ?? 'N/A',
                'product': transaction.productName ?? 'N/A',
                'status': transaction.status?.label ?? 'N/A',
                'date':
                    transaction.createdAt?.toString().split(' ')[0] ?? 'N/A',
                'amount': _formatCurrency(
                  (transaction.loanAmount ?? 0).toDouble(),
                ),
                'currentFlowStep': transaction.currentFlowStep?.name,
              },
            ),
          ),
        );
      },
      onCall: () {
        // TODO: Make phone call using transaction.borrowerPhone
      },
      onShare: () {
        // TODO: Share transaction
      },
    );
  }

  /// Build shimmer effect for transaction summary section
  Widget _buildTransactionSummaryShimmer() {
    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: AppColors.neutral200,
        highlightColor: AppColors.neutral100,
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title placeholder
              Container(
                width: 200,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),

              SizedBox(height: AppDimensions.spacingL),

              // Stats grid placeholder
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: AppDimensions.spacingM,
                mainAxisSpacing: AppDimensions.spacingM,
                childAspectRatio: 2.5,
                children: List.generate(4, (index) {
                  return Container(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                      border: Border.all(color: AppColors.borderLight),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Number placeholder
                        Container(
                          width: 60,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),

                        SizedBox(height: AppDimensions.spacingS),

                        // Label placeholder
                        Container(
                          width: 80,
                          height: 14,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),

              SizedBox(height: AppDimensions.spacingL),

              // Total amount placeholder
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    width: 120,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build shimmer loading card for transaction item
  Widget _buildTransactionCardSkeleton() {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.spacingS,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: AppColors.neutral200,
        highlightColor: AppColors.neutral100,
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              // Avatar/Icon placeholder
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
              ),

              SizedBox(width: AppDimensions.spacingM),

              // Content placeholder
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Customer name placeholder
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),

                    SizedBox(height: AppDimensions.spacingS),

                    // Product and status row
                    Row(
                      children: [
                        // Product placeholder
                        Container(
                          width: 100,
                          height: 14,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),

                        SizedBox(width: AppDimensions.spacingM),

                        // Status placeholder
                        Container(
                          width: 60,
                          height: 14,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: AppDimensions.spacingS),

                    // Date placeholder
                    Container(
                      width: 80,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(width: AppDimensions.spacingM),

              // Amount and actions placeholder
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Amount placeholder
                  Container(
                    width: 80,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),

                  SizedBox(height: AppDimensions.spacingS),

                  // Actions placeholder
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Container(
                        width: 24,
                        height: 24,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(TransactionError state) {
    return SingleChildScrollView(
      physics: NeverScrollableScrollPhysics(),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(TablerIcons.alert_circle, size: 64, color: Colors.grey[400]),
            const SizedBox(height: AppDimensions.spacingM),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              state.message,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingL),
            ElevatedButton(
              onPressed: () {
                // Retry với current filter values
                final currentRequest = _buildCurrentFilterRequest();
                context.read<TransactionBloc>().add(
                  RetryLoadTransactions(request: currentRequest),
                );
              },
              child: const Text('Thử lại'),
            ),
          ],
        ),
      ),
    );
  }
}
