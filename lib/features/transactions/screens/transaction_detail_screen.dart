import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';

class TransactionDetailScreen extends StatefulWidget {
  final Map<String, dynamic> transaction;

  const TransactionDetailScreen({
    super.key,
    required this.transaction,
  });

  @override
  State<TransactionDetailScreen> createState() => _TransactionDetailScreenState();
}

class _TransactionDetailScreenState extends State<TransactionDetailScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _showTitle = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Show title when header is mostly collapsed
    final shouldShowTitle = _scrollController.hasClients && 
                           _scrollController.offset > 200;
    if (shouldShowTitle != _showTitle) {
      setState(() {
        _showTitle = shouldShowTitle;
      });
    }
  }

  void _contactCustomer() {
    // TODO: Implement contact customer
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đang liên hệ ${widget.transaction['customerName']}...'),
        backgroundColor: AppColors.kienlongOrange,
      ),
    );
  }

  void _viewCustomerDetail() {
    // TODO: Navigate to customer detail
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Xem chi tiết khách hàng...')),
    );
  }

  void _updateTransaction() {
    // TODO: Navigate to update transaction
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Cập nhật giao dịch...')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // Transaction Detail Header
            TransactionDetailHeader(
              transaction: widget.transaction,
              showTitle: _showTitle,
              onBack: () => Navigator.of(context).pop(),
            ),
            
            // Customer Quick Link
            SliverToBoxAdapter(
              child: TransactionDetailCustomerLink(
                transaction: widget.transaction,
                onCall: _contactCustomer,
                onMessage: _contactCustomer,
                onViewCustomer: _viewCustomerDetail,
              ),
            ),
            
            // Key Metrics Cards
            SliverToBoxAdapter(
              child: TransactionDetailMetrics(transaction: widget.transaction),
            ),
            
            // Tab Bar
            SliverPersistentHeader(
              delegate: _StickyTabBarDelegate(
                TabBar(
                  controller: _tabController,
                  labelColor: AppColors.kienlongOrange,
                  unselectedLabelColor: AppColors.textSecondary,
                  indicatorColor: AppColors.kienlongOrange,
                  indicatorWeight: 3,
                  labelStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: Theme.of(context).textTheme.titleSmall,
                  tabs: const [
                    Tab(text: 'Chi tiết'),
                    Tab(text: 'Tiến trình'),
                    Tab(text: 'Hồ sơ'),
                    Tab(text: 'Tương tác'),
                  ],
                ),
              ),
              pinned: true,
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            TransactionInfoTab(transaction: widget.transaction),
            TransactionProgressTab(transaction: widget.transaction),
            TransactionDocumentsTab(transaction: widget.transaction),
            TransactionActivityTab(transaction: widget.transaction),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          border: Border(
            top: BorderSide(
              color: AppColors.borderLight,
              width: 1,
            ),
          ),
        ),
        child: SafeArea(
          child: Row(
            children: [
              // Update Transaction Button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _updateTransaction,
                  icon: const Icon(TablerIcons.edit),
                  label: const Text('Cập nhật'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.kienlongOrange,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingM,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              // Contact Customer Button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _contactCustomer,
                  icon: const Icon(TablerIcons.phone),
                  label: const Text('Liên hệ'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.kienlongSkyBlue,
                    side: BorderSide(color: AppColors.kienlongSkyBlue),
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingM,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _StickyTabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;
  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(_StickyTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
} 