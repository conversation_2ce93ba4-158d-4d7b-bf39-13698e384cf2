import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../../../shared/blocs/file_upload_bloc.dart';
import '../widgets/index.dart';
import '../models/index.dart';
import '../blocs/index.dart';
import '../utils/transaction_form_validator.dart';
import '../utils/transaction_proposal_mapper.dart';
import '../../products/index.dart' hide SelectProduct; // Hide conflicting SelectProduct
import '../../customers/index.dart';
import '../../auth/blocs/master_data_bloc.dart';

/// CreateTransactionScreen - Using TransactionFormBloc for better performance
/// Kế thừa toàn bộ logic và UI từ CreateTransactionScreen nhưng sử dụng typed models
class CreateTransactionScreen extends StatefulWidget {
  final CustomerModel? preselectedCustomer;
  final ProductModel? preselectedProduct;

  const CreateTransactionScreen({
    super.key,
    this.preselectedCustomer,
    this.preselectedProduct,
  });

  @override
  State<CreateTransactionScreen> createState() => _CreateTransactionScreenState();
}

class _CreateTransactionScreenState extends State<CreateTransactionScreen> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ProductBloc()..add(LoadProducts()),
        ),
        BlocProvider(
          create: (context) => CustomerListBloc(
            customerService: CustomerService(),
          ),
        ),
        BlocProvider(
          create: (context) => MasterDataBloc(),
        ),
        BlocProvider(
          create: (context) => FileUploadBloc(),
        ),
        BlocProvider(
          create: (context) => TransactionBloc(),
        ),
        // ADD: TransactionFormBloc với typed models
        BlocProvider(
          create: (context) => TransactionFormBloc(),
        ),
      ],
      child: _CreateTransactionScreenV2Content(
        preselectedCustomer: widget.preselectedCustomer,
        preselectedProduct: widget.preselectedProduct,
      ),
    );
  }
}

class _CreateTransactionScreenV2Content extends StatefulWidget {
  final CustomerModel? preselectedCustomer;
  final ProductModel? preselectedProduct;

  const _CreateTransactionScreenV2Content({
    this.preselectedCustomer,
    this.preselectedProduct,
  });

  @override
  State<_CreateTransactionScreenV2Content> createState() => _CreateTransactionScreenV2ContentState();
}

class _CreateTransactionScreenV2ContentState extends State<_CreateTransactionScreenV2Content> {
  final PageController _pageController = PageController();
  final ScrollController _stepScrollController = ScrollController();
  int _currentStep = 0;
  
  // Local state - chỉ giữ lại những gì thực sự cần thiết
  final List<DocumentModel> _documents = <DocumentModel>[];
  final Map<String, List<DocumentModel>> _documentsMapping = <String, List<DocumentModel>>{};
  
  // Form keys management cho scroll to error functionality
  final Map<String, GlobalKey> _formKeys = {};
  
  // VALIDATION STRATEGY:
  // - Chỉ validate CHI TIẾT khi nhấn "Xác nhận tạo" (step 4)
  // - Các step khác chỉ kiểm tra cơ bản để cho phép navigation và lưu nháp
  // - ProductFormValidator được sử dụng TẬP TRUNG tại đây thay vì phân tán

  final List<String> _stepTitles = [
    'Chọn sản phẩm',
    'Chọn khách hàng', 
    'Chi tiết sản phẩm',
    'Tài liệu yêu cầu',
    'Xem lại & Xác nhận',
  ];

  @override
  void initState() {
    super.initState();
    // Pre-fill data if provided - dispatch directly to bloc
    if (widget.preselectedCustomer != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<TransactionFormBloc>().add(SelectCustomer(widget.preselectedCustomer!));
      });
    }
    if (widget.preselectedProduct != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<TransactionFormBloc>().add(SelectProduct(widget.preselectedProduct!));
      });
    }
    
    // Initial scroll to current step
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentStep();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _stepScrollController.dispose();
    super.dispose();
  }

  // Removed _transactionDetails getter - now using bloc state directly

  /// Get form key cho product hiện tại
  GlobalKey? _getCurrentFormKey() {
    final blocState = context.read<TransactionFormBloc>().state;
    if (blocState is! TransactionFormLoaded) {
      debugPrint('⚠️ _getCurrentFormKey: TransactionFormBloc not loaded');
      return null;
    }
    
    final productCode = blocState.selectedProduct?.code;
    if (productCode == null) {
      debugPrint('⚠️ _getCurrentFormKey: No product selected');
      return null;
    }
    
    debugPrint('🔍 _getCurrentFormKey: Product code: $productCode');
    debugPrint('🔍 _getCurrentFormKey: Existing keys: ${_formKeys.keys.toList()}');
    
    // Tạo form key nếu chưa có
    if (!_formKeys.containsKey(productCode)) {
      _formKeys[productCode] = GlobalKey();
      debugPrint('🔍 _getCurrentFormKey: Created new key for $productCode');
    } else {
      debugPrint('🔍 _getCurrentFormKey: Using existing key for $productCode');
    }
    
    final key = _formKeys[productCode];
    debugPrint('🔍 _getCurrentFormKey: Returning key: $key');
    return key;
  }

  void _scrollToCurrentStep() {
    if (!_stepScrollController.hasClients) return;
    
    // Calculate position of current step
    // Each step (circle + text) takes about 80px width
    // Each connection line takes 40px width  
    // Total per step: 120px
    final stepWidth = 120.0;
    final targetPosition = _currentStep * stepWidth;
    
    // Get scroll view width to center the step
    final scrollViewWidth = MediaQuery.of(context).size.width - 32; // Account for padding
    final centerOffset = (scrollViewWidth / 2) - 40; // Center the step circle
    
    final scrollPosition = (targetPosition - centerOffset).clamp(
      0.0, 
      _stepScrollController.position.maxScrollExtent,
    );
    
    _stepScrollController.animateTo(
      scrollPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _nextStep() async {
    // Ẩn bàn phím trước khi chuyển step
    FocusScope.of(context).unfocus();
    
    // Validate current step before proceeding
    if (!(await _validateCurrentStep())) {
      return;
    }
    
    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _goToStep(int stepIndex) {
    if (stepIndex >= 0 && stepIndex < _stepTitles.length) {
      setState(() {
        _currentStep = stepIndex;
      });
      _pageController.animateToPage(
        stepIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _jumpToStep(int step) {
    if (step >= 0 && step < _stepTitles.length) {
      setState(() {
        _currentStep = step;
      });
      _pageController.animateToPage(
        step,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }


  Future<bool> _validateCurrentStep() async {
    final blocState = context.read<TransactionFormBloc>().state;
    
    if (blocState is! TransactionFormLoaded) return false;
    
    switch (_currentStep) {
      case 0: // Product selection
        return blocState.selectedProduct != null;
      case 1: // Customer selection  
        return blocState.selectedCustomer != null;
      case 2: // Product details
        // LUÔN CHO PHÉP TIẾP TỤC - không validate form data ở đây
        // Validation sẽ được thực hiện khi nhấn "Xác nhận tạo"
        return true;
      case 3: // Documents
        return true; // Documents are optional initially
      case 4: // Review
        // Validate tất cả steps tại đây - CHỈ KHI NHẤN "XÁC NHẬN TẠO"
        return true;
        // return await _validateAllSteps();
      default:
        return false;
    }
  }

  /// Validate tất cả các steps và jump về step có lỗi đầu tiên
  /// CHỈ ĐƯỢC GỌI KHI NHẤN "XÁC NHẬN TẠO" - đúng nghiệp vụ
  /// Sử dụng TransactionFormValidator mới cho typed form data
  Future<bool> _validateAllSteps() async {
    debugPrint('=== START: _validateAllSteps (FINAL VALIDATION) ===');

    final blocState = context.read<TransactionFormBloc>().state;
    if (blocState is! TransactionFormLoaded) {
      debugPrint('Validation failed: No transaction form loaded');
      return false;
    }

    final selectedProduct = blocState.selectedProduct;
    final selectedCustomer = blocState.selectedCustomer;
    final formData = blocState.formData;

    // Step 1: Validate form widget trước (để trigger UI validation)
    if (selectedProduct?.code == 'GOLD_LOAN' || selectedProduct?.code == 'MANGO') {
      debugPrint('🔍 Attempting form validation for product: ${selectedProduct?.code}');
      final formKey = _getCurrentFormKey();
      debugPrint('🔍 Form key: $formKey');
      debugPrint('🔍 Form key current state: ${formKey?.currentState}');
      
      if (formKey?.currentState != null) {
        try {
          final formState = formKey!.currentState as dynamic;
          final firstErrorField = formState.validateAndGetFirstErrorField();
          debugPrint('🔍 First error field result: $firstErrorField');
          
          if (firstErrorField != null) {
            debugPrint('❌ Form validation failed, first error field: $firstErrorField');
            _jumpToInvalidStep(2, 'Vui lòng kiểm tra lại thông tin trong form');
            return false;
          }
        } catch (e) {
          debugPrint('❌ Error calling form validation: $e');
        }
      } else {
        debugPrint('⚠️ Form key or form state is null - skipping form validation');
      }
    }

    // Step 2: Validate với TransactionFormValidator
    final validator = TransactionFormValidator(
      selectedProduct: selectedProduct,
      selectedCustomer: selectedCustomer,
      formData: formData,
      documents: _documents,
    );
    
    // Validate toàn bộ form
    final validationErrors = validator.validateAll();
    
    if (validationErrors.isNotEmpty) {
      debugPrint('❌ Validation failed:');
      for (int i = 0; i < validationErrors.length; i++) {
        debugPrint('  ${i + 1}. ${validationErrors[i]}');
      }
      
      // Jump về step có lỗi đầu tiên
      final firstError = validationErrors.first;
      int stepToJump = 2; // Default to product details step
      
      if (firstError.contains('chọn sản phẩm')) {
        stepToJump = 0;
      } else if (firstError.contains('chọn khách hàng')) {
        stepToJump = 1;
      } else if (firstError.contains('upload') || firstError.contains('tài liệu')) {
        stepToJump = 3;
      }
      
      _jumpToInvalidStep(stepToJump, firstError);
      return false;
    }

    debugPrint('All validation passed');
    debugPrint('=== END: _validateAllSteps ===');
    return true;
  }

  /// Jump về step có lỗi validation
  Future<void> _jumpToInvalidStep(int stepIndex, String errorMessage) async {
    debugPrint('=== START: _jumpToInvalidStep to step $stepIndex ===');
    
    // Jump về step có lỗi trước
    _goToStep(stepIndex);
    await Future.delayed(const Duration(milliseconds: 400));
    
    // If it's step 2 (product details), scroll to first error field
    if (stepIndex == 2) {
      debugPrint('🔍 Jumping to step 2, setting up scroll to error...');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debugPrint('🔍 Post frame callback executing...');
        final formKey = _getCurrentFormKey();
        debugPrint('🔍 Form key in callback: $formKey');
        debugPrint('🔍 Form key current state in callback: ${formKey?.currentState}');
        
        if (formKey?.currentState != null) {
          try {
            // Cast to dynamic để access methods (vì không biết cụ thể loại form)
            final formState = formKey!.currentState as dynamic;
            
            // Call validateAndGetFirstErrorField method
            final firstErrorField = formState.validateAndGetFirstErrorField();
            debugPrint('🔍 First error field in callback: $firstErrorField');
            
            if (firstErrorField != null) {
              // Call scrollToField method
              formState.scrollToField(firstErrorField);
              debugPrint('🔍 Scrolled to first error field: $firstErrorField');
            } else {
              debugPrint('⚠️ No error field found - cannot scroll');
            }
          } catch (e) {
            debugPrint('❌ Error in post frame callback: $e');
          }
        } else {
          debugPrint('⚠️ Form key or state is null in callback');
        }
      });
    }
    
    // Hiển thị thông báo lỗi
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Đi đến',
          textColor: Colors.white,
          onPressed: () {
            _goToStep(stepIndex);
          },
        ),
      ),
    );
    
    debugPrint('=== END: _jumpToInvalidStep ===');
  }

  void _saveDraft() {
    // Kiểm tra xem có dữ liệu để lưu không
    if (!_canSaveDraft()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Chưa có dữ liệu để lưu nháp'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    // Tạo proposal request với mode DRAFT
    final proposalRequest = _createDraftProposalRequest();
    
    // Gọi API để lưu nháp
    context.read<TransactionBloc>().add(CreateProposal(request: proposalRequest));
  }

  /// Kiểm tra có thể lưu nháp không - sử dụng TransactionFormValidator
  bool _canSaveDraft() {
    final blocState = context.read<TransactionFormBloc>().state;
    
    if (blocState is! TransactionFormLoaded) return false;
    
    final validator = TransactionFormValidator(
      selectedProduct: blocState.selectedProduct,
      selectedCustomer: blocState.selectedCustomer,
      formData: blocState.formData,
      documents: _documents,
    );
    
    return validator.canSaveDraft();
  }

  /// Tạo proposal request cho lưu nháp với mode DRAFT
  TransactionProposalRequest _createDraftProposalRequest() {
    final blocState = context.read<TransactionFormBloc>().state;
    if (blocState is! TransactionFormLoaded) {
      throw StateError('TransactionFormBloc is not in loaded state');
    }
    
    debugPrint('=== START: _createDraftProposalRequest ===');
    debugPrint('📋 Selected Product: ${blocState.selectedProduct?.code}');
    debugPrint('📋 Selected Customer: ${blocState.selectedCustomer?.fullName}');
    debugPrint('📋 Form Data Type: ${blocState.formData?.runtimeType}');
    
    // Sử dụng TransactionProposalMapper để map dữ liệu với mode DRAFT
    final proposalRequest = TransactionProposalMapper.mapToProposalRequest(
      selectedProduct: blocState.selectedProduct,
      selectedCustomer: blocState.selectedCustomer,
      formData: blocState.formData,
      documentsMapping: _documentsMapping,
      mode: 'DRAFT',
    );
    
    debugPrint('✅ Draft proposal request created successfully');
    debugPrint('=== END: _createDraftProposalRequest ===');
    
    return proposalRequest;
  }



  /// Create proposal request from current data using TransactionProposalMapper
  TransactionProposalRequest _createProposalRequest() {
    final blocState = context.read<TransactionFormBloc>().state;
    if (blocState is! TransactionFormLoaded) {
      throw StateError('TransactionFormBloc is not in loaded state');
    }
    
    debugPrint('=== START: _createProposalRequest ===');
    debugPrint('📋 Selected Product: ${blocState.selectedProduct?.code}');
    debugPrint('📋 Selected Customer: ${blocState.selectedCustomer?.fullName}');
    debugPrint('📋 Form Data Type: ${blocState.formData?.runtimeType}');
    debugPrint('📋 Documents Mapping Keys: ${_documentsMapping.keys.toList()}');
    
    // Sử dụng TransactionProposalMapper để map dữ liệu
    final proposalRequest = TransactionProposalMapper.mapToProposalRequest(
      selectedProduct: blocState.selectedProduct,
      selectedCustomer: blocState.selectedCustomer,
      formData: blocState.formData,
      documentsMapping: _documentsMapping,
      mode: 'FULL',
    );
    
    debugPrint('✅ Proposal request created successfully');
    debugPrint('=== END: _createProposalRequest ===');
    
    return proposalRequest;
  }



  /// Handle confirm transaction
  Future<void> _confirmTransaction() async {
    debugPrint('=== START: _confirmTransaction ===');
    
    // Validate all steps before creating transaction
    if (!(await _validateAllSteps())) {
      debugPrint('Transaction creation cancelled due to validation failure');
      return;
    }
    
    debugPrint('All validation passed, creating proposal...');
    // Create proposal using bloc
    final proposalRequest = _createProposalRequest();
    if (mounted) {
      context.read<TransactionBloc>().add(CreateProposal(request: proposalRequest));
    }
    
    debugPrint('=== END: _confirmTransaction ===');
  }


  /// Show error dialog
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        title: Row(
          children: [
            Icon(
              TablerIcons.alert_circle,
              color: AppColors.error,
              size: AppDimensions.iconM,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _confirmTransaction(); // Retry
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  /// Show snackbar when draft is saved successfully
  void _showDraftSavedSnackBar(BuildContext context, String proposalId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã lưu nháp giao dịch: $proposalId'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Xem danh sách',
          textColor: Colors.white,
          onPressed: () {
            // TODO: Navigate to drafts list
            // Navigator.pushNamed(context, '/transactions/drafts');
          },
        ),
      ),
    );
  }


  /// Build step content với error boundary và BlocBuilder
  Widget _buildStepContent(int index) {
    try {
      switch (index) {
        case 0:
          return BlocBuilder<TransactionFormBloc, TransactionFormState>(
            buildWhen: (previous, current) {
              // Only rebuild when selectedProduct changes
              if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
                return previous.selectedProduct?.id != current.selectedProduct?.id;
              }
              return true; // Rebuild for initial state or state type changes
            },
            builder: (context, state) {
              final selectedProduct = state is TransactionFormLoaded ? state.selectedProduct : null;
              
              return ProductSelectionStep(
                selectedProduct: selectedProduct,
                onProductSelected: (product) {
                  // Clear documents when product changes
                  _documents.clear();
                  _documentsMapping.clear();
                  // Dispatch typed event to TransactionFormBloc
                  context.read<TransactionFormBloc>().add(SelectProduct(product));
                },
              );
            },
          );
        case 1:
          return BlocBuilder<TransactionFormBloc, TransactionFormState>(
            buildWhen: (previous, current) {
              // Only rebuild when selectedCustomer changes
              if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
                return previous.selectedCustomer?.id != current.selectedCustomer?.id;
              }
              return true; // Rebuild for initial state or state type changes
            },
            builder: (context, state) {
              final selectedCustomer = state is TransactionFormLoaded ? state.selectedCustomer : null;
              
              return CustomerSelectionStep(
                selectedCustomer: selectedCustomer,
                preSelectedCustomer: widget.preselectedCustomer,
                onCustomerSelected: (customer) {
                  // Dispatch typed event to TransactionFormBloc
                  context.read<TransactionFormBloc>().add(SelectCustomer(customer));
                },
              );
            },
          );
        case 2:
          // ProductDetailsStep - pass data directly, no need for BlocBuilder here
          // Parent's BlocBuilder already handles the state management
          return BlocBuilder<TransactionFormBloc, TransactionFormState>(
            buildWhen: (previous, current) {
              // Only rebuild when product or customer changes (affects which form widget to show)
              if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
                final productChanged = previous.selectedProduct?.id != current.selectedProduct?.id;
                final customerChanged = previous.selectedCustomer?.id != current.selectedCustomer?.id;
                
                if (productChanged || customerChanged) {
                  debugPrint('🔄 CreateTransactionScreenV2 Step 2 rebuilding: product=$productChanged, customer=$customerChanged');
                  return true;
                }
                
                debugPrint('⏭️ CreateTransactionScreenV2 Step 2 skipping rebuild: only formData changed');
                return false;
              }
              return true; // Rebuild for initial state or state type changes
            },
            builder: (context, state) {
              if (state is TransactionFormLoaded) {
                return ProductDetailsStep(
                  product: state.selectedProduct,
                  selectedCustomer: state.selectedCustomer,
                  formKey: _getCurrentFormKey(),
                );
              }
              return ProductDetailsStep(
                product: null,
                selectedCustomer: null,
                formKey: _getCurrentFormKey(),
              );
            },
          );
        case 3:
          return BlocBuilder<TransactionFormBloc, TransactionFormState>(
            buildWhen: (previous, current) {
              // Only rebuild when product or formData changes (affects document requirements)
              if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
                final productChanged = previous.selectedProduct?.id != current.selectedProduct?.id;
                final formDataChanged = previous.formData != current.formData;
                
                if (productChanged || formDataChanged) {
                  debugPrint('🔄 CreateTransactionScreenV2 Step 3 rebuilding: product=$productChanged, formData=$formDataChanged');
                  return true;
                }
                
                debugPrint('⏭️ CreateTransactionScreenV2 Step 3 skipping rebuild: no relevant changes');
                return false;
              }
              return true; // Rebuild for initial state or state type changes
            },
            builder: (context, state) {
              final product = state is TransactionFormLoaded ? state.selectedProduct : null;
              
              return DocumentsStep(
                product: product?.code,
                formData: state is TransactionFormLoaded ? state.formData : null, // Pass typed form data
                documents: List<DocumentModel>.from(_documents),
                onDocumentsChanged: (documents) {
                  if (mounted) {
                    _documents.clear();
                    _documents.addAll(documents);
                  }
                },
                onDocumentsMappingChanged: (mapping) {
                  if (mounted) {
                    _documentsMapping.clear();
                    _documentsMapping.addAll(mapping);
                    // Update TransactionFormBloc with documents
                    context.read<TransactionFormBloc>().add(UpdateDocuments(mapping));
                  }
                },
              );
            },
          );
        case 4:
          return BlocBuilder<TransactionFormBloc, TransactionFormState>(
            buildWhen: (previous, current) {
              // Only rebuild when product, customer, or formData changes (affects review content)
              if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
                final productChanged = previous.selectedProduct?.id != current.selectedProduct?.id;
                final customerChanged = previous.selectedCustomer?.id != current.selectedCustomer?.id;
                final formDataChanged = previous.formData != current.formData;
                
                if (productChanged || customerChanged || formDataChanged) {
                  debugPrint('🔄 CreateTransactionScreenV2 Step 4 rebuilding: product=$productChanged, customer=$customerChanged, formData=$formDataChanged');
                  return true;
                }
                
                debugPrint('⏭️ CreateTransactionScreenV2 Step 4 skipping rebuild: no relevant changes');
                return false;
              }
              return true; // Rebuild for initial state or state type changes
            },
            builder: (context, state) {
              if (state is TransactionFormLoaded) {
                return ReviewConfirmStep(
                  selectedProduct: state.selectedProduct,
                  selectedCustomer: state.selectedCustomer,
                  formData: state.formData, // Pass typed form data directly
                  documents: List<DocumentModel>.from(_documents),
                  onConfirm: null, // Let ReviewConfirmStep handle the bloc integration
                  onEditCustomer: () => _goToStep(1), // Quay lại step chọn khách hàng
                  onEditProduct: () => _goToStep(2), // Quay lại step chọn sản phẩm
                  onEditDocuments: () => _goToStep(3), // Quay lại step tài liệu
                );
              }
              return ReviewConfirmStep(
                selectedProduct: null,
                selectedCustomer: null,
                formData: null, // No form data available
                documents: List<DocumentModel>.from(_documents),
                onConfirm: null,
                onEditCustomer: () => _goToStep(1),
                onEditProduct: () => _goToStep(2),
                onEditDocuments: () => _goToStep(3),
              );
            },
          );
        default:
          return _buildErrorStep('Step không tồn tại');
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error building step $index: $e');
      debugPrint('Stack trace: $stackTrace');
      return _buildErrorStep('Lỗi tải step: $e');
    }
  }

  /// Build error step widget
  Widget _buildErrorStep(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.alert_triangle,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  // Force rebuild for error recovery
                });
              },
              icon: Icon(TablerIcons.refresh),
              label: Text('Thử lại'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TransactionBloc, TransactionState>(
      listener: (context, state) {
        if (state is ProposalCreating) {
          // Show loading dialog
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (state is ProposalCreated) {
          // Close loading dialog if open
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          
          // Kiểm tra xem đây là lưu nháp hay tạo giao dịch hoàn chỉnh
          if (_currentStep == _stepTitles.length - 1) {
            // Đây là tạo giao dịch hoàn chỉnh - chỉ pop về màn danh sách
            Navigator.of(context).pop(true);
          } else {
            // Đây là lưu nháp
            _showDraftSavedSnackBar(context, state.proposalId);
          }
        } else if (state is ProposalError) {
          // Close loading dialog if open
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          // Show error dialog
          _showErrorDialog(context, state.message);
        }
      },
      child: Scaffold(
       appBar: AppNavHeader(
          title: 'Tạo giao dịch mới',
          showBackButton: true,
          actions: [
            InkWell(
              onTap: _canSaveDraft() ? _saveDraft : null,
              child: Text(
                _canSaveDraft() ? 'Tạo nhanh ' : '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _canSaveDraft()
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.5),
                ),
              ),
            ),
          ],
        ),
      body: GestureDetector(
        onTap: () {
          // Ẩn bàn phím khi tap ra ngoài
          FocusScope.of(context).unfocus();
        },
        child: Column(
          children: [
            // Step Indicator
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: BlocBuilder<TransactionFormBloc, TransactionFormState>(
                buildWhen: (previous, current) {
                  // Only rebuild step indicator when product or customer changes
                  if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
                    final productChanged = previous.selectedProduct?.id != current.selectedProduct?.id;
                    final customerChanged = previous.selectedCustomer?.id != current.selectedCustomer?.id;
                    
                    if (productChanged || customerChanged) {
                      debugPrint('🔄 Step indicator rebuilding: product=$productChanged, customer=$customerChanged');
                      return true;
                    }
                    
                    debugPrint('⏭️ Step indicator skipping rebuild: only formData changed');
                    return false;
                  }
                  return true;
                },
                builder: (context, state) {
                  return _buildStepIndicator(state);
                },
              ),
            ),

            // Page Content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _stepTitles.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                  // Auto scroll to current step when user swipes
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToCurrentStep();
                  });
                },
                itemBuilder: (context, index) {
                  return _buildStepContent(index);
                },
              ),
            ),

            // Navigation Controls
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  top: BorderSide(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: SafeArea(
                child: BlocBuilder<TransactionFormBloc, TransactionFormState>(
                  buildWhen: (previous, current) {
                    // Only rebuild navigation when product or customer changes
                    if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
                      final productChanged = previous.selectedProduct?.id != current.selectedProduct?.id;
                      final customerChanged = previous.selectedCustomer?.id != current.selectedCustomer?.id;
                      
                      if (productChanged || customerChanged) {
                        debugPrint('🔄 Navigation controls rebuilding: product=$productChanged, customer=$customerChanged');
                        return true;
                      }
                      
                      debugPrint('⏭️ Navigation controls skipping rebuild: only formData changed');
                      return false;
                    }
                    return true;
                  },
                  builder: (context, state) {
                    return _buildNavigationControls(state);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildStepIndicator(TransactionFormState state) {
    // Helper function to check if a specific step can proceed
    bool canProceedToStep(int stepIndex) {
      if (state is! TransactionFormLoaded) return false;
      
      switch (stepIndex) {
        case 0: // Product selection
          return state.selectedProduct != null;
        case 1: // Customer selection
          return state.selectedCustomer != null;
        case 2: // Product details
          // LUÔN CHO PHÉP NAVIGATION - không kiểm tra form data
          return true;
        case 3: // Documents
          return true; // Documents are optional initially
        case 4: // Review
          return true;
        default:
          return false;
      }
    }

    return SingleChildScrollView(
      controller: _stepScrollController,
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_stepTitles.length * 2 - 1, (index) {
          if (index % 2 == 0) {
            // Step circle
            final stepIndex = index ~/ 2;
            final isCompleted = stepIndex < _currentStep;
            final isCurrent = stepIndex == _currentStep;
            final isEnabled = stepIndex <= _currentStep || canProceedToStep(stepIndex);

            return GestureDetector(
              onTap: isEnabled ? () => _jumpToStep(stepIndex) : null,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Step Circle
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isCompleted
                          ? AppColors.success
                          : isCurrent
                              ? AppColors.kienlongOrange
                              : AppColors.neutral300,
                      shape: BoxShape.circle,
                      boxShadow: isCurrent ? [
                        BoxShadow(
                          color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Center(
                      child: isCompleted
                          ? Icon(
                              TablerIcons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : Text(
                              '${stepIndex + 1}',
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Step Title
                  SizedBox(
                    width: 80,
                    child: Text(
                      _stepTitles[stepIndex],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isCurrent
                            ? AppColors.kienlongOrange
                            : isCompleted
                                ? AppColors.success
                                : AppColors.textSecondary,
                        fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Connection line
            final lineIndex = index ~/ 2;
            final isLineCompleted = lineIndex < _currentStep;
            
            return Container(
              width: 40,
              height: 2,
              margin: EdgeInsets.only(top: 15), // Align with circle center (32/2 - 1)
              decoration: BoxDecoration(
                color: isLineCompleted
                    ? AppColors.success
                    : AppColors.neutral300,
                borderRadius: BorderRadius.circular(1),
              ),
            );
          }
        }),
      ),
    );
  }

  Widget _buildNavigationControls(TransactionFormState state) {
    // Helper function to check if can proceed to next step based on state
    bool canProceedToNext() {
      switch (_currentStep) {
        case 0: // Product selection
          return state is TransactionFormLoaded && state.selectedProduct != null;
        case 1: // Customer selection
          return state is TransactionFormLoaded && state.selectedCustomer != null;
        case 2: // Product details
          // LUÔN CHO PHÉP TIẾP TỤC - không kiểm tra form data
          // Form data validation sẽ được thực hiện khi nhấn "Xác nhận tạo"
          return true;
        case 3: // Documents
          return true; // Documents are optional initially
        case 4: // Review
          return true;
        default:
          return false;
      }
    }

    return Row(
      children: [
        // Previous Button
        if (_currentStep > 0)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _previousStep,
              icon: const Icon(TablerIcons.arrow_left),
              label: const Text('Quay lại'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.kienlongSkyBlue,
                side: BorderSide(color: AppColors.kienlongSkyBlue),
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              ),
            ),
          ),
        
        if (_currentStep > 0)
          SizedBox(width: AppDimensions.spacingM),
        
        // Next/Confirm Button
        Expanded(
          flex: _currentStep == 0 ? 1 : 2,
          child: ElevatedButton.icon(
            onPressed: canProceedToNext()
                ? (_currentStep == _stepTitles.length - 1
                    ? _confirmTransaction
                    : _nextStep)
                : null,
            icon: Icon(
              _currentStep == _stepTitles.length - 1
                  ? TablerIcons.check
                  : TablerIcons.arrow_right,
            ),
            label: Text(
              _currentStep == _stepTitles.length - 1
                  ? 'Xác nhận tạo'
                  : 'Tiếp tục',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              disabledBackgroundColor: AppColors.neutral300,
            ),
          ),
        ),
      ],
    );
  }
} 
