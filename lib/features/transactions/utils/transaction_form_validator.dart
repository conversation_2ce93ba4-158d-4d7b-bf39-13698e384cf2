import 'package:flutter/foundation.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../models/document_model.dart';
import '../models/form_data/base_form_data.dart';
import '../models/form_data/installment_loan_form_data.dart';
import '../models/form_data/personal_loan_form_data.dart';
import '../models/enums/loan_type_enum.dart';
import '../blocs/transaction_form_bloc.dart';
import 'document_validator.dart';

/// TransactionFormValidator - Validator chuyên dụng cho create_transaction_screen.dart
/// Tích hợp với TransactionFormBloc và typed form data models
class TransactionFormValidator {
  final ProductModel? selectedProduct;
  final CustomerModel? selectedCustomer;
  final BaseFormData? formData;
  final List<DocumentModel> documents;

  TransactionFormValidator({
    this.selectedProduct,
    this.selectedCustomer,
    this.formData,
    this.documents = const [],
  });

  /// Validate toàn bộ form - CHỈ ĐƯỢC GỌI KHI NHẤN "XÁC NHẬN TẠO"
  List<String> validateAll() {
    final errors = <String>[];
    
    debugPrint('=== START: TransactionFormValidator.validateAll() ===');
    debugPrint('📋 Product: ${selectedProduct?.code}');
    debugPrint('📋 Customer: ${selectedCustomer?.fullName}');
    debugPrint('📋 FormData: ${formData?.runtimeType}');
    debugPrint('📋 Documents: ${documents.length}');
    
    // Step 1: Validate product selection
    errors.addAll(_validateProductSelection());
    
    // Step 2: Validate customer selection
    errors.addAll(_validateCustomerSelection());
    
    // Step 3: Validate form data based on product type
    if (selectedProduct != null && formData != null) {
      errors.addAll(_validateFormData());
    }
    
    // Step 4: Validate documents
    if (selectedProduct != null) {
      errors.addAll(_validateDocuments());
    }
    
    debugPrint('❌ Total validation errors: ${errors.length}');
    for (int i = 0; i < errors.length; i++) {
      debugPrint('  ${i + 1}. ${errors[i]}');
    }
    debugPrint('=== END: TransactionFormValidator.validateAll() ===');
    
    return errors;
  }

  /// Validate từng step riêng lẻ - dùng cho navigation giữa các steps
  List<String> validateStep(int stepIndex) {
    switch (stepIndex) {
      case 0: // Product selection
        return _validateProductSelection();
      case 1: // Customer selection
        return _validateCustomerSelection();
      case 2: // Product details
        return _validateFormDataBasic();
      case 3: // Documents
        return []; // Documents optional initially
      case 4: // Review
        return validateAll(); // Full validation
      default:
        return ['Step không hợp lệ'];
    }
  }

  /// Kiểm tra có thể lưu nháp không
  bool canSaveDraft() {
    // Phải có sản phẩm được chọn
    if (selectedProduct == null) return false;
    
    // Phải có ít nhất một trong các điều kiện sau:
    // 1. Có khách hàng được chọn
    // 2. Có dữ liệu form data
    // 3. Có tài liệu được upload
    return selectedCustomer != null || 
           (formData != null && _hasAnyFormData()) || 
           documents.isNotEmpty;
  }

  /// Kiểm tra form có hợp lệ hoàn toàn không
  bool isValid() {
    return validateAll().isEmpty;
  }

  // ===== PRIVATE VALIDATION METHODS =====

  /// Validate product selection
  List<String> _validateProductSelection() {
    final errors = <String>[];
    
    if (selectedProduct == null) {
      errors.add('Vui lòng chọn sản phẩm');
    }
    
    return errors;
  }

  /// Validate customer selection
  List<String> _validateCustomerSelection() {
    final errors = <String>[];
    
    if (selectedCustomer == null) {
      errors.add('Vui lòng chọn khách hàng');
    }
    
    return errors;
  }

  /// Validate form data - full validation
  List<String> _validateFormData() {
    final errors = <String>[];
    
    if (formData == null) {
      errors.add('Thông tin sản phẩm chưa được điền');
      return errors;
    }
    
    // Thực hiện validation toàn diện bao gồm cả basic fields và business rules
    if (formData is InstallmentLoanFormData) {
      errors.addAll(_validateInstallmentLoanFormData(formData as InstallmentLoanFormData));
    } else if (formData is PersonalLoanFormData) {
      errors.addAll(_validatePersonalLoanFormData(formData as PersonalLoanFormData));
    }
    
    return errors;
  }

  /// Validate form data - basic validation (for navigation)
  List<String> _validateFormDataBasic() {
    final errors = <String>[];
    
    if (formData == null) {
      errors.add('Thông tin sản phẩm chưa được điền');
      return errors;
    }
    
    // Chỉ validate các trường cơ bản nhất cho navigation
    if (formData is InstallmentLoanFormData) {
      final data = formData as InstallmentLoanFormData;
      
      // Chỉ validate tên người vay chính và số tiền vay
      if (data.borrowerName?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên người vay chính');
      }
      
      if (data.loanAmount == null || data.loanAmount! <= 0) {
        errors.add('Vui lòng nhập số tiền vay');
      }
    } else if (formData is PersonalLoanFormData) {
      final data = formData as PersonalLoanFormData;
      
      // Chỉ validate tên người vay chính và số tiền vay
      if (data.borrowerName?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên người vay chính');
      }
      
      if (data.loanAmount == null || data.loanAmount! <= 0) {
        errors.add('Vui lòng nhập số tiền vay');
      }
    }
    
    return errors;
  }

  /// Validate InstallmentLoanFormData - comprehensive validation including business rules
  List<String> _validateInstallmentLoanFormData(InstallmentLoanFormData data) {
    final errors = <String>[];
    
    // ===== BORROWER INFORMATION VALIDATION =====
    if (data.borrowerName?.isEmpty ?? true) {
      errors.add('Tên người vay chính là bắt buộc');
    }
    if (data.borrowerIdType?.isEmpty ?? true) {
      errors.add('Loại giấy tờ tùy thân người vay chính là bắt buộc');
    }
    if (data.borrowerIdNumber?.isEmpty ?? true) {
      errors.add('Số CCCD người vay chính là bắt buộc');
    }
    if (data.borrowerBirthDate?.isEmpty ?? true) {
      errors.add('Ngày sinh người vay chính là bắt buộc');
    }
    if (data.borrowerGender?.isEmpty ?? true) {
      errors.add('Giới tính người vay chính là bắt buộc');
    }
    if (data.borrowerPhone?.isEmpty ?? true) {
      errors.add('Số điện thoại người vay chính là bắt buộc');
    }
    if (data.borrowerPermanentProvinceId?.isEmpty ?? true) {
      errors.add('Tỉnh/thành phố thường trú là bắt buộc');
    }
    if (data.borrowerPermanentWardId?.isEmpty ?? true) {
      errors.add('Quận/huyện thường trú là bắt buộc');
    }
    if (data.borrowerPermanentAddress?.isEmpty ?? true) {
      errors.add('Địa chỉ thường trú là bắt buộc');
    }
    
    // ===== CO-BORROWER VALIDATION =====
    if (data.hasCoBorrower == true) {
      if (data.coBorrowerName?.isEmpty ?? true) {
        errors.add('Tên người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdType?.isEmpty ?? true) {
        errors.add('Loại giấy tờ tùy thân người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdNumber?.isEmpty ?? true) {
        errors.add('Số CCCD người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdIssueDate?.isEmpty ?? true) {
        errors.add('Ngày cấp GTTT người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdExpiryDate?.isEmpty ?? true) {
        errors.add('Ngày hết hạn GTTT người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdIssuePlace?.isEmpty ?? true) {
        errors.add('Nơi cấp GTTT người đồng vay là bắt buộc');
      }
      if (data.coBorrowerBirthDate?.isEmpty ?? true) {
        errors.add('Ngày sinh người đồng vay là bắt buộc');
      }
      if (data.coBorrowerGender?.isEmpty ?? true) {
        errors.add('Giới tính người đồng vay là bắt buộc');
      }
      if (data.coBorrowerPhone?.isEmpty ?? true) {
        errors.add('Số điện thoại người đồng vay là bắt buộc');
      }
    }
    
    // ===== LOAN PROPOSAL VALIDATION =====
    if (data.ownCapital == null || data.ownCapital! < 0) {
      errors.add('Vốn tự có phải lớn hơn hoặc bằng 0');
    }
    if (data.loanAmount == null || data.loanAmount! < 1000000) {
      errors.add('Số tiền vay tối thiểu 1.000.000 VNĐ');
    }
    if (data.loanAmount != null && data.loanAmount! > 500000000) {
      errors.add('Số tiền vay tối đa 500.000.000 VNĐ');
    }
    if (data.loanTermId?.isEmpty ?? true) {
      errors.add('Thời hạn vay là bắt buộc');
    }
    if (data.loanMethodId?.isEmpty ?? true) {
      errors.add('Phương thức vay là bắt buộc');
    }
    if (data.loanPurposeId?.isEmpty ?? true) {
      errors.add('Mục đích vay là bắt buộc');
    }
    if (data.repaymentMethodId?.isEmpty ?? true) {
      errors.add('Hình thức trả nợ là bắt buộc');
    }
    
    // ===== COLLATERAL VALIDATION =====
    if (data.loanType == LoanType.withCollateral) {
      if (data.collateralValue == null || data.collateralValue! <= 0) {
        errors.add('Giá trị tài sản bảo đảm phải lớn hơn 0');
      }
      if (data.collateralTypeId?.isEmpty ?? true) {
        errors.add('Loại tài sản bảo đảm là bắt buộc');
      }
      if (data.collateralOwner?.isEmpty ?? true) {
        errors.add('Tên chủ sở hữu tài sản là bắt buộc');
      }
      if (data.collateralOwnerBirthYear?.isEmpty ?? true) {
        errors.add('Ngày tháng năm sinh của chủ tài sản là bắt buộc');
      }
      if (data.vehicleName?.isEmpty ?? true) {
        errors.add('Tên tài sản bảo đảm là bắt buộc');
      }
    }
    
    // ===== FINANCIAL INFORMATION VALIDATION =====
    if (data.incomeSourceId?.isEmpty ?? true) {
      errors.add('Vui lòng chọn nguồn thu chính');
    }
    
    if (data.dailyIncome == null || data.dailyIncome! <= 0) {
      errors.add('Vui lòng nhập thu nhập bình quân/ngày');
    }
    
    // Validate business income specific fields
    if (_isBusinessIncomeSource(data.incomeSourceId)) {
      if (data.dailyRevenue != null && data.dailyRevenue! < 0) {
        errors.add('Doanh thu bình quân/ngày không được âm');
      }
    }
    
    return errors;
  }

  /// Validate PersonalLoanFormData - comprehensive validation including business rules
  List<String> _validatePersonalLoanFormData(PersonalLoanFormData data) {
    final errors = <String>[];
    
    // ===== BORROWER INFORMATION VALIDATION =====
    if (data.borrowerName?.isEmpty ?? true) {
      errors.add('Tên người vay chính là bắt buộc');
    }
    if (data.borrowerIdNumber?.isEmpty ?? true) {
      errors.add('Số CCCD người vay chính là bắt buộc');
    }
    if (data.borrowerBirthDate?.isEmpty ?? true) {
      errors.add('Ngày sinh người vay chính là bắt buộc');
    }
    if (data.borrowerGender?.isEmpty ?? true) {
      errors.add('Giới tính người vay chính là bắt buộc');
    }
    if (data.borrowerPhone?.isEmpty ?? true) {
      errors.add('Số điện thoại người vay chính là bắt buộc');
    }
    if (data.borrowerPermanentProvince?.isEmpty ?? true) {
      errors.add('Tỉnh/thành phố thường trú là bắt buộc');
    }
    if (data.borrowerPermanentDistrict?.isEmpty ?? true) {
      errors.add('Quận/huyện thường trú là bắt buộc');
    }
    if (data.borrowerPermanentAddress?.isEmpty ?? true) {
      errors.add('Địa chỉ thường trú là bắt buộc');
    }
    
    // ===== CO-BORROWER VALIDATION =====
    if (data.hasCoBorrower == true) {
      if (data.coBorrowerName?.isEmpty ?? true) {
        errors.add('Tên người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdNumber?.isEmpty ?? true) {
        errors.add('Số CCCD người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdIssueDate?.isEmpty ?? true) {
        errors.add('Ngày cấp GTTT người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdExpiryDate?.isEmpty ?? true) {
        errors.add('Ngày hết hạn GTTT người đồng vay là bắt buộc');
      }
      if (data.coBorrowerIdIssuePlace?.isEmpty ?? true) {
        errors.add('Nơi cấp GTTT người đồng vay là bắt buộc');
      }
      if (data.coBorrowerBirthDate?.isEmpty ?? true) {
        errors.add('Ngày sinh người đồng vay là bắt buộc');
      }
      if (data.coBorrowerGender?.isEmpty ?? true) {
        errors.add('Giới tính người đồng vay là bắt buộc');
      }
      if (data.coBorrowerPhone?.isEmpty ?? true) {
        errors.add('Số điện thoại người đồng vay là bắt buộc');
      }
    }
    
    // ===== LOAN PROPOSAL VALIDATION =====
    if (data.ownCapital == null || data.ownCapital! < 0) {
      errors.add('Vốn tự có phải lớn hơn hoặc bằng 0');
    }
    if (data.loanAmount == null || data.loanAmount! < 1000000) {
      errors.add('Số tiền vay tối thiểu 1.000.000 VNĐ');
    }
    if (data.loanAmount != null && data.loanAmount! > 500000000) {
      errors.add('Số tiền vay tối đa 500.000.000 VNĐ');
    }
    if (data.loanTerm?.isEmpty ?? true) {
      errors.add('Thời hạn vay là bắt buộc');
    }
    if (data.loanMethod?.isEmpty ?? true) {
      errors.add('Phương thức vay là bắt buộc');
    }
    if (data.loanPurpose?.isEmpty ?? true) {
      errors.add('Mục đích vay là bắt buộc');
    }
    if (data.repaymentMethod?.isEmpty ?? true) {
      errors.add('Hình thức trả nợ là bắt buộc');
    }
    
    // ===== FINANCIAL INFORMATION VALIDATION =====
    if (data.incomeSource?.isEmpty ?? true) {
      errors.add('Vui lòng chọn nguồn thu chính');
    }
    
    if (data.dailyIncome == null || data.dailyIncome! <= 0) {
      errors.add('Vui lòng nhập thu nhập bình quân/ngày');
    }
    
    // Validate business income specific fields
    if (_isBusinessIncomeSource(data.incomeSource)) {
      if (data.dailyRevenue != null && data.dailyRevenue! < 0) {
        errors.add('Doanh thu bình quân/ngày không được âm');
      }
    }
    
    return errors;
  }


  /// Check if income source is business-related
  bool _isBusinessIncomeSource(String? incomeSourceId) {
    if (incomeSourceId?.isEmpty ?? true) return false;
    
    final lowerValue = incomeSourceId!.toLowerCase();
    return lowerValue.contains('kinh doanh') || 
           lowerValue.contains('business') ||
           lowerValue.contains('kinhdoanh') ||
           incomeSourceId == '31a90b85-2954-47c7-889b-2a53f968b21e';
  }

  /// Validate documents using DocumentValidator
  List<String> _validateDocuments() {
    if (selectedProduct == null) return [];
    
    final documentValidator = DocumentValidator(
      documents: documents,
      productCode: selectedProduct!.code,
      formData: formData,
    );
    
    return documentValidator.validateAll();
  }


  /// Check if form data has any meaningful content
  bool _hasAnyFormData() {
    if (formData == null) return false;
    
    if (formData is InstallmentLoanFormData) {
      final data = formData as InstallmentLoanFormData;
      return (data.borrowerName?.isNotEmpty ?? false) ||
             (data.loanAmount != null && data.loanAmount! > 0) ||
             (data.incomeSourceId?.isNotEmpty ?? false);
    }
    
    // Fallback - check if form data has any non-null values
    final map = formData!.toMap();
    return map.values.any((value) => value != null && value.toString().isNotEmpty);
  }
}

/// Extension để dễ dàng sử dụng TransactionFormValidator
extension TransactionFormValidatorExtension on TransactionFormValidator {
  /// Check if can proceed to next step
  bool canProceedToStep(int stepIndex) {
    return validateStep(stepIndex).isEmpty;
  }
}

/// Factory methods cho TransactionFormValidator
class TransactionFormValidatorFactory {
  /// Create validator for create_transaction_screen.dart
  static TransactionFormValidator create({
    ProductModel? selectedProduct,
    CustomerModel? selectedCustomer,
    BaseFormData? formData,
    List<DocumentModel> documents = const [],
  }) {
    return TransactionFormValidator(
      selectedProduct: selectedProduct,
      selectedCustomer: selectedCustomer,
      formData: formData,
      documents: documents,
    );
  }
  
  /// Create validator from TransactionFormBloc state
  static TransactionFormValidator fromBlocState({
    required dynamic blocState,
    List<DocumentModel> documents = const [],
  }) {
    // Extract data from bloc state (assuming TransactionFormLoaded state)
    ProductModel? selectedProduct;
    CustomerModel? selectedCustomer;
    BaseFormData? formData;
    
    try {
      // Type-safe extraction using pattern matching
      if (blocState is TransactionFormLoaded) {
        selectedProduct = blocState.selectedProduct;
        selectedCustomer = blocState.selectedCustomer;
        formData = blocState.formData;
      } else {
        debugPrint('Warning: Bloc state is not TransactionFormLoaded: ${blocState.runtimeType}');
      }
    } catch (e) {
      debugPrint('Error extracting data from bloc state: $e');
    }
    
    return TransactionFormValidator(
      selectedProduct: selectedProduct,
      selectedCustomer: selectedCustomer,
      formData: formData,
      documents: documents,
    );
  }
}
