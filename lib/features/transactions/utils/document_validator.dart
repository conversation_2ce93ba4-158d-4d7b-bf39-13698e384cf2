import 'package:flutter/foundation.dart';
import '../models/document_model.dart';
import '../models/form_data/form_data.dart';

/// DocumentValidator - Validate required documents for transaction creation
/// Updated to work with typed form data from TransactionFormBloc
class DocumentValidator {
  final List<DocumentModel> documents;
  final String? productCode;
  final BaseFormData? formData;

  DocumentValidator({
    required this.documents,
    this.productCode,
    this.formData,
  });

  /// Validate all required documents
  List<String> validateAll() {
    debugPrint('=== START: DocumentValidator.validateAll() ===');
    debugPrint('📋 Product: $productCode');
    debugPrint('📋 FormData: ${formData?.runtimeType}');
    debugPrint('📋 Total documents: ${documents.length}');

    final errors = <String>[];

    // Get required documents based on product
    final requiredDocuments = _getRequiredDocuments();
    debugPrint('📋 Required documents count: ${requiredDocuments.length}');
    debugPrint('📋 Required documents: ${requiredDocuments.map((d) => d.name).join(', ')}');

    // Validate each required document
    for (final requiredDoc in requiredDocuments) {
      final uploadedDoc = _findDocumentById(requiredDoc.id);
      
      if (uploadedDoc == null) {
        debugPrint('❌ Required document not found: ${requiredDoc.name}');
        errors.add('Vui lòng upload ${requiredDoc.name}');
        continue;
      }

      if (!uploadedDoc.hasFiles) {
        debugPrint('❌ Required document has no files: ${requiredDoc.name}');
        errors.add('Vui lòng upload ${requiredDoc.name}');
        continue;
      }

      debugPrint('✅ Required document validated: ${requiredDoc.name} (${uploadedDoc.files.length} files)');
    }

    debugPrint('📋 Document validation errors: ${errors.length}');
    for (int i = 0; i < errors.length; i++) {
      debugPrint('  ${i + 1}. ${errors[i]}');
    }

    debugPrint('=== END: DocumentValidator.validateAll() ===');
    return errors;
  }

  /// Get required documents based on product and form data
  List<DocumentModel> _getRequiredDocuments() {
    if (productCode == null) {
      return [];
    }

    // Use form data to get required documents if available
    if (formData is InstallmentLoanFormData) {
      final installmentData = formData as InstallmentLoanFormData;
      final requiredDocsData = installmentData.getRequiredDocuments();
      
      return requiredDocsData.map((docData) => DocumentModel(
        id: docData['id']!,
        type: DocumentType.otherDoc,
        name: docData['name']!,
        isRequired: true,
        status: DocumentStatus.pending,
        files: [],
        productCode: productCode,
        mappingKey: docData['mappingKey']!,
      )).toList();
    }

    // Fallback for other product types or when form data is not available
    final requiredDocs = <DocumentModel>[];

    switch (productCode) {
      case 'Vay tín chấp':
        requiredDocs.addAll([
          DocumentModel(
            id: 'cmnd_cccd_1',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
          DocumentModel(
            id: 'household_book_1',
            type: DocumentType.otherDoc,
            name: 'Sổ hộ khẩu',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'residenceProofDocuments',
          ),
          DocumentModel(
            id: 'income_proof_1',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        break;

      case 'Thẻ tín dụng':
        requiredDocs.addAll([
          DocumentModel(
            id: 'cmnd_cccd_2',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
          DocumentModel(
            id: 'income_proof_2',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        break;

      case 'Vay thế chấp':
        requiredDocs.addAll([
          DocumentModel(
            id: 'cmnd_cccd_3',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
          DocumentModel(
            id: 'household_book_2',
            type: DocumentType.otherDoc,
            name: 'Sổ hộ khẩu',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'residenceProofDocuments',
          ),
          DocumentModel(
            id: 'income_proof_3',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'optionalBusinessCertificates',
          ),
          DocumentModel(
            id: 'property_papers_1',
            type: DocumentType.collateralDoc,
            name: 'Giấy tờ tài sản',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        break;

      case 'Gửi tiết kiệm':
        requiredDocs.addAll([
          DocumentModel(
            id: 'cmnd_cccd_4',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
        ]);
        break;

      default:
        // Default required document
        requiredDocs.add(
          DocumentModel(
            id: 'cmnd_cccd_default',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: productCode,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
        );
    }

    return requiredDocs;
  }

  /// Find document by ID in uploaded documents
  DocumentModel? _findDocumentById(String documentId) {
    return documents.firstWhere(
      (doc) => doc.id == documentId,
      orElse: () => DocumentModel(
        id: '',
        type: DocumentType.otherDoc,
        name: '',
        isRequired: false,
        status: DocumentStatus.pending,
        files: [],
      ),
    );
  }

}
