import 'package:flutter/foundation.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../models/form_data/base_form_data.dart';
import '../models/form_data/installment_loan_form_data.dart';
import '../models/document_model.dart';
import '../models/proposal_request.dart';
import '../models/enums/loan_type_enum.dart';

/// Mapper service để chuyển đổi từ TransactionFormBloc state sang TransactionProposalRequest
/// Không hardcode keys, sử dụng typed models
class TransactionProposalMapper {
  
  /// Map từ TransactionFormBloc state sang TransactionProposalRequest
  static TransactionProposalRequest mapToProposalRequest({
    required ProductModel? selectedProduct,
    required CustomerModel? selectedCustomer,
    required BaseFormData? formData,
    required Map<String, List<DocumentModel>> documentsMapping,
    String mode = 'FULL',
  }) {
    debugPrint('=== START: TransactionProposalMapper.mapToProposalRequest ===');
    debugPrint('📋 Product: ${selectedProduct?.code}');
    debugPrint('📋 Customer: ${selectedCustomer?.fullName}');
    debugPrint('📋 FormData: ${formData?.runtimeType}');
    debugPrint('📋 Mode: $mode');
    
    // Map main borrower
    final mainBorrower = _mapMainBorrower(formData, selectedCustomer);
    
    // Map co-borrower if exists
    BorrowerInfo? coBorrower;
    if (formData is InstallmentLoanFormData && (formData.hasCoBorrower ?? false)) {
      coBorrower = _mapCoBorrower(formData);
    }
    
    // Map loan plan
    final loanPlan = _mapLoanPlan(formData);
    
    // Map financial info
    final financialInfo = _mapFinancialInfo(formData);
    
    // Map collateral info if applicable
    final collateralInfo = _mapCollateralInfo(formData);
    
    // Map documents
    final documentsInfo = _mapDocumentsInfo(documentsMapping);
    
    final result = TransactionProposalRequest(
      productId: selectedProduct?.id,
      mode: mode,
      data: TransactionData(
        customerId: selectedCustomer?.id,
        mainBorrower: mainBorrower,
        coBorrower: coBorrower,
        loanPlan: loanPlan,
        financialInfo: financialInfo,
        collateralInfo: collateralInfo,
        documents: documentsInfo,
      ),
    );
    
    debugPrint('✅ TransactionProposalRequest mapped successfully');
    debugPrint('=== END: TransactionProposalMapper.mapToProposalRequest ===');
    
    return result;
  }
  
  /// Map main borrower từ form data
  static BorrowerInfo? _mapMainBorrower(BaseFormData? formData, CustomerModel? customer) {
    if (formData is! InstallmentLoanFormData) {
      debugPrint('⚠️ FormData is not InstallmentLoanFormData, cannot map main borrower');
      return null;
    }
    
    final data = formData;
    
    // Debug address mapping logic
    debugPrint('🏠 MAIN BORROWER Address mapping debug:');
    debugPrint('   - borrowerCurrentSamePermanent: ${data.borrowerCurrentSamePermanent}');
    debugPrint('   - borrowerPermanentProvinceId: ${data.borrowerPermanentProvinceId}');
    debugPrint('   - borrowerPermanentWardId: ${data.borrowerPermanentWardId}');
    debugPrint('   - borrowerPermanentAddress: ${data.borrowerPermanentAddress}');
    debugPrint('   - borrowerCurrentProvinceId: ${data.borrowerCurrentProvinceId}');
    debugPrint('   - borrowerCurrentWardId: ${data.borrowerCurrentWardId}');
    debugPrint('   - borrowerCurrentAddress: ${data.borrowerCurrentAddress}');
    
    // Sử dụng data từ form, fallback sang customer nếu cần
    final result = BorrowerInfo(
      fullName: data.borrowerName ?? customer?.fullName,
      idNo: data.borrowerIdNumber ?? customer?.idCardNumber,
      issueDate: data.borrowerIdIssueDate ?? 
                 (customer?.idCardIssueDate != null ? _formatDateForApi(customer!.idCardIssueDate!) : null),
      expiryDate: data.borrowerIdExpiryDate ?? 
                  (customer?.idCardExpiryDate != null ? _formatDateForApi(customer!.idCardExpiryDate!) : null),
      issuePlace: data.borrowerIdIssuePlace ?? customer?.idCardIssuePlace,
      dob: data.borrowerBirthDate ?? 
           (customer?.birthDate != null ? _formatDateForApi(customer!.birthDate!) : null),
      sex: data.borrowerGender ?? customer?.gender?.id,
      permanentProvinceId: data.borrowerPermanentProvinceId ?? customer?.province?.id,
      permanentWardId: data.borrowerPermanentWardId ?? customer?.ward?.id,
      permanentAddressDetail: data.borrowerPermanentAddress ?? customer?.permanentAddress,
      maritalStatusId: data.borrowerMaritalStatusId,
      phoneNumber: data.borrowerPhone ?? customer?.phoneNumber,
      currentProvinceId: (data.borrowerCurrentSamePermanent ?? true)
          ? (data.borrowerPermanentProvinceId ?? customer?.province?.id)
          : data.borrowerCurrentProvinceId,
      currentWardId: (data.borrowerCurrentSamePermanent ?? true) 
          ? (data.borrowerPermanentWardId ?? customer?.ward?.id)
          : data.borrowerCurrentWardId,
      currentAddressDetail: (data.borrowerCurrentSamePermanent ?? true) 
          ? (data.borrowerPermanentAddress ?? customer?.permanentAddress)
          : data.borrowerCurrentAddress,
    );
    
    // Debug final result
    debugPrint('🏠 MAIN BORROWER Final address result:');
    debugPrint('   - currentProvinceId: ${result.currentProvinceId}');
    debugPrint('   - currentWardId: ${result.currentWardId}');
    debugPrint('   - currentAddressDetail: ${result.currentAddressDetail}');
    
    // Check if borrower has any meaningful data
    if (!result.hasData) {
      debugPrint('⚠️ Main borrower has no meaningful data, returning null');
      return null;
    }
    
    debugPrint('✅ Main borrower mapped:');
    debugPrint('   - fullName: ${result.fullName}');
    debugPrint('   - idNo: ${result.idNo}');
    debugPrint('   - permanentProvinceId: ${result.permanentProvinceId}');
    debugPrint('   - permanentWardId: ${result.permanentWardId}');
    
    return result;
  }
  
  /// Map co-borrower từ form data
  static BorrowerInfo? _mapCoBorrower(InstallmentLoanFormData data) {
    debugPrint('🔄 Mapping co-borrower data');
    debugPrint('   - coBorrowerName: ${data.coBorrowerName}');
    debugPrint('   - coBorrowerIdNumber: ${data.coBorrowerIdNumber}');
    debugPrint('   - coBorrowerIdIssueDate: ${data.coBorrowerIdIssueDate}');
    debugPrint('   - coBorrowerIdExpiryDate: ${data.coBorrowerIdExpiryDate}');
    debugPrint('   - coBorrowerIdIssuePlace: ${data.coBorrowerIdIssuePlace}');
    debugPrint('   - coBorrowerBirthDate: ${data.coBorrowerBirthDate}');
    debugPrint('   - coBorrowerGender: ${data.coBorrowerGender}');
    debugPrint('   - coBorrowerPhone: ${data.coBorrowerPhone}');
    
    // Debug co-borrower address mapping logic
    debugPrint('🏠 CO-BORROWER Address mapping debug:');
    debugPrint('   - coBorrowerCurrentSamePermanent: ${data.coBorrowerCurrentSamePermanent}');
    debugPrint('   - coBorrowerPermanentProvinceId: ${data.coBorrowerPermanentProvinceId}');
    debugPrint('   - coBorrowerPermanentWardId: ${data.coBorrowerPermanentWardId}');
    debugPrint('   - coBorrowerPermanentAddress: ${data.coBorrowerPermanentAddress}');
    debugPrint('   - coBorrowerCurrentProvinceId: ${data.coBorrowerCurrentProvinceId}');
    debugPrint('   - coBorrowerCurrentWardId: ${data.coBorrowerCurrentWardId}');
    debugPrint('   - coBorrowerCurrentAddress: ${data.coBorrowerCurrentAddress}');
    
    final result = BorrowerInfo(
      fullName: data.coBorrowerName,
      idNo: data.coBorrowerIdNumber,
      issueDate: data.coBorrowerIdIssueDate,
      expiryDate: data.coBorrowerIdExpiryDate,
      issuePlace: data.coBorrowerIdIssuePlace,
      dob: data.coBorrowerBirthDate,
      sex: data.coBorrowerGender,
      permanentProvinceId: data.coBorrowerPermanentProvinceId,
      permanentWardId: data.coBorrowerPermanentWardId,
      permanentAddressDetail: data.coBorrowerPermanentAddress,
      maritalStatusId: data.coBorrowerMaritalStatusId,
      phoneNumber: data.coBorrowerPhone,
      currentProvinceId: (data.coBorrowerCurrentSamePermanent ?? true) 
          ? data.coBorrowerPermanentProvinceId
          : data.coBorrowerCurrentProvinceId,
      currentWardId: (data.coBorrowerCurrentSamePermanent ?? true) 
          ? data.coBorrowerPermanentWardId
          : data.coBorrowerCurrentWardId,
      currentAddressDetail: (data.coBorrowerCurrentSamePermanent ?? true) 
          ? data.coBorrowerPermanentAddress
          : data.coBorrowerCurrentAddress,
    );
    
    // Check if co-borrower has any meaningful data
    if (!result.hasData) {
      debugPrint('⚠️ Co-borrower has no meaningful data, returning null');
      return null;
    }
    
    debugPrint('✅ Co-borrower mapped:');
    debugPrint('   - fullName: ${result.fullName}');
    debugPrint('   - idNo: ${result.idNo}');
    debugPrint('   - issueDate: ${result.issueDate}');
    debugPrint('   - expiryDate: ${result.expiryDate}');
    debugPrint('   - issuePlace: ${result.issuePlace}');
    debugPrint('   - dob: ${result.dob}');
    debugPrint('   - sex: ${result.sex}');
    debugPrint('   - phoneNumber: ${result.phoneNumber}');
    
    // Warning nếu các trường quan trọng bị thiếu
    if (result.issueDate == null) {
      debugPrint('⚠️ WARNING: Co-borrower issueDate is null');
    }
    if (result.expiryDate == null) {
      debugPrint('⚠️ WARNING: Co-borrower expiryDate is null');
    }
    if (result.issuePlace == null) {
      debugPrint('⚠️ WARNING: Co-borrower issuePlace is null');
    }
    
    return result;
  }
  
  /// Map loan plan từ form data
  static LoanPlan? _mapLoanPlan(BaseFormData? formData) {
    if (formData is! InstallmentLoanFormData) {
      debugPrint('⚠️ FormData is not InstallmentLoanFormData, cannot map loan plan');
      return null;
    }
    
    final data = formData;
    
    final result = LoanPlan(
      ownCapital: data.ownCapital,
      requestedAmount: data.loanAmount,
      loanTermId: data.loanTermId,
      loanMethodId: data.loanMethodId,
      loanPurposeId: data.loanPurposeId,
      loanPurposeName: data.loanPurposeName,
      repaymentMethodId: data.repaymentMethodId,
      disbursementMethodId: data.disbursementMethodId,
      receivingAccountNumber: data.disbursementAccount,
    );
    
    // Check if loan plan has any meaningful data
    if (!result.hasData) {
      debugPrint('⚠️ Loan plan has no meaningful data, returning null');
      return null;
    }
    
    debugPrint('✅ Loan plan mapped:');
    debugPrint('   - ownCapital: ${result.ownCapital}');
    debugPrint('   - requestedAmount: ${result.requestedAmount}');
    debugPrint('   - loanTermId: ${result.loanTermId}');
    debugPrint('   - loanPurposeId: ${result.loanPurposeId}');
    
    return result;
  }
  
  /// Map financial info từ form data
  static FinancialInfo? _mapFinancialInfo(BaseFormData? formData) {
    if (formData is! InstallmentLoanFormData) {
      debugPrint('⚠️ FormData is not InstallmentLoanFormData, cannot map financial info');
      return null;
    }
    
    final data = formData;
    
    final result = FinancialInfo(
      incomeSourceId: data.incomeSourceId,
      averageRevenuePerDay: data.dailyRevenue,
      averageIncomePerDay: data.dailyIncome,
      businessProvinceId: data.businessLocationProvinceId,
      businessWardId: data.businessLocationWardId,
      businessAddressDetail: data.businessLocationAddress,
    );
    
    // Check if financial info has any meaningful data
    if (!result.hasData) {
      debugPrint('⚠️ Financial info has no meaningful data, returning null');
      return null;
    }
    
    debugPrint('✅ Financial info mapped:');
    debugPrint('   - incomeSourceId: ${result.incomeSourceId}');
    debugPrint('   - averageIncomePerDay: ${result.averageIncomePerDay}');
    debugPrint('   - averageRevenuePerDay: ${result.averageRevenuePerDay}');
    
    return result;
  }
  
  /// Map collateral info từ form data (chỉ khi có TSBĐ)
  static CollateralInfo? _mapCollateralInfo(BaseFormData? formData) {
    if (formData is! InstallmentLoanFormData) {
      debugPrint('⚠️ FormData is not InstallmentLoanFormData, cannot map collateral info');
      return null;
    }
    
    final data = formData;
    
    // Chỉ tạo collateral info khi loan type có TSBĐ
    if (data.loanType != LoanType.withCollateral) {
      debugPrint('ℹ️ No collateral info needed - loan type: ${data.loanType?.displayName}');
      return null;
    }
    
    final result = CollateralInfo(
      typeId: data.collateralTypeId,
      value: data.collateralValue,
      condition: data.collateralConditionId,
      ownerName: data.collateralOwner,
      ownerDob: data.collateralOwnerBirthYear,
      name: data.vehicleName,
      licensePlate: data.vehiclePlateNumber,
      chassisNumber: data.vehicleFrameNumber,
      engineNumber: data.vehicleEngineNumber,
      registrationCertificateNumber: data.vehicleRegistrationNumber,
      registrationIssuePlace: data.vehicleRegistrationPlace,
      registrationIssueDate: data.vehicleRegistrationDate,
      handoverConditionId: data.vehicleConditionAtHandover,
      totalValue: data.totalCollateralValue ?? data.collateralValue,
    );
    
    // Check if collateral info has any meaningful data
    if (!result.hasData) {
      debugPrint('⚠️ Collateral info has no meaningful data, returning null');
      return null;
    }
    
    debugPrint('✅ Collateral info mapped:');
    debugPrint('   - typeId: ${result.typeId}');
    debugPrint('   - value: ${result.value}');
    debugPrint('   - name: ${result.name}');
    debugPrint('   - ownerName: ${result.ownerName}');
    debugPrint('   - ownerDob: ${result.ownerDob}');
    
    return result;
  }
  
  /// Map documents từ documents mapping
  static DocumentsInfo? _mapDocumentsInfo(Map<String, List<DocumentModel>> documentsMapping) {
    debugPrint('🔄 Mapping documents from mapping: ${documentsMapping.keys.toList()}');
    
    final result = DocumentsInfo(
      mainBorrowerIdentityImages: _mapDocumentsByKey(documentsMapping, 'mainBorrowerIdentityImages'),
      coBorrowerIdentityImages: _mapDocumentsByKey(documentsMapping, 'coBorrowerIdentityImages'),
      maritalRelationshipDocuments: _mapDocumentsByKey(documentsMapping, 'maritalRelationshipDocuments'),
      residenceProofDocuments: _mapDocumentsByKey(documentsMapping, 'residenceProofDocuments'),
      motoAppraisalDocuments: _mapDocumentsByKey(documentsMapping, 'motoAppraisalDocuments'),
      vehicleRegistrationDocuments: _mapDocumentsByKey(documentsMapping, 'vehicleRegistrationDocuments'),
      optionalBusinessCertificates: _mapDocumentsByKey(documentsMapping, 'optionalBusinessCertificates'),
    );
    
    // Check if documents info has any meaningful data
    if (!result.hasData) {
      debugPrint('⚠️ Documents info has no meaningful data, returning null');
      return null;
    }
    
    debugPrint('✅ Documents mapped:');
    debugPrint('   - mainBorrowerIdentityImages: ${result.mainBorrowerIdentityImages?.length ?? 0}');
    debugPrint('   - coBorrowerIdentityImages: ${result.coBorrowerIdentityImages?.length ?? 0}');
    debugPrint('   - maritalRelationshipDocuments: ${result.maritalRelationshipDocuments?.length ?? 0}');
    debugPrint('   - residenceProofDocuments: ${result.residenceProofDocuments?.length ?? 0}');
    debugPrint('   - motoAppraisalDocuments: ${result.motoAppraisalDocuments?.length ?? 0}');
    debugPrint('   - vehicleRegistrationDocuments: ${result.vehicleRegistrationDocuments?.length ?? 0}');
    
    return result;
  }
  
  /// Map documents by mapping key
  static List<DocumentFile>? _mapDocumentsByKey(
    Map<String, List<DocumentModel>> documentsMapping, 
    String mappingKey
  ) {
    final documents = documentsMapping[mappingKey] ?? [];
    if (documents.isEmpty) return null;
    
    final result = <DocumentFile>[];
    
    for (final doc in documents) {
      if (!doc.hasFiles) continue;
      
      for (final uploadedFile in doc.files) {
        result.add(DocumentFile(
          fileName: uploadedFile.name,
          fileSize: uploadedFile.size,
          mimeType: _getMimeType(uploadedFile),
          fileUrl: uploadedFile.url ?? 'local://${uploadedFile.path}',
        ));
      }
    }
    
    return result.isNotEmpty ? result : null;
  }
  
  /// Get MIME type from uploaded file
  static String _getMimeType(UploadedFile uploadedFile) {
    if (uploadedFile.isPdf) return 'application/pdf';
    if (uploadedFile.isImage) return 'image/jpeg';
    return 'application/octet-stream';
  }
  
  /// Format date for API (DD/MM/YYYY)
  static String _formatDateForApi(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}

/// Extension để dễ dàng sử dụng mapper
extension TransactionProposalMapperExtension on TransactionProposalMapper {
  /// Map với default mode FULL
  static TransactionProposalRequest mapToFullProposal({
    required ProductModel? selectedProduct,
    required CustomerModel? selectedCustomer,
    required BaseFormData? formData,
    required Map<String, List<DocumentModel>> documentsMapping,
  }) {
    return TransactionProposalMapper.mapToProposalRequest(
      selectedProduct: selectedProduct,
      selectedCustomer: selectedCustomer,
      formData: formData,
      documentsMapping: documentsMapping,
      mode: 'FULL',
    );
  }
  
  /// Map với mode DRAFT
  static TransactionProposalRequest mapToDraftProposal({
    required ProductModel? selectedProduct,
    required CustomerModel? selectedCustomer,
    required BaseFormData? formData,
    required Map<String, List<DocumentModel>> documentsMapping,
  }) {
    return TransactionProposalMapper.mapToProposalRequest(
      selectedProduct: selectedProduct,
      selectedCustomer: selectedCustomer,
      formData: formData,
      documentsMapping: documentsMapping,
      mode: 'DRAFT',
    );
  }
}
