import 'package:equatable/equatable.dart';
import '../models/get_proposals_request.dart';
import '../models/proposal_request.dart';

/// Base class cho tất cả transaction events
abstract class TransactionEvent extends Equatable {
  const TransactionEvent();

  @override
  List<Object?> get props => [];
}

/// Event để load danh sách transactions/proposals lần đầu
class LoadTransactionList extends TransactionEvent {
  const LoadTransactionList();
}

/// Event để refresh danh sách transactions/proposals
class RefreshTransactionList extends TransactionEvent {
  const RefreshTransactionList();
}

/// Event để load more transactions/proposals (pagination)
class LoadMoreTransactions extends TransactionEvent {
  final int limit;

  const LoadMoreTransactions({
    this.limit = 20,
  });

  @override
  List<Object?> get props => [limit];
}

/// Event để filter transactions/proposals
class FilterTransactions extends TransactionEvent {
  final GetProposalsRequest? request;

  const FilterTransactions({
    this.request,
  });

  @override
  List<Object?> get props => [request];
}

/// Event để search transactions/proposals
class SearchTransactions extends TransactionEvent {
  final String searchQuery;

  const SearchTransactions({
    required this.searchQuery,
  });

  @override
  List<Object?> get props => [searchQuery];
}

/// Event để clear cache
class ClearTransactionCache extends TransactionEvent {
  const ClearTransactionCache();
}

/// Event để retry khi có lỗi
class RetryLoadTransactions extends TransactionEvent {
  final GetProposalsRequest? request; // Truyền filter request để retry với cùng filters
  
  const RetryLoadTransactions({
    this.request,
  });
  
  @override
  List<Object?> get props => [request];
}

/// Event để tạo proposal mới
class CreateProposal extends TransactionEvent {
  final TransactionProposalRequest request;
  
  const CreateProposal({
    required this.request,
  });
  
  @override
  List<Object?> get props => [request];
}

/// Event để validate proposal data
class ValidateProposalData extends TransactionEvent {
  final TransactionProposalRequest request;
  
  const ValidateProposalData({
    required this.request,
  });
  
  @override
  List<Object?> get props => [request];
}
