import 'package:equatable/equatable.dart';
import '../models/proposal_list.dart';
import '../models/get_proposals_request.dart';
import '../models/transaction_exception.dart';

/// Base class cho tất cả transaction states
abstract class TransactionState extends Equatable {
  const TransactionState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class TransactionInitial extends TransactionState {
  const TransactionInitial();
}

/// Loading state cho lần load đầu tiên
class TransactionLoading extends TransactionState {
  final bool isRefreshing;

  const TransactionLoading({
    this.isRefreshing = false,
  });

  @override
  List<Object?> get props => [isRefreshing];
}

/// State khi load thành công
class TransactionLoaded extends TransactionState {
  final List<ProposalItem> transactions;
  final int totalCount;
  final bool hasMoreData;
  final bool isRefreshing;
  final bool isLoadingMore;
  final int currentLimit;
  final int currentOffset;
  final String? currentSearchQuery;
  final GetProposalsRequest? currentFilter;

  const TransactionLoaded({
    required this.transactions,
    required this.totalCount,
    this.hasMoreData = false,
    this.isRefreshing = false,
    this.isLoadingMore = false,
    this.currentLimit = 20,
    this.currentOffset = 0,
    this.currentSearchQuery,
    this.currentFilter,
  });

  @override
  List<Object?> get props => [
        transactions,
        totalCount,
        hasMoreData,
        isRefreshing,
        isLoadingMore,
        currentLimit,
        currentOffset,
        currentSearchQuery,
        currentFilter,
      ];

  /// Copy with method để tạo state mới
  TransactionLoaded copyWith({
    List<ProposalItem>? transactions,
    int? totalCount,
    bool? hasMoreData,
    bool? isRefreshing,
    bool? isLoadingMore,
    int? currentLimit,
    int? currentOffset,
    String? currentSearchQuery,
    GetProposalsRequest? currentFilter,
    bool clearSearchQuery = false,
    bool clearFilter = false,
  }) {
    return TransactionLoaded(
      transactions: transactions ?? this.transactions,
      totalCount: totalCount ?? this.totalCount,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      currentLimit: currentLimit ?? this.currentLimit,
      currentOffset: currentOffset ?? this.currentOffset,
      currentSearchQuery: clearSearchQuery ? null : (currentSearchQuery ?? this.currentSearchQuery),
      currentFilter: clearFilter ? null : (currentFilter ?? this.currentFilter),
    );
  }
}

/// State khi đang refresh
class TransactionRefreshing extends TransactionState {
  final List<ProposalItem> previousTransactions;

  const TransactionRefreshing({
    required this.previousTransactions,
  });

  @override
  List<Object?> get props => [previousTransactions];
}

/// State khi đang load more
class TransactionLoadingMore extends TransactionState {
  final List<ProposalItem> currentTransactions;

  const TransactionLoadingMore({
    required this.currentTransactions,
  });

  @override
  List<Object?> get props => [currentTransactions];
}

/// State khi có lỗi
class TransactionError extends TransactionState {
  final String message;
  final TransactionExceptionType type;
  final Object? originalError;
  final List<ProposalItem>? previousTransactions; // Giữ data cũ nếu có

  const TransactionError({
    required this.message,
    required this.type,
    this.originalError,
    this.previousTransactions,
  });

  @override
  List<Object?> get props => [message, type, originalError, previousTransactions];
}

/// State khi không có data
class TransactionEmpty extends TransactionState {
  final String? message;
  final bool hasFilters;

  const TransactionEmpty({
    this.message,
    this.hasFilters = false,
  });

  @override
  List<Object?> get props => [message, hasFilters];
}

/// State cho action thành công (như mark as read, etc.)
class TransactionActionSuccess extends TransactionState {
  final String message;
  final String actionType;

  const TransactionActionSuccess({
    required this.message,
    required this.actionType,
  });

  @override
  List<Object?> get props => [message, actionType];
}

/// State khi đang tạo proposal
class ProposalCreating extends TransactionState {
  const ProposalCreating();
}

/// State khi tạo proposal thành công
class ProposalCreated extends TransactionState {
  final String proposalId;
  final String? status;
  final String? message;
  final DateTime? createdAt;

  const ProposalCreated({
    required this.proposalId,
    this.status,
    this.message,
    this.createdAt,
  });

  @override
  List<Object?> get props => [proposalId, status, message, createdAt];
}

/// State khi validate proposal data
class ProposalValidated extends TransactionState {
  final bool isValid;
  final List<String>? errors;

  const ProposalValidated({
    required this.isValid,
    this.errors,
  });

  @override
  List<Object?> get props => [isValid, errors];
}

/// State khi có lỗi tạo proposal
class ProposalError extends TransactionState {
  final String message;
  final TransactionExceptionType type;
  final Object? originalError;

  const ProposalError({
    required this.message,
    required this.type,
    this.originalError,
  });

  @override
  List<Object?> get props => [message, type, originalError];
}
