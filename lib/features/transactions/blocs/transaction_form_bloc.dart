import 'package:flutter_bloc/flutter_bloc.dart';
import '../../products/models/product_model.dart';
import '../../customers/models/customer_model.dart';
import '../models/form_data/base_form_data.dart';
import '../models/form_data/installment_loan_form_data.dart';
import '../models/document_model.dart';

// Events với type safety
abstract class TransactionFormEvent {}

class SelectProduct extends TransactionFormEvent {
  final ProductModel product;
  SelectProduct(this.product);
}

class SelectCustomer extends TransactionFormEvent {
  final CustomerModel customer;
  SelectCustomer(this.customer);
}

class UpdateFormData extends TransactionFormEvent {
  final BaseFormData formData; // InstallmentLoanFormData hoặc subclass khác
  final bool shouldMerge; // Flag để control việc merge, mặc định false
  UpdateFormData(this.formData, {this.shouldMerge = false});
}

class SetDefaults extends TransactionFormEvent {
  final CustomerModel? customer; // Optional customer data for pre-filling
  SetDefaults({this.customer});
}

class ValidateForm extends TransactionFormEvent {}

class ClearForm extends TransactionFormEvent {}

class UpdateDocuments extends TransactionFormEvent {
  final Map<String, List<DocumentModel>> documents;
  UpdateDocuments(this.documents);
}

// States với typed models
abstract class TransactionFormState {}

class TransactionFormInitial extends TransactionFormState {}

class TransactionFormLoaded extends TransactionFormState {
  final ProductModel? selectedProduct;
  final CustomerModel? selectedCustomer;
  final BaseFormData? formData; // InstallmentLoanFormData hoặc PersonalLoanFormData
  final Map<String, List<DocumentModel>> documents;
  final bool isValid;
  final List<String> validationErrors;

  TransactionFormLoaded({
    this.selectedProduct,
    this.selectedCustomer,
    this.formData,
    this.documents = const {},
    this.isValid = false,
    this.validationErrors = const [],
  });

  TransactionFormLoaded copyWith({
    ProductModel? selectedProduct,
    CustomerModel? selectedCustomer,
    BaseFormData? formData,
    Map<String, List<DocumentModel>>? documents,
    bool? isValid,
    List<String>? validationErrors,
  }) {
    return TransactionFormLoaded(
      selectedProduct: selectedProduct ?? this.selectedProduct,
      selectedCustomer: selectedCustomer ?? this.selectedCustomer,
      formData: formData ?? this.formData,
      documents: documents ?? this.documents,
      isValid: isValid ?? this.isValid,
      validationErrors: validationErrors ?? this.validationErrors,
    );
  }

  @override
  String toString() {
    return 'TransactionFormLoaded(product: ${selectedProduct?.code}, customer: ${selectedCustomer?.fullName}, formDataType: ${formData.runtimeType}, isValid: $isValid)';
  }
}

class TransactionFormBloc extends Bloc<TransactionFormEvent, TransactionFormState> {
  TransactionFormBloc() : super(TransactionFormInitial()) {
    on<SelectProduct>(_onSelectProduct);
    on<SelectCustomer>(_onSelectCustomer);
    on<UpdateFormData>(_onUpdateFormData);
    on<SetDefaults>(_onSetDefaults);
    on<ValidateForm>(_onValidateForm);
    on<ClearForm>(_onClearForm);
    on<UpdateDocuments>(_onUpdateDocuments);
  }

  void _onSelectProduct(SelectProduct event, Emitter<TransactionFormState> emit) {
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      // Create appropriate form data based on product type
      BaseFormData? newFormData;
      switch (event.product.code) {
        case 'GOLD_LOAN':
        case 'MANGO':
          newFormData = InstallmentLoanFormData();
          break;
        case 'PERSONAL_LOAN':
          // TODO: newFormData = PersonalLoanFormData();
          break;
        default:
          break;
      }
      
      emit(currentState.copyWith(
        selectedProduct: event.product,
        formData: newFormData,
        isValid: false,
        validationErrors: [],
      ));
    } else {
      // Initial state
      BaseFormData? initialFormData;
      switch (event.product.code) {
        case 'GOLD_LOAN':
        case 'MANGO':
          initialFormData = InstallmentLoanFormData();
          break;
        case 'PERSONAL_LOAN':
          // TODO: initialFormData = PersonalLoanFormData();
          break;
        default:
          break;
      }
      
      emit(TransactionFormLoaded(
        selectedProduct: event.product,
        formData: initialFormData,
      ));
    }
  }

  void _onSelectCustomer(SelectCustomer event, Emitter<TransactionFormState> emit) {
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      // Auto-fill form data với customer information
      BaseFormData? updatedFormData = currentState.formData;
      if (updatedFormData is InstallmentLoanFormData) {
        updatedFormData = _mapCustomerToInstallmentForm(updatedFormData, event.customer);
      }
      
      emit(currentState.copyWith(
        selectedCustomer: event.customer,
        formData: updatedFormData,
      ));
    } else {
      // Initial state - create new loaded state with customer
      emit(TransactionFormLoaded(
        selectedCustomer: event.customer,
      ));
    }
  }

  void _onUpdateFormData(UpdateFormData event, Emitter<TransactionFormState> emit) {
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      final productCode = currentState.selectedProduct?.code;
      
      BaseFormData? finalFormData = event.formData;
      
      // Only merge if shouldMerge flag is true AND product type supports merge
      if (event.shouldMerge && currentState.formData != null) {
        // Check product type and merge accordingly
        switch (productCode) {
          case 'GOLD_LOAN':
          case 'MANGO':
            if (currentState.formData is InstallmentLoanFormData && event.formData is InstallmentLoanFormData) {
              finalFormData = _mergeInstallmentLoanFormData(
                currentState.formData as InstallmentLoanFormData,
                event.formData as InstallmentLoanFormData,
              );
            }
            break;
            
          case 'PERSONAL_LOAN':
            // TODO: Implement PersonalLoanFormData merge when ready
            break;
            
          default:
            break;
        }
      }
      
      // Validate final form data
      final isValid = finalFormData.isValid;
      final errors = finalFormData.validationErrors;
      
      emit(currentState.copyWith(
        formData: finalFormData,
        isValid: isValid,
        validationErrors: errors,
      ));
    }
  }

  /// Merge InstallmentLoanFormData with current form data
  /// Only update fields that are not null in newFormData
  InstallmentLoanFormData _mergeInstallmentLoanFormData(
    InstallmentLoanFormData currentFormData,
    InstallmentLoanFormData newFormData,
  ) {
    return currentFormData.copyWith(
          // Basic borrower info
          borrowerName: newFormData.borrowerName ?? currentFormData.borrowerName,
          borrowerPhone: newFormData.borrowerPhone ?? currentFormData.borrowerPhone,
          borrowerIdNumber: newFormData.borrowerIdNumber ?? currentFormData.borrowerIdNumber,
          borrowerIdType: newFormData.borrowerIdType ?? currentFormData.borrowerIdType,
          borrowerIdTypeModel: newFormData.borrowerIdTypeModel ?? currentFormData.borrowerIdTypeModel,
          borrowerIdIssueDate: newFormData.borrowerIdIssueDate ?? currentFormData.borrowerIdIssueDate,
          borrowerIdExpiryDate: newFormData.borrowerIdExpiryDate ?? currentFormData.borrowerIdExpiryDate,
          borrowerIdIssuePlace: newFormData.borrowerIdIssuePlace ?? currentFormData.borrowerIdIssuePlace,
          borrowerBirthDate: newFormData.borrowerBirthDate ?? currentFormData.borrowerBirthDate,
          borrowerGender: newFormData.borrowerGender ?? currentFormData.borrowerGender,
          borrowerGenderModel: newFormData.borrowerGenderModel ?? currentFormData.borrowerGenderModel,
          borrowerMaritalStatusId: newFormData.borrowerMaritalStatusId ?? currentFormData.borrowerMaritalStatusId,
          borrowerMaritalStatusModel: newFormData.borrowerMaritalStatusModel ?? currentFormData.borrowerMaritalStatusModel,
          
          // Borrower address
          borrowerPermanentProvinceId: newFormData.borrowerPermanentProvinceId ?? currentFormData.borrowerPermanentProvinceId,
          borrowerPermanentProvinceModel: newFormData.borrowerPermanentProvinceModel ?? currentFormData.borrowerPermanentProvinceModel,
          borrowerPermanentWardId: newFormData.borrowerPermanentWardId ?? currentFormData.borrowerPermanentWardId,
          borrowerPermanentWardModel: newFormData.borrowerPermanentWardModel ?? currentFormData.borrowerPermanentWardModel,
          borrowerPermanentAddress: newFormData.borrowerPermanentAddress ?? currentFormData.borrowerPermanentAddress,
          borrowerCurrentSamePermanent: newFormData.borrowerCurrentSamePermanent ?? currentFormData.borrowerCurrentSamePermanent,
          borrowerCurrentProvinceId: newFormData.borrowerCurrentProvinceId ?? currentFormData.borrowerCurrentProvinceId,
          borrowerCurrentProvinceModel: newFormData.borrowerCurrentProvinceModel ?? currentFormData.borrowerCurrentProvinceModel,
          borrowerCurrentWardId: newFormData.borrowerCurrentWardId ?? currentFormData.borrowerCurrentWardId,
          borrowerCurrentWardModel: newFormData.borrowerCurrentWardModel ?? currentFormData.borrowerCurrentWardModel,
          borrowerCurrentAddress: newFormData.borrowerCurrentAddress ?? currentFormData.borrowerCurrentAddress,
          
          // Co-borrower info
          hasCoBorrower: newFormData.hasCoBorrower ?? currentFormData.hasCoBorrower,
          coBorrowerName: newFormData.coBorrowerName ?? currentFormData.coBorrowerName,
          coBorrowerPhone: newFormData.coBorrowerPhone ?? currentFormData.coBorrowerPhone,
          coBorrowerIdNumber: newFormData.coBorrowerIdNumber ?? currentFormData.coBorrowerIdNumber,
          coBorrowerIdType: newFormData.coBorrowerIdType ?? currentFormData.coBorrowerIdType,
          coBorrowerIdTypeModel: newFormData.coBorrowerIdTypeModel ?? currentFormData.coBorrowerIdTypeModel,
          coBorrowerIdIssueDate: newFormData.coBorrowerIdIssueDate ?? currentFormData.coBorrowerIdIssueDate,
          coBorrowerIdExpiryDate: newFormData.coBorrowerIdExpiryDate ?? currentFormData.coBorrowerIdExpiryDate,
          coBorrowerIdIssuePlace: newFormData.coBorrowerIdIssuePlace ?? currentFormData.coBorrowerIdIssuePlace,
          coBorrowerBirthDate: newFormData.coBorrowerBirthDate ?? currentFormData.coBorrowerBirthDate,
          coBorrowerGender: newFormData.coBorrowerGender ?? currentFormData.coBorrowerGender,
          coBorrowerGenderModel: newFormData.coBorrowerGenderModel ?? currentFormData.coBorrowerGenderModel,
          coBorrowerMaritalStatusId: newFormData.coBorrowerMaritalStatusId ?? currentFormData.coBorrowerMaritalStatusId,
          coBorrowerMaritalStatusModel: newFormData.coBorrowerMaritalStatusModel ?? currentFormData.coBorrowerMaritalStatusModel,
          
          // Co-borrower address
          coBorrowerPermanentProvinceId: newFormData.coBorrowerPermanentProvinceId ?? currentFormData.coBorrowerPermanentProvinceId,
          coBorrowerPermanentProvinceModel: newFormData.coBorrowerPermanentProvinceModel ?? currentFormData.coBorrowerPermanentProvinceModel,
          coBorrowerPermanentWardId: newFormData.coBorrowerPermanentWardId ?? currentFormData.coBorrowerPermanentWardId,
          coBorrowerPermanentWardModel: newFormData.coBorrowerPermanentWardModel ?? currentFormData.coBorrowerPermanentWardModel,
          coBorrowerPermanentAddress: newFormData.coBorrowerPermanentAddress ?? currentFormData.coBorrowerPermanentAddress,
          coBorrowerCurrentSamePermanent: newFormData.coBorrowerCurrentSamePermanent ?? currentFormData.coBorrowerCurrentSamePermanent,
          coBorrowerCurrentProvinceId: newFormData.coBorrowerCurrentProvinceId ?? currentFormData.coBorrowerCurrentProvinceId,
          coBorrowerCurrentProvinceModel: newFormData.coBorrowerCurrentProvinceModel ?? currentFormData.coBorrowerCurrentProvinceModel,
          coBorrowerCurrentWardId: newFormData.coBorrowerCurrentWardId ?? currentFormData.coBorrowerCurrentWardId,
          coBorrowerCurrentWardModel: newFormData.coBorrowerCurrentWardModel ?? currentFormData.coBorrowerCurrentWardModel,
          coBorrowerCurrentAddress: newFormData.coBorrowerCurrentAddress ?? currentFormData.coBorrowerCurrentAddress,
          
          // Loan proposal
          loanType: newFormData.loanType ?? currentFormData.loanType,
          loanAmount: newFormData.loanAmount ?? currentFormData.loanAmount,
          ownCapital: newFormData.ownCapital ?? currentFormData.ownCapital,
          totalCapitalNeed: newFormData.totalCapitalNeed ?? currentFormData.totalCapitalNeed,
          loanTermId: newFormData.loanTermId ?? currentFormData.loanTermId,
          loanTermModel: newFormData.loanTermModel ?? currentFormData.loanTermModel,
          loanMethodId: newFormData.loanMethodId ?? currentFormData.loanMethodId,
          loanMethodModel: newFormData.loanMethodModel ?? currentFormData.loanMethodModel,
          loanPurposeId: newFormData.loanPurposeId ?? currentFormData.loanPurposeId,
          loanPurposeModel: newFormData.loanPurposeModel ?? currentFormData.loanPurposeModel,
          repaymentMethodId: newFormData.repaymentMethodId ?? currentFormData.repaymentMethodId,
          repaymentMethodModel: newFormData.repaymentMethodModel ?? currentFormData.repaymentMethodModel,
          disbursementMethodId: newFormData.disbursementMethodId ?? currentFormData.disbursementMethodId,
          disbursementMethodModel: newFormData.disbursementMethodModel ?? currentFormData.disbursementMethodModel,
          disbursementAccount: newFormData.disbursementAccount ?? currentFormData.disbursementAccount,
          branchCode: newFormData.branchCode ?? currentFormData.branchCode,
          
          // Financial info
          incomeSourceId: newFormData.incomeSourceId ?? currentFormData.incomeSourceId,
          incomeSourceModel: newFormData.incomeSourceModel ?? currentFormData.incomeSourceModel,
          dailyIncome: newFormData.dailyIncome ?? currentFormData.dailyIncome,
          dailyRevenue: newFormData.dailyRevenue ?? currentFormData.dailyRevenue,
          businessLocationProvinceId: newFormData.businessLocationProvinceId ?? currentFormData.businessLocationProvinceId,
          businessLocationProvinceModel: newFormData.businessLocationProvinceModel ?? currentFormData.businessLocationProvinceModel,
          businessLocationWardId: newFormData.businessLocationWardId ?? currentFormData.businessLocationWardId,
          businessLocationWardModel: newFormData.businessLocationWardModel ?? currentFormData.businessLocationWardModel,
          businessLocationAddress: newFormData.businessLocationAddress ?? currentFormData.businessLocationAddress,
          
          // Collateral info
          collateralTypeId: newFormData.collateralTypeId ?? currentFormData.collateralTypeId,
          collateralTypeModel: newFormData.collateralTypeModel ?? currentFormData.collateralTypeModel,
          collateralValue: newFormData.collateralValue ?? currentFormData.collateralValue,
          collateralValueText: newFormData.collateralValueText ?? currentFormData.collateralValueText,
          totalCollateralValue: newFormData.totalCollateralValue ?? currentFormData.totalCollateralValue,
          collateralConditionId: newFormData.collateralConditionId ?? currentFormData.collateralConditionId,
          collateralOwner: newFormData.collateralOwner ?? currentFormData.collateralOwner,
          collateralOwnerBirthYear: newFormData.collateralOwnerBirthYear ?? currentFormData.collateralOwnerBirthYear,
          
          // Vehicle info
          vehicleName: newFormData.vehicleName ?? currentFormData.vehicleName,
          vehiclePlateNumber: newFormData.vehiclePlateNumber ?? currentFormData.vehiclePlateNumber,
          vehicleFrameNumber: newFormData.vehicleFrameNumber ?? currentFormData.vehicleFrameNumber,
          vehicleEngineNumber: newFormData.vehicleEngineNumber ?? currentFormData.vehicleEngineNumber,
          vehicleRegistrationNumber: newFormData.vehicleRegistrationNumber ?? currentFormData.vehicleRegistrationNumber,
          vehicleRegistrationPlace: newFormData.vehicleRegistrationPlace ?? currentFormData.vehicleRegistrationPlace,
          vehicleRegistrationDate: newFormData.vehicleRegistrationDate ?? currentFormData.vehicleRegistrationDate,
          vehicleConditionAtHandover: newFormData.vehicleConditionAtHandover ?? currentFormData.vehicleConditionAtHandover,
          vehicleConditionAtHandoverModel: newFormData.vehicleConditionAtHandoverModel ?? currentFormData.vehicleConditionAtHandoverModel,
        );
    }

  void _onSetDefaults(SetDefaults event, Emitter<TransactionFormState> emit) {
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      final productCode = currentState.selectedProduct?.code;
      
      if (productCode == null) {
        return;
      }
      
      BaseFormData? updatedFormData = currentState.formData;
      
      // Set defaults based on product type
      switch (productCode) {
        case 'GOLD_LOAN':
        case 'MANGO':
          if (updatedFormData is InstallmentLoanFormData) {
            // Only set defaults if the values are null (never been set by user)
            updatedFormData = updatedFormData.copyWith(
              hasCoBorrower: updatedFormData.hasCoBorrower ?? true,
              borrowerCurrentSamePermanent: updatedFormData.borrowerCurrentSamePermanent ?? true,
              coBorrowerCurrentSamePermanent: updatedFormData.coBorrowerCurrentSamePermanent ?? true,
            );
            
            // Map customer data if available
            if (event.customer != null) {
              updatedFormData = _mapCustomerToInstallmentForm(updatedFormData, event.customer!);
            }
          }
          break;
          
        case 'PERSONAL_LOAN':
          // TODO: Implement when PersonalLoanFormData is ready
          break;
          
        default:
          break;
      }
      
      if (updatedFormData != null) {
        emit(currentState.copyWith(formData: updatedFormData));
      }
    }
  }

  void _onValidateForm(ValidateForm event, Emitter<TransactionFormState> emit) {
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      if (currentState.formData != null) {
        final isValid = currentState.formData!.isValid;
        final errors = currentState.formData!.validationErrors;
        
        emit(currentState.copyWith(
          isValid: isValid,
          validationErrors: errors,
        ));
      }
    }
  }

  void _onClearForm(ClearForm event, Emitter<TransactionFormState> emit) {
    emit(TransactionFormInitial());
  }

  void _onUpdateDocuments(UpdateDocuments event, Emitter<TransactionFormState> emit) {
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      emit(currentState.copyWith(
        documents: event.documents,
      ));
    }
  }

  // Helper method to map customer to InstallmentLoanFormData
  InstallmentLoanFormData _mapCustomerToInstallmentForm(
    InstallmentLoanFormData formData, 
    CustomerModel customer
  ) {
    final result = formData.copyWith(
      borrowerName: customer.fullName.isNotEmpty ? customer.fullName : null,
      borrowerPhone: customer.phoneNumber?.isNotEmpty == true ? customer.phoneNumber : null,
      borrowerIdNumber: customer.idCardNumber?.isNotEmpty == true ? customer.idCardNumber : null,
      borrowerIdIssueDate: customer.idCardIssueDate != null ? _formatDateForForm(customer.idCardIssueDate!) : null,
      borrowerIdExpiryDate: customer.idCardExpiryDate != null ? _formatDateForForm(customer.idCardExpiryDate!) : null,
      borrowerIdIssuePlace: customer.idCardIssuePlace?.isNotEmpty == true ? customer.idCardIssuePlace : null,
      borrowerBirthDate: customer.birthDate != null ? _formatDateForForm(customer.birthDate!) : null,
      borrowerGender: customer.gender?.id?.isNotEmpty == true ? customer.gender!.id : null,
      borrowerGenderModel: customer.gender?.id?.isNotEmpty == true ? customer.gender : null,
      borrowerPermanentAddress: customer.permanentAddress?.isNotEmpty == true ? customer.permanentAddress : null,
      borrowerPermanentProvinceId: customer.province?.id.isNotEmpty == true ? customer.province!.id : null,
      borrowerPermanentProvinceModel: customer.province?.id.isNotEmpty == true ? customer.province : null,
      borrowerPermanentWardId: customer.ward?.id?.isNotEmpty == true ? customer.ward!.id : null,
      borrowerPermanentWardModel: customer.ward?.id?.isNotEmpty == true ? customer.ward : null,
      borrowerCurrentAddress: customer.currentAddress?.isNotEmpty == true ? customer.currentAddress : (customer.sameAddress && customer.permanentAddress?.isNotEmpty == true ? customer.permanentAddress : null),
      borrowerCurrentSamePermanent: customer.sameAddress,
      // Map collateral info - auto-fill from customer name and birth date
      collateralOwner: customer.fullName.isNotEmpty ? customer.fullName : null,
      collateralOwnerBirthYear: customer.birthDate != null ? _formatDateForForm(customer.birthDate!) : null,
    );
    
    return result;
  }

  String _formatDateForForm(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
