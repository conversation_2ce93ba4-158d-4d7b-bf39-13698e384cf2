import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:rxdart/rxdart.dart';
import '../../../../shared/utils/app_logger.dart';
import '../../../customers/models/customer_model.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/models/collateral_category_model.dart';
import '../../../../shared/models/bank_account_model.dart';
import '../../models/enums/loan_type_enum.dart';
import 'base_form_bloc.dart';

/// InstallmentLoanFormBloc - extends BaseFormBloc với shared logic
class InstallmentLoanFormBloc extends BaseFormBloc {
  
  // ===== INSTALLMENT LOAN SPECIFIC FIELDS =====
  // Keep streams for dropdowns, switches, and date fields - text fields use TextController
  final _loanTermModelSubject = BehaviorSubject<ConfigModel?>();
  final _collateralTypeModelSubject = BehaviorSubject<CollateralCategoryModel?>();
  final _collateralConditionModelSubject = BehaviorSubject<ConfigModel?>();
  final _vehicleConditionAtHandoverModelSubject = BehaviorSubject<ConfigModel?>();
  final _vehicleRegistrationDateSubject = BehaviorSubject<String?>();
  final _collateralOwnerBirthYearSubject = BehaviorSubject<String?>();
  
  // Additional dropdown fields
  final _loanMethodModelSubject = BehaviorSubject<ConfigModel?>();
  final _loanPurposeModelSubject = BehaviorSubject<ConfigModel?>();
  final _repaymentMethodModelSubject = BehaviorSubject<ConfigModel?>();
  final _disbursementMethodModelSubject = BehaviorSubject<ConfigModel?>();
  final _incomeSourceModelSubject = BehaviorSubject<ConfigModel?>();
  final _disbursementAccountModelSubject = BehaviorSubject<BankAccountModel?>();
  
  // Loan type and financial fields
  final _loanTypeSubject = BehaviorSubject<LoanType?>.seeded(LoanType.withCollateral);
  final _totalCapitalNeedSubject = BehaviorSubject<int?>();
  final _totalCollateralValueSubject = BehaviorSubject<int?>();
  final _branchCodeSubject = BehaviorSubject<String?>();
  final _branchNameSubject = BehaviorSubject<String?>();

  // ===== STREAMS =====
  // Keep streams for dropdowns, switches, and date fields - text fields use TextController
  Stream<ConfigModel?> get loanTermModelStream => _loanTermModelSubject.stream;
  Stream<CollateralCategoryModel?> get collateralTypeModelStream => _collateralTypeModelSubject.stream;
  Stream<ConfigModel?> get collateralConditionModelStream => _collateralConditionModelSubject.stream;
  Stream<ConfigModel?> get vehicleConditionAtHandoverModelStream => _vehicleConditionAtHandoverModelSubject.stream;
  Stream<String?> get vehicleRegistrationDateStream => _vehicleRegistrationDateSubject.stream;
  Stream<String?> get collateralOwnerBirthYearStream => _collateralOwnerBirthYearSubject.stream;
  
  // Additional dropdown streams
  Stream<ConfigModel?> get loanMethodModelStream => _loanMethodModelSubject.stream;
  Stream<ConfigModel?> get loanPurposeModelStream => _loanPurposeModelSubject.stream;
  Stream<ConfigModel?> get repaymentMethodModelStream => _repaymentMethodModelSubject.stream;
  Stream<ConfigModel?> get disbursementMethodModelStream => _disbursementMethodModelSubject.stream;
  Stream<ConfigModel?> get incomeSourceModelStream => _incomeSourceModelSubject.stream;
  Stream<BankAccountModel?> get disbursementAccountModelStream => _disbursementAccountModelSubject.stream;
  
  // Loan type and financial streams
  Stream<LoanType?> get loanTypeStream => _loanTypeSubject.stream;
  Stream<int?> get totalCapitalNeedStream => _totalCapitalNeedSubject.stream;
  Stream<int?> get totalCollateralValueStream => _totalCollateralValueSubject.stream;
  Stream<String?> get branchCodeStream => _branchCodeSubject.stream;
  Stream<String?> get branchNameStream => _branchNameSubject.stream;

  // ===== VALUES =====
  // Keep values for dropdowns, switches, and date fields - text fields use TextController
  ConfigModel? get loanTermModel => _loanTermModelSubject.valueOrNull;
  CollateralCategoryModel? get collateralTypeModel => _collateralTypeModelSubject.valueOrNull;
  ConfigModel? get collateralConditionModel => _collateralConditionModelSubject.valueOrNull;
  ConfigModel? get vehicleConditionAtHandoverModel => _vehicleConditionAtHandoverModelSubject.valueOrNull;
  String? get vehicleRegistrationDate => _vehicleRegistrationDateSubject.valueOrNull;
  String? get collateralOwnerBirthYear => _collateralOwnerBirthYearSubject.valueOrNull;
  
  // Additional dropdown values
  ConfigModel? get loanMethodModel => _loanMethodModelSubject.valueOrNull;
  ConfigModel? get loanPurposeModel => _loanPurposeModelSubject.valueOrNull;
  ConfigModel? get repaymentMethodModel => _repaymentMethodModelSubject.valueOrNull;
  ConfigModel? get disbursementMethodModel => _disbursementMethodModelSubject.valueOrNull;
  ConfigModel? get incomeSourceModel => _incomeSourceModelSubject.valueOrNull;
  BankAccountModel? get disbursementAccountModel => _disbursementAccountModelSubject.valueOrNull;
  
  // Loan type and financial values
  LoanType? get loanType => _loanTypeSubject.valueOrNull;
  int? get totalCapitalNeed => _totalCapitalNeedSubject.valueOrNull;
  int? get totalCollateralValue => _totalCollateralValueSubject.valueOrNull;
  String? get branchCode => _branchCodeSubject.valueOrNull;
  String? get branchName => _branchNameSubject.valueOrNull;

  // ===== INSTALLMENT LOAN SPECIFIC METHODS =====
  
  /// Update collateral owner birth year
  void updateCollateralOwnerBirthYear(String birthYear) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating collateral owner birth year to: $birthYear');
    updateFieldAndValidate(_collateralOwnerBirthYearSubject, birthYear);
    appLogger.d('✅ InstallmentLoanFormBloc: Collateral owner birth year updated');
  }

  /// Update vehicle registration date
  void updateVehicleRegistrationDate(String date) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating vehicle registration date to: $date');
    updateFieldAndValidate(_vehicleRegistrationDateSubject, date);
    appLogger.d('✅ InstallmentLoanFormBloc: Vehicle registration date updated');
  }

  /// Update loan term
  void updateLoanTerm(ConfigModel termModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating loan term to: ${termModel.id}');
    _loanTermModelSubject.add(termModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Loan term updated');
  }

  /// Update collateral type
  void updateCollateralType(CollateralCategoryModel collateralTypeModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating collateral type to: ${collateralTypeModel.id}');
    _collateralTypeModelSubject.add(collateralTypeModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Collateral type updated');
  }


  /// Update collateral condition
  void updateCollateralCondition(ConfigModel conditionModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating collateral condition to: ${conditionModel.id}');
    _collateralConditionModelSubject.add(conditionModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Collateral condition updated');
  }


  /// Update loan method
  void updateLoanMethod(ConfigModel methodModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating loan method to: ${methodModel.id}');
    _loanMethodModelSubject.add(methodModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Loan method updated');
  }

  /// Update loan purpose
  void updateLoanPurpose(ConfigModel purposeModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating loan purpose to: ${purposeModel.id}');
    _loanPurposeModelSubject.add(purposeModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Loan purpose updated');
  }

  /// Update repayment method
  void updateRepaymentMethod(ConfigModel methodModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating repayment method to: ${methodModel.id}');
    _repaymentMethodModelSubject.add(methodModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Repayment method updated');
  }

  /// Update disbursement method
  void updateDisbursementMethod(ConfigModel methodModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating disbursement method to: ${methodModel.id}');
    _disbursementMethodModelSubject.add(methodModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Disbursement method updated');
  }


  /// Update income source
  void updateIncomeSource(ConfigModel sourceModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating income source to: ${sourceModel.id}');
    _incomeSourceModelSubject.add(sourceModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Income source updated');
  }

  /// Update vehicle condition at handover
  void updateVehicleConditionAtHandover(ConfigModel conditionModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating vehicle condition at handover to: ${conditionModel.id}');
    _vehicleConditionAtHandoverModelSubject.add(conditionModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Vehicle condition at handover updated');
  }

  /// Update disbursement account
  void updateDisbursementAccount(BankAccountModel? accountModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating disbursement account to: ${accountModel?.accountNo}');
    _disbursementAccountModelSubject.add(accountModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Disbursement account updated');
  }

  /// Update co-borrower gender
  @override
  void updateCoBorrowerGender(ConfigModel genderModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating co-borrower gender to: ${genderModel.id}');
    // Note: This method is called from the form widget but the actual update
    // is handled by the form data copyWith method
    appLogger.d('✅ InstallmentLoanFormBloc: Co-borrower gender updated');
  }

  /// Update loan type
  void updateLoanType(LoanType? loanType) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating loan type to: $loanType');
    _loanTypeSubject.add(loanType);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Loan type updated');
  }

  /// Update branch code and name
  void updateBranchInfo(String? branchCode, String? branchName) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating branch info to: $branchCode - $branchName');
    _branchCodeSubject.add(branchCode);
    _branchNameSubject.add(branchName);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Branch info updated');
  }

  /// Calculate total capital need from external values
  void calculateTotalCapitalNeed(int loanAmount, int ownCapital) {
    final totalCapitalNeed = loanAmount + ownCapital;
    
    appLogger.d('🔄 InstallmentLoanFormBloc: Calculating total capital need: $loanAmount + $ownCapital = $totalCapitalNeed');
    debugPrint('🔄 InstallmentLoanFormBloc: Calculating total capital need: $loanAmount + $ownCapital = $totalCapitalNeed');
    _totalCapitalNeedSubject.add(totalCapitalNeed);
  }

  /// Update total collateral value
  void updateTotalCollateralValue(int collateralValue) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating total collateral value to: $collateralValue');
    debugPrint('🔄 InstallmentLoanFormBloc: Updating total collateral value to: $collateralValue');
    _totalCollateralValueSubject.add(collateralValue);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Total collateral value updated');
  }







  // ===== OVERRIDE SHARED METHODS =====
  
  @override
  void initializeWithCustomer(CustomerModel? customer) {
    super.initializeWithCustomer(customer);
    
    // Set installment loan specific defaults
    // Co-borrower current same permanent is now handled by base form bloc
    
    appLogger.d('✅ InstallmentLoanFormBloc: Initialized with installment loan defaults');
  }

  // ===== IMPLEMENT ABSTRACT METHODS =====
  
  @override
  List<String> validateForm() {
    final errors = <String>[];
    
    // Installment loan specific validation - only for dropdown fields
    if (loanTermModel == null) {
      errors.add('Vui lòng chọn kỳ hạn vay');
    }
    
    if (collateralTypeModel == null) {
      errors.add('Vui lòng chọn loại tài sản thế chấp');
    }
    
    // Text field validation now handled by form data validation
    
    return errors;
  }

  @override
  void syncToParentBloc() {
    // TODO: Implement sync to parent TransactionFormBloc
    appLogger.d('🔄 InstallmentLoanFormBloc: Syncing to parent bloc');
  }

  // ===== OVERRIDE DISPOSE =====
  
  @override
  void dispose() {
    appLogger.d('🔄 InstallmentLoanFormBloc: Disposing installment loan specific subjects');
    
    // Dispose installment loan specific subjects
    _loanTermModelSubject.close();
    _collateralTypeModelSubject.close();
    _collateralConditionModelSubject.close();
    _vehicleConditionAtHandoverModelSubject.close();
    _vehicleRegistrationDateSubject.close();
    _collateralOwnerBirthYearSubject.close();
    
    // Dispose additional dropdown subjects
    _loanMethodModelSubject.close();
    _loanPurposeModelSubject.close();
    _repaymentMethodModelSubject.close();
    _disbursementMethodModelSubject.close();
    _incomeSourceModelSubject.close();
    _disbursementAccountModelSubject.close();
    
    // Dispose loan type and financial subjects
    _loanTypeSubject.close();
    _totalCapitalNeedSubject.close();
    _totalCollateralValueSubject.close();
    _branchCodeSubject.close();
    _branchNameSubject.close();
    
    // Dispose base subjects
    super.dispose();
    
    appLogger.d('✅ InstallmentLoanFormBloc: All subjects disposed');
  }
}