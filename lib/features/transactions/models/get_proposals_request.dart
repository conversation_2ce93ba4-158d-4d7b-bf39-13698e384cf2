/// Request parameters for get proposals
class GetProposalsRequest {
  final DateTime? createdFrom;
  final DateTime? createdTo;
  final String? productId;
  final String? status;
  final List<String>? regionIds;
  final List<String>? branchIds;
  final List<String>? assignedEmployeeIds;
  final String? pKeySearch; // Parameter cho tìm kiếm text
  final int limit;
  final int offset;

  GetProposalsRequest({
    this.createdFrom,
    this.createdTo,
    this.productId,
    this.status,
    this.regionIds,
    this.branchIds,
    this.assignedEmployeeIds,
    this.pKeySearch,
    this.limit = 20,
    this.offset = 0,
  });

  Map<String, dynamic> toJson() {
    return {
      if (createdFrom != null) 'p_created_from': createdFrom!.toIso8601String(),
      if (createdTo != null) 'p_created_to': createdTo!.toIso8601String(),
      if (productId != null) 'p_product_id': productId,
      // Nếu có productId thì dùng p_step_code, không thì dùng p_status
      if (status != null) 
        (productId != null ? 'p_step_code' : 'p_status'): status,
      if (regionIds != null) 'p_region_ids': regionIds,
      if (branchIds != null) 'p_branch_ids': branchIds,
      if (assignedEmployeeIds != null) 'p_assigned_employee_ids': assignedEmployeeIds,
      if (pKeySearch != null && pKeySearch!.isNotEmpty) 'p_key_search': pKeySearch,
      'p_limit': limit,
      'p_offset': offset,
    };
  }

  /// Copy with method để tạo instance mới với một số field thay đổi
  GetProposalsRequest copyWith({
    DateTime? createdFrom,
    DateTime? createdTo,
    String? productId,
    String? status,
    List<String>? regionIds,
    List<String>? branchIds,
    List<String>? assignedEmployeeIds,
    String? pKeySearch,
    int? limit,
    int? offset,
  }) {
    return GetProposalsRequest(
      createdFrom: createdFrom ?? this.createdFrom,
      createdTo: createdTo ?? this.createdTo,
      productId: productId ?? this.productId,
      status: status ?? this.status,
      regionIds: regionIds ?? this.regionIds,
      branchIds: branchIds ?? this.branchIds,
      assignedEmployeeIds: assignedEmployeeIds ?? this.assignedEmployeeIds,
      pKeySearch: pKeySearch ?? this.pKeySearch,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  /// Kiểm tra có filter nào được apply không
  bool get hasFilters {
    return createdFrom != null ||
        createdTo != null ||
        productId != null ||
        status != null ||
        (regionIds != null && regionIds!.isNotEmpty) ||
        (branchIds != null && branchIds!.isNotEmpty) ||
        (assignedEmployeeIds != null && assignedEmployeeIds!.isNotEmpty) ||
        (pKeySearch != null && pKeySearch!.isNotEmpty);
  }

  /// Kiểm tra có date filter không
  bool get hasDateFilter {
    return createdFrom != null || createdTo != null;
  }

  /// Kiểm tra có search text không
  bool get hasSearchText {
    return pKeySearch != null && pKeySearch!.isNotEmpty;
  }

  @override
  String toString() {
    return 'GetProposalsRequest('
        'createdFrom: $createdFrom, '
        'createdTo: $createdTo, '
        'productId: $productId, '
        'status: $status, '
        'regionIds: $regionIds, '
        'branchIds: $branchIds, '
        'assignedEmployeeIds: $assignedEmployeeIds, '
        'pKeySearch: $pKeySearch, '
        'limit: $limit, '
        'offset: $offset)';
  }
}
