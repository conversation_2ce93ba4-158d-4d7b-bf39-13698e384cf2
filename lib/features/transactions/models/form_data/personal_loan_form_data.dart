import 'base_form_data.dart';

/// Form data for Personal Loan products (no collateral required)
class PersonalLoanFormData extends BaseFormData {
  // Borrower information
  final String? borrowerName;
  final String? borrowerIdNumber;
  final String? borrowerIdIssueDate;
  final String? borrowerIdExpiryDate;
  final String? borrowerIdIssuePlace;
  final String? borrowerBirthDate;
  final String? borrowerGender;
  final String? borrowerPhone;
  final String? borrowerPermanentProvince;
  final String? borrowerPermanentDistrict;
  final String? borrowerPermanentAddress;
  final String? borrowerMaritalStatus;
  final bool borrowerCurrentSamePermanent;
  final String? borrowerCurrentProvince;
  final String? borrowerCurrentDistrict;
  final String? borrowerCurrentAddress;
  
  // Co-borrower information
  final bool hasCoBorrower;
  final String? coBorrowerName;
  final String? coBorrowerIdNumber;
  final String? coBorrowerIdIssueDate;
  final String? coBorrowerIdExpiryDate;
  final String? coBorrowerIdIssuePlace;
  final String? coBorrowerBirthDate;
  final String? coBorrowerGender;
  final String? coBorrowerPhone;
  final String? coBorrowerPermanentProvince;
  final String? coBorrowerPermanentDistrict;
  final String? coBorrowerPermanentAddress;
  final String? coBorrowerMaritalStatus;
  final bool coBorrowerCurrentSamePermanent;
  final String? coBorrowerCurrentProvince;
  final String? coBorrowerCurrentDistrict;
  final String? coBorrowerCurrentAddress;
  
  // Loan proposal
  final String? loanType;
  final int? ownCapital;
  final int? loanAmount;
  final String? loanTerm;
  final String? loanMethod;
  final String? loanPurpose;
  final String? repaymentMethod;
  final String? disbursementMethod;
  final String? disbursementAccount;
  final int? totalCapitalNeed;
  
  // Financial information
  final String? incomeSource;
  final int? dailyRevenue;
  final int? dailyIncome;
  final String? businessLocationProvince;
  final String? businessLocationDistrict;
  final String? businessLocationAddress;
  
  // Branch information
  final String? branchCode;
  
  PersonalLoanFormData({
    // Borrower information
    this.borrowerName,
    this.borrowerIdNumber,
    this.borrowerIdIssueDate,
    this.borrowerIdExpiryDate,
    this.borrowerIdIssuePlace,
    this.borrowerBirthDate,
    this.borrowerGender,
    this.borrowerPhone,
    this.borrowerPermanentProvince,
    this.borrowerPermanentDistrict,
    this.borrowerPermanentAddress,
    this.borrowerMaritalStatus,
    this.borrowerCurrentSamePermanent = false,
    this.borrowerCurrentProvince,
    this.borrowerCurrentDistrict,
    this.borrowerCurrentAddress,
    
    // Co-borrower information
    this.hasCoBorrower = false,
    this.coBorrowerName,
    this.coBorrowerIdNumber,
    this.coBorrowerIdIssueDate,
    this.coBorrowerIdExpiryDate,
    this.coBorrowerIdIssuePlace,
    this.coBorrowerBirthDate,
    this.coBorrowerGender,
    this.coBorrowerPhone,
    this.coBorrowerPermanentProvince,
    this.coBorrowerPermanentDistrict,
    this.coBorrowerPermanentAddress,
    this.coBorrowerMaritalStatus,
    this.coBorrowerCurrentSamePermanent = false,
    this.coBorrowerCurrentProvince,
    this.coBorrowerCurrentDistrict,
    this.coBorrowerCurrentAddress,
    
    // Loan proposal
    this.loanType = 'Không TSBĐ',
    this.ownCapital,
    this.loanAmount,
    this.loanTerm,
    this.loanMethod,
    this.loanPurpose,
    this.repaymentMethod,
    this.disbursementMethod,
    this.disbursementAccount,
    this.totalCapitalNeed,
    
    // Financial information
    this.incomeSource,
    this.dailyRevenue,
    this.dailyIncome,
    this.businessLocationProvince,
    this.businessLocationDistrict,
    this.businessLocationAddress,
    
    // Branch information
    this.branchCode,
  });
  
  @override
  Map<String, dynamic> toMap() {
    return {
      // Borrower information
      'borrower_name': borrowerName,
      'borrower_id_number': borrowerIdNumber,
      'borrower_id_issue_date': borrowerIdIssueDate,
      'borrower_id_expiry_date': borrowerIdExpiryDate,
      'borrower_id_issue_place': borrowerIdIssuePlace,
      'borrower_birth_date': borrowerBirthDate,
      'borrower_gender': borrowerGender,
      'borrower_phone': borrowerPhone,
      'borrower_permanent_province': borrowerPermanentProvince,
      'borrower_permanent_district': borrowerPermanentDistrict,
      'borrower_permanent_address': borrowerPermanentAddress,
      'borrower_marital_status': borrowerMaritalStatus,
      'borrower_current_same_permanent': borrowerCurrentSamePermanent,
      'borrower_current_province': borrowerCurrentProvince,
      'borrower_current_district': borrowerCurrentDistrict,
      'borrower_current_address': borrowerCurrentAddress,
      
      // Co-borrower information
      'has_co_borrower': hasCoBorrower,
      'co_borrower_name': coBorrowerName,
      'co_borrower_id_number': coBorrowerIdNumber,
      'co_borrower_id_issue_date': coBorrowerIdIssueDate,
      'co_borrower_id_expiry_date': coBorrowerIdExpiryDate,
      'co_borrower_id_issue_place': coBorrowerIdIssuePlace,
      'co_borrower_birth_date': coBorrowerBirthDate,
      'co_borrower_gender': coBorrowerGender,
      'co_borrower_phone': coBorrowerPhone,
      'co_borrower_permanent_province': coBorrowerPermanentProvince,
      'co_borrower_permanent_district': coBorrowerPermanentDistrict,
      'co_borrower_permanent_address': coBorrowerPermanentAddress,
      'co_borrower_marital_status': coBorrowerMaritalStatus,
      'co_borrower_current_same_permanent': coBorrowerCurrentSamePermanent,
      'co_borrower_current_province': coBorrowerCurrentProvince,
      'co_borrower_current_district': coBorrowerCurrentDistrict,
      'co_borrower_current_address': coBorrowerCurrentAddress,
      
      // Loan proposal
      'loan_type': loanType,
      'own_capital': ownCapital,
      'loan_amount': loanAmount,
      'loan_term': loanTerm,
      'loan_method': loanMethod,
      'loan_purpose': loanPurpose,
      'repayment_method': repaymentMethod,
      'disbursement_method': disbursementMethod,
      'disbursement_account': disbursementAccount,
      'total_capital_need': totalCapitalNeed,
      
      // Financial information
      'income_source': incomeSource,
      'daily_revenue': dailyRevenue,
      'daily_income': dailyIncome,
      'business_location_province': businessLocationProvince,
      'business_location_district': businessLocationDistrict,
      'business_location_address': businessLocationAddress,
      
      // Branch information
      'branch_code': branchCode,
    };
  }
  
  @override
  PersonalLoanFormData copyWith({
    // Borrower information
    String? borrowerName,
    String? borrowerIdNumber,
    String? borrowerIdIssueDate,
    String? borrowerIdExpiryDate,
    String? borrowerIdIssuePlace,
    String? borrowerBirthDate,
    String? borrowerGender,
    String? borrowerPhone,
    String? borrowerPermanentProvince,
    String? borrowerPermanentDistrict,
    String? borrowerPermanentAddress,
    String? borrowerMaritalStatus,
    bool? borrowerCurrentSamePermanent,
    String? borrowerCurrentProvince,
    String? borrowerCurrentDistrict,
    String? borrowerCurrentAddress,
    
    // Co-borrower information
    bool? hasCoBorrower,
    String? coBorrowerName,
    String? coBorrowerIdNumber,
    String? coBorrowerIdIssueDate,
    String? coBorrowerIdExpiryDate,
    String? coBorrowerIdIssuePlace,
    String? coBorrowerBirthDate,
    String? coBorrowerGender,
    String? coBorrowerPhone,
    String? coBorrowerPermanentProvince,
    String? coBorrowerPermanentDistrict,
    String? coBorrowerPermanentAddress,
    String? coBorrowerMaritalStatus,
    bool? coBorrowerCurrentSamePermanent,
    String? coBorrowerCurrentProvince,
    String? coBorrowerCurrentDistrict,
    String? coBorrowerCurrentAddress,
    
    // Loan proposal
    String? loanType,
    int? ownCapital,
    int? loanAmount,
    String? loanTerm,
    String? loanMethod,
    String? loanPurpose,
    String? repaymentMethod,
    String? disbursementMethod,
    String? disbursementAccount,
    int? totalCapitalNeed,
    
    // Financial information
    String? incomeSource,
    int? dailyRevenue,
    int? dailyIncome,
    String? businessLocationProvince,
    String? businessLocationDistrict,
    String? businessLocationAddress,
    
    // Branch information
    String? branchCode,
  }) {
    return PersonalLoanFormData(
      // Borrower information
      borrowerName: borrowerName ?? this.borrowerName,
      borrowerIdNumber: borrowerIdNumber ?? this.borrowerIdNumber,
      borrowerIdIssueDate: borrowerIdIssueDate ?? this.borrowerIdIssueDate,
      borrowerIdExpiryDate: borrowerIdExpiryDate ?? this.borrowerIdExpiryDate,
      borrowerIdIssuePlace: borrowerIdIssuePlace ?? this.borrowerIdIssuePlace,
      borrowerBirthDate: borrowerBirthDate ?? this.borrowerBirthDate,
      borrowerGender: borrowerGender ?? this.borrowerGender,
      borrowerPhone: borrowerPhone ?? this.borrowerPhone,
      borrowerPermanentProvince: borrowerPermanentProvince ?? this.borrowerPermanentProvince,
      borrowerPermanentDistrict: borrowerPermanentDistrict ?? this.borrowerPermanentDistrict,
      borrowerPermanentAddress: borrowerPermanentAddress ?? this.borrowerPermanentAddress,
      borrowerMaritalStatus: borrowerMaritalStatus ?? this.borrowerMaritalStatus,
      borrowerCurrentSamePermanent: borrowerCurrentSamePermanent ?? this.borrowerCurrentSamePermanent,
      borrowerCurrentProvince: borrowerCurrentProvince ?? this.borrowerCurrentProvince,
      borrowerCurrentDistrict: borrowerCurrentDistrict ?? this.borrowerCurrentDistrict,
      borrowerCurrentAddress: borrowerCurrentAddress ?? this.borrowerCurrentAddress,
      
      // Co-borrower information
      hasCoBorrower: hasCoBorrower ?? this.hasCoBorrower,
      coBorrowerName: coBorrowerName ?? this.coBorrowerName,
      coBorrowerIdNumber: coBorrowerIdNumber ?? this.coBorrowerIdNumber,
      coBorrowerIdIssueDate: coBorrowerIdIssueDate ?? this.coBorrowerIdIssueDate,
      coBorrowerIdExpiryDate: coBorrowerIdExpiryDate ?? this.coBorrowerIdExpiryDate,
      coBorrowerIdIssuePlace: coBorrowerIdIssuePlace ?? this.coBorrowerIdIssuePlace,
      coBorrowerBirthDate: coBorrowerBirthDate ?? this.coBorrowerBirthDate,
      coBorrowerGender: coBorrowerGender ?? this.coBorrowerGender,
      coBorrowerPhone: coBorrowerPhone ?? this.coBorrowerPhone,
      coBorrowerPermanentProvince: coBorrowerPermanentProvince ?? this.coBorrowerPermanentProvince,
      coBorrowerPermanentDistrict: coBorrowerPermanentDistrict ?? this.coBorrowerPermanentDistrict,
      coBorrowerPermanentAddress: coBorrowerPermanentAddress ?? this.coBorrowerPermanentAddress,
      coBorrowerMaritalStatus: coBorrowerMaritalStatus ?? this.coBorrowerMaritalStatus,
      coBorrowerCurrentSamePermanent: coBorrowerCurrentSamePermanent ?? this.coBorrowerCurrentSamePermanent,
      coBorrowerCurrentProvince: coBorrowerCurrentProvince ?? this.coBorrowerCurrentProvince,
      coBorrowerCurrentDistrict: coBorrowerCurrentDistrict ?? this.coBorrowerCurrentDistrict,
      coBorrowerCurrentAddress: coBorrowerCurrentAddress ?? this.coBorrowerCurrentAddress,
      
      // Loan proposal
      loanType: loanType ?? this.loanType,
      ownCapital: ownCapital ?? this.ownCapital,
      loanAmount: loanAmount ?? this.loanAmount,
      loanTerm: loanTerm ?? this.loanTerm,
      loanMethod: loanMethod ?? this.loanMethod,
      loanPurpose: loanPurpose ?? this.loanPurpose,
      repaymentMethod: repaymentMethod ?? this.repaymentMethod,
      disbursementMethod: disbursementMethod ?? this.disbursementMethod,
      disbursementAccount: disbursementAccount ?? this.disbursementAccount,
      totalCapitalNeed: totalCapitalNeed ?? this.totalCapitalNeed,
      
      // Financial information
      incomeSource: incomeSource ?? this.incomeSource,
      dailyRevenue: dailyRevenue ?? this.dailyRevenue,
      dailyIncome: dailyIncome ?? this.dailyIncome,
      businessLocationProvince: businessLocationProvince ?? this.businessLocationProvince,
      businessLocationDistrict: businessLocationDistrict ?? this.businessLocationDistrict,
      businessLocationAddress: businessLocationAddress ?? this.businessLocationAddress,
      
      // Branch information
      branchCode: branchCode ?? this.branchCode,
    );
  }
  
  
  PersonalLoanFormData fromMap(Map<String, dynamic> map) {
    return PersonalLoanFormData(
      // Borrower information
      borrowerName: map['borrower_name'] as String?,
      borrowerIdNumber: map['borrower_id_number'] as String?,
      borrowerIdIssueDate: map['borrower_id_issue_date'] as String?,
      borrowerIdExpiryDate: map['borrower_id_expiry_date'] as String?,
      borrowerIdIssuePlace: map['borrower_id_issue_place'] as String?,
      borrowerBirthDate: map['borrower_birth_date'] as String?,
      borrowerGender: map['borrower_gender'] as String?,
      borrowerPhone: map['borrower_phone'] as String?,
      borrowerPermanentProvince: map['borrower_permanent_province'] as String?,
      borrowerPermanentDistrict: map['borrower_permanent_district'] as String?,
      borrowerPermanentAddress: map['borrower_permanent_address'] as String?,
      borrowerMaritalStatus: map['borrower_marital_status'] as String?,
      borrowerCurrentSamePermanent: map['borrower_current_same_permanent'] as bool? ?? false,
      borrowerCurrentProvince: map['borrower_current_province'] as String?,
      borrowerCurrentDistrict: map['borrower_current_district'] as String?,
      borrowerCurrentAddress: map['borrower_current_address'] as String?,
      
      // Co-borrower information
      hasCoBorrower: map['has_co_borrower'] as bool? ?? false,
      coBorrowerName: map['co_borrower_name'] as String?,
      coBorrowerIdNumber: map['co_borrower_id_number'] as String?,
      coBorrowerIdIssueDate: map['co_borrower_id_issue_date'] as String?,
      coBorrowerIdExpiryDate: map['co_borrower_id_expiry_date'] as String?,
      coBorrowerIdIssuePlace: map['co_borrower_id_issue_place'] as String?,
      coBorrowerBirthDate: map['co_borrower_birth_date'] as String?,
      coBorrowerGender: map['co_borrower_gender'] as String?,
      coBorrowerPhone: map['co_borrower_phone'] as String?,
      coBorrowerPermanentProvince: map['co_borrower_permanent_province'] as String?,
      coBorrowerPermanentDistrict: map['co_borrower_permanent_district'] as String?,
      coBorrowerPermanentAddress: map['co_borrower_permanent_address'] as String?,
      coBorrowerMaritalStatus: map['co_borrower_marital_status'] as String?,
      coBorrowerCurrentSamePermanent: map['co_borrower_current_same_permanent'] as bool? ?? false,
      coBorrowerCurrentProvince: map['co_borrower_current_province'] as String?,
      coBorrowerCurrentDistrict: map['co_borrower_current_district'] as String?,
      coBorrowerCurrentAddress: map['co_borrower_current_address'] as String?,
      
      // Loan proposal
      loanType: map['loan_type'] as String? ?? 'Không TSBĐ',
      ownCapital: map['own_capital'] as int?,
      loanAmount: map['loan_amount'] as int?,
      loanTerm: map['loan_term'] as String?,
      loanMethod: map['loan_method'] as String?,
      loanPurpose: map['loan_purpose'] as String?,
      repaymentMethod: map['repayment_method'] as String?,
      disbursementMethod: map['disbursement_method'] as String?,
      disbursementAccount: map['disbursement_account'] as String?,
      totalCapitalNeed: map['total_capital_need'] as int?,
      
      // Financial information
      incomeSource: map['income_source'] as String?,
      dailyRevenue: map['daily_revenue'] as int?,
      dailyIncome: map['daily_income'] as int?,
      businessLocationProvince: map['business_location_province'] as String?,
      businessLocationDistrict: map['business_location_district'] as String?,
      businessLocationAddress: map['business_location_address'] as String?,
      
      // Branch information
      branchCode: map['branch_code'] as String?,
    );
  }
  
  // Validation methods removed - use TransactionFormValidator instead
}
