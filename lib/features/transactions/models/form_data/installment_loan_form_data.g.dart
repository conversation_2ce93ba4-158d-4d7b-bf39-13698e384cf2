// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'installment_loan_form_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InstallmentLoanFormData _$InstallmentLoanFormDataFromJson(
  Map<String, dynamic> json,
) => InstallmentLoanFormData(
  borrowerName: json['borrower_name'] as String?,
  borrowerIdType: json['borrower_id_type'] as String?,
  borrowerIdNumber: json['borrower_id_number'] as String?,
  borrowerIdIssueDate: json['borrower_id_issue_date'] as String?,
  borrowerIdExpiryDate: json['borrower_id_expiry_date'] as String?,
  borrowerIdIssuePlace: json['borrower_id_issue_place'] as String?,
  borrowerBirthDate: json['borrower_birth_date'] as String?,
  borrowerGender: json['borrower_gender'] as String?,
  borrowerPhone: json['borrower_phone'] as String?,
  borrowerPermanentProvinceId:
      json['borrower_permanent_province_id'] as String?,
  borrowerPermanentWardId: json['borrower_permanent_ward_id'] as String?,
  borrowerPermanentAddress: json['borrower_permanent_address'] as String?,
  borrowerMaritalStatusId: json['borrower_marital_status_id'] as String?,
  borrowerCurrentSamePermanent:
      json['borrower_current_same_permanent'] as bool? ?? true,
  borrowerCurrentProvinceId: json['borrower_current_province_id'] as String?,
  borrowerCurrentWardId: json['borrower_current_ward_id'] as String?,
  borrowerCurrentAddress: json['borrower_current_address'] as String?,
  hasCoBorrower: json['has_co_borrower'] as bool? ?? true,
  coBorrowerName: json['co_borrower_name'] as String?,
  coBorrowerIdType: json['co_borrower_id_type'] as String?,
  coBorrowerIdNumber: json['co_borrower_id_number'] as String?,
  coBorrowerIdIssueDate: json['co_borrower_id_issue_date'] as String?,
  coBorrowerIdExpiryDate: json['co_borrower_id_expiry_date'] as String?,
  coBorrowerIdIssuePlace: json['co_borrower_id_issue_place'] as String?,
  coBorrowerBirthDate: json['co_borrower_birth_date'] as String?,
  coBorrowerGender: json['co_borrower_gender'] as String?,
  coBorrowerPhone: json['co_borrower_phone'] as String?,
  coBorrowerPermanentProvinceId:
      json['co_borrower_permanent_province_id'] as String?,
  coBorrowerPermanentWardId: json['co_borrower_permanent_ward_id'] as String?,
  coBorrowerPermanentAddress: json['co_borrower_permanent_address'] as String?,
  coBorrowerMaritalStatusId: json['co_borrower_marital_status_id'] as String?,
  coBorrowerCurrentSamePermanent:
      json['co_borrower_current_same_permanent'] as bool? ?? true,
  coBorrowerCurrentProvinceId:
      json['co_borrower_current_province_id'] as String?,
  coBorrowerCurrentWardId: json['co_borrower_current_ward_id'] as String?,
  coBorrowerCurrentAddress: json['co_borrower_current_address'] as String?,
  loanType:
      $enumDecodeNullable(_$LoanTypeEnumMap, json['loan_type']) ??
      LoanType.withCollateral,
  ownCapital: (json['own_capital'] as num?)?.toInt(),
  loanAmount: (json['loan_amount'] as num?)?.toInt(),
  loanTermId: json['loan_term_id'] as String?,
  loanMethodId: json['loan_method_id'] as String?,
  loanPurposeId: json['loan_purpose_id'] as String?,
  loanPurposeName: json['loan_purpose_name'] as String?,
  loanPurposeOther: json['loan_purpose_other'] as String?,
  repaymentMethodId: json['repayment_method_id'] as String?,
  disbursementMethodId: json['disbursement_method_id'] as String?,
  disbursementAccount: json['disbursement_account'] as String?,
  totalCapitalNeed: (json['total_capital_need'] as num?)?.toInt(),
  incomeSourceId: json['income_source_id'] as String?,
  dailyRevenue: (json['daily_revenue'] as num?)?.toInt(),
  dailyIncome: (json['daily_income'] as num?)?.toInt(),
  businessLocationProvinceId: json['business_location_province_id'] as String?,
  businessLocationWardId: json['business_location_ward_id'] as String?,
  businessLocationAddress: json['business_location_address'] as String?,
  employerName: json['employer_name'] as String?,
  position: json['position'] as String?,
  workExperience: json['work_experience'] as String?,
  monthlySalary: (json['monthly_salary'] as num?)?.toInt(),
  collateralTypeId: json['collateral_type_id'] as String?,
  collateralType: json['collateral_type'] as String?,
  collateralValue: (json['collateral_value'] as num?)?.toInt(),
  collateralValueText: json['collateral_value_text'] as String?,
  collateralConditionId: json['collateral_condition_id'] as String?,
  collateralOwner: json['collateral_owner'] as String?,
  collateralOwnerBirthYear: json['collateral_owner_birth_year'] as String?,
  vehicleName: json['vehicle_name'] as String?,
  vehiclePlateNumber: json['vehicle_plate_number'] as String?,
  vehicleFrameNumber: json['vehicle_frame_number'] as String?,
  vehicleEngineNumber: json['vehicle_engine_number'] as String?,
  vehicleRegistrationNumber: json['vehicle_registration_number'] as String?,
  vehicleRegistrationPlace: json['vehicle_registration_place'] as String?,
  vehicleRegistrationDate: json['vehicle_registration_date'] as String?,
  vehicleConditionAtHandover: json['vehicle_condition_at_handover'] as String?,
  totalCollateralValue: (json['total_collateral_value'] as num?)?.toInt(),
  branchCode: json['branch_code'] as String?,
);

Map<String, dynamic> _$InstallmentLoanFormDataToJson(
  InstallmentLoanFormData instance,
) => <String, dynamic>{
  'borrower_name': instance.borrowerName,
  'borrower_id_type': instance.borrowerIdType,
  'borrower_id_number': instance.borrowerIdNumber,
  'borrower_id_issue_date': instance.borrowerIdIssueDate,
  'borrower_id_expiry_date': instance.borrowerIdExpiryDate,
  'borrower_id_issue_place': instance.borrowerIdIssuePlace,
  'borrower_birth_date': instance.borrowerBirthDate,
  'borrower_gender': instance.borrowerGender,
  'borrower_phone': instance.borrowerPhone,
  'borrower_permanent_province_id': instance.borrowerPermanentProvinceId,
  'borrower_permanent_ward_id': instance.borrowerPermanentWardId,
  'borrower_permanent_address': instance.borrowerPermanentAddress,
  'borrower_marital_status_id': instance.borrowerMaritalStatusId,
  'borrower_current_same_permanent': instance.borrowerCurrentSamePermanent,
  'borrower_current_province_id': instance.borrowerCurrentProvinceId,
  'borrower_current_ward_id': instance.borrowerCurrentWardId,
  'borrower_current_address': instance.borrowerCurrentAddress,
  'has_co_borrower': instance.hasCoBorrower,
  'co_borrower_name': instance.coBorrowerName,
  'co_borrower_id_type': instance.coBorrowerIdType,
  'co_borrower_id_number': instance.coBorrowerIdNumber,
  'co_borrower_id_issue_date': instance.coBorrowerIdIssueDate,
  'co_borrower_id_expiry_date': instance.coBorrowerIdExpiryDate,
  'co_borrower_id_issue_place': instance.coBorrowerIdIssuePlace,
  'co_borrower_birth_date': instance.coBorrowerBirthDate,
  'co_borrower_gender': instance.coBorrowerGender,
  'co_borrower_phone': instance.coBorrowerPhone,
  'co_borrower_permanent_province_id': instance.coBorrowerPermanentProvinceId,
  'co_borrower_permanent_ward_id': instance.coBorrowerPermanentWardId,
  'co_borrower_permanent_address': instance.coBorrowerPermanentAddress,
  'co_borrower_marital_status_id': instance.coBorrowerMaritalStatusId,
  'co_borrower_current_same_permanent': instance.coBorrowerCurrentSamePermanent,
  'co_borrower_current_province_id': instance.coBorrowerCurrentProvinceId,
  'co_borrower_current_ward_id': instance.coBorrowerCurrentWardId,
  'co_borrower_current_address': instance.coBorrowerCurrentAddress,
  'loan_type': _$LoanTypeEnumMap[instance.loanType],
  'own_capital': instance.ownCapital,
  'loan_amount': instance.loanAmount,
  'loan_term_id': instance.loanTermId,
  'loan_method_id': instance.loanMethodId,
  'loan_purpose_id': instance.loanPurposeId,
  'loan_purpose_name': instance.loanPurposeName,
  'loan_purpose_other': instance.loanPurposeOther,
  'repayment_method_id': instance.repaymentMethodId,
  'disbursement_method_id': instance.disbursementMethodId,
  'disbursement_account': instance.disbursementAccount,
  'total_capital_need': instance.totalCapitalNeed,
  'income_source_id': instance.incomeSourceId,
  'daily_revenue': instance.dailyRevenue,
  'daily_income': instance.dailyIncome,
  'business_location_province_id': instance.businessLocationProvinceId,
  'business_location_ward_id': instance.businessLocationWardId,
  'business_location_address': instance.businessLocationAddress,
  'employer_name': instance.employerName,
  'position': instance.position,
  'work_experience': instance.workExperience,
  'monthly_salary': instance.monthlySalary,
  'collateral_type_id': instance.collateralTypeId,
  'collateral_type': instance.collateralType,
  'collateral_value': instance.collateralValue,
  'collateral_value_text': instance.collateralValueText,
  'collateral_condition_id': instance.collateralConditionId,
  'collateral_owner': instance.collateralOwner,
  'collateral_owner_birth_year': instance.collateralOwnerBirthYear,
  'vehicle_name': instance.vehicleName,
  'vehicle_plate_number': instance.vehiclePlateNumber,
  'vehicle_frame_number': instance.vehicleFrameNumber,
  'vehicle_engine_number': instance.vehicleEngineNumber,
  'vehicle_registration_number': instance.vehicleRegistrationNumber,
  'vehicle_registration_place': instance.vehicleRegistrationPlace,
  'vehicle_registration_date': instance.vehicleRegistrationDate,
  'vehicle_condition_at_handover': instance.vehicleConditionAtHandover,
  'total_collateral_value': instance.totalCollateralValue,
  'branch_code': instance.branchCode,
};

const _$LoanTypeEnumMap = {
  LoanType.withCollateral: 'withCollateral',
  LoanType.withoutCollateral: 'withoutCollateral',
};
