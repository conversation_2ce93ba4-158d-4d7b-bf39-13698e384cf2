/// Enum cho các loại hình vay vốn
enum LoanType {
  withCollateral('Có TSBĐ'),
  withoutCollateral('Không TSBĐ');

  const LoanType(this.displayName);
  
  final String displayName;
  
  /// Tạo LoanType từ string value
  static LoanType? fromString(String? value) {
    if (value == null) return null;
    
    for (final type in LoanType.values) {
      if (type.displayName == value) {
        return type;
      }
    }
    return null;
  }
  
  /// L<PERSON>y tất cả display names
  static List<String> getAllDisplayNames() {
    return LoanType.values.map((e) => e.displayName).toList();
  }
}


