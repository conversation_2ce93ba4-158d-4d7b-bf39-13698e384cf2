/// Enum cho các loại giấy tờ tùy thân
enum IdCardType {
  cccd('CHIP_ID', 'CCCD'),
  passport('PASSPORT', 'Hộ Chiếu');

  const IdCardType(this.code, this.label);

  final String code;
  final String label;

  /// Tìm IdCardType theo code
  static IdCardType? fromCode(String? code) {
    if (code == null) return null;
    return IdCardType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => IdCardType.cccd, // Default to CCCD
    );
  }

  /// Tìm IdCardType theo label
  static IdCardType? fromLabel(String? label) {
    if (label == null) return null;
    return IdCardType.values.firstWhere(
      (type) => type.label == label,
      orElse: () => IdCardType.cccd, // Default to CCCD
    );
  }

  /// Tìm IdCardType theo ID từ config response
  /// <PERSON><PERSON> dụng mapping từ API response thay vì hardcode
  static IdCardType? fromConfigId(String? id, Map<String, String>? idMapping) {
    if (id == null) return null;
    
    // Sử dụng mapping từ config nếu có
    if (idMapping != null && idMapping.containsKey(id)) {
      final code = idMapping[id];
      return fromCode(code);
    }
    
    // Fallback: tìm theo code patterns
    if (id.contains('CHIP_ID') || id.contains('cccd')) {
      return IdCardType.cccd;
    }
    if (id.contains('PASSPORT') || id.contains('passport')) {
      return IdCardType.passport;
    }
    
    // Default to CCCD
    return IdCardType.cccd;
  }

  /// Tạo ConfigModel từ IdCardType với ID từ config
  /// ID sẽ được truyền từ bên ngoài thay vì hardcode
  Map<String, dynamic> toConfigMap(String id) {
    return {
      'id': id,
      'group_code': 'ID_CARD_TYPE',
      'code': code,
      'label': label,
      'value': label,
      'description': null,
      'order_no': 0,
    };
  }
}
