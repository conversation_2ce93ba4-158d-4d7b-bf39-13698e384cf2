import '../../../shared/models/config_model.dart';
import '../../products/models/product_model.dart';

/// Models for get proposals list response
class GetProposalsData {
  final int? totalCount;
  final int? limit;
  final int? offset;
  final List<ProposalItem>? proposals;

  GetProposalsData({
    this.totalCount,
    this.limit,
    this.offset,
    this.proposals,
  });

  factory GetProposalsData.fromJson(Map<String, dynamic> json) {
    return GetProposalsData(
      totalCount: json['total_count'],
      limit: json['limit'],
      offset: json['offset'],
      proposals: (json['proposals'] as List<dynamic>?)
          ?.map((item) => ProposalItem.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (totalCount != null) 'total_count': totalCount,
      if (limit != null) 'limit': limit,
      if (offset != null) 'offset': offset,
      if (proposals != null) 'proposals': proposals!.map((item) => item.toJson()).toList(),
    };
  }
}

class ProposalItem {
  final String? id;
  final ProductInfo? product;
  final String? borrowerName;
  final String? borrowerPhone;
  final int? loanAmount;
  final ConfigModel? status;
  final FlowStepInfo? currentFlowStep;
  final BranchInfo? branch;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final CreatedByInfo? createdBy;

  ProposalItem({
    this.id,
    this.product,
    this.borrowerName,
    this.borrowerPhone,
    this.loanAmount,
    this.status,
    this.currentFlowStep,
    this.branch,
    this.createdAt,
    this.updatedAt,
    this.createdBy,
  });

  factory ProposalItem.fromJson(Map<String, dynamic> json) {
    return ProposalItem(
      id: json['id'],
      product: json['product'] != null ? ProductInfo.fromJson(json['product']) : null,
      borrowerName: json['borrower_name'],
      borrowerPhone: json['borrower_phone'],
      loanAmount: json['loan_amount'],
      status: json['status'] != null ? ConfigModel.fromJson(json['status']) : null,
      currentFlowStep: json['current_flow_step'] != null ? FlowStepInfo.fromJson(json['current_flow_step']) : null,
      branch: json['branch'] != null ? BranchInfo.fromJson(json['branch']) : null,
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at']) : null,
      createdBy: json['created_by'] != null ? CreatedByInfo.fromJson(json['created_by']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (product != null) 'product': product!.toJson(),
      if (borrowerName != null) 'borrower_name': borrowerName,
      if (borrowerPhone != null) 'borrower_phone': borrowerPhone,
      if (loanAmount != null) 'loan_amount': loanAmount,
      if (status != null) 'status': status!.toJson(),
      if (currentFlowStep != null) 'current_flow_step': currentFlowStep!.toJson(),
      if (branch != null) 'branch': branch!.toJson(),
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
      if (createdBy != null) 'created_by': createdBy!.toJson(),
    };
  }

  // Backward compatibility getters
  String? get productId => product?.id;
  String? get productName => product?.name;
  ConfigModel? get statusConfig => status;
}

class ProductInfo {
  final String? id;
  final String? code;
  final String? name;
  final ProductDisplayConfig? displayConfig;

  ProductInfo({
    this.id,
    this.code,
    this.name,
    this.displayConfig,
  });

  factory ProductInfo.fromJson(Map<String, dynamic> json) {
    return ProductInfo(
      id: json['id'],
      code: json['code'],
      name: json['name'],
      displayConfig: json['display_config'] != null 
        ? ProductDisplayConfig.fromJson(json['display_config']) 
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (code != null) 'code': code,
      if (name != null) 'name': name,
      if (displayConfig != null) 'display_config': displayConfig!.toJson(),
    };
  }
}

class FlowStepInfo {
  final String? id;
  final String? code;
  final String? name;
  final int? stepOrder;

  FlowStepInfo({
    this.id,
    this.code,
    this.name,
    this.stepOrder,
  });

  factory FlowStepInfo.fromJson(Map<String, dynamic> json) {
    return FlowStepInfo(
      id: json['id'],
      code: json['code'],
      name: json['name'],
      stepOrder: json['step_order'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (code != null) 'code': code,
      if (name != null) 'name': name,
      if (stepOrder != null) 'step_order': stepOrder,
    };
  }
}

class BranchInfo {
  final String? id;
  final String? code;
  final String? name;

  BranchInfo({
    this.id,
    this.code,
    this.name,
  });

  factory BranchInfo.fromJson(Map<String, dynamic> json) {
    return BranchInfo(
      id: json['id'],
      code: json['code'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (code != null) 'code': code,
      if (name != null) 'name': name,
    };
  }
}

class CreatedByInfo {
  final String? id;
  final String? fullName;

  CreatedByInfo({
    this.id,
    this.fullName,
  });

  factory CreatedByInfo.fromJson(Map<String, dynamic> json) {
    return CreatedByInfo(
      id: json['id'],
      fullName: json['full_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (fullName != null) 'full_name': fullName,
    };
  }
}

