/// Exception types for transaction operations
enum TransactionExceptionType {
  validation,
  unauthorized,
  networkError,
  serverError,
  apiError,
}

class TransactionException implements Exception {
  final TransactionExceptionType type;
  final String message;
  final String? details;
  final int? statusCode;

  TransactionException({
    required this.type,
    required this.message,
    this.details,
    this.statusCode,
  });

  @override
  String toString() {
    return 'TransactionException(type: $type, message: $message, details: $details, statusCode: $statusCode)';
  }
}
