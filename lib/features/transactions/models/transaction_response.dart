// Response models for transaction operations

class TransactionProposalData {
  final String? proposalId;
  final String? status;
  final String? message;
  final DateTime? createdAt;

  TransactionProposalData({
    this.proposalId,
    this.status,
    this.message,
    this.createdAt,
  });

  factory TransactionProposalData.fromJson(Map<String, dynamic> json) {
    return TransactionProposalData(
      proposalId: json['proposalId'] ?? json['proposal_id'],
      status: json['status'],
      message: json['message'],
      createdAt: json['createdAt'] != null 
          ? DateTime.tryParse(json['createdAt'])
          : json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (proposalId != null) 'proposal_id': proposalId,
      if (status != null) 'status': status,
      if (message != null) 'message': message,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
    };
  }
}

class TransactionProposalResponse {
  final String? proposalId;
  final String? status;
  final String? message;
  final DateTime? createdAt;

  TransactionProposalResponse({
    this.proposalId,
    this.status,
    this.message,
    this.createdAt,
  });

  factory TransactionProposalResponse.fromJson(Map<String, dynamic> json) {
    return TransactionProposalResponse(
      proposalId: json['proposal_id'],
      status: json['status'],
      message: json['message'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (proposalId != null) 'proposal_id': proposalId,
      if (status != null) 'status': status,
      if (message != null) 'message': message,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
    };
  }
}

