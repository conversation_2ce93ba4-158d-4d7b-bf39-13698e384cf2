import 'dart:io';

/// Document types that can be uploaded - matching backend types
enum DocumentType {
  // Backend supported types
  idCard('CMND/CCCD', 'ID_CARD'),
  passport('<PERSON><PERSON> chiếu', 'PASSPORT'),
  drivingLicense('Bằng lái xe', 'DRIVING_LICENSE'),
  incomeCertificate('Gi<PERSON>y xác nhận thu nhập', 'INCOME_CERTIFICATE'),
  businessLicense('Gi<PERSON>y phép kinh doanh', 'BUSINESS_LICENSE'),
  companyRegistration('Đăng ký doanh nghiệp', 'COMPANY_REGISTRATION'),
  contract('Hợp đồng', 'CONTRACT'),
  collateralDoc('Tài liệu tài sản đảm bảo', 'COLLATERAL_DOC'),
  marriageCert('Gi<PERSON>y đăng ký kết hôn', 'MARRIAGE_CERT'),
  otherDoc('Tài liệ<PERSON> kh<PERSON>', 'OTHER_DOC');

  const DocumentType(this.displayName, this.backendCode);
  final String displayName;
  final String backendCode;
  
  /// Get backend code for API calls
  String get code => backendCode;
}

/// Document upload status
enum DocumentStatus {
  pending('Chưa tải'),
  uploading('Đang tải lên'),
  uploaded('Đã tải'),
  failed('Lỗi tải lên');

  const DocumentStatus(this.displayName);
  final String displayName;
}

/// File type for documents
enum FileType {
  image('image'),
  pdf('pdf'),
  document('document');

  const FileType(this.value);
  final String value;
}

/// Uploaded file information
class UploadedFile {
  final String id;
  final String name;
  final String path;
  final FileType type;
  final int size;
  final DateTime uploadedAt;
  final String? url; // Server URL after upload
  final File? localFile; // Local file reference

  const UploadedFile({
    required this.id,
    required this.name,
    required this.path,
    required this.type,
    required this.size,
    required this.uploadedAt,
    this.url,
    this.localFile,
  });

  /// Get file size in readable format
  String get sizeFormatted {
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    if (size < 1024 * 1024 * 1024) return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  /// Get file extension
  String get extension {
    return path.split('.').last.toLowerCase();
  }

  /// Check if file is image
  bool get isImage {
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp'].contains(extension);
  }

  /// Check if file is PDF
  bool get isPdf {
    return extension == 'pdf';
  }

  UploadedFile copyWith({
    String? id,
    String? name,
    String? path,
    FileType? type,
    int? size,
    DateTime? uploadedAt,
    String? url,
    File? localFile,
  }) {
    return UploadedFile(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      type: type ?? this.type,
      size: size ?? this.size,
      uploadedAt: uploadedAt ?? this.uploadedAt,
      url: url ?? this.url,
      localFile: localFile ?? this.localFile,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'type': type.value,
      'size': size,
      'uploadedAt': uploadedAt.toIso8601String(),
      'url': url,
    };
  }

  factory UploadedFile.fromJson(Map<String, dynamic> json) {
    return UploadedFile(
      id: json['id'],
      name: json['name'],
      path: json['path'],
      type: FileType.values.firstWhere((e) => e.value == json['type']),
      size: json['size'],
      uploadedAt: DateTime.parse(json['uploadedAt']),
      url: json['url'],
    );
  }
}

/// Document model for transaction
class DocumentModel {
  final String id;
  final DocumentType type;
  final String name;
  final bool isRequired;
  final DocumentStatus status;
  final List<UploadedFile> files;
  final String? description;
  final String? productCode; // Associated product code
  final String? mappingKey; // Key for mapping to DocumentsInfo fields

  const DocumentModel({
    required this.id,
    required this.type,
    required this.name,
    required this.isRequired,
    required this.status,
    required this.files,
    this.description,
    this.productCode,
    this.mappingKey,
  });

  /// Check if document has any uploaded files
  bool get hasFiles => files.isNotEmpty;

  /// Check if document is completed (has required files)
  bool get isCompleted => hasFiles && status == DocumentStatus.uploaded;

  /// Get total files count
  int get filesCount => files.length;

  /// Get total size of all files
  int get totalSize => files.fold(0, (sum, file) => sum + file.size);

  /// Get total size formatted
  String get totalSizeFormatted {
    if (totalSize < 1024) return '${totalSize}B';
    if (totalSize < 1024 * 1024) return '${(totalSize / 1024).toStringAsFixed(1)}KB';
    if (totalSize < 1024 * 1024 * 1024) return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  DocumentModel copyWith({
    String? id,
    DocumentType? type,
    String? name,
    bool? isRequired,
    DocumentStatus? status,
    List<UploadedFile>? files,
    String? description,
    String? productCode,
    String? mappingKey,
  }) {
    return DocumentModel(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      isRequired: isRequired ?? this.isRequired,
      status: status ?? this.status,
      files: files ?? this.files,
      description: description ?? this.description,
      productCode: productCode ?? this.productCode,
      mappingKey: mappingKey ?? this.mappingKey,
    );
  }

  /// Add a file to this document
  DocumentModel addFile(UploadedFile file) {
    return copyWith(
      files: [...files, file],
      status: DocumentStatus.uploaded,
    );
  }

  /// Remove a file from this document
  DocumentModel removeFile(String fileId) {
    final updatedFiles = files.where((f) => f.id != fileId).toList();
    return copyWith(
      files: updatedFiles,
      status: updatedFiles.isEmpty ? DocumentStatus.pending : DocumentStatus.uploaded,
    );
  }

  /// Update document status
  DocumentModel updateStatus(DocumentStatus newStatus) {
    return copyWith(status: newStatus);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'name': name,
      'isRequired': isRequired,
      'status': status.name,
      'files': files.map((f) => f.toJson()).toList(),
      'description': description,
      'productCode': productCode,
      'mappingKey': mappingKey,
    };
  }

  factory DocumentModel.fromJson(Map<String, dynamic> json) {
    return DocumentModel(
      id: json['id'],
      type: DocumentType.values.firstWhere((e) => e.name == json['type']),
      name: json['name'],
      isRequired: json['isRequired'],
      status: DocumentStatus.values.firstWhere((e) => e.name == json['status']),
      files: (json['files'] as List<dynamic>)
          .map((f) => UploadedFile.fromJson(f))
          .toList(),
      description: json['description'],
      productCode: json['productCode'],
      mappingKey: json['mappingKey'],
    );
  }
}
