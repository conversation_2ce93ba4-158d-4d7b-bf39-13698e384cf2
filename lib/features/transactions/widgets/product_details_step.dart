import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../models/form_data/form_data.dart';
import '../blocs/transaction_form_bloc.dart';
import 'forms/installment_loan_form_widget.dart';

/// Product Details Step - Using Typed Form Data
/// Sử dụng typed form data và tích hợp với các form widget chuyên biệt
class ProductDetailsStep extends StatefulWidget {
  final ProductModel? product;
  final CustomerModel? selectedCustomer;
  final GlobalKey? formKey; // Add form key parameter
  // REMOVE: final Function(Map<String, dynamic>) onDetailsChanged;
  // REMOVE: final Function(bool)? onValidationChanged; // Deprecated - sẽ remove sau

  const ProductDetailsStep({
    super.key,
    required this.product,
    this.selectedCustomer,
    this.formKey, // Add form key parameter
    // REMOVE: required this.onDetailsChanged,
    // REMOVE: this.onValidationChanged,
  });

  @override
  State<ProductDetailsStep> createState() => _ProductDetailsStepState();
}

class _ProductDetailsStepState extends State<ProductDetailsStep>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;
  
  // Track last customer to avoid duplicate SetDefaults calls
  CustomerModel? _lastProcessedCustomer;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    
    if (widget.product == null) {
      return const SizedBox();
    }

    // Call SetDefaults when customer changes to ensure data is mapped to TransactionFormBloc
    if (widget.selectedCustomer != null && widget.selectedCustomer != _lastProcessedCustomer) {
      _lastProcessedCustomer = widget.selectedCustomer;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<TransactionFormBloc>().add(SetDefaults(customer: widget.selectedCustomer));
      });
    }

    return _buildFormWidget();
  }

  /// Build appropriate form widget based on product type
  Widget _buildFormWidget() {
    final productCode = widget.product!.code;
    
    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
        return _buildInstallmentLoanForm();
      case 'PERSONAL_LOAN':
        return _buildPersonalLoanForm();
      default:
        return _buildErrorWidget('Sản phẩm không được hỗ trợ: $productCode');
    }
  }

  /// Build installment loan form - NO BlocBuilder needed, parent handles state
  Widget _buildInstallmentLoanForm() {
    // Get form data from parent's BlocBuilder context
    final blocState = context.read<TransactionFormBloc>().state;
    InstallmentLoanFormData formData = InstallmentLoanFormData();
    
    if (blocState is TransactionFormLoaded) {
      if (blocState.formData is InstallmentLoanFormData) {
        formData = blocState.formData as InstallmentLoanFormData;
        debugPrint('✅ Using existing InstallmentLoanFormData from parent bloc state');
      } else if (blocState.formData != null) {
        // Should not happen, but handle gracefully
        debugPrint('⚠️ FormData exists but is not InstallmentLoanFormData: ${blocState.formData.runtimeType}');
        formData = InstallmentLoanFormData();
      } else {
        // No form data exists, create new
        debugPrint('📝 Creating new InstallmentLoanFormData');
        formData = InstallmentLoanFormData();
      }
    }
    
    return InstallmentLoanFormWidget(
      key: widget.formKey,
      formData: formData,
      selectedCustomer: widget.selectedCustomer,
      onChanged: (newFormData) {
        // Dispatch typed event to TransactionFormBloc with merge flag
        context.read<TransactionFormBloc>().add(UpdateFormData(newFormData, shouldMerge: true));
      },
    );
  }


  /// Build personal loan form (placeholder for now)
  Widget _buildPersonalLoanForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '📋 Thông tin Vay cá nhân',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Vui lòng điền đầy đủ thông tin để tạo giao dịch vay cá nhân',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingXL),
          
          // TODO: Implement PersonalLoanFormWidget
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: AppColors.warning),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.info_circle,
                  color: AppColors.warning,
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    'PersonalLoanFormWidget chưa được implement. Sẽ được phát triển trong tương lai.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.warning,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.alert_triangle,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  // Force rebuild
                });
              },
              icon: Icon(TablerIcons.refresh),
              label: Text('Thử lại'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
