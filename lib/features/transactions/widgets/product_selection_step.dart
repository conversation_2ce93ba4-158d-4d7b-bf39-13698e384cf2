import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:shimmer/shimmer.dart';
import '../../../core/theme/index.dart';
import '../../../features/products/index.dart';
import '../../../shared/utils/product_icon_mapper.dart';
import 'product_filter_bar.dart';

class ProductSelectionStep extends StatefulWidget {
  final ProductModel? selectedProduct;
  final Function(ProductModel) onProductSelected;

  const ProductSelectionStep({
    super.key,
    required this.selectedProduct,
    required this.onProductSelected,
  });

  @override
  State<ProductSelectionStep> createState() => _ProductSelectionStepState();
}

class _ProductSelectionStepState extends State<ProductSelectionStep>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Bắt buộc phải gọi!
    
    return BlocBuilder<ProductBloc, ProductState>(
      builder: (context, state) {
        return _buildContent(context, state);
      },
    );
  }

  Widget _buildContent(BuildContext context, ProductState state) {
    if (state is ProductLoading) {
      return _buildLoadingState();
    } else if (state is ProductError) {
      return _buildErrorState(context, state);
    } else if (state is ProductEmpty) {
      return _buildEmptyState();
    } else if (state is ProductLoaded) {
      return _buildLoadedState(context, state);
    }

    return _buildLoadingState();
  }

  Widget _buildLoadingState() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header shimmer
          _buildHeaderShimmer(),
          SizedBox(height: AppDimensions.spacingL),

          // Search and Filter shimmer
          _buildSearchFilterShimmer(),
          SizedBox(height: AppDimensions.spacingL),

          // Products Grid shimmer
          _buildProductsGridShimmer(),
        ],
      ),
    );
  }

  Widget _buildHeaderShimmer() {
    return Shimmer.fromColors(
      baseColor: AppColors.neutral200,
      highlightColor: AppColors.neutral100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 280,
            height: 28,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Container(
            width: 200,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchFilterShimmer() {
    return Shimmer.fromColors(
      baseColor: AppColors.neutral200,
      highlightColor: AppColors.neutral100,
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsGridShimmer() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: 6, // Show 6 shimmer cards
      itemBuilder: (context, index) {
        return _buildProductCardShimmer();
      },
    );
  }

  Widget _buildProductCardShimmer() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Shimmer.fromColors(
        baseColor: AppColors.neutral200,
        highlightColor: AppColors.neutral100,
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon and selection indicator placeholder
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),

              SizedBox(height: AppDimensions.spacingM),

              // Product name placeholder
              Container(
                width: double.infinity,
                height: 18,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),

              SizedBox(height: AppDimensions.spacingXS),

              // Description placeholder - 2 lines
              Container(
                width: double.infinity,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              SizedBox(height: AppDimensions.spacingXS),
              Container(
                width: double.infinity * 0.7,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),

              SizedBox(height: AppDimensions.spacingM),

              // Status and group info placeholders
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Group info placeholder
                    Row(
                      children: [
                        Container(
                          width: 4,
                          height: 4,
                          margin: EdgeInsets.only(
                            top: 6,
                            right: AppDimensions.spacingS,
                          ),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        Expanded(
                          child: Container(
                            width: double.infinity * 0.8,
                            height: 12,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: AppDimensions.spacingXS),
                    // Featured placeholder (sometimes visible)
                    Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: AppDimensions.spacingXS),
                        Container(
                          width: 60,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, ProductError state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            TablerIcons.exclamation_circle,
            size: 64,
            color: AppColors.error,
          ),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Có lỗi xảy ra',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            state.message,
            style: TextStyle(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppDimensions.spacingL),
          ElevatedButton.icon(
            onPressed: () =>
                context.read<ProductBloc>().add(RetryLoadProducts()),
            icon: const Icon(TablerIcons.refresh),
            label: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(TablerIcons.package, size: 64, color: AppColors.textSecondary),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Không có sản phẩm nào',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(BuildContext context, ProductLoaded state) {
    final products = state.displayProducts;

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),
          SizedBox(height: AppDimensions.spacingL),

          // Search and Filter
          _buildSearchAndFilter(context, state),
          SizedBox(height: AppDimensions.spacingL),

          // Products Grid
          _buildProductsGrid(context, products),

          // Selected product details
          if (widget.selectedProduct != null) ...[
            SizedBox(height: AppDimensions.spacingL),
            _buildSelectedProductDetails(context, state),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '🏦 Chọn sản phẩm cho giao dịch',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.kienlongOrange,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        Text(
          'Chọn loại sản phẩm bạn muốn tạo giao dịch',
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
        ),
      ],
    );
  }

  Widget _buildSearchAndFilter(BuildContext context, ProductLoaded state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search field
        TextField(
          decoration: InputDecoration(
            hintText: 'Tìm kiếm sản phẩm...',
            prefixIcon: const Icon(TablerIcons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
          ),
          onChanged: (query) {
            context.read<ProductBloc>().add(SearchProducts(query));
          },
        ),
        
        SizedBox(height: AppDimensions.spacingM),
        
        // Filter bar
        ProductFilterBar(
          selectedFilter: state.selectedGroup,
          filters: ['Tất cả sản phẩm', ...state.productGroups],
          onFilter: (group) {
            context.read<ProductBloc>().add(FilterProductsByGroup(group));
          },
        ),
      ],
    );
  }

  Widget _buildProductsGrid(BuildContext context, List<ProductModel> products) {
    if (products.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Text(
            'Không tìm thấy sản phẩm nào',
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        final isSelected = widget.selectedProduct?.id == product.id;

        return _buildProductCard(context, product, isSelected);
      },
    );
  }

  Widget _buildProductCard(
    BuildContext context,
    ProductModel product,
    bool isSelected,
  ) {
    final productColor = product.hasCustomColor
        ? product.displayColor
        : AppColors.kienlongOrange;

    return GestureDetector(
      onTap: () {
        widget.onProductSelected(product);
        context.read<ProductBloc>().add(SelectProduct(product.id));
      },
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected ? productColor : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? productColor.withValues(alpha: 0.2)
                  : AppColors.shadowLight,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon and selection indicator
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: productColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                    child: Icon(
                      ProductIconMapper.getIcon(
                        product.displayConfig?.icon ?? product.group,
                      ),
                      color: productColor,
                      size: 24,
                    ),
                  ),
                  const Spacer(),
                  if (isSelected)
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: productColor,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        TablerIcons.check,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                ],
              ),

              SizedBox(height: AppDimensions.spacingM),

              // Product name
              Text(
                product.displayName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isSelected ? productColor : AppColors.textPrimary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: AppDimensions.spacingXS),

              // Description
              Text(
                product.displayDescription,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: AppDimensions.spacingM),

              // Status and group info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 4,
                          height: 4,
                          margin: EdgeInsets.only(
                            top: 6,
                            right: AppDimensions.spacingS,
                          ),
                          decoration: BoxDecoration(
                            color: productColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'Nhóm: ${product.group}',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppColors.textSecondary),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    if (product.isFeatured) ...[
                      SizedBox(height: AppDimensions.spacingXS),
                      Row(
                        children: [
                          Icon(
                            TablerIcons.star,
                            size: 12,
                            color: AppColors.warning,
                          ),
                          SizedBox(width: AppDimensions.spacingXS),
                          Text(
                            'Nổi bật',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: AppColors.warning,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedProductDetails(
    BuildContext context,
    ProductLoaded state,
  ) {
    final selectedProduct = state.selectedProduct;
    if (selectedProduct == null) return const SizedBox.shrink();

    final productColor = selectedProduct.hasCustomColor
        ? selectedProduct.displayColor
        : AppColors.kienlongOrange;

    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: productColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: productColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                ProductIconMapper.getIcon(
                  selectedProduct.displayConfig?.icon ?? selectedProduct.group,
                ),
                color: productColor,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  selectedProduct.displayName,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: productColor,
                  ),
                ),
              ),
              if (selectedProduct.isFeatured)
                Icon(TablerIcons.star, color: AppColors.warning, size: 20),
            ],
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Description
          Text(
            'Mô tả:',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            selectedProduct.displayDescription,
            style: Theme.of(context).textTheme.bodyMedium,
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Product info
          _buildProductInfo(context, selectedProduct),
        ],
      ),
    );
  }

  Widget _buildProductInfo(BuildContext context, ProductModel product) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thông tin sản phẩm:',
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: AppDimensions.spacingS),

        _buildInfoRow(context, 'Mã sản phẩm', product.code),
        _buildInfoRow(context, 'Nhóm sản phẩm', product.group),
        _buildInfoRow(context, 'Trạng thái', product.status),

        if (product.isFeatured)
          _buildInfoRow(context, 'Đặc biệt', 'Sản phẩm nổi bật'),
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }


}
