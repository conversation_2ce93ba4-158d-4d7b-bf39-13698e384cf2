import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../features/customers/models/customer_model.dart';
import '../../../features/customers/blocs/customer_list_bloc.dart';
import '../../../features/customers/blocs/customer_list_event.dart';
import '../../../features/customers/blocs/customer_list_state.dart';
import '../../../features/customers/screens/add_customer_screen.dart';
import '../../../features/auth/services/auth_service.dart';
import '../../../shared/widgets/qr_scanner_widget.dart';
import '../../auth/services/qr_scanner_service.dart';
import '../../../shared/utils/app_logger.dart';
import '../../../shared/utils/qr_utils.dart';
import '../../../shared/models/qr_models.dart';

class CustomerSelectionStep extends StatefulWidget {
  final CustomerModel? selectedCustomer;
  final Function(CustomerModel) onCustomerSelected;

  /// Nếu có preSelectedCustomer, sẽ lock selection và chỉ hiển thị customer này
  final CustomerModel? preSelectedCustomer;

  const CustomerSelectionStep({
    super.key,
    required this.selectedCustomer,
    required this.onCustomerSelected,
    this.preSelectedCustomer,
  });

  @override
  State<CustomerSelectionStep> createState() => _CustomerSelectionStepState();
}

class _CustomerSelectionStepState extends State<CustomerSelectionStep>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // Tab controller
  late TabController _tabController;
  int _currentTabIndex = 0; // 0: Recent, 1: All

  // Controllers
  final TextEditingController _searchController = TextEditingController();
  late ScrollController _scrollController;

  // Search debounce timer
  Timer? _searchDebounceTimer;

  // Animation controllers
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // State variables
  bool _isSelectionLocked = false;
  CustomerModel? _currentSelectedCustomer;
  final AuthService _authService = AuthService();
  final AppLogger _logger = AppLogger();
  
  // FAB expansion state
  bool _isFabExpanded = false;

  // All customers tab state
  String _searchText = '';
  bool _isLoadingMore = false;
  Timer? _scrollDebounceTimer;
  static const Duration _searchDebounceDelay = Duration(milliseconds: 500);

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    _initializeAnimations();
    _initializeScrollController();

    // Nếu có preSelectedCustomer, lock selection
    if (widget.preSelectedCustomer != null) {
      _isSelectionLocked = true;
      _currentSelectedCustomer = widget.preSelectedCustomer;
    } else {
      _currentSelectedCustomer = widget.selectedCustomer;
    }

    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isSelectionLocked) {
        // Nếu locked, chỉ hiển thị customer đã chọn
        return;
      } else {
        // Load recent customers cho tab đầu tiên
        _loadRecentCustomers();
      }
    });
  }

  void _initializeAnimations() {
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );
    _shimmerController.repeat();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
  }

  void _initializeScrollController() {
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    // Chỉ áp dụng load more cho tab "Tất cả"
    if (_currentTabIndex != 1) return;

    // Debounce scroll events to prevent too many triggers
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      // Check if user scrolled to near the bottom and not already loading
      if (!_isLoadingMore &&
          _scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent - 200) {
        _onLoadMore();
      }
    });
  }

  void _onTabChanged() {
    if (!mounted) return;

    final newIndex = _tabController.index;
    if (newIndex == _currentTabIndex) return;

    setState(() {
      _currentTabIndex = newIndex;
    });

    // Load data based on tab
    if (newIndex == 0) {
      // Recent customers tab
      if (_searchText.isEmpty) {
        _loadRecentCustomers();
      } else {
        _loadRecentCustomersWithSearch();
      }
    } else {
      // All customers tab
      if (_searchText.isEmpty) {
        _loadAllCustomers();
      } else {
        _applySearchAndFilters();
      }
    }
  }

  /// Load customers từ 7 ngày gần đây với staffIds
  void _loadRecentCustomers() {
    final now = DateTime.now();
    final sevenDaysAgo = now.subtract(const Duration(days: 7));

    // Lấy CIF của user hiện tại
    String? currentUserCif;
    if (_authService.isAuthenticated &&
        _authService.currentUser?.profile != null) {
      currentUserCif = _authService.currentUser!.profile!.personCifNo;
    }

    context.read<CustomerListBloc>().add(
      CustomerListFiltered(
        fromDate: sevenDaysAgo,
        toDate: now,
        staffIds: currentUserCif != null ? [currentUserCif] : null,
      ),
    );
  }

  /// Load recent customers với search query
  void _loadRecentCustomersWithSearch() {
    if (_searchText.isEmpty) {
      _loadRecentCustomers();
      return;
    }

    final now = DateTime.now();
    final sevenDaysAgo = now.subtract(const Duration(days: 7));

    // Lấy CIF của user hiện tại
    String? currentUserCif;
    if (_authService.isAuthenticated &&
        _authService.currentUser?.profile != null) {
      currentUserCif = _authService.currentUser!.profile!.personCifNo;
    }

    context.read<CustomerListBloc>().add(
      CustomerListFiltered(
        searchQuery: _searchText.trim(),
        fromDate: sevenDaysAgo,
        toDate: now,
        staffIds: currentUserCif != null ? [currentUserCif] : null,
      ),
    );
  }

  /// Load all customers
  void _loadAllCustomers() {
    context.read<CustomerListBloc>().add(const CustomerListInitialized());
  }

  /// Apply search and filters for all customers tab
  void _applySearchAndFilters() {
    if (_searchText.isEmpty) {
      _loadAllCustomers();
      return;
    }

    context.read<CustomerListBloc>().add(
      CustomerListFiltered(searchQuery: _searchText.trim()),
    );
  }

  void _onSearchChanged(String text) {
    // Cancel previous timer first
    _searchDebounceTimer?.cancel();

    // Update local state immediately without setState to avoid rebuild
    _searchText = text;

    // Schedule search with debounce
    _searchDebounceTimer = Timer(_searchDebounceDelay, () {
      if (!mounted) return;
      
      // Apply search based on current tab
      if (_currentTabIndex == 0) {
        // Recent customers tab - search with date filter
        _loadRecentCustomersWithSearch();
      } else {
        // All customers tab - search all customers
        _applySearchAndFilters();
      }
    });
  }

  Color _getStatusColorByLabel(String status) {
    switch (status) {
      case 'Đang chăm sóc':
        return AppColors.warning;
      case 'Tiềm năng':
        return AppColors.info;
      case 'Đã giao dịch':
        return AppColors.success;
      case 'Đã chốt':
        return AppColors.kienlongOrange;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Đang chăm sóc':
        return TablerIcons.heart_handshake;
      case 'Tiềm năng':
        return TablerIcons.target;
      case 'Đã giao dịch':
        return TablerIcons.circle_check;
      case 'Đã chốt':
        return TablerIcons.trophy;
      default:
        return TablerIcons.user;
    }
  }

  void _onLoadMore() {
    // Prevent duplicate calls
    if (_isLoadingMore) return;

    // Check if we can load more from current state
    final currentState = context.read<CustomerListBloc>().state;
    if (currentState is CustomerListSuccess && !currentState.hasMore) {
      return; // No more data to load
    }

    _isLoadingMore = true;
    context.read<CustomerListBloc>().add(
      const CustomerListLoadMore(pLimit: 10),
    );
  }

  @override
  void dispose() {
    _searchDebounceTimer?.cancel();
    _scrollDebounceTimer?.cancel();
    _searchController.dispose();
    _scrollController.dispose();
    _tabController.dispose();
    _shimmerController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Bắt buộc phải gọi!
    
    // Nếu selection bị lock, hiển thị customer đã chọn
    if (_isSelectionLocked && widget.preSelectedCustomer != null) {
      return _buildLockedState(context, widget.preSelectedCustomer!);
    }

    return Stack(
      children: [
        GestureDetector(
          onTap: () {
            // Collapse FAB when tapping outside
            if (_isFabExpanded) {
              _toggleFab();
            }
          },
          child: BlocListener<CustomerListBloc, CustomerListState>(
            listener: (context, state) {
              // Reset loading more flag when load more completes
              if (state is CustomerListSuccess || state is CustomerListFailure) {
                _isLoadingMore = false;
              }
            },
            child: Column(
              children: [
                // Header
                // _buildHeader(context),
                SizedBox(height: AppDimensions.spacingS),
                
                // Search bar (common for both tabs)
                _buildSearchBar(context),
                SizedBox(height: AppDimensions.spacingS),
                
                // Tab Bar
                _buildTabBar(context),
                SizedBox(height: AppDimensions.spacingS),

                // Tab Content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildRecentCustomersTab(context),
                      _buildAllCustomersTab(context),
                    ],
                  ),
                ),

                // Selected customer details
                // if (_currentSelectedCustomer != null) ...[
                //   SizedBox(height: AppDimensions.spacingM),
                //   _buildSelectedCustomerSummary(context, _currentSelectedCustomer!),
                // ],
              ],
            ),
          ),
        ),
        
        // Floating Action Buttons
        _buildFloatingActionButtons(context),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      padding: EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.12),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.kienlongOrange,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          boxShadow: [
            BoxShadow(
              color: AppColors.kienlongOrange.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppColors.textSecondary,
        labelStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        unselectedLabelStyle: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        dividerColor: Colors.transparent,
        indicatorSize: TabBarIndicatorSize.tab,
        splashFactory: NoSplash.splashFactory,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        tabs: const [
          Tab(
            height: 36,
            text: 'Gần đây',
          ),
          Tab(
            height: 36,
            text: 'Tất cả',
          ),
        ],
      ),
    );
  }

  Widget _buildRecentCustomersTab(BuildContext context) {
    return BlocBuilder<CustomerListBloc, CustomerListState>(
      buildWhen: (previous, current) {
        // Chỉ rebuild khi ở tab recent (index 0)
        if (_currentTabIndex != 0) return false;

        // Always rebuild for different state types
        if (previous.runtimeType != current.runtimeType) {
          return true;
        }

        // For success states, rebuild if customer data changes
        if (current is CustomerListSuccess && previous is CustomerListSuccess) {
          return current.customers != previous.customers;
        }

        return true;
      },
      builder: (context, state) {
        if (state is CustomerListLoading) {
          return _buildLoadingContent();
        } else if (state is CustomerListSuccess) {
          return _buildRecentCustomersList(context, state.customers);
        } else if (state is CustomerListFailure) {
          return _buildErrorContent(context, state.error);
        }

        return _buildLoadingContent();
      },
    );
  }

  Widget _buildAllCustomersTab(BuildContext context) {
    return _buildAllCustomersList(context);
  }


  Widget _buildAllCustomersList(BuildContext context) {
    return BlocBuilder<CustomerListBloc, CustomerListState>(
      buildWhen: (previous, current) {
        // Chỉ rebuild khi ở tab all customers (index 1)
        if (_currentTabIndex != 1) return false;

        // Always rebuild for different state types
        if (previous.runtimeType != current.runtimeType) {
          return true;
        }

        // For CustomerListSuccess states, rebuild if customer data or pagination changes
        if (current is CustomerListSuccess && previous is CustomerListSuccess) {
          return current.customers.length != previous.customers.length ||
              current.hasMore != previous.hasMore ||
              current.customers != previous.customers;
        }

        // For CustomerListLoadingMore states, rebuild if customer data changes
        if (current is CustomerListLoadingMore &&
            previous is CustomerListLoadingMore) {
          return current.customers.length != previous.customers.length ||
              current.customers != previous.customers;
        }

        return true;
      },
      builder: (context, state) {
        if (state is CustomerListLoading) {
          return _buildLoadingContent();
        } else if (state is CustomerListSuccess) {
          return _buildAllCustomersContent(context, state);
        } else if (state is CustomerListLoadingMore) {
          return _buildLoadingMoreContent(context, state);
        } else if (state is CustomerListFailure) {
          return _buildErrorContent(context, state.error);
        }

        return _buildLoadingContent();
      },
    );
  }

  Widget _buildRecentCustomersList(
    BuildContext context,
    List<CustomerModel> customers,
  ) {
    if (customers.isEmpty) {
      return _buildEmptyRecentState(context);
    }

    return ListView.separated(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      itemCount: customers.length,
      separatorBuilder: (context, index) =>
          SizedBox(height: AppDimensions.spacingXS),
      itemBuilder: (context, index) {
        final customer = customers[index];
        return _buildSelectableCustomerCard(customer);
      },
    );
  }

  Widget _buildAllCustomersContent(
    BuildContext context,
    CustomerListSuccess state,
  ) {
    final customers = state.customers;

    if (customers.isEmpty) {
      return _buildEmptyAllState(context);
    }

    return ListView.separated(
      controller: _scrollController,
      padding: EdgeInsets.all(AppDimensions.paddingS),
      itemCount: customers.length + (state.hasMore ? 1 : 0),
      separatorBuilder: (context, index) {
        if (index == customers.length - 1 && state.hasMore) {
          return const SizedBox.shrink(); // No separator before loading indicator
        }
        return SizedBox(height: AppDimensions.spacingXS);
      },
      itemBuilder: (context, index) {
        // Loading indicator at the end
        if (index == customers.length) {
          return Padding(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            child: Center(
              child: SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: AppColors.kienlongSkyBlue,
                ),
              ),
            ),
          );
        }

        final customer = customers[index];
        return _buildSelectableCustomerCard(customer);
      },
    );
  }

  Widget _buildLoadingMoreContent(
    BuildContext context,
    CustomerListLoadingMore state,
  ) {
    final customers = state.customers;

    return ListView.separated(
      controller: _scrollController,
      padding: EdgeInsets.all(AppDimensions.paddingM),
      itemCount: customers.length + 1, // +1 for loading indicator
      separatorBuilder: (context, index) {
        if (index == customers.length - 1) {
          return const SizedBox.shrink(); // No separator before loading indicator
        }
        return SizedBox(height: AppDimensions.spacingM);
      },
      itemBuilder: (context, index) {
        // Loading indicator at the end
        if (index == customers.length) {
          return Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Center(
              child: SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: AppColors.kienlongSkyBlue,
                ),
              ),
            ),
          );
        }

        final customer = customers[index];
        return _buildSelectableCustomerCard(customer);
      },
    );
  }

  Widget _buildSelectableCustomerCard(CustomerModel customer) {
    final isSelected = _currentSelectedCustomer?.id == customer.id;
    final statusLabel = customer.status?.label ?? '';
    final statusHex = customer.status?.value;
    final statusColor = (statusHex != null && statusHex.isNotEmpty)
        ? Color(int.parse(statusHex.replaceFirst('#', '0xff')))
        : _getStatusColorByLabel(statusLabel);
    final tags = customer.tags;

    // Check for CBPT and Branch info
    final hasCbpt = customer.assignedManager?.fullName?.isNotEmpty == true;
    final hasBranch = customer.branch?.name.isNotEmpty == true;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isSelected
              ? AppColors.kienlongOrange
              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                : AppColors.shadowLight,
            blurRadius: isSelected ? 8 : 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _selectCustomer(customer),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              // Avatar với status indicator
              Stack(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusM,
                      ),
                    ),
                    child: Icon(
                      TablerIcons.user,
                      color: statusColor,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  // Status indicator
                  if (statusLabel.isNotEmpty)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: statusColor,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Theme.of(context).colorScheme.surface,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          _getStatusIcon(statusLabel),
                          size: 8,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),

              SizedBox(width: AppDimensions.spacingM),

              // Customer Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name và Tag
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            customer.fullName,
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? AppColors.kienlongOrange
                                      : null,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (tags.isNotEmpty) ...[
                          SizedBox(width: AppDimensions.spacingXS),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppDimensions.paddingXS,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: tags.first.name == 'VIP'
                                  ? AppColors.kienlongOrange.withValues(
                                      alpha: 0.15,
                                    )
                                  : AppColors.kienlongSkyBlue.withValues(
                                      alpha: 0.15,
                                    ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              tags.first.name ?? '',
                              style: Theme.of(context).textTheme.labelSmall
                                  ?.copyWith(
                                    color: tags.first.name == 'VIP'
                                        ? AppColors.kienlongOrange
                                        : AppColors.kienlongSkyBlue,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 10,
                                  ),
                            ),
                          ),
                        ],
                      ],
                    ),

                    SizedBox(height: AppDimensions.spacingXS),

                    // Phone và Status
                    Row(
                      children: [
                        Icon(
                          TablerIcons.phone,
                          size: 14,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(width: AppDimensions.spacingXS),
                        Expanded(
                          child: Text(
                            customer.phoneNumber ?? 'Chưa có SĐT',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppColors.textSecondary),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (statusLabel.isNotEmpty) ...[
                          SizedBox(width: AppDimensions.spacingS),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppDimensions.paddingXS,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: statusColor.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              statusLabel,
                              style: Theme.of(context).textTheme.labelSmall
                                  ?.copyWith(
                                    color: statusColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 10,
                                  ),
                            ),
                          ),
                        ],
                      ],
                    ),

                    // CBPT và Chi nhánh
                    if (hasCbpt || hasBranch) ...[
                      SizedBox(height: AppDimensions.spacingXS),
                      Row(
                        children: [
                          // CBPT
                          if (hasCbpt) ...[
                            Icon(
                              TablerIcons.user_check,
                              size: 12,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(width: AppDimensions.spacingXS),
                            Expanded(
                              child: Text(
                                'CBPT: ${customer.assignedManager?.fullName ?? ''}',
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: AppColors.textSecondary,
                                      fontSize: 11,
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],

                          // Chi nhánh
                          if (hasBranch) ...[
                            if (hasCbpt)
                              SizedBox(width: AppDimensions.spacingS),
                            Icon(
                              TablerIcons.building,
                              size: 12,
                              color: AppColors.textSecondary,
                            ),
                            SizedBox(width: AppDimensions.spacingXS),
                            Expanded(
                              child: Text(
                                customer.branch?.name ?? '',
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: AppColors.textSecondary,
                                      fontSize: 11,
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              // Selection indicator
              if (isSelected)
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.kienlongOrange,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    TablerIcons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _selectCustomer(CustomerModel customer) {
    setState(() {
      _currentSelectedCustomer = customer;
    });
    widget.onCustomerSelected(customer);

    // Trigger fade animation
    if (_fadeAnimation.value == 0.0) {
      _fadeController.forward();
    }
  }

  Widget _buildLoadingContent() {
    return ListView.separated(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      itemCount: 5,
      separatorBuilder: (context, index) =>
          SizedBox(height: AppDimensions.spacingM),
      itemBuilder: (context, index) => _buildCustomerCardSkeleton(),
    );
  }

  Widget _buildErrorContent(BuildContext context, String error) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.exclamation_circle,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              error,
              style: TextStyle(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                if (_currentTabIndex == 0) {
                  _loadRecentCustomers();
                } else {
                  _loadAllCustomers();
                }
              },
              icon: const Icon(TablerIcons.refresh),
              label: const Text('Thử lại'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyRecentState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchText.isNotEmpty ? TablerIcons.search : TablerIcons.clock, 
              size: 64, 
              color: AppColors.neutral400,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              _searchText.isNotEmpty 
                  ? 'Không tìm thấy khách hàng gần đây'
                  : 'Chưa có khách hàng gần đây',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              _searchText.isNotEmpty
                  ? 'Thử tìm kiếm với từ khóa khác trong khách hàng gần đây'
                  : 'Khách hàng bạn đã chăm sóc trong 7 ngày qua sẽ hiển thị ở đây',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textTertiary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyAllState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.user_search,
              size: 64,
              color: AppColors.neutral400,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              _searchText.isNotEmpty
                  ? 'Không tìm thấy khách hàng'
                  : 'Chưa có khách hàng',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(color: AppColors.textSecondary),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              _searchText.isNotEmpty
                  ? 'Thử tìm kiếm với từ khóa khác hoặc tạo khách hàng mới'
                  : 'Tạo khách hàng đầu tiên hoặc quét mã QR từ CCCD',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: AppColors.textTertiary),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLockedState(
    BuildContext context,
    CustomerModel preSelectedCustomer,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),
          SizedBox(height: AppDimensions.spacingL),

          // Section Title
          Row(
            children: [
              Icon(
                TablerIcons.lock,
                color: AppColors.textSecondary,
                size: AppDimensions.iconS,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Khách hàng đã chọn',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),

          // Customer Card
          _buildSelectableCustomerCard(preSelectedCustomer),

          // SizedBox(height: AppDimensions.spacingL),
          // _buildSelectedCustomerSummary(context, preSelectedCustomer),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '👤 Chọn khách hàng',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingXS),
          Text(
            'Chọn từ khách hàng gần đây hoặc tìm kiếm trong tất cả khách hàng',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildFloatingActionButtons(BuildContext context) {
    return Positioned(
      right: AppDimensions.paddingM,
      bottom: AppDimensions.paddingL,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Expandable options
          if (_isFabExpanded) ...[
            // QR Scanner option
            _buildFabOption(
              icon: TablerIcons.qrcode,
              label: 'Quét mã QR',
              color: AppColors.kienlongSkyBlue,
              onTap: _isSelectionLocked ? null : () {
                _toggleFab();
                _openQrScanner();
              },
            ),
            
            SizedBox(height: AppDimensions.spacingS),
            
            // Add Customer option
            _buildFabOption(
              icon: TablerIcons.user_plus,
              label: 'Khách hàng mới',
              color: AppColors.kienlongOrange,
              onTap: _isSelectionLocked ? null : () {
                _toggleFab();
                _navigateToAddCustomer();
              },
            ),
            
            SizedBox(height: AppDimensions.spacingM),
          ],
          
          // Main FAB
          FloatingActionButton(
            heroTag: "main_fab",
            onPressed: _isSelectionLocked ? null : _toggleFab,
            backgroundColor: _isSelectionLocked 
                ? AppColors.neutral300 
                : AppColors.kienlongOrange,
            foregroundColor: Colors.white,
            child: AnimatedRotation(
              turns: _isFabExpanded ? 0.125 : 0, // 45 degree rotation when expanded
              duration: const Duration(milliseconds: 200),
              child: Icon(
                _isFabExpanded ? TablerIcons.x : TablerIcons.plus,
                size: AppDimensions.iconM,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFabOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback? onTap,
  }) {
    return AnimatedScale(
      scale: _isFabExpanded ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 200),
      child: AnimatedOpacity(
        opacity: _isFabExpanded ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 150),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Label
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: AppDimensions.paddingXS,
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight.withValues(alpha: 0.15),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: onTap != null ? AppColors.textPrimary : AppColors.textSecondary,
                ),
              ),
            ),
            
            SizedBox(width: AppDimensions.spacingS),
            
            // Mini FAB
            FloatingActionButton.small(
              heroTag: "fab_$label",
              onPressed: onTap,
              backgroundColor: onTap != null ? color : AppColors.neutral300,
              foregroundColor: Colors.white,
              child: Icon(
                icon,
                size: AppDimensions.iconS,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFab() {
    setState(() {
      _isFabExpanded = !_isFabExpanded;
    });
  }

  Widget _buildSearchBar(BuildContext context) {
    return Padding(
      padding:const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: TextField(
        controller: _searchController,
        onChanged: _onSearchChanged,
        decoration: InputDecoration(
          hintText: 'Tìm kiếm khách hàng...',
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
          prefixIcon: Icon(
            TablerIcons.search,
            color: AppColors.textSecondary,
            size: AppDimensions.iconS,
          ),
          suffixIcon: _searchText.isNotEmpty
              ? IconButton(
            icon: Icon(
              TablerIcons.x,
              color: AppColors.textSecondary,
              size: AppDimensions.iconS,
            ),
            onPressed: () {
              _searchController.clear();
              _onSearchChanged('');
            },
          )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingM,
          ),
        ),
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }


  Widget _buildCustomerCardSkeleton() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          height: 100, // Height phù hợp với CBPT và chi nhánh
          margin: EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingXS,
          ),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: _buildShimmerEffect(),
        );
      },
    );
  }

  Widget _buildShimmerEffect() {
    final shimmerValue = _shimmerAnimation.value;
    final shimmerColor = Colors.grey.withValues(alpha: 0.3);

    return ClipRRect(
      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(shimmerValue - 1, 0),
            end: Alignment(shimmerValue, 0),
            colors: [Colors.transparent, shimmerColor, Colors.transparent],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
      ),
    );
  }

  /// Mở QR scanner để quét mã QR từ CCCD
  void _openQrScanner() {
    if (_isSelectionLocked) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không thể quét QR khi đã chọn khách hàng'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QrScannerWidget(
          onQrDetected: _handleQrScanResult,
          onError: _handleQrScanError,
          instructionText:
              'Hướng camera vào mã QR trên CCCD hoặc chọn ảnh từ thư viện',
          scanMode: QrScanMode.continuous,
        ),
      ),
    );
  }

  /// Xử lý kết quả scan QR
  void _handleQrScanResult(List<QrScanResult> results) async {
    try {
      await _logger.i('QR scan completed with ${results.length} results');

      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }

      // Lấy kết quả đầu tiên
      final qrResult = results.first;
      await _logger.d('QR content: ${qrResult.value}');

      // Đóng QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Hiển thị thông báo thành công
      _showQrScanMessage('Đã quét thành công mã QR từ CCCD');

      // Parse QR data và tạo khách hàng
      await _parseQrAndCreateCustomer(qrResult.value);
    } catch (e) {
      await _logger.e('Error handling QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR', isError: true);
    }
  }

  /// Xử lý lỗi scan QR
  void _handleQrScanError(String error) async {
    await _logger.e('QR scan error: $error');
    _showQrScanMessage('Lỗi quét QR: $error', isError: true);
  }

  /// Hiển thị thông báo kết quả scan QR
  void _showQrScanMessage(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Parse QR data và tạo khách hàng
  Future<void> _parseQrAndCreateCustomer(String qrData) async {
    try {
      // Parse CCCD QR data using QrUtils to get model
      final cccdQrData = QrUtils.getCccdQrDataModel(qrData);
      
      if (cccdQrData != null && cccdQrData.isValid) {
        // Navigate to add customer screen with CccdQrData model
        await _navigateToAddCustomer(cccdQrData: cccdQrData);
      } else {
        // Show error dialog and fallback option
        _showQrParseErrorDialog(qrData);
      }
    } catch (e) {
      await _logger.e('Error processing QR data: $e');
      _showQrParseErrorDialog(qrData);
    }
  }

  /// Hiển thị dialog lỗi parse QR với option fallback
  void _showQrParseErrorDialog(String qrData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              TablerIcons.alert_triangle,
              color: AppColors.warning,
              size: AppDimensions.iconM,
            ),
            SizedBox(width: AppDimensions.spacingS),
            const Text('Không thể đọc QR'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Không thể đọc thông tin khách hàng từ mã QR này. Bạn có muốn tạo khách hàng mới thủ công không?',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: Text(
                'Dữ liệu QR: ${qrData.length > 100 ? '${qrData.substring(0, 100)}...' : qrData}',
                style: const TextStyle(fontFamily: 'monospace', fontSize: 10),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Test với dữ liệu QR mẫu
              _testWithSampleQrData();
            },
            child: const Text('Test QR'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToAddCustomer();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Tạo thủ công'),
          ),
        ],
      ),
    );
  }

  /// Test với dữ liệu QR mẫu
  Future<void> _testWithSampleQrData() async {
    // Dữ liệu QR CCCD mẫu theo format thường gặp
    // Format: số CCCD|họ tên|ngày sinh|giới tính|địa chỉ|ngày cấp|nơi cấp
    const sampleQrData =
        '001234567890|NGUYỄN VĂN A|15/03/1990|Nam|123 Đường ABC, Phường XYZ, Quận 1, TP.HCM|01/01/2015|Cục Cảnh sát QLHC về TTXH';

    await _parseQrAndCreateCustomer(sampleQrData);
  }

  /// Navigate to add customer screen
  Future<void> _navigateToAddCustomer({
    CccdQrData? cccdQrData,
  }) async {
    if (_isSelectionLocked) {
      _showQrScanMessage(
        'Không thể tạo khách hàng khi đã chọn khách hàng',
        isError: true,
      );
      return;
    }

    // Lưu context trước khi sử dụng async
    final BuildContext currentContext = context;

    try {
      final result = await Navigator.of(currentContext).push(
        MaterialPageRoute(
          builder: (context) => AddCustomerScreen(cccdQrData: cccdQrData),
        ),
      );

      if (result == true) {
        // Customer was created successfully
        _showQrScanMessage('Đã tạo khách hàng thành công');

        // Refresh customer list to include new customer
        _loadRecentCustomers();
      }
    } catch (e) {
      await _logger.e('Error navigating to add customer screen: $e');
      _showQrScanMessage(
        'Có lỗi xảy ra khi mở màn hình tạo khách hàng',
        isError: true,
      );
    }
  }
}