import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionProgressTab extends StatelessWidget {
  final Map<String, dynamic> transaction;

  const TransactionProgressTab({
    super.key,
    required this.transaction,
  });

  List<Map<String, dynamic>> _getProgressSteps() {
    // Mock progress steps based on product type
    switch (transaction['product']) {
      case 'Vay tín chấp':
      case 'Vay thế chấp':
        return [
          {
            'title': 'Tạo hồ sơ',
            'description': '<PERSON>h<PERSON>ch hàng nộp hồ sơ vay',
            'status': 'completed',
            'date': '10/06/2024 09:00',
            'staff': '<PERSON><PERSON>ễ<PERSON>ăn <PERSON>',
            'icon': TablerIcons.file_plus,
            'notes': '<PERSON><PERSON> sơ được tạo thành công với đầy đủ giấy tờ',
          },
          {
            'title': 'Thẩm định',
            'description': '<PERSON>ể<PERSON> tra điều kiện và tài chính',
            'status': 'completed',
            'date': '11/06/2024 14:30',
            'staff': 'Trần Thị B',
            'icon': TablerIcons.search,
            'notes': 'Thẩm định tài chính đạt yêu cầu',
          },
          {
            'title': 'Phê duyệt',
            'description': 'Xét duyệt khoản vay',
            'status': transaction['status'] == 'Thành công' ? 'completed' : 'current',
            'date': transaction['status'] == 'Thành công' ? '12/06/2024 10:15' : null,
            'staff': transaction['status'] == 'Thành công' ? 'Lê Văn C' : null,
            'icon': TablerIcons.check,
            'notes': transaction['status'] == 'Thành công' ? 'Khoản vay được phê duyệt' : 'Đang chờ phê duyệt',
          },
          {
            'title': 'Giải ngân',
            'description': 'Chuyển tiền vào tài khoản',
            'status': transaction['status'] == 'Thành công' ? 'completed' : 'pending',
            'date': transaction['status'] == 'Thành công' ? '12/06/2024 16:00' : null,
            'staff': transaction['status'] == 'Thành công' ? 'Phạm Thị D' : null,
            'icon': TablerIcons.cash,
            'notes': transaction['status'] == 'Thành công' ? 'Giải ngân thành công' : 'Chờ giải ngân',
          },
        ];
      case 'Thẻ tín dụng':
        return [
          {
            'title': 'Đăng ký',
            'description': 'Khách hàng đăng ký thẻ',
            'status': 'completed',
            'date': '10/06/2024 09:00',
            'staff': 'Nguyễn Văn A',
            'icon': TablerIcons.credit_card,
            'notes': 'Đăng ký thẻ Visa Gold',
          },
          {
            'title': 'Xác minh',
            'description': 'Xác minh thông tin khách hàng',
            'status': 'completed',
            'date': '10/06/2024 15:00',
            'staff': 'Trần Thị B',
            'icon': TablerIcons.shield_check,
            'notes': 'Xác minh thông tin thành công',
          },
          {
            'title': 'Sản xuất thẻ',
            'description': 'In và làm thẻ',
            'status': transaction['status'] == 'Thành công' ? 'completed' : 'current',
            'date': transaction['status'] == 'Thành công' ? '11/06/2024 10:00' : null,
            'staff': transaction['status'] == 'Thành công' ? 'Hệ thống' : null,
            'icon': TablerIcons.printer,
            'notes': transaction['status'] == 'Thành công' ? 'Thẻ đã được sản xuất' : 'Đang sản xuất thẻ',
          },
          {
            'title': 'Giao thẻ',
            'description': 'Giao thẻ cho khách hàng',
            'status': transaction['status'] == 'Thành công' ? 'completed' : 'pending',
            'date': transaction['status'] == 'Thành công' ? '12/06/2024 14:00' : null,
            'staff': transaction['status'] == 'Thành công' ? 'Lê Văn C' : null,
            'icon': TablerIcons.truck_delivery,
            'notes': transaction['status'] == 'Thành công' ? 'Thẻ đã được giao' : 'Chờ giao thẻ',
          },
        ];
      default:
        return [
          {
            'title': 'Tạo giao dịch',
            'description': 'Giao dịch được khởi tạo',
            'status': 'completed',
            'date': transaction['date'] + ' 09:00',
            'staff': 'Nguyễn Văn A',
            'icon': TablerIcons.plus,
            'notes': 'Giao dịch được tạo thành công',
          },
          {
            'title': 'Xử lý',
            'description': 'Đang xử lý giao dịch',
            'status': transaction['status'] == 'Thành công' ? 'completed' : 'current',
            'date': transaction['status'] == 'Thành công' ? transaction['date'] + ' 15:00' : null,
            'staff': transaction['status'] == 'Thành công' ? 'Hệ thống' : null,
            'icon': TablerIcons.refresh,
            'notes': transaction['status'] == 'Thành công' ? 'Xử lý thành công' : 'Đang xử lý',
          },
        ];
    }
  }

  Color _getStepColor(String status) {
    switch (status) {
      case 'completed':
        return AppColors.success;
      case 'current':
        return AppColors.kienlongOrange;
      case 'pending':
        return AppColors.neutral400;
      default:
        return AppColors.neutral400;
    }
  }

  @override
  Widget build(BuildContext context) {
    final steps = _getProgressSteps();
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        children: [
          // Progress Overview Card
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.borderLight,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      TablerIcons.chart_line,
                      color: AppColors.kienlongOrange,
                      size: AppDimensions.iconM,
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Tổng quan tiến trình',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.kienlongOrange,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: AppDimensions.spacingL),
                
                // Progress Bar
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Tiến độ hoàn thành',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                              Text(
                                '${_calculateProgress(steps)}%',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.success,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: AppDimensions.spacingS),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: _calculateProgress(steps) / 100,
                              backgroundColor: AppColors.neutral200,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
                              minHeight: 8,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: AppDimensions.spacingL),
                
                // Estimated completion
                Container(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  decoration: BoxDecoration(
                    color: AppColors.info.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        TablerIcons.clock,
                        color: AppColors.info,
                        size: AppDimensions.iconS,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Expanded(
                        child: Text(
                          transaction['status'] == 'Thành công' 
                            ? 'Giao dịch đã hoàn thành' 
                            : 'Dự kiến hoàn thành: ${_getEstimatedCompletion()}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.info,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: AppDimensions.spacingL),
          
          // Timeline Steps
          ...steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            final isLast = index == steps.length - 1;
            
            return Container(
              margin: EdgeInsets.only(bottom: isLast ? 0 : AppDimensions.spacingM),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Timeline indicator
                  Column(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: _getStepColor(step['status']).withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: _getStepColor(step['status']),
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          step['icon'],
                          color: _getStepColor(step['status']),
                          size: AppDimensions.iconM,
                        ),
                      ),
                      if (!isLast)
                        Container(
                          width: 2,
                          height: 80,
                          color: AppColors.borderLight,
                        ),
                    ],
                  ),
                  
                  SizedBox(width: AppDimensions.spacingM),
                  
                  // Content
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.all(AppDimensions.paddingM),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        border: Border.all(
                          color: AppColors.borderLight,
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.shadowLight,
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  step['title'],
                                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: _getStepColor(step['status']),
                                  ),
                                ),
                              ),
                              if (step['date'] != null)
                                Text(
                                  step['date'],
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                            ],
                          ),
                          
                          SizedBox(height: AppDimensions.spacingS),
                          
                          // Description
                          Text(
                            step['description'],
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          
                          SizedBox(height: AppDimensions.spacingS),
                          
                          // Notes
                          Container(
                            padding: EdgeInsets.all(AppDimensions.paddingS),
                            decoration: BoxDecoration(
                              color: AppColors.neutral100,
                              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                            ),
                            child: Text(
                              step['notes'],
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppColors.textSecondary,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          ),
                          
                          if (step['staff'] != null) ...[
                            SizedBox(height: AppDimensions.spacingS),
                            Row(
                              children: [
                                Icon(
                                  TablerIcons.user,
                                  size: AppDimensions.iconS,
                                  color: AppColors.textTertiary,
                                ),
                                SizedBox(width: AppDimensions.spacingXS),
                                Text(
                                  'Thực hiện bởi: ${step['staff']}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.textTertiary,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
                           );
             }),
        ],
      ),
    );
  }

  int _calculateProgress(List<Map<String, dynamic>> steps) {
    final completedSteps = steps.where((step) => step['status'] == 'completed').length;
    return ((completedSteps / steps.length) * 100).round();
  }

  String _getEstimatedCompletion() {
    // Mock estimation based on product type
    switch (transaction['product']) {
      case 'Vay tín chấp':
      case 'Vay thế chấp':
        return '15/06/2024';
      case 'Thẻ tín dụng':
        return '20/06/2024';
      default:
        return '14/06/2024';
    }
  }
} 