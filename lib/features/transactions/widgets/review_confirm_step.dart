import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/index.dart';
import '../models/form_data/base_form_data.dart';
import '../models/form_data/installment_loan_form_data.dart';
import '../models/form_data/personal_loan_form_data.dart';
import '../../products/models/product_model.dart';
import '../../customers/models/customer_model.dart';
import '../../../shared/utils/product_icon_mapper.dart';

class ReviewConfirmStep extends StatefulWidget {
  final ProductModel? selectedProduct;
  final CustomerModel? selectedCustomer;
  final BaseFormData? formData; // Changed from Map to typed form data
  final List<DocumentModel> documents;
  final VoidCallback? onConfirm;
  final VoidCallback? onEditCustomer;
  final VoidCallback? onEditProduct;
  final VoidCallback? onEditDocuments;

  const ReviewConfirmStep({
    super.key,
    this.selectedProduct,
    this.selectedCustomer,
    this.formData, // Changed from required to optional
    required this.documents,
    this.onConfirm,
    this.onEditCustomer,
    this.onEditProduct,
    this.onEditDocuments,
  });

  @override
  State<ReviewConfirmStep> createState() => _ReviewConfirmStepState();
}

class _ReviewConfirmStepState extends State<ReviewConfirmStep>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  String _getTransactionId() {
    final productCode = widget.selectedProduct?.code;
    if (productCode == null) return DateTime.now().millisecondsSinceEpoch.toString().substring(8);
    
    final code = productCode.length >= 2 ? productCode.substring(0, 2).toUpperCase() : productCode.toUpperCase();
    return '$code${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
  }

  Color _getProductColor() {
    if (widget.selectedProduct == null) return AppColors.kienlongOrange;
    
    return widget.selectedProduct!.hasCustomColor
        ? widget.selectedProduct!.displayColor
        : AppColors.kienlongOrange;
  }

  IconData _getProductIcon() {
    if (widget.selectedProduct == null) return TablerIcons.file_text;
    
    return ProductIconMapper.getIcon(
      widget.selectedProduct!.displayConfig?.icon ?? widget.selectedProduct!.group,
    );
  }

  String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    final number = int.tryParse(cleanValue) ?? 0;
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (match) => '${match[1]}.',
    );
  }

  /// Get appropriate color for each section header
  Color _getSectionColor(String sectionLabel) {
    final cleanLabel = sectionLabel.replaceAll('━', '').trim().toLowerCase();
    
    if (cleanLabel.contains('đề nghị vay vốn')) {
      return AppColors.success;
    } else if (cleanLabel.contains('người vay chính')) {
      return AppColors.kienlongOrange;
    } else if (cleanLabel.contains('người đồng vay')) {
      return AppColors.kienlongSkyBlue; 
    } else if (cleanLabel.contains('tình hình tài chính')) {
      return AppColors.info; 
    } else if (cleanLabel.contains('tài sản bảo đảm')) {
      return AppColors.warning;
    }
    
    return AppColors.kienlongOrange; // Default color
  }

  /// Get appropriate icon for each section header
  IconData _getSectionIcon(String sectionLabel) {
    final cleanLabel = sectionLabel.replaceAll('━', '').trim().toLowerCase();
    
    if (cleanLabel.contains('đề nghị vay vốn')) {
      return TablerIcons.file_text; // Icon file cho đề nghị vay vốn
    } else if (cleanLabel.contains('người vay chính')) {
      return TablerIcons.user; // Icon user cho người vay chính
    } else if (cleanLabel.contains('người đồng vay')) {
      return TablerIcons.users; // Icon users cho người đồng vay
    } else if (cleanLabel.contains('tình hình tài chính')) {
      return TablerIcons.coins; // Icon coins cho tài chính
    } else if (cleanLabel.contains('tài sản bảo đảm')) {
      return TablerIcons.shield; // Icon shield cho tài sản bảo đảm
    }
    
    return TablerIcons.info_circle; // Default icon
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Bắt buộc phải gọi!
    
    if (widget.selectedProduct == null || widget.selectedCustomer == null || widget.formData == null) {
      return _buildIncompleteData(context);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '✅ Xác nhận tạo giao dịch',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Xem lại thông tin trước khi tạo giao dịch',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Transaction Summary Card
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getProductColor(),
                  _getProductColor().withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              boxShadow: [
                BoxShadow(
                  color: _getProductColor().withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getProductIcon(),
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    SizedBox(width: AppDimensions.spacingM),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getTransactionId(),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            widget.selectedProduct?.displayName ?? '',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (_getMainAmountFromFormData() != null) ...[
                  SizedBox(height: AppDimensions.spacingL),
                  Text(
                    '${_formatCurrency(_getMainAmountFromFormData()!)} VND',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Customer Information
          _buildInfoSection(
            context,
            'Thông tin khách hàng',
            TablerIcons.user,
            AppColors.kienlongSkyBlue,
            [
              _InfoItem('Họ tên', widget.selectedCustomer?.fullName ?? ''),
              _InfoItem('Số điện thoại', widget.selectedCustomer?.phoneNumber ?? ''),
              _InfoItem('Email', widget.selectedCustomer?.email ?? ''),
              _InfoItem('Loại khách hàng', 'Cá nhân'), // Default for now
              // Note: tag field doesn't exist in CustomerModel
            ],
            onEdit: widget.onEditCustomer,
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Product Details
          if (widget.formData != null)
            _buildInfoSection(
              context,
              'Chi tiết sản phẩm',
              _getProductIcon(),
              _getProductColor(),
              _buildProductDetailsFromFormData(),
              onEdit: widget.onEditProduct,
            ),

          SizedBox(height: AppDimensions.spacingM),

          // Documents Status
          _buildDocumentsSection(context, widget.documents, onEdit: widget.onEditDocuments),

          SizedBox(height: AppDimensions.spacingM),

          SizedBox(height: AppDimensions.spacingL),
  ],
            ),
          );
  }

  Widget _buildIncompleteData(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.alert_circle,
              size: 64,
              color: AppColors.warning,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Thông tin chưa đầy đủ',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.warning,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Vui lòng hoàn thành các bước trước để xem thông tin tổng hợp',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    List<_InfoItem> items, {
    VoidCallback? onEdit,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (onEdit != null)
                  GestureDetector(
                    onTap: onEdit,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(
                          color: color.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            TablerIcons.edit,
                            color: color,
                            size: 16,
                          ),
                          SizedBox(width: AppDimensions.spacingXS),
                          Text(
                            'Chỉnh sửa',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: color,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),

                     // Items
           for (final item in items)
             _buildInfoRow(context, item.label, item.value),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    // Check if this is a section header
    if (label.startsWith('━━━') && label.endsWith('━━━')) {
      // Get appropriate color for each section
      Color sectionColor = _getSectionColor(label);
      
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingM,
        ),
        margin: EdgeInsets.only(top: AppDimensions.spacingS),
        decoration: BoxDecoration(
          color: sectionColor.withValues(alpha: 0.05),
          border: Border(
            left: BorderSide(
              color: sectionColor,
              width: 4,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getSectionIcon(label),
              color: sectionColor,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: Text(
                label.replaceAll('━', '').trim(),
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: sectionColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    // Regular info row
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderLight,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsSection(BuildContext buildContext, List<DocumentModel> documents, {VoidCallback? onEdit}) {
    
    // Get required and optional documents based on product type
    final requiredDocs = _getRequiredDocumentsForProduct();
    final optionalDocs = _getOptionalDocumentsForProduct();
    
    final totalRequiredCount = requiredDocs.length;
    final totalOptionalCount = optionalDocs.length;
    
    // Count uploaded documents with improved matching
    final uploadedRequiredCount = requiredDocs.where((docName) {
      return documents.any((doc) => 
          doc.hasFiles && (
            doc.name == docName || 
            doc.name.contains(docName) || 
            docName.contains(doc.name)
          )
      );
    }).length;
    
    final uploadedOptionalCount = optionalDocs.where((docName) {
      return documents.any((doc) => 
          doc.hasFiles && (
            doc.name == docName || 
            doc.name.contains(docName) || 
            docName.contains(doc.name)
          )
      );
    }).length;
    
    final totalUploadedDocuments = documents.where((doc) => doc.hasFiles).length;
    debugPrint('Document counts - Required: $uploadedRequiredCount/$totalRequiredCount, Optional: $uploadedOptionalCount/$totalOptionalCount, Total uploaded: $totalUploadedDocuments');
    
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(buildContext).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(TablerIcons.file_text, color: AppColors.info, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    'Tài liệu',
                    style: Theme.of(buildContext).textTheme.titleMedium?.copyWith(
                      color: AppColors.info,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (onEdit != null) ...[
                  SizedBox(width: AppDimensions.spacingM),
                  GestureDetector(
                    onTap: onEdit,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.info.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(
                          color: AppColors.info.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            TablerIcons.edit,
                            color: AppColors.info,
                            size: 16,
                          ),
                          SizedBox(width: AppDimensions.spacingXS),
                          Text(
                            'Chỉnh sửa',
                            style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                              color: AppColors.info,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Document Status
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              
                // All Documents Section (showing both uploaded and pending)
                if (documents.isNotEmpty) ...[
                 
                  Text(
                    'Tình trạng tài liệu',
                    style: Theme.of(buildContext).textTheme.titleSmall?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  ...documents.map((doc) {
                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingXS),
                      child: Row(
                        children: [
                          Icon(
                            doc.hasFiles ? TablerIcons.file_check : TablerIcons.file_x,
                            color: doc.hasFiles ? AppColors.success : AppColors.warning,
                            size: 16,
                          ),
                          SizedBox(width: AppDimensions.spacingS),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  doc.name,
                                  style: Theme.of(buildContext).textTheme.bodyMedium?.copyWith(
                                    color: doc.hasFiles ? AppColors.success : AppColors.textPrimary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                if (doc.hasFiles && doc.filesCount > 0) ...[
                                  SizedBox(height: 2),
                                  Text(
                                    '${doc.filesCount} file${doc.filesCount > 1 ? 's' : ''} • ${doc.totalSizeFormatted}',
                                    style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                                if (!doc.hasFiles) ...[
                                  SizedBox(height: 2),
                                  Text(
                                    'Chưa có file nào được tải lên',
                                    style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppDimensions.paddingS,
                              vertical: AppDimensions.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: doc.hasFiles 
                                  ? AppColors.success.withValues(alpha: 0.1)
                                  : AppColors.warning.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                            ),
                            child: Text(
                              doc.hasFiles ? 'Đã tải' : 'Chưa tải',
                              style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                                color: doc.hasFiles ? AppColors.success : AppColors.warning,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
                
                // TODO: Sumary số tài liệu
                // Summary
                // if (totalDocumentsCount > 0) ...[
                //   SizedBox(height: AppDimensions.spacingM),
                //   Container(
                //     padding: EdgeInsets.all(AppDimensions.paddingM),
                //     decoration: BoxDecoration(
                //       color: uploadedRequiredCount == totalRequiredCount 
                //           ? AppColors.success.withValues(alpha: 0.1)
                //           : AppColors.warning.withValues(alpha: 0.1),
                //       borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                //       border: Border.all(
                //         color: uploadedRequiredCount == totalRequiredCount 
                //             ? AppColors.success.withValues(alpha: 0.3)
                //             : AppColors.warning.withValues(alpha: 0.3),
                //       ),
                //     ),
                //     child: Row(
                //       children: [
                //         Icon(
                //           uploadedRequiredCount == totalRequiredCount 
                //               ? TablerIcons.circle_check
                //               : TablerIcons.alert_circle,
                //           color: uploadedRequiredCount == totalRequiredCount 
                //               ? AppColors.success
                //               : AppColors.warning,
                //           size: AppDimensions.iconS,
                //         ),
                //         SizedBox(width: AppDimensions.spacingS),
                //         Expanded(
                //           child: Text(
                //             uploadedRequiredCount == totalRequiredCount
                //                 ? 'Tài liệu bắt buộc đã đầy đủ. Có thể tạo giao dịch.'
                //                 : 'Còn thiếu ${totalRequiredCount - uploadedRequiredCount} tài liệu bắt buộc.',
                //             style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                //               color: uploadedRequiredCount == totalRequiredCount 
                //                   ? AppColors.success
                //                   : AppColors.warning,
                //               fontWeight: FontWeight.w500,
                //             ),
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ],
              ],
            ),
          ),
        ],
      ),
    );
  }


  /// Build product details from typed form data models
  List<_InfoItem> _buildProductDetailsFromFormData() {
    if (widget.formData == null) return [];
    
    if (widget.formData is InstallmentLoanFormData) {
      return _buildInstallmentLoanDetails(widget.formData as InstallmentLoanFormData);
    } 
    
    return [];
  }

  /// Get main amount from form data models
  String? _getMainAmountFromFormData() {
    if (widget.formData == null) return null;
    
    if (widget.formData is InstallmentLoanFormData) {
      final data = widget.formData as InstallmentLoanFormData;
      return data.loanAmount?.toString();
    }
    
    return null;
  }

  /// Build InstallmentLoanFormData details
  List<_InfoItem> _buildInstallmentLoanDetails(InstallmentLoanFormData data) {
    final items = <_InfoItem>[];
    
    // ====== 1. THÔNG TIN ĐỀ NGHỊ VAY VỐN ======
    items.add(_InfoItem('━━━ ĐỀ NGHỊ VAY VỐN ━━━', ''));
    
    // Loan type information
    final loanTypeDisplayName = data.loanType?.displayName;
    if (loanTypeDisplayName?.isNotEmpty == true) {
      items.add(_InfoItem('Hình thức vay', loanTypeDisplayName!));
    }
    
    // Basic loan details
    if (data.loanAmount != null && data.loanAmount! > 0) {
      items.add(_InfoItem('Số tiền vay', '${_formatCurrency(data.loanAmount.toString())} VND'));
    }
    
    if (data.ownCapital != null && data.ownCapital! > 0) {
      items.add(_InfoItem('Vốn tự có', '${_formatCurrency(data.ownCapital.toString())} VND'));
    }
    
    if (data.totalCapitalNeed != null && data.totalCapitalNeed! > 0) {
      items.add(_InfoItem('Tổng nhu cầu vốn', '${_formatCurrency(data.totalCapitalNeed.toString())} VND'));
    }
    
    if (data.loanTermModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Thời hạn vay', '${data.loanTermModel!.label!} ngày'));
    }
    
    if (data.branchCode?.isNotEmpty == true) {
      items.add(_InfoItem('CN/PGD', data.branchCode!));
    }
    
    if (data.loanMethodModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Phương thức vay', data.loanMethodModel!.label!));
    }
    
    if (data.loanPurposeModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Mục đích vay', data.loanPurposeModel!.label!));
    }
    
    if (data.repaymentMethodModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Phương thức trả nợ', data.repaymentMethodModel!.label!));
    }
    
    if (data.disbursementMethodModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Phương thức giải ngân', data.disbursementMethodModel!.label!));
    }
    
    if (data.disbursementAccount?.isNotEmpty == true) {
      items.add(_InfoItem('Tài khoản nhận tiền', data.disbursementAccount!));
    }
    
    // ====== 2. THÔNG TIN NGƯỜI VAY CHÍNH ======
    items.add(_InfoItem('━━━ NGƯỜI VAY CHÍNH ━━━', ''));
    
    if (data.borrowerName?.isNotEmpty == true) {
      items.add(_InfoItem('Họ và tên', data.borrowerName!));
    }
    
    if (data.borrowerIdTypeModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Loại giấy tờ', data.borrowerIdTypeModel!.label!));
    }
    
    if (data.borrowerIdNumber?.isNotEmpty == true) {
      items.add(_InfoItem('Số GTTT', data.borrowerIdNumber!));
    }
    
    if (data.borrowerIdIssueDate?.isNotEmpty == true) {
      items.add(_InfoItem('Ngày cấp', data.borrowerIdIssueDate!));
    }
    
    if (data.borrowerIdExpiryDate?.isNotEmpty == true) {
      items.add(_InfoItem('Ngày hết hạn', data.borrowerIdExpiryDate!));
    }
    
    if (data.borrowerIdIssuePlace?.isNotEmpty == true) {
      items.add(_InfoItem('Nơi cấp', data.borrowerIdIssuePlace!));
    }
    
    if (data.borrowerBirthDate?.isNotEmpty == true) {
      items.add(_InfoItem('Ngày sinh', data.borrowerBirthDate!));
    }
    
    if (data.borrowerGenderModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Giới tính', data.borrowerGenderModel!.label!));
    }
    
    if (data.borrowerMaritalStatusModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Tình trạng hôn nhân', data.borrowerMaritalStatusModel!.label!));
    }
    
    if (data.borrowerPhone?.isNotEmpty == true) {
      items.add(_InfoItem('Số điện thoại', data.borrowerPhone!));
    }
    
    // Address information
    if (data.borrowerPermanentAddress?.isNotEmpty == true) {
      final province = data.borrowerPermanentProvinceModel?.name ?? '';
      final district = data.borrowerPermanentWardModel?.name ?? '';
      final address = data.borrowerPermanentAddress!;
      
      String fullAddress = address;
      if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
      if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
      
      items.add(_InfoItem('Hộ khẩu thường trú', fullAddress));
    }
    
    // Current address if different from permanent
    if (data.borrowerCurrentSamePermanent != true && data.borrowerCurrentAddress?.isNotEmpty == true) {
      final province = data.borrowerCurrentProvinceModel?.name ?? '';
      final district = data.borrowerCurrentWardModel?.name ?? '';
      final address = data.borrowerCurrentAddress!;
        
        String fullAddress = address;
        if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
        if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
        
        items.add(_InfoItem('Địa chỉ hiện tại', fullAddress));
    }
    
    // ====== 3. THÔNG TIN NGƯỜI ĐỒNG VAY ======
    if (data.hasCoBorrower == true) {
      items.add(_InfoItem('━━━ NGƯỜI ĐỒNG VAY ━━━', ''));
      
      if (data.coBorrowerName?.isNotEmpty == true) {
        items.add(_InfoItem('Họ và tên', data.coBorrowerName!));
      }
      
      if (data.coBorrowerIdTypeModel?.label?.isNotEmpty == true) {
        items.add(_InfoItem('Loại giấy tờ', data.coBorrowerIdTypeModel!.label!));
      }
      
      if (data.coBorrowerIdNumber?.isNotEmpty == true) {
        items.add(_InfoItem('Số GTTT', data.coBorrowerIdNumber!));
      }
      
      if (data.coBorrowerIdIssueDate?.isNotEmpty == true) {
        items.add(_InfoItem('Ngày cấp', data.coBorrowerIdIssueDate!));
      }
      
      if (data.coBorrowerIdExpiryDate?.isNotEmpty == true) {
        items.add(_InfoItem('Ngày hết hạn', data.coBorrowerIdExpiryDate!));
      }
      
      if (data.coBorrowerIdIssuePlace?.isNotEmpty == true) {
        items.add(_InfoItem('Nơi cấp', data.coBorrowerIdIssuePlace!));
      }
      
      if (data.coBorrowerBirthDate?.isNotEmpty == true) {
        items.add(_InfoItem('Ngày sinh', data.coBorrowerBirthDate!));
      }
      
      if (data.coBorrowerGenderModel?.label?.isNotEmpty == true) {
        items.add(_InfoItem('Giới tính', data.coBorrowerGenderModel!.label!));
      }
      
      if (data.coBorrowerMaritalStatusModel?.label?.isNotEmpty == true) {
        items.add(_InfoItem('Tình trạng hôn nhân', data.coBorrowerMaritalStatusModel!.label!));
      }
      
      if (data.coBorrowerPhone?.isNotEmpty == true) {
        items.add(_InfoItem('Số điện thoại', data.coBorrowerPhone!));
      }
      
      // Co-borrower permanent address
      if (data.coBorrowerPermanentAddress?.isNotEmpty == true) {
        final province = data.coBorrowerPermanentProvinceModel?.name ?? '';
        final district = data.coBorrowerPermanentWardModel?.name ?? '';
        final address = data.coBorrowerPermanentAddress!;
        
        String fullAddress = address;
        if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
        if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
        
        items.add(_InfoItem('Hộ khẩu thường trú', fullAddress));
      }
      
      // Co-borrower current address if different
      if (data.coBorrowerCurrentSamePermanent != true && data.coBorrowerCurrentAddress?.isNotEmpty == true) {
        final province = data.coBorrowerCurrentProvinceModel?.name ?? '';
        final district = data.coBorrowerCurrentWardModel?.name ?? '';
        final address = data.coBorrowerCurrentAddress!;
          
          String fullAddress = address;
          if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
          if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
          
          items.add(_InfoItem('Địa chỉ hiện tại', fullAddress));
      }
    }
    
    // ====== 4. THÔNG TIN TÀI CHÍNH ======
    items.add(_InfoItem('━━━ TÌNH HÌNH TÀI CHÍNH ━━━', ''));
    
    if (data.incomeSourceModel?.label?.isNotEmpty == true) {
      items.add(_InfoItem('Nguồn thu', data.incomeSourceModel!.label!));
    }
    
    if (data.dailyIncome != null && data.dailyIncome! > 0) {
      items.add(_InfoItem('Thu nhập bình quân/ngày', '${_formatCurrency(data.dailyIncome.toString())} VND'));
    }
    
    // Business information
    if (data.dailyRevenue != null && data.dailyRevenue! > 0) {
      items.add(_InfoItem('Doanh số bình quân/ngày', '${_formatCurrency(data.dailyRevenue.toString())} VND'));
    }
    
    // Business location if applicable
    if (data.businessLocationAddress?.isNotEmpty == true) {
      final province = data.businessLocationProvinceModel?.name ?? '';
      final district = data.businessLocationWardModel?.name ?? '';
      final address = data.businessLocationAddress!;
      
      String fullAddress = address;
      if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
      if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
      
      items.add(_InfoItem('Địa điểm SXKD', fullAddress));
    }
    
    // ====== 5. THÔNG TIN TÀI SẢN BẢO ĐẢM ======
    if (data.loanType?.displayName == 'Có TSBĐ') {
      items.add(_InfoItem('━━━ TÀI SẢN BẢO ĐẢM ━━━', ''));
      
      if (data.collateralTypeModel?.name?.isNotEmpty == true) {
        items.add(_InfoItem('Loại tài sản', data.collateralTypeModel!.name!));
      }
      
      if (data.collateralValue != null && data.collateralValue! > 0) {
        items.add(_InfoItem('Giá trị tài sản', '${_formatCurrency(data.collateralValue.toString())} VND'));
      }
      
      if (data.collateralOwner?.isNotEmpty == true) {
        items.add(_InfoItem('Chủ sở hữu', data.collateralOwner!));
      }
      
      if (data.collateralOwnerBirthYear?.isNotEmpty == true) {
        items.add(_InfoItem('Năm sinh chủ sở hữu', data.collateralOwnerBirthYear!));
      }
      
      if (data.vehicleName?.isNotEmpty == true) {
        items.add(_InfoItem('Tên tài sản', data.vehicleName!));
      }
      
      if (data.vehiclePlateNumber?.isNotEmpty == true) {
        items.add(_InfoItem('Biển kiểm soát', data.vehiclePlateNumber!));
      }
      
      if (data.vehicleFrameNumber?.isNotEmpty == true) {
        items.add(_InfoItem('Số khung', data.vehicleFrameNumber!));
      }
      
      if (data.vehicleEngineNumber?.isNotEmpty == true) {
        items.add(_InfoItem('Số máy', data.vehicleEngineNumber!));
      }
    }
    
    return items;
  }


  /// Get required documents based on product type and form data
  List<String> _getRequiredDocumentsForProduct() {
    final productCode = widget.selectedProduct?.code;

    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
        final requiredDocs = <String>[
          'CMND/CCCD người vay chính',
          'Giấy chứng nhận đăng ký xe',
        ];

        // Add co-borrower documents if applicable - use form data
        bool hasCoBorrower = false;
        if (widget.formData is InstallmentLoanFormData) {
          hasCoBorrower = (widget.formData as InstallmentLoanFormData).hasCoBorrower == true;
        } else if (widget.formData is PersonalLoanFormData) {
          hasCoBorrower = (widget.formData as PersonalLoanFormData).hasCoBorrower == true;
        }
        
        if (hasCoBorrower) {
          requiredDocs.add('CMND/CCCD người đồng vay');
        }

        // Add marital documents based on marital status - use form data
        String maritalStatus = '';
        if (widget.formData is InstallmentLoanFormData) {
          maritalStatus = (widget.formData as InstallmentLoanFormData).borrowerMaritalStatusModel?.label ?? '';
        } else if (widget.formData is PersonalLoanFormData) {
          maritalStatus = (widget.formData as PersonalLoanFormData).borrowerMaritalStatus ?? '';
        }
        
        if (maritalStatus.contains('Có gia đình') || maritalStatus.contains('Kết hôn')) {
          requiredDocs.add('Giấy kết hôn/Giấy xác nhận tình trạng hôn nhân');
        }

        return requiredDocs;

      case 'PERSONAL_LOAN':
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
          'Sổ hộ khẩu',
        ];

      case 'MORTGAGE_LOAN':
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
          'Sổ đỏ/Giấy chứng nhận quyền sử dụng đất',
          'Giấy thẩm định giá',
        ];

      case 'AUTO_LOAN':
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
          'Đăng ký xe',
          'Bảo hiểm xe',
        ];

      case 'BUSINESS_LOAN':
        return [
          'CMND/CCCD',
          'Giấy phép kinh doanh',
          'Báo cáo tài chính',
          'Chứng từ thu nhập',
        ];

      default:
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
        ];
    }
  }

  /// Get optional documents based on product type
  List<String> _getOptionalDocumentsForProduct() {
    final productCode = widget.selectedProduct?.code;

    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
        final optionalDocs = <String>[
          'Chứng minh thu nhập',
          'Sổ hộ khẩu',
        ];

        // Add business certificate if income source is business - use form data
        String incomeSource = '';
        if (widget.formData is InstallmentLoanFormData) {
          incomeSource = (widget.formData as InstallmentLoanFormData).incomeSourceModel?.label ?? '';
        } else if (widget.formData is PersonalLoanFormData) {
          incomeSource = (widget.formData as PersonalLoanFormData).incomeSource ?? '';
        }
        
        if (incomeSource.toLowerCase().contains('kinh doanh') ||
            incomeSource.toLowerCase().contains('business')) {
          optionalDocs.add('Giấy phép kinh doanh');
        }

        return optionalDocs;

      case 'PERSONAL_LOAN':
        return [
          'Giấy kết hôn',
          'Chứng nhận lương',
        ];

      case 'MORTGAGE_LOAN':
        return [
          'Giấy kết hôn',
          'Hợp đồng mua bán',
          'Báo cáo thẩm định',
        ];

      case 'AUTO_LOAN':
        return [
          'Giấy kết hôn',
          'Hợp đồng mua xe',
          'Hóa đơn mua xe',
        ];

      case 'BUSINESS_LOAN':
        return [
          'Giấy kết hôn',
          'Hợp đồng thuê mặt bằng',
          'Chứng nhận đăng ký thuế',
        ];

      default:
        return [
          'Giấy kết hôn',
          'Chứng nhận khác',
        ];
    }
  }

}

class _InfoItem {
  final String label;
  final String value;

  _InfoItem(this.label, this.value);
} 