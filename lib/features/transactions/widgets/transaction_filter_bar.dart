import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../../../shared/models/index.dart';
import '../../../shared/utils/index.dart';
import '../../../shared/utils/color_utils.dart';

class TransactionFilterBar extends StatelessWidget {
  final int selectedIndex;
  final ValueChanged<int> onChanged;
  final List<ConfigModel> availableStatuses;
  final Function(ConfigModel?)? onStatusSelected; // Callback để notify khi status được chọn

  const TransactionFilterBar({
    super.key,
    required this.selectedIndex,
    required this.onChanged,
    this.availableStatuses = const [],
    this.onStatusSelected,
  });

  // Dynamic filters based on available statuses
  List<String> get filters {
    final List<String> dynamicFilters = ['Tất cả'];
    
    // Add statuses from API if available
    if (availableStatuses.isNotEmpty) {
      dynamicFilters.addAll(
        availableStatuses
            .map((status) => status.label ?? status.code)
            .where((label) => 
                label != null && 
                label.isNotEmpty && 
                label.toLowerCase() != 'tất cả') // Filter out "Tất cả" from API
            .cast<String>()
            .toList(),
      );
    } else {
      // Fallback to default filters if no API data
      dynamicFilters.addAll([
        'Chờ xử lý',
        'Thành công',
        'Lỗi',
      ]);
    }
    
    return dynamicFilters;
  }

  // Check if current selection is valid
  bool get isSelectionValid {
    return selectedIndex >= 0 && selectedIndex < filters.length;
  }

  // Get safe selected index (fallback to 0 if invalid)
  int get safeSelectedIndex {
    return isSelectionValid ? selectedIndex : 0;
  }

  /// Get status color from ConfigModel value field using ColorUtils
  Color _getStatusColor(ConfigModel status) {
    // Use color from API value field if available
    if (status.value != null && status.value!.isNotEmpty) {
      return ColorUtils.hexToColor(status.value!);
    }

    return AppColors.kienlongDarkBlue;
  }

  /// Get status color at specific index for filter bar
  Color _getStatusColorAtIndex(int index, List<ConfigModel> availableStatuses) {
    if (index == 0) {
      // Index 0 = "Tất cả" - use primary color
      return AppColors.kienlongOrange;
    }

    // Get filtered statuses (without "Tất cả")
    final filteredStatuses = availableStatuses.where((status) {
      final label = status.label ?? status.code;
      return label != null && 
             label.isNotEmpty && 
             label.toLowerCase() != 'tất cả';
    }).toList();

    final statusIndex = index - 1; // -1 because "Tất cả" is at index 0
    if (statusIndex >= 0 && statusIndex < filteredStatuses.length) {
      return _getStatusColor(filteredStatuses[statusIndex]);
    }

    // Fallback color
    return AppColors.kienlongDarkBlue;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final isSelected = index == safeSelectedIndex;
          final statusColor = _getStatusColorAtIndex(index, availableStatuses);
          
          return ChoiceChip(
            label: Text(filters[index]),
            selected: isSelected,
            onSelected: (_) {
              onChanged(index);
              
              // Notify parent about status selection
              if (onStatusSelected != null && availableStatuses.isNotEmpty) {
                // Map index to ConfigModel
                ConfigModel? selectedStatus;
                if (index == 0) {
                  // Index 0 = "Tất cả" = null status
                  selectedStatus = null;
                } else {
                  // Index > 0 = actual status from API (adjusted for filtered list)
                  final filteredStatuses = availableStatuses.where((status) {
                    final label = status.label ?? status.code;
                    return label != null && 
                           label.isNotEmpty && 
                           label.toLowerCase() != 'tất cả';
                  }).toList();
                  
                  final statusIndex = index - 1; // -1 because "Tất cả" is at index 0
                  if (statusIndex >= 0 && statusIndex < filteredStatuses.length) {
                    selectedStatus = filteredStatuses[statusIndex];
                  }
                }
                onStatusSelected!(selectedStatus);
              }
            },
            selectedColor: statusColor,
            labelStyle: TextStyle(
              color: isSelected
                  ? Colors.white
                  : Theme.of(context).colorScheme.onSurface,
            ),
          );
        },
      ),
    );
  }
} 