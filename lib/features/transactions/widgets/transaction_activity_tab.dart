import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionActivityTab extends StatelessWidget {
  final Map<String, dynamic> transaction;

  const TransactionActivityTab({
    super.key,
    required this.transaction,
  });

  @override
  Widget build(BuildContext context) {
    // Mock activity data - in real app would come from API
    final activities = [
      {
        'type': 'status_update',
        'title': 'Cập nhật trạng thái',
        'description': '<PERSON><PERSON><PERSON> dịch đượ<PERSON> chuyể<PERSON> sang trạng thái "${transaction['status']}"',
        'date': '12/06/2024',
        'time': '15:30',
        'staff': '<PERSON><PERSON> thống',
        'icon': TablerIcons.refresh,
        'color': AppColors.info,
      },
      {
        'type': 'call',
        'title': '<PERSON>uộc gọi tư vấn',
        'description': '<PERSON><PERSON> vấn thông tin ${transaction['product']} cho khách hàng',
        'date': '11/06/2024',
        'time': '14:15',
        'duration': '12 phút',
        'staff': '<PERSON><PERSON><PERSON><PERSON>ăn <PERSON>',
        'icon': TablerIcons.phone,
        'color': AppColors.success,
      },
      {
        'type': 'document',
        'title': 'Upload tài liệu',
        'description': 'Khách hàng upload CMND/CCCD',
        'date': '10/06/2024',
        'time': '16:45',
        'staff': transaction['customerName'],
        'icon': TablerIcons.upload,
        'color': AppColors.kienlongSkyBlue,
      },
      {
        'type': 'message',
        'title': 'Gửi tin nhắn',
        'description': 'Gửi thông tin hướng dẫn nộp hồ sơ',
        'date': '10/06/2024',
        'time': '09:30',
        'staff': 'Trần Thị B',
        'icon': TablerIcons.message_circle,
        'color': AppColors.kienlongOrange,
      },
      {
        'type': 'create',
        'title': 'Tạo giao dịch',
        'description': 'Giao dịch được khởi tạo trong hệ thống',
        'date': transaction['date'],
        'time': '09:00',
        'staff': 'Nguyễn Văn A',
        'icon': TablerIcons.plus,
        'color': AppColors.kienlongOrange,
      },
    ];

    if (activities.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      children: [
        // Add Activity Button
        Container(
          width: double.infinity,
          margin: EdgeInsets.all(AppDimensions.paddingM),
          child: ElevatedButton.icon(
            onPressed: () {
              _showAddActivityModal(context);
            },
            icon: const Icon(TablerIcons.plus),
            label: const Text('Thêm hoạt động'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
          ),
        ),

        // Activity Timeline
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
            itemCount: activities.length,
            itemBuilder: (context, index) {
              final activity = activities[index];
              final isLast = index == activities.length - 1;
              
              return Container(
                margin: EdgeInsets.only(bottom: isLast ? AppDimensions.spacingXL : AppDimensions.spacingM),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Timeline indicator
                    Column(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: (activity['color'] as Color).withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: activity['color'] as Color,
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            activity['icon'] as IconData,
                            color: activity['color'] as Color,
                            size: AppDimensions.iconS,
                          ),
                        ),
                        if (!isLast)
                          Container(
                            width: 2,
                            height: 60,
                            color: AppColors.borderLight,
                          ),
                      ],
                    ),
                    
                    SizedBox(width: AppDimensions.spacingM),
                    
                    // Activity Content
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(AppDimensions.paddingM),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                          border: Border.all(
                            color: AppColors.borderLight,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.shadowLight,
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    activity['title'] as String,
                                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: activity['color'] as Color,
                                    ),
                                  ),
                                ),
                                Text(
                                  '${activity['date']} ${activity['time']}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                            
                            SizedBox(height: AppDimensions.spacingS),
                            
                            // Description
                            Text(
                              activity['description'] as String,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                            
                            SizedBox(height: AppDimensions.spacingS),
                            
                            // Footer info
                            Row(
                              children: [
                                if (activity['duration'] != null) ...[
                                  Icon(
                                    TablerIcons.clock,
                                    size: 12,
                                    color: AppColors.textTertiary,
                                  ),
                                  SizedBox(width: AppDimensions.spacingXS),
                                  Text(
                                    activity['duration'] as String,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textTertiary,
                                    ),
                                  ),
                                  SizedBox(width: AppDimensions.spacingM),
                                ],
                                Icon(
                                  TablerIcons.user,
                                  size: 12,
                                  color: AppColors.textTertiary,
                                ),
                                SizedBox(width: AppDimensions.spacingXS),
                                Expanded(
                                  child: Text(
                                    activity['staff'] as String,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textTertiary,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.neutral200,
                shape: BoxShape.circle,
              ),
              child: Icon(
                TablerIcons.activity,
                size: 40,
                color: AppColors.neutral500,
              ),
            ),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'Chưa có hoạt động nào',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Các hoạt động liên quan đến giao dịch sẽ hiển thị ở đây',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                _showAddActivityModal(context);
              },
              icon: const Icon(TablerIcons.plus),
              label: const Text('Thêm hoạt động đầu tiên'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddActivityModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          margin: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Thêm hoạt động',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingL),
                
                // Activity options
                ListTile(
                  leading: Icon(
                    TablerIcons.phone,
                    color: AppColors.success,
                  ),
                  title: const Text('Ghi nhận cuộc gọi'),
                  subtitle: const Text('Ghi lại cuộc gọi với khách hàng'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Add call activity
                  },
                ),
                ListTile(
                  leading: Icon(
                    TablerIcons.message_circle,
                    color: AppColors.kienlongOrange,
                  ),
                  title: const Text('Ghi nhận tin nhắn'),
                  subtitle: const Text('Ghi lại tin nhắn đã gửi'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Add message activity
                  },
                ),
                ListTile(
                  leading: Icon(
                    TablerIcons.note,
                    color: AppColors.info,
                  ),
                  title: const Text('Thêm ghi chú'),
                  subtitle: const Text('Ghi chú nội bộ về giao dịch'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Add note activity
                  },
                ),
                ListTile(
                  leading: Icon(
                    TablerIcons.users,
                    color: AppColors.kienlongSkyBlue,
                  ),
                  title: const Text('Ghi nhận cuộc hẹn'),
                  subtitle: const Text('Lịch hẹn gặp mặt khách hàng'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Add meeting activity
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
} 