import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionDocumentsTab extends StatelessWidget {
  final Map<String, dynamic> transaction;

  const TransactionDocumentsTab({
    super.key,
    required this.transaction,
  });

  List<Map<String, dynamic>> _getRequiredDocuments() {
    switch (transaction['product']) {
      case 'Vay tín chấp':
      case 'Vay thế chấp':
        return [
          {
            'name': 'CMND/CCCD',
            'description': 'Chứng minh nhân dân hoặc Căn cước công dân',
            'status': 'completed',
            'icon': TablerIcons.id,
            'required': true,
          },
          {
            'name': '<PERSON><PERSON> hộ khẩu',
            'description': 'Bản sao sổ hộ khẩu có công chứng',
            'status': 'completed',
            'icon': TablerIcons.home,
            'required': true,
          },
          {
            'name': 'Chứng minh thu nhập',
            'description': '<PERSON><PERSON><PERSON> lươ<PERSON>, hợp đồng lao động',
            'status': 'pending',
            'icon': TablerIcons.currency_dong,
            'required': true,
          },
          {
            'name': 'Tài sản thế chấp',
            'description': 'Giấy tờ tài sản thế chấp (nếu có)',
            'status': transaction['product'] == 'Vay thế chấp' ? 'pending' : 'not_required',
            'icon': TablerIcons.building,
            'required': transaction['product'] == 'Vay thế chấp',
          },
        ];
      case 'Thẻ tín dụng':
        return [
          {
            'name': 'CMND/CCCD',
            'description': 'Chứng minh nhân dân hoặc Căn cước công dân',
            'status': 'completed',
            'icon': TablerIcons.id,
            'required': true,
          },
          {
            'name': 'Chứng minh thu nhập',
            'description': 'Bảng lương 3 tháng gần nhất',
            'status': 'completed',
            'icon': TablerIcons.currency_dong,
            'required': true,
          },
          {
            'name': 'Hóa đơn điện/nước',
            'description': 'Xác nhận địa chỉ cư trú',
            'status': 'pending',
            'icon': TablerIcons.receipt,
            'required': false,
          },
        ];
      default:
        return [
          {
            'name': 'CMND/CCCD',
            'description': 'Chứng minh nhân dân hoặc Căn cước công dân',
            'status': 'completed',
            'icon': TablerIcons.id,
            'required': true,
          },
        ];
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return AppColors.success;
      case 'pending':
        return AppColors.warning;
      case 'not_required':
        return AppColors.neutral400;
      default:
        return AppColors.error;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'Đã nộp';
      case 'pending':
        return 'Chờ nộp';
      case 'not_required':
        return 'Không yêu cầu';
      default:
        return 'Thiếu';
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'completed':
        return TablerIcons.check;
      case 'pending':
        return TablerIcons.clock;
      case 'not_required':
        return TablerIcons.minus;
      default:
        return TablerIcons.x;
    }
  }

  @override
  Widget build(BuildContext context) {
    final documents = _getRequiredDocuments();
    final requiredDocs = documents.where((doc) => doc['required'] == true).toList();
    final optionalDocs = documents.where((doc) => doc['required'] == false).toList();
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        children: [
          // Upload New Document Button
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
            child: ElevatedButton.icon(
              onPressed: () {
                _showUploadModal(context);
              },
              icon: const Icon(TablerIcons.upload),
              label: const Text('Tải lên tài liệu'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
              ),
            ),
          ),

          // Required Documents Section
          if (requiredDocs.isNotEmpty) ...[
            _buildDocumentSection(
              context: context,
              title: 'Tài liệu bắt buộc',
              icon: TablerIcons.alert_circle,
              iconColor: AppColors.error,
              documents: requiredDocs,
            ),
            SizedBox(height: AppDimensions.spacingL),
          ],

          // Optional Documents Section
          if (optionalDocs.isNotEmpty) ...[
            _buildDocumentSection(
              context: context,
              title: 'Tài liệu tùy chọn',
              icon: TablerIcons.info_circle,
              iconColor: AppColors.info,
              documents: optionalDocs,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDocumentSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<Map<String, dynamic>> documents,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: iconColor,
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: iconColor,
                  ),
                ),
                const Spacer(),
                Text(
                  '${documents.where((doc) => doc['status'] == 'completed').length}/${documents.length}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: iconColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Documents List
                                ...documents.map((doc) {
             return Container(
               padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.borderLight,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Document Icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _getStatusColor(doc['status']).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    ),
                    child: Icon(
                      doc['icon'],
                      color: _getStatusColor(doc['status']),
                      size: AppDimensions.iconM,
                    ),
                  ),
                  
                  SizedBox(width: AppDimensions.spacingM),
                  
                  // Document Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          doc['name'],
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: AppDimensions.spacingXS),
                        Text(
                          doc['description'],
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  
                  SizedBox(width: AppDimensions.spacingM),
                  
                  // Status
                  Column(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: AppDimensions.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(doc['status']).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _getStatusColor(doc['status']),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getStatusIcon(doc['status']),
                              size: 12,
                              color: _getStatusColor(doc['status']),
                            ),
                            SizedBox(width: AppDimensions.spacingXS),
                            Text(
                              _getStatusText(doc['status']),
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: _getStatusColor(doc['status']),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      if (doc['status'] == 'completed') ...[
                        SizedBox(height: AppDimensions.spacingS),
                        InkWell(
                          onTap: () {
                            _viewDocument(context, doc);
                          },
                          child: Text(
                            'Xem',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.kienlongSkyBlue,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  void _showUploadModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          margin: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Tải lên tài liệu',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingL),
                
                // Upload options
                ListTile(
                  leading: Icon(
                    TablerIcons.camera,
                    color: AppColors.kienlongOrange,
                  ),
                  title: const Text('Chụp ảnh'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Open camera
                  },
                ),
                ListTile(
                  leading: Icon(
                    TablerIcons.photo,
                    color: AppColors.kienlongSkyBlue,
                  ),
                  title: const Text('Chọn từ thư viện'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Open gallery
                  },
                ),
                ListTile(
                  leading: Icon(
                    TablerIcons.file,
                    color: AppColors.info,
                  ),
                  title: const Text('Chọn file'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Open file picker
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _viewDocument(BuildContext context, Map<String, dynamic> doc) {
    // TODO: Implement document viewer
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Xem tài liệu: ${doc['name']}')),
    );
  }
} 