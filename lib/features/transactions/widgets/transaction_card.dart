import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/index.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/utils/color_utils.dart';
import '../models/proposal_list.dart';
import '../../products/models/product_model.dart';

class TransactionCard extends StatelessWidget {
  final String customerName;
  final String product;
  final ConfigModel? statusConfig;
  final String date;
  final String amount;
  final String? customerTag;
  final String? borrowerPhone;
  final String? branchName;
  final String? createdByName;
  final String? createdAt;
  final FlowStepInfo? currentFlowStep;
  final ProductDisplayConfig? productDisplayConfig;
  final VoidCallback? onTap;
  final VoidCallback? onCall;
  final VoidCallback? onShare;

  const TransactionCard({
    super.key,
    required this.customerName,
    required this.product,
    this.statusConfig,
    required this.date,
    required this.amount,
    this.customerTag,
    this.borrowerPhone,
    this.branchName,
    this.createdByName,
    this.createdAt,
    this.currentFlowStep,
    this.productDisplayConfig,
    this.onTap,
    this.onCall,
    this.onShare,
  });

  /// Get status color from ConfigModel value field using ColorUtils
  Color _statusColor() {
    if (statusConfig == null) return AppColors.textSecondary;
    
    // Use color from API value field if available
    if (statusConfig!.value != null && statusConfig!.value!.isNotEmpty) {
      return ColorUtils.hexToColor(statusConfig!.value!);
    }

    return AppColors.kienlongDarkBlue;
  }

  IconData _productIcon() {
    switch (product) {
      case 'Vay tín chấp':
        return TablerIcons.wallet;
      case 'Thẻ tín dụng':
        return TablerIcons.credit_card;
      case 'Gửi tiết kiệm':
        return TablerIcons.building_bank;
      case 'Vay thế chấp':
        return TablerIcons.home_2;
      default:
        return TablerIcons.receipt_2;
    }
  }

  Color _productColor() {
    // Use color from productDisplayConfig if available
    if (productDisplayConfig?.color != null && productDisplayConfig!.color!.isNotEmpty) {
      return ColorUtils.hexToColor(productDisplayConfig!.color!);
    }
    
    // Fallback to product name based colors
    switch (product) {
      case 'Vay tín chấp':
        return AppColors.kienlongOrange;
      case 'Thẻ tín dụng':
        return AppColors.kienlongSkyBlue;
      case 'Gửi tiết kiệm':
        return AppColors.success;
      case 'Vay thế chấp':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }

  String _formatCreatedDate() {
    if (createdAt == null) return '';
    try {
      final dateTime = DateTime.parse(createdAt!);
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    } catch (e) {
      return createdAt!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            children: [
              // Header row
              Row(
                children: [
                  // Customer avatar & info
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: _productColor().withValues(alpha: 0.15),
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusM,
                            ),
                          ),
                          child: Icon(
                            _productIcon(),
                            color: _productColor(),
                            size: AppDimensions.iconM,
                          ),
                        ),
                        const SizedBox(width: AppDimensions.spacingM),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Flexible(
                                    child: Text(
                                      customerName,
                                      style: AppTypography.textTheme.titleMedium
                                          ?.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  if (customerTag != null) ...[
                                    const SizedBox(
                                      width: AppDimensions.spacingXS,
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: AppDimensions.paddingXS,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.kienlongOrange
                                            .withValues(alpha: 0.15),
                                        borderRadius: BorderRadius.circular(
                                          AppDimensions.radiusXS,
                                        ),
                                      ),
                                      child: Text(
                                        customerTag!,
                                        style: AppTypography.textTheme.bodySmall
                                            ?.copyWith(
                                              color: AppColors.kienlongOrange,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 10,
                                            ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              const SizedBox(height: AppDimensions.spacingXS),
                              Text(
                                product,
                                style: AppTypography.textTheme.bodyMedium
                                    ?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withValues(alpha: 0.7),
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  //  Status
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingS,
                      vertical: AppDimensions.paddingXS,
                    ),
                    decoration: BoxDecoration(
                      color: _statusColor().withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                    child: Text(
                      statusConfig?.label ?? 'N/A',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: _statusColor(),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppDimensions.spacingM),

              // Additional info section
              if (borrowerPhone != null ||
                  branchName != null ||
                  createdByName != null ||
                  createdAt != null) ...[
                Container(
                  padding: const EdgeInsets.all(AppDimensions.paddingS),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  ),
                  child: Column(
                    children: [
                      // Phone number
                      if (borrowerPhone != null) ...[
                        _buildInfoRow(
                          context,
                          icon: TablerIcons.phone,
                          label: 'SĐT',
                          value: borrowerPhone!,
                        ),
                        const SizedBox(height: AppDimensions.spacingS),
                      ],

                      // Branch name
                      if (branchName != null) ...[
                        _buildInfoRow(
                          context,
                          icon: TablerIcons.building_bank,
                          label: 'Chi nhánh',
                          value: branchName!,
                        ),
                        const SizedBox(height: AppDimensions.spacingS),
                      ],

                      // Created by
                      if (createdByName != null) ...[
                        _buildInfoRow(
                          context,
                          icon: TablerIcons.user,
                          label: 'Người tạo',
                          value: createdByName!,
                        ),
                        const SizedBox(height: AppDimensions.spacingS),
                      ],

                      // Created date
                      if (createdAt != null) ...[
                        _buildInfoRow(
                          context,
                          icon: TablerIcons.calendar_plus,
                          label: 'Ngày tạo',
                          value: _formatCreatedDate(),
                        ),
                        const SizedBox(height: AppDimensions.spacingS),
                      ],

                      // Current flow step
                      if (currentFlowStep != null) ...[
                        _buildInfoRow(
                          context,
                          icon: TablerIcons.route,
                          label: 'Bước hiện tại',
                          value: currentFlowStep!.name ?? currentFlowStep!.code ?? 'N/A',
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(height: AppDimensions.spacingS),
              ],

              // Footer with date and actions
              // Action buttons row
              Row(
                children: [
                  if (onCall != null) ...[
                    _buildActionButton(
                      context,
                      icon: TablerIcons.phone,
                      label: 'Gọi',
                      onTap: onCall!,
                      color: AppColors.success,
                    ),
                    const SizedBox(width: AppDimensions.spacingS),
                  ],
                  _buildActionButton(
                    context,
                    icon: TablerIcons.eye,
                    label: 'Chi tiết',
                    onTap: onTap ?? () {},
                    color: AppColors.kienlongSkyBlue,
                  ),
                  Spacer(),
                  Text(
                    amount,
                    style: AppTypography.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: _productColor(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: AppDimensions.iconS,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
        ),
        const SizedBox(width: AppDimensions.spacingXS),
        Text(
          '$label:',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(width: AppDimensions.spacingXS),
        Expanded(
          child: Text(
            value,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingS,
          vertical: AppDimensions.paddingXS,
        ),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: AppDimensions.iconS, color: color),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              label,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
