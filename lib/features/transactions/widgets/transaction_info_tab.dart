import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionInfoTab extends StatelessWidget {
  final Map<String, dynamic> transaction;

  const TransactionInfoTab({
    super.key,
    required this.transaction,
  });

  String _generateTransactionId() {
    final productCode = transaction['product'].toString().substring(0, 2).toUpperCase();
    return 'TX$productCode${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
  }

  Widget _buildInfoCard({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<Widget> children,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: iconColor,
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: iconColor,
                  ),
                ),
              ],
            ),
          ),
          
          // Card Content
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String value,
    Color? iconColor,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: (iconColor ?? AppColors.neutral500).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: AppDimensions.iconS,
              color: iconColor ?? AppColors.neutral500,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingXS),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, String>> _getProductTerms() {
    switch (transaction['product']) {
      case 'Vay tín chấp':
        return [
          {'label': 'Lãi suất', 'value': '12%/năm'},
          {'label': 'Thời hạn vay', 'value': '24 tháng'},
          {'label': 'Hình thức trả', 'value': 'Trả góp hàng tháng'},
          {'label': 'Điều kiện', 'value': 'Thu nhập ổn định tối thiểu 8 triệu/tháng'},
        ];
      case 'Thẻ tín dụng':
        return [
          {'label': 'Hạn mức', 'value': '50.000.000 VND'},
          {'label': 'Loại thẻ', 'value': 'Visa Gold'},
          {'label': 'Phí thường niên', 'value': '500.000 VND'},
          {'label': 'Ưu đãi', 'value': 'Hoàn tiền 1% cho mua sắm'},
        ];
      case 'Gửi tiết kiệm':
        return [
          {'label': 'Lãi suất', 'value': '6.5%/năm'},
          {'label': 'Kỳ hạn', 'value': '12 tháng'},
          {'label': 'Tái tục', 'value': 'Tự động gia hạn'},
          {'label': 'Rút trước hạn', 'value': 'Áp dụng lãi suất không kỳ hạn'},
        ];
      default:
        return [
          {'label': 'Chi tiết', 'value': 'Thông tin sẽ được cập nhật'},
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final transactionId = _generateTransactionId();
    final terms = _getProductTerms();
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        children: [
          // Transaction Information Card
          _buildInfoCard(
            context: context,
            title: 'Thông tin giao dịch',
            icon: TablerIcons.file_text,
            iconColor: AppColors.kienlongOrange,
            children: [
              _buildInfoRow(
                context: context,
                icon: TablerIcons.hash,
                label: 'Mã giao dịch',
                value: transactionId,
                iconColor: AppColors.kienlongOrange,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.package,
                label: 'Loại sản phẩm',
                value: transaction['product'],
                iconColor: AppColors.kienlongSkyBlue,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.currency_dong,
                label: 'Số tiền',
                value: transaction['amount'],
                iconColor: AppColors.success,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.calendar,
                label: 'Ngày tạo',
                value: transaction['date'],
                iconColor: AppColors.info,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.clock,
                label: 'Trạng thái',
                value: transaction['status'],
                iconColor: _getStatusColor(transaction['status']),
              ),
            ],
          ),

          // Terms & Conditions Card
          _buildInfoCard(
            context: context,
            title: 'Điều khoản & Điều kiện',
            icon: TablerIcons.file_certificate,
            iconColor: AppColors.kienlongSkyBlue,
            children: terms.map((term) {
              return _buildInfoRow(
                context: context,
                icon: TablerIcons.point,
                label: term['label']!,
                value: term['value']!,
                iconColor: AppColors.neutral600,
              );
            }).toList(),
          ),

          // Additional Information Card
          _buildInfoCard(
            context: context,
            title: 'Thông tin bổ sung',
            icon: TablerIcons.info_circle,
            iconColor: AppColors.info,
            children: [
              _buildInfoRow(
                context: context,
                icon: TablerIcons.user,
                label: 'Nhân viên tạo',
                value: 'Nguyễn Văn A',
                iconColor: AppColors.neutral600,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.building_bank,
                label: 'Chi nhánh',
                value: 'KienLong Bank - CN Tân Bình',
                iconColor: AppColors.neutral600,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.device_mobile,
                label: 'Kênh tạo',
                value: 'Mobile App',
                iconColor: AppColors.neutral600,
              ),
              _buildInfoRow(
                context: context,
                icon: TablerIcons.qrcode,
                label: 'Mã tham chiếu',
                value: 'REF${DateTime.now().millisecondsSinceEpoch.toString().substring(10)}',
                iconColor: AppColors.neutral600,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Thành công':
        return AppColors.success;
      case 'Chờ xử lý':
        return AppColors.warning;
      case 'Lỗi':
        return AppColors.error;
      case 'Đang xử lý':
        return AppColors.info;
      default:
        return AppColors.neutral500;
    }
  }
} 