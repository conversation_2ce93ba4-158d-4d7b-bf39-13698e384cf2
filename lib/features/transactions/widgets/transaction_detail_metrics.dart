import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionDetailMetrics extends StatelessWidget {
  final Map<String, dynamic> transaction;

  const TransactionDetailMetrics({
    super.key,
    required this.transaction,
  });

  String _calculateProcessingTime() {
    // Mock calculation - in real app would calculate from transaction dates
    final random = DateTime.now().millisecondsSinceEpoch % 7 + 1;
    return '$random ngày';
  }

  String _getServiceFee() {
    // Mock service fee based on transaction amount
    final amountStr = transaction['amount'].replaceAll(RegExp(r'[^\d]'), '');
    final amount = double.tryParse(amountStr) ?? 0;
    final fee = (amount * 0.005).round(); // 0.5% fee
    return '${fee.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (match) => '${match[1]}.')}đ';
  }

  int _getProgress() {
    // Mock progress based on status
    switch (transaction['status']) {
      case 'Thành công':
        return 100;
      case 'Chờ xử lý':
        return 45;
      case 'Đang xử lý':
        return 75;
      case 'Lỗi':
        return 25;
      default:
        return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    final metrics = [
      {
        'icon': TablerIcons.calendar,
        'title': 'Ngày tạo',
        'value': transaction['date'],
        'color': AppColors.kienlongSkyBlue,
      },
      {
        'icon': TablerIcons.clock,
        'title': 'Thời gian xử lý',
        'value': _calculateProcessingTime(),
        'color': AppColors.info,
      },
      {
        'icon': TablerIcons.currency_dong,
        'title': 'Phí dịch vụ',
        'value': _getServiceFee(),
        'color': AppColors.warning,
      },
      {
        'icon': TablerIcons.chart_line,
        'title': 'Tiến độ',
        'value': '${_getProgress()}%',
        'color': AppColors.success,
      },
    ];

    return Container(
      margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Row(
        children: metrics.map((metric) {
          return Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: AppDimensions.paddingXS),
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.borderLight,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadowLight,
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: (metric['color'] as Color).withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      metric['icon'] as IconData,
                      color: metric['color'] as Color,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Value
                  Text(
                    metric['value'] as String,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: metric['color'] as Color,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: AppDimensions.spacingXS),
                  
                  // Title
                  Text(
                    metric['title'] as String,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  // Progress bar for progress metric
                  if (metric['title'] == 'Tiến độ') ...[
                    SizedBox(height: AppDimensions.spacingS),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(2),
                      child: LinearProgressIndicator(
                        value: _getProgress() / 100,
                        backgroundColor: AppColors.neutral200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          metric['color'] as Color,
                        ),
                        minHeight: 4,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
} 