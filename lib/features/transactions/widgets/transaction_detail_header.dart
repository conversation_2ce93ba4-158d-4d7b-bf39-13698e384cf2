import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionDetailHeader extends StatelessWidget {
  final Map<String, dynamic> transaction;
  final bool showTitle;
  final VoidCallback onBack;

  const TransactionDetailHeader({
    super.key,
    required this.transaction,
    required this.showTitle,
    required this.onBack,
  });

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Thành công':
        return AppColors.success;
      case 'Chờ xử lý':
        return AppColors.warning;
      case 'Lỗi':
        return AppColors.error;
      case 'Đang xử lý':
        return AppColors.info;
      default:
        return AppColors.neutral500;
    }
  }

  IconData _getProductIcon(String product) {
    switch (product) {
      case 'Vay tín chấp':
      case 'Vay thế chấp':
        return TablerIcons.cash;
      case 'Thẻ tín dụng':
        return TablerIcons.credit_card;
      case '<PERSON><PERSON><PERSON> tiết kiệm':
        return TablerIcons.pig;
      case 'Bảo hiểm':
        return TablerIcons.shield;
      default:
        return TablerIcons.file_text;
    }
  }

  Color _getProductColor(String product) {
    switch (product) {
      case 'Vay tín chấp':
      case 'Vay thế chấp':
        return AppColors.kienlongOrange;
      case 'Thẻ tín dụng':
        return AppColors.kienlongSkyBlue;
      case 'Gửi tiết kiệm':
        return AppColors.success;
      case 'Bảo hiểm':
        return AppColors.info;
      default:
        return AppColors.neutral500;
    }
  }

  String _getTransactionId() {
    // Generate transaction ID from product and date
    final productCode = transaction['product'].toString().substring(0, 2).toUpperCase();
    return 'TX$productCode${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
  }

  @override
  Widget build(BuildContext context) {
    final transactionId = _getTransactionId();
    final productColor = _getProductColor(transaction['product']);
    
    return SliverAppBar(
      expandedHeight: 320,
      pinned: true,
      stretch: true,
      backgroundColor: productColor,
      centerTitle: true,
      title: showTitle ? Text(
        transactionId,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ) : null,
      leading: Container(
        margin: EdgeInsets.all(AppDimensions.paddingXS),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          shape: BoxShape.circle,
        ),
        child: IconButton(
          icon: const Icon(
            TablerIcons.arrow_left,
            color: Colors.white,
          ),
          onPressed: onBack,
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        collapseMode: CollapseMode.parallax,
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                productColor,
                productColor.withValues(alpha: 0.7),
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                AppDimensions.paddingM,
                AppDimensions.paddingXL + AppDimensions.paddingS,
                AppDimensions.paddingM,
                AppDimensions.paddingS,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Product Icon
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      _getProductIcon(transaction['product']),
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingXS),
                  
                  // Transaction ID
                  Text(
                    transactionId,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: 2),
                  
                  // Product Name
                  Text(
                    transaction['product'],
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Amount
                  Text(
                    transaction['amount'],
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Status Badge
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(transaction['status']).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 6,
                          height: 6,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: AppDimensions.spacingXS),
                        Text(
                          transaction['status'],
                          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 