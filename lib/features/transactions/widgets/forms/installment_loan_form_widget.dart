import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../models/form_data/form_data.dart';
import '../../../auth/blocs/master_data_bloc.dart';
import '../../../customers/models/customer_model.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/models/province_model.dart';
import '../../../../shared/models/ward_model.dart';
import '../../../../shared/models/collateral_category_model.dart';
import '../../../../shared/models/bank_account_model.dart';
import '../../../../shared/constants/config_types.dart';
import '../../../../shared/widgets/qr_scanner_widget.dart';
import '../../../../shared/widgets/app_date_field.dart';
import '../../../../shared/widgets/common_dropdown.dart';
import '../../../../shared/widgets/common_text_field.dart';
import '../../../../shared/utils/currency_utils.dart';
import '../../../../shared/utils/qr_utils.dart';
import '../../../../shared/utils/date_utils.dart' as app_date_utils;
import '../../../../shared/utils/number_to_words_util.dart';
import '../../../auth/services/auth_service.dart';
import '../../../auth/services/qr_scanner_service.dart';
import '../../blocs/form_blocs/installment_loan_form_bloc.dart';

/// Widget form cho sản phẩm Vay trả góp ngày (MONGO, GOLD_LOAN)
class InstallmentLoanFormWidget extends StatefulWidget {
  final InstallmentLoanFormData formData;
  final Function(InstallmentLoanFormData) onChanged;
  final CustomerModel? selectedCustomer;

  const InstallmentLoanFormWidget({
    super.key,
    required this.formData,
    required this.onChanged,
    this.selectedCustomer,
  });

  @override
  State<InstallmentLoanFormWidget> createState() =>
      _InstallmentLoanFormWidgetState();
}

class _InstallmentLoanFormWidgetState extends State<InstallmentLoanFormWidget> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, TextEditingController> _controllers = {};
  Timer? _debounceTimer;
  bool _defaultsSet = false;

  // Form bloc instance
  late final InstallmentLoanFormBloc _formBloc;
  
  // Scroll controller và field keys cho scroll to error
  late final ScrollController _scrollController;
  final Map<String, GlobalKey> _fieldKeys = {};

  // Note: Removed _authSubscription as _autofillBranchInfo is called in post frame callback

  // Track last loaded ID card to avoid duplicate API calls
  String? _lastLoadedIdCard;

  // Expansion state for cards
  final Map<String, bool> _cardExpansionStates = {
    'borrower_info': true,
    'co_borrower_info': true,
    'loan_proposal': true,
    'financial_info': true,
    'collateral_basic': true,
    'collateral_detailed': true,
  };

  // Auth service for getting branch info
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();

    // Initialize form bloc
    _formBloc = InstallmentLoanFormBloc();
    
    // Initialize scroll controller
    _scrollController = ScrollController();
    
    // Initialize field keys
    _initializeFieldKeys();

    _initializeControllers();
    _loadMasterData();

    // Initialize form bloc with customer data
    _formBloc.initializeWithCustomer(widget.selectedCustomer);

    // Use post frame callback to avoid setState during build
    debugPrint('🚀 initState completed, scheduling post frame callback');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint(
        '🚀 Post frame callback triggered, calling default value methods',
      );
      _autofillBranchInfo();
      _setDefaultValues();
    });

    // Note: _autofillBranchInfo is called in post frame callback, no need for auth subscription
  }

  /// Load master data for form dropdowns
  void _loadMasterData() {
    // Load provinces for address dropdowns
    context.read<MasterDataBloc>().add(const LoadProvincesEvent());

    // Load wards if province is already selected (from customer data)
    if (widget.formData.borrowerPermanentProvinceId?.isNotEmpty == true) {
      context.read<MasterDataBloc>().add(
        LoadWardsEvent(widget.formData.borrowerPermanentProvinceId!),
      );
    }

    // Load transaction-related configs
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.LOAN_TERM_DAYS),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.LOAN_PURPOSE),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.INCOME_SOURCE),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.ASSET_CONDITION),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.LOAN_METHOD),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.REPAYMENT_METHOD),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.DISBURSEMENT_METHOD),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.HANDOVER_CONDITION),
    );

    // Load personal info related configs
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.SEX));
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.MARITAL_STATUS),
    );
    context.read<MasterDataBloc>().add(
      const LoadConfigEvent(ConfigTypes.ID_CARD_TYPE),
    );

    // Load collateral categories for installment loan products
    context.read<MasterDataBloc>().add(LoadCollateralCategoriesEvent());
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _scrollDebounceTimer?.cancel();
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    // Dispose scroll controller
    _scrollController.dispose();
    // Dispose form bloc
    _formBloc.dispose();
    super.dispose();
  }

  /// Initialize field keys cho tất cả fields
  void _initializeFieldKeys() {
    final fieldNames = [
      'borrower_name', 'borrower_id_type', 'borrower_id_number', 'borrower_birth_date',
      'borrower_gender', 'borrower_phone', 'borrower_permanent_province', 'borrower_permanent_ward',
      'borrower_permanent_address', 'borrower_current_address', 'co_borrower_name', 'co_borrower_id_type', 'co_borrower_id_number',
      'co_borrower_id_issue_date', 'co_borrower_id_expiry_date', 'co_borrower_id_issue_place',
      'co_borrower_birth_date', 'co_borrower_gender', 'co_borrower_phone', 'co_borrower_permanent_address',
      'co_borrower_current_address', 'own_capital', 'loan_amount', 'loan_term', 'loan_method', 'loan_purpose', 'repayment_method',
      'collateral_value', 'collateral_type', 'collateral_owner', 'collateral_owner_birth_year',
      'vehicle_name', 'income_source', 'daily_income', 'daily_revenue', 'business_location_address',
    ];
    
    for (final fieldName in fieldNames) {
      _fieldKeys[fieldName] = GlobalKey();
    }
  }

  /// Expose validation method để parent có thể gọi
  String? validateAndGetFirstErrorField() {
    debugPrint('🔍 validateAndGetFirstErrorField called');
    debugPrint('🔍 Form key current state: ${_formKey.currentState}');
    
    // Trigger Flutter form validation để show UI errors
    final isFormValid = _formKey.currentState?.validate() ?? false;
    debugPrint('🔍 Form validation result: $isFormValid');
    
    // Nếu form validation fail, tìm field đầu tiên có lỗi
    if (!isFormValid) {
      debugPrint('❌ Form validation failed - finding first error field');
      final firstErrorField = _getFirstErrorField();
      debugPrint('🔍 First error field: $firstErrorField');
      return firstErrorField;
    }
    
    debugPrint('✅ Form validation passed - no errors');
    return null;
  }
  
  // Debounce timer để tránh scroll liên tục
  Timer? _scrollDebounceTimer;
  String? _lastScrollField;

  /// Expose scroll method để parent có thể gọi
  void scrollToField(String fieldName, {bool animated = true}) {
    // Debounce: nếu scroll cùng field trong 200ms thì bỏ qua
    if (_lastScrollField == fieldName && _scrollDebounceTimer?.isActive == true) {
      debugPrint('🔍 Debounced scroll to same field: $fieldName');
      return;
    }
    
    _lastScrollField = fieldName;
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 200), () {
      _lastScrollField = null;
    });
    
    _performScroll(fieldName, animated);
  }
  
  /// Scroll nhanh đến field (200ms animation)
  void scrollToFieldFast(String fieldName) {
    scrollToField(fieldName, animated: true);
  }
  
  /// Scroll instant đến field (không animation)
  void scrollToFieldInstant(String fieldName) {
    scrollToField(fieldName, animated: false);
  }
  
  /// Scroll thông minh - tự động chọn animation dựa trên context
  void scrollToFieldSmart(String fieldName) {
    // Nếu field ở step hiện tại, scroll nhanh
    // Nếu field ở step khác, scroll instant để tránh lag
    final currentStep = _getCurrentStepForField(fieldName);
    final isCurrentStep = currentStep == _getCurrentStep();
    
    if (isCurrentStep) {
      scrollToFieldFast(fieldName);
    } else {
      scrollToFieldInstant(fieldName);
    }
  }
  
  /// Lấy step hiện tại của field
  String _getCurrentStepForField(String fieldName) {
    // Borrower fields
    if (['borrower_name', 'borrower_phone', 'borrower_id_number', 'borrower_id_issue_place', 
         'borrower_permanent_address', 'borrower_current_address'].contains(fieldName)) {
      return 'borrower';
    }
    // Co-borrower fields
    if (['co_borrower_name', 'co_borrower_phone', 'co_borrower_id_number', 'co_borrower_id_issue_place',
         'co_borrower_permanent_address', 'co_borrower_current_address'].contains(fieldName)) {
      return 'co_borrower';
    }
    // Loan fields
    if (['loan_amount', 'own_capital', 'loan_term', 'loan_method', 'loan_purpose'].contains(fieldName)) {
      return 'loan';
    }
    // Financial fields
    if (['income_source', 'daily_income', 'daily_revenue', 'business_location_address'].contains(fieldName)) {
      return 'financial';
    }
    // Collateral fields
    if (['collateral_value', 'collateral_type', 'collateral_owner'].contains(fieldName)) {
      return 'collateral';
    }
    return 'unknown';
  }
  
  /// Lấy step hiện tại (có thể implement logic phức tạp hơn)
  String _getCurrentStep() {
    // Tạm thời return 'borrower' - có thể cải thiện sau
    return 'borrower';
  }
  
  /// Scroll tối ưu cho jump từ step khác - instant scroll + smooth animation
  void scrollToFieldOptimized(String fieldName) {
    debugPrint('🚀 scrollToFieldOptimized called with: $fieldName');
    
    // Bước 1: Instant scroll để jump đến field ngay lập tức
    _performScroll(fieldName, false);
    
    // Bước 2: Sau 100ms, thực hiện smooth scroll để hiển thị đẹp
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _performScroll(fieldName, true);
      }
    });
  }
  
  /// Lấy error message cho field
  String? _getFieldErrorMessage(String fieldName) {
    switch (fieldName) {
      case 'borrower_id_type':
        if (widget.formData.borrowerIdType == null || widget.formData.borrowerIdType!.isEmpty) {
          return 'Vui lòng chọn loại giấy tờ';
        }
        return null;
        
      case 'borrower_gender':
        if (widget.formData.borrowerGender == null || widget.formData.borrowerGender!.isEmpty) {
          return 'Vui lòng chọn giới tính';
        }
        return null;
        
      case 'borrower_marital_status':
        if (widget.formData.borrowerMaritalStatusId == null || widget.formData.borrowerMaritalStatusId!.isEmpty) {
          return 'Vui lòng chọn tình trạng hôn nhân';
        }
        return null;
        
      case 'borrower_permanent_province':
        if (widget.formData.borrowerPermanentProvinceId == null || widget.formData.borrowerPermanentProvinceId!.isEmpty) {
          return 'Vui lòng chọn tỉnh/thành phố thường trú';
        }
        return null;
        
      case 'borrower_permanent_ward':
        if (widget.formData.borrowerPermanentWardId == null || widget.formData.borrowerPermanentWardId!.isEmpty) {
          return 'Vui lòng chọn phường/xã thường trú';
        }
        return null;
        
      case 'borrower_current_province':
        if (widget.formData.borrowerCurrentSamePermanent != true) {
          if (widget.formData.borrowerCurrentProvinceId == null || widget.formData.borrowerCurrentProvinceId!.isEmpty) {
            return 'Vui lòng chọn tỉnh/thành phố hiện tại';
          }
        }
        return null;
        
      case 'borrower_current_ward':
        if (widget.formData.borrowerCurrentSamePermanent != true) {
          if (widget.formData.borrowerCurrentWardId == null || widget.formData.borrowerCurrentWardId!.isEmpty) {
            return 'Vui lòng chọn phường/xã hiện tại';
          }
        }
        return null;
        
      case 'co_borrower_id_type':
        if (widget.formData.hasCoBorrower == true) {
          if (widget.formData.coBorrowerIdType == null || widget.formData.coBorrowerIdType!.isEmpty) {
            return 'Vui lòng chọn loại giấy tờ người đồng vay';
          }
        }
        return null;
        
      case 'co_borrower_gender':
        if (widget.formData.hasCoBorrower == true) {
          if (widget.formData.coBorrowerGender == null || widget.formData.coBorrowerGender!.isEmpty) {
            return 'Vui lòng chọn giới tính người đồng vay';
          }
        }
        return null;
        
      case 'co_borrower_marital_status':
        if (widget.formData.hasCoBorrower == true) {
          if (widget.formData.coBorrowerMaritalStatusId == null || widget.formData.coBorrowerMaritalStatusId!.isEmpty) {
            return 'Vui lòng chọn tình trạng hôn nhân người đồng vay';
          }
        }
        return null;
        
      case 'co_borrower_permanent_province':
        if (widget.formData.hasCoBorrower == true) {
          if (widget.formData.coBorrowerPermanentProvinceId == null || widget.formData.coBorrowerPermanentProvinceId!.isEmpty) {
            return 'Vui lòng chọn tỉnh/thành phố thường trú người đồng vay';
          }
        }
        return null;
        
      case 'co_borrower_permanent_ward':
        if (widget.formData.hasCoBorrower == true) {
          if (widget.formData.coBorrowerPermanentWardId == null || widget.formData.coBorrowerPermanentWardId!.isEmpty) {
            return 'Vui lòng chọn phường/xã thường trú người đồng vay';
          }
        }
        return null;
        
      case 'co_borrower_current_province':
        if (widget.formData.hasCoBorrower == true && widget.formData.coBorrowerCurrentSamePermanent != true) {
          if (widget.formData.coBorrowerCurrentProvinceId == null || widget.formData.coBorrowerCurrentProvinceId!.isEmpty) {
            return 'Vui lòng chọn tỉnh/thành phố hiện tại người đồng vay';
          }
        }
        return null;
        
      case 'co_borrower_current_ward':
        if (widget.formData.hasCoBorrower == true && widget.formData.coBorrowerCurrentSamePermanent != true) {
          if (widget.formData.coBorrowerCurrentWardId == null || widget.formData.coBorrowerCurrentWardId!.isEmpty) {
            return 'Vui lòng chọn phường/xã hiện tại người đồng vay';
          }
        }
        return null;
        
      case 'loan_term':
        if (widget.formData.loanTermId == null || widget.formData.loanTermId!.isEmpty) {
          return 'Vui lòng chọn thời hạn vay';
        }
        return null;
        
      case 'loan_method':
        if (widget.formData.loanMethodId == null || widget.formData.loanMethodId!.isEmpty) {
          return 'Vui lòng chọn phương thức vay';
        }
        return null;
        
      case 'loan_purpose':
        if (widget.formData.loanPurposeId == null || widget.formData.loanPurposeId!.isEmpty) {
          return 'Vui lòng chọn mục đích sử dụng vốn';
        }
        return null;
        
      case 'income_source':
        if (widget.formData.incomeSourceId == null || widget.formData.incomeSourceId!.isEmpty) {
          return 'Vui lòng chọn nguồn thu';
        }
        return null;
        
      case 'collateral_type':
        if (widget.formData.collateralType == null || widget.formData.collateralType!.isEmpty) {
          return 'Vui lòng chọn loại tài sản';
        }
        return null;
        
      default:
        return null;
    }
  }
  
  /// Thực hiện scroll thực tế
  void _performScroll(String fieldName, bool animated) {
    debugPrint('🔍 _performScroll called with: $fieldName, animated: $animated');
    debugPrint('🔍 Available field keys: ${_fieldKeys.keys.toList()}');
    
    if (_fieldKeys.containsKey(fieldName)) {
      final context = _fieldKeys[fieldName]?.currentContext;
      debugPrint('🔍 Field context: $context');
      
      if (context != null) {
        try {
          Scrollable.ensureVisible(
            context,
            duration: animated ? const Duration(milliseconds: 300) : Duration.zero,
            curve: animated ? Curves.easeOutCubic : Curves.linear,
            alignment: 0.2, // Hiển thị field ở vị trí 20% từ đầu màn hình
          );
          debugPrint('🔍 Successfully scrolled to field: $fieldName');
        } catch (e) {
          debugPrint('❌ Error scrolling to field $fieldName: $e');
        }
      } else {
        debugPrint('⚠️ Field context is null for: $fieldName - trying delayed scroll');
        // Thử scroll sau một khoảng thời gian ngắn để context có thời gian build
        SchedulerBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            final delayedContext = _fieldKeys[fieldName]?.currentContext;
            if (delayedContext != null) {
              try {
                Scrollable.ensureVisible(
                  delayedContext,
                  duration: animated ? const Duration(milliseconds: 300) : Duration.zero,
                  curve: animated ? Curves.easeOutCubic : Curves.linear,
                  alignment: 0.2,
                );
                debugPrint('🔍 Successfully scrolled to field (delayed): $fieldName');
              } catch (e) {
                debugPrint('❌ Error scrolling to field (delayed) $fieldName: $e');
              }
            } else {
              debugPrint('❌ Field context still null after delay for: $fieldName');
            }
          }
        });
      }
    } else {
      debugPrint('⚠️ Field key not found: $fieldName');
    }
  }
  
  /// Get first error field based on form order
  String? _getFirstErrorField() {
    // Return field names theo thứ tự trong form
    final fieldOrder = [
      'borrower_name', 'borrower_id_type', 'borrower_id_number', 'borrower_birth_date',
      'borrower_gender', 'borrower_phone', 'borrower_permanent_province', 'borrower_permanent_ward',
      'borrower_permanent_address', 'borrower_current_address', 'co_borrower_name', 'co_borrower_id_type', 'co_borrower_id_number',
      'co_borrower_id_issue_date', 'co_borrower_id_expiry_date', 'co_borrower_id_issue_place',
      'co_borrower_birth_date', 'co_borrower_gender', 'co_borrower_phone', 'co_borrower_permanent_address',
      'co_borrower_current_address', 'own_capital', 'loan_amount', 'loan_term', 'loan_method', 'loan_purpose', 'repayment_method',
      'collateral_value', 'collateral_type', 'collateral_owner', 'collateral_owner_birth_year',
      'vehicle_name', 'income_source', 'daily_income', 'daily_revenue', 'business_location_address',
    ];
    
    // Check từng field theo thứ tự để tìm field đầu tiên có lỗi
    for (final fieldName in fieldOrder) {
      if (_hasFieldError(fieldName)) {
        debugPrint('🔍 First error field found: $fieldName');
        return fieldName;
      }
    }
    
    // Nếu không tìm thấy field nào có lỗi, return field đầu tiên (fallback)
    debugPrint('⚠️ No specific error field found, returning first field');
    return fieldOrder.first;
  }
  
  /// Check if field has error based on validation rules
  bool _hasFieldError(String fieldName) {
    switch (fieldName) {
      case 'borrower_name':
        final value = widget.formData.borrowerName;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 100) return true;
        return false;
        
      case 'borrower_phone':
        final value = widget.formData.borrowerPhone;
        if (value == null || value.trim().isEmpty) return true;
        final phoneRegex = RegExp(r'^(0[3|5|7|8|9])[0-9]{8}$');
        if (!phoneRegex.hasMatch(value.trim())) return true;
        return false;
        
      case 'loan_amount':
        final amount = widget.formData.loanAmount;
        if (amount == null || amount <= 0) return true;
        if (amount < 1000000) return true;
        if (amount > 500000000) return true;
        return false;
        
      case 'income_source':
        final value = widget.formData.incomeSourceId;
        if (value == null || value.isEmpty) return true;
        return false;
        
      case 'daily_income':
        final amount = widget.formData.dailyIncome;
        if (amount == null || amount <= 0) return true;
        return false;
        
      // Add more field validations as needed
      case 'borrower_id_number':
        final value = widget.formData.borrowerIdNumber;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 12) return true;
        final numberRegex = RegExp(r'^[0-9]+$');
        if (!numberRegex.hasMatch(value.trim())) return true;
        return false;
        
      case 'borrower_id_issue_place':
        final value = widget.formData.borrowerIdIssuePlace;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 50) return true;
        return false;
        
      case 'borrower_permanent_address':
        final value = widget.formData.borrowerPermanentAddress;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 250) return true;
        return false;
        
      case 'borrower_current_address':
        // Chỉ validate nếu địa chỉ hiện tại khác thường trú
        if (widget.formData.borrowerCurrentSamePermanent == true) return false;
        final value = widget.formData.borrowerCurrentAddress;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 250) return true;
        return false;
        
      case 'co_borrower_name':
        // Chỉ validate nếu có người đồng vay
        if (widget.formData.hasCoBorrower != true) return false;
        final value = widget.formData.coBorrowerName;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 100) return true;
        return false;
        
      case 'co_borrower_phone':
        // Chỉ validate nếu có người đồng vay
        if (widget.formData.hasCoBorrower != true) return false;
        final value = widget.formData.coBorrowerPhone;
        if (value == null || value.trim().isEmpty) return true;
        final phoneRegex = RegExp(r'^(0[3|5|7|8|9])[0-9]{8}$');
        if (!phoneRegex.hasMatch(value.trim())) return true;
        return false;
        
      case 'co_borrower_id_number':
        // Chỉ validate nếu có người đồng vay
        if (widget.formData.hasCoBorrower != true) return false;
        final value = widget.formData.coBorrowerIdNumber;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 12) return true;
        final numberRegex = RegExp(r'^[0-9]+$');
        if (!numberRegex.hasMatch(value.trim())) return true;
        return false;
        
      case 'co_borrower_id_issue_place':
        // Chỉ validate nếu có người đồng vay
        if (widget.formData.hasCoBorrower != true) return false;
        final value = widget.formData.coBorrowerIdIssuePlace;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 50) return true;
        return false;
        
      case 'co_borrower_permanent_address':
        // Chỉ validate nếu có người đồng vay
        if (widget.formData.hasCoBorrower != true) return false;
        final value = widget.formData.coBorrowerPermanentAddress;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 250) return true;
        return false;
        
      case 'co_borrower_current_address':
        // Chỉ validate nếu có người đồng vay và địa chỉ khác thường trú
        if (widget.formData.hasCoBorrower != true) return false;
        if (widget.formData.coBorrowerCurrentSamePermanent == true) return false;
        final value = widget.formData.coBorrowerCurrentAddress;
        if (value == null || value.trim().isEmpty) return true;
        if (value.trim().length > 250) return true;
        return false;
        
      default:
        return false;
    }
  }


  void _initializeControllers() {
    // Initialize all text controllers for text fields
    _controllers['borrower_name'] = TextEditingController(
      text: widget.formData.borrowerName ?? '',
    );
    _controllers['borrower_phone'] = TextEditingController(
      text: widget.formData.borrowerPhone ?? '',
    );
    _controllers['borrower_id_number'] = TextEditingController(
      text: widget.formData.borrowerIdNumber ?? '',
    );
    _controllers['borrower_id_issue_place'] = TextEditingController(
      text: widget.formData.borrowerIdIssuePlace ?? '',
    );
    _controllers['borrower_permanent_address'] = TextEditingController(
      text: widget.formData.borrowerPermanentAddress ?? '',
    );
    _controllers['borrower_current_address'] = TextEditingController(
      text: widget.formData.borrowerCurrentAddress ?? '',
    );

    // Co-borrower controllers
    _controllers['co_borrower_name'] = TextEditingController(
      text: widget.formData.coBorrowerName ?? '',
    );
    _controllers['co_borrower_phone'] = TextEditingController(
      text: widget.formData.coBorrowerPhone ?? '',
    );
    _controllers['co_borrower_id_number'] = TextEditingController(
      text: widget.formData.coBorrowerIdNumber ?? '',
    );
    _controllers['co_borrower_id_issue_place'] = TextEditingController(
      text: widget.formData.coBorrowerIdIssuePlace ?? '',
    );
    _controllers['co_borrower_permanent_address'] = TextEditingController(
      text: widget.formData.coBorrowerPermanentAddress ?? '',
    );
    _controllers['co_borrower_current_address'] = TextEditingController(
      text: widget.formData.coBorrowerCurrentAddress ?? '',
    );

    // Collateral controllers
    _controllers['collateral_owner'] = TextEditingController(
      text: widget.formData.collateralOwner ?? '',
    );
    _controllers['vehicle_name'] = TextEditingController(
      text: widget.formData.vehicleName ?? '',
    );
    _controllers['vehicle_plate_number'] = TextEditingController(
      text: widget.formData.vehiclePlateNumber ?? '',
    );
    _controllers['vehicle_frame_number'] = TextEditingController(
      text: widget.formData.vehicleFrameNumber ?? '',
    );
    _controllers['vehicle_engine_number'] = TextEditingController(
      text: widget.formData.vehicleEngineNumber ?? '',
    );
    _controllers['vehicle_registration_number'] = TextEditingController(
      text: widget.formData.vehicleRegistrationNumber ?? '',
    );
    _controllers['vehicle_registration_place'] = TextEditingController(
      text: widget.formData.vehicleRegistrationPlace ?? '',
    );

    // Business location controllers
    _controllers['business_location_address'] = TextEditingController(
      text: widget.formData.businessLocationAddress ?? '',
    );

    // Branch code controller - display both code and name
    final branchCode = widget.formData.branchCode ?? '';
    _controllers['branch_code'] = TextEditingController(text: branchCode);

    // Financial controllers
    _controllers['loan_amount'] = TextEditingController(
      text: CurrencyUtils.formatIntToCurrency(widget.formData.loanAmount),
    );
    _controllers['own_capital'] = TextEditingController(
      text: CurrencyUtils.formatIntToCurrency(widget.formData.ownCapital ?? 0),
    );

    // Initialize total capital need calculation from controller values
    final initialLoanAmount =
        CurrencyUtils.parseCurrencyToInt(
          _controllers['loan_amount']?.text ?? '',
        ) ??
        0;
    final initialOwnCapital =
        CurrencyUtils.parseCurrencyToInt(
          _controllers['own_capital']?.text ?? '',
        ) ??
        0;
    final initialTotal = initialLoanAmount + initialOwnCapital;
    debugPrint(
      '🔄 Widget: Initializing total capital need from controllers: $initialLoanAmount + $initialOwnCapital = $initialTotal',
    );
    _formBloc.calculateTotalCapitalNeed(initialLoanAmount, initialOwnCapital);

    // Initialize default loan type if not set
    if (widget.formData.loanType == null) {
      _formBloc.updateLoanType(LoanType.withCollateral);
    }

    // Initialize total collateral value from form data
    final initialCollateralValue = widget.formData.collateralValue ?? 0;
    debugPrint('🔄 Widget: Initializing total collateral value: $initialCollateralValue');
    _formBloc.updateTotalCollateralValue(initialCollateralValue);

    // Date field controllers
    _controllers['borrower_id_issue_date'] = TextEditingController(
      text: widget.formData.borrowerIdIssueDate ?? '',
    );
    _controllers['borrower_id_expiry_date'] = TextEditingController(
      text: widget.formData.borrowerIdExpiryDate ?? '',
    );
    _controllers['borrower_birth_date'] = TextEditingController(
      text: widget.formData.borrowerBirthDate ?? '',
    );
    _controllers['co_borrower_id_issue_date'] = TextEditingController(
      text: widget.formData.coBorrowerIdIssueDate ?? '',
    );
    _controllers['co_borrower_id_expiry_date'] = TextEditingController(
      text: widget.formData.coBorrowerIdExpiryDate ?? '',
    );
    _controllers['co_borrower_birth_date'] = TextEditingController(
      text: widget.formData.coBorrowerBirthDate ?? '',
    );
    _controllers['collateral_owner_birth_year'] = TextEditingController(
      text: widget.formData.collateralOwnerBirthYear ?? '',
    );
    _controllers['vehicle_registration_date'] = TextEditingController(
      text: widget.formData.vehicleRegistrationDate ?? '',
    );

    debugPrint('✅ Controllers initialized for all text fields and date fields');
  }

  void _updateFormData(InstallmentLoanFormData newFormData) {
    // Auto-map collateral owner birth year from borrower birth date if not already set
    if ((newFormData.collateralOwnerBirthYear?.isEmpty ?? true) &&
        (newFormData.borrowerBirthDate?.isNotEmpty ?? false)) {
      final updatedFormData = newFormData.copyWith(
        collateralOwnerBirthYear: newFormData.borrowerBirthDate,
      );
      _debouncedUpdate(updatedFormData);
      return;
    }

    // Auto-calculate total capital need when own capital or loan amount changes
    final totalCapitalNeed = _calculateTotalCapitalNeedFromData(newFormData);
    final updatedFormData = newFormData.copyWith(
      totalCapitalNeed: totalCapitalNeed,
    );

    // Update form bloc with calculated total
    _formBloc.calculateTotalCapitalNeed(
      newFormData.loanAmount ?? 0,
      newFormData.ownCapital ?? 0,
    );

    _debouncedUpdate(updatedFormData);
  }

  /// Calculate total capital need from form data
  int _calculateTotalCapitalNeedFromData(InstallmentLoanFormData data) {
    final ownCapital = data.ownCapital ?? 0;
    final loanAmount = data.loanAmount ?? 0;
    final total = ownCapital + loanAmount;
    debugPrint(
      '🔄 Widget: Calculating total capital need: $loanAmount + $ownCapital = $total',
    );
    return total;
  }

  /// Debounced update to avoid too many calls to parent
  void _debouncedUpdate(InstallmentLoanFormData newFormData) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 100), () {
      widget.onChanged(newFormData);
    });
  }

  /// Helper method để check nếu configs thay đổi
  bool _shouldRebuildConfigs(
    MasterDataState previous,
    MasterDataState current,
    String configType,
  ) {
    if (current is MasterDataLoading && current.type == 'config') return true;
    if (current is MasterDataLoaded) {
      final prevConfigs = previous is MasterDataLoaded
          ? (previous.configsByGroup[configType] ?? <ConfigModel>[])
          : <ConfigModel>[];
      final currConfigs = current.configsByGroup[configType] ?? <ConfigModel>[];
      return prevConfigs != currConfigs;
    }
    return false;
  }

  /// Helper method để check nếu provinces thay đổi
  bool _shouldRebuildProvinces(
    MasterDataState previous,
    MasterDataState current,
  ) {
    if (current is MasterDataLoading && current.type == 'provinces') {
      return true;
    }
    if (current is MasterDataLoaded) {
      final prevProvinces = previous is MasterDataLoaded
          ? previous.provinces
          : <ProvinceModel>[];
      final currProvinces = current.provinces;
      return prevProvinces != currProvinces;
    }
    return false;
  }

  /// Helper method để check nếu wards thay đổi
  bool _shouldRebuildWards(
    MasterDataState previous,
    MasterDataState current,
    String? provinceId,
  ) {
    if (provinceId?.isEmpty ?? true) return false;
    if (current is MasterDataLoading && current.type == 'wards') return true;
    if (current is MasterDataLoaded) {
      final prevWards = previous is MasterDataLoaded
          ? (previous.wardsByProvince[provinceId!] ?? <WardModel>[])
          : <WardModel>[];
      final currWards = current.wardsByProvince[provinceId!] ?? <WardModel>[];
      return prevWards != currWards;
    }
    return false;
  }

  /// Helper method để check nếu collateral categories thay đổi
  bool _shouldRebuildCollateralCategories(
    MasterDataState previous,
    MasterDataState current,
  ) {
    if (current is MasterDataLoading && current.type == 'collateral_categories') {
      return true;
    }
    if (current is MasterDataLoaded) {
      final prevCategories = previous is MasterDataLoaded
          ? previous.collateralCategories
          : <CollateralCategoryModel>[];
      final currCategories = current.collateralCategories;
      return prevCategories != currCategories;
    }
    return false;
  }

  /// Check if income source is business-related using master data
  bool _isBusinessIncomeSource() {
    return _isBusinessIncomeSourceFromConfig(_formBloc.incomeSourceModel);
  }

  /// Check if income source is business-related from config model
  bool _isBusinessIncomeSourceFromConfig(ConfigModel? config) {
    if (config == null) return false;

    // Check using config model - so chiều value với masterdata
    final code = config.code?.toLowerCase() ?? '';
    final value = config.value?.toLowerCase() ?? '';
    final label = config.label?.toLowerCase() ?? '';

    // So chiều với data từ API:
    // {"code": "BUSINESS", "label": "Kinh doanh", "value": "Kinh doanh"}
    return code == 'business' ||
        value.contains('kinh doanh') ||
        label.contains('kinh doanh') ||
        code.contains('business');
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
      'Building InstallmentLoanFormWidget - hasCoBorrower: ${widget.formData.hasCoBorrower}',
    );
    debugPrint('🏠 Form Data Debug:');
    debugPrint(
      '   - borrowerPermanentProvinceId: ${widget.formData.borrowerPermanentProvinceId}',
    );
    debugPrint(
      '   - borrowerPermanentWardId: ${widget.formData.borrowerPermanentWardId}',
    );
    debugPrint('   - borrowerName: ${widget.formData.borrowerName}');
    debugPrint('👥 Co-Borrower Data Debug:');
    debugPrint(
      '   - coBorrowerPermanentProvinceId: ${widget.formData.coBorrowerPermanentProvinceId}',
    );
    debugPrint(
      '   - coBorrowerPermanentWardId: ${widget.formData.coBorrowerPermanentWardId}',
    );
    debugPrint(
      '   - coBorrowerCurrentProvinceId: ${widget.formData.coBorrowerCurrentProvinceId}',
    );
    debugPrint(
      '   - coBorrowerCurrentWardId: ${widget.formData.coBorrowerCurrentWardId}',
    );

    return _buildFormContent();
  }

  Widget _buildFormContent() {
    return SingleChildScrollView(
      controller: _scrollController,
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingL),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
                border: Border.all(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        TablerIcons.file_text,
                        color: AppColors.kienlongOrange,
                        size: AppDimensions.iconL,
                      ),
                      SizedBox(width: AppDimensions.spacingM),
                      Expanded(
                        child: Text(
                          'Thông tin Vay trả góp ngày',
                          style: Theme.of(context).textTheme.headlineSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.kienlongOrange,
                              ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Text(
                    'Vui lòng điền đầy đủ thông tin để tạo giao dịch vay trả góp',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),

            // Borrower Information Card
            _buildBorrowerInfoCard(),
            SizedBox(height: AppDimensions.spacingM),

            // Co-borrower Information Card
            _buildCoBorrowerInfoCard(),
            SizedBox(height: AppDimensions.spacingM),

            // Loan Proposal Card
            _buildLoanProposalCard(),
            SizedBox(height: AppDimensions.spacingM),

            // Financial Information Card
            _buildFinancialInfoCard(),
            SizedBox(height: AppDimensions.spacingM),

            // Collateral Information Card
            _buildCollateralInfoCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildBorrowerInfoCard() {
    return _buildExpandableCard(
      title: 'Thông tin người vay chính',
      icon: TablerIcons.user,
      color: AppColors.kienlongOrange,
      expansionKey: 'borrower_info',
      children: [
        // QR/NFC scanning buttons
        _buildQrNfcScanningButtons(
          'Quét thông tin từ GTTT người vay chính',
          _openBorrowerQrScanner,
          () {
            _showQrScanMessage(
              'Tính năng NFC đang được phát triển',
              isError: false,
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Basic borrower info
        CommonTextField(
          key: _fieldKeys['borrower_name'],
          label: 'Họ và tên',
          controller: _controllers['borrower_name'],
          required: true,
          hint: 'Tối đa 100 ký tự',
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập họ và tên';
            }
            if (value.trim().length > 100) {
              return 'Họ và tên không được vượt quá 100 ký tự';
            }
            return null;
          },
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerName: value.isEmpty ? null : value,
              ),
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        CommonTextField(
          key: _fieldKeys['borrower_phone'],
          label: 'Số điện thoại',
          controller: _controllers['borrower_phone'],
          required: true,
          keyboardType: TextInputType.phone,
          hint: '0xxxxxxxxx',
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập số điện thoại';
            }
            // Kiểm tra format số điện thoại Việt Nam
            final phoneRegex = RegExp(r'^(0[3|5|7|8|9])[0-9]{8}$');
            if (!phoneRegex.hasMatch(value.trim())) {
              return 'Số điện thoại không hợp lệ (VD: 0987654321)';
            }
            return null;
          },
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerPhone: value.isEmpty ? null : value,
              ),
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // ID Card Type
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) => _shouldRebuildConfigs(
            previous,
            current,
            ConfigTypes.ID_CARD_TYPE,
          ),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.ID_CARD_TYPE] ??
                      <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.borrowerIdTypeModelStream,
              builder: (context, snapshot) {
                return CommonDropdown<ConfigModel>(
                  label: 'Loại giấy tờ',
                  value: snapshot.data?.id,
                  selectedModel: snapshot.data,
                  items: configs,
                  getItemId: (config) => config.id ?? '',
                  getItemDisplayText: (config) =>
                      config.label ?? config.code ?? '',
                  onChanged: (config) {
                    if (config != null) {
                      _formBloc.updateBorrowerIdType(config);
                    }
                    final updatedFormData = widget.formData.copyWith(
                      borrowerIdType: config?.id,
                      borrowerIdTypeModel: config,
                    );
                    widget.onChanged(updatedFormData);
                  },
                  required: true,
                  isLoading: isLoading,
                  errorMessage: _getFieldErrorMessage('borrower_id_type'),
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // ID Number
        CommonTextField(
          key: _fieldKeys['borrower_id_number'],
          label: 'Số GTTT',
          controller: _controllers['borrower_id_number'],
          required: true,
          hint: 'Nhập số giấy tờ tùy thân, tối đa 12 ký tự số',
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập số GTTT';
            }
            if (value.trim().length > 12) {
              return 'Số GTTT không được vượt quá 12 ký tự';
            }
            // Kiểm tra chỉ chứa số
            final numberRegex = RegExp(r'^[0-9]+$');
            if (!numberRegex.hasMatch(value.trim())) {
              return 'Số GTTT chỉ được chứa ký tự số';
            }
            return null;
          },
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerIdNumber: value.isEmpty ? null : value,
              ),
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        AppDateField(
          label: 'Ngày cấp',
          controller: _controllers['borrower_id_issue_date'],
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerIdIssueDate: value?.isEmpty == true ? null : value,
              ),
            );
          },
          required: true,
          hintText: 'dd/mm/yyyy',
          firstDate: app_date_utils.DateUtils.getCccdIssueDateRange()['first'],
          lastDate: app_date_utils.DateUtils.getCccdIssueDateRange()['last'],
          initialDate:
              app_date_utils.DateUtils.getCccdIssueDateRange()['initial'],
        ),
        SizedBox(height: AppDimensions.spacingM),

        AppDateField(
          label: 'Ngày hết hạn',
          controller: _controllers['borrower_id_expiry_date'],
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerIdExpiryDate: value?.isEmpty == true ? null : value,
              ),
            );
          },
          required: true,
          hintText: 'dd/mm/yyyy',
          firstDate: app_date_utils.DateUtils.getCccdExpiryDateRange()['first'],
          lastDate: app_date_utils.DateUtils.getCccdExpiryDateRange()['last'],
          initialDate:
              app_date_utils.DateUtils.getCccdExpiryDateRange()['initial'],
        ),
        SizedBox(height: AppDimensions.spacingM),

        CommonTextField(
          key: _fieldKeys['borrower_id_issue_place'],
          label: 'Nơi cấp',
          controller: _controllers['borrower_id_issue_place'],
          required: true,
          hint: 'Nơi cấp giấy tờ tùy thân, tối đa 50 ký tự',
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập nơi cấp';
            }
            if (value.trim().length > 50) {
              return 'Nơi cấp không được vượt quá 50 ký tự';
            }
            return null;
          },
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerIdIssuePlace: value.isEmpty ? null : value,
              ),
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Date field
        AppDateField(
          label: 'Ngày sinh',
          controller: _controllers['borrower_birth_date'],
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerBirthDate: value?.isEmpty == true ? null : value,
              ),
            );
          },
          required: true,
          hintText: 'dd/mm/yyyy',
          firstDate: app_date_utils.DateUtils.getBirthDateRange()['first'],
          lastDate: app_date_utils.DateUtils.getBirthDateRange()['last'],
          initialDate: app_date_utils.DateUtils.getBirthDateRange()['initial'],
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Gender dropdown
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) =>
              _shouldRebuildConfigs(previous, current, ConfigTypes.SEX),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.SEX] ?? <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.borrowerGenderModelStream,
              builder: (context, snapshot) {
                return CommonDropdown<ConfigModel>(
                  label: 'Giới tính',
                  value: snapshot.data?.id,
                  selectedModel: snapshot.data,
                  items: configs,
                  getItemId: (config) => config.id ?? '',
                  getItemDisplayText: (config) =>
                      config.label ?? config.code ?? '',
                  onChanged: (config) {
                    if (config != null) {
                      _formBloc.updateBorrowerGender(config);
                    }
                    final updatedFormData = widget.formData.copyWith(
                      borrowerGender: config?.id,
                      borrowerGenderModel: config,
                    );
                    widget.onChanged(updatedFormData);
                  },
                  required: true,
                  isLoading: isLoading,
                  errorMessage: _getFieldErrorMessage('borrower_gender'),
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Marital status dropdown
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) => _shouldRebuildConfigs(
            previous,
            current,
            ConfigTypes.MARITAL_STATUS,
          ),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.MARITAL_STATUS] ??
                      <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.borrowerMaritalStatusModelStream,
              builder: (context, snapshot) {
                return CommonDropdown<ConfigModel>(
                  label: 'Tình trạng hôn nhân',
                  value: snapshot.data?.id,
                  selectedModel: snapshot.data,
                  items: configs,
                  getItemId: (config) => config.id ?? '',
                  getItemDisplayText: (config) =>
                      config.label ?? config.code ?? '',
                  onChanged: (config) {
                    if (config != null) {
                      _formBloc.updateBorrowerMaritalStatus(config);
                    }
                    final updatedFormData = widget.formData.copyWith(
                      borrowerMaritalStatusId: config?.id,
                      borrowerMaritalStatusModel: config,
                    );
                    widget.onChanged(updatedFormData);
                  },
                  required: true,
                  isLoading: isLoading,
                  errorMessage: _getFieldErrorMessage('borrower_marital_status'),
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Address fields
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) =>
              _shouldRebuildProvinces(previous, current),
          builder: (context, state) {
            final provinces = state is MasterDataLoaded
                ? state.provinces
                : <ProvinceModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'provinces';

            return StreamBuilder<ProvinceModel?>(
              stream: _formBloc.borrowerPermanentProvinceModelStream,
              builder: (context, provinceSnapshot) {
                return CommonDropdown<ProvinceModel>(
                  label: 'Tỉnh/Thành phố thường trú',
                  value: provinceSnapshot.data?.id,
                  selectedModel: provinceSnapshot.data,
                  items: provinces,
                  getItemId: (province) => province.id,
                  getItemDisplayText: (province) => province.name,
                  onChanged: (province) {
                    debugPrint(
                      '🏙️ Province changed for borrower permanent: ${province?.id} (${province?.name})',
                    );

                    // Load wards for new province FIRST
                    if (province != null && province.id.isNotEmpty) {
                      debugPrint(
                        '🔄 Loading wards for borrower permanent province: ${province.id} - ${province.name}',
                      );
                      context.read<MasterDataBloc>().add(
                        LoadWardsEvent(province.id),
                      );
                    }

                    // Update form bloc
                    if (province != null) {
                      _formBloc.updateBorrowerPermanentProvince(province);
                    }

                    // Update form data using existing UpdateFormData event
                    final updatedFormData = widget.formData.copyWith(
                      borrowerPermanentProvinceId: province?.id,
                      borrowerPermanentProvinceModel: province,
                      // Clear ward when province changes
                      borrowerPermanentWardId: null,
                      borrowerPermanentWardModel: null,
                    );

                    // Update form data via parent callback
                    widget.onChanged(updatedFormData);
                  },
                  required: true,
                  isLoading: isLoading,
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        StreamBuilder<ProvinceModel?>(
          stream: _formBloc.borrowerPermanentProvinceModelStream,
          builder: (context, provinceSnapshot) {
            final provinceId = provinceSnapshot.data?.id;

            return BlocBuilder<MasterDataBloc, MasterDataState>(
              buildWhen: (previous, current) =>
                  _shouldRebuildWards(previous, current, provinceId),
              builder: (context, state) {
                final wards =
                    (state is MasterDataLoaded &&
                        provinceId?.isNotEmpty == true)
                    ? (state.wardsByProvince[provinceId!] ?? <WardModel>[])
                    : <WardModel>[];
                final isLoading =
                    state is MasterDataLoading && state.type == 'wards';

                return StreamBuilder<WardModel?>(
                  stream: _formBloc.borrowerPermanentWardModelStream,
                  builder: (context, wardSnapshot) {
                    return CommonDropdown<WardModel>(
                      label: 'Phường/Xã thường trú',
                      value: wardSnapshot.data?.id,
                      selectedModel: wardSnapshot.data,
                      items: wards,
                      getItemId: (ward) => ward.id ?? '',
                      getItemDisplayText: (ward) =>
                          ward.name ?? '(Không có tên)',
                      onChanged: (ward) {
                        debugPrint(
                          '🏘️ Ward changed for borrower permanent: ${ward?.id} (${ward?.name})',
                        );

                        // Update form bloc
                        if (ward != null) {
                          _formBloc.updateBorrowerPermanentWard(ward);
                        }

                        // Update form data using existing UpdateFormData event
                        final updatedFormData = widget.formData.copyWith(
                          borrowerPermanentWardId: ward?.id,
                          borrowerPermanentWardModel: ward,
                        );

                        // Update form data via parent callback
                        widget.onChanged(updatedFormData);
                      },
                      required: true,
                      isLoading: isLoading,
                      hintText: provinceId?.isEmpty ?? true
                          ? 'Chọn tỉnh/thành phố trước'
                          : null,
                    );
                  },
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        CommonTextField(
          key: _fieldKeys['borrower_permanent_address'],
          label: 'Địa chỉ cụ thể thường trú',
          controller: _controllers['borrower_permanent_address'],
          required: true,
          hint: 'Nhập địa chỉ chi tiết, tối đa 250 ký tự',
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Vui lòng nhập địa chỉ cụ thể thường trú';
            }
            if (value.trim().length > 250) {
              return 'Địa chỉ không được vượt quá 250 ký tự';
            }
            return null;
          },
          onChanged: (value) {
            _updateFormData(
              widget.formData.copyWith(
                borrowerPermanentAddress: value.isEmpty ? null : value,
              ),
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Current address same as permanent switch
        StreamBuilder<bool>(
          stream: _formBloc.borrowerCurrentSamePermanentStream,
          builder: (context, snapshot) {
            return _buildSwitchField(
              label: 'Địa chỉ hiện tại trùng với địa chỉ thường trú',
              value: snapshot.data ?? true,
              onChanged: (value) {
                debugPrint(
                  '🔄 borrowerCurrentSamePermanent changed to: $value',
                );
                _formBloc.updateBorrowerCurrentSamePermanent(value);

                // Update form data with new switch state and clear current address if set to same
                InstallmentLoanFormData updatedFormData = widget.formData
                    .copyWith(borrowerCurrentSamePermanent: value);

                // If setting to same as permanent, clear current address fields
                if (value == true) {
                  updatedFormData = updatedFormData.copyWith(
                    borrowerCurrentProvinceId: null,
                    borrowerCurrentProvinceModel: null,
                    borrowerCurrentWardId: null,
                    borrowerCurrentWardModel: null,
                    borrowerCurrentAddress: null,
                  );

                  // Clear current address controller
                  _controllers['borrower_current_address']?.clear();

                  // Clear form bloc current address models manually by resetting streams
                  _formBloc.clearBorrowerCurrentAddressModels();
                }

                _updateFormData(updatedFormData);
              },
            );
          },
        ),

        // Current address fields (if different from permanent)
        StreamBuilder<bool>(
          stream: _formBloc.borrowerCurrentSamePermanentStream,
          builder: (context, snapshot) {
            final isSame = snapshot.data ?? true;
            if (isSame) return const SizedBox.shrink();

            return Column(
              children: [
                SizedBox(height: AppDimensions.spacingM),
                BlocBuilder<MasterDataBloc, MasterDataState>(
                  buildWhen: (previous, current) =>
                      _shouldRebuildProvinces(previous, current),
                  builder: (context, state) {
                    final provinces = state is MasterDataLoaded
                        ? state.provinces
                        : <ProvinceModel>[];
                    final isLoading =
                        state is MasterDataLoading && state.type == 'provinces';

                    return StreamBuilder<ProvinceModel?>(
                      stream: _formBloc.borrowerCurrentProvinceModelStream,
                      builder: (context, provinceSnapshot) {
                        return CommonDropdown<ProvinceModel>(
                          label: 'Tỉnh/Thành phố hiện tại',
                          value: provinceSnapshot.data?.id,
                          selectedModel: provinceSnapshot.data,
                          items: provinces,
                          getItemId: (province) => province.id,
                          getItemDisplayText: (province) => province.name,
                          onChanged: (province) {
                            debugPrint(
                              '🏙️ Province changed for borrower current: ${province?.id} (${province?.name})',
                            );

                            // Load wards for new province FIRST
                            if (province != null && province.id.isNotEmpty) {
                              debugPrint(
                                '🔄 Loading wards for borrower current province: ${province.id} - ${province.name}',
                              );
                              context.read<MasterDataBloc>().add(
                                LoadWardsEvent(province.id),
                              );
                            }

                            // Update form bloc
                            if (province != null) {
                              _formBloc.updateBorrowerCurrentProvince(province);
                            }

                            debugPrint(
                              '🔄 Province changed for borrower current: ${province?.id} (${province?.name})',
                            );

                            final updatedFormData = widget.formData.copyWith(
                              borrowerCurrentProvinceId: province?.id,
                              borrowerCurrentProvinceModel: province,
                              // Clear ward when province changes
                              borrowerCurrentWardId: null,
                              borrowerCurrentWardModel: null,
                            );

                            debugPrint(
                              '🔄 Updated borrowerCurrentSamePermanent: ${updatedFormData.borrowerCurrentSamePermanent}',
                            );

                            widget.onChanged(updatedFormData);
                          },
                          required: true,
                          isLoading: isLoading,
                        );
                      },
                    );
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),
                StreamBuilder<ProvinceModel?>(
                  stream: _formBloc.borrowerCurrentProvinceModelStream,
                  builder: (context, provinceSnapshot) {
                    final provinceId = provinceSnapshot.data?.id;

                    return BlocBuilder<MasterDataBloc, MasterDataState>(
                      buildWhen: (previous, current) =>
                          _shouldRebuildWards(previous, current, provinceId),
                      builder: (context, state) {
                        final wards =
                            (state is MasterDataLoaded &&
                                provinceId?.isNotEmpty == true)
                            ? (state.wardsByProvince[provinceId!] ??
                                  <WardModel>[])
                            : <WardModel>[];
                        final isLoading =
                            state is MasterDataLoading && state.type == 'wards';

                        return StreamBuilder<WardModel?>(
                          stream: _formBloc.borrowerCurrentWardModelStream,
                          builder: (context, wardSnapshot) {
                            return CommonDropdown<WardModel>(
                              label: 'Phường/Xã hiện tại',
                              value: wardSnapshot.data?.id,
                              selectedModel: wardSnapshot.data,
                              items: wards,
                              getItemId: (ward) => ward.id ?? '',
                              getItemDisplayText: (ward) =>
                                  ward.name ?? '(Không có tên)',
                              onChanged: (ward) {
                                debugPrint(
                                  '🏘️ Ward changed for borrower current: ${ward?.id} (${ward?.name})',
                                );

                                // Update form bloc first
                                if (ward != null) {
                                  _formBloc.updateBorrowerCurrentWard(ward);
                                }

                                final updatedFormData = widget.formData
                                    .copyWith(
                                      borrowerCurrentWardId: ward?.id,
                                      borrowerCurrentWardModel: ward,
                                    );

                                widget.onChanged(updatedFormData);
                              },
                              required: true,
                              isLoading: isLoading,
                              hintText: provinceId?.isEmpty ?? true
                                  ? 'Chọn tỉnh/thành phố trước'
                                  : null,
                            );
                          },
                        );
                      },
                    );
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),
                CommonTextField(
                  key: _fieldKeys['borrower_current_address'],
                  label: 'Địa chỉ cụ thể hiện tại',
                  controller: _controllers['borrower_current_address'],
                  required: true,
                  hint: 'Nhập địa chỉ chi tiết hiện tại, tối đa 250 ký tự',
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Vui lòng nhập địa chỉ cụ thể hiện tại';
                    }
                    if (value.trim().length > 250) {
                      return 'Địa chỉ không được vượt quá 250 ký tự';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    widget.onChanged(
                      widget.formData.copyWith(
                        borrowerCurrentAddress: value.isEmpty ? null : value,
                      ),
                    );
                  },
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildCoBorrowerInfoCard() {
    return Column(
      children: [
        StreamBuilder<bool>(
          stream: _formBloc.hasCoBorrowerStream,
          builder: (context, snapshot) {
            return _buildSwitchField(
              label: 'Có người đồng vay',
              value: snapshot.data ?? true,
              onChanged: (value) {
                debugPrint('🔄 hasCoBorrower changed to: $value');
                _formBloc.updateHasCoBorrower(value);
                final updatedFormData = widget.formData.copyWith(
                  hasCoBorrower: value,
                );
                _updateFormData(updatedFormData);

                // Force UI rebuild to show/hide co-borrower fields
                if (mounted) {
                  setState(() {
                    // This will trigger rebuild of co-borrower section
                  });
                }
              },
            );
          },
        ),
        StreamBuilder<bool>(
          stream: _formBloc.hasCoBorrowerStream,
          builder: (context, snapshot) {
            final hasCoBorrower = snapshot.data ?? false;
            if (!hasCoBorrower) return const SizedBox.shrink();

            return Column(
              children: [
                SizedBox(height: AppDimensions.spacingM),
                _buildExpandableCard(
                  title: 'Thông tin người đồng vay',
                  icon: TablerIcons.users,
                  color: AppColors.kienlongSkyBlue,
                  expansionKey: 'co_borrower_info',
                  children: [
                    // QR/NFC scanning buttons
                    _buildQrNfcScanningButtons(
                      'Quét thông tin từ GTTT',
                      _openCoBorrowerQrScanner,
                      () {
                        _showQrScanMessage(
                          'Tính năng NFC đang được phát triển',
                          isError: false,
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),
                    CommonTextField(
                      key: _fieldKeys['co_borrower_name'],
                      label: 'Họ và tên',
                      controller: _controllers['co_borrower_name'],
                      required: true,
                      hint: 'Tối đa 100 ký tự tính cả khoảng trắng',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vui lòng nhập họ và tên người đồng vay';
                        }
                        if (value.trim().length > 100) {
                          return 'Họ và tên không được vượt quá 100 ký tự';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        _updateFormData(
                          widget.formData.copyWith(
                            coBorrowerName: value.isEmpty ? null : value,
                          ),
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),
                    CommonTextField(
                      key: _fieldKeys['co_borrower_phone'],
                      label: 'Số điện thoại',
                      controller: _controllers['co_borrower_phone'],
                      required: true,
                      keyboardType: TextInputType.phone,
                      hint: 'Bắt đầu bằng số 0, 10 chữ số',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vui lòng nhập số điện thoại người đồng vay';
                        }
                        // Kiểm tra format số điện thoại Việt Nam
                        final phoneRegex = RegExp(r'^(0[3|5|7|8|9])[0-9]{8}$');
                        if (!phoneRegex.hasMatch(value.trim())) {
                          return 'Số điện thoại không hợp lệ (VD: 0987654321)';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        _updateFormData(
                          widget.formData.copyWith(
                            coBorrowerPhone: value.isEmpty ? null : value,
                          ),
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // ID Card Type
                    BlocBuilder<MasterDataBloc, MasterDataState>(
                      buildWhen: (previous, current) => _shouldRebuildConfigs(
                        previous,
                        current,
                        ConfigTypes.ID_CARD_TYPE,
                      ),
                      builder: (context, state) {
                        final configs = state is MasterDataLoaded
                            ? (state.configsByGroup[ConfigTypes.ID_CARD_TYPE] ??
                                  <ConfigModel>[])
                            : <ConfigModel>[];
                        final isLoading =
                            state is MasterDataLoading &&
                            state.type == 'config';

                        return StreamBuilder<ConfigModel?>(
                          stream: _formBloc.coBorrowerIdTypeModelStream,
                          builder: (context, snapshot) {
                            return CommonDropdown<ConfigModel>(
                              label: 'Loại giấy tờ',
                              value: snapshot.data?.id,
                              selectedModel: snapshot.data,
                              items: configs,
                              getItemId: (config) => config.id ?? '',
                              getItemDisplayText: (config) =>
                                  config.label ?? config.code ?? '',
                              onChanged: (config) {
                                if (config != null) {
                                  _formBloc.updateCoBorrowerIdType(config);
                                }
                                final updatedFormData = widget.formData
                                    .copyWith(
                                      coBorrowerIdType: config?.id,
                                      coBorrowerIdTypeModel: config,
                                    );
                                widget.onChanged(updatedFormData);
                              },
                              required: true,
                              isLoading: isLoading,
                              errorMessage: _getFieldErrorMessage('co_borrower_id_type'),
                            );
                          },
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // ID Number
                    CommonTextField(
                      key: _fieldKeys['co_borrower_id_number'],
                      label: 'Số GTTT',
                      controller: _controllers['co_borrower_id_number'],
                      required: true,
                      hint: 'Nhập số giấy tờ tùy thân, tối đa 12 ký tự số',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vui lòng nhập số GTTT người đồng vay';
                        }
                        if (value.trim().length > 12) {
                          return 'Số GTTT không được vượt quá 12 ký tự';
                        }
                        // Kiểm tra chỉ chứa số
                        final numberRegex = RegExp(r'^[0-9]+$');
                        if (!numberRegex.hasMatch(value.trim())) {
                          return 'Số GTTT chỉ được chứa ký tự số';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        widget.onChanged(
                          widget.formData.copyWith(
                            coBorrowerIdNumber: value.isEmpty ? null : value,
                          ),
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Issue Date
                    AppDateField(
                      label: 'Ngày cấp',
                      controller: _controllers['co_borrower_id_issue_date'],
                      onChanged: (value) {
                        _updateFormData(
                          widget.formData.copyWith(
                            coBorrowerIdIssueDate: value?.isEmpty == true
                                ? null
                                : value,
                          ),
                        );
                      },
                      required: true,
                      hintText: 'dd/mm/yyyy',
                      firstDate: app_date_utils
                          .DateUtils.getCccdIssueDateRange()['first'],
                      lastDate: app_date_utils
                          .DateUtils.getCccdIssueDateRange()['last'],
                      initialDate: app_date_utils
                          .DateUtils.getCccdIssueDateRange()['initial'],
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Expiry Date
                    AppDateField(
                      label: 'Ngày hết hạn',
                      controller: _controllers['co_borrower_id_expiry_date'],
                      onChanged: (value) {
                        _updateFormData(
                          widget.formData.copyWith(
                            coBorrowerIdExpiryDate: value?.isEmpty == true
                                ? null
                                : value,
                          ),
                        );
                      },
                      required: true,
                      hintText: 'dd/mm/yyyy',
                      firstDate: app_date_utils
                          .DateUtils.getCccdExpiryDateRange()['first'],
                      lastDate: app_date_utils
                          .DateUtils.getCccdExpiryDateRange()['last'],
                      initialDate: app_date_utils
                          .DateUtils.getCccdExpiryDateRange()['initial'],
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Issue Place
                    CommonTextField(
                      key: _fieldKeys['co_borrower_id_issue_place'],
                      label: 'Nơi cấp',
                      controller: _controllers['co_borrower_id_issue_place'],
                      required: true,
                      hint: 'Nơi cấp giấy tờ tùy thân, tối đa 50 ký tự',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vui lòng nhập nơi cấp người đồng vay';
                        }
                        if (value.trim().length > 50) {
                          return 'Nơi cấp không được vượt quá 50 ký tự';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        widget.onChanged(
                          widget.formData.copyWith(
                            coBorrowerIdIssuePlace: value.isEmpty
                                ? null
                                : value,
                          ),
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Birth date
                    AppDateField(
                      label: 'Ngày sinh',
                      controller: _controllers['co_borrower_birth_date'],
                      onChanged: (value) {
                        _updateFormData(
                          widget.formData.copyWith(coBorrowerBirthDate: value),
                        );
                      },
                      required: true,
                      hintText: 'dd/mm/yyyy',
                      firstDate:
                          app_date_utils.DateUtils.getBirthDateRange()['first'],
                      lastDate:
                          app_date_utils.DateUtils.getBirthDateRange()['last'],
                      initialDate: app_date_utils
                          .DateUtils.getBirthDateRange()['initial'],
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Gender
                    BlocBuilder<MasterDataBloc, MasterDataState>(
                      buildWhen: (previous, current) => _shouldRebuildConfigs(
                        previous,
                        current,
                        ConfigTypes.SEX,
                      ),
                      builder: (context, state) {
                        final configs = state is MasterDataLoaded
                            ? (state.configsByGroup[ConfigTypes.SEX] ??
                                  <ConfigModel>[])
                            : <ConfigModel>[];
                        final isLoading =
                            state is MasterDataLoading &&
                            state.type == 'config';

                        return StreamBuilder<ConfigModel?>(
                          stream: _formBloc.coBorrowerGenderModelStream,
                          builder: (context, snapshot) {
                            return CommonDropdown<ConfigModel>(
                              label: 'Giới tính',
                              value: snapshot.data?.id,
                              selectedModel: snapshot.data,
                              items: configs,
                              getItemId: (config) => config.id ?? '',
                              getItemDisplayText: (config) =>
                                  config.label ?? config.code ?? '',
                              onChanged: (config) {
                                if (config != null) {
                                  _formBloc.updateCoBorrowerGender(config);
                                }
                                final updatedFormData = widget.formData
                                    .copyWith(coBorrowerGender: config?.id);
                                widget.onChanged(updatedFormData);
                              },
                              required: true,
                              isLoading: isLoading,
                              errorMessage: _getFieldErrorMessage('co_borrower_gender'),
                            );
                          },
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Marital status
                    BlocBuilder<MasterDataBloc, MasterDataState>(
                      buildWhen: (previous, current) => _shouldRebuildConfigs(
                        previous,
                        current,
                        ConfigTypes.MARITAL_STATUS,
                      ),
                      builder: (context, state) {
                        final configs = state is MasterDataLoaded
                            ? (state.configsByGroup[ConfigTypes
                                      .MARITAL_STATUS] ??
                                  <ConfigModel>[])
                            : <ConfigModel>[];
                        final isLoading =
                            state is MasterDataLoading &&
                            state.type == 'config';

                        return StreamBuilder<ConfigModel?>(
                          stream: _formBloc.coBorrowerMaritalStatusModelStream,
                          builder: (context, snapshot) {
                            return CommonDropdown<ConfigModel>(
                              label: 'Tình trạng hôn nhân',
                              value: snapshot.data?.id,
                              selectedModel: snapshot.data,
                              items: configs,
                              getItemId: (config) => config.id ?? '',
                              getItemDisplayText: (config) =>
                                  config.label ?? config.code ?? '',
                              onChanged: (config) {
                                if (config != null) {
                                  _formBloc.updateCoBorrowerMaritalStatus(
                                    config,
                                  );
                                }
                                final updatedFormData = widget.formData
                                    .copyWith(
                                      coBorrowerMaritalStatusId: config?.id,
                                      coBorrowerMaritalStatusModel: config,
                                    );
                                widget.onChanged(updatedFormData);
                              },
                              required: true,
                              isLoading: isLoading,
                              errorMessage: _getFieldErrorMessage('co_borrower_marital_status'),
                            );
                          },
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Permanent address section
                    _buildFieldLabel('Địa chỉ thường trú', false),
                    SizedBox(height: AppDimensions.spacingM),

                    // Permanent province
                    BlocBuilder<MasterDataBloc, MasterDataState>(
                      buildWhen: (previous, current) =>
                          _shouldRebuildProvinces(previous, current),
                      builder: (context, state) {
                        final provinces = state is MasterDataLoaded
                            ? state.provinces
                            : <ProvinceModel>[];
                        final isLoading =
                            state is MasterDataLoading &&
                            state.type == 'provinces';

                        return CommonDropdown<ProvinceModel>(
                          label: 'Tỉnh/Thành phố thường trú',
                          value: widget.formData.coBorrowerPermanentProvinceId,
                          selectedModel:
                              widget.formData.coBorrowerPermanentProvinceModel,
                          items: provinces,
                          getItemId: (province) => province.id,
                          getItemDisplayText: (province) => province.name,
                          onChanged: (province) {
                            debugPrint(
                              '🏙️ Province changed for co-borrower permanent: ${province?.id} (${province?.name})',
                            );

                            // Load wards for new province FIRST
                            if (province != null && province.id.isNotEmpty) {
                              debugPrint(
                                '🔄 Loading wards for co-borrower permanent province: ${province.id} - ${province.name}',
                              );
                              context.read<MasterDataBloc>().add(
                                LoadWardsEvent(province.id),
                              );
                            }

                            // Update form bloc
                            if (province != null) {
                              _formBloc.updateCoBorrowerPermanentProvince(
                                province,
                              );
                            }

                            final updatedFormData = widget.formData.copyWith(
                              coBorrowerPermanentProvinceId: province?.id,
                              coBorrowerPermanentProvinceModel: province,
                              // Clear ward when province changes
                              coBorrowerPermanentWardId: null,
                              coBorrowerPermanentWardModel: null,
                            );

                            widget.onChanged(updatedFormData);
                          },
                          required: true,
                          isLoading: isLoading,
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Permanent ward
                    StreamBuilder<ProvinceModel?>(
                      stream: _formBloc.coBorrowerPermanentProvinceModelStream,
                      builder: (context, provinceSnapshot) {
                        final provinceId = provinceSnapshot.data?.id;

                        return BlocBuilder<MasterDataBloc, MasterDataState>(
                          buildWhen: (previous, current) => _shouldRebuildWards(
                            previous,
                            current,
                            provinceId,
                          ),
                          builder: (context, state) {
                            final wards =
                                (state is MasterDataLoaded &&
                                    provinceId?.isNotEmpty == true)
                                ? (state.wardsByProvince[provinceId!] ??
                                      <WardModel>[])
                                : <WardModel>[];
                            final isLoading =
                                state is MasterDataLoading &&
                                state.type == 'wards';

                            return StreamBuilder<WardModel?>(
                              stream:
                                  _formBloc.coBorrowerPermanentWardModelStream,
                              builder: (context, wardSnapshot) {
                                return CommonDropdown<WardModel>(
                                  label: 'Phường/Xã thường trú',
                                  value: wardSnapshot.data?.id,
                                  selectedModel: wardSnapshot.data,
                                  items: wards,
                                  getItemId: (ward) => ward.id ?? '',
                                  getItemDisplayText: (ward) =>
                                      ward.name ?? '(Không có tên)',
                                  onChanged: (ward) {
                                    debugPrint(
                                      '🏘️ Ward changed for co-borrower permanent: ${ward?.id} (${ward?.name})',
                                    );

                                    // Update form bloc
                                    if (ward != null) {
                                      _formBloc.updateCoBorrowerPermanentWard(
                                        ward,
                                      );
                                    }

                                    final updatedFormData = widget.formData
                                        .copyWith(
                                          coBorrowerPermanentWardId: ward?.id,
                                          coBorrowerPermanentWardModel: ward,
                                        );

                                    widget.onChanged(updatedFormData);
                                  },
                                  required: true,
                                  isLoading: isLoading,
                                  hintText: provinceId?.isEmpty ?? true
                                      ? 'Chọn tỉnh/thành phố trước'
                                      : null,
                                );
                              },
                            );
                          },
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Permanent address detail
                    CommonTextField(
                      key: _fieldKeys['co_borrower_permanent_address'],
                      label: 'Địa chỉ thường trú',
                      controller: _controllers['co_borrower_permanent_address'],
                      required: true,
                      hint: 'Số nhà, tên đường, tổ, khu phố, tối đa 250 ký tự',
                      maxLines: 2,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vui lòng nhập địa chỉ thường trú người đồng vay';
                        }
                        if (value.trim().length > 250) {
                          return 'Địa chỉ không được vượt quá 250 ký tự';
                        }
                        return null;
                      },
                      onChanged: (value) {
                        widget.onChanged(
                          widget.formData.copyWith(
                            coBorrowerPermanentAddress: value.isEmpty
                                ? null
                                : value,
                          ),
                        );
                      },
                    ),
                    SizedBox(height: AppDimensions.spacingM),

                    // Current address same as permanent switch for co-borrower
                    StreamBuilder<bool>(
                      stream: _formBloc.coBorrowerCurrentSamePermanentStream,
                      builder: (context, snapshot) {
                        return _buildSwitchField(
                          label:
                              'Địa chỉ hiện tại trùng với địa chỉ thường trú',
                          value: snapshot.data ?? true,
                          onChanged: (value) {
                            debugPrint(
                              '🔄 coBorrowerCurrentSamePermanent changed to: $value',
                            );
                            _formBloc.updateCoBorrowerCurrentSamePermanent(
                              value,
                            );

                            // Update form data with new switch state and clear current address if set to same
                            InstallmentLoanFormData updatedFormData = widget
                                .formData
                                .copyWith(
                                  coBorrowerCurrentSamePermanent: value,
                                );

                            // If setting to same as permanent, clear current address fields
                            if (value == true) {
                              updatedFormData = updatedFormData.copyWith(
                                coBorrowerCurrentProvinceId: null,
                                coBorrowerCurrentProvinceModel: null,
                                coBorrowerCurrentWardId: null,
                                coBorrowerCurrentWardModel: null,
                                coBorrowerCurrentAddress: null,
                              );

                              // Clear current address controller
                              _controllers['co_borrower_current_address']
                                  ?.clear();

                              // Clear form bloc current address models manually by resetting streams
                              _formBloc.clearCoBorrowerCurrentAddressModels();
                            }

                            _updateFormData(updatedFormData);
                          },
                        );
                      },
                    ),

                    // Current address section (only show if not same as permanent)
                    StreamBuilder<bool>(
                      stream: _formBloc.coBorrowerCurrentSamePermanentStream,
                      builder: (context, snapshot) {
                        final isSame = snapshot.data ?? true;
                        if (isSame) return const SizedBox.shrink();

                        return Column(
                          children: [
                            SizedBox(height: AppDimensions.spacingM),
                            _buildFieldLabel('Địa chỉ hiện tại', false),
                            SizedBox(height: AppDimensions.spacingM),

                            // Current province
                            BlocBuilder<MasterDataBloc, MasterDataState>(
                              buildWhen: (previous, current) =>
                                  _shouldRebuildProvinces(previous, current),
                              builder: (context, state) {
                                final provinces = state is MasterDataLoaded
                                    ? state.provinces
                                    : <ProvinceModel>[];
                                final isLoading =
                                    state is MasterDataLoading &&
                                    state.type == 'provinces';

                                return StreamBuilder<ProvinceModel?>(
                                  stream: _formBloc
                                      .coBorrowerCurrentProvinceModelStream,
                                  builder: (context, provinceSnapshot) {
                                    return CommonDropdown<ProvinceModel>(
                                      label: 'Tỉnh/Thành phố hiện tại',
                                      value: provinceSnapshot.data?.id,
                                      selectedModel: provinceSnapshot.data,
                                      items: provinces,
                                      getItemId: (province) => province.id,
                                      getItemDisplayText: (province) =>
                                          province.name,
                                      onChanged: (province) {
                                        debugPrint(
                                          '🏙️ Province changed for co-borrower current: ${province?.id} (${province?.name})',
                                        );

                                        // Load wards for new province FIRST
                                        if (province != null &&
                                            province.id.isNotEmpty) {
                                          debugPrint(
                                            '🔄 Loading wards for co-borrower current province: ${province.id} - ${province.name}',
                                          );
                                          context.read<MasterDataBloc>().add(
                                            LoadWardsEvent(province.id),
                                          );
                                        }

                                        // Update form bloc
                                        if (province != null) {
                                          _formBloc
                                              .updateCoBorrowerCurrentProvince(
                                                province,
                                              );
                                        }

                                        debugPrint(
                                          '🔄 Province changed for co-borrower current: ${province?.id} (${province?.name})',
                                        );

                                        final updatedFormData = widget.formData
                                            .copyWith(
                                              coBorrowerCurrentProvinceId:
                                                  province?.id,
                                              coBorrowerCurrentProvinceModel:
                                                  province,
                                              // Clear ward when province changes
                                              coBorrowerCurrentWardId: null,
                                              coBorrowerCurrentWardModel: null,
                                            );

                                        debugPrint(
                                          '🔄 Updated coBorrowerCurrentSamePermanent: ${updatedFormData.coBorrowerCurrentSamePermanent}',
                                        );

                                        widget.onChanged(updatedFormData);
                                      },
                                      required: true,
                                      isLoading: isLoading,
                                    );
                                  },
                                );
                              },
                            ),
                            SizedBox(height: AppDimensions.spacingM),

                            // Current ward
                            StreamBuilder<ProvinceModel?>(
                              stream: _formBloc
                                  .coBorrowerCurrentProvinceModelStream,
                              builder: (context, provinceSnapshot) {
                                final provinceId = provinceSnapshot.data?.id;

                                return BlocBuilder<
                                  MasterDataBloc,
                                  MasterDataState
                                >(
                                  buildWhen: (previous, current) =>
                                      _shouldRebuildWards(
                                        previous,
                                        current,
                                        provinceId,
                                      ),
                                  builder: (context, state) {
                                    final wards =
                                        (state is MasterDataLoaded &&
                                            provinceId?.isNotEmpty == true)
                                        ? (state.wardsByProvince[provinceId!] ??
                                              <WardModel>[])
                                        : <WardModel>[];
                                    final isLoading =
                                        state is MasterDataLoading &&
                                        state.type == 'wards';

                                    return StreamBuilder<WardModel?>(
                                      stream: _formBloc
                                          .coBorrowerCurrentWardModelStream,
                                      builder: (context, wardSnapshot) {
                                        return CommonDropdown<WardModel>(
                                          label: 'Phường/Xã hiện tại',
                                          value: wardSnapshot.data?.id,
                                          selectedModel: wardSnapshot.data,
                                          items: wards,
                                          getItemId: (ward) => ward.id ?? '',
                                          getItemDisplayText: (ward) =>
                                              ward.name ?? '(Không có tên)',
                                          onChanged: (ward) {
                                            debugPrint(
                                              '🏘️ Ward changed for co-borrower current: ${ward?.id} (${ward?.name})',
                                            );

                                            // Update form bloc
                                            if (ward != null) {
                                              _formBloc
                                                  .updateCoBorrowerCurrentWard(
                                                    ward,
                                                  );
                                            }

                                            final updatedFormData = widget
                                                .formData
                                                .copyWith(
                                                  coBorrowerCurrentWardId:
                                                      ward?.id,
                                                  coBorrowerCurrentWardModel:
                                                      ward,
                                                );

                                            widget.onChanged(updatedFormData);
                                          },
                                          required: true,
                                          isLoading: isLoading,
                                          hintText: provinceId?.isEmpty ?? true
                                              ? 'Chọn tỉnh/thành phố trước'
                                              : null,
                                        );
                                      },
                                    );
                                  },
                                );
                              },
                            ),
                            SizedBox(height: AppDimensions.spacingM),

                            // Current address detail
                            CommonTextField(
                              key: _fieldKeys['co_borrower_current_address'],
                              label: 'Địa chỉ hiện tại',
                              controller:
                                  _controllers['co_borrower_current_address'],
                              required: true,
                              hint: 'Số nhà, tên đường, tổ, khu phố, tối đa 250 ký tự',
                              maxLines: 2,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Vui lòng nhập địa chỉ hiện tại người đồng vay';
                                }
                                if (value.trim().length > 250) {
                                  return 'Địa chỉ không được vượt quá 250 ký tự';
                                }
                                return null;
                              },
                              onChanged: (value) {
                                widget.onChanged(
                                  widget.formData.copyWith(
                                    coBorrowerCurrentAddress: value.isEmpty
                                        ? null
                                        : value,
                                  ),
                                );
                              },
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildLoanProposalCard() {
    return _buildExpandableCard(
      title: 'Đề nghị và phương án vay vốn',
      icon: TablerIcons.cash,
      color: AppColors.success,
      expansionKey: 'loan_proposal',
      children: [
        // Loan type selection
        _buildLoanTypeSelection(),
        SizedBox(height: AppDimensions.spacingM),

        // Loan amount
        CurrencyTextField(
          key: _fieldKeys['loan_amount'],
          label: 'Số tiền đề nghị vay',
          controller: _controllers['loan_amount'],
          required: true,
          hint: 'Tối thiểu 1.000.000 VNĐ, tối đa 500.000.000 VNĐ',
          maxValue: CurrencyUtils.maxLoanAmount,
          onChanged: (value) {
            final parsedValue = CurrencyUtils.parseCurrencyToInt(value);
            debugPrint('🔄 Widget: Loan amount changed to: $parsedValue');

            // Get current own capital from controller
            final ownCapitalText = _controllers['own_capital']?.text ?? '';
            final ownCapitalValue =
                CurrencyUtils.parseCurrencyToInt(ownCapitalText) ?? 0;

            // Calculate total capital need from controller values
            final totalCapitalNeed = (parsedValue ?? 0) + ownCapitalValue;
            debugPrint(
              '🔄 Widget: Calculating total from controllers: ${parsedValue ?? 0} + $ownCapitalValue = $totalCapitalNeed',
            );

            // Update form data
            final newFormData = widget.formData.copyWith(
              loanAmount: parsedValue,
              totalCapitalNeed: totalCapitalNeed,
            );

            // Update form bloc
            _formBloc.calculateTotalCapitalNeed(
              parsedValue ?? 0,
              ownCapitalValue,
            );

            // Update parent
            _debouncedUpdate(newFormData);
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Own capital
        CurrencyTextField(
          label: 'Vốn tự có',
          controller: _controllers['own_capital'],
          required: false,
          maxValue: CurrencyUtils.maxOwnCapital,
          onChanged: (value) {
            final parsedValue = CurrencyUtils.parseCurrencyToInt(value);
            debugPrint('🔄 Widget: Own capital changed to: $parsedValue');

            // Get current loan amount from controller
            final loanAmountText = _controllers['loan_amount']?.text ?? '';
            final loanAmountValue =
                CurrencyUtils.parseCurrencyToInt(loanAmountText) ?? 0;

            // Calculate total capital need from controller values
            final totalCapitalNeed = loanAmountValue + (parsedValue ?? 0);
            debugPrint(
              '🔄 Widget: Calculating total from controllers: $loanAmountValue + ${parsedValue ?? 0} = $totalCapitalNeed',
            );

            // Update form data
            final newFormData = widget.formData.copyWith(
              ownCapital: parsedValue,
              totalCapitalNeed: totalCapitalNeed,
            );

            // Update form bloc
            _formBloc.calculateTotalCapitalNeed(
              loanAmountValue,
              parsedValue ?? 0,
            );

            // Update parent
            _debouncedUpdate(newFormData);
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Total capital need (read-only)
        StreamBuilder<int?>(
          stream: _formBloc.totalCapitalNeedStream,
          builder: (context, snapshot) {
            debugPrint(
              '🔄 Widget: Total capital need stream value: ${snapshot.data}',
            );
            return CurrencyTextField(
              label: 'Tổng nhu cầu vốn',
              value: CurrencyUtils.formatIntToCurrency(snapshot.data ?? 0),
              required: false,
              hint: 'Tự động tính toán',
              readOnly: true,
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Loan term
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) => _shouldRebuildConfigs(
            previous,
            current,
            ConfigTypes.LOAN_TERM_DAYS,
          ),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.LOAN_TERM_DAYS] ??
                      <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.loanTermModelStream,
              builder: (context, snapshot) {
                return CommonDropdown<ConfigModel>(
                  label: 'Thời hạn vay',
                  value: snapshot.data?.id,
                  selectedModel: snapshot.data,
                  items: configs,
                  getItemId: (config) => config.id ?? '',
                  getItemDisplayText: (config) =>
                      config.label ?? config.code ?? '',
                  onChanged: (config) {
                    if (config != null) {
                      _formBloc.updateLoanTerm(config);
                    }
                    final updatedFormData = widget.formData.copyWith(
                      loanTermId: config?.id,
                      loanTermModel: config,
                    );
                    widget.onChanged(updatedFormData);
                  },
                  required: true,
                  isLoading: isLoading,
                  errorMessage: _getFieldErrorMessage('loan_term'),
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Loan method
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) =>
              _shouldRebuildConfigs(previous, current, ConfigTypes.LOAN_METHOD),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.LOAN_METHOD] ??
                      <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.loanMethodModelStream,
              builder: (context, snapshot) {
                return CommonDropdown<ConfigModel>(
                  label: 'Phương thức vay',
                  value: snapshot.data?.id,
                  selectedModel: snapshot.data,
                  items: configs,
                  getItemId: (config) => config.id ?? '',
                  getItemDisplayText: (config) =>
                      config.label ?? config.code ?? '',
                  onChanged: (config) {
                    if (config != null) {
                      _formBloc.updateLoanMethod(config);
                    }
                    final updatedFormData = widget.formData.copyWith(
                      loanMethodId: config?.id,
                      loanMethodModel: config,
                    );
                    widget.onChanged(updatedFormData);
                  },
                  required: true,
                  isLoading: isLoading,
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Loan purpose
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) => _shouldRebuildConfigs(
            previous,
            current,
            ConfigTypes.LOAN_PURPOSE,
          ),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.LOAN_PURPOSE] ??
                      <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.loanPurposeModelStream,
              builder: (context, snapshot) {
                return CommonDropdown<ConfigModel>(
                  label: 'Mục đích sử dụng vốn',
                  value: snapshot.data?.id,
                  selectedModel: snapshot.data,
                  items: configs,
                  getItemId: (config) => config.id ?? '',
                  getItemDisplayText: (config) =>
                      config.label ?? config.code ?? '',
                  onChanged: (config) {
                    if (config != null) {
                      _formBloc.updateLoanPurpose(config);
                    }
                    final updatedFormData = widget.formData.copyWith(
                      loanPurposeId: config?.id,
                      loanPurposeModel: config,
                    );
                    widget.onChanged(updatedFormData);
                  },
                  required: true,
                  isLoading: isLoading,
                  errorMessage: _getFieldErrorMessage('loan_purpose'),
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Repayment method selection
        _buildRepaymentMethodSelection(),
        SizedBox(height: AppDimensions.spacingM),

        // Disbursement method selection
        _buildDisbursementMethodSelection(),
        SizedBox(height: AppDimensions.spacingM),

        // Branch code (read-only, auto-filled)
        CommonTextField(
          label: 'Đơn vị kinh doanh',
          controller: _controllers['branch_code'],
          onChanged: (value) {
            // Parse branch code and name from display text
            final parts = value.split(' - ');
            final branchCode = parts.isNotEmpty ? parts[0] : null;
            final branchName = parts.length > 1 ? parts[1] : null;

            _formBloc.updateBranchInfo(branchCode, branchName);
            _updateFormData(widget.formData.copyWith(branchCode: branchCode));
          },
          required: true,
          hint: 'Tự động điền từ thông tin đăng nhập',
          readOnly: true,
        ),
      ],
    );
  }

  Widget _buildFinancialInfoCard() {
    return _buildExpandableCard(
      title: 'Tình hình tài chính',
      icon: TablerIcons.chart_line,
      color: AppColors.info,
      expansionKey: 'financial_info',
      children: [
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) => _shouldRebuildConfigs(
            previous,
            current,
            ConfigTypes.INCOME_SOURCE,
          ),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.INCOME_SOURCE] ??
                      <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.incomeSourceModelStream,
              builder: (context, snapshot) {
                return CommonDropdown<ConfigModel>(
                  key: _fieldKeys['income_source'],
                  label: 'Nguồn thu',
                  value: snapshot.data?.id,
                  selectedModel: snapshot.data,
                  items: configs,
                  getItemId: (config) => config.id ?? '',
                  getItemDisplayText: (config) =>
                      config.label ?? config.code ?? '',
                  onChanged: (config) {
                    if (config != null) {
                      _formBloc.updateIncomeSource(config);
                    }

                    // Update both id and model config
                    final updatedFormData = widget.formData.copyWith(
                      incomeSourceId: config?.id,
                      incomeSourceModel: config,
                    );

                    // Check if new income source is business-related using the selected config
                    final isBusinessSource = _isBusinessIncomeSourceFromConfig(
                      config,
                    );

                    // Clear business fields when income source changes to non-business
                    final finalFormData = isBusinessSource
                        ? updatedFormData
                        : updatedFormData.copyWith(
                            dailyRevenue: 0,
                            businessLocationProvinceId: null,
                            businessLocationProvinceModel: null,
                            businessLocationWardId: null,
                            businessLocationWardModel: null,
                            businessLocationAddress: null,
                          );

                    widget.onChanged(finalFormData);

                    // Trigger rebuild to show/hide business fields
                    if (mounted) {
                      setState(() {});
                    }
                  },
                  required: true,
                  isLoading: isLoading,
                  errorMessage: _getFieldErrorMessage('income_source'),
                );
              },
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        CurrencyTextField(
          label: 'Thu nhập bình quân/ngày',
          value: CurrencyUtils.formatIntToCurrency(widget.formData.dailyIncome),
          required: true,
          hint: 'Nhập thu nhập hàng ngày',
          maxValue: CurrencyUtils.maxDailyIncome,
          onChanged: (value) {
            final parsedValue = CurrencyUtils.parseCurrencyToInt(value);
            widget.onChanged(
              widget.formData.copyWith(dailyIncome: parsedValue),
            );
          },
        ),
        SizedBox(height: AppDimensions.spacingM),

        // Show business fields if income source is business
        if (_isBusinessIncomeSource()) ...[
          CurrencyTextField(
            label: 'Doanh thu bình quân/ngày',
            value: CurrencyUtils.formatIntToCurrency(
              widget.formData.dailyRevenue,
            ),
            required: false,
            hint: 'Nhập doanh thu hàng ngày',
            maxValue: CurrencyUtils.maxDailyRevenue,
            onChanged: (value) {
              final parsedValue = CurrencyUtils.parseCurrencyToInt(value);
              widget.onChanged(
                widget.formData.copyWith(dailyRevenue: parsedValue),
              );
            },
          ),
          SizedBox(height: AppDimensions.spacingM),

          // Business location section
          _buildFieldLabel('Địa điểm kinh doanh', false),
          SizedBox(height: AppDimensions.spacingM),

          // Business location province
          BlocBuilder<MasterDataBloc, MasterDataState>(
            buildWhen: (previous, current) =>
                _shouldRebuildProvinces(previous, current),
            builder: (context, state) {
              final provinces = state is MasterDataLoaded
                  ? state.provinces
                  : <ProvinceModel>[];
              final isLoading =
                  state is MasterDataLoading && state.type == 'provinces';

              return StreamBuilder<ProvinceModel?>(
                stream: _formBloc.businessLocationProvinceModelStream,
                builder: (context, provinceSnapshot) {
                  return CommonDropdown<ProvinceModel>(
                    label: 'Tỉnh/Thành phố',
                    value: provinceSnapshot.data?.id,
                    selectedModel: provinceSnapshot.data,
                    items: provinces,
                    getItemId: (province) => province.id,
                    getItemDisplayText: (province) => province.name,
                    onChanged: (province) {
                      debugPrint(
                        '🏙️ Province changed for business location: ${province?.id} (${province?.name})',
                      );

                      // Load wards for new province FIRST
                      if (province != null && province.id.isNotEmpty) {
                        debugPrint(
                          '🔄 Loading wards for business location province: ${province.id} - ${province.name}',
                        );
                        context.read<MasterDataBloc>().add(
                          LoadWardsEvent(province.id),
                        );
                      }

                      // Update form bloc
                      if (province != null) {
                        _formBloc.updateBusinessLocationProvince(province);
                      }

                      final updatedFormData = widget.formData.copyWith(
                        businessLocationProvinceId: province?.id,
                        businessLocationProvinceModel: province,
                        // Clear ward when province changes
                        businessLocationWardId: null,
                        businessLocationWardModel: null,
                      );

                      widget.onChanged(updatedFormData);
                    },
                    required: false,
                    isLoading: isLoading,
                  );
                },
              );
            },
          ),
          SizedBox(height: AppDimensions.spacingM),

          // Business location ward
          StreamBuilder<ProvinceModel?>(
            stream: _formBloc.businessLocationProvinceModelStream,
            builder: (context, provinceSnapshot) {
              final provinceId = provinceSnapshot.data?.id;

              return BlocBuilder<MasterDataBloc, MasterDataState>(
                buildWhen: (previous, current) =>
                    _shouldRebuildWards(previous, current, provinceId),
                builder: (context, state) {
                  final wards =
                      (state is MasterDataLoaded &&
                          provinceId?.isNotEmpty == true)
                      ? (state.wardsByProvince[provinceId!] ?? <WardModel>[])
                      : <WardModel>[];
                  final isLoading =
                      state is MasterDataLoading && state.type == 'wards';

                  return StreamBuilder<WardModel?>(
                    stream: _formBloc.businessLocationWardModelStream,
                    builder: (context, wardSnapshot) {
                      return CommonDropdown<WardModel>(
                        label: 'Phường/Xã',
                        value: wardSnapshot.data?.id,
                        selectedModel: wardSnapshot.data,
                        items: wards,
                        getItemId: (ward) => ward.id ?? '',
                        getItemDisplayText: (ward) =>
                            ward.name ?? '(Không có tên)',
                        onChanged: (ward) {
                          debugPrint(
                            '🏘️ Ward changed for business location: ${ward?.id} (${ward?.name})',
                          );

                          // Update form bloc
                          if (ward != null) {
                            _formBloc.updateBusinessLocationWard(ward);
                          }

                          final updatedFormData = widget.formData.copyWith(
                            businessLocationWardId: ward?.id,
                            businessLocationWardModel: ward,
                          );

                          widget.onChanged(updatedFormData);
                        },
                        required: false,
                        isLoading: isLoading,
                        hintText: provinceId?.isEmpty ?? true
                            ? 'Chọn tỉnh/thành phố trước'
                            : null,
                      );
                    },
                  );
                },
              );
            },
          ),
          SizedBox(height: AppDimensions.spacingM),

          CommonTextField(
            key: _fieldKeys['business_location_address'],
            label: 'Địa chỉ cụ thể',
            controller: _controllers['business_location_address'],
            required: false,
            hint: 'Nhập địa chỉ kinh doanh chi tiết, tối đa 250 ký tự',
            maxLines: 3,
            validator: (value) {
              // Chỉ validate nếu có giá trị (không bắt buộc)
              if (value != null && value.trim().isNotEmpty && value.trim().length > 250) {
                return 'Địa chỉ không được vượt quá 250 ký tự';
              }
              return null;
            },
            onChanged: (value) {
              widget.onChanged(
                widget.formData.copyWith(
                  businessLocationAddress: value.isEmpty ? null : value,
                ),
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildCollateralInfoCard() {
    return StreamBuilder<LoanType?>(
      stream: _formBloc.loanTypeStream,
      builder: (context, snapshot) {
        // Only show if loan type has collateral
        if (snapshot.data != LoanType.withCollateral) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            // Basic collateral info
            _buildExpandableCard(
              title: 'Tài sản bảo đảm',
              icon: TablerIcons.shield_check,
              color: AppColors.warning,
              expansionKey: 'collateral_basic',
              children: [
                BlocBuilder<MasterDataBloc, MasterDataState>(
                  buildWhen: (previous, current) =>
                      _shouldRebuildCollateralCategories(previous, current),
                  builder: (context, state) {
                    final categories = state is MasterDataLoaded
                        ? state.collateralCategories
                        : <CollateralCategoryModel>[];
                    final isLoading =
                        state is MasterDataLoading &&
                        state.type == 'collateral_categories';

                    return StreamBuilder<CollateralCategoryModel?>(
                      stream: _formBloc.collateralTypeModelStream,
                      builder: (context, snapshot) {
                        return CommonDropdown<CollateralCategoryModel>(
                          label: 'Loại tài sản',
                          value: snapshot.data?.id,
                          selectedModel: snapshot.data,
                          items: categories,
                          getItemId: (category) => category.id ?? '',
                          getItemDisplayText: (category) => category.name ?? '',
                          onChanged: (category) {
                            if (category != null) {
                              _formBloc.updateCollateralType(category);
                            }
                            final updatedFormData = widget.formData.copyWith(
                              collateralTypeId: category?.id,
                              collateralTypeModel: category,
                            );
                            widget.onChanged(updatedFormData);
                          },
                          required: true,
                          isLoading: isLoading,
                        );
                      },
                    );
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),

                 CurrencyTextField(
                   label: 'Giá trị tài sản',
                   value: CurrencyUtils.formatIntToCurrency(
                     widget.formData.collateralValue,
                   ),
                   required: true,
                   hint: 'Nhập giá trị tài sản bằng VNĐ',
                   maxValue: CurrencyUtils.maxCollateralValue,
                   onChanged: (value) {
                     final parsedValue = CurrencyUtils.parseCurrencyToInt(value);
                     debugPrint('🔄 Widget: Collateral value changed to: $parsedValue');
                     
                     // Only update if parsedValue is not null, otherwise keep current value
                     if (parsedValue != null) {
                       // Update all related fields in one call to avoid multiple rebuilds
                       final textValue = NumberToWordsUtil.convertToWords(
                         parsedValue,
                       );
                       
                       // Calculate total collateral value (currently same as collateral value)
                       final totalCollateralValue = parsedValue;
                       debugPrint('🔄 Widget: Calculating total collateral value: $parsedValue');
                       
                       // Update form bloc
                       _formBloc.updateTotalCollateralValue(totalCollateralValue);
                       
                       // Update form data
                       final updatedFormData = widget.formData.copyWith(
                         collateralValue: parsedValue,
                         collateralValueText: textValue,
                         totalCollateralValue: totalCollateralValue,
                       );
                       _updateFormData(updatedFormData);
                     }
                   },
                 ),
                SizedBox(height: AppDimensions.spacingM),

                // Collateral value in words (read-only)
                // CommonTextField(
                //   label: 'Giá trị tài sản (bằng chữ)',
                //   controller: _controllers['collateral_value_text'],
                //   required: false,
                //   hint: 'Tự động hiển thị',
                //   readOnly: true,
                //   maxLines: 2,
                // ),
                // SizedBox(height: AppDimensions.spacingM),
                CommonTextField(
                  label: 'Hiện trạng tài sản',
                  value: widget.formData.collateralConditionId,
                  onChanged: (value) {
                    widget.onChanged(
                      widget.formData.copyWith(
                        collateralConditionId: value.isEmpty ? null : value,
                      ),
                    );
                  },
                  required: true,
                  hint:
                      'Mô tả tình trạng hiện tại của tài sản (ví dụ: Mới, Cũ, Đã qua sử dụng)',
                ),
                SizedBox(height: AppDimensions.spacingM),

                CommonTextField(
                  label: 'Chủ sở hữu tài sản',
                  controller: _controllers['collateral_owner'],
                  required: true,
                  hint: 'Tên chủ sở hữu tài sản',
                  onChanged: (value) {
                    _updateFormData(
                      widget.formData.copyWith(
                        collateralOwner: value.isEmpty ? null : value,
                      ),
                    );
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),

                AppDateField(
                  label: 'Ngày tháng năm sinh của chủ tài sản',
                  controller: _controllers['collateral_owner_birth_year'],
                  required: true,
                  hintText: 'dd/mm/yyyy',
                  onChanged: (value) {
                    _updateFormData(
                      widget.formData.copyWith(
                        collateralOwnerBirthYear: value?.isEmpty == true
                            ? null
                            : value,
                      ),
                    );
                  },
                  firstDate:
                      app_date_utils.DateUtils.getBirthDateRange()['first'],
                  lastDate:
                      app_date_utils.DateUtils.getBirthDateRange()['last'],
                  initialDate:
                      app_date_utils.DateUtils.getBirthDateRange()['initial'],
                ),
              ],
            ),
            SizedBox(height: AppDimensions.spacingM),

            // Detailed collateral info
            _buildExpandableCard(
              title: 'Chi tiết tài sản bảo đảm',
              icon: TablerIcons.file_description,
              color: AppColors.info,
              expansionKey: 'collateral_detailed',
              children: [
                // Vehicle QR scanning button
                _buildVehicleQrScanningButton(),
                SizedBox(height: AppDimensions.spacingM),
                CommonTextField(
                  label: 'Tên tài sản',
                  controller: _controllers['vehicle_name'],
                  required: true,
                  hint: 'Nhập tên tài sản đảm bảo',
                  onChanged: (value) {
                    _updateFormData(
                      widget.formData.copyWith(
                        vehicleName: value.isEmpty ? null : value,
                      ),
                    );
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),

                CommonTextField(
                  label: 'Biển kiểm soát',
                  controller: _controllers['vehicle_plate_number'],
                  required: true,
                  hint: 'Nhập biển số xe',
                  onChanged: (value) {
                    _updateFormData(
                      widget.formData.copyWith(
                        vehiclePlateNumber: value.isEmpty ? null : value,
                      ),
                    );
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),

                CommonTextField(
                  label: 'Số khung',
                  controller: _controllers['vehicle_frame_number'],
                  onChanged: (value) {
                    widget.onChanged(
                      widget.formData.copyWith(
                        vehicleFrameNumber: value.isEmpty ? null : value,
                      ),
                    );
                  },
                  required: true,
                  hint: 'Nhập số khung xe',
                ),
                SizedBox(height: AppDimensions.spacingM),

                CommonTextField(
                  label: 'Số máy',
                  controller: _controllers['vehicle_engine_number'],
                  onChanged: (value) {
                    widget.onChanged(
                      widget.formData.copyWith(
                        vehicleEngineNumber: value.isEmpty ? null : value,
                      ),
                    );
                  },
                  required: true,
                  hint: 'Nhập số máy xe',
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Số giấy chứng nhận đăng ký xe
                CommonTextField(
                  label: 'Số giấy chứng nhận đăng ký xe',
                  controller: _controllers['vehicle_registration_number'],
                  onChanged: (value) {
                    widget.onChanged(
                      widget.formData.copyWith(
                        vehicleRegistrationNumber: value.isEmpty ? null : value,
                      ),
                    );
                  },
                  required: false,
                  hint: 'Nhập số giấy đăng ký xe',
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Nơi cấp
                CommonTextField(
                  label: 'Nơi cấp',
                  controller: _controllers['vehicle_registration_place'],
                  onChanged: (value) {
                    widget.onChanged(
                      widget.formData.copyWith(
                        vehicleRegistrationPlace: value.isEmpty ? null : value,
                      ),
                    );
                  },
                  required: false,
                  hint: 'Nhập nơi cấp giấy đăng ký xe',
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Ngày cấp
                AppDateField(
                  label: 'Ngày cấp',
                  controller: _controllers['vehicle_registration_date'],
                  onChanged: (value) {
                    _updateFormData(
                      widget.formData.copyWith(
                        vehicleRegistrationDate: value?.isEmpty == true
                            ? null
                            : value,
                      ),
                    );
                  },
                  required: false,
                  hintText: 'dd/mm/yyyy',
                  firstDate:
                      app_date_utils.DateUtils.getCccdIssueDateRange()['first'],
                  lastDate:
                      app_date_utils.DateUtils.getCccdIssueDateRange()['last'],
                  initialDate: app_date_utils
                      .DateUtils.getCccdIssueDateRange()['initial'],
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Tình trạng tài sản khi giao
                BlocBuilder<MasterDataBloc, MasterDataState>(
                  buildWhen: (previous, current) => _shouldRebuildConfigs(
                    previous,
                    current,
                    ConfigTypes.HANDOVER_CONDITION,
                  ),
                  builder: (context, state) {
                    final configs = state is MasterDataLoaded
                        ? (state.configsByGroup[ConfigTypes
                                  .HANDOVER_CONDITION] ??
                              <ConfigModel>[])
                        : <ConfigModel>[];
                    final isLoading =
                        state is MasterDataLoading && state.type == 'config';

                    return StreamBuilder<ConfigModel?>(
                      stream: _formBloc.vehicleConditionAtHandoverModelStream,
                      builder: (context, snapshot) {
                        return CommonDropdown<ConfigModel>(
                          label: 'Tình trạng tài sản khi giao',
                          value: snapshot.data?.id,
                          selectedModel: snapshot.data,
                          items: configs,
                          getItemId: (config) => config.id ?? '',
                          getItemDisplayText: (config) =>
                              config.label ?? config.code ?? '',
                          onChanged: (config) {
                            if (config != null) {
                              _formBloc.updateVehicleConditionAtHandover(
                                config,
                              );
                            }
                            final updatedFormData = widget.formData.copyWith(
                              vehicleConditionAtHandover: config?.id,
                              vehicleConditionAtHandoverModel: config,
                            );
                            widget.onChanged(updatedFormData);
                          },
                          required: false,
                          isLoading: isLoading,
                        );
                      },
                    );
                  },
                ),
                SizedBox(height: AppDimensions.spacingM),

                 // Tổng giá trị tài sản bảo đảm (read-only) - tự động tính từ Giá trị tài sản
                 StreamBuilder<int?>(
                   stream: _formBloc.totalCollateralValueStream,
                   builder: (context, snapshot) {
                     debugPrint('🔄 Widget: Total collateral value stream value: ${snapshot.data}');
                     return CurrencyTextField(
                       label: 'Tổng giá trị tài sản bảo đảm',
                       value: CurrencyUtils.formatIntToCurrency(snapshot.data ?? 0),
                       required: false,
                       hint: 'Tự động tính toán từ Giá trị tài sản',
                       readOnly: true,
                     );
                   },
                 ),
              ],
            ),
          ],
        );
      },
    );
  }

  // Helper methods for building form fields
  Widget _buildExpandableCard({
    required String title,
    required IconData icon,
    required Color color,
    required List<Widget> children,
    String? expansionKey,
  }) {
    final isExpanded = expansionKey != null
        ? (_cardExpansionStates[expansionKey] ?? true)
        : true;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: expansionKey != null
                ? () {
                    setState(() {
                      _cardExpansionStates[expansionKey] = !isExpanded;
                    });
                  }
                : null,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(AppDimensions.radiusM),
              topRight: Radius.circular(AppDimensions.radiusM),
            ),
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusM),
                  topRight: Radius.circular(AppDimensions.radiusM),
                ),
              ),
              child: Row(
                children: [
                  Icon(icon, color: color, size: AppDimensions.iconM),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ),
                  if (expansionKey != null)
                    AnimatedRotation(
                      turns: isExpanded ? 0.5 : 0,
                      duration: const Duration(milliseconds: 200),
                      child: Icon(
                        TablerIcons.chevron_down,
                        color: color,
                        size: AppDimensions.iconS,
                      ),
                    ),
                ],
              ),
            ),
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: isExpanded ? null : 0,
            child: isExpanded
                ? Container(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: children,
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildFieldLabel(String label, bool required) {
    return Row(
      children: [
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
        if (required) ...[
          SizedBox(width: AppDimensions.spacingXS),
          Text(
            '*',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSwitchField({
    required String label,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.kienlongOrange,
          ),
        ],
      ),
    );
  }

  Widget _buildLoanTypeSelection() {
    return StreamBuilder<LoanType?>(
      stream: _formBloc.loanTypeStream,
      builder: (context, snapshot) {
        final selectedLoanType = snapshot.data;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Hình thức vay vốn',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Row(
              children: [
                Expanded(
                  child: _buildRadioTile(
                    'Có TSBĐ',
                    LoanType.withCollateral,
                    selectedLoanType,
                  ),
                ),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: _buildRadioTile(
                    'Không TSBĐ',
                    LoanType.withoutCollateral,
                    selectedLoanType,
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildRepaymentMethodSelection() {
    return BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) => _shouldRebuildConfigs(
        previous,
        current,
        ConfigTypes.REPAYMENT_METHOD,
      ),
      builder: (context, state) {
        final configs = state is MasterDataLoaded
            ? (state.configsByGroup[ConfigTypes.REPAYMENT_METHOD] ??
                  <ConfigModel>[])
            : <ConfigModel>[];
        final isLoading = state is MasterDataLoading && state.type == 'config';

        return StreamBuilder<ConfigModel?>(
          stream: _formBloc.repaymentMethodModelStream,
          builder: (context, snapshot) {
            return CommonDropdown<ConfigModel>(
              label: 'Hình thức trả nợ',
              value: snapshot.data?.id,
              selectedModel: snapshot.data,
              items: configs,
              getItemId: (config) => config.id ?? '',
              getItemDisplayText: (config) => (config.label?.isNotEmpty == true)
                  ? config.label!
                  : (config.code ?? ''),
              onChanged: (config) {
                if (config != null) {
                  _formBloc.updateRepaymentMethod(config);
                }
                _updateFormData(
                  widget.formData.copyWith(
                    repaymentMethodId: config?.id,
                    repaymentMethodModel: config,
                  ),
                );
              },
              required: true,
              isLoading: isLoading,
              hintText: 'Chọn hình thức trả nợ',
            );
          },
        );
      },
    );
  }

  Widget _buildDisbursementMethodSelection() {
    return Column(
      children: [
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) => _shouldRebuildConfigs(
            previous,
            current,
            ConfigTypes.DISBURSEMENT_METHOD,
          ),
          builder: (context, state) {
            final configs = state is MasterDataLoaded
                ? (state.configsByGroup[ConfigTypes.DISBURSEMENT_METHOD] ??
                      <ConfigModel>[])
                : <ConfigModel>[];
            final isLoading =
                state is MasterDataLoading && state.type == 'config';

            if (isLoading) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Phương thức giải ngân',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.success,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  Center(child: CircularProgressIndicator()),
                ],
              );
            }

            return StreamBuilder<ConfigModel?>(
              stream: _formBloc.disbursementMethodModelStream,
              builder: (context, snapshot) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Phương thức giải ngân',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.success,
                      ),
                    ),
                    SizedBox(height: AppDimensions.spacingM),
                    Row(
                      children: configs.map((config) {
                        final isSelected = snapshot.data?.id == config.id;
                        return Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(
                              right: config == configs.last
                                  ? 0
                                  : AppDimensions.spacingM,
                            ),
                            child: _buildDisbursementMethodRadioTile(
                              config.label ?? config.code ?? '',
                              config,
                              isSelected,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                );
              },
            );
          },
        ),

        // Show bank account dropdown if BANK_TRANSFER is selected
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) => _shouldRebuildConfigs(
            previous,
            current,
            ConfigTypes.DISBURSEMENT_METHOD,
          ),
          builder: (context, state) {
            if (_shouldShowBankAccountField(state)) {
              return Column(
                children: [
                  SizedBox(height: AppDimensions.spacingM),
                  _buildBankAccountDropdown(),
                ],
              );
            }
            return SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget _buildRadioTile(
    String title,
    LoanType value,
    LoanType? selectedValue,
  ) {
    final isSelected = selectedValue == value;

    return InkWell(
      onTap: () {
        // Update form bloc
        _formBloc.updateLoanType(value);

        // Update form data
        _updateFormData(widget.formData.copyWith(loanType: value));
      },
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.success.withValues(alpha: 0.1)
              : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected ? AppColors.success : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? TablerIcons.circle_check_filled : TablerIcons.circle,
              color: isSelected ? AppColors.success : AppColors.textSecondary,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isSelected ? AppColors.success : AppColors.textPrimary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisbursementMethodRadioTile(
    String title,
    ConfigModel config,
    bool isSelected,
  ) {
    return InkWell(
      onTap: () {
        _formBloc.updateDisbursementMethod(config);
        _updateFormData(
          widget.formData.copyWith(
            disbursementMethodId: config.id,
            disbursementMethodModel: config,
          ),
        );

        // Load bank accounts if BANK_TRANSFER is selected
        if (config.code == 'BANK_TRANSFER') {
          _loadBankAccountsForCustomer();
        }

        // Force UI rebuild to show/hide bank account field
        if (mounted) {
          setState(() {
            // This will trigger rebuild of bank account section
          });
        }
      },
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.success.withValues(alpha: 0.1)
              : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected ? AppColors.success : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? TablerIcons.circle_check_filled : TablerIcons.circle,
              color: isSelected ? AppColors.success : AppColors.textSecondary,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isSelected ? AppColors.success : AppColors.textPrimary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Set default values for form fields (non-API data only)
  /// Using parent callback for better performance
  void _setDefaultValues() {
    // Prevent multiple calls to avoid resetting user input
    if (_defaultsSet) {
      debugPrint('⚠️ Default values already set, skipping');
      return;
    }

    // Set defaults using parent callback
    // Note: Default values will be set via widget.onChanged calls below

    // Note: Default values are set via widget.onChanged calls below
    // This approach provides better performance and state management
    debugPrint('✅ Setting default values via parent callback');
    _defaultsSet = true;

    // Map customer data to text controllers if selectedCustomer is available
    // Use post frame callback to ensure this runs after form data has been processed
    if (widget.selectedCustomer != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _mapCustomerDataToControllers(widget.selectedCustomer!);
          // ✅ REMOVED: SetDefaults is now called by parent widget (product_details_step.dart)
          // This provides better separation of concerns
        }
      });
    }

    // Keep local UI updates for immediate feedback
    // Set default loan type to "Có TSBĐ" (with collateral)
    if (widget.formData.loanType == null) {
      _formBloc.updateLoanType(LoanType.withCollateral);
      final updatedFormData = widget.formData.copyWith(
        loanType: LoanType.withCollateral,
      );
      widget.onChanged(updatedFormData);
    }

    // Set default own capital to 0
    if (widget.formData.ownCapital == null) {
      final updatedFormData = widget.formData.copyWith(ownCapital: 0);
      widget.onChanged(updatedFormData);
    }

    // Set default daily income to 0
    if (widget.formData.dailyIncome == null) {
      final updatedFormData = widget.formData.copyWith(dailyIncome: 0);
      widget.onChanged(updatedFormData);
    }

    // Set default daily revenue to 0 (for business income)
    if (widget.formData.dailyRevenue == null) {
      final updatedFormData = widget.formData.copyWith(dailyRevenue: 0);
      widget.onChanged(updatedFormData);
    }

    // Set default collateral value to 0 only if not already set
    if (widget.formData.collateralValue == null) {
      final updatedFormData = widget.formData.copyWith(collateralValue: 0);
      widget.onChanged(updatedFormData);
      // Collateral value is now handled by form bloc, no need to update controller
    } else {
      // Collateral value is now handled by form bloc, no need to update controller
    }

    // Set default switch values according to SRS (only if not already set)
    var updatedFormData = widget.formData;

    debugPrint(
      '🔧 BEFORE set defaults - hasCoBorrower: ${updatedFormData.hasCoBorrower}, borrowerCurrentSamePermanent: ${updatedFormData.borrowerCurrentSamePermanent}',
    );

    // Set default values for switches if they are null (never been set by user)
    bool hasChanges = false;

    if (updatedFormData.hasCoBorrower == null) {
      updatedFormData = updatedFormData.copyWith(hasCoBorrower: true);
      hasChanges = true;
      debugPrint('🔧 Set default hasCoBorrower: true');
    }

    if (updatedFormData.borrowerCurrentSamePermanent == null) {
      updatedFormData = updatedFormData.copyWith(
        borrowerCurrentSamePermanent: true,
      );
      hasChanges = true;
      debugPrint('🔧 Set default borrowerCurrentSamePermanent: true');
    }

    if (updatedFormData.coBorrowerCurrentSamePermanent == null) {
      updatedFormData = updatedFormData.copyWith(
        coBorrowerCurrentSamePermanent: true,
      );
      hasChanges = true;
      debugPrint('🔧 Set default coBorrowerCurrentSamePermanent: true');
    }

    if (hasChanges) {
      debugPrint('🔧 Updated default switch values:');
      debugPrint('   - hasCoBorrower: ${updatedFormData.hasCoBorrower}');
      debugPrint(
        '   - borrowerCurrentSamePermanent: ${updatedFormData.borrowerCurrentSamePermanent}',
      );
      debugPrint(
        '   - coBorrowerCurrentSamePermanent: ${updatedFormData.coBorrowerCurrentSamePermanent}',
      );
    } else {
      debugPrint(
        '🔧 No switch defaults needed - all switches have been set by user',
      );
    }

    debugPrint(
      '🔧 AFTER set defaults - hasCoBorrower: ${updatedFormData.hasCoBorrower}, borrowerCurrentSamePermanent: ${updatedFormData.borrowerCurrentSamePermanent}',
    );

    // Set other default values according to SRS
    // Loan term default: 60 days
    if (updatedFormData.loanTermId == null) {
      // Note: This will be set when master data is loaded, but we can set a fallback
      debugPrint('Loan term will be set when master data loads');
    }

    // Loan method default: "Vay trả góp" (Installment) - will be set via master data
    if (updatedFormData.loanMethodId == null) {
      // Note: This will be set when master data is loaded
      debugPrint('Loan method will be set when master data loads');
    }

    // Repayment method default - will be set via master data
    if (updatedFormData.repaymentMethodId == null) {
      // Note: This will be set when master data is loaded
      debugPrint('Repayment method will be set when master data loads');
    }

    // Collateral type default: "Mô tô/xe máy" (Motorcycle)
    if (updatedFormData.collateralTypeId == null) {
      // Note: This will be set when master data is loaded, but we can set a fallback
      debugPrint('Collateral type will be set when master data loads');
    }

    // Apply all defaults at once if there were changes
    if (hasChanges) {
      debugPrint('🔄 Updating form data with default switches');
      widget.onChanged(updatedFormData);
    } else {
      debugPrint('🔄 No form data update needed - no defaults applied');
    }

    // Force UI rebuild to ensure switches show correct state
    if (mounted) {
      debugPrint('🔄 Calling setState to force UI rebuild');
      setState(() {
        // Force rebuild
      });
    }

    debugPrint(
      '✅ Default values set for installment loan form - hasCoBorrower: ${updatedFormData.hasCoBorrower}, borrowerCurrentSamePermanent: ${updatedFormData.borrowerCurrentSamePermanent}, coBorrowerCurrentSamePermanent: ${updatedFormData.coBorrowerCurrentSamePermanent}',
    );
  }

  /// Map customer data to controllers and form data
  void _mapCustomerDataToControllers(CustomerModel customer) {
    debugPrint(
      '=== START: _mapCustomerDataToControllers (TextController + FormData) ===',
    );
    debugPrint('Customer: ${customer.fullName} (${customer.id})');

    // Map basic borrower info to controllers
    if (customer.fullName.isNotEmpty) {
      _controllers['borrower_name']?.text = customer.fullName;
    }

    if (customer.phoneNumber?.isNotEmpty == true) {
      _controllers['borrower_phone']?.text = customer.phoneNumber!;
    }

    if (customer.idCardNumber?.isNotEmpty == true) {
      _controllers['borrower_id_number']?.text = customer.idCardNumber!;
    }

    if (customer.idCardIssuePlace?.isNotEmpty == true) {
      _controllers['borrower_id_issue_place']?.text =
          customer.idCardIssuePlace!;
    }

    if (customer.permanentAddress?.isNotEmpty == true) {
      _controllers['borrower_permanent_address']?.text =
          customer.permanentAddress!;
    }

    // Map collateral info - auto-fill from customer name and birth date
    if (customer.fullName.isNotEmpty) {
      _controllers['collateral_owner']?.text = customer.fullName;
    }

    // Map collateral owner birth year from customer birth date
    if (customer.birthDate != null) {
      final birthDateStr = app_date_utils.DateUtils.formatDate(
        customer.birthDate!,
      );
      _controllers['collateral_owner_birth_year']?.text = birthDateStr;
    }

    // Map date fields if available (convert DateTime to string)
    if (customer.birthDate != null) {
      final birthDateStr = app_date_utils.DateUtils.formatDate(
        customer.birthDate!,
      );
      _controllers['borrower_birth_date']?.text = birthDateStr;
    }

    if (customer.idCardIssueDate != null) {
      final issueDateStr = app_date_utils.DateUtils.formatDate(
        customer.idCardIssueDate!,
      );
      _controllers['borrower_id_issue_date']?.text = issueDateStr;
    }

    if (customer.idCardExpiryDate != null) {
      final expiryDateStr = app_date_utils.DateUtils.formatDate(
        customer.idCardExpiryDate!,
      );
      _controllers['borrower_id_expiry_date']?.text = expiryDateStr;
    }

    // Update form data with all customer data at once - bypass debounce for immediate update
    final updatedFormData = widget.formData.copyWith(
      borrowerName: customer.fullName.isNotEmpty
          ? customer.fullName
          : widget.formData.borrowerName,
      borrowerPhone: customer.phoneNumber?.isNotEmpty == true
          ? customer.phoneNumber
          : widget.formData.borrowerPhone,
      borrowerIdNumber: customer.idCardNumber?.isNotEmpty == true
          ? customer.idCardNumber
          : widget.formData.borrowerIdNumber,
      borrowerIdIssuePlace: customer.idCardIssuePlace?.isNotEmpty == true
          ? customer.idCardIssuePlace
          : widget.formData.borrowerIdIssuePlace,
      borrowerPermanentAddress: customer.permanentAddress?.isNotEmpty == true
          ? customer.permanentAddress
          : widget.formData.borrowerPermanentAddress,
      collateralOwner: customer.fullName.isNotEmpty
          ? customer.fullName
          : widget.formData.collateralOwner,
      collateralOwnerBirthYear: customer.birthDate != null
          ? app_date_utils.DateUtils.formatDate(customer.birthDate!)
          : widget.formData.collateralOwnerBirthYear,
      borrowerBirthDate: customer.birthDate != null
          ? app_date_utils.DateUtils.formatDate(customer.birthDate!)
          : widget.formData.borrowerBirthDate,
      borrowerIdIssueDate: customer.idCardIssueDate != null
          ? app_date_utils.DateUtils.formatDate(customer.idCardIssueDate!)
          : widget.formData.borrowerIdIssueDate,
      borrowerIdExpiryDate: customer.idCardExpiryDate != null
          ? app_date_utils.DateUtils.formatDate(customer.idCardExpiryDate!)
          : widget.formData.borrowerIdExpiryDate,
    );

    // Update form data immediately without debounce
    _debounceTimer?.cancel();
    widget.onChanged(updatedFormData);

    debugPrint(
      '=== END: _mapCustomerDataToControllers (TextController + FormData) ===',
    );
  }

  /// Map ID type from QR scan (defaults to CCCD)
  /// Sử dụng ID từ master data thay vì hardcode
  ConfigModel? _mapIdTypeFromQr() {
    // Lấy CCCD config từ master data
    final masterDataState = context.read<MasterDataBloc>().state;

    if (masterDataState is MasterDataLoaded) {
      final idCardConfigs =
          masterDataState.configsByGroup['ID_CARD_TYPE'] ?? [];

      // Tìm CCCD config theo code hoặc value
      ConfigModel? cccdConfig;

      // Thử tìm theo code trước
      try {
        cccdConfig = idCardConfigs.firstWhere(
          (config) =>
              config.code?.toLowerCase() == 'chip_id' ||
              config.code?.toLowerCase() == 'cccd',
        );
      } catch (e) {
        // Nếu không tìm thấy theo code, thử tìm theo value
        try {
          cccdConfig = idCardConfigs.firstWhere(
            (config) =>
                config.value?.toLowerCase().contains('cccd') == true ||
                config.label?.toLowerCase().contains('cccd') == true ||
                config.value?.toLowerCase().contains('chip') == true ||
                config.label?.toLowerCase().contains('chip') == true,
          );
        } catch (e2) {
          debugPrint('⚠️ CCCD config not found by code or value');
        }
      }

      // Chỉ trả về config nếu tìm thấy CCCD config hợp lệ
      if (cccdConfig?.id != null && cccdConfig!.id!.isNotEmpty) {
        debugPrint(
          '✅ Found CCCD config: ${cccdConfig.code} - ${cccdConfig.label}',
        );
        return cccdConfig;
      }
    }

    // Nếu không tìm thấy trong master data, trả về null
    // Sẽ để UI xử lý việc hiển thị dropdown cho user chọn
    debugPrint('⚠️ CCCD config not found in master data, returning null');
    return null;
  }

  /// Auto-fill branch information from AuthService
  void _autofillBranchInfo() {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser?.profile?.branchCode?.isNotEmpty == true) {
        final branchCode = currentUser!.profile!.branchCode!;
        final branchName = currentUser.profile!.branchName ?? '';

        debugPrint('Branch info available: $branchCode - $branchName');

        // Update form bloc and sync with parent widget
        _formBloc.updateBranchInfo(branchCode, branchName);
        _controllers['branch_code']?.text = '$branchCode - $branchName';
        _updateFormData(widget.formData.copyWith(branchCode: branchCode));

        debugPrint('✅ Branch code updated in form data');
      } else {
        debugPrint('No branch code found in user profile');
      }
    } catch (e) {
      debugPrint('Error auto-filling branch info: $e');
    }
  }

  /// Open QR scanner for borrower CCCD
  void _openBorrowerQrScanner() {
    showDialog(
      context: context,
      builder: (context) => QrScannerWidget(
        onQrDetected: _handleBorrowerQrScanResult,
        onError: _handleQrScanError,
        instructionText:
            'Hướng camera vào mã QR trên CCCD hoặc chọn ảnh từ thư viện',
        scanMode: QrScanMode.continuous,
      ),
    );
  }

  /// Open QR scanner for co-borrower CCCD
  void _openCoBorrowerQrScanner() {
    showDialog(
      context: context,
      builder: (context) => QrScannerWidget(
        onQrDetected: _handleCoBorrowerQrScanResult,
        onError: _handleQrScanError,
        instructionText: 'Hướng camera vào mã QR trên CCCD của người đồng vay',
        scanMode: QrScanMode.continuous,
      ),
    );
  }

  /// Open QR scanner for vehicle registration
  void _openVehicleQrScanner() {
    showDialog(
      context: context,
      builder: (context) => QrScannerWidget(
        onQrDetected: _handleVehicleQrScanResult,
        onError: _handleQrScanError,
        instructionText: 'Hướng camera vào mã QR trên giấy đăng ký xe',
        scanMode: QrScanMode.continuous,
      ),
    );
  }

  /// Handle borrower QR scan result
  void _handleBorrowerQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('Borrower QR scan completed with ${results.length} results');

      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }

      final qrResult = results.first;
      debugPrint('Borrower QR content: ${qrResult.value}');

      // Close QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }

      _showQrScanMessage('Đã quét thành công mã QR từ CCCD');

      // Parse QR data and map to form
      await _parseQrAndMapToBorrowerForm(qrResult.value);
    } catch (e) {
      debugPrint('Error handling borrower QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR', isError: true);
    }
  }

  /// Handle co-borrower QR scan result
  void _handleCoBorrowerQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint(
        'Co-borrower QR scan completed with ${results.length} results',
      );

      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }

      final qrResult = results.first;
      debugPrint('Co-borrower QR content: ${qrResult.value}');

      // Close QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }

      _showQrScanMessage('Đã quét thành công mã QR từ CCCD người đồng vay');

      // Parse QR data and map to form
      await _parseQrAndMapToCoBorrowerForm(qrResult.value);
    } catch (e) {
      debugPrint('Error handling co-borrower QR scan result: $e');
      _showQrScanMessage(
        'Có lỗi xảy ra khi xử lý mã QR người đồng vay',
        isError: true,
      );
    }
  }

  /// Handle vehicle QR scan result
  void _handleVehicleQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('Vehicle QR scan completed with ${results.length} results');

      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }

      final qrResult = results.first;
      debugPrint('Vehicle QR content: ${qrResult.value}');

      // Close QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }

      _showQrScanMessage('Đã quét thành công mã QR từ giấy đăng ký xe');

      // Parse QR data and map to form
      await _parseQrAndMapToVehicleForm(qrResult.value);
    } catch (e) {
      debugPrint('Error handling vehicle QR scan result: $e');
      _showQrScanMessage(
        'Có lỗi xảy ra khi xử lý mã QR giấy đăng ký xe',
        isError: true,
      );
    }
  }

  /// Handle QR scan error
  void _handleQrScanError(String error) async {
    debugPrint('QR scan error: $error');
    _showQrScanMessage('Lỗi quét QR: $error', isError: true);
  }

  /// Show QR scan message
  void _showQrScanMessage(String message, {bool isError = false}) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Parse QR data and map to borrower form
  Future<void> _parseQrAndMapToBorrowerForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToBorrowerForm ===');
      debugPrint('QR Data: $qrData');

      // Parse CCCD QR data using QrUtils to get model
      final cccdQrData = QrUtils.getCccdQrDataModel(qrData);
      debugPrint('Parsed CCCD QR Data Model: $cccdQrData');

      if (cccdQrData != null && cccdQrData.isValid) {
        debugPrint('QR data parsed successfully, mapping to borrower form...');

        // Prepare form data updates
        var updatedFormData = widget.formData;

        // Map QR data to controllers and form data using model properties
        if (cccdQrData.fullName?.isNotEmpty == true) {
          _controllers['borrower_name']?.text = cccdQrData.fullName!;
          updatedFormData = updatedFormData.copyWith(borrowerName: cccdQrData.fullName);
        }

        if (cccdQrData.idNumber?.isNotEmpty == true) {
          _controllers['borrower_id_number']?.text = cccdQrData.idNumber!;
          
          // Auto-set ID type to CCCD when QR is scanned
          final cccdConfig = _mapIdTypeFromQr();
          if (cccdConfig != null && cccdConfig.id != null) {
            // Update form bloc first
            _formBloc.updateBorrowerIdType(cccdConfig);

            // Update form data with both ID number and ID type
            updatedFormData = updatedFormData.copyWith(
              borrowerIdNumber: cccdQrData.idNumber,
              borrowerIdType: cccdConfig.id,
              borrowerIdTypeModel: cccdConfig,
            );
            debugPrint(
              '✅ Auto-set borrower ID type to CCCD: ${cccdConfig.id} - ${cccdConfig.label}',
            );
          } else {
            // Only update ID number if CCCD config not found
            updatedFormData = updatedFormData.copyWith(borrowerIdNumber: cccdQrData.idNumber);
            debugPrint(
              '⚠️ Could not auto-set borrower ID type, user needs to select manually',
            );
          }
        }

        if (cccdQrData.issuePlace?.isNotEmpty == true) {
          _controllers['borrower_id_issue_place']?.text = cccdQrData.issuePlace!;
          updatedFormData = updatedFormData.copyWith(
            borrowerIdIssuePlace: cccdQrData.issuePlace,
          );
        }

        if (cccdQrData.placeOfResidence?.isNotEmpty == true) {
          _controllers['borrower_permanent_address']?.text = cccdQrData.placeOfResidence!;
          updatedFormData = updatedFormData.copyWith(
            borrowerPermanentAddress: cccdQrData.placeOfResidence,
          );
        }

        // Date fields - update form data and controllers (dates are already formatted by getCccdQrDataModel)
        if (cccdQrData.dateOfBirth?.isNotEmpty == true) {
          _controllers['borrower_birth_date']?.text = cccdQrData.dateOfBirth!;
          updatedFormData = updatedFormData.copyWith(borrowerBirthDate: cccdQrData.dateOfBirth);
        }

        if (cccdQrData.issueDate?.isNotEmpty == true) {
          _controllers['borrower_id_issue_date']?.text = cccdQrData.issueDate!;
          updatedFormData = updatedFormData.copyWith(borrowerIdIssueDate: cccdQrData.issueDate);
        }

        // Update form data once with all changes
        _updateFormData(updatedFormData);

        debugPrint('QR data mapped successfully to borrower form');
        
        // Add small delay to ensure form data is fully updated before any validation
        await Future.delayed(const Duration(milliseconds: 100));
      } else {
        debugPrint('QR data parsing failed or invalid data');
        _showQrScanMessage(
          'Không thể đọc thông tin từ mã QR này',
          isError: true,
        );
      }

      debugPrint('=== END: _parseQrAndMapToBorrowerForm ===');
    } catch (e) {
      debugPrint('Error processing borrower QR data: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR', isError: true);
    }
  }

  /// Parse QR data and map to co-borrower form
  Future<void> _parseQrAndMapToCoBorrowerForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToCoBorrowerForm ===');
      debugPrint('Co-borrower QR Data: $qrData');

      // Parse CCCD QR data using QrUtils to get model
      final cccdQrData = QrUtils.getCccdQrDataModel(qrData);
      debugPrint('Parsed Co-borrower CCCD QR Data Model: $cccdQrData');

      if (cccdQrData != null && cccdQrData.isValid) {
        debugPrint(
          'Co-borrower QR data parsed successfully, mapping to form...',
        );

        // Prepare form data updates
        var updatedFormData = widget.formData;

        // Auto-enable co-borrower when QR is scanned
        if (!(widget.formData.hasCoBorrower ?? false)) {
          _formBloc.updateHasCoBorrower(true);
          updatedFormData = updatedFormData.copyWith(hasCoBorrower: true);
          debugPrint('✅ Auto-enabled co-borrower because QR was scanned');
        }

        if (cccdQrData.fullName?.isNotEmpty == true) {
          _controllers['co_borrower_name']?.text = cccdQrData.fullName!;
          updatedFormData = updatedFormData.copyWith(coBorrowerName: cccdQrData.fullName);
        }

        if (cccdQrData.idNumber?.isNotEmpty == true) {
          _controllers['co_borrower_id_number']?.text = cccdQrData.idNumber!;
          
          // Auto-set ID type to CCCD when QR is scanned
          final cccdConfig = _mapIdTypeFromQr();
          if (cccdConfig != null && cccdConfig.id != null) {
            // Update form bloc first
            _formBloc.updateCoBorrowerIdType(cccdConfig);

            // Update form data with both ID number and ID type
            updatedFormData = updatedFormData.copyWith(
              coBorrowerIdNumber: cccdQrData.idNumber,
              coBorrowerIdType: cccdConfig.id,
              coBorrowerIdTypeModel: cccdConfig,
            );
            debugPrint(
              '✅ Auto-set co-borrower ID type to CCCD: ${cccdConfig.id} - ${cccdConfig.label}',
            );
          } else {
            // Only update ID number if CCCD config not found
            updatedFormData = updatedFormData.copyWith(coBorrowerIdNumber: cccdQrData.idNumber);
            debugPrint(
              '⚠️ Could not auto-set co-borrower ID type, user needs to select manually',
            );
          }
        }

        if (cccdQrData.issuePlace?.isNotEmpty == true) {
          _controllers['co_borrower_id_issue_place']?.text = cccdQrData.issuePlace!;
          updatedFormData = updatedFormData.copyWith(
            coBorrowerIdIssuePlace: cccdQrData.issuePlace,
          );
        }

        if (cccdQrData.placeOfResidence?.isNotEmpty == true) {
          _controllers['co_borrower_permanent_address']?.text = cccdQrData.placeOfResidence!;
          updatedFormData = updatedFormData.copyWith(
            coBorrowerPermanentAddress: cccdQrData.placeOfResidence,
          );
        }

        // Date fields - update form data and controllers (dates are already formatted by getCccdQrDataModel)
        if (cccdQrData.dateOfBirth?.isNotEmpty == true) {
          _controllers['co_borrower_birth_date']?.text = cccdQrData.dateOfBirth!;
          updatedFormData = updatedFormData.copyWith(
            coBorrowerBirthDate: cccdQrData.dateOfBirth,
          );
        }

        if (cccdQrData.issueDate?.isNotEmpty == true) {
          _controllers['co_borrower_id_issue_date']?.text = cccdQrData.issueDate!;
          updatedFormData = updatedFormData.copyWith(
            coBorrowerIdIssueDate: cccdQrData.issueDate,
          );
        }

        // Update form data once with all changes
        _updateFormData(updatedFormData);

        debugPrint('QR data mapped successfully to co-borrower form');
        
        // Add small delay to ensure form data is fully updated before any validation
        await Future.delayed(const Duration(milliseconds: 100));
      } else {
        debugPrint('Co-borrower QR data parsing failed or invalid data');
        _showQrScanMessage(
          'Không thể đọc thông tin từ mã QR này',
          isError: true,
        );
      }

      debugPrint('=== END: _parseQrAndMapToCoBorrowerForm ===');
    } catch (e) {
      debugPrint('Error processing co-borrower QR data: $e');
      _showQrScanMessage(
        'Có lỗi xảy ra khi xử lý mã QR người đồng vay',
        isError: true,
      );
    }
  }

  /// Parse QR data and map to vehicle form
  Future<void> _parseQrAndMapToVehicleForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToVehicleForm ===');
      debugPrint('Vehicle QR Data: $qrData');

      // Parse vehicle QR data using QrUtils to get model
      final vehicleQrData = QrUtils.getVehicleQrDataModel(qrData);
      debugPrint('Parsed Vehicle QR Data Model: $vehicleQrData');

      if (vehicleQrData != null && vehicleQrData.isValid) {
        debugPrint('Vehicle QR data parsed successfully, mapping to form...');

        // Map vehicle data to form using model properties
        if (vehicleQrData.plateNumber?.isNotEmpty == true) {
          _controllers['vehicle_plate_number']?.text =
              vehicleQrData.plateNumber!;
          _updateFormData(
            widget.formData.copyWith(
              vehiclePlateNumber: vehicleQrData.plateNumber,
            ),
          );
        }

        if (vehicleQrData.frameNumber?.isNotEmpty == true) {
          _controllers['vehicle_frame_number']?.text =
              vehicleQrData.frameNumber!;
          _updateFormData(
            widget.formData.copyWith(
              vehicleFrameNumber: vehicleQrData.frameNumber,
            ),
          );
        }

        if (vehicleQrData.engineNumber?.isNotEmpty == true) {
          _controllers['vehicle_engine_number']?.text =
              vehicleQrData.engineNumber!;
          _updateFormData(
            widget.formData.copyWith(
              vehicleEngineNumber: vehicleQrData.engineNumber,
            ),
          );
        }

        if (vehicleQrData.vehicleName?.isNotEmpty == true) {
          _controllers['vehicle_name']?.text = vehicleQrData.vehicleName!;
          _updateFormData(
            widget.formData.copyWith(
              vehicleName: vehicleQrData.vehicleName,
            ),
          );
        }

        if (vehicleQrData.collateralOwner?.isNotEmpty == true) {
          _controllers['collateral_owner']?.text =
              vehicleQrData.collateralOwner!;
          _updateFormData(
            widget.formData.copyWith(
              collateralOwner: vehicleQrData.collateralOwner,
            ),
          );
        }

        if (vehicleQrData.registrationNumber?.isNotEmpty == true) {
          _controllers['vehicle_registration_number']?.text =
              vehicleQrData.registrationNumber!;
          _updateFormData(
            widget.formData.copyWith(
              vehicleRegistrationNumber: vehicleQrData.registrationNumber,
            ),
          );
        }

        if (vehicleQrData.registrationPlace?.isNotEmpty == true) {
          _controllers['vehicle_registration_place']?.text =
              vehicleQrData.registrationPlace!;
          _updateFormData(
            widget.formData.copyWith(
              vehicleRegistrationPlace: vehicleQrData.registrationPlace,
            ),
          );
        }

        if (vehicleQrData.registrationDate?.isNotEmpty == true) {
          // Date is already formatted by getVehicleQrDataModel
          _controllers['vehicle_registration_date']?.text =
              vehicleQrData.registrationDate!;
          _updateFormData(
            widget.formData.copyWith(
              vehicleRegistrationDate: vehicleQrData.registrationDate,
            ),
          );
        }

        debugPrint('Vehicle QR data mapped successfully to form');
        _showQrScanMessage('Đã quét và điền thông tin xe thành công');
      } else {
        debugPrint('Vehicle QR data parsing failed or invalid data');
        _showQrScanMessage(
          'Mã QR không hợp lệ hoặc thiếu thông tin cần thiết',
          isError: true,
        );
      }

      debugPrint('=== END: _parseQrAndMapToVehicleForm ===');
    } catch (e) {
      debugPrint('Error processing vehicle QR data: $e');
      _showQrScanMessage(
        'Có lỗi xảy ra khi xử lý mã QR giấy đăng ký xe',
        isError: true,
      );
    }
  }

  /// Build QR/NFC scanning buttons
  Widget _buildQrNfcScanningButtons(
    String title,
    VoidCallback onQrTap,
    VoidCallback onNfcTap,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.kienlongOrange,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        Row(
          children: [
            Expanded(
              child: _buildScanButton(
                'Quét QR',
                TablerIcons.qrcode,
                AppColors.kienlongOrange,
                onQrTap,
              ),
            ),
            SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildScanButton(
                'Quét NFC',
                TablerIcons.nfc,
                AppColors.kienlongSkyBlue,
                onNfcTap,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build vehicle QR scanning button (only QR, no NFC)
  Widget _buildVehicleQrScanningButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quét thông tin từ giấy đăng ký xe',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.kienlongOrange,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),
        SizedBox(
          width: double.infinity,
          child: _buildScanButton(
            'Quét QR',
            TablerIcons.qrcode,
            AppColors.kienlongOrange,
            _openVehicleQrScanner,
          ),
        ),
      ],
    );
  }

  /// Build individual scan button
  Widget _buildScanButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: AppDimensions.iconS),
            SizedBox(width: AppDimensions.spacingS),
            Flexible(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Check if bank account field should be shown
  bool _shouldShowBankAccountField(MasterDataState state) {
    final disbursementMethodModel = _formBloc.disbursementMethodModel;
    if (disbursementMethodModel?.code?.isEmpty ?? true) return false;

    // Check if the selected disbursement method is BANK_TRANSFER
    debugPrint(
      'Selected disbursement method code: ${disbursementMethodModel?.code}',
    );
    return disbursementMethodModel?.code == 'BANK_TRANSFER';
  }

  /// Build bank account dropdown
  Widget _buildBankAccountDropdown() {
    return BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) {
        return current is MasterDataLoaded ||
            (current is MasterDataLoading && current.type == 'bank_accounts') ||
            (current is MasterDataError && current.type == 'bank_accounts');
      },
      builder: (context, state) {
        final bankAccounts = state is MasterDataLoaded
            ? state.bankAccounts
            : <BankAccountModel>[];
        final isLoading =
            state is MasterDataLoading && state.type == 'bank_accounts';
        final hasError =
            state is MasterDataError && state.type == 'bank_accounts';

        return StreamBuilder<BankAccountModel?>(
          stream: _formBloc.disbursementAccountModelStream,
          builder: (context, snapshot) {
            return CommonDropdown<BankAccountModel>(
              label: 'Số tài khoản nhận tiền',
              value: snapshot.data?.accountNo,
              selectedModel: snapshot.data,
              items: bankAccounts,
              getItemId: (account) => account.accountNo,
              getItemDisplayText: (account) => account.displayName.isNotEmpty
                  ? account.displayName
                  : account.accountNo,
              onChanged: (account) {
                _formBloc.updateDisbursementAccount(account);
                final updatedFormData = widget.formData.copyWith(
                  disbursementAccount: account?.accountNo,
                );
                widget.onChanged(updatedFormData);
              },
              required: true,
              isLoading: isLoading,
              hintText: bankAccounts.isEmpty && !isLoading
                  ? 'Không có tài khoản nào'
                  : 'Chọn tài khoản',
              errorMessage: hasError
                  ? 'Không thể tải danh sách tài khoản'
                  : null,
            );
          },
        );
      },
    );
  }

  /// Load bank accounts for the selected customer
  void _loadBankAccountsForCustomer() {
    String? idCardNo;

    // Thứ tự ưu tiên: 1) Profile từ _authService, 2) selectedCustomer, 3) Form data
    try {
      // 1. Lấy từ profile của user hiện tại từ _authService
      final currentUser = _authService.currentUser;
      if (currentUser?.profile?.personIdCardNo?.isNotEmpty == true) {
        idCardNo = currentUser!.profile!.personIdCardNo;
        debugPrint('Using ID card from current user profile: $idCardNo');
      }
    } catch (e) {
      debugPrint('Error getting ID card number: $e');
    }

    // Check if we have ID card number
    if (idCardNo?.isEmpty != false) {
      debugPrint('No ID card number found - cannot load bank accounts');
      return;
    }

    // Check if we already loaded bank accounts for this ID card
    if (_lastLoadedIdCard == idCardNo) {
      debugPrint('Bank accounts already loaded for this ID card: $idCardNo');
      return;
    }

    // Check if currently loading bank accounts
    final masterDataState = context.read<MasterDataBloc>().state;
    if (masterDataState is MasterDataLoading &&
        masterDataState.type == 'bank_accounts') {
      debugPrint(
        'Bank accounts are currently being loaded - skipping duplicate call',
      );
      return;
    }

    // Call API với idCardNo đã lấy được
    debugPrint('Loading bank accounts for customer ID card: $idCardNo');
    _lastLoadedIdCard = idCardNo; // Track this ID card
    context.read<MasterDataBloc>().add(LoadBankAccountsEvent(idCardNo!));
  }
}
