import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';

class ProductFilterBar extends StatelessWidget {
  final void Function(String?)? onFilter;
  final String? selectedFilter;
  final List<String> filters;
  
  const ProductFilterBar({
    this.onFilter,
    this.selectedFilter,
    this.filters = const ['Tất cả sản phẩm'],
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: filters.length,
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = filter == selectedFilter || 
              (selectedFilter == null && filter == 'Tất cả sản phẩm');
          
          return ChoiceChip(
            label: Text(filter),
            selected: isSelected,
            onSelected: (_) {
              if (filter == 'Tất cả sản phẩm') {
                onFilter?.call(null);
              } else {
                onFilter?.call(filter);
              }
            },
            selectedColor: AppColors.kienlongOrange,
            labelStyle: TextStyle(
              color: isSelected ? Colors.white : AppColors.textPrimary,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
            backgroundColor: AppColors.neutral100,
            side: BorderSide(
              color: isSelected ? AppColors.kienlongOrange : AppColors.borderLight,
              width: 1,
            ),
          );
        },
      ),
    );
  }
}
