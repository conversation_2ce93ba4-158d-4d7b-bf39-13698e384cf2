import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/models/index.dart';
import '../../../shared/utils/index.dart';
import '../../../shared/utils/color_utils.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/auth/blocs/master_data_bloc.dart';
import '../../employees/models/employee_model.dart';
import '../../customers/widgets/staff_search_button.dart';
import '../../../shared/widgets/region_multi_select_modal.dart';
import '../../../shared/widgets/branch_multi_select_modal.dart';

class ModernFilterModal extends StatefulWidget {
  final DateTimeRange? initialDateRange;
  final ProductModel? initialProduct;
  final ConfigModel? initialStatus;
  final List<RegionModel>? initialRegions;
  final List<BranchModel>? initialBranches;
  final EmployeeModel? initialEmployee;
  final List<ProductModel> availableProducts;
  final List<ConfigModel> availableStatuses;
  final List<RegionModel> availableRegions;
  final List<BranchModel> availableBranches;
  final List<EmployeeModel> availableEmployees;
  final bool isLoadingOptions;
  final bool branchesLoading;
  final bool isSearchingEmployees;
  final Function(
    DateTimeRange?,
    ProductModel?,
    ConfigModel?,
    List<RegionModel>?,
    List<BranchModel>?,
    EmployeeModel?,
  )
  onApply;
  final Function(ProductModel?)
  onProductChanged; // Callback để load statuses theo product
  final MasterDataBloc?
  masterDataBloc; // Bloc instance để truy cập từ modal context
  final ConfigModel?
  externalSelectedStatus; // Trạng thái được chọn từ bên ngoài (TransactionFilterBar)
  final Function(List<RegionModel>)?
  onRegionsChanged; // Callback để load branches theo regions
  final Stream<List<BranchModel>>? branchesStream;
  final Stream<List<EmployeeModel>>? employeeSearchStream;
  final Function(String)? onEmployeeSearch;

  const ModernFilterModal({
    super.key,
    this.initialDateRange,
    this.initialProduct,
    this.initialStatus,
    this.initialRegions,
    this.initialBranches,
    this.initialEmployee,
    required this.availableProducts,
    required this.availableStatuses,
    this.availableRegions = const [],
    this.availableBranches = const [],
    this.availableEmployees = const [],
    this.isLoadingOptions = false,
    this.branchesLoading = false,
    this.isSearchingEmployees = false,
    required this.onApply,
    required this.onProductChanged,
    this.masterDataBloc,
    this.externalSelectedStatus,
    this.onRegionsChanged,
    this.branchesStream,
    this.employeeSearchStream,
    this.onEmployeeSearch,
  });

  @override
  State<ModernFilterModal> createState() => _ModernFilterModalState();
}

class _ModernFilterModalState extends State<ModernFilterModal> {
  DateTimeRange? _selectedDateRange;
  ProductModel? _selectedProduct;
  ConfigModel? _selectedStatus;
  final List<RegionModel> _selectedRegions = [];
  final List<BranchModel> _selectedBranches = [];
  EmployeeModel? _selectedEmployee;

  // Dynamic branches từ stream
  List<BranchModel> _dynamicBranches = [];

  // Stream subscription for proper cleanup
  StreamSubscription<List<BranchModel>>? _branchesSubscription;

  // Track which quick date is selected (simple approach)
  String? _selectedQuickDate;

  @override
  void initState() {
    super.initState();
    _selectedDateRange = widget.initialDateRange;
    _selectedProduct = widget.initialProduct;
    _selectedStatus = widget.initialStatus;
    _selectedEmployee = widget.initialEmployee;
    
    // Sync with external selected status from TransactionFilterBar
    if (widget.externalSelectedStatus != null) {
      _selectedStatus = widget.externalSelectedStatus;
    }
    
    // Initialize multi-select lists with initial values
    if (widget.initialRegions != null) {
      _selectedRegions.addAll(widget.initialRegions!);
    }
    if (widget.initialBranches != null) {
      _selectedBranches.addAll(widget.initialBranches!);
    }

    // Khởi tạo branches ban đầu - bao gồm cả widget.availableBranches
    _dynamicBranches = widget.availableBranches;

    // Listen stream để cập nhật branches (khi parent load branches) - PROPER STREAM HANDLING
    _branchesSubscription = widget.branchesStream?.listen((branches) {
      if (mounted) {
        setState(() {
          if (branches.isNotEmpty) {
            // SMART MERGE: Combine API response với selected branches để preserve selection
            final Set<String> allBranchIds = branches
                .map((branch) => branch.id)
                .toSet();
            final List<BranchModel> missingSelectedBranches = _selectedBranches
                .where((selected) => !allBranchIds.contains(selected.id))
                .toList();

            // Merge: API branches + missing selected branches
            final List<BranchModel> mergedBranches = [
              ...branches,
              ...missingSelectedBranches,
            ];

            _dynamicBranches = mergedBranches;
          } else {
            // Nếu nhận empty list, CHỈ update nếu không có branches nào trước đó
            // Tránh clear branches khi parent đang loading
            if (_dynamicBranches.isEmpty) {
              _dynamicBranches = branches;
            }
          }
        });
      }
    });

    // Trigger load all branches nếu chưa có branches và parent không đang loading
    // Hoặc nếu parent không đang loading nhưng cũng chưa có data
    if (widget.onRegionsChanged != null &&
        widget.availableBranches.isEmpty &&
        !widget.branchesLoading) {
      // Pass empty list để parent load all branches
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onRegionsChanged?.call([]);
      });
    }
  }

  @override
  void dispose() {
    _branchesSubscription?.cancel();
    super.dispose();
  }

  /// Extract statuses from MasterDataState - prioritize product-specific statuses
  List<ConfigModel> _getStatusesFromState(MasterDataState state) {
    if (state is MasterDataLoaded) {
      // Prioritize Mango statuses if available and product is Mango
      if (_selectedProduct?.code == 'MANGO' &&
          state.configsByGroup.containsKey('PROPOSAL_MANGO_STATUS') &&
          state.configsByGroup['PROPOSAL_MANGO_STATUS']!.isNotEmpty) {
        return state.configsByGroup['PROPOSAL_MANGO_STATUS']!;
      }

      // Fallback to general statuses
      if (state.configsByGroup.containsKey('PROPOSAL_GENERAL_STATUS') &&
          state.configsByGroup['PROPOSAL_GENERAL_STATUS']!.isNotEmpty) {
        return state.configsByGroup['PROPOSAL_GENERAL_STATUS']!;
      }

      // Legacy fallback (for backward compatibility)
      if (state.configsByGroup.containsKey('PROPOSAL_STATUS') &&
          state.configsByGroup['PROPOSAL_STATUS']!.isNotEmpty) {
        return state.configsByGroup['PROPOSAL_STATUS']!;
      }
    }

    // Fallback to widget's availableStatuses if state doesn't have data
    return widget.availableStatuses;
  }

  @override
  void didUpdateWidget(ModernFilterModal oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Sync with external selected status changes
    if (widget.externalSelectedStatus != oldWidget.externalSelectedStatus) {
      setState(() {
        _selectedStatus = widget.externalSelectedStatus;
      });
    }

    // Clear selected status if available statuses changed (e.g., when product changes)
    if (widget.availableStatuses != oldWidget.availableStatuses) {
      // Check if current selected status is still valid in new list
      if (_selectedStatus != null) {
        final isStillValid = widget.availableStatuses.any(
          (status) => status.code == _selectedStatus!.code,
        );
        if (!isStillValid) {
          setState(() {
            _selectedStatus = null;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: AppDimensions.paddingS),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.outline.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.adjustments_horizontal,
                    color: AppColors.kienlongOrange,
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Text(
                    'Lọc nâng cao',
                    style: AppTypography.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      TablerIcons.x,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingL,
              ),
              child: widget.isLoadingOptions
                  ? _buildLoadingState()
                  : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Quick date filters
                  _buildQuickDateFilters(),
                  const SizedBox(height: AppDimensions.spacingL),

                  // Custom date range
                  _buildDateRangeSection(),
                  const SizedBox(height: AppDimensions.spacingL),

                  // Product filter
                  _buildProductSection(),
                  const SizedBox(height: AppDimensions.spacingL),

                  // Status filter - với BlocBuilder để tự động cập nhật
                  widget.masterDataBloc != null
                      ? BlocBuilder<MasterDataBloc, MasterDataState>(
                    bloc: widget.masterDataBloc,
                    buildWhen: (previous, current) {
                      // Rebuild khi config data thay đổi
                      if (current is MasterDataLoaded &&
                          previous is MasterDataLoaded) {
                        return current.configsByGroup !=
                            previous.configsByGroup;
                      }
                      return current is MasterDataLoaded;
                    },
                    builder: (context, state) {
                      return _buildStatusSection(state);
                    },
                  )
                      : _buildStatusSection(MasterDataInitial()),
                  const SizedBox(height: AppDimensions.spacingL),

                  // Regions filter (multi-select)
                  if (widget.availableRegions.isNotEmpty) ...[
                    _buildRegionsSection(),
                    const SizedBox(height: AppDimensions.spacingL),
                  ],

                  // Branches filter (multi-select)
                  _buildBranchesSection(),
                  const SizedBox(height: AppDimensions.spacingL),

                  // Employee filter
                  _buildEmployeeSection(),
                  const SizedBox(height: AppDimensions.spacingL),

                  // Quick filters
                  // _buildQuickFilters(),
                  // const SizedBox(height: AppDimensions.spacingXL),
                ],
              ),
            ),
          ),

          // Bottom actions
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildQuickDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thời gian',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: [
            _buildQuickDateChip('Hôm nay', () => _setQuickDate(0)),
            _buildQuickDateChip('7 ngày', () => _setQuickDate(7)),
            _buildQuickDateChip('Tháng này', () => _setQuickDate(30)),
            _buildQuickDateChip('3 tháng', () => _setQuickDate(90)),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickDateChip(String label, VoidCallback onTap) {
    // Check if this chip is currently selected
    final isSelected = _selectedQuickDate == label;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.kienlongSkyBlue.withValues(alpha: 0.3)
              : AppColors.kienlongSkyBlue.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected
                ? AppColors.kienlongSkyBlue
                : AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Text(
          label,
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: isSelected
                ? AppColors.kienlongSkyBlue
                : AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
            fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Khoảng thời gian tùy chọn',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        GestureDetector(
          onTap: _selectDateRange,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.calendar,
                  color: AppColors.kienlongOrange,
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    _selectedDateRange == null
                        ? 'Chọn khoảng ngày'
                        : '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: _selectedDateRange == null
                          ? Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6)
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                if (_selectedDateRange != null)
                  GestureDetector(
                    onTap: () => setState(() => _selectedDateRange = null),
                    child: Icon(
                      TablerIcons.x,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                      size: AppDimensions.iconS,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProductSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              TablerIcons.package,
              color: AppColors.success,
              size: AppDimensions.iconS,
            ),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              'Sản phẩm',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: widget.availableProducts.map((product) {
            final isSelected = _selectedProduct?.id == product.id;
            return GestureDetector(
              onTap: () => setState(() {
                final previousProduct = _selectedProduct;
                _selectedProduct = isSelected ? null : product;

                // Nếu product thay đổi, clear selected status và trigger load statuses mới
                if (previousProduct?.id != _selectedProduct?.id) {
                  _selectedStatus = null;
                  widget.onProductChanged(_selectedProduct);
                }
              }),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.kienlongOrange.withValues(alpha: 0.15)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: isSelected
                        ? AppColors.kienlongOrange
                        : Theme.of(
                      context,
                    ).colorScheme.outline.withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Text(
                  product.name,
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? AppColors.kienlongOrange
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStatusSection(MasterDataState state) {
    // Lấy danh sách trạng thái dựa trên sản phẩm được chọn
    final availableStatuses = _getStatusesFromState(state);

    // Filter out "Tất cả" từ API response để tránh duplicate
    final filteredStatuses = availableStatuses.where((status) {
      final label = status.label ?? status.code;
      return label != null &&
          label.isNotEmpty &&
          label.toLowerCase() != 'tất cả';
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              TablerIcons.flag,
              color: AppColors.error,
              size: AppDimensions.iconS,
            ),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              'Trạng thái',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: [
            // Thêm item "Tất cả" như customer filter
            GestureDetector(
              onTap: () => setState(() {
                _selectedStatus = null;
              }),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: _selectedStatus == null
                      ? AppColors.kienlongOrange.withValues(alpha: 0.15)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: _selectedStatus == null
                        ? AppColors.kienlongOrange
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    width: _selectedStatus == null ? 2 : 1,
                  ),
                ),
                child: Text(
                  'Tất cả',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: _selectedStatus == null
                        ? AppColors.kienlongOrange
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight: _selectedStatus == null ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
            // Các status items (đã filter out "Tất cả")
            ...filteredStatuses.map((status) {
              final isSelected = _selectedStatus?.code == status.code;
              final statusLabel = status.label ?? status.code;
              final color = _getStatusColor(status);

              return GestureDetector(
                onTap: () => setState(() {
                  _selectedStatus = isSelected ? null : status;
                }),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                    vertical: AppDimensions.paddingS,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? color.withValues(alpha: 0.15)
                        : Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    border: Border.all(
                      color: isSelected
                          ? color
                          : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Text(
                    statusLabel ?? '',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: isSelected
                          ? color
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ],
    );
  }

  Widget _buildRegionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Khu vực',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Text(
          'Có thể chọn nhiều khu vực',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        GestureDetector(
          onTap: () => _showRegionSelectionModal(),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: _selectedRegions.isNotEmpty
                    ? AppColors.kienlongSkyBlue
                    : Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
                width: _selectedRegions.isNotEmpty ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.map_pin,
                  color: _selectedRegions.isNotEmpty
                      ? AppColors.kienlongSkyBlue
                      : Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: AppDimensions.iconM,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    _selectedRegions.isEmpty
                        ? 'Chọn khu vực'
                        : _selectedRegions.length == 1
                        ? _selectedRegions.first.name
                        : '${_selectedRegions.length} khu vực đã chọn',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: _selectedRegions.isEmpty
                          ? Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6)
                          : AppColors.kienlongSkyBlue,
                      fontWeight: _selectedRegions.isNotEmpty
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
                Icon(
                  TablerIcons.chevron_down,
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.6),
                  size: AppDimensions.iconS,
                ),
              ],
            ),
          ),
        ),
        if (_selectedRegions.isNotEmpty) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Wrap(
            spacing: AppDimensions.spacingXS,
            runSpacing: AppDimensions.spacingXS,
            children: _selectedRegions
                .map(
                  (region) => Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingS,
                  vertical: AppDimensions.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusS,
                  ),
                  border: Border.all(
                    color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      region.name,
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.kienlongSkyBlue,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.spacingXS),
                    GestureDetector(
                      onTap: () => setState(() {
                        _selectedRegions.remove(region);

                        // Remove all branches thuộc region này
                        _selectedBranches.removeWhere(
                              (branch) => branch.regionId == region.id,
                        );

                        // Load branches cho remaining regions hoặc all nếu no regions
                        if (_selectedRegions.isEmpty) {
                          widget.onRegionsChanged?.call([]);
                        } else {
                          widget.onRegionsChanged?.call(_selectedRegions);
                        }
                      }),
                      child: Icon(
                        TablerIcons.x,
                        size: 12,
                        color: AppColors.kienlongSkyBlue,
                      ),
                    ),
                  ],
                ),
              ),
            )
                .toList(),
          ),
        ],
      ],
    );
  }

  void _showRegionSelectionModal() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.3,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
            return RegionMultiSelectModal(
              title: 'Chọn khu vực',
              items: widget.availableRegions,
              selectedItems: _selectedRegions,
              color: AppColors.kienlongSkyBlue,
              icon: TablerIcons.map_pin,
              itemToString: (RegionModel region) => region.name,
              onSelectionChanged: (selectedItems) {
                setState(() {
                  // Track which regions were removed
                  final previousRegionIds = _selectedRegions
                      .map((r) => r.id)
                      .toSet();
                  final newRegionIds = selectedItems.map((r) => r.id).toSet();
                  final removedRegionIds = previousRegionIds.difference(
                    newRegionIds,
                  );

                  _selectedRegions.clear();
                  _selectedRegions.addAll(selectedItems);

                  // Remove branches thuộc các regions đã bị remove
                  if (removedRegionIds.isNotEmpty) {
                    _selectedBranches.removeWhere(
                          (branch) =>
                      branch.regionId != null &&
                          removedRegionIds.contains(branch.regionId),
                    );
                  }
                });

                // Load branches cho selected regions hoặc all nếu no regions
                if (_selectedRegions.isEmpty) {
                  widget.onRegionsChanged?.call([]);
                } else {
                  widget.onRegionsChanged?.call(_selectedRegions);
                }
              },
              scrollController: scrollController,
            );
          },
        );
      },
    );
  }

  void _showBranchSelectionModal() {
    // Merge selected branches với available branches để ensure proper highlighting
    final Set<String> availableBranchIds = _dynamicBranches
        .map((b) => b.id)
        .toSet();
    final List<BranchModel> missingSelectedBranches = _selectedBranches
        .where((selected) => !availableBranchIds.contains(selected.id))
        .toList();

    final List<BranchModel> completeItemsList = [
      ..._dynamicBranches,
      ...missingSelectedBranches,
    ];

    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.3,
          maxChildSize: 0.95,
          builder: (_, scrollController) {
            return BranchMultiSelectModal(
              title: 'Chọn đơn vị kinh doanh',
              items: completeItemsList,
              selectedItems: _selectedBranches,
              color: AppColors.success,
              icon: TablerIcons.building_bank,
              itemToString: (BranchModel branch) => branch.name,
              onSelectionChanged: (selectedItems) {
                setState(() {
                  _selectedBranches.clear();
                  _selectedBranches.addAll(selectedItems);
                });
              },
              scrollController: scrollController,
            );
          },
        );
      },
    );
  }



  Widget _buildBottomActions() {
    return Container(
      padding: EdgeInsets.only(
        left: AppDimensions.paddingL,
        right: AppDimensions.paddingL,
        top: AppDimensions.paddingM,
        bottom:
        MediaQuery.of(context).viewInsets.bottom +
            MediaQuery.of(context).padding.bottom +
            AppDimensions.paddingL,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _selectedDateRange = null;
                  _selectedProduct = null;
                  _selectedStatus = null;
                  _selectedRegions.clear();
                  _selectedBranches.clear();
                  _selectedEmployee = null;
                  _selectedQuickDate = null; // Clear quick date selection
                });
                widget.onApply(null, null, null, null, null, null);
                Navigator.pop(context);
              },
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.paddingM,
                ),
                side: BorderSide(color: AppColors.kienlongOrange),
              ),
              child: Text(
                'Xóa lọc',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: () {
                widget.onApply(
                  _selectedDateRange,
                  _selectedProduct,
                  _selectedStatus,
                  _selectedRegions.isNotEmpty ? _selectedRegions : null,
                  _selectedBranches.isNotEmpty ? _selectedBranches : null,
                  _selectedEmployee,
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                padding: const EdgeInsets.symmetric(
                  vertical: AppDimensions.paddingM,
                ),
              ),
              child: Text(
                'Áp dụng',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _setQuickDate(int days) {
    final now = DateTime.now();
    setState(() {
      if (days == 0) {
        _selectedDateRange = DateTimeRange(
          start: DateTime(now.year, now.month, now.day),
          end: DateTime(now.year, now.month, now.day),
        );
        _selectedQuickDate = 'Hôm nay';
      } else if (days == 7) {
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days)),
          end: now,
        );
        _selectedQuickDate = '7 ngày';
      } else if (days == 30) {
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days)),
          end: now,
        );
        _selectedQuickDate = 'Tháng này';
      } else if (days == 90) {
        _selectedDateRange = DateTimeRange(
          start: now.subtract(Duration(days: days)),
          end: now,
        );
        _selectedQuickDate = '3 tháng';
      }
    });
  }

  void _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(DateTime.now().year - 2),
      lastDate: DateTime(DateTime.now().year + 1),
      initialDateRange: _selectedDateRange,
    );
    if (picked != null) {
      setState(() => _selectedDateRange = picked);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }


  Widget _buildLoadingState() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Loading skeleton for quick date filters
        _buildLoadingSkeleton('Thời gian', 4),
        const SizedBox(height: AppDimensions.spacingL),

        // Loading skeleton for date range
        _buildLoadingSkeleton('Khoảng thời gian tùy chọn', 1),
        const SizedBox(height: AppDimensions.spacingL),

        // Loading skeleton for product filter
        _buildLoadingSkeleton('Sản phẩm', 3),
        const SizedBox(height: AppDimensions.spacingL),

        // Loading skeleton for status filter
        _buildLoadingSkeleton('Trạng thái', 3),
        const SizedBox(height: AppDimensions.spacingL),

        // Loading skeleton for quick filters
        _buildLoadingSkeleton('Lọc nhanh', 3),
      ],
    );
  }

  Widget _buildBranchesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              TablerIcons.building_bank,
              color: AppColors.success,
              size: AppDimensions.iconS,
            ),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              'Đơn vị kinh doanh',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingXS),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingXS,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusXS),
              ),
              child: Text(
                'Chọn nhiều',
                style: AppTypography.textTheme.labelSmall?.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const Spacer(),
            if (_selectedBranches.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingS,
                  vertical: AppDimensions.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Text(
                  '${_selectedBranches.length} đã chọn',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),

        if (widget.branchesLoading)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: AppDimensions.iconS,
                  height: AppDimensions.iconS,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Đang tải đơn vị kinh doanh...',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          )
        else if (_dynamicBranches.isEmpty && !widget.branchesLoading)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Theme.of(
                  context,
                ).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      TablerIcons.info_circle,
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withValues(alpha: 0.6),
                      size: AppDimensions.iconS,
                    ),
                    const SizedBox(width: AppDimensions.spacingS),
                    Text(
                      'Chưa có đơn vị kinh doanh nào',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.spacingS),
                TextButton.icon(
                  onPressed: () {
                    // Retry loading branches
                    widget.onRegionsChanged?.call(_selectedRegions);
                  },
                  icon: const Icon(TablerIcons.refresh, size: 16),
                  label: const Text('Thử lại'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.kienlongSkyBlue,
                  ),
                ),
              ],
            ),
          )
        else
          GestureDetector(
            onTap: () => _showBranchSelectionModal(),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: _selectedBranches.isNotEmpty
                      ? AppColors.success
                      : Theme.of(
                    context,
                  ).colorScheme.outline.withValues(alpha: 0.3),
                  width: _selectedBranches.isNotEmpty ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.building_bank,
                    color: _selectedBranches.isNotEmpty
                        ? AppColors.success
                        : Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.6),
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      _selectedBranches.isEmpty
                          ? 'Chọn đơn vị kinh doanh'
                          : _selectedBranches.length == 1
                          ? _selectedBranches.first.name
                          : '${_selectedBranches.length} đơn vị đã chọn',
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: _selectedBranches.isEmpty
                            ? Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.6)
                            : AppColors.success,
                        fontWeight: _selectedBranches.isNotEmpty
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                  Icon(
                    TablerIcons.chevron_down,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.6),
                    size: AppDimensions.iconS,
                  ),
                ],
              ),
            ),
          ),
        if (_selectedBranches.isNotEmpty) ...[
          const SizedBox(height: AppDimensions.spacingS),
          Wrap(
            spacing: AppDimensions.spacingXS,
            runSpacing: AppDimensions.spacingXS,
            children: _selectedBranches
                .map(
                  (branch) => Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingS,
                  vertical: AppDimensions.paddingXS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusS,
                  ),
                  border: Border.all(
                    color: AppColors.success.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      branch.name,
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.spacingXS),
                    GestureDetector(
                      onTap: () => setState(() {
                        _selectedBranches.remove(branch);
                      }),
                      child: Icon(
                        TablerIcons.x,
                        size: 12,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              ),
            )
                .toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildEmployeeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              TablerIcons.users,
              color: AppColors.kienlongOrange,
              size: AppDimensions.iconS,
            ),
            const SizedBox(width: AppDimensions.spacingXS),
            Text(
              'Cán bộ phụ trách',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Text(
          'Nhấn để chọn cán bộ từ danh sách',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),

        // Sử dụng StaffSearchButton
        StaffSearchButton(
          availableStaff: widget.availableEmployees,
          selectedStaff: _selectedEmployee,
          onStaffSelected: (employee) {
            setState(() {
              _selectedEmployee = employee;
            });
          },
          onSearchChanged: widget.onEmployeeSearch,
          isSearching: widget.isSearchingEmployees,
          employeeSearchStream: widget.employeeSearchStream,
        ),
      ],
    );
  }

  /// Get status color from ConfigModel value field using ColorUtils
  Color _getStatusColor(ConfigModel status) {
    // Use color from API value field if available
    if (status.value != null && status.value!.isNotEmpty) {
      return ColorUtils.hexToColor(status.value!);
    }

    return AppColors.kienlongDarkBlue;
  }

  Widget _buildLoadingSkeleton(String title, int chipCount) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title skeleton
        Container(
          width: 120,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),

        // Chips skeleton
        Wrap(
          spacing: AppDimensions.spacingS,
          runSpacing: AppDimensions.spacingS,
          children: List.generate(chipCount, (index) {
            return Container(
              width: 80 + (index * 20),
              height: 32,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            );
          }),
        ),
      ],
    );
  }
}
