import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class TransactionDetailCustomerLink extends StatelessWidget {
  final Map<String, dynamic> transaction;
  final VoidCallback onCall;
  final VoidCallback onMessage;
  final VoidCallback onViewCustomer;

  const TransactionDetailCustomerLink({
    super.key,
    required this.transaction,
    required this.onCall,
    required this.onMessage,
    required this.onViewCustomer,
  });

  Color _getTagColor(String? tag) {
    switch (tag) {
      case 'VIP':
        return const Color(0xFFFFD700); // Gold
      case 'Premium':
        return AppColors.kienlongSkyBlue;
      case 'Mới':
        return AppColors.kienlongOrange;
      default:
        return AppColors.neutral600;
    }
  }

  @override
  Widget build(BuildContext context) {
    final customerTag = transaction['customerTag'] as String?;
    
    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingM),
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Row(
            children: [
              Icon(
                TablerIcons.user,
                color: AppColors.kienlongSkyBlue,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Khách hàng',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.kienlongSkyBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Customer Info Row
          Row(
            children: [
              // Avatar
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    _getInitials(transaction['customerName']),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.kienlongSkyBlue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              
              SizedBox(width: AppDimensions.spacingM),
              
              // Name and Tag
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      transaction['customerName'],
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (customerTag != null && customerTag.isNotEmpty) ...[
                      SizedBox(height: AppDimensions.spacingXS),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getTagColor(customerTag).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _getTagColor(customerTag),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          customerTag,
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: _getTagColor(customerTag),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // Quick Actions
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Call Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.success.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: onCall,
                      icon: Icon(
                        TablerIcons.phone,
                        color: AppColors.success,
                        size: AppDimensions.iconS,
                      ),
                      tooltip: 'Gọi điện',
                    ),
                  ),
                  
                  SizedBox(width: AppDimensions.spacingS),
                  
                  // Message Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.info.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: onMessage,
                      icon: Icon(
                        TablerIcons.message_circle,
                        color: AppColors.info,
                        size: AppDimensions.iconS,
                      ),
                      tooltip: 'Nhắn tin',
                    ),
                  ),
                  
                  SizedBox(width: AppDimensions.spacingS),
                  
                  // View Detail Button
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: onViewCustomer,
                      icon: Icon(
                        TablerIcons.eye,
                        color: AppColors.kienlongOrange,
                        size: AppDimensions.iconS,
                      ),
                      tooltip: 'Xem chi tiết',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getInitials(String name) {
    final nameParts = name.split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts.first[0]}${nameParts.last[0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : '?';
  }
} 