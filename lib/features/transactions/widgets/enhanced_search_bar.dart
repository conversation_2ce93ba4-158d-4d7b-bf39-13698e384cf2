import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class EnhancedSearchBar extends StatefulWidget {
  final String? searchText;
  final ValueChanged<String> onSearchChanged;
  final VoidCallback onAdvancedFilter;
  final bool hasActiveFilters;

  const EnhancedSearchBar({
    super.key,
    this.searchText,
    required this.onSearchChanged,
    required this.onAdvancedFilter,
    this.hasActiveFilters = false,
  });

  @override
  State<EnhancedSearchBar> createState() => _EnhancedSearchBarState();
}

class _EnhancedSearchBarState extends State<EnhancedSearchBar> {
  late TextEditingController _controller;
  bool _isSearchFocused = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.searchText);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
      child: Column(
        children: [
          // Search input
          TextField(
            controller: _controller,
            onChanged: widget.onSearchChanged,
            onTap: () => setState(() => _isSearchFocused = true),
            onTapOutside: (_) => setState(() => _isSearchFocused = false),
            decoration: InputDecoration(
              hintText: 'Tìm kiếm khách hàng, số tiền, mã GD...',
              hintStyle: AppTypography.textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              prefixIcon: Icon(
                TablerIcons.search,
                color: _isSearchFocused
                    ? AppColors.kienlongOrange
                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(
                        TablerIcons.x,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                      onPressed: () {
                        _controller.clear();
                        widget.onSearchChanged('');
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingM,
                vertical: AppDimensions.paddingM,
              ),
            ),
          ),
      
          const SizedBox(height: AppDimensions.spacingM),
          
          // Advanced filter button
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: widget.onAdvancedFilter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    decoration: BoxDecoration(
                      color: widget.hasActiveFilters
                          ? AppColors.kienlongOrange.withValues(alpha: 0.15)
                          : Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      border: Border.all(
                        color: widget.hasActiveFilters
                            ? AppColors.kienlongOrange
                            : Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                        width: widget.hasActiveFilters ? 2 : 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          TablerIcons.adjustments_horizontal,
                          size: AppDimensions.iconS,
                          color: widget.hasActiveFilters
                              ? AppColors.kienlongOrange
                              : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                        const SizedBox(width: AppDimensions.spacingXS),
                        Text(
                          widget.hasActiveFilters ? 'Đang lọc' : 'Lọc nâng cao',
                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: widget.hasActiveFilters
                                ? AppColors.kienlongOrange
                                : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        if (widget.hasActiveFilters) ...[
                          const SizedBox(width: AppDimensions.spacingXS),
                          Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: AppColors.kienlongOrange,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 