import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/models/base_response.dart';
import '../models/transaction_models.dart';
import '../models/get_proposals_request.dart';

/// Service để quản lý tạo transaction/proposal
class TransactionService {
  static final TransactionService _instance = TransactionService._internal();
  factory TransactionService() => _instance;
  TransactionService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // API endpoints
  static const String _createProposalEndpoint = '/kilobabiz-api/api/v1/proposal';
  static const String _getProposalsEndpoint = '/rest/rpc/get_proposals';

  /// Tạo proposal/transaction mới
  Future<BaseResponse<TransactionProposalData>> createProposal({
    required TransactionProposalRequest request,
  }) async {
    try {
      _logger.i('🚀 Creating new proposal for product: ${request.productId}');
      
      final requestJson = request.toJson();
      _logger.d('📤 Request JSON: ${requestJson.toString()}');

      final response = await _apiService.post(
        _createProposalEndpoint,
        data: requestJson,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Parse response theo format BaseResponse
        final baseResponse = BaseResponse<TransactionProposalData>.fromJson(
          responseData,
          (data) => TransactionProposalData.fromJson(data as Map<String, dynamic>),
        );
        
        _logger.i('✅ Proposal created successfully: ${baseResponse.data?.proposalId}');
        return baseResponse;
      } else {
        _logger.e('❌ Failed to create proposal: ${response.statusCode}');
        throw TransactionException(
          type: TransactionExceptionType.apiError,
          message: 'Failed to create proposal',
          statusCode: response.statusCode,
        );
      }
    } on TransactionException {
      rethrow;
    } catch (e) {
      _logger.e('❌ Error creating proposal: $e');
      throw TransactionException(
        type: TransactionExceptionType.networkError,
        message: 'Network error occurred while creating proposal',
        details: e.toString(),
      );
    }
  }

  /// Validate proposal data trước khi gửi
  bool validateProposalData(TransactionProposalRequest request) {
    try {
      // Validate productId
      if (request.productId == null || request.productId!.isEmpty) {
        throw TransactionException(
          type: TransactionExceptionType.validation,
          message: 'Product ID is required',
        );
      }

      // Validate data
      if (request.data == null) {
        throw TransactionException(
          type: TransactionExceptionType.validation,
          message: 'Transaction data is required',
        );
      }

      // Validate customerId
      if (request.data!.customerId == null || request.data!.customerId!.isEmpty) {
        throw TransactionException(
          type: TransactionExceptionType.validation,
          message: 'Customer ID is required',
        );
      }

      // Validate main borrower
      if (request.data!.mainBorrower != null) {
        _validateBorrowerInfo(request.data!.mainBorrower!, 'Main borrower');
      }

      // Validate co-borrower if exists
      if (request.data!.coBorrower != null) {
        _validateBorrowerInfo(request.data!.coBorrower!, 'Co-borrower');
      }

      // Validate loan plan
      if (request.data!.loanPlan != null) {
        _validateLoanPlan(request.data!.loanPlan!);
      }

      // Validate financial info
      if (request.data!.financialInfo != null) {
        _validateFinancialInfo(request.data!.financialInfo!);
      }

      // Validate collateral info
      if (request.data!.collateralInfo != null) {
        _validateCollateralInfo(request.data!.collateralInfo!);
      }

      _logger.i('✅ Proposal data validation passed');
      return true;
    } on TransactionException {
      rethrow;
    } catch (e) {
      _logger.e('❌ Proposal data validation failed: $e');
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Data validation failed',
        details: e.toString(),
      );
    }
  }

  void _validateBorrowerInfo(BorrowerInfo borrower, String borrowerType) {
    if (borrower.fullName == null || borrower.fullName!.isEmpty) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: '$borrowerType full name is required',
      );
    }
    if (borrower.idNo == null || borrower.idNo!.isEmpty) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: '$borrowerType ID number is required',
      );
    }
    if (borrower.phoneNumber == null || borrower.phoneNumber!.isEmpty) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: '$borrowerType phone number is required',
      );
    }
  }

  void _validateLoanPlan(LoanPlan loanPlan) {
    if (loanPlan.requestedAmount == null || loanPlan.requestedAmount! <= 0) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Requested amount must be greater than 0',
      );
    }
    if (loanPlan.ownCapital != null && loanPlan.ownCapital! < 0) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Own capital cannot be negative',
      );
    }
    if (loanPlan.loanPurposeName == null || loanPlan.loanPurposeName!.isEmpty) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Loan purpose name is required',
      );
    }
  }

  void _validateFinancialInfo(FinancialInfo financialInfo) {
    if (financialInfo.averageRevenuePerDay != null && financialInfo.averageRevenuePerDay! < 0) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Average revenue per day cannot be negative',
      );
    }
    if (financialInfo.averageIncomePerDay != null && financialInfo.averageIncomePerDay! < 0) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Average income per day cannot be negative',
      );
    }
  }

  void _validateCollateralInfo(CollateralInfo collateralInfo) {
    if (collateralInfo.value != null && collateralInfo.value! <= 0) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Asset value must be greater than 0',
      );
    }
    if (collateralInfo.name == null || collateralInfo.name!.isEmpty) {
      throw TransactionException(
        type: TransactionExceptionType.validation,
        message: 'Asset name is required',
      );
    }
  }

  /// Lấy danh sách proposals
  Future<BaseResponse<GetProposalsData>> getProposals({
    GetProposalsRequest? request,
  }) async {
    try {
      _logger.i('🚀 Getting proposals list');

      // Sử dụng default request nếu không có request được cung cấp
      final finalRequest = request ?? GetProposalsRequest();

      final response = await _apiService.post(
        _getProposalsEndpoint,
        data: finalRequest.toJson(),
      );

      if (response.statusCode == 200) {
        final responseData = response.data as Map<String, dynamic>;
        
        // Parse response theo format BaseResponse
        final baseResponse = BaseResponse<GetProposalsData>.fromJson(
          responseData,
          (data) => GetProposalsData.fromJson(data as Map<String, dynamic>),
        );
        
        _logger.i('✅ Proposals list retrieved successfully: ${baseResponse.data?.totalCount} items');
        return baseResponse;
      } else {
        _logger.e('❌ Failed to get proposals: ${response.statusCode}');
        throw TransactionException(
          type: TransactionExceptionType.apiError,
          message: 'Failed to get proposals',
          statusCode: response.statusCode,
        );
      }
    } on TransactionException {
      rethrow;
    } catch (e) {
      _logger.e('❌ Error getting proposals: $e');
      throw TransactionException(
        type: TransactionExceptionType.networkError,
        message: 'Network error occurred while getting proposals',
        details: e.toString(),
      );
    }
  }

}

