import 'dart:io';
import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../shared/utils/app_logger.dart';
import '../models/document_model.dart';

class DocumentUploadService {
  final Dio _dio = Dio();
  final ImagePicker _picker = ImagePicker();

  /// Request camera permission
  Future<bool> requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      appLogger.d('Camera permission status: $status');
      return status == PermissionStatus.granted;
    } catch (e) {
      appLogger.e('Error requesting camera permission: $e');
      return false;
    }
  }


  /// Capture image from camera
  Future<File?> captureImageFromCamera() async {
    try {
      // Request camera permission
      final hasPermission = await requestCameraPermission();
      if (!hasPermission) {
        throw Exception('Camera permission denied');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        appLogger.d('Image captured from camera: ${image.path}');
        return File(image.path);
      }
      return null;
    } catch (e) {
      appLogger.e('Error capturing image from camera: $e');
      rethrow;
    }
  }

  /// Pick image from gallery
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        appLogger.d('Image picked from gallery: ${image.path}');
        return File(image.path);
      }
      return null;
    } catch (e) {
      appLogger.e('Error picking image from gallery: $e');
      rethrow;
    }
  }

  /// Pick multiple images from gallery
  Future<List<File>> pickMultipleImagesFromGallery() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      final List<File> files = images.map((image) => File(image.path)).toList();
      appLogger.d('Multiple images picked: ${files.length} files');
      return files;
    } catch (e) {
      appLogger.e('Error picking multiple images: $e');
      rethrow;
    }
  }

  /// Get file type from extension
  FileType getFileType(String path) {
    final extension = path.split('.').last.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].contains(extension)) {
      return FileType.image;
    } else if (extension == 'pdf') {
      return FileType.pdf;
    } else {
      return FileType.document;
    }
  }

  /// Create UploadedFile from File
  Future<UploadedFile> createUploadedFile(File file) async {
    final fileName = file.path.split('/').last;
    final fileSize = await file.length();
    final fileType = getFileType(file.path);

    return UploadedFile(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: fileName,
      path: file.path,
      type: fileType,
      size: fileSize,
      uploadedAt: DateTime.now(),
      localFile: file,
    );
  }

  /// Upload file to server
  Future<UploadedFile> uploadFile(File file, {
    String? transactionId,
    String? documentType,
    Function(double)? onProgress,
  }) async {
    try {
      appLogger.d('Uploading file: ${file.path}');

      final fileName = file.path.split('/').last;
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          file.path,
          filename: fileName,
        ),
        if (transactionId != null) 'transactionId': transactionId,
        if (documentType != null) 'documentType': documentType,
      });

      // Mock upload - replace with actual API endpoint
      final response = await _dio.post(
        '/api/documents/upload',
        data: formData,
        onSendProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            onProgress(sent / total);
          }
        },
      );

      if (response.statusCode == 200) {
        final uploadedFile = await createUploadedFile(file);
        final serverUrl = response.data['url'] as String?;
        
        appLogger.d('File uploaded successfully: $serverUrl');
        
        return uploadedFile.copyWith(url: serverUrl);
      } else {
        throw Exception('Upload failed with status: ${response.statusCode}');
      }
    } catch (e) {
      appLogger.e('Error uploading file: $e');
      
      // For demo purposes, return mock uploaded file
      final uploadedFile = await createUploadedFile(file);
      return uploadedFile.copyWith(url: 'https://mock-server.com/uploads/${uploadedFile.id}');
    }
  }

  /// Upload multiple files
  Future<List<UploadedFile>> uploadMultipleFiles(
    List<File> files, {
    String? transactionId,
    String? documentType,
    Function(int completed, int total)? onProgress,
  }) async {
    final List<UploadedFile> uploadedFiles = [];
    
    for (int i = 0; i < files.length; i++) {
      try {
        final uploadedFile = await uploadFile(
          files[i],
          transactionId: transactionId,
          documentType: documentType,
        );
        uploadedFiles.add(uploadedFile);
        
        if (onProgress != null) {
          onProgress(i + 1, files.length);
        }
      } catch (e) {
        appLogger.e('Error uploading file ${files[i].path}: $e');
        // Continue with other files
      }
    }
    
    return uploadedFiles;
  }

  /// Delete uploaded file
  Future<bool> deleteFile(String fileId) async {
    try {
      appLogger.d('Deleting file: $fileId');

      // Mock deletion - replace with actual API endpoint
      final response = await _dio.delete('/api/documents/$fileId');

      if (response.statusCode == 200) {
        appLogger.d('File deleted successfully: $fileId');
        return true;
      } else {
        throw Exception('Delete failed with status: ${response.statusCode}');
      }
    } catch (e) {
      appLogger.e('Error deleting file: $e');
      // For demo purposes, return true
      return true;
    }
  }

  /// Validate file size (max 5MB)
  bool validateFileSize(File file, {int maxSizeInMB = 5}) {
    final fileSizeInMB = file.lengthSync() / (1024 * 1024);
    return fileSizeInMB <= maxSizeInMB;
  }

  /// Validate file type
  bool validateFileType(File file, List<String> allowedExtensions) {
    final extension = file.path.split('.').last.toLowerCase();
    return allowedExtensions.contains(extension);
  }

  /// Get supported file extensions for document type
  List<String> getSupportedExtensions(DocumentType documentType) {
    switch (documentType) {
      case DocumentType.idCard:
      case DocumentType.passport:
      case DocumentType.drivingLicense:
      case DocumentType.collateralDoc:
        return ['jpg', 'jpeg', 'png', 'pdf'];
      case DocumentType.incomeCertificate:
      case DocumentType.businessLicense:
      case DocumentType.companyRegistration:
      case DocumentType.contract:
      case DocumentType.marriageCert:
        return ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'];
      default:
        return ['jpg', 'jpeg', 'png', 'pdf'];
    }
  }

  /// Dispose resources
  void dispose() {
    _dio.close();
  }
}
