import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/theme/index.dart';
import '../../../shared/models/index.dart';
import '../../../shared/services/index.dart';

class SettingsSection extends StatefulWidget {
  const SettingsSection({super.key});

  @override
  State<SettingsSection> createState() => _SettingsSectionState();
}

class _SettingsSectionState extends State<SettingsSection> {
  bool _notificationsEnabled = true;
  bool _biometricEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final settings = await SettingsService.instance.getSettings();
    if (mounted) {
      setState(() {
        _notificationsEnabled = settings.notificationsEnabled;
        _biometricEnabled = settings.biometricEnabled;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.settings,
                color: AppColors.kienlongSkyBlue,
                size: AppDimensions.iconM,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Cài đặt',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingL),
          
          // Theme Toggle
          BlocBuilder<ThemeCubit, ThemeState>(
            builder: (context, themeState) {
              return _buildSettingItem(
                context,
                icon: _getThemeIcon(themeState.themeMode),
                title: 'Giao diện',
                subtitle: themeState.themeMode.displayName,
                trailing: GestureDetector(
                  onTap: () => _showThemePicker(context),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          themeState.themeMode.displayName,
                          style: AppTypography.textTheme.labelMedium?.copyWith(
                            color: AppColors.kienlongOrange,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          TablerIcons.chevron_down,
                          size: 14,
                          color: AppColors.kienlongOrange,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          
          const Divider(height: AppDimensions.spacingL),
          
          // Notifications Toggle
          _buildSettingItem(
            context,
            icon: _notificationsEnabled ? TablerIcons.bell : TablerIcons.bell_off,
            title: 'Thông báo',
            subtitle: _notificationsEnabled ? 'Đã bật' : 'Đã tắt',
            trailing: Switch(
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
                _toggleNotifications(value);
              },
              activeColor: AppColors.kienlongOrange,
            ),
          ),
          
          const Divider(height: AppDimensions.spacingL),
          
          // Biometric Toggle
          _buildSettingItem(
            context,
            icon: TablerIcons.fingerprint,
            title: 'Sinh trắc học',
            subtitle: _biometricEnabled ? 'Đã kích hoạt' : 'Chưa kích hoạt',
            trailing: Switch(
              value: _biometricEnabled,
              onChanged: (value) {
                setState(() {
                  _biometricEnabled = value;
                });
                _toggleBiometric(value);
              },
              activeColor: AppColors.kienlongOrange,
            ),
          ),
          
          const Divider(height: AppDimensions.spacingL),
          
          // Language Setting
          _buildSettingItem(
            context,
            icon: TablerIcons.language,
            title: 'Ngôn ngữ',
            subtitle: 'Tiếng Việt',
            trailing: const Icon(
              TablerIcons.chevron_right,
              size: 20,
            ),
            onTap: () {
              // TODO: Show language picker
              _showLanguagePicker(context);
            },
          ),
          
          const Divider(height: AppDimensions.spacingL),
          
          // Cache Setting
          _buildSettingItem(
            context,
            icon: TablerIcons.trash,
            title: 'Xóa dữ liệu cache',
            subtitle: 'Giải phóng bộ nhớ thiết bị',
            trailing: const Icon(
              TablerIcons.chevron_right,
              size: 20,
            ),
            onTap: () {
              _clearCache(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget trailing,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppColors.kienlongSkyBlue,
                size: 20,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            trailing,
          ],
        ),
      ),
    );
  }

  IconData _getThemeIcon(AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.system:
        return TablerIcons.device_desktop;
      case AppThemeMode.light:
        return TablerIcons.sun;
      case AppThemeMode.dark:
        return TablerIcons.moon;
    }
  }

  void _showThemePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Chọn giao diện',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingL),
            ...AppThemeMode.values.map((themeMode) => 
              BlocBuilder<ThemeCubit, ThemeState>(
                builder: (context, themeState) {
                  final isSelected = themeState.themeMode == themeMode;
                  return ListTile(
                    leading: Icon(
                      _getThemeIcon(themeMode),
                      color: isSelected ? AppColors.kienlongOrange : null,
                    ),
                    title: Text(themeMode.displayName),
                    trailing: isSelected 
                        ? Icon(
                            TablerIcons.check,
                            color: AppColors.kienlongOrange,
                          )
                        : null,
                    onTap: () {
                      context.read<ThemeCubit>().changeTheme(themeMode);
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Đã chuyển sang ${themeMode.displayName.toLowerCase()}'),
                          backgroundColor: AppColors.kienlongOrange,
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleNotifications(bool enabled) async {
    setState(() {
      _notificationsEnabled = enabled;
    });
    
    final success = await SettingsService.instance.updateNotifications(enabled);
    
    if (!mounted) return;
    
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            enabled ? 'Đã bật thông báo' : 'Đã tắt thông báo',
          ),
          backgroundColor: AppColors.kienlongSkyBlue,
        ),
      );
    } else {
      // Revert state if save failed
      setState(() {
        _notificationsEnabled = !enabled;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không thể lưu cài đặt'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _toggleBiometric(bool enabled) async {
    setState(() {
      _biometricEnabled = enabled;
    });
    
    final success = await SettingsService.instance.updateBiometric(enabled);
    
    if (!mounted) return;
    
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            enabled ? 'Đã kích hoạt sinh trắc học' : 'Đã tắt sinh trắc học',
          ),
          backgroundColor: AppColors.success,
        ),
      );
    } else {
      // Revert state if save failed
      setState(() {
        _biometricEnabled = !enabled;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không thể lưu cài đặt'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _showLanguagePicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Chọn ngôn ngữ',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingL),
            ListTile(
              leading: const Text('🇻🇳'),
              title: const Text('Tiếng Việt'),
              trailing: const Icon(TablerIcons.check),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Text('🇺🇸'),
              title: const Text('English'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _clearCache(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa dữ liệu cache'),
        content: const Text('Bạn có chắc muốn xóa tất cả dữ liệu cache?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Đã xóa dữ liệu cache'),
                ),
              );
            },
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }
} 