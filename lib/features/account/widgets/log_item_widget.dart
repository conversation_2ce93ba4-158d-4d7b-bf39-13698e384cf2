import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/log_entry.dart';

/// Widget hiển thị từng log item với thiết kế chuyên nghiệp
class LogItemWidget extends StatelessWidget {
  final LogEntry log;
  final int index;
  final VoidCallback onTap;

  const LogItemWidget({
    super.key,
    required this.log,
    required this.index,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AnimatedContainer(
      duration: Duration(milliseconds: 200 + (index * 50)),
      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Container(
        decoration:
            BankingTheme.getTransactionDecoration(
              transactionType: _getTransactionTypeFromLevel(log.level),
            ).copyWith(
              color: isDarkMode
                  ? AppColors.backgroundDarkSecondary
                  : AppColors.backgroundSecondary,
            ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            onTap: onTap,
            child: Padding(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              child: Row(
                children: [
                  // Log level indicator
                  _buildLogLevelIndicator(log.level),
                  SizedBox(width: AppDimensions.spacingM),

                  // Log content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          log.shortMessage,
                          style: AppTypography.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: isDarkMode
                                ? AppColors.textOnPrimary
                                : AppColors.textPrimary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: AppDimensions.spacingXS),
                        Row(
                          children: [
                            Icon(
                              TablerIcons.clock,
                              size: AppDimensions.iconXS,
                              color: isDarkMode
                                  ? AppColors.textSecondary
                                  : AppColors.textTertiary,
                            ),
                            SizedBox(width: AppDimensions.spacingXS),
                            Text(
                              log.formattedTimestamp,
                              style: AppTypography.textTheme.bodySmall
                                  ?.copyWith(
                                    color: isDarkMode
                                        ? AppColors.textSecondary
                                        : AppColors.textTertiary,
                                  ),
                            ),
                            if (log.feature != null) ...[
                              SizedBox(width: AppDimensions.spacingS),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppDimensions.paddingS,
                                  vertical: AppDimensions.paddingXS,
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.kienlongSkyBlue.withValues(
                                    alpha: 0.1,
                                  ),
                                  borderRadius: BorderRadius.circular(
                                    AppDimensions.radiusS,
                                  ),
                                ),
                                child: Text(
                                  log.feature!,
                                  style: AppTypography.textTheme.bodySmall
                                      ?.copyWith(
                                        color: AppColors.kienlongSkyBlue,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Actions
                  Icon(
                    TablerIcons.chevron_right,
                    size: AppDimensions.iconS,
                    color: AppColors.textTertiary,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogLevelIndicator(LogLevel level) {
    final color = _getLevelColor(level);
    final icon = _getLevelIcon(level);

    return Container(
      width: AppDimensions.quickActionButtonSize * 0.8,
      height: AppDimensions.quickActionButtonSize * 0.8,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: AppDimensions.borderWidthThin,
        ),
      ),
      child: Icon(icon, color: color, size: AppDimensions.iconM),
    );
  }

  // Helper methods
  String _getTransactionTypeFromLevel(LogLevel level) {
    switch (level) {
      case LogLevel.error:
        return 'expense';
      case LogLevel.warning:
        return 'pending';
      case LogLevel.info:
      case LogLevel.debug:
      case LogLevel.verbose:
        return 'income';
    }
  }

  Color _getLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return AppColors.neutral500;
      case LogLevel.debug:
        return AppColors.kienlongSkyBlue;
      case LogLevel.info:
        return AppColors.success;
      case LogLevel.warning:
        return AppColors.warning;
      case LogLevel.error:
        return AppColors.error;
    }
  }

  IconData _getLevelIcon(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return TablerIcons.message_dots;
      case LogLevel.debug:
        return TablerIcons.bug;
      case LogLevel.info:
        return TablerIcons.info_circle;
      case LogLevel.warning:
        return TablerIcons.alert_triangle;
      case LogLevel.error:
        return TablerIcons.alert_circle;
    }
  }
}
