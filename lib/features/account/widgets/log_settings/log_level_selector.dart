import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';
import '../../models/log_entry.dart' as log_models;

/// Widget chọn mức độ log tối thiểu
class LogLevelSelector extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const LogLevelSelector({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mức độ log tối thiểu',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),

        DropdownButtonFormField<log_models.LogLevel>(
          value: settings.minimumLevel,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              borderSide: BorderSide(
                color: isDarkMode
                    ? AppColors.borderDark
                    : AppColors.borderLight,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              borderSide: BorderSide(
                color: isDarkMode
                    ? AppColors.borderDark
                    : AppColors.borderLight,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              borderSide: BorderSide(color: AppColors.kienlongOrange, width: 2),
            ),
            filled: true,
            fillColor: isDarkMode
                ? AppColors.backgroundDark
                : AppColors.backgroundPrimary,
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
          ),
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
          items: log_models.LogLevel.values.map((level) {
            return DropdownMenuItem(
              value: level,
              child: Row(
                children: [
                  Icon(
                    _getLevelIcon(level),
                    color: _getLevelColor(level),
                    size: 16,
                  ),
                  SizedBox(width: AppDimensions.spacingS),
                  Text(
                    level.name.toUpperCase(),
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: isDarkMode
                          ? AppColors.textOnPrimary
                          : AppColors.textPrimary,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (level) {
            if (level != null) {
              final newSettings = settings.copyWith(minimumLevel: level);
              onChanged(newSettings);
            }
          },
        ),
      ],
    );
  }

  IconData _getLevelIcon(log_models.LogLevel level) {
    switch (level) {
      case log_models.LogLevel.verbose:
        return TablerIcons.message_circle;
      case log_models.LogLevel.debug:
        return TablerIcons.bug;
      case log_models.LogLevel.info:
        return TablerIcons.info_circle;
      case log_models.LogLevel.warning:
        return TablerIcons.alert_triangle;
      case log_models.LogLevel.error:
        return TablerIcons.alert_circle;
    }
  }

  Color _getLevelColor(log_models.LogLevel level) {
    switch (level) {
      case log_models.LogLevel.verbose:
        return AppColors.textTertiary;
      case log_models.LogLevel.debug:
        return AppColors.kienlongSkyBlue;
      case log_models.LogLevel.info:
        return AppColors.success;
      case log_models.LogLevel.warning:
        return AppColors.warning;
      case log_models.LogLevel.error:
        return AppColors.error;
    }
  }
}
