import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../services/log_management_service.dart';

/// Widget hiển thị thông tin hệ thống logging
class LogSystemInfoWidget extends StatelessWidget {
  const LogSystemInfoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return FutureBuilder<Map<String, dynamic>>(
      future: Future.value(LogManagementService.getSystemStatus()),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return SizedBox.shrink();
        }

        final status = snapshot.data!;

        return Container(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: isDarkMode
                ? AppColors.backgroundDark.withValues(alpha: 0.3)
                : AppColors.backgroundSecondary.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            border: Border.all(
              color: isDarkMode 
                  ? AppColors.borderDark.withValues(alpha: 0.3)
                  : AppColors.borderLight,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionHeader(context, isDarkMode),
              SizedBox(height: AppDimensions.spacingS),
              _buildStatusRow(
                context,
                'Đã khởi tạo',
                status['isInitialized'] ?? false,
              ),
              _buildStatusRow(
                context,
                'Isolate sẵn sàng',
                status['isIsolateInitialized'] ?? false,
              ),
              _buildStatusRow(
                context,
                'Log đang chờ',
                status['pendingLogsCount'] ?? 0,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(BuildContext context, bool isDarkMode) {
    return Row(
      children: [
        Icon(
          TablerIcons.info_circle,
          color: AppColors.kienlongOrange,
          size: 16,
        ),
        SizedBox(width: AppDimensions.spacingS),
        Text(
          'Trạng thái hệ thống',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusRow(BuildContext context, String label, dynamic value) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final isBool = value is bool;
    final isSuccess = isBool ? value : (value is int && value == 0);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: isSuccess ? AppColors.success : AppColors.warning,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: AppDimensions.spacingS),
          Text(
            '$label: ',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode
                  ? AppColors.textSecondary
                  : AppColors.textTertiary,
            ),
          ),
          Text(
            isBool ? (value ? 'Có' : 'Không') : value.toString(),
            style: AppTypography.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: isSuccess ? AppColors.success : AppColors.warning,
            ),
          ),
        ],
      ),
    );
  }
}
