import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';

/// Widget chứa các action buttons cho log settings
class LogSettingsActions extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const LogSettingsActions({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Reset buttons row
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _resetToDefault(),
                icon: Icon(TablerIcons.refresh, size: 16),
                label: Text('Mặc định'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.kienlongOrange,
                  side: BorderSide(color: AppColors.kienlongOrange),
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.paddingS,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _resetToMinimal(),
                icon: Icon(TablerIcons.minus, size: 16),
                label: Text('Tối thiểu'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.kienlongOrange,
                  side: BorderSide(color: AppColors.kienlongOrange),
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.paddingS,
                  ),
                ),
              ),
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _resetToVerbose(),
                icon: Icon(TablerIcons.maximize, size: 16),
                label: Text('Chi tiết'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.kienlongOrange,
                  side: BorderSide(color: AppColors.kienlongOrange),
                  padding: EdgeInsets.symmetric(
                    vertical: AppDimensions.paddingS,
                  ),
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: AppDimensions.spacingS),

        // Info text
        _buildInfoText(context),
      ],
    );
  }

  Widget _buildInfoText(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(TablerIcons.info_circle, color: AppColors.info, size: 16),
          SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Text(
              'Các cài đặt sẽ được áp dụng ngay lập tức',
              style: AppTypography.textTheme.bodySmall?.copyWith(
                color: AppColors.info,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _resetToDefault() {
    final newSettings = LogSettings.defaultSettings();
    onChanged(newSettings);
  }

  void _resetToMinimal() {
    final newSettings = LogSettings.minimal();
    onChanged(newSettings);
  }

  void _resetToVerbose() {
    final newSettings = LogSettings.verbose();
    onChanged(newSettings);
  }
}
