import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';
import 'retention_period_bottom_sheet.dart';
import 'max_entries_selector.dart';

/// Section cài đặt lưu trữ log
class RetentionSettingsSection extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const RetentionSettingsSection({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cài đặt lưu trữ',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),

        // Retention period
        ListTile(
          title: Text(
            'Thời gian lưu trữ',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            '${settings.retentionPeriod.inDays} ngày',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode
                  ? AppColors.textSecondary
                  : AppColors.textTertiary,
            ),
          ),
          trailing: Icon(
            TablerIcons.chevron_right,
            color: isDarkMode
                ? AppColors.textSecondary
                : AppColors.textTertiary,
          ),
          onTap: () => _showRetentionDialog(context),
        ),

        Divider(
          height: 1,
          color: isDarkMode 
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
        ),

        // Max log entries
        MaxEntriesSelector(settings: settings, onChanged: onChanged),

        SizedBox(height: AppDimensions.spacingM),

        Divider(
          height: 1,
          color: isDarkMode 
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
        ),

        SizedBox(height: AppDimensions.spacingS),

        // Auto cleanup
        SwitchListTile(
          title: Text(
            'Tự động dọn dẹp',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            'Xóa log cũ tự động',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode
                  ? AppColors.textSecondary
                  : AppColors.textTertiary,
            ),
          ),
          value: settings.autoCleanup,
          onChanged: (value) {
            final newSettings = settings.copyWith(autoCleanup: value);
            onChanged(newSettings);
          },
          activeColor: AppColors.kienlongOrange,
        ),
      ],
    );
  }

  void _showRetentionDialog(BuildContext context) {
    RetentionPeriodBottomSheet.show(
      context: context,
      settings: settings,
      onChanged: onChanged,
    );
  }
}
