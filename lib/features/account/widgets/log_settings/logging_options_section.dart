import 'package:flutter/material.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';

/// Section tùy chọn ghi log (database, console)
class LoggingOptionsSection extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const LoggingOptionsSection({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tùy chọn ghi log',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),

        // Database logging
        CheckboxListTile(
          title: Text(
            '<PERSON><PERSON>u vào cơ sở dữ liệu',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            'Lưu log vào SQLite database',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode
                  ? AppColors.textSecondary
                  : AppColors.textTertiary,
            ),
          ),
          value: settings.saveToDatabase,
          onChanged: (value) {
            if (value != null) {
              final newSettings = settings.copyWith(saveToDatabase: value);
              onChanged(newSettings);
            }
          },
          activeColor: AppColors.kienlongOrange,
        ),

        Divider(
          height: 1,
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
        ),

        // Console logging
        CheckboxListTile(
          title: Text(
            'Hiển thị trên console',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            'In log ra console (debug mode)',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode
                  ? AppColors.textSecondary
                  : AppColors.textTertiary,
            ),
          ),
          value: settings.saveToConsole,
          onChanged: (value) {
            if (value != null) {
              final newSettings = settings.copyWith(saveToConsole: value);
              onChanged(newSettings);
            }
          },
          activeColor: AppColors.kienlongOrange,
        ),
      ],
    );
  }
}
