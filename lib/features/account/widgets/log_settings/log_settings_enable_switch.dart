import 'package:flutter/material.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';

/// Widget switch để bật/tắt logging
class LogSettingsEnableSwitch extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const LogSettingsEnableSwitch({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SwitchListTile(
      title: Text(
        'Bật logging',
        style: AppTypography.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        '<PERSON><PERSON> lại các hoạt động của <PERSON>ng dụng',
        style: AppTypography.textTheme.bodySmall?.copyWith(
          color: isDarkMode ? AppColors.textSecondary : AppColors.textTertiary,
        ),
      ),
      value: settings.isEnabled,
      onChanged: (value) {
        final newSettings = settings.copyWith(isEnabled: value);
        onChanged(newSettings);
      },
      activeColor: AppColors.kienlongOrange,
    );
  }
}
