import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';

/// Header widget cho cài đặt logging
class LogSettingsHeader extends StatelessWidget {
  final bool isSaving;

  const LogSettingsHeader({super.key, this.isSaving = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongOrange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusM),
          topRight: Radius.circular(AppDimensions.radiusM),
        ),
      ),
      child: Row(
        children: [
          Icon(TablerIcons.settings, color: AppColors.kienlongOrange, size: 20),
          SizedBox(width: AppDimensions.spacingS),
          Expanded(
            child: Text(
              'Cài đặt Logging',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.kienlongOrange,
              ),
            ),
          ),
          if (isSaving)
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppColors.kienlongOrange,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
