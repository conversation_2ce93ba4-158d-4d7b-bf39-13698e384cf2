import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';

/// Widget dropdown chọn số lượng log tối đa
class MaxEntriesSelector extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const MaxEntriesSelector({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Số lượng log tối đa',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),

        DropdownButtonFormField<int>(
          value: _findClosestValue(settings.maxLogEntries),
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: isDarkMode
                    ? AppColors.borderDark
                    : AppColors.borderLight,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(
                color: isDarkMode
                    ? AppColors.borderDark
                    : AppColors.borderLight,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              borderSide: BorderSide(color: AppColors.kienlongOrange, width: 2),
            ),
            filled: true,
            fillColor: isDarkMode
                ? AppColors.backgroundDark
                : AppColors.backgroundPrimary,
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingM,
            ),
            prefixIcon: Container(
              margin: EdgeInsets.all(AppDimensions.paddingS),
              padding: EdgeInsets.all(AppDimensions.paddingXS),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Icon(
                TablerIcons.database,
                color: AppColors.kienlongOrange,
                size: 16,
              ),
            ),
          ),
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
          items: _getMaxEntriesOptions().map((option) {
            return DropdownMenuItem<int>(
              value: option['value'],
              child: _buildDropdownItem(option, isDarkMode),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              final newSettings = settings.copyWith(maxLogEntries: value);
              onChanged(newSettings);
            }
          },
        ),

        SizedBox(height: AppDimensions.spacingS),

        // Info text
        Container(
          padding: EdgeInsets.all(AppDimensions.paddingS),
          decoration: BoxDecoration(
            color: AppColors.info.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            border: Border.all(
              color: AppColors.info.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(TablerIcons.info_circle, color: AppColors.info, size: 14),
                  SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      'Hướng dẫn chọn số lượng log',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: AppColors.info,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppDimensions.spacingXS),
              Text(
                '• 1K-5K: Ứng dụng nhỏ, debug thông thường\n'
                '• 10K: Ứng dụng có nhiều hoạt động\n'
                '• 50K+: Debug chi tiết hoặc môi trường production\n'
                '• Log cũ sẽ tự động xóa khi vượt quá giới hạn',
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: AppColors.info,
                  fontSize: 11,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownItem(Map<String, dynamic> option, bool isDarkMode) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: option['color'],
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: AppDimensions.spacingS),
        Flexible(
          child: Text(
            option['label'],
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (option['recommended'] == true)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              'Khuyên dùng',
              style: AppTypography.textTheme.labelSmall?.copyWith(
                color: AppColors.success,
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  int _findClosestValue(int currentValue) {
    final options = _getMaxEntriesOptions();
    int closest = options.first['value'];
    int minDiff = (currentValue - closest).abs();

    for (final option in options) {
      final diff = (currentValue - option['value'] as int).abs();
      if (diff < minDiff) {
        minDiff = diff;
        closest = option['value'];
      }
    }

    return closest;
  }

  List<Map<String, dynamic>> _getMaxEntriesOptions() {
    return [
      {
        'value': 1000,
        'label': '1,000 entries',
        'description': 'Phù hợp cho ứng dụng nhỏ',
        'color': AppColors.success,
        'recommended': false,
      },
      {
        'value': 5000,
        'label': '5,000 entries',
        'description': 'Cân bằng tốt cho hầu hết ứng dụng',
        'color': AppColors.kienlongOrange,
        'recommended': true,
      },
      {
        'value': 10000,
        'label': '10,000 entries',
        'description': 'Cho ứng dụng có nhiều activity',
        'color': AppColors.warning,
        'recommended': false,
      },
      {
        'value': 50000,
        'label': '50,000 entries',
        'description': 'Để debug chi tiết hoặc production',
        'color': AppColors.error,
        'recommended': false,
      },
      {
        'value': 100000,
        'label': '100,000 entries',
        'description': 'Tối đa - có thể ảnh hưởng hiệu suất',
        'color': AppColors.textTertiary,
        'recommended': false,
      },
    ];
  }
}
