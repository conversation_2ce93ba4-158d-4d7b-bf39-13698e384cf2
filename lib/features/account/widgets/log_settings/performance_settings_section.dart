import 'package:flutter/material.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';

/// Section cài đặt hiệu suất logging
class PerformanceSettingsSection extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const PerformanceSettingsSection({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cài đặt hiệu suất',
          style: AppTypography.textTheme.labelMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
          ),
        ),
        SizedBox(height: AppDimensions.spacingS),

        // Performance monitoring
        SwitchListTile(
          title: Text(
            '<PERSON> dõi hiệu suất',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            'Giám sát hiệu suất logging',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: isDarkMode
                  ? AppColors.textSecondary
                  : AppColors.textTertiary,
            ),
          ),
          value: settings.enablePerformanceMonitoring,
          onChanged: (value) {
            final newSettings = settings.copyWith(
              enablePerformanceMonitoring: value,
            );
            onChanged(newSettings);
          },
          activeColor: AppColors.kienlongOrange,
        ),
      ],
    );
  }
}
