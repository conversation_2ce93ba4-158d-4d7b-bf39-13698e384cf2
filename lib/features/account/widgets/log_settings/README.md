# Log Settings Widget Components

## Tổng quan

Th<PERSON> mục này chứa các widget con được refactor từ `LogSettingsWidget` nhằm tạo ra cấu trúc code modular, dễ bảo trì và chuyên nghiệp hơn.

## Kiến trúc

### Widget Components

- **`LogSettingsHeader`** - Header với title và loading indicator
- **`LogSettingsEnableSwitch`** - Switch bật/tắt logging
- **`LoggingOptionsSection`** - Section tùy chọn logging (database/console)
- **`LogLevelSelector`** - Dropdown chọn log level với icons
- **`RetentionSettingsSection`** - Cài đặt retention và auto cleanup
- **`PerformanceSettingsSection`** - Cài đặt performance monitoring
- **`LogSettingsActions`** - Action buttons (default/minimal/verbose)
- **`LogSystemInfoWidget`** - Hiển thị system status

### UI Components

- **`RetentionPeriodBottomSheet`** - Modern bottom sheet chọn thời gian lưu trữ
- **`MaxEntriesSelector`** - Dropdown selector cho số lượng log tối đa

## Đặc điểm

### ✅ Tính năng chính

- **Modular Architecture**: Mỗi widget có trách nhiệm riêng biệt
- **Theme Support**: Hỗ trợ đầy đủ dark/light theme
- **Responsive Design**: Thiết kế responsive cho nhiều màn hình
- **Error Handling**: Xử lý lỗi với validation và feedback
- **Performance**: Tối ưu hiệu suất với widget con nhỏ gọn

### 🎨 UI/UX

- **Consistent Styling**: Sử dụng AppColors và AppTypography
- **Visual Feedback**: Loading states, success/error messages
- **Interactive Elements**: Hover effects, proper touch targets
- **Accessibility**: Screen reader friendly, proper contrast

### 🔧 Technical

- **State Management**: Proper state lifting và callback patterns
- **Type Safety**: Strong typing với LogSettings model
- **Logging**: Sử dụng appLogger thay vì print
- **Code Reusability**: Components có thể tái sử dụng

## Cách sử dụng

### Import

```dart
import 'log_settings/index.dart';
```

### Basic Usage

```dart
LogSettingsEnableSwitch(
  settings: currentSettings,
  onChanged: (newSettings) {
    // Handle settings change
  },
)
```

### Bottom Sheet Usage

```dart
RetentionPeriodBottomSheet.show(
  context: context,
  settings: settings,
  onChanged: onSettingsChanged,
);
```

### Selector Usage

```dart
MaxEntriesSelector(
  settings: settings,
  onChanged: onSettingsChanged,
)
```

## Best Practices

### 1. State Management
- Luôn lift state lên parent widget
- Sử dụng callback pattern cho communication
- Validate data trước khi save

### 2. Theme Support
- Kiểm tra `Theme.of(context).brightness` cho dark mode
- Sử dụng AppColors constants
- Test trên cả light và dark theme

### 3. Error Handling
- Hiển thị error messages user-friendly
- Log technical errors với appLogger
- Provide fallback UI cho error states

### 4. Performance
- Sử dụng const constructors khi có thể
- Tránh rebuild không cần thiết
- Optimize widget tree depth

## Migration từ code cũ

### Trước (Monolithic)
```dart
// File dài 995 dòng với nhiều method lớn
class LogSettingsWidget extends StatefulWidget {
  // Tất cả logic trong một file
}
```

### Sau (Modular)
```dart
// File chính ngắn gọn, rõ ràng
class LogSettingsWidget extends StatefulWidget {
  // Sử dụng các widget con chuyên biệt
  LogSettingsHeader(),
  LogSettingsEnableSwitch(),
  LoggingOptionsSection(),
  // ...
}
```

## Testing

### Unit Tests
- Test từng widget component riêng biệt
- Mock LogSettings data
- Test theme variations

### Widget Tests
- Test user interactions
- Test dialog flows
- Test validation logic

### Integration Tests
- Test complete settings flow
- Test persistence
- Test error scenarios

## Contributing

### Code Style
- Tuân thủ project coding standards
- Sử dụng meaningful variable names
- Add documentation cho public methods

### Adding New Components
1. Tạo widget file trong thư mục này
2. Export trong `index.dart`
3. Update README
4. Add tests

## Changelog

### v1.1.0 (Current)
- ✅ Modern Bottom Sheet cho retention settings
- ✅ Dropdown selector cho max entries  
- ✅ Professional UI với recommended options
- ✅ Better UX với card-based selection
- ✅ Color-coded options với descriptions

### v1.0.0
- ✅ Refactor từ monolithic sang modular
- ✅ Improved theme support
- ✅ Better error handling
- ✅ Enhanced validation
- ✅ Performance optimizations 