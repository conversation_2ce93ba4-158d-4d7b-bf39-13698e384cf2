import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../models/log_settings.dart';

/// Bottom sheet chọn thời gian lưu trữ log với thiết kế chuyên nghiệp
class RetentionPeriodBottomSheet extends StatelessWidget {
  final LogSettings settings;
  final ValueChanged<LogSettings> onChanged;

  const RetentionPeriodBottomSheet({
    super.key,
    required this.settings,
    required this.onChanged,
  });

  static void show({
    required BuildContext context,
    required LogSettings settings,
    required ValueChanged<LogSettings> onChanged,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          RetentionPeriodBottomSheet(settings: settings, onChanged: onChanged),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusXL),
          topRight: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHandle(),
          _buildHeader(isDarkMode),
          _buildContent(isDarkMode, context),
          SizedBox(
            height:
                MediaQuery.of(context).padding.bottom + AppDimensions.paddingM,
          ),
        ],
      ),
    );
  }

  Widget _buildHandle() {
    return Container(
      margin: EdgeInsets.only(top: AppDimensions.paddingS),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: AppColors.borderLight,
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Widget _buildHeader(bool isDarkMode) {
    return Padding(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              TablerIcons.clock,
              color: AppColors.kienlongOrange,
              size: 20,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Thời gian lưu trữ',
                  style: AppTypography.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDarkMode
                        ? AppColors.textOnPrimary
                        : AppColors.textPrimary,
                  ),
                ),
                Text(
                  'Chọn thời gian lưu trữ log trên thiết bị',
                  style: AppTypography.textTheme.bodySmall?.copyWith(
                    color: isDarkMode
                        ? AppColors.textSecondary
                        : AppColors.textTertiary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

    Widget _buildContent(bool isDarkMode, BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      child: Column(
        children: _getRetentionOptions().map((option) {
          final isSelected = settings.retentionPeriod.inDays == option['days'];
          
          return _buildOptionCard(
            isDarkMode: isDarkMode,
            option: option,
            isSelected: isSelected,
            onTap: () => _selectOption(option['days'], context),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildOptionCard({
    required bool isDarkMode,
    required Map<String, dynamic> option,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppColors.kienlongOrange.withValues(alpha: 0.1)
                  : (isDarkMode
                        ? AppColors.backgroundDark
                        : AppColors.backgroundPrimary),
              border: Border.all(
                color: isSelected
                    ? AppColors.kienlongOrange
                    : (isDarkMode
                          ? AppColors.borderDark
                          : AppColors.borderLight),
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? AppColors.kienlongOrange
                          : AppColors.borderLight,
                      width: 2,
                    ),
                    color: isSelected
                        ? AppColors.kienlongOrange
                        : Colors.transparent,
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          size: 12,
                          color: AppColors.textOnPrimary,
                        )
                      : null,
                ),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        option['label'],
                        style: AppTypography.textTheme.bodyLarge?.copyWith(
                          fontWeight: isSelected
                              ? FontWeight.w600
                              : FontWeight.w500,
                          color: isSelected
                              ? AppColors.kienlongOrange
                              : (isDarkMode
                                    ? AppColors.textOnPrimary
                                    : AppColors.textPrimary),
                        ),
                      ),
                      Text(
                        option['description'],
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: isDarkMode
                              ? AppColors.textSecondary
                              : AppColors.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (option['recommended'] == true)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingS,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.success.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                    child: Text(
                      'Khuyên dùng',
                      style: AppTypography.textTheme.labelSmall?.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _selectOption(int days, BuildContext context) {
    final newSettings = settings.copyWith(
      retentionPeriod: Duration(days: days),
    );
    onChanged(newSettings);
    Navigator.of(context).pop();
  }

  List<Map<String, dynamic>> _getRetentionOptions() {
    return [
      {
        'days': 7,
        'label': '1 tuần',
        'description': 'Lưu trữ log trong 7 ngày - phù hợp cho debug',
        'recommended': false,
      },
      {
        'days': 30,
        'label': '1 tháng',
        'description': 'Lưu trữ log trong 30 ngày - cân bằng tốt',
        'recommended': true,
      },
      {
        'days': 90,
        'label': '3 tháng',
        'description': 'Lưu trữ log trong 90 ngày - theo dõi lâu dài',
        'recommended': false,
      },
      {
        'days': 365,
        'label': '1 năm',
        'description': 'Lưu trữ log trong 365 ngày - tốn nhiều bộ nhớ',
        'recommended': false,
      },
    ];
  }
}
