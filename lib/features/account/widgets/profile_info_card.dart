import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../auth/services/auth_service.dart';
import '../../auth/models/user_profile.dart';

class ProfileInfoCard extends StatelessWidget {
  const ProfileInfoCard({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return StreamBuilder<UserProfile?>(
      stream: AuthService().authStateStream.map((state) => state.user?.profile),
      builder: (context, snapshot) {
        final userProfile = snapshot.data;
        
        return Container(
          padding: const EdgeInsets.all(AppDimensions.paddingL),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDarkMode ? [
                // Dark theme gradient - more muted
                AppColors.kienlongDarkBlue,
                AppColors.neutral800,
              ] : [
                // Light theme gradient - bright
                AppColors.kienlongSkyBlue,
                AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
            boxShadow: [
              BoxShadow(
                color: isDarkMode
                    ? AppColors.shadowDark.withValues(alpha: 0.4)
                    : AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Content
              Column(
                children: [
                  Row(
                    children: [
                      // Avatar section
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(40),
                          border: Border.all(
                            color: Colors.white,
                            width: 3,
                          ),
                          boxShadow: isDarkMode ? [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ] : null,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(37),
                          child: Container(
                            color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                            child: userProfile?.avatar != null
                                ? Image.network(
                                    userProfile!.avatar!,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return Icon(
                                        TablerIcons.user_circle,
                                        size: 50,
                                        color: AppColors.kienlongSkyBlue,
                                      );
                                    },
                                  )
                                : Icon(
                                    TablerIcons.user_circle,
                                    size: 50,
                                    color: AppColors.kienlongSkyBlue,
                                  ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppDimensions.spacingL),
                      
                      // User info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              userProfile?.personFullName ?? userProfile?.displayName ?? 'Chưa có tên',
                              style: AppTypography.textTheme.headlineSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                shadows: isDarkMode ? [
                                  const Shadow(
                                    offset: Offset(0, 1),
                                    blurRadius: 3,
                                    color: Colors.black54,
                                  ),
                                ] : null,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              userProfile?.positionName ?? 'Chưa có chức vụ',
                              style: AppTypography.textTheme.bodyMedium?.copyWith(
                                color: Colors.white.withValues(alpha: isDarkMode ? 0.85 : 0.9),
                                shadows: isDarkMode ? [
                                  const Shadow(
                                    offset: Offset(0, 1),
                                    blurRadius: 2,
                                    color: Colors.black54,
                                  ),
                                ] : null,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              userProfile?.branchName ?? 'Chưa có chi nhánh',
                              style: AppTypography.textTheme.bodySmall?.copyWith(
                                color: Colors.white.withValues(alpha: isDarkMode ? 0.75 : 0.8),
                                shadows: isDarkMode ? [
                                  const Shadow(
                                    offset: Offset(0, 1),
                                    blurRadius: 2,
                                    color: Colors.black54,
                                  ),
                                ] : null,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // Edit button
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: isDarkMode ? Border.all(
                            color: Colors.white.withValues(alpha: 0.2),
                            width: 1,
                          ) : null,
                          boxShadow: isDarkMode ? [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 3,
                              offset: const Offset(0, 1),
                            ),
                          ] : null,
                        ),
                        child: GestureDetector(
                          onTap: () {
                            // TODO: Navigate to edit profile
                          },
                          child: const Icon(
                            TablerIcons.edit,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppDimensions.spacingL),
                  
                  // Contact info - làm trong suốt hơn cho dark mode
                  Container(
                    padding: const EdgeInsets.all(AppDimensions.paddingM),
                    decoration: BoxDecoration(
                      // Làm transparent hơn cho dark mode
                      color: Colors.white.withValues(alpha: isDarkMode ? 0.08 : 0.15),
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      border: isDarkMode ? Border.all(
                        color: Colors.white.withValues(alpha: 0.15),
                        width: 1,
                      ) : null,
                      // Thêm shadow cho dark mode để tạo độ sâu
                      boxShadow: isDarkMode ? [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Column(
                      children: [
                        _buildContactItem(
                          icon: TablerIcons.mail,
                          label: 'Email',
                          value: userProfile?.email ?? 'Chưa có email',
                          isDarkMode: isDarkMode,
                        ),
                        const SizedBox(height: AppDimensions.spacingS),
                        _buildContactItem(
                          icon: TablerIcons.phone,
                          label: 'Điện thoại',
                          value: userProfile?.personPhoneNumber ?? 'Chưa có số điện thoại',
                          isDarkMode: isDarkMode,
                        ),
                        const SizedBox(height: AppDimensions.spacingS),
                        _buildContactItem(
                          icon: TablerIcons.id_badge,
                          label: 'Mã nhân viên',
                          value: userProfile?.personCode ?? 'Chưa có mã nhân viên',
                          isDarkMode: isDarkMode,
                        ),
                        if (userProfile?.personCifNo != null) ...[
                          const SizedBox(height: AppDimensions.spacingS),
                          _buildContactItem(
                            icon: TablerIcons.credit_card,
                            label: 'Mã CIF',
                            value: userProfile!.personCifNo!,
                            isDarkMode: isDarkMode,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String label,
    required String value,
    required bool isDarkMode,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 16,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Text(
          '$label:',
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: isDarkMode ? 0.75 : 0.8),
            shadows: isDarkMode ? [
              const Shadow(
                offset: Offset(0, 1),
                blurRadius: 2,
                color: Colors.black54,
              ),
            ] : null,
          ),
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Text(
            value,
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              shadows: isDarkMode ? [
                const Shadow(
                  offset: Offset(0, 1),
                  blurRadius: 2,
                  color: Colors.black54,
                ),
              ] : null,
            ),
          ),
        ),
      ],
    );
  }
} 