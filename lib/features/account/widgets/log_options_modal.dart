import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

/// Modal hiển thị các tùy chọn thêm với contrast tối ưu cho dark theme
class LogOptionsModal extends StatelessWidget {
  final VoidCallback onExport;
  final VoidCallback onClear;
  final VoidCallback onRefresh;
  final VoidCallback onSettings;

  const LogOptionsModal({
    super.key,
    required this.onExport,
    required this.onClear,
    required this.onRefresh,
    required this.onSettings,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: isDarkMode
            ? Border.all(
                color: AppColors.borderDark.withValues(alpha: 0.3),
                width: 1,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? AppColors.shadowDark.withValues(alpha: 0.8)
                : AppColors.shadowDark.withValues(alpha: 0.2),
            blurRadius: AppDimensions.shadowBlurRadiusL,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context, isDarkMode),
          _buildContent(context, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDarkMode
              ? [
                  AppColors.kienlongOrange.withValues(alpha: 0.2),
                  AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                ]
              : [
                  AppColors.kienlongOrange.withValues(alpha: 0.1),
                  AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusL),
        ),
        border: isDarkMode
            ? Border(
                bottom: BorderSide(
                  color: AppColors.borderDark.withValues(alpha: 0.3),
                  width: 1,
                ),
              )
            : null,
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.kienlongOrange.withValues(alpha: 0.2)
                  : AppColors.kienlongOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: isDarkMode
                  ? Border.all(
                      color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                      width: 1,
                    )
                  : null,
            ),
            child: Icon(
              TablerIcons.menu_2,
              color: AppColors.kienlongOrange,
              size: AppDimensions.iconM,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              'Tùy chọn Log',
              style: AppTypography.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDarkMode
                    ? AppColors.textOnPrimary
                    : AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, bool isDarkMode) {
    return Padding(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        children: [
          _buildOptionTile(
            context,
            isDarkMode: isDarkMode,
            icon: TablerIcons.refresh,
            title: 'Làm mới',
            subtitle: 'Tải lại danh sách logs',
            onTap: onRefresh,
            color: AppColors.kienlongSkyBlue,
          ),
          _buildOptionTile(
            context,
            isDarkMode: isDarkMode,
            icon: TablerIcons.download,
            title: 'Xuất logs',
            subtitle: 'Tải logs về thiết bị',
            onTap: onExport,
            color: AppColors.success,
          ),
          _buildOptionTile(
            context,
            isDarkMode: isDarkMode,
            icon: TablerIcons.settings,
            title: 'Cài đặt',
            subtitle: 'Cấu hình log system',
            onTap: onSettings,
            color: AppColors.neutral600,
          ),
          _buildOptionTile(
            context,
            isDarkMode: isDarkMode,
            icon: TablerIcons.trash,
            title: 'Xóa logs',
            subtitle: 'Xóa tất cả logs hiện tại',
            onTap: onClear,
            color: AppColors.error,
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildOptionTile(
    BuildContext context, {
    required bool isDarkMode,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color color,
    bool isDestructive = false,
  }) {
    final backgroundColor = isDarkMode
        ? color.withValues(alpha: 0.15)
        : color.withValues(alpha: 0.05);

    final borderColor = isDarkMode
        ? color.withValues(alpha: 0.3)
        : color.withValues(alpha: 0.1);

    final iconBackgroundColor = isDarkMode
        ? color.withValues(alpha: 0.25)
        : color.withValues(alpha: 0.1);

    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: borderColor,
          width: AppDimensions.borderWidthThin,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(AppDimensions.paddingS),
                  decoration: BoxDecoration(
                    color: iconBackgroundColor,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    border: isDarkMode
                        ? Border.all(
                            color: color.withValues(alpha: 0.4),
                            width: 1,
                          )
                        : null,
                  ),
                  child: Icon(icon, color: color, size: AppDimensions.iconM),
                ),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTypography.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: isDestructive
                              ? color
                              : (isDarkMode
                                    ? AppColors.textOnPrimary
                                    : AppColors.textPrimary),
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: AppTypography.textTheme.bodySmall?.copyWith(
                          color: isDarkMode
                              ? AppColors.textSecondary
                              : AppColors.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  TablerIcons.chevron_right,
                  color: isDarkMode
                      ? AppColors.textSecondary
                      : AppColors.textTertiary,
                  size: AppDimensions.iconS,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
