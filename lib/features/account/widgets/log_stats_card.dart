import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/log_stats.dart';

/// Widget hiển thị thống kê logs với thiết kế chuyên nghiệp
class LogStatsCard extends StatelessWidget {
  final LogStats stats;

  const LogStatsCard({super.key, required this.stats});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Container(
        decoration:
            BankingTheme.getTransactionDecoration(
              transactionType: 'income', // Sử dụng income color cho stats
            ).copyWith(
              color: isDarkMode
                  ? AppColors.backgroundDarkSecondary
                  : AppColors.backgroundSecondary,
            ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            onTap: () {}, // Stats card không cần action
            child: Padding(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    children: [
                      Icon(
                        TablerIcons.chart_dots,
                        color: AppColors.kienlongOrange,
                        size: AppDimensions.iconM,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Text(
                        'Thống kê Logs',
                        style: AppTypography.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isDarkMode
                              ? AppColors.textOnPrimary
                              : AppColors.textPrimary,
                        ),
                      ),
                      Spacer(),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.paddingS,
                          vertical: AppDimensions.paddingXS,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.kienlongOrange.withValues(
                            alpha: 0.1,
                          ),
                          borderRadius: BorderRadius.circular(
                            AppDimensions.radiusS,
                          ),
                        ),
                        child: Text(
                          stats.formattedDatabaseSize,
                          style: AppTypography.textTheme.bodySmall?.copyWith(
                            color: AppColors.kienlongOrange,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimensions.spacingL),

                  // Stats in 4 rows
                  _buildStatRow(
                    'Tổng logs',
                    stats.totalCount.toString(),
                    TablerIcons.files,
                    AppColors.kienlongSkyBlue,
                    isDarkMode,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  _buildStatRow(
                    'Logs hôm nay',
                    stats.todayCount.toString(),
                    TablerIcons.calendar,
                    AppColors.info,
                    isDarkMode,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  _buildStatRow(
                    'Logs lỗi',
                    stats.errorCount.toString(),
                    TablerIcons.alert_circle,
                    AppColors.error,
                    isDarkMode,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  _buildStatRow(
                    'Logs cảnh báo',
                    stats.warningCount.toString(),
                    TablerIcons.alert_triangle,
                    AppColors.warning,
                    isDarkMode,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(
    String label,
    String value,
    IconData icon,
    Color color,
    bool isDarkMode,
  ) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: AppDimensions.borderWidthThin,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(icon, color: color, size: AppDimensions.iconS),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Text(
              label,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: isDarkMode
                    ? AppColors.textOnPrimary
                    : AppColors.textPrimary,
              ),
            ),
          ),
          Text(
            value,
            style: AppTypography.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
