import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/log_entry.dart';
import '../models/log_filter.dart';

class QuickFilterModal extends StatelessWidget {
  final LogFilter currentFilter;
  final Function(LogFilter) onFilterSelected;

  const QuickFilterModal({
    super.key,
    required this.currentFilter,
    required this.onFilterSelected,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: EdgeInsets.only(top: AppDimensions.paddingS),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.neutral300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: EdgeInsets.all(AppDimensions.paddingL),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(AppDimensions.paddingS),
                    decoration: BoxDecoration(
                      color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusM,
                      ),
                    ),
                    child: Icon(
                      TablerIcons.filter,
                      color: AppColors.kienlongOrange,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Quick Filters',
                          style: AppTypography.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isDarkMode
                                ? AppColors.textOnPrimary
                                : AppColors.textPrimary,
                          ),
                        ),
                        Text(
                          'Lọc nhanh logs theo tiêu chí phổ biến',
                          style: AppTypography.textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Filter options với layout 2 cột
            Padding(
              padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              child: Column(
                children: [
                  // Thời gian section
                  _buildSectionTitle(context, 'Thời gian', isDarkMode),
                  SizedBox(height: AppDimensions.spacingS),
                  _buildTwoColumnGrid(
                    [
                      _FilterOption(
                        icon: TablerIcons.calendar_time,
                        title: 'Hôm nay',
                        filter: LogFilter.today(),
                        isSelected: _isFilterSelected(LogFilter.today()),
                      ),
                      _FilterOption(
                        icon: TablerIcons.calendar_week,
                        title: '7 ngày qua',
                        filter: LogFilter.recent(days: 7),
                        isSelected: _isFilterSelected(
                          LogFilter.recent(days: 7),
                        ),
                      ),
                      _FilterOption(
                        icon: TablerIcons.calendar_month,
                        title: '30 ngày qua',
                        filter: LogFilter.recent(days: 30),
                        isSelected: _isFilterSelected(
                          LogFilter.recent(days: 30),
                        ),
                      ),
                    ],
                    context,
                    isDarkMode,
                  ),

                  SizedBox(height: AppDimensions.spacingL),

                  // Mức độ section
                  _buildSectionTitle(context, 'Mức độ', isDarkMode),
                  SizedBox(height: AppDimensions.spacingS),
                  _buildTwoColumnGrid(
                    [
                      _FilterOption(
                        icon: TablerIcons.alert_circle,
                        title: 'Lỗi',
                        filter: LogFilter.errorsOnly(),
                        isSelected: _isFilterSelected(LogFilter.errorsOnly()),
                      ),
                      _FilterOption(
                        icon: TablerIcons.alert_triangle,
                        title: 'Warning',
                        filter: LogFilter.byLevel(LogLevel.warning),
                        isSelected: _isFilterSelected(
                          LogFilter.byLevel(LogLevel.warning),
                        ),
                      ),
                      _FilterOption(
                        icon: TablerIcons.info_circle,
                        title: 'Info',
                        filter: LogFilter.byLevel(LogLevel.info),
                        isSelected: _isFilterSelected(
                          LogFilter.byLevel(LogLevel.info),
                        ),
                      ),
                      _FilterOption(
                        icon: TablerIcons.bug,
                        title: 'Debug',
                        filter: LogFilter.byLevel(LogLevel.debug),
                        isSelected: _isFilterSelected(
                          LogFilter.byLevel(LogLevel.debug),
                        ),
                      ),
                    ],
                    context,
                    isDarkMode,
                  ),

                  SizedBox(height: AppDimensions.spacingL),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            onFilterSelected(const LogFilter());
                            Navigator.of(context).pop();
                          },
                          icon: Icon(
                            TablerIcons.filter_off,
                            size: AppDimensions.iconS,
                          ),
                          label: Text('Xóa bộ lọc'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.textSecondary,
                            side: BorderSide(color: AppColors.neutral300),
                            padding: EdgeInsets.symmetric(
                              vertical: AppDimensions.paddingM,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: AppDimensions.spacingM),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            onFilterSelected(const LogFilter());
                            Navigator.of(context).pop();
                          },
                          icon: Icon(
                            TablerIcons.list,
                            size: AppDimensions.iconS,
                          ),
                          label: Text('Tất cả logs'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.kienlongOrange,
                            foregroundColor: AppColors.textOnPrimary,
                            padding: EdgeInsets.symmetric(
                              vertical: AppDimensions.paddingM,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: AppDimensions.paddingL),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(
    BuildContext context,
    String title,
    bool isDarkMode,
  ) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: AppTypography.textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildTwoColumnGrid(
    List<_FilterOption> options,
    BuildContext context,
    bool isDarkMode,
  ) {
    List<Widget> rows = [];

    for (int i = 0; i < options.length; i += 2) {
      List<Widget> rowChildren = [];

      // First column
      rowChildren.add(
        Expanded(
          child: _buildCompactFilterOption(context, options[i], isDarkMode),
        ),
      );

      // Second column (if exists)
      if (i + 1 < options.length) {
        rowChildren.add(SizedBox(width: AppDimensions.spacingS));
        rowChildren.add(
          Expanded(
            child: _buildCompactFilterOption(
              context,
              options[i + 1],
              isDarkMode,
            ),
          ),
        );
      } else {
        // Empty space for odd number of items
        rowChildren.add(SizedBox(width: AppDimensions.spacingS));
        rowChildren.add(Expanded(child: SizedBox()));
      }

      rows.add(
        Padding(
          padding: EdgeInsets.only(bottom: AppDimensions.spacingS),
          child: Row(children: rowChildren),
        ),
      );
    }

    return Column(children: rows);
  }

  Widget _buildCompactFilterOption(
    BuildContext context,
    _FilterOption option,
    bool isDarkMode,
  ) {
    return InkWell(
      onTap: () {
        onFilterSelected(option.filter);
        Navigator.of(context).pop();
      },
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: option.isSelected
              ? AppColors.kienlongOrange.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: option.isSelected
                ? AppColors.kienlongOrange
                : AppColors.neutral300.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingXS),
              decoration: BoxDecoration(
                color: option.isSelected
                    ? AppColors.kienlongOrange.withValues(alpha: 0.2)
                    : AppColors.neutral100.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Icon(
                option.icon,
                color: option.isSelected
                    ? AppColors.kienlongOrange
                    : AppColors.textSecondary,
                size: AppDimensions.iconS,
              ),
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: Text(
                option.title,
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  fontWeight: option.isSelected
                      ? FontWeight.w600
                      : FontWeight.w500,
                  color: option.isSelected
                      ? AppColors.kienlongOrange
                      : (isDarkMode
                            ? AppColors.textOnPrimary
                            : AppColors.textPrimary),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (option.isSelected) ...[
              SizedBox(width: AppDimensions.spacingXS),
              Icon(
                TablerIcons.check,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconXS,
              ),
            ],
          ],
        ),
      ),
    );
  }

  bool _isFilterSelected(LogFilter filter) {
    // Improved logic cho filter comparison

    // Check for errorsOnly filter specifically
    if (filter.levels?.contains(LogLevel.error) == true &&
        filter.hasError == true) {
      return currentFilter.levels?.contains(LogLevel.error) == true &&
          currentFilter.hasError == true;
    }

    // Check single level filters (warning, info, debug)
    if (filter.levels != null &&
        filter.levels!.length == 1 &&
        filter.hasError != true) {
      return currentFilter.levels != null &&
          currentFilter.levels!.length == 1 &&
          currentFilter.levels!.first == filter.levels!.first &&
          currentFilter.hasError != true;
    }

    // Check date range filters
    if (filter.startDate != null && filter.endDate != null) {
      if (currentFilter.startDate == null || currentFilter.endDate == null) {
        return false;
      }

      // For today filter
      final filterDate = filter.startDate!;
      final currentDate = currentFilter.startDate!;
      return filterDate.year == currentDate.year &&
          filterDate.month == currentDate.month &&
          filterDate.day == currentDate.day;
    }

    // Fallback comparison
    return currentFilter == filter;
  }
}

class _FilterOption {
  final IconData icon;
  final String title;
  final LogFilter filter;
  final bool isSelected;

  const _FilterOption({
    required this.icon,
    required this.title,
    required this.filter,
    required this.isSelected,
  });
}
