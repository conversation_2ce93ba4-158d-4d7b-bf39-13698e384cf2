import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

/// Dialog xác nhận xóa logs
class LogClearDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const LogClearDialog({super.key, required this.onConfirm});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      title: Row(
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              TablerIcons.alert_triangle,
              color: AppColors.error,
              size: AppDimensions.iconM,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Text(
            'Xóa logs',
            style: AppTypography.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      content: Text(
        'Bạn có chắc chắn muốn xóa tất cả logs?\n\nHành động này không thể hoàn tác.',
        style: AppTypography.textTheme.bodyMedium?.copyWith(height: 1.5),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Hủy', style: TextStyle(color: AppColors.textSecondary)),
        ),
        ElevatedButton(
          onPressed: onConfirm,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            foregroundColor: AppColors.textOnPrimary,
          ),
          child: Text('Xóa', style: TextStyle(fontWeight: FontWeight.w600)),
        ),
      ],
    );
  }
}
