import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/log_entry.dart';

/// Modal hiển thị chi tiết log với thiết kế chuyên nghiệp
class LogDetailModal extends StatelessWidget {
  final LogEntry log;

  const LogDetailModal({super.key, required this.log});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) => Container(
        decoration: BoxDecoration(
          color: isDarkMode
              ? AppColors.backgroundDarkSecondary
              : AppColors.backgroundSecondary,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(AppDimensions.radiusXL),
          ),
          border: isDarkMode
              ? Border.all(
                  color: AppColors.borderDark.withValues(alpha: 0.3),
                  width: 1,
                )
              : null,
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? AppColors.shadowDark.withValues(alpha: 0.8)
                  : AppColors.shadowDark.withValues(alpha: 0.3),
              blurRadius: AppDimensions.shadowBlurRadiusL,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildModalHeader(context, isDarkMode),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: EdgeInsets.all(AppDimensions.paddingL),
                  child: _buildModalContent(context, isDarkMode),
                ),
              ),
              _buildModalActions(context, isDarkMode),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModalHeader(BuildContext context, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDarkMode 
              ? [
                  _getLevelColor(log.level).withValues(alpha: 0.2),
                  _getLevelColor(log.level).withValues(alpha: 0.1),
                ]
              : [
                  _getLevelColor(log.level).withValues(alpha: 0.1),
                  _getLevelColor(log.level).withValues(alpha: 0.05),
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        border: isDarkMode
            ? Border(
                bottom: BorderSide(
                  color: AppColors.borderDark.withValues(alpha: 0.3),
                  width: 1,
                ),
              )
            : null,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppDimensions.radiusXL),
        ),
      ),
      child: Column(
        children: [
          // Drag handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: isDarkMode ? AppColors.neutral600 : AppColors.neutral300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Header content
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? _getLevelColor(log.level).withValues(alpha: 0.2)
                      : _getLevelColor(log.level).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(
                    color: isDarkMode
                        ? _getLevelColor(log.level).withValues(alpha: 0.4)
                        : _getLevelColor(log.level).withValues(alpha: 0.3),
                    width: AppDimensions.borderWidthThin,
                  ),
                ),
                child: Icon(
                  _getLevelIcon(log.level),
                  color: _getLevelColor(log.level),
                  size: AppDimensions.iconL,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? _getLevelColor(log.level).withValues(alpha: 0.2)
                            : _getLevelColor(log.level).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusS,
                        ),
                        border: isDarkMode
                            ? Border.all(
                                color: _getLevelColor(log.level).withValues(alpha: 0.3),
                                width: 1,
                              )
                            : null,
                      ),
                      child: Text(
                        log.levelDisplayName.toUpperCase(),
                        style: AppTypography.textTheme.labelSmall?.copyWith(
                          color: _getLevelColor(log.level),
                          fontWeight: FontWeight.w700,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                    SizedBox(height: AppDimensions.spacingS),
                    Text(
                      log.formattedTimestamp,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: isDarkMode
                            ? AppColors.textOnPrimary
                            : AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(
                  TablerIcons.x,
                  color: isDarkMode
                      ? AppColors.textOnPrimary
                      : AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildModalContent(BuildContext context, bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Message section
        _buildSection(
          context,
          title: 'Thông điệp',
          icon: TablerIcons.message,
          isDarkMode: isDarkMode,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: isDarkMode
                  ? AppColors.backgroundDarkTertiary
                  : AppColors.backgroundTertiary,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: isDarkMode
                    ? AppColors.borderDark
                    : AppColors.borderLight,
                width: AppDimensions.borderWidthThin,
              ),
            ),
            child: Text(
              log.message,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                height: 1.5,
                color: isDarkMode
                    ? AppColors.textOnPrimary
                    : AppColors.textPrimary,
              ),
            ),
          ),
        ),

        SizedBox(height: AppDimensions.spacingL),

        // Metadata section
        if (log.feature != null || log.tag != null)
          _buildSection(
            context,
            title: 'Thông tin',
            icon: TablerIcons.info_circle,
            isDarkMode: isDarkMode,
            child: Column(
              children: [
                if (log.feature != null)
                  _buildMetadataRow(
                    'Tính năng',
                    log.feature!,
                    TablerIcons.package,
                    isDarkMode,
                  ),
                if (log.tag != null)
                  _buildMetadataRow(
                    'Thẻ',
                    log.tag!,
                    TablerIcons.tag,
                    isDarkMode,
                  ),
                _buildMetadataRow('ID', log.id, TablerIcons.hash, isDarkMode),
              ],
            ),
          ),

        if (log.feature != null || log.tag != null)
          SizedBox(height: AppDimensions.spacingL),

        // Error section
        if (log.hasError && log.errorData != null)
          _buildSection(
            context,
            title: 'Dữ liệu lỗi',
            icon: TablerIcons.alert_circle,
            color: AppColors.error,
            isDarkMode: isDarkMode,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.error.withValues(alpha: 0.2),
                  width: AppDimensions.borderWidthThin,
                ),
              ),
              child: Text(
                log.errorData!,
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: AppColors.error,
                  fontFamily: 'monospace',
                  height: 1.4,
                ),
              ),
            ),
          ),

        if (log.hasError && log.errorData != null)
          SizedBox(height: AppDimensions.spacingL),

        // Stack trace section
        if (log.hasError && log.stackTrace != null)
          _buildSection(
            context,
            title: 'Stack Trace',
            icon: TablerIcons.code,
            color: AppColors.error,
            isDarkMode: isDarkMode,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.error.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.error.withValues(alpha: 0.2),
                  width: AppDimensions.borderWidthThin,
                ),
              ),
              child: Text(
                log.stackTrace!,
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: AppColors.error,
                  fontFamily: 'monospace',
                  height: 1.3,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Widget child,
    Color? color,
    bool isDarkMode = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: AppDimensions.iconS,
              color: color ?? AppColors.kienlongOrange,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Text(
              title,
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color:
                    color ??
                    (isDarkMode
                        ? AppColors.textOnPrimary
                        : AppColors.textPrimary),
              ),
            ),
          ],
        ),
        SizedBox(height: AppDimensions.spacingM),
        child,
      ],
    );
  }

  Widget _buildMetadataRow(
    String label,
    String value,
    IconData icon,
    bool isDarkMode,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingS),
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkTertiary
            : AppColors.backgroundTertiary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            size: AppDimensions.iconS,
            color: isDarkMode
                ? AppColors.textOnPrimary
                : AppColors.textSecondary,
          ),
          SizedBox(width: AppDimensions.spacingS),
          Text(
            '$label: ',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textSecondary,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                color: isDarkMode
                    ? AppColors.textOnPrimary
                    : AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModalActions(BuildContext context, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: isDarkMode ? AppColors.borderDark : AppColors.borderLight,
            width: AppDimensions.borderWidthThin,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _shareLog(context),
              icon: Icon(TablerIcons.share, size: AppDimensions.iconS),
              label: Text('Chia sẻ'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.kienlongSkyBlue,
                side: BorderSide(color: AppColors.kienlongSkyBlue),
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              ),
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _copyLog(context),
              icon: Icon(TablerIcons.copy, size: AppDimensions.iconS),
              label: Text('Sao chép'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: AppColors.textOnPrimary,
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _shareLog(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Chia sẻ log: ${log.id}'),
        backgroundColor: AppColors.kienlongSkyBlue,
      ),
    );
  }

  void _copyLog(BuildContext context) {
    Clipboard.setData(ClipboardData(text: log.message));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã sao chép log vào clipboard'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  Color _getLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return AppColors.neutral500;
      case LogLevel.debug:
        return AppColors.kienlongSkyBlue;
      case LogLevel.info:
        return AppColors.success;
      case LogLevel.warning:
        return AppColors.warning;
      case LogLevel.error:
        return AppColors.error;
    }
  }

  IconData _getLevelIcon(LogLevel level) {
    switch (level) {
      case LogLevel.verbose:
        return TablerIcons.message_dots;
      case LogLevel.debug:
        return TablerIcons.bug;
      case LogLevel.info:
        return TablerIcons.info_circle;
      case LogLevel.warning:
        return TablerIcons.alert_triangle;
      case LogLevel.error:
        return TablerIcons.alert_circle;
    }
  }
}
