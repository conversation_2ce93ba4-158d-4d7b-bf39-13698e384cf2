import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/utils/app_logger.dart';
import '../models/log_settings.dart';
import '../services/log_settings_service.dart';
import 'log_settings/index.dart';

/// Widget quản lý cài đặt logging
/// Cho phép user tùy chỉnh các thiết lập logging
class LogSettingsWidget extends StatefulWidget {
  const LogSettingsWidget({super.key});

  @override
  State<LogSettingsWidget> createState() => _LogSettingsWidgetState();
}

class _LogSettingsWidgetState extends State<LogSettingsWidget> {
  LogSettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// Load current settings
  Future<void> _loadSettings() async {
    try {
      setState(() => _isLoading = true);
      final settings = await LogSettingsService.getSettings();
      setState(() {
        _currentSettings = settings;
        _isLoading = false;
      });
    } catch (e) {
      appLogger.e('Error loading log settings: $e');
      setState(() => _isLoading = false);
    }
  }

  /// Save settings
  Future<void> _saveSettings(LogSettings newSettings) async {
    try {
      setState(() => _isSaving = true);
      final success = await LogSettingsService.updateSettings(newSettings);

      if (success) {
        setState(() {
          _currentSettings = newSettings;
          _isSaving = false;
        });

        if (mounted) {
          _showSuccessSnackBar();
        }
      } else {
        setState(() => _isSaving = false);
        if (mounted) {
          _showErrorSnackBar('Lỗi khi lưu cài đặt');
        }
      }
    } catch (e) {
      setState(() => _isSaving = false);
      appLogger.e('Error saving log settings: $e');
      if (mounted) {
        _showErrorSnackBar('Lỗi: $e');
      }
    }
  }

  void _showSuccessSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Cài đặt logging đã được lưu'),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_currentSettings == null) {
      return _buildErrorState();
    }

    return _buildSettingsContent();
  }

  Widget _buildLoadingState() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.kienlongOrange),
          ),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Đang tải cài đặt...',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? AppColors.textOnPrimary
                  : AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        children: [
          Icon(TablerIcons.alert_circle, color: AppColors.error, size: 48),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Không thể tải cài đặt',
            style: AppTypography.textTheme.bodyLarge?.copyWith(
              color: AppColors.error,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          ElevatedButton(
            onPressed: _loadSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: AppColors.textOnPrimary,
            ),
            child: Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsContent() {
    final settings = _currentSettings!;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          LogSettingsHeader(isSaving: _isSaving),

          // Content
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Enable/Disable Logging
                LogSettingsEnableSwitch(
                  settings: settings,
                  onChanged: _saveSettings,
                ),

                if (settings.isEnabled) ...[
                  SizedBox(height: AppDimensions.spacingM),
                  _buildDivider(isDarkMode),
                  SizedBox(height: AppDimensions.spacingM),

                  // Logging Options
                  LoggingOptionsSection(
                    settings: settings,
                    onChanged: _saveSettings,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  _buildDivider(isDarkMode),
                  SizedBox(height: AppDimensions.spacingM),

                  // Log Level
                  LogLevelSelector(
                    settings: settings,
                    onChanged: _saveSettings,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  _buildDivider(isDarkMode),
                  SizedBox(height: AppDimensions.spacingM),

                  // Retention Settings
                  RetentionSettingsSection(
                    settings: settings,
                    onChanged: _saveSettings,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  _buildDivider(isDarkMode),
                  SizedBox(height: AppDimensions.spacingM),

                  // Performance Settings
                  PerformanceSettingsSection(
                    settings: settings,
                    onChanged: _saveSettings,
                  ),
                  SizedBox(height: AppDimensions.spacingM),
                  _buildDivider(isDarkMode),
                  SizedBox(height: AppDimensions.spacingM),
                ],

                // Action Buttons
                LogSettingsActions(
                  settings: settings,
                  onChanged: _saveSettings,
                ),
                SizedBox(height: AppDimensions.spacingM),

                // System Info
                LogSystemInfoWidget(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(bool isDarkMode) {
    return Divider(
      height: 1,
      color: isDarkMode
          ? AppColors.borderDark.withValues(alpha: 0.3)
          : AppColors.borderLight,
    );
  }
}
