import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../screens/log_viewer_screen.dart';

class AccountActionsSection extends StatelessWidget {
  const AccountActionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.user_cog,
                color: AppColors.kienlongSkyBlue,
                size: AppDimensions.iconM,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                'Hành động tài khoản',
                style: AppTypography.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingL),

          // Change Password
          _buildActionItem(
            context,
            icon: TablerIcons.lock,
            title: 'Đổi mật khẩu',
            subtitle: 'Cập nhật mật khẩu đăng nhập',
            color: AppColors.kienlongSkyBlue,
            onTap: () => _changePassword(context),
          ),

          const Divider(height: AppDimensions.spacingL),

          // Security Settings
          _buildActionItem(
            context,
            icon: TablerIcons.shield_check,
            title: 'Bảo mật tài khoản',
            subtitle: 'Cài đặt bảo mật nâng cao',
            color: AppColors.success,
            onTap: () => _securitySettings(context),
          ),

          const Divider(height: AppDimensions.spacingL),

          // Privacy Policy
          _buildActionItem(
            context,
            icon: TablerIcons.file_text,
            title: 'Chính sách bảo mật',
            subtitle: 'Điều khoản sử dụng và bảo mật',
            color: AppColors.kienlongSkyBlue,
            onTap: () => _privacyPolicy(context),
          ),

          const Divider(height: AppDimensions.spacingL),

          // Help & Support
          _buildActionItem(
            context,
            icon: TablerIcons.help_circle,
            title: 'Hỗ trợ khách hàng',
            subtitle: 'Liên hệ tổng đài: 1900 1234',
            color: AppColors.warning,
            onTap: () => _contactSupport(context),
          ),

          const Divider(height: AppDimensions.spacingL),

          // Log Viewer
          _buildActionItem(
            context,
            icon: TablerIcons.file_text,
            title: 'Xem Logs',
            subtitle: 'Xem và quản lý logs ứng dụng',
            color: AppColors.kienlongOrange,
            onTap: () => _viewLogs(context),
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: AppDimensions.spacingS),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Theme.of(
                          context,
                        ).colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                TablerIcons.chevron_right,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.5),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _changePassword(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đổi mật khẩu'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Mật khẩu hiện tại',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Mật khẩu mới',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Xác nhận mật khẩu mới',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Đã cập nhật mật khẩu thành công'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('Cập nhật'),
          ),
        ],
      ),
    );
  }

  void _securitySettings(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Tính năng bảo mật nâng cao sẽ có trong phiên bản tiếp theo',
        ),
        backgroundColor: AppColors.kienlongSkyBlue,
      ),
    );
  }

  void _privacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chính sách bảo mật'),
        content: const SingleChildScrollView(
          child: Text(
            'Chính sách bảo mật KienlongBank...\n\n'
            '1. Chúng tôi cam kết bảo vệ thông tin cá nhân của khách hàng\n'
            '2. Mọi giao dịch được mã hóa end-to-end\n'
            '3. Dữ liệu được lưu trữ an toàn theo chuẩn ISO 27001\n\n'
            'Để xem đầy đủ chính sách, vui lòng truy cập website chính thức.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đã hiểu'),
          ),
        ],
      ),
    );
  }

  void _contactSupport(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Liên hệ hỗ trợ',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingL),
            ListTile(
              leading: const Icon(TablerIcons.phone, color: AppColors.success),
              title: const Text('Tổng đài hỗ trợ'),
              subtitle: const Text('1900 1234 (24/7)'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(
                TablerIcons.mail,
                color: AppColors.kienlongSkyBlue,
              ),
              title: const Text('Email hỗ trợ'),
              subtitle: const Text('<EMAIL>'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(
                TablerIcons.message_circle,
                color: AppColors.warning,
              ),
              title: const Text('Chat trực tuyến'),
              subtitle: const Text('8:00 - 22:00 hàng ngày'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  void _viewLogs(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LogViewerScreen()),
    );
  }
}
