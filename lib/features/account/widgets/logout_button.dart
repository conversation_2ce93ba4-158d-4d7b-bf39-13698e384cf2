import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/services/navigation_service.dart';
import '../../../shared/widgets/index.dart';
import '../../auth/services/auth_service.dart';

class LogoutButton extends StatelessWidget {
  const LogoutButton({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton(
        onPressed: () => _logout(context),
        style: OutlinedButton.styleFrom(
          side: BorderSide(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.3),
            width: 1,
          ),
          foregroundColor: Theme.of(
            context,
          ).colorScheme.onSurface.withValues(alpha: 0.7),
          padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.logout_2,
              size: 18,
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            const SizedBox(width: AppDimensions.spacingS),
            Text(
              'Đăng xuất',
              style: AppTypography.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _logout(BuildContext context) async {
    final result = await showConfirmationDialog(
      context,
      title: 'Đăng xuất',
      content:
          'Bạn có chắc muốn đăng xuất khỏi ứng dụng?\n\nBạn có thể đăng nhập lại bất cứ lúc nào.',
      confirmText: 'Đăng xuất',
      cancelText: 'Hủy',
      icon: TablerIcons.logout_2,
      iconColor: AppColors.error,
      confirmButtonColor: AppColors.error,
      isDestructive: true,
    );

    if (result == true && context.mounted) {
      await _performLogout(context);
    }
  }

  /// Perform logout operation
  Future<void> _performLogout(BuildContext context) async {
    try {
      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 12),
                Text('Đang đăng xuất...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Perform logout (only clears tokens, keeps saved credentials)
      final authService = AuthService();
      await authService.logout();

      // Navigate to login screen with clear stack
      await NavigationService().navigateToLogin(clearStack: true, force: true);

      // Show success message if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã đăng xuất thành công'),
            backgroundColor: AppColors.success,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Show error message if logout fails
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đăng xuất: $e'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
