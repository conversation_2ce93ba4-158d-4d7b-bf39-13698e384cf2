import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

/// Widget hiển thị các action cho logs
class LogActionsWidget extends StatelessWidget {
  final VoidCallback? onExport;
  final VoidCallback? onClear;
  final VoidCallback? onRefresh;
  final VoidCallback? onSettings;

  const LogActionsWidget({
    super.key,
    this.onExport,
    this.onClear,
    this.onRefresh,
    this.onSettings,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Icon(TablerIcons.dots_vertical, color: AppColors.kienlongOrange),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Tùy chọn Logs',
                style: AppTypography.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.kienlongOrange,
                ),
              ),
              Spacer(),
              IconButton(
                icon: Icon(TablerIcons.x),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Actions
          _buildActionTile(
            context,
            icon: TablerIcons.download,
            title: 'Xuất logs',
            subtitle: 'Tải xuống logs dưới dạng file',
            onTap: onExport,
          ),

          _buildActionTile(
            context,
            icon: TablerIcons.refresh,
            title: 'Làm mới',
            subtitle: 'Tải lại danh sách logs',
            onTap: onRefresh,
          ),

          _buildActionTile(
            context,
            icon: TablerIcons.settings,
            title: 'Cài đặt',
            subtitle: 'Cấu hình logging system',
            onTap: onSettings,
          ),

          Divider(),

          _buildActionTile(
            context,
            icon: TablerIcons.trash,
            title: 'Xóa tất cả logs',
            subtitle: 'Xóa toàn bộ logs (không thể hoàn tác)',
            onTap: onClear,
            isDestructive: true,
          ),

          SizedBox(height: AppDimensions.spacingL),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    bool isDestructive = false,
  }) {
    final color = isDestructive ? Colors.red : AppColors.kienlongOrange;

    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: color, size: 20),
      ),
      title: Text(
        title,
        style: AppTypography.textTheme.bodyMedium?.copyWith(
          fontWeight: FontWeight.w500,
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTypography.textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Icon(
        TablerIcons.chevron_right,
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }
}
