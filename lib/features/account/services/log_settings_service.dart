import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/log_settings.dart';
import '../models/log_entry.dart';

/// Service để quản lý log settings
/// Lưu trữ và quản lý user preferences cho logging system
class LogSettingsService {
  static const String _settingsKey = 'log_settings';
  static LogSettings? _cachedSettings;

  /// Get current log settings
  static Future<LogSettings> getSettings() async {
    if (_cachedSettings != null) return _cachedSettings!;

    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_settingsKey);

      if (settingsJson != null) {
        _cachedSettings = LogSettings.fromJson(settingsJson);
      } else {
        _cachedSettings = LogSettings.defaultSettings();
      }

      if (kDebugMode) {
        debugPrint('Loaded log settings: ${_cachedSettings!.summary}');
      }

      return _cachedSettings!;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error loading log settings: $e');
      }
      return LogSettings.defaultSettings();
    }
  }

  /// Update log settings
  static Future<bool> updateSettings(LogSettings settings) async {
    try {
      // Validate settings before saving
      final errors = settings.validate();
      if (errors.isNotEmpty) {
        if (kDebugMode) {
          debugPrint('Invalid log settings: ${errors.join(', ')}');
        }
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final settingsJson = settings.toJson();

      final success = await prefs.setString(_settingsKey, settingsJson);

      if (success) {
        _cachedSettings = settings;

        if (kDebugMode) {
          debugPrint('Updated log settings: ${settings.summary}');
        }

        // Apply settings immediately
        await _applySettings(settings);
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error saving log settings: $e');
      }
      return false;
    }
  }

  /// Reset settings to default
  static Future<bool> resetToDefault() async {
    return await updateSettings(LogSettings.defaultSettings());
  }

  /// Reset settings to minimal (for performance)
  static Future<bool> resetToMinimal() async {
    return await updateSettings(LogSettings.minimal());
  }

  /// Reset settings to verbose (for debugging)
  static Future<bool> resetToVerbose() async {
    return await updateSettings(LogSettings.verbose());
  }

  /// Enable/disable logging
  static Future<bool> setLoggingEnabled(bool enabled) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(isEnabled: enabled);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating logging enabled: $e');
      }
      return false;
    }
  }

  /// Enable/disable database logging
  static Future<bool> setDatabaseLoggingEnabled(bool enabled) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(saveToDatabase: enabled);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating database logging: $e');
      }
      return false;
    }
  }

  /// Enable/disable console logging
  static Future<bool> setConsoleLoggingEnabled(bool enabled) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(saveToConsole: enabled);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating console logging: $e');
      }
      return false;
    }
  }

  /// Set minimum log level
  static Future<bool> setMinimumLevel(LogLevel level) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(minimumLevel: level);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating minimum level: $e');
      }
      return false;
    }
  }

  /// Set retention period
  static Future<bool> setRetentionPeriod(Duration period) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(retentionPeriod: period);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating retention period: $e');
      }
      return false;
    }
  }

  /// Set max log entries
  static Future<bool> setMaxLogEntries(int maxEntries) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(maxLogEntries: maxEntries);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating max log entries: $e');
      }
      return false;
    }
  }

  /// Enable/disable auto cleanup
  static Future<bool> setAutoCleanupEnabled(bool enabled) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(autoCleanup: enabled);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating auto cleanup: $e');
      }
      return false;
    }
  }

  /// Set cleanup interval
  static Future<bool> setCleanupInterval(Duration interval) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(cleanupInterval: interval);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating cleanup interval: $e');
      }
      return false;
    }
  }

  /// Enable/disable sensitive data masking
  static Future<bool> setMaskSensitiveData(bool enabled) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(maskSensitiveData: enabled);
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating sensitive data masking: $e');
      }
      return false;
    }
  }

  /// Enable/disable performance monitoring
  static Future<bool> setPerformanceMonitoringEnabled(bool enabled) async {
    try {
      final currentSettings = await getSettings();
      final newSettings = currentSettings.copyWith(
        enablePerformanceMonitoring: enabled,
      );
      return await updateSettings(newSettings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error updating performance monitoring: $e');
      }
      return false;
    }
  }

  /// Get settings summary
  static Future<String> getSettingsSummary() async {
    final settings = await getSettings();
    return settings.summary;
  }

  /// Check if logging is enabled
  static Future<bool> isLoggingEnabled() async {
    final settings = await getSettings();
    return settings.isEnabled;
  }

  /// Check if database logging is enabled
  static Future<bool> isDatabaseLoggingEnabled() async {
    final settings = await getSettings();
    return settings.isDatabaseLoggingEnabled;
  }

  /// Check if console logging is enabled
  static Future<bool> isConsoleLoggingEnabled() async {
    final settings = await getSettings();
    return settings.isConsoleLoggingEnabled;
  }

  /// Get minimum log level
  static Future<LogLevel> getMinimumLevel() async {
    final settings = await getSettings();
    return settings.minimumLevel;
  }

  /// Get retention period
  static Future<Duration> getRetentionPeriod() async {
    final settings = await getSettings();
    return settings.retentionPeriod;
  }

  /// Get max log entries
  static Future<int> getMaxLogEntries() async {
    final settings = await getSettings();
    return settings.maxLogEntries;
  }

  /// Check if auto cleanup is enabled
  static Future<bool> isAutoCleanupEnabled() async {
    final settings = await getSettings();
    return settings.autoCleanup;
  }

  /// Get cleanup interval
  static Future<Duration> getCleanupInterval() async {
    final settings = await getSettings();
    return settings.cleanupInterval;
  }

  /// Check if sensitive data masking is enabled
  static Future<bool> isMaskSensitiveDataEnabled() async {
    final settings = await getSettings();
    return settings.maskSensitiveData;
  }

  /// Check if performance monitoring is enabled
  static Future<bool> isPerformanceMonitoringEnabled() async {
    final settings = await getSettings();
    return settings.isPerformanceMonitoringEnabled;
  }

  /// Clear cached settings
  static void clearCache() {
    _cachedSettings = null;
  }

  /// Export settings to JSON
  static Future<String> exportSettings() async {
    final settings = await getSettings();
    return settings.toJson();
  }

  /// Import settings from JSON
  static Future<bool> importSettings(String json) async {
    try {
      final settings = LogSettings.fromJson(json);
      return await updateSettings(settings);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error importing log settings: $e');
      }
      return false;
    }
  }

  /// Apply settings to logging system
  static Future<void> _applySettings(LogSettings settings) async {
    try {
      // Apply settings to AppLogger
      // This will be implemented when we enhance AppLogger

      if (kDebugMode) {
        debugPrint('Applied log settings: ${settings.summary}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error applying log settings: $e');
      }
    }
  }

  /// Get settings statistics
  static Future<Map<String, dynamic>> getSettingsStats() async {
    try {
      final settings = await getSettings();

      return {
        'settings': settings.toMap(),
        'lastUpdated': DateTime.now().toIso8601String(),
        'storageSize': settings.toJson().length,
        'isValid': settings.isValid,
        'validationErrors': settings.validate(),
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting settings stats: $e');
      }
      return {'error': e.toString()};
    }
  }
}
