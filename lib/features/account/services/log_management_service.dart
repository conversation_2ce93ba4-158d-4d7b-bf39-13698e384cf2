import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:convert';
import '../models/log_entry.dart';
import '../models/log_filter.dart';
import '../models/log_stats.dart';
import '../repositories/isolate_log_repository.dart';
import '../services/log_writer_isolate.dart';
import '../../../shared/database/log_database_helper.dart';

/// Service quản lý tổng thể logging system
/// Điều phối giữa các components khác nhau của logging system
class LogManagementService {
  static bool _isInitialized = false;
  static Timer? _cleanupTimer;
  static Timer? _statsTimer;

  /// Initialize logging system
  static Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      if (kDebugMode) {
        debugPrint('🔄 Initializing logging system...');
      }

      // Initialize log database
      await LogDatabaseHelper.instance.database;

      // Try to initialize log writer isolate, but don't fail if it doesn't work
      try {
        await LogWriterIsolate.initialize();
        if (kDebugMode) {
          debugPrint('✅ Log writer isolate initialized successfully');
        }
      } catch (isolateError) {
        if (kDebugMode) {
          debugPrint(
            '⚠️ Log writer isolate failed to initialize: $isolateError',
          );
          debugPrint('⚠️ Will use fallback synchronous logging');
        }
        // Continue without isolate - we'll use fallback
      }

      // Start background tasks
      _startBackgroundTasks();

      _isInitialized = true;

      if (kDebugMode) {
        debugPrint('✅ Logging system initialized successfully');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error initializing logging system: $e');
      }
      return false;
    }
  }

  /// Write log entry
  static Future<void> writeLog(LogEntry logEntry) async {
    if (kDebugMode) {
      debugPrint('📝 Attempting to write log: ${logEntry.id}');
    }

    if (!_isInitialized) {
      if (kDebugMode) {
        debugPrint('⚠️ Logging system not initialized, using fallback');
      }
      return;
    }

    try {
      // Try to write via isolate first
      if (LogWriterIsolate.isInitialized) {
        await LogWriterIsolate.writeLog(logEntry);
      } else {
        final repository = IsolateLogRepository();
        await repository.insertLog(logEntry);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error writing log: $e');
      }
    }
  }

  /// Write multiple log entries
  static Future<void> writeLogs(List<LogEntry> logEntries) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        debugPrint('⚠️ Logging system not initialized, using fallback');
      }
      return;
    }

    try {
      await LogWriterIsolate.writeLogs(logEntries);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error writing logs: $e');
      }
    }
  }

  /// Get logs with filter
  static Future<List<LogEntry>> getLogs(LogFilter filter) async {
    try {
      final repository = IsolateLogRepository();
      return await repository.getLogs(filter);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting logs: $e');
      }
      return [];
    }
  }

  /// Get log statistics
  static Future<LogStats> getLogStats() async {
    try {
      final repository = IsolateLogRepository();
      return await repository.getLogStats();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting log stats: $e');
      }
      return LogStats.empty();
    }
  }

  /// Search logs
  static Future<List<LogEntry>> searchLogs(String query) async {
    try {
      final repository = IsolateLogRepository();
      return await repository.searchLogs(query);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error searching logs: $e');
      }
      return [];
    }
  }

  /// Clear logs
  static Future<int> clearLogs(ClearLogsOptions options) async {
    try {
      final repository = IsolateLogRepository();
      final clearedCount = await repository.clearLogs(options);

      if (kDebugMode) {
        debugPrint('Cleared $clearedCount logs');
      }

      return clearedCount;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error clearing logs: $e');
      }
      return 0;
    }
  }

  /// Clear all logs
  static Future<int> clearAllLogs() async {
    return await clearLogs(const ClearLogsOptions());
  }

  /// Clear old logs (older than specified days)
  static Future<int> clearOldLogs(int daysOld) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
    return await clearLogs(ClearLogsOptions(beforeDate: cutoffDate));
  }

  /// Clear logs by level
  static Future<int> clearLogsByLevel(List<LogLevel> levels) async {
    return await clearLogs(ClearLogsOptions(levels: levels));
  }

  /// Clear logs by feature
  static Future<int> clearLogsByFeature(List<String> features) async {
    return await clearLogs(ClearLogsOptions(features: features));
  }

  /// Get unique tags
  static Future<List<String>> getUniqueTags() async {
    try {
      final repository = IsolateLogRepository();
      return await repository.getUniqueTags();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting unique tags: $e');
      }
      return [];
    }
  }

  /// Get unique features
  static Future<List<String>> getUniqueFeatures() async {
    try {
      final repository = IsolateLogRepository();
      return await repository.getUniqueFeatures();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting unique features: $e');
      }
      return [];
    }
  }

  /// Get logs count
  static Future<int> getLogsCount(LogFilter? filter) async {
    try {
      final repository = IsolateLogRepository();
      return await repository.getLogsCount(filter);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting logs count: $e');
      }
      return 0;
    }
  }

  /// Force flush pending logs
  static Future<void> forceFlush() async {
    if (!_isInitialized) return;

    try {
      await LogWriterIsolate.forceFlush();

      if (kDebugMode) {
        debugPrint('Forced flush of pending logs');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error forcing flush: $e');
      }
    }
  }

  /// Get pending logs count
  static int getPendingLogsCount() {
    return LogWriterIsolate.pendingLogsCount;
  }

  /// Check if system is initialized
  static bool get isInitialized => _isInitialized;

  /// Check if isolate is initialized
  static bool get isIsolateInitialized => LogWriterIsolate.isInitialized;

  /// Get system status
  static Map<String, dynamic> getSystemStatus() {
    return {
      'isInitialized': _isInitialized,
      'isIsolateInitialized': LogWriterIsolate.isInitialized,
      'pendingLogsCount': LogWriterIsolate.pendingLogsCount,
      'cleanupTimerActive': _cleanupTimer?.isActive ?? false,
      'statsTimerActive': _statsTimer?.isActive ?? false,
    };
  }

  /// Start background tasks
  static void _startBackgroundTasks() {
    // Start cleanup timer (run every hour)
    _cleanupTimer = Timer.periodic(const Duration(hours: 1), (timer) {
      _performCleanup();
    });

    // Start stats collection timer (run every 5 minutes)
    _statsTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _collectStats();
    });

    if (kDebugMode) {
      debugPrint('Started background tasks');
    }
  }

  /// Perform automatic cleanup
  static Future<void> _performCleanup() async {
    try {
      // Get current settings to determine cleanup strategy
      // For now, use default cleanup (logs older than 30 days)
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final clearedCount = await clearLogs(
        ClearLogsOptions(beforeDate: cutoffDate),
      );

      if (clearedCount > 0 && kDebugMode) {
        debugPrint('Auto-cleanup: cleared $clearedCount old logs');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error during auto-cleanup: $e');
      }
    }
  }

  /// Collect system statistics
  static Future<void> _collectStats() async {
    try {
      final stats = await getLogStats();

      if (kDebugMode && stats.totalCount > 0) {
        debugPrint(
          'Log stats: ${stats.totalCount} total, ${stats.todayCount} today, ${stats.errorCount} errors',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error collecting stats: $e');
      }
    }
  }

  /// Shutdown logging system
  static Future<void> shutdown() async {
    try {
      if (kDebugMode) {
        debugPrint('🔄 Shutting down logging system...');
      }

      // Stop background timers
      _cleanupTimer?.cancel();
      _statsTimer?.cancel();

      // Force flush pending logs
      await forceFlush();

      // Close log writer isolate
      await LogWriterIsolate.close();

      // Close database
      await LogDatabaseHelper.instance.close();

      _isInitialized = false;

      if (kDebugMode) {
        debugPrint('✅ Logging system shut down successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error shutting down logging system: $e');
      }
    }
  }

  /// Export logs to JSON
  static Future<String> exportLogs(LogFilter filter) async {
    try {
      final logs = await getLogs(filter);
      final exportData = {
        'exportedAt': DateTime.now().toIso8601String(),
        'filter': filter.toMap(),
        'totalCount': logs.length,
        'logs': logs.map((log) => log.toMap()).toList(),
      };

      return jsonEncode(exportData);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error exporting logs: $e');
      }
      rethrow;
    }
  }

  /// Get database info
  static Future<Map<String, dynamic>> getDatabaseInfo() async {
    try {
      final info = await LogDatabaseHelper.instance.getDatabaseInfo();
      final size = await LogDatabaseHelper.instance.getDatabaseSize();
      final integrity = await LogDatabaseHelper.instance.checkIntegrity();

      return {...info, 'size': size, 'integrity': integrity};
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting database info: $e');
      }
      return {'error': e.toString()};
    }
  }

  /// Perform database maintenance
  static Future<bool> performMaintenance() async {
    try {
      await LogDatabaseHelper.instance.performMaintenance();

      if (kDebugMode) {
        debugPrint('Database maintenance completed');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error performing database maintenance: $e');
      }
      return false;
    }
  }
}
