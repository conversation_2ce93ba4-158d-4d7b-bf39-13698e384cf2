import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:collection';
import 'log_settings_service.dart';

/// Performance monitor cho logging system
/// Theo dõi hiệu suất và tối ưu hóa logging operations
class LogPerformanceMonitor {
  static final LogPerformanceMonitor _instance =
      LogPerformanceMonitor._internal();
  factory LogPerformanceMonitor() => _instance;
  LogPerformanceMonitor._internal();

  final Map<String, Duration> _operationTimes = {};
  final Queue<PerformanceMetric> _metrics = Queue<PerformanceMetric>();
  final int _maxMetrics = 1000;

  Timer? _cleanupTimer;
  bool _isEnabled = false;

  /// Initialize performance monitoring
  Future<void> initialize() async {
    final settings = await LogSettingsService.getSettings();
    _isEnabled = settings.isPerformanceMonitoringEnabled;

    if (_isEnabled) {
      _startCleanupTimer();

      if (kDebugMode) {
        debugPrint('Log performance monitoring initialized');
      }
    }
  }

  /// Start monitoring an operation
  void startOperation(String operationName) {
    if (!_isEnabled) return;

    _operationTimes[operationName] = DateTime.now().difference(DateTime(1970));
  }

  /// End monitoring an operation
  void endOperation(String operationName) {
    if (!_isEnabled) return;

    final startTime = _operationTimes.remove(operationName);
    if (startTime != null) {
      final duration = DateTime.now().difference(DateTime(1970)) - startTime;
      _addMetric(operationName, duration);
    }
  }

  /// Monitor async operation
  Future<T> monitorAsyncOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    if (!_isEnabled) return await operation();

    startOperation(operationName);
    try {
      final result = await operation();
      endOperation(operationName);
      return result;
    } catch (e) {
      endOperation(operationName);
      rethrow;
    }
  }

  /// Monitor sync operation
  T monitorSyncOperation<T>(String operationName, T Function() operation) {
    if (!_isEnabled) return operation();

    startOperation(operationName);
    try {
      final result = operation();
      endOperation(operationName);
      return result;
    } catch (e) {
      endOperation(operationName);
      rethrow;
    }
  }

  /// Add performance metric
  void _addMetric(String operationName, Duration duration) {
    final metric = PerformanceMetric(
      operationName: operationName,
      duration: duration,
      timestamp: DateTime.now(),
    );

    _metrics.add(metric);

    // Remove old metrics if queue is too large
    if (_metrics.length > _maxMetrics) {
      _metrics.removeFirst();
    }

    // Log slow operations
    if (duration.inMilliseconds > 1000) {
      if (kDebugMode) {
        debugPrint(
          '⚠️ Slow operation detected: $operationName took ${duration.inMilliseconds}ms',
        );
      }
    }
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    if (!_isEnabled || _metrics.isEmpty) {
      return {
        'enabled': _isEnabled,
        'total_operations': 0,
        'average_duration_ms': 0,
        'slow_operations': 0,
        'operation_breakdown': {},
      };
    }

    final operationStats = <String, List<Duration>>{};

    for (final metric in _metrics) {
      operationStats
          .putIfAbsent(metric.operationName, () => [])
          .add(metric.duration);
    }

    final breakdown = <String, Map<String, dynamic>>{};
    int totalSlowOperations = 0;
    int totalOperations = _metrics.length;
    int totalDurationMs = 0;

    for (final entry in operationStats.entries) {
      final operationName = entry.key;
      final durations = entry.value;

      final avgDuration =
          durations.fold<Duration>(
            Duration.zero,
            (sum, duration) => sum + duration,
          ) ~/
          durations.length;

      final slowCount = durations.where((d) => d.inMilliseconds > 1000).length;
      totalSlowOperations += slowCount;
      totalDurationMs += durations.fold<int>(
        0,
        (sum, d) => sum + d.inMilliseconds,
      );

      breakdown[operationName] = {
        'count': durations.length,
        'average_duration_ms': avgDuration.inMilliseconds,
        'min_duration_ms': durations
            .map((d) => d.inMilliseconds)
            .reduce((a, b) => a < b ? a : b),
        'max_duration_ms': durations
            .map((d) => d.inMilliseconds)
            .reduce((a, b) => a > b ? a : b),
        'slow_operations': slowCount,
      };
    }

    return {
      'enabled': _isEnabled,
      'total_operations': totalOperations,
      'average_duration_ms': totalOperations > 0
          ? totalDurationMs ~/ totalOperations
          : 0,
      'slow_operations': totalSlowOperations,
      'operation_breakdown': breakdown,
      'monitoring_period': _getMonitoringPeriod(),
    };
  }

  /// Get monitoring period
  String _getMonitoringPeriod() {
    if (_metrics.isEmpty) return 'No data';

    final firstMetric = _metrics.first;
    final lastMetric = _metrics.last;
    final duration = lastMetric.timestamp.difference(firstMetric.timestamp);

    return '${duration.inMinutes} minutes';
  }

  /// Get slow operations (operations taking more than 1 second)
  List<PerformanceMetric> getSlowOperations() {
    return _metrics
        .where((metric) => metric.duration.inMilliseconds > 1000)
        .toList();
  }

  /// Get operations by name
  List<PerformanceMetric> getOperationsByName(String operationName) {
    return _metrics
        .where((metric) => metric.operationName == operationName)
        .toList();
  }

  /// Clear all metrics
  void clearMetrics() {
    _metrics.clear();
    _operationTimes.clear();

    if (kDebugMode) {
      debugPrint('Performance metrics cleared');
    }
  }

  /// Start cleanup timer
  void _startCleanupTimer() {
    _cleanupTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _cleanupOldMetrics();
    });
  }

  /// Cleanup old metrics (older than 1 hour)
  void _cleanupOldMetrics() {
    final cutoffTime = DateTime.now().subtract(const Duration(hours: 1));
    final initialCount = _metrics.length;

    _metrics.removeWhere((metric) => metric.timestamp.isBefore(cutoffTime));

    final removedCount = initialCount - _metrics.length;
    if (removedCount > 0 && kDebugMode) {
      debugPrint('Cleaned up $removedCount old performance metrics');
    }
  }

  /// Enable/disable performance monitoring
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;

    if (enabled) {
      _startCleanupTimer();
    } else {
      _cleanupTimer?.cancel();
      _cleanupTimer = null;
    }

    if (kDebugMode) {
      debugPrint('Performance monitoring ${enabled ? 'enabled' : 'disabled'}');
    }
  }

  /// Check if monitoring is enabled
  bool get isEnabled => _isEnabled;

  /// Get current metrics count
  int get metricsCount => _metrics.length;

  /// Dispose resources
  void dispose() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    _metrics.clear();
    _operationTimes.clear();
  }
}

/// Performance metric data class
class PerformanceMetric {
  final String operationName;
  final Duration duration;
  final DateTime timestamp;

  PerformanceMetric({
    required this.operationName,
    required this.duration,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'operation_name': operationName,
      'duration_ms': duration.inMilliseconds,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PerformanceMetric.fromMap(Map<String, dynamic> map) {
    return PerformanceMetric(
      operationName: map['operation_name'] as String,
      duration: Duration(milliseconds: map['duration_ms'] as int),
      timestamp: DateTime.parse(map['timestamp'] as String),
    );
  }

  @override
  String toString() {
    return 'PerformanceMetric(operation: $operationName, duration: ${duration.inMilliseconds}ms, time: $timestamp)';
  }
}

/// Global performance monitor instance
final logPerformanceMonitor = LogPerformanceMonitor();
