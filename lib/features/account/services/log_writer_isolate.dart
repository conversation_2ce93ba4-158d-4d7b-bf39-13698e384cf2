import 'dart:isolate';
import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import '../../../shared/database/log_database_helper.dart';
import '../models/log_entry.dart';

/// Isolate cho log writing operations
/// Xử lý log writing trong background để không block main thread
class LogWriterIsolate {
  static Isolate? _isolate;
  static SendPort? _sendPort;
  static bool _isInitialized = false;
  static final Queue<LogEntry> _pendingLogs = Queue<LogEntry>();

  /// Initialize isolate
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        debugPrint('🔄 Initializing log writer isolate...');
      }

      final receivePort = ReceivePort();
      _isolate = await Isolate.spawn(
        _logWriterIsolateFunction,
        receivePort.sendPort,
        debugName: 'log_writer_isolate',
        errorsAreFatal: false, // Don't crash main isolate
      );

      _sendPort = await receivePort.first as SendPort;

      // Initialize database in isolate
      _sendPort?.send({'type': 'init'});

      // Wait for initialization response
      final response = await receivePort.first;
      if (response is Map<String, dynamic> &&
          response['type'] == 'initialized') {
        _isInitialized = true;

        if (kDebugMode) {
          debugPrint('✅ Log writer isolate initialized successfully');
        }

        // Process pending logs
        if (_pendingLogs.isNotEmpty) {
          if (kDebugMode) {
            debugPrint('📝 Processing ${_pendingLogs.length} pending logs');
          }
          while (_pendingLogs.isNotEmpty) {
            final log = _pendingLogs.removeFirst();
            await writeLog(log);
          }
        }

        // Listen for responses
        receivePort.listen((message) {
          if (message is Map<String, dynamic>) {
            if (kDebugMode) {
              debugPrint('📨 Isolate response: ${message['type']}');
            }
          }
        });
      }
    } catch (e) {
      _isInitialized = false;
      if (kDebugMode) {
        debugPrint('❌ Failed to initialize log writer isolate: $e');
      }
    }
  }

  /// Isolate function
  static void _logWriterIsolateFunction(SendPort sendPort) {
    final receivePort = ReceivePort();
    sendPort.send(receivePort.sendPort);

    Database? logDatabase;
    final logQueue = <Map<String, dynamic>>[];
    Timer? flushTimer;

    receivePort.listen((message) async {
      try {
        if (message is Map<String, dynamic>) {
          switch (message['type']) {
            case 'init':
              // Initialize database trong isolate
              logDatabase = await LogDatabaseHelper.instance.database;
              sendPort.send({'type': 'initialized'});
              break;

            case 'write':
              // Add log to queue
              final data = message['data'];
              logQueue.add(data);
              if (kDebugMode) {
                debugPrint(
                  '📝 Isolate: Added log to queue: ${data['id']} (queue size: ${logQueue.length})',
                );
              }

              // Flush immediately if queue is large
              if (logQueue.length >= 50) {
                if (kDebugMode) {
                  debugPrint(
                    '📝 Isolate: Flushing large queue (${logQueue.length} logs)',
                  );
                }
                await _flushLogQueue(logDatabase!, logQueue, sendPort);
              } else {
                // Schedule flush timer
                flushTimer?.cancel();
                flushTimer = Timer(const Duration(seconds: 2), () async {
                  if (kDebugMode) {
                    debugPrint(
                      '📝 Isolate: Timer flush (${logQueue.length} logs)',
                    );
                  }
                  await _flushLogQueue(logDatabase!, logQueue, sendPort);
                });
              }
              break;

            case 'flush':
              // Force flush
              if (logDatabase != null) {
                await _flushLogQueue(logDatabase!, logQueue, sendPort);
              }
              break;

            case 'close':
              // Close database và cleanup
              flushTimer?.cancel();
              if (logDatabase != null) {
                await _flushLogQueue(logDatabase!, logQueue, sendPort);
                await logDatabase!.close();
              }
              sendPort.send({'type': 'closed'});
              break;
          }
        }
      } catch (e) {
        sendPort.send({'type': 'error', 'error': e.toString()});
      }
    });
  }

  /// Flush log queue to database
  static Future<void> _flushLogQueue(
    Database db,
    List<Map<String, dynamic>> queue,
    SendPort sendPort,
  ) async {
    if (queue.isEmpty) return;

    try {
      if (kDebugMode) {
        debugPrint('🗄️ Isolate: Flushing ${queue.length} logs to database');
      }

      final batch = db.batch();

      for (final logData in queue) {
        batch.insert(
          'app_logs',
          logData,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      await batch.commit(noResult: true);

      if (kDebugMode) {
        debugPrint(
          '✅ Isolate: Successfully flushed ${queue.length} logs to database',
        );
      }

      sendPort.send({'type': 'flush_success', 'count': queue.length});

      queue.clear();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Isolate: Error flushing logs: $e');
      }
      sendPort.send({
        'type': 'flush_error',
        'error': e.toString(),
        'count': queue.length,
      });
    }
  }

  /// Write log entry
  static Future<void> writeLog(LogEntry logEntry) async {
    if (!_isInitialized) {
      _pendingLogs.add(logEntry);
      await initialize();
      return;
    }

    try {
      _sendPort?.send({'type': 'write', 'data': logEntry.toSQLiteMap()});
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to send log to isolate: $e');
      }
      // Fallback to main thread
      await _writeLogSync(logEntry);
    }
  }

  /// Write batch of logs
  static Future<void> writeLogs(List<LogEntry> logEntries) async {
    if (!_isInitialized) {
      _pendingLogs.addAll(logEntries);
      await initialize();
      return;
    }

    try {
      for (final logEntry in logEntries) {
        _sendPort?.send({'type': 'write', 'data': logEntry.toSQLiteMap()});
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to send logs to isolate: $e');
      }
      // Fallback to main thread
      await _writeLogsSync(logEntries);
    }
  }

  /// Force flush
  static Future<void> forceFlush() async {
    if (!_isInitialized) return;

    try {
      _sendPort?.send({'type': 'flush'});
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to force flush: $e');
      }
    }
  }

  /// Close isolate
  static Future<void> close() async {
    if (!_isInitialized) return;

    try {
      _sendPort?.send({'type': 'close'});
      _isolate?.kill();
      _isolate = null;
      _sendPort = null;
      _isInitialized = false;

      if (kDebugMode) {
        debugPrint('Log writer isolate closed');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error closing log isolate: $e');
      }
    }
  }

  /// Check if isolate is initialized
  static bool get isInitialized => _isInitialized;

  /// Get pending logs count
  static int get pendingLogsCount => _pendingLogs.length;

  /// Fallback synchronous writing
  static Future<void> _writeLogSync(LogEntry logEntry) async {
    try {
      final db = await LogDatabaseHelper.instance.database;
      await db.insert(
        'app_logs',
        logEntry.toSQLiteMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to write log synchronously: $e');
      }
    }
  }

  /// Fallback synchronous batch writing
  static Future<void> _writeLogsSync(List<LogEntry> logEntries) async {
    try {
      final db = await LogDatabaseHelper.instance.database;
      final batch = db.batch();

      for (final logEntry in logEntries) {
        batch.insert(
          'app_logs',
          logEntry.toSQLiteMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      await batch.commit(noResult: true);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to write logs synchronously: $e');
      }
    }
  }
}
