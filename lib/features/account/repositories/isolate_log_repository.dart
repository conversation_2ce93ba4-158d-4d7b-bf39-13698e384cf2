import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import '../../../shared/database/log_database_helper.dart';
import '../models/log_entry.dart';
import '../models/log_filter.dart';
import '../models/log_stats.dart';

/// Repository cho log operations trong isolate
/// Quản lý CRUD operations cho logs với database riêng biệt
class IsolateLogRepository {
  static const String _tableName = 'app_logs';

  /// Get database instance
  Future<Database> get _database async {
    return await LogDatabaseHelper.instance.database;
  }

  /// Insert log entry
  Future<void> insertLog(LogEntry logEntry) async {
    try {
      if (kDebugMode) {
        debugPrint('🗄️ Inserting log into database: ${logEntry.id}');
      }
      final db = await _database;
      final data = logEntry.toSQLiteMap();
      if (kDebugMode) {
        debugPrint('🗄️ Log data: $data');
      }
      await db.insert(
        _tableName,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error inserting log: $e');
      }
      rethrow;
    }
  }

  /// Insert multiple logs (batch operation)
  Future<void> insertLogs(List<LogEntry> logs) async {
    if (logs.isEmpty) return;

    try {
      final db = await _database;
      final batch = db.batch();

      for (final log in logs) {
        batch.insert(
          _tableName,
          log.toSQLiteMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      await batch.commit(noResult: true);

      if (kDebugMode) {
        debugPrint('Inserted ${logs.length} logs');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error inserting logs: $e');
      }
      rethrow;
    }
  }

  /// Get logs with filter
  Future<List<LogEntry>> getLogs(LogFilter filter) async {
    try {
      if (kDebugMode) {
        debugPrint('📖 Getting logs with filter: ${filter.description}');
      }
      final db = await _database;

      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      // Build WHERE clause
      if (filter.levels != null && filter.levels!.isNotEmpty) {
        final placeholders = List.filled(filter.levels!.length, '?').join(',');
        whereConditions.add('level IN ($placeholders)');
        whereArgs.addAll(filter.levels!.map((l) => l.name));
      }

      if (filter.features != null && filter.features!.isNotEmpty) {
        final placeholders = List.filled(
          filter.features!.length,
          '?',
        ).join(',');
        whereConditions.add('feature IN ($placeholders)');
        whereArgs.addAll(filter.features!);
      }

      if (filter.tags != null && filter.tags!.isNotEmpty) {
        final placeholders = List.filled(filter.tags!.length, '?').join(',');
        whereConditions.add('tag IN ($placeholders)');
        whereArgs.addAll(filter.tags!);
      }

      if (filter.startDate != null) {
        whereConditions.add('timestamp >= ?');
        whereArgs.add(filter.startDate!.millisecondsSinceEpoch);
      }

      if (filter.endDate != null) {
        whereConditions.add('timestamp <= ?');
        whereArgs.add(filter.endDate!.millisecondsSinceEpoch);
      }

      if (filter.userId != null) {
        whereConditions.add('user_id = ?');
        whereArgs.add(filter.userId);
      }

      if (filter.sessionId != null) {
        whereConditions.add('session_id = ?');
        whereArgs.add(filter.sessionId);
      }

      //if (filter.hasError == true) {
      //  whereConditions.add(
      //    '(error_data IS NOT NULL OR stack_trace IS NOT NULL)',
      //  );
      //}

      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        whereConditions.add('(message LIKE ? OR tag LIKE ?)');
        final query = '%${filter.searchQuery}%';
        whereArgs.addAll([query, query]);
      }

      final whereClause = whereConditions.isNotEmpty
          ? 'WHERE ${whereConditions.join(' AND ')}'
          : '';

      final limitClause = filter.limit != null ? 'LIMIT ${filter.limit}' : '';

      final offsetClause = filter.offset != null
          ? 'OFFSET ${filter.offset}'
          : '';

      final query =
          '''
        SELECT * FROM $_tableName 
        $whereClause 
        ORDER BY timestamp DESC 
        $limitClause 
        $offsetClause
      ''';

      final results = await db.rawQuery(query, whereArgs);

      if (kDebugMode) {
        debugPrint('📖 Found ${results.length} logs in database');
      }

      return results.map((map) => LogEntry.fromSQLiteMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error getting logs: $e');
      }
      rethrow;
    }
  }

  /// Get log by ID
  Future<LogEntry?> getLogById(String id) async {
    try {
      final db = await _database;
      final results = await db.query(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return LogEntry.fromSQLiteMap(results.first);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting log by ID: $e');
      }
      rethrow;
    }
  }

  /// Get log statistics
  Future<LogStats> getLogStats() async {
    try {
      final db = await _database;

      // Get total count
      final totalCount = await db.rawQuery(
        'SELECT COUNT(*) as count FROM $_tableName',
      );

      // Get level statistics
      final levelStats = await db.rawQuery('''
        SELECT level, COUNT(*) as count 
        FROM $_tableName 
        GROUP BY level
      ''');

      // Get feature statistics
      final featureStats = await db.rawQuery('''
        SELECT feature, COUNT(*) as count 
        FROM $_tableName 
        WHERE feature IS NOT NULL
        GROUP BY feature
      ''');

      // Get today's count
      final todayCount = await db.rawQuery(
        '''
        SELECT COUNT(*) as count 
        FROM $_tableName 
        WHERE timestamp >= ?
      ''',
        [
          DateTime.now()
              .subtract(const Duration(days: 1))
              .millisecondsSinceEpoch,
        ],
      );

      // Get error count
      final errorCount = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM $_tableName 
        WHERE level = 'error'
      ''');

      // Get warning count
      final warningCount = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM $_tableName 
        WHERE level = 'warning'
      ''');

      // Get oldest and newest log timestamps
      final oldestLog = await db.rawQuery('''
        SELECT timestamp 
        FROM $_tableName 
        ORDER BY timestamp ASC 
        LIMIT 1
      ''');

      final newestLog = await db.rawQuery('''
        SELECT timestamp 
        FROM $_tableName 
        ORDER BY timestamp DESC 
        LIMIT 1
      ''');

      // Get database size
      final databaseSize = await LogDatabaseHelper.instance.getDatabaseSize();

      return LogStats(
        totalCount: totalCount.first['count'] as int,
        levelStats: Map.fromEntries(
          levelStats.map(
            (row) => MapEntry(
              LogLevel.values.firstWhere((l) => l.name == row['level']),
              row['count'] as int,
            ),
          ),
        ),
        featureStats: Map.fromEntries(
          featureStats.map(
            (row) => MapEntry(row['feature'] as String, row['count'] as int),
          ),
        ),
        todayCount: todayCount.first['count'] as int,
        errorCount: errorCount.first['count'] as int,
        warningCount: warningCount.first['count'] as int,
        oldestLog: oldestLog.isNotEmpty
            ? DateTime.fromMillisecondsSinceEpoch(
                oldestLog.first['timestamp'] as int,
              )
            : null,
        newestLog: newestLog.isNotEmpty
            ? DateTime.fromMillisecondsSinceEpoch(
                newestLog.first['timestamp'] as int,
              )
            : null,
        databaseSize: databaseSize,
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting log stats: $e');
      }
      rethrow;
    }
  }

  /// Clear logs
  Future<int> clearLogs(ClearLogsOptions options) async {
    try {
      final db = await _database;

      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      if (options.beforeDate != null) {
        whereConditions.add('timestamp < ?');
        whereArgs.add(options.beforeDate!.millisecondsSinceEpoch);
      }

      if (options.levels != null && options.levels!.isNotEmpty) {
        final placeholders = List.filled(options.levels!.length, '?').join(',');
        whereConditions.add('level IN ($placeholders)');
        whereArgs.addAll(options.levels!.map((l) => l.name));
      }

      if (options.features != null && options.features!.isNotEmpty) {
        final placeholders = List.filled(
          options.features!.length,
          '?',
        ).join(',');
        whereConditions.add('feature IN ($placeholders)');
        whereArgs.addAll(options.features!);
      }

      if (options.keepErrors == true) {
        whereConditions.add('level != "error"');
      }

      final whereClause = whereConditions.isNotEmpty
          ? 'WHERE ${whereConditions.join(' AND ')}'
          : '';

      final result = await db.rawDelete('''
        DELETE FROM $_tableName $whereClause
      ''', whereArgs);

      if (kDebugMode) {
        debugPrint('Cleared $result logs');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error clearing logs: $e');
      }
      rethrow;
    }
  }

  /// Get unique tags
  Future<List<String>> getUniqueTags() async {
    try {
      final db = await _database;
      final results = await db.rawQuery('''
        SELECT DISTINCT tag 
        FROM $_tableName 
        WHERE tag IS NOT NULL AND tag != ''
        ORDER BY tag
      ''');

      return results.map((row) => row['tag'] as String).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting unique tags: $e');
      }
      rethrow;
    }
  }

  /// Get unique features
  Future<List<String>> getUniqueFeatures() async {
    try {
      final db = await _database;
      final results = await db.rawQuery('''
        SELECT DISTINCT feature 
        FROM $_tableName 
        WHERE feature IS NOT NULL AND feature != ''
        ORDER BY feature
      ''');

      return results.map((row) => row['feature'] as String).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting unique features: $e');
      }
      rethrow;
    }
  }

  /// Search logs by text
  Future<List<LogEntry>> searchLogs(String query) async {
    try {
      final db = await _database;
      final results = await db.rawQuery(
        '''
        SELECT * FROM $_tableName 
        WHERE message LIKE ? OR tag LIKE ? OR feature LIKE ?
        ORDER BY timestamp DESC
        LIMIT 100
      ''',
        ['%$query%', '%$query%', '%$query%'],
      );

      return results.map((map) => LogEntry.fromSQLiteMap(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error searching logs: $e');
      }
      rethrow;
    }
  }

  /// Get logs count
  Future<int> getLogsCount(LogFilter? filter) async {
    try {
      final db = await _database;

      if (filter == null || filter.isEmpty) {
        final result = await db.rawQuery(
          'SELECT COUNT(*) as count FROM $_tableName',
        );
        return result.first['count'] as int;
      }

      // Build WHERE clause similar to getLogs
      final whereConditions = <String>[];
      final whereArgs = <dynamic>[];

      if (filter.levels != null && filter.levels!.isNotEmpty) {
        final placeholders = List.filled(filter.levels!.length, '?').join(',');
        whereConditions.add('level IN ($placeholders)');
        whereArgs.addAll(filter.levels!.map((l) => l.name));
      }

      if (filter.features != null && filter.features!.isNotEmpty) {
        final placeholders = List.filled(
          filter.features!.length,
          '?',
        ).join(',');
        whereConditions.add('feature IN ($placeholders)');
        whereArgs.addAll(filter.features!);
      }

      if (filter.startDate != null) {
        whereConditions.add('timestamp >= ?');
        whereArgs.add(filter.startDate!.millisecondsSinceEpoch);
      }

      if (filter.endDate != null) {
        whereConditions.add('timestamp <= ?');
        whereArgs.add(filter.endDate!.millisecondsSinceEpoch);
      }

      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        whereConditions.add('(message LIKE ? OR tag LIKE ?)');
        final query = '%${filter.searchQuery}%';
        whereArgs.addAll([query, query]);
      }

      final whereClause = whereConditions.isNotEmpty
          ? 'WHERE ${whereConditions.join(' AND ')}'
          : '';

      final result = await db.rawQuery('''
        SELECT COUNT(*) as count FROM $_tableName $whereClause
      ''', whereArgs);

      return result.first['count'] as int;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error getting logs count: $e');
      }
      rethrow;
    }
  }
}

/// Options for clearing logs
class ClearLogsOptions {
  final DateTime? beforeDate;
  final List<LogLevel>? levels;
  final List<String>? features;
  final bool? keepErrors;

  const ClearLogsOptions({
    this.beforeDate,
    this.levels,
    this.features,
    this.keepErrors,
  });
}
