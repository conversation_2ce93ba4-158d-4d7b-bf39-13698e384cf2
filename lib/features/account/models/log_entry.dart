import 'package:flutter/foundation.dart';
import 'dart:convert';

/// Log levels cho consistent usage
enum LogLevel { verbose, debug, info, warning, error }

/// Model cho log entry
@immutable
class LogEntry {
  final String id;
  final LogLevel level;
  final String message;
  final String? tag;
  final String? feature;
  final DateTime timestamp;
  final dynamic errorData;
  final String? stackTrace;
  final Map<String, dynamic>? metadata;
  final String? userId;
  final String? sessionId;
  final Map<String, dynamic>? deviceInfo;

  const LogEntry({
    required this.id,
    required this.level,
    required this.message,
    this.tag,
    this.feature,
    required this.timestamp,
    this.errorData,
    this.stackTrace,
    this.metadata,
    this.userId,
    this.sessionId,
    this.deviceInfo,
  });

  /// Copy with method
  LogEntry copyWith({
    String? id,
    LogLevel? level,
    String? message,
    String? tag,
    String? feature,
    DateTime? timestamp,
    dynamic errorData,
    String? stackTrace,
    Map<String, dynamic>? metadata,
    String? userId,
    String? sessionId,
    Map<String, dynamic>? deviceInfo,
  }) {
    return LogEntry(
      id: id ?? this.id,
      level: level ?? this.level,
      message: message ?? this.message,
      tag: tag ?? this.tag,
      feature: feature ?? this.feature,
      timestamp: timestamp ?? this.timestamp,
      errorData: errorData ?? this.errorData,
      stackTrace: stackTrace ?? this.stackTrace,
      metadata: metadata ?? this.metadata,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
      deviceInfo: deviceInfo ?? this.deviceInfo,
    );
  }

  /// Convert to SQLite map
  Map<String, dynamic> toSQLiteMap() {
    return {
      'id': id,
      'level': level.name,
      'message': message,
      'tag': tag,
      'feature': feature,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'error_data': errorData != null ? jsonEncode(errorData) : null,
      'stack_trace': stackTrace,
      'metadata': metadata != null ? jsonEncode(metadata) : null,
      'user_id': userId,
      'session_id': sessionId,
      'device_info': deviceInfo != null ? jsonEncode(deviceInfo) : null,
    };
  }

  /// Create from SQLite map
  factory LogEntry.fromSQLiteMap(Map<String, dynamic> map) {
    return LogEntry(
      id: map['id'],
      level: LogLevel.values.firstWhere(
        (e) => e.name == map['level'],
        orElse: () => LogLevel.info,
      ),
      message: map['message'],
      tag: map['tag'],
      feature: map['feature'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
      errorData: map['error_data'] != null
          ? jsonDecode(map['error_data'])
          : null,
      stackTrace: map['stack_trace'],
      metadata: map['metadata'] != null ? jsonDecode(map['metadata']) : null,
      userId: map['user_id'],
      sessionId: map['session_id'],
      deviceInfo: map['device_info'] != null
          ? jsonDecode(map['device_info'])
          : null,
    );
  }

  /// Convert to JSON map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'level': level.name,
      'message': message,
      'tag': tag,
      'feature': feature,
      'timestamp': timestamp.toIso8601String(),
      'errorData': errorData,
      'stackTrace': stackTrace,
      'metadata': metadata,
      'userId': userId,
      'sessionId': sessionId,
      'deviceInfo': deviceInfo,
    };
  }

  /// Create from JSON map
  factory LogEntry.fromMap(Map<String, dynamic> map) {
    return LogEntry(
      id: map['id'],
      level: LogLevel.values.firstWhere(
        (e) => e.name == map['level'],
        orElse: () => LogLevel.info,
      ),
      message: map['message'],
      tag: map['tag'],
      feature: map['feature'],
      timestamp: DateTime.parse(map['timestamp']),
      errorData: map['errorData'],
      stackTrace: map['stackTrace'],
      metadata: map['metadata'] != null
          ? Map<String, dynamic>.from(map['metadata'])
          : null,
      userId: map['userId'],
      sessionId: map['sessionId'],
      deviceInfo: map['deviceInfo'] != null
          ? Map<String, dynamic>.from(map['deviceInfo'])
          : null,
    );
  }

  /// Get display name for log level
  String get levelDisplayName {
    switch (level) {
      case LogLevel.verbose:
        return 'Chi tiết';
      case LogLevel.debug:
        return 'Debug';
      case LogLevel.info:
        return 'Thông tin';
      case LogLevel.warning:
        return 'Cảnh báo';
      case LogLevel.error:
        return 'Lỗi';
    }
  }

  /// Get color for log level
  String get levelColor {
    switch (level) {
      case LogLevel.verbose:
        return '#9E9E9E'; // Grey
      case LogLevel.debug:
        return '#2196F3'; // Blue
      case LogLevel.info:
        return '#4CAF50'; // Green
      case LogLevel.warning:
        return '#FF9800'; // Orange
      case LogLevel.error:
        return '#F44336'; // Red
    }
  }

  /// Get emoji for log level
  String get levelEmoji {
    switch (level) {
      case LogLevel.verbose:
        return '📝';
      case LogLevel.debug:
        return '🐛';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
    }
  }

  /// Check if log has error data
  bool get hasError => errorData != null || stackTrace != null;

  /// Get formatted timestamp
  String get formattedTimestamp {
    return '${timestamp.day.toString().padLeft(2, '0')}/${timestamp.month.toString().padLeft(2, '0')}/${timestamp.year} ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}:${timestamp.second.toString().padLeft(2, '0')}';
  }

  /// Get short message (truncated if too long)
  String get shortMessage {
    if (message.length <= 100) return message;
    return '${message.substring(0, 97)}...';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LogEntry &&
        other.id == id &&
        other.level == level &&
        other.message == message &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return Object.hash(id, level, message, timestamp);
  }

  @override
  String toString() {
    return 'LogEntry(id: $id, level: ${level.name}, message: $message, timestamp: $timestamp)';
  }
}
