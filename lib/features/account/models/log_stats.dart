import 'package:flutter/foundation.dart';
import 'log_entry.dart';

/// Model cho log statistics
class LogStats {
  final int totalCount;
  final Map<LogLevel, int> levelStats;
  final Map<String, int> featureStats;
  final int todayCount;
  final int errorCount;
  final int warningCount;
  final DateTime? oldestLog;
  final DateTime? newestLog;
  final int databaseSize;

  const LogStats({
    required this.totalCount,
    required this.levelStats,
    required this.featureStats,
    required this.todayCount,
    required this.errorCount,
    required this.warningCount,
    this.oldestLog,
    this.newestLog,
    this.databaseSize = 0,
  });

  /// Create empty stats
  factory LogStats.empty() {
    return const LogStats(
      totalCount: 0,
      levelStats: {},
      featureStats: {},
      todayCount: 0,
      errorCount: 0,
      warningCount: 0,
    );
  }

  /// Copy with method
  LogStats copyWith({
    int? totalCount,
    Map<LogLevel, int>? levelStats,
    Map<String, int>? featureStats,
    int? todayCount,
    int? errorCount,
    int? warningCount,
    DateTime? oldestLog,
    DateTime? newestLog,
    int? databaseSize,
  }) {
    return LogStats(
      totalCount: totalCount ?? this.totalCount,
      levelStats: levelStats ?? this.levelStats,
      featureStats: featureStats ?? this.featureStats,
      todayCount: todayCount ?? this.todayCount,
      errorCount: errorCount ?? this.errorCount,
      warningCount: warningCount ?? this.warningCount,
      oldestLog: oldestLog ?? this.oldestLog,
      newestLog: newestLog ?? this.newestLog,
      databaseSize: databaseSize ?? this.databaseSize,
    );
  }

  /// Get count for specific level
  int getCountForLevel(LogLevel level) {
    return levelStats[level] ?? 0;
  }

  /// Get count for specific feature
  int getCountForFeature(String feature) {
    return featureStats[feature] ?? 0;
  }

  /// Get error percentage
  double get errorPercentage {
    if (totalCount == 0) return 0.0;
    return (errorCount / totalCount) * 100;
  }

  /// Get warning percentage
  double get warningPercentage {
    if (totalCount == 0) return 0.0;
    return (warningCount / totalCount) * 100;
  }

  /// Get today percentage
  double get todayPercentage {
    if (totalCount == 0) return 0.0;
    return (todayCount / totalCount) * 100;
  }

  /// Get formatted database size
  String get formattedDatabaseSize {
    if (databaseSize < 1024) {
      return '${databaseSize}B';
    } else if (databaseSize < 1024 * 1024) {
      return '${(databaseSize / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(databaseSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Get formatted oldest log date
  String? get formattedOldestLog {
    if (oldestLog == null) return null;
    return '${oldestLog!.day.toString().padLeft(2, '0')}/${oldestLog!.month.toString().padLeft(2, '0')}/${oldestLog!.year}';
  }

  /// Get formatted newest log date
  String? get formattedNewestLog {
    if (newestLog == null) return null;
    return '${newestLog!.day.toString().padLeft(2, '0')}/${newestLog!.month.toString().padLeft(2, '0')}/${newestLog!.year}';
  }

  /// Get top features (sorted by count)
  List<MapEntry<String, int>> get topFeatures {
    final sorted = featureStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return sorted.take(5).toList();
  }

  /// Get top levels (sorted by count)
  List<MapEntry<LogLevel, int>> get topLevels {
    final sorted = levelStats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return sorted;
  }

  /// Check if stats are empty
  bool get isEmpty => totalCount == 0;

  /// Check if stats are not empty
  bool get isNotEmpty => totalCount > 0;

  /// Convert to map for persistence
  Map<String, dynamic> toMap() {
    return {
      'totalCount': totalCount,
      'levelStats': levelStats.map((key, value) => MapEntry(key.name, value)),
      'featureStats': featureStats,
      'todayCount': todayCount,
      'errorCount': errorCount,
      'warningCount': warningCount,
      'oldestLog': oldestLog?.toIso8601String(),
      'newestLog': newestLog?.toIso8601String(),
      'databaseSize': databaseSize,
    };
  }

  /// Create from map
  factory LogStats.fromMap(Map<String, dynamic> map) {
    return LogStats(
      totalCount: map['totalCount'] ?? 0,
      levelStats:
          (map['levelStats'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(
              LogLevel.values.firstWhere(
                (e) => e.name == key,
                orElse: () => LogLevel.info,
              ),
              value as int,
            ),
          ) ??
          {},
      featureStats:
          (map['featureStats'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, value as int),
          ) ??
          {},
      todayCount: map['todayCount'] ?? 0,
      errorCount: map['errorCount'] ?? 0,
      warningCount: map['warningCount'] ?? 0,
      oldestLog: map['oldestLog'] != null
          ? DateTime.parse(map['oldestLog'])
          : null,
      newestLog: map['newestLog'] != null
          ? DateTime.parse(map['newestLog'])
          : null,
      databaseSize: map['databaseSize'] ?? 0,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LogStats &&
        other.totalCount == totalCount &&
        mapEquals(other.levelStats, levelStats) &&
        mapEquals(other.featureStats, featureStats) &&
        other.todayCount == todayCount &&
        other.errorCount == errorCount &&
        other.warningCount == warningCount &&
        other.oldestLog == oldestLog &&
        other.newestLog == newestLog &&
        other.databaseSize == databaseSize;
  }

  @override
  int get hashCode {
    return Object.hash(
      totalCount,
      levelStats,
      featureStats,
      todayCount,
      errorCount,
      warningCount,
      oldestLog,
      newestLog,
      databaseSize,
    );
  }

  @override
  String toString() {
    return 'LogStats(totalCount: $totalCount, todayCount: $todayCount, errorCount: $errorCount)';
  }
}
