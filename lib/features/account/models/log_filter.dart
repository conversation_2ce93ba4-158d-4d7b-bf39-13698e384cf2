import 'package:flutter/foundation.dart';
import 'log_entry.dart';

/// Model cho log filter
class LogFilter {
  final List<LogLevel>? levels;
  final List<String>? features;
  final List<String>? tags;
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? userId;
  final String? sessionId;
  final int? limit;
  final int? offset;
  final bool? hasError;

  const LogFilter({
    this.levels,
    this.features,
    this.tags,
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.userId,
    this.sessionId,
    this.limit,
    this.offset,
    this.hasError,
  });

  /// Copy with method
  LogFilter copyWith({
    List<LogLevel>? levels,
    List<String>? features,
    List<String>? tags,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String? userId,
    String? sessionId,
    int? limit,
    int? offset,
    bool? hasError,
  }) {
    return LogFilter(
      levels: levels ?? this.levels,
      features: features ?? this.features,
      tags: tags ?? this.tags,
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      userId: userId ?? this.userId,
      sessionId: sessionId ?? this.sessionId,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
      hasError: hasError ?? this.hasError,
    );
  }

  /// Create filter for specific level
  factory LogFilter.byLevel(LogLevel level) {
    return LogFilter(levels: [level]);
  }

  /// Create filter for specific feature
  factory LogFilter.byFeature(String feature) {
    return LogFilter(features: [feature]);
  }

  /// Create filter for error logs only
  factory LogFilter.errorsOnly() {
    return LogFilter(levels: [LogLevel.error], hasError: true);
  }

  /// Create filter for recent logs
  factory LogFilter.recent({int days = 7}) {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));
    return LogFilter(startDate: startDate, endDate: endDate);
  }

  /// Create filter for today's logs
  factory LogFilter.today() {
    final now = DateTime.now();
    final startDate = DateTime(now.year, now.month, now.day);
    final endDate = startDate.add(const Duration(days: 1));
    return LogFilter(startDate: startDate, endDate: endDate);
  }

  /// Create filter for search
  factory LogFilter.search(String query) {
    return LogFilter(searchQuery: query);
  }

  /// Check if filter is empty (no conditions)
  bool get isEmpty {
    return levels == null &&
        features == null &&
        tags == null &&
        searchQuery == null &&
        startDate == null &&
        endDate == null &&
        userId == null &&
        sessionId == null &&
        hasError == null;
  }

  /// Check if filter has any conditions
  bool get isNotEmpty => !isEmpty;

  /// Get filter description
  String get description {
    final conditions = <String>[];

    if (levels != null && levels!.isNotEmpty) {
      conditions.add('Levels: ${levels!.map((l) => l.name).join(', ')}');
    }

    if (features != null && features!.isNotEmpty) {
      conditions.add('Features: ${features!.join(', ')}');
    }

    if (tags != null && tags!.isNotEmpty) {
      conditions.add('Tags: ${tags!.join(', ')}');
    }

    if (searchQuery != null && searchQuery!.isNotEmpty) {
      conditions.add('Search: "$searchQuery"');
    }

    if (startDate != null) {
      conditions.add('From: ${startDate!.toIso8601String().split('T')[0]}');
    }

    if (endDate != null) {
      conditions.add('To: ${endDate!.toIso8601String().split('T')[0]}');
    }

    if (hasError == true) {
      conditions.add('Has Error: Yes');
    }

    if (conditions.isEmpty) {
      return 'All logs';
    }

    return conditions.join(' | ');
  }

  /// Convert to map for persistence
  Map<String, dynamic> toMap() {
    return {
      'levels': levels?.map((l) => l.name).toList(),
      'features': features,
      'tags': tags,
      'searchQuery': searchQuery,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'userId': userId,
      'sessionId': sessionId,
      'limit': limit,
      'offset': offset,
      'hasError': hasError,
    };
  }

  /// Create from map
  factory LogFilter.fromMap(Map<String, dynamic> map) {
    return LogFilter(
      levels: map['levels'] != null
          ? (map['levels'] as List)
                .map(
                  (l) => LogLevel.values.firstWhere(
                    (e) => e.name == l,
                    orElse: () => LogLevel.info,
                  ),
                )
                .toList()
          : null,
      features: map['features'] != null
          ? List<String>.from(map['features'])
          : null,
      tags: map['tags'] != null ? List<String>.from(map['tags']) : null,
      searchQuery: map['searchQuery'],
      startDate: map['startDate'] != null
          ? DateTime.parse(map['startDate'])
          : null,
      endDate: map['endDate'] != null ? DateTime.parse(map['endDate']) : null,
      userId: map['userId'],
      sessionId: map['sessionId'],
      limit: map['limit'],
      offset: map['offset'],
      hasError: map['hasError'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LogFilter &&
        listEquals(other.levels, levels) &&
        listEquals(other.features, features) &&
        listEquals(other.tags, tags) &&
        other.searchQuery == searchQuery &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.userId == userId &&
        other.sessionId == sessionId &&
        other.limit == limit &&
        other.offset == offset &&
        other.hasError == hasError;
  }

  @override
  int get hashCode {
    return Object.hash(
      levels,
      features,
      tags,
      searchQuery,
      startDate,
      endDate,
      userId,
      sessionId,
      limit,
      offset,
      hasError,
    );
  }

  @override
  String toString() {
    return 'LogFilter(description: $description)';
  }
}
