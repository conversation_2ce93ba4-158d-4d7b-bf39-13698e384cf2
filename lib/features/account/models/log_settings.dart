import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'log_entry.dart';

/// Model cho log settings
@immutable
class LogSettings {
  final bool isEnabled;
  final bool saveToDatabase;
  final bool saveToConsole;
  final LogLevel minimumLevel;
  final Duration retentionPeriod;
  final int maxLogEntries;
  final bool autoCleanup;
  final Duration cleanupInterval;
  final bool maskSensitiveData;
  final bool enablePerformanceMonitoring;

  const LogSettings({
    this.isEnabled = true,
    this.saveToDatabase = true,
    this.saveToConsole = true,
    this.minimumLevel = LogLevel.info,
    this.retentionPeriod = const Duration(days: 30),
    this.maxLogEntries = 10000,
    this.autoCleanup = true,
    this.cleanupInterval = const Duration(days: 1),
    this.maskSensitiveData = true,
    this.enablePerformanceMonitoring = false,
  });

  /// Copy with method
  LogSettings copyWith({
    bool? isEnabled,
    bool? saveToDatabase,
    bool? saveToConsole,
    LogLevel? minimumLevel,
    Duration? retentionPeriod,
    int? maxLogEntries,
    bool? autoCleanup,
    Duration? cleanupInterval,
    bool? maskSensitiveData,
    bool? enablePerformanceMonitoring,
  }) {
    return LogSettings(
      isEnabled: isEnabled ?? this.isEnabled,
      saveToDatabase: saveToDatabase ?? this.saveToDatabase,
      saveToConsole: saveToConsole ?? this.saveToConsole,
      minimumLevel: minimumLevel ?? this.minimumLevel,
      retentionPeriod: retentionPeriod ?? this.retentionPeriod,
      maxLogEntries: maxLogEntries ?? this.maxLogEntries,
      autoCleanup: autoCleanup ?? this.autoCleanup,
      cleanupInterval: cleanupInterval ?? this.cleanupInterval,
      maskSensitiveData: maskSensitiveData ?? this.maskSensitiveData,
      enablePerformanceMonitoring:
          enablePerformanceMonitoring ?? this.enablePerformanceMonitoring,
    );
  }

  /// Create default settings
  factory LogSettings.defaultSettings() {
    return const LogSettings();
  }

  /// Create minimal settings (for performance)
  factory LogSettings.minimal() {
    return const LogSettings(
      isEnabled: true,
      saveToDatabase: false,
      saveToConsole: true,
      minimumLevel: LogLevel.warning,
      retentionPeriod: Duration(days: 7),
      maxLogEntries: 1000,
      autoCleanup: true,
      cleanupInterval: Duration(days: 1),
      maskSensitiveData: true,
      enablePerformanceMonitoring: false,
    );
  }

  /// Create verbose settings (for debugging)
  factory LogSettings.verbose() {
    return const LogSettings(
      isEnabled: true,
      saveToDatabase: true,
      saveToConsole: true,
      minimumLevel: LogLevel.verbose,
      retentionPeriod: Duration(days: 7),
      maxLogEntries: 50000,
      autoCleanup: false,
      cleanupInterval: Duration(days: 7),
      maskSensitiveData: false,
      enablePerformanceMonitoring: true,
    );
  }

  /// Check if logging is completely disabled
  bool get isCompletelyDisabled => !isEnabled;

  /// Check if database logging is enabled
  bool get isDatabaseLoggingEnabled => isEnabled && saveToDatabase;

  /// Check if console logging is enabled
  bool get isConsoleLoggingEnabled => isEnabled && saveToConsole;

  /// Check if performance monitoring is enabled
  bool get isPerformanceMonitoringEnabled =>
      isEnabled && enablePerformanceMonitoring;

  /// Get retention period in days
  int get retentionDays => retentionPeriod.inDays;

  /// Get cleanup interval in days
  int get cleanupIntervalDays => cleanupInterval.inDays;

  /// Get formatted retention period
  String get formattedRetentionPeriod {
    final days = retentionDays;
    if (days == 1) return '1 ngày';
    if (days < 30) return '$days ngày';
    if (days == 30) return '1 tháng';
    if (days < 365) return '${(days / 30).round()} tháng';
    return '${(days / 365).round()} năm';
  }

  /// Get formatted cleanup interval
  String get formattedCleanupInterval {
    final days = cleanupIntervalDays;
    if (days == 1) return 'Hàng ngày';
    if (days == 7) return 'Hàng tuần';
    if (days == 30) return 'Hàng tháng';
    return 'Mỗi $days ngày';
  }

  /// Get minimum level display name
  String get minimumLevelDisplayName {
    switch (minimumLevel) {
      case LogLevel.verbose:
        return 'Chi tiết';
      case LogLevel.debug:
        return 'Debug';
      case LogLevel.info:
        return 'Thông tin';
      case LogLevel.warning:
        return 'Cảnh báo';
      case LogLevel.error:
        return 'Lỗi';
    }
  }

  /// Convert to map for persistence
  Map<String, dynamic> toMap() {
    return {
      'isEnabled': isEnabled,
      'saveToDatabase': saveToDatabase,
      'saveToConsole': saveToConsole,
      'minimumLevel': minimumLevel.name,
      'retentionPeriod': retentionDays,
      'maxLogEntries': maxLogEntries,
      'autoCleanup': autoCleanup,
      'cleanupInterval': cleanupIntervalDays,
      'maskSensitiveData': maskSensitiveData,
      'enablePerformanceMonitoring': enablePerformanceMonitoring,
    };
  }

  /// Create from map
  factory LogSettings.fromMap(Map<String, dynamic> map) {
    return LogSettings(
      isEnabled: map['isEnabled'] ?? true,
      saveToDatabase: map['saveToDatabase'] ?? true,
      saveToConsole: map['saveToConsole'] ?? true,
      minimumLevel: LogLevel.values.firstWhere(
        (e) => e.name == map['minimumLevel'],
        orElse: () => LogLevel.info,
      ),
      retentionPeriod: Duration(days: map['retentionPeriod'] ?? 30),
      maxLogEntries: map['maxLogEntries'] ?? 10000,
      autoCleanup: map['autoCleanup'] ?? true,
      cleanupInterval: Duration(days: map['cleanupInterval'] ?? 1),
      maskSensitiveData: map['maskSensitiveData'] ?? true,
      enablePerformanceMonitoring: map['enablePerformanceMonitoring'] ?? false,
    );
  }

  /// Convert to JSON string
  String toJson() => jsonEncode(toMap());

  /// Create from JSON string
  factory LogSettings.fromJson(String json) {
    return LogSettings.fromMap(jsonDecode(json));
  }

  /// Validate settings
  List<String> validate() {
    final errors = <String>[];

    if (maxLogEntries < 100) {
      errors.add('Số lượng log tối đa phải ít nhất 100');
    }

    if (maxLogEntries > 100000) {
      errors.add('Số lượng log tối đa không được vượt quá 100,000');
    }

    if (retentionDays < 1) {
      errors.add('Thời gian lưu trữ phải ít nhất 1 ngày');
    }

    if (retentionDays > 365) {
      errors.add('Thời gian lưu trữ không được vượt quá 1 năm');
    }

    if (cleanupIntervalDays < 1) {
      errors.add('Tần suất dọn dẹp phải ít nhất 1 ngày');
    }

    if (cleanupIntervalDays > 30) {
      errors.add('Tần suất dọn dẹp không được vượt quá 30 ngày');
    }

    return errors;
  }

  /// Check if settings are valid
  bool get isValid => validate().isEmpty;

  /// Get settings summary
  String get summary {
    if (isCompletelyDisabled) {
      return 'Logging đã tắt';
    }

    final parts = <String>[];

    if (isDatabaseLoggingEnabled) {
      parts.add('Database');
    }

    if (isConsoleLoggingEnabled) {
      parts.add('Console');
    }

    final storage = parts.join(' + ');
    return 'Logging: $storage | Level: $minimumLevelDisplayName | Lưu trữ: $formattedRetentionPeriod';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LogSettings &&
        other.isEnabled == isEnabled &&
        other.saveToDatabase == saveToDatabase &&
        other.saveToConsole == saveToConsole &&
        other.minimumLevel == minimumLevel &&
        other.retentionPeriod == retentionPeriod &&
        other.maxLogEntries == maxLogEntries &&
        other.autoCleanup == autoCleanup &&
        other.cleanupInterval == cleanupInterval &&
        other.maskSensitiveData == maskSensitiveData &&
        other.enablePerformanceMonitoring == enablePerformanceMonitoring;
  }

  @override
  int get hashCode {
    return Object.hash(
      isEnabled,
      saveToDatabase,
      saveToConsole,
      minimumLevel,
      retentionPeriod,
      maxLogEntries,
      autoCleanup,
      cleanupInterval,
      maskSensitiveData,
      enablePerformanceMonitoring,
    );
  }

  @override
  String toString() {
    return 'LogSettings(summary: $summary)';
  }
}
