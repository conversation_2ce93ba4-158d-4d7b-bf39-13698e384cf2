import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/log_entry.dart';
import '../models/log_filter.dart';
import '../models/log_stats.dart';
import '../services/log_management_service.dart';
import '../widgets/index.dart';
import 'log_settings_screen.dart';

/// Màn hình xem và quản lý logs với thiết kế chuyên nghiệp
class LogViewerScreen extends StatefulWidget {
  const LogViewerScreen({super.key});

  @override
  State<LogViewerScreen> createState() => _LogViewerScreenState();
}

class _LogViewerScreenState extends State<LogViewerScreen>
    with TickerProviderStateMixin {
  List<LogEntry> _logs = [];
  LogStats? _stats;
  LogFilter _currentFilter = const LogFilter();
  bool _isLoading = true;

  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  late AnimationController _fabAnimationController;
  late Animation<double> _fabAnimation;

  // Pagination variables
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasMoreData = true;
  static const int _pageSize = 100;
  int _currentOffset = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupScrollController();
    _loadLogs();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: BankingTheme.cardAnimationDuration,
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _fabAnimationController,
        curve: BankingTheme.cardAnimationCurve,
      ),
    );
  }

  void _setupScrollController() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreLogs();
      }
    });
  }

  /// Load logs with current filter
  Future<void> _loadLogs() async {
    try {
      setState(() {
        _isLoading = true;
        _currentOffset = 0;
        _hasMoreData = true;
      });

      final filterWithPagination = _currentFilter.copyWith(
        limit: _pageSize,
        offset: 0,
      );

      final logs = await LogManagementService.getLogs(filterWithPagination);
      final stats = await LogManagementService.getLogStats();

      setState(() {
        _logs = logs;
        _stats = stats;
        _isLoading = false;
        _currentOffset = logs.length;
        _hasMoreData = logs.length == _pageSize;
      });

      // Animate fab when data loaded
      _fabAnimationController.forward();
      if (kDebugMode) {
        debugPrint('Đã tải ${logs.length} logs thành công');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Lỗi khi tải logs: $e');
      }
      setState(() => _isLoading = false);
      _showErrorSnackBar('Không thể tải logs. Vui lòng thử lại.');
    }
  }

  /// Load more logs for pagination
  Future<void> _loadMoreLogs() async {
    if (_isLoadingMore || !_hasMoreData || _isLoading) return;

    try {
      setState(() => _isLoadingMore = true);

      final filterWithPagination = _currentFilter.copyWith(
        limit: _pageSize,
        offset: _currentOffset,
      );

      final newLogs = await LogManagementService.getLogs(filterWithPagination);

      setState(() {
        _logs.addAll(newLogs);
        _currentOffset += newLogs.length;
        _hasMoreData = newLogs.length == _pageSize;
        _isLoadingMore = false;
      });

      if (kDebugMode) {
        debugPrint('Đã tải thêm ${newLogs.length} logs');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Lỗi khi tải thêm logs: $e');
      }
      setState(() => _isLoadingMore = false);
      _showErrorSnackBar('Không thể tải thêm logs');
    }
  }

  /// Refresh logs
  Future<void> _refreshLogs() async {
    // Reset animation
    _fabAnimationController.reset();

    await _loadLogs();
  }

  /// Search logs
  Future<void> _searchLogs(String query) async {
    try {
      setState(() {
        _isLoading = true;
        _currentOffset = 0;
        _hasMoreData = true;
      });

      List<LogEntry> results;
      if (query.isEmpty) {
        final filterWithPagination = _currentFilter.copyWith(
          limit: _pageSize,
          offset: 0,
        );
        results = await LogManagementService.getLogs(filterWithPagination);
      } else {
        results = await LogManagementService.searchLogs(query);
      }

      setState(() {
        _logs = results;
        _isLoading = false;
        _currentOffset = results.length;
        _hasMoreData = query.isEmpty ? results.length == _pageSize : false;
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Lỗi khi tìm kiếm logs: $e');
      }
      setState(() => _isLoading = false);
      _showErrorSnackBar('Không thể tìm kiếm logs');
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode
          ? AppColors.backgroundDark
          : AppColors.backgroundPrimary,
      appBar: _buildAppBar(context, isDarkMode),
      body: _buildBody(context, isDarkMode),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, bool isDarkMode) {
    return AppBar(
      title: Text(
        'Log Management',
        style: AppTypography.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColors.textOnPrimary,
        ),
      ),
      centerTitle: true, // Căn giữa title
      backgroundColor: isDarkMode
          ? AppColors.backgroundDarkSecondary
          : AppColors.kienlongOrange,
      foregroundColor: AppColors.textOnPrimary,
      elevation: AppDimensions.cardElevation,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      actions: [
        // Quick filter button
        Container(
          margin: EdgeInsets.only(right: AppDimensions.paddingM),
          child: IconButton(
            onPressed: _showQuickFilter,
            icon: Stack(
              children: [
                Icon(
                  TablerIcons.filter,
                  color: AppColors.textOnPrimary,
                  size: AppDimensions.iconM,
                ),
                if (_currentFilter.isNotEmpty)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: AppColors.warning,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
            tooltip: 'Quick Filter',
            style: IconButton.styleFrom(
              backgroundColor: AppColors.textOnPrimary.withValues(alpha: 0.1),
              padding: EdgeInsets.all(AppDimensions.paddingS),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody(BuildContext context, bool isDarkMode) {
    return Column(
      children: [
        // Search bar
        _buildSearchSection(context, isDarkMode),

        // Logs list
        Expanded(
          child: _isLoading
              ? _buildLoadingState(context)
              : _logs.isEmpty
              ? _buildEmptyState(context, isDarkMode)
              : _buildLogsList(context, isDarkMode),
        ),
      ],
    );
  }

  Widget _buildSearchSection(BuildContext context, bool isDarkMode) {
    return Container(
      margin: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BankingTheme.balanceDisplayDecoration.copyWith(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
      ),
      child: TextField(
        controller: _searchController,
        style: AppTypography.textTheme.bodyMedium?.copyWith(
          color: isDarkMode ? AppColors.textOnPrimary : AppColors.textPrimary,
        ),
        decoration: InputDecoration(
          hintText: 'Tìm kiếm trong logs...',
          hintStyle: AppTypography.textTheme.bodyMedium?.copyWith(
            color: isDarkMode
                ? AppColors.textSecondary
                : AppColors.textTertiary,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              TablerIcons.search,
              color: AppColors.kienlongOrange,
              size: AppDimensions.iconS,
            ),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    TablerIcons.x,
                    color: isDarkMode
                        ? AppColors.textSecondary
                        : AppColors.textSecondary,
                    size: AppDimensions.iconS,
                  ),
                  onPressed: () {
                    _searchController.clear();
                    _searchQuery = '';
                    _searchLogs('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.transparent,
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingM,
            vertical: AppDimensions.paddingS,
          ),
        ),
        onChanged: (value) {
          _searchQuery = value;
          _searchLogs(value);
        },
      ),
    );
  }

  Widget _buildStatsCard(BuildContext context, bool isDarkMode) {
    return LogStatsCard(stats: _stats!);
  }

  Widget _buildLoadingState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingXL),
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusXXL),
            ),
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.kienlongOrange,
              ),
              strokeWidth: AppDimensions.progressIndicatorStrokeWidth,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),
          Text(
            'Đang tải logs...',
            style: AppTypography.textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Vui lòng đợi trong giây lát',
            style: AppTypography.textTheme.bodySmall?.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, bool isDarkMode) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingXL),
            decoration: BoxDecoration(
              color: AppColors.neutral100.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(AppDimensions.radiusXXL),
            ),
            child: Icon(
              TablerIcons.file_text,
              size: AppDimensions.iconXXL,
              color: AppColors.textTertiary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),
          Text(
            'Không có logs nào',
            style: AppTypography.textTheme.headlineSmall?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Hãy thử thay đổi bộ lọc hoặc\ntạo một số hoạt động để tạo logs',
            style: AppTypography.textTheme.bodyMedium?.copyWith(
              color: AppColors.textTertiary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppDimensions.spacingXL),
          OutlinedButton.icon(
            onPressed: _refreshLogs,
            icon: Icon(TablerIcons.refresh, size: AppDimensions.iconS),
            label: Text('Làm mới'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.kienlongOrange,
              side: BorderSide(color: AppColors.kienlongOrange),
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingL,
                vertical: AppDimensions.paddingM,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogsList(BuildContext context, bool isDarkMode) {
    return RefreshIndicator(
      onRefresh: _refreshLogs,
      color: AppColors.kienlongOrange,
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.all(AppDimensions.paddingM),
        itemCount: _stats != null
            ? _logs.length + 1 + (_isLoadingMore ? 1 : 0)
            : _logs.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          // First item is stats if available
          if (_stats != null && index == 0) {
            return _buildStatsCard(context, isDarkMode);
          }

          // Adjust index for logs
          final logIndex = _stats != null ? index - 1 : index;

          // Show loading indicator at the end
          if (logIndex >= _logs.length) {
            return _buildLoadingMoreIndicator();
          }

          final log = _logs[logIndex];
          return _buildLogItem(log, isDarkMode, logIndex);
        },
      ),
    );
  }

  Widget _buildLogItem(LogEntry log, bool isDarkMode, int index) {
    return LogItemWidget(
      log: log,
      index: index,
      onTap: () => _showLogDetails(log),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _fabAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _fabAnimation.value,
          child: FloatingActionButton.extended(
            onPressed: _showMoreOptions,
            backgroundColor: isDarkMode
                ? AppColors.kienlongOrange
                : AppColors.kienlongOrange,
            foregroundColor: AppColors.textOnPrimary,
            elevation: AppDimensions.cardElevation,
            icon: Icon(TablerIcons.menu_2),
            label: Text(
              'Tùy chọn',
              style: AppTypography.textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textOnPrimary,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.kienlongOrange),
          strokeWidth: 2,
        ),
      ),
    );
  }

  // Action methods
  void _showQuickFilter() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => QuickFilterModal(
        currentFilter: _currentFilter,
        onFilterSelected: (filter) {
          setState(() {
            _currentFilter = filter;
          });
          _loadLogs();
        },
      ),
    );
  }

  void _showLogDetails(LogEntry log) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => LogDetailModal(log: log),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => LogOptionsModal(
        onExport: () {
          Navigator.of(context).pop();
          _exportLogs();
        },
        onClear: () {
          Navigator.of(context).pop();
          _clearLogs();
        },
        onRefresh: () {
          Navigator.of(context).pop();
          _refreshLogs();
        },
        onSettings: () {
          Navigator.of(context).pop();
          _showLogSettings();
        },
      ),
    );
  }

  void _exportLogs() {
    _showInfoSnackBar('Tính năng xuất logs đang được phát triển');
  }

  void _clearLogs() {
    showDialog(
      context: context,
      builder: (context) => LogClearDialog(
        onConfirm: () {
          Navigator.of(context).pop();
          _performClearLogs();
        },
      ),
    );
  }

  void _showLogSettings() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const LogSettingsScreen()));
  }

  void _performClearLogs() {
    _showInfoSnackBar('Đã xóa tất cả logs');
    _refreshLogs();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.kienlongOrange,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
