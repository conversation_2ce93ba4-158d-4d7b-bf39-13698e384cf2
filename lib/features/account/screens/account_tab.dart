import 'package:flutter/material.dart';
import '../../../shared/widgets/index.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';

class AccountTab extends StatelessWidget {
  const AccountTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppNavHeaderExtension.forTab(title: 'T<PERSON><PERSON> kho<PERSON>n'),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Info
            const ProfileInfoCard(),
            const SizedBox(height: AppDimensions.spacingL),

            // Performance Stats
            const PerformanceStatsCard(),
            const SizedBox(height: AppDimensions.spacingL),

            // Settings Section
            const SettingsSection(),
            const SizedBox(height: AppDimensions.spacingL),

            // Account Actions
            const AccountActionsSection(),
            const SizedBox(height: AppDimensions.spacingL),

            // Logout Button
            const LogoutButton(),
            const SizedBox(height: AppDimensions.spacingL),

            // App Info Footer
            const AppInfoFooter(),
          ],
        ),
      ),
    );
  }
}
