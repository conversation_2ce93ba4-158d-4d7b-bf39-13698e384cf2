import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/theme/index.dart';
import '../widgets/log_settings_widget.dart';

/// <PERSON><PERSON>n hình cài đặt logging với thiết kế chuyên nghiệp
class LogSettingsScreen extends StatelessWidget {
  const LogSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode
          ? AppColors.backgroundDark
          : AppColors.backgroundPrimary,
      appBar: _buildAppBar(context, isDarkMode),
      body: _buildBody(context, isDarkMode),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, bool isDarkMode) {
    return AppBar(
      title: Text(
        'Cài đặt Logging',
        style: AppTypography.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: AppColors.textOnPrimary,
        ),
      ),
      centerTitle: true,
      backgroundColor: isDarkMode
          ? AppColors.backgroundDarkSecondary
          : AppColors.kienlongOrange,
      foregroundColor: AppColors.textOnPrimary,
      elevation: AppDimensions.cardElevation,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      leading: IconButton(
        icon: Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }

  Widget _buildBody(BuildContext context, bool isDarkMode) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          _buildHeaderSection(context, isDarkMode),
          SizedBox(height: AppDimensions.spacingL),

          // Log settings widget
          LogSettingsWidget(),
          SizedBox(height: AppDimensions.spacingL),

          // Info section
          _buildInfoSection(context, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode ? AppColors.borderDark : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  Icons.settings,
                  color: AppColors.kienlongOrange,
                  size: AppDimensions.iconM,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Cấu hình Logging',
                      style: AppTypography.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isDarkMode
                            ? AppColors.textOnPrimary
                            : AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: AppDimensions.spacingS),
                    Text(
                      'Tùy chỉnh cách ứng dụng ghi lại các hoạt động',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: isDarkMode
                            ? AppColors.textSecondary
                            : AppColors.textTertiary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context, bool isDarkMode) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: isDarkMode ? AppColors.borderDark : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingS),
                decoration: BoxDecoration(
                  color: AppColors.info.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Icon(
                  Icons.info_outline,
                  color: AppColors.info,
                  size: AppDimensions.iconM,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: Text(
                  'Thông tin',
                  style: AppTypography.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDarkMode
                        ? AppColors.textOnPrimary
                        : AppColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),
          _buildInfoItem(
            context,
            isDarkMode,
            icon: Icons.storage,
            title: 'Lưu trữ',
            description:
                'Logs được lưu trữ cục bộ trên thiết bị và có thể được dọn dẹp tự động.',
          ),
          SizedBox(height: AppDimensions.spacingS),
          _buildInfoItem(
            context,
            isDarkMode,
            icon: Icons.security,
            title: 'Bảo mật',
            description:
                'Dữ liệu nhạy cảm sẽ được che giấu tự động để bảo vệ quyền riêng tư.',
          ),
          SizedBox(height: AppDimensions.spacingS),
          _buildInfoItem(
            context,
            isDarkMode,
            icon: Icons.speed,
            title: 'Hiệu suất',
            description:
                'Cài đặt logging có thể ảnh hưởng đến hiệu suất ứng dụng.',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    bool isDarkMode, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: AppDimensions.iconS,
          color: isDarkMode ? AppColors.textSecondary : AppColors.textTertiary,
        ),
        SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isDarkMode
                      ? AppColors.textOnPrimary
                      : AppColors.textPrimary,
                ),
              ),
              SizedBox(height: AppDimensions.spacingXS),
              Text(
                description,
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: isDarkMode
                      ? AppColors.textSecondary
                      : AppColors.textTertiary,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
