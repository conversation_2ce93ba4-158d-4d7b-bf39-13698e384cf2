import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/api/api_service.dart';
import '../models/employee_model.dart';

/// Service để quản lý nhân viên (người phụ trách) từ backend API
class EmployeeService {
  static final EmployeeService _instance = EmployeeService._internal();
  factory EmployeeService() => _instance;
  EmployeeService._internal();

  // Use getter để đảm bảo lấy ApiService singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();

  // API endpoints
  static const String _getEmployeesEndpoint = '/rest/rpc/get_employee';

  // Cache data
  final Map<String, List<EmployeeModel>> _employeesCache = {};
  DateTime? _lastCacheTime;
  
  // Cache timeout trong phút
  static const int _cacheTimeoutMinutes = 10;

  /// Lấy danh sách nhân viên (người phụ trách)
  Future<List<EmployeeModel>> getEmployees({String? keySearch}) async {
    try {
      _logger.i('=== START: EmployeeService.getEmployees ===');
      _logger.i('Key search: $keySearch');
      _logger.i('API endpoint: $_getEmployeesEndpoint');

      // Check cache
      final cacheKey = keySearch ?? 'all';
      if (_isCacheValid() && _employeesCache.containsKey(cacheKey)) {
        _logger.i('Returning cached employees for key: $cacheKey');
        return _employeesCache[cacheKey]!;
      }

      final Map<String, dynamic> params = {};
      if (keySearch != null && keySearch.isNotEmpty) {
        params['p_keysearch'] = keySearch;
      }

      _logger.i('Request params: $params');

      final response = await _apiService.post(
        _getEmployeesEndpoint,
        data: params.isNotEmpty ? params : null,
      );

      _logger.i('API response received');
      _logger.i('Response status: ${response.statusCode}');
      _logger.i('Response data type: ${response.data.runtimeType}');

      // Response có thể là array trực tiếp hoặc object chứa data.employees
      List<dynamic> employeesData;
      
      if (response.data is List) {
        // Trường hợp response.data là array trực tiếp
        employeesData = response.data as List;
        _logger.i('Response data is direct array with ${employeesData.length} items');
      } else if (response.data is Map) {
        // Trường hợp response.data là object chứa data.employees
        final dataMap = response.data as Map<String, dynamic>;
        
        // Kiểm tra cả 2 trường hợp:
        // 1. response.data.employees (trực tiếp)
        // 2. response.data.data.employees (nested trong data)
        if (dataMap.containsKey('employees') && dataMap['employees'] is List) {
          // Trường hợp 1: response.data.employees
          employeesData = dataMap['employees'] as List;
          _logger.i('Found employees array directly in response.data.employees with ${employeesData.length} items');
        } else if (dataMap.containsKey('data') && dataMap['data'] is Map) {
          // Trường hợp 2: response.data.data.employees
          final nestedData = dataMap['data'] as Map<String, dynamic>;
          
          if (nestedData.containsKey('employees') && nestedData['employees'] is List) {
            employeesData = nestedData['employees'] as List;
            _logger.i('Found employees array in response.data.data.employees with ${employeesData.length} items');
          } else {
            _logger.e('Nested data does not contain employees array');
            throw EmployeeException(
              message: 'Nested data does not contain employees array',
              type: EmployeeExceptionType.invalidResponse,
            );
          }
        } else {
          _logger.e('Response data is Map but does not contain employees array');
          throw EmployeeException(
            message: 'Response does not contain employees array',
            type: EmployeeExceptionType.invalidResponse,
          );
        }
      } else {
        _logger.e('Invalid response format - expected List or Map but got ${response.data.runtimeType}');
        throw EmployeeException(
          message: 'Invalid response format for employees',
          type: EmployeeExceptionType.invalidResponse,
        );
      }
      
      final employees = employeesData.map((item) => EmployeeModel.fromJson(item)).toList();
      
      // Cache kết quả theo keySearch
      _employeesCache[cacheKey] = employees;
      _updateCacheTime();
      
      _logger.i('Employees parsed successfully: ${employees.length} items');
      _logger.i('=== END: EmployeeService.getEmployees ===');
      return employees;
    } on ApiException catch (e) {
      _logger.e('API error when fetching employees: ${e.message}');
      throw EmployeeException(
        message: 'Không thể lấy danh sách nhân viên: ${e.message}',
        type: EmployeeExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when fetching employees: $e');
      throw EmployeeException(
        message: 'Lỗi không xác định khi lấy danh sách nhân viên',
        type: EmployeeExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy danh sách nhân viên theo key search
  Future<List<EmployeeModel>> searchEmployees(String keySearch) async {
    return await getEmployees(keySearch: keySearch);
  }

  /// Lấy tất cả nhân viên
  Future<List<EmployeeModel>> getAllEmployees() async {
    return await getEmployees();
  }

  /// Lấy cache employees theo key
  List<EmployeeModel>? getCachedEmployees(String keySearch) {
    final cacheKey = keySearch.isEmpty ? 'all' : keySearch;
    return _employeesCache[cacheKey];
  }

  /// Lấy cache tất cả employees
  List<EmployeeModel>? getCachedAllEmployees() {
    return _employeesCache['all'];
  }

  /// Clear cache
  void clearCache() {
    _employeesCache.clear();
    _lastCacheTime = null;
    _logger.i('Employee cache cleared');
  }

  /// Refresh employee data
  Future<void> refreshEmployeeData() async {
    try {
      await getAllEmployees();
      _logger.i('Employee data refreshed successfully');
    } catch (e) {
      _logger.w('Failed to refresh employee data: $e');
    }
  }

  /// Kiểm tra tính khả dụng của employee API
  Future<bool> checkEmployeeApiAvailability() async {
    try {
      await getAllEmployees();
      return true;
    } catch (e) {
      _logger.w('Employee API not available: $e');
      return false;
    }
  }

  // Private helper methods

  /// Kiểm tra cache có hợp lệ không
  bool _isCacheValid() {
    if (_lastCacheTime == null) return false;
    
    final now = DateTime.now();
    final difference = now.difference(_lastCacheTime!);
    return difference.inMinutes < _cacheTimeoutMinutes;
  }

  /// Cập nhật thời gian cache
  void _updateCacheTime() {
    _lastCacheTime = DateTime.now();
  }
}

/// Custom exception cho Employee service
class EmployeeException implements Exception {
  final String message;
  final EmployeeExceptionType type;
  final Object? originalException;

  const EmployeeException({
    required this.message,
    required this.type,
    this.originalException,
  });

  @override
  String toString() => 'EmployeeException: $message (Type: $type)';
}

/// Loại lỗi Employee
enum EmployeeExceptionType {
  notFound,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
}
