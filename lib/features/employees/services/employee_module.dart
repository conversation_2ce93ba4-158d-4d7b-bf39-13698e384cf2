import '../../../shared/core/feature_module.dart';
import 'employee_service.dart';

/// Employee Feature Module
/// Organizes employee-related services and provides public interface
class EmployeeModule extends FeatureModule with SingletonFeatureModule<EmployeeModule> {
  static EmployeeModule? _instance;
  bool _initialized = false;

  /// Singleton instance getter
  static EmployeeModule get instance {
    return _instance ??= EmployeeModule._internal();
  }

  EmployeeModule._internal();

  @override
  String get featureName => 'employee';

  @override
  bool get isInitialized => _initialized;

  // Feature-scoped service getters (internal singletons)
  EmployeeService get employeeService => EmployeeService();

  @override
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      logInfo('Initializing employee module...');
      
      // Employee service không cần initialize đặc biệt
      // Chỉ cần đảm bảo service locator đã sẵn sàng
      
      _initialized = true;
      logInfo('Employee module initialized successfully');
      
    } catch (e) {
      logError('Employee module initialization failed', e);
      rethrow;
    }
  }

  @override
  void dispose() {
    try {
      logInfo('Disposing employee module...');
      
      // Clear cache khi dispose
      employeeService.clearCache();
      
      _initialized = false;
      logInfo('Employee module disposed');
      
    } catch (e) {
      logError('Employee module disposal error', e);
    }
  }

  /// For testing - reset module state
  static void resetForTesting() {
    _instance?._initialized = false;
    _instance = null;
  }
}
