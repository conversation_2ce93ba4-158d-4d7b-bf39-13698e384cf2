import 'package:json_annotation/json_annotation.dart';

part 'employee_model.g.dart';

/// Model cho nhân viên (người phụ trách)
@JsonSerializable()
class EmployeeModel {
  @J<PERSON><PERSON>ey(name: 'id')
  final String? id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'full_name')
  final String? fullName;

  @J<PERSON><PERSON><PERSON>(name: 'username')
  final String? username;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'email')
  final String? email;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'cif_no')
  final String? cifNo;

  const EmployeeModel({
    this.id,
    this.fullName,
    this.username,
    this.email,
    this.cifNo,
  });

  /// Tạo từ JSON
  factory EmployeeModel.fromJson(Map<String, dynamic> json) => _$EmployeeModelFromJson(json);

  /// Chuyển thành JSON
  Map<String, dynamic> toJson() => _$EmployeeModelToJson(this);

  /// Copy với thay đổi
  EmployeeModel copyWith({
    String? id,
    String? fullName,
    String? username,
    String? email,
    String? cifNo,
  }) {
    return EmployeeModel(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      username: username ?? this.username,
      email: email ?? this.email,
      cifNo: cifNo ?? this.cifNo,
    );
  }

  @override
  String toString() {
    return 'EmployeeModel(id: $id, fullName: $fullName, username: $username, email: $email, cifNo: $cifNo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmployeeModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
