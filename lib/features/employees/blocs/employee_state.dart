import 'package:equatable/equatable.dart';
import '../models/employee_model.dart';

/// Base class cho tất cả Employee states
abstract class EmployeeState extends Equatable {
  const EmployeeState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class EmployeeInitial extends EmployeeState {
  const EmployeeInitial();

  @override
  List<Object?> get props => [];
}

/// Loading state
class EmployeeLoading extends EmployeeState {
  const EmployeeLoading();

  @override
  List<Object?> get props => [];
}

/// Loaded state với tất cả employees
class AllEmployeesLoaded extends EmployeeState {
  final List<EmployeeModel> employees;
  final DateTime lastUpdated;

  const AllEmployeesLoaded({
    required this.employees,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [employees, lastUpdated];
}

/// Loaded state với search results
class EmployeesSearchLoaded extends EmployeeState {
  final List<EmployeeModel> employees;
  final String searchTerm;
  final DateTime lastUpdated;

  const EmployeesSearchLoaded({
    required this.employees,
    required this.searchTerm,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [employees, searchTerm, lastUpdated];
}

/// Loaded state với single employee
class SingleEmployeeLoaded extends EmployeeState {
  final EmployeeModel employee;
  final DateTime lastUpdated;

  const SingleEmployeeLoaded({
    required this.employee,
    required this.lastUpdated,
  });

  @override
  List<Object?> get props => [employee, lastUpdated];
}

/// Error state
class EmployeeError extends EmployeeState {
  final String message;
  final Object? error;

  const EmployeeError({
    required this.message,
    this.error,
  });

  @override
  List<Object?> get props => [message, error];
}

/// Empty state (không có kết quả)
class EmployeeEmpty extends EmployeeState {
  final String message;

  const EmployeeEmpty({
    this.message = 'Không tìm thấy nhân viên nào',
  });

  @override
  List<Object?> get props => [message];
}

/// API availability check state
class EmployeeApiAvailabilityChecked extends EmployeeState {
  final bool isAvailable;
  final String message;

  const EmployeeApiAvailabilityChecked({
    required this.isAvailable,
    required this.message,
  });

  @override
  List<Object?> get props => [isAvailable, message];
}

/// Cache cleared state
class EmployeeCacheCleared extends EmployeeState {
  final String message;

  const EmployeeCacheCleared({
    this.message = 'Cache đã được xóa',
  });

  @override
  List<Object?> get props => [message];
}
