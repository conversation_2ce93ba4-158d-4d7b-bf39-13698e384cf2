import 'package:equatable/equatable.dart';

/// Base class cho tất cả Employee events
abstract class EmployeeEvent extends Equatable {
  const EmployeeEvent();

  @override
  List<Object?> get props => [];
}

/// Event để load tất cả employees
class LoadAllEmployees extends EmployeeEvent {
  const LoadAllEmployees();

  @override
  List<Object?> get props => [];
}

/// Event để search employees
class SearchEmployees extends EmployeeEvent {
  final String searchTerm;

  const SearchEmployees(this.searchTerm);

  @override
  List<Object?> get props => [searchTerm];
}

/// Event để load employee theo ID
class LoadEmployeeById extends EmployeeEvent {
  final String employeeId;

  const LoadEmployeeById(this.employeeId);

  @override
  List<Object?> get props => [employeeId];
}

/// Event để load employee theo username
class LoadEmployeeByUsername extends EmployeeEvent {
  final String username;

  const LoadEmployeeByUsername(this.username);

  @override
  List<Object?> get props => [username];
}

/// Event để load employee theo CIF number
class LoadEmployeeByCifNo extends EmployeeEvent {
  final String cifNo;

  const LoadEmployeeByCifNo(this.cifNo);

  @override
  List<Object?> get props => [cifNo];
}

/// Event để load employee theo email
class LoadEmployeeByEmail extends EmployeeEvent {
  final String email;

  const LoadEmployeeByEmail(this.email);

  @override
  List<Object?> get props => [email];
}

/// Event để refresh employee data
class RefreshEmployees extends EmployeeEvent {
  const RefreshEmployees();

  @override
  List<Object?> get props => [];
}

/// Event để clear employee cache
class ClearEmployeeCache extends EmployeeEvent {
  const ClearEmployeeCache();

  @override
  List<Object?> get props => [];
}

/// Event để check API availability
class CheckEmployeeApiAvailability extends EmployeeEvent {
  const CheckEmployeeApiAvailability();

  @override
  List<Object?> get props => [];
}
