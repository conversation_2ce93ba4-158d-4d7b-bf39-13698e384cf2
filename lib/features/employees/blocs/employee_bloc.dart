import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import '../services/employee_service.dart';
import 'employee_event.dart';
import 'employee_state.dart';

/// BLoC để quản lý state của Employee feature
class EmployeeBloc extends Bloc<EmployeeEvent, EmployeeState> {
  final EmployeeService _employeeService;
  IAppLogger get _logger => getIt.get<IAppLogger>();

  EmployeeBloc({EmployeeService? employeeService})
      : _employeeService = employeeService ?? EmployeeService(),
        super(const EmployeeInitial()) {
    
    on<LoadAllEmployees>(_onLoadAllEmployees);
    on<SearchEmployees>(_onSearchEmployees);
    on<LoadEmployeeById>(_onLoadEmployeeById);
    on<LoadEmployeeByUsername>(_onLoadEmployeeByUsername);
    on<LoadEmployeeByCifNo>(_onLoadEmployeeByCifNo);
    on<LoadEmployeeByEmail>(_onLoadEmployeeByEmail);
    on<RefreshEmployees>(_onRefreshEmployees);
    on<ClearEmployeeCache>(_onClearEmployeeCache);
    on<CheckEmployeeApiAvailability>(_onCheckEmployeeApiAvailability);
  }

  /// Xử lý LoadAllEmployees event
  Future<void> _onLoadAllEmployees(
    LoadAllEmployees event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onLoadAllEmployees ===');
      
      emit(const EmployeeLoading());
      
      final employees = await _employeeService.getAllEmployees();
      
      if (employees.isEmpty) {
        emit(const EmployeeEmpty());
      } else {
        emit(AllEmployeesLoaded(
          employees: employees,
          lastUpdated: DateTime.now(),
        ));
      }
      
      _logger.i('All employees loaded successfully: ${employees.length} items');
      _logger.i('=== END: EmployeeBloc._onLoadAllEmployees ===');
      
    } catch (e) {
      _logger.e('Error loading all employees: $e');
      emit(EmployeeError(
        message: 'Không thể tải danh sách nhân viên: $e',
        error: e,
      ));
    }
  }

  /// Xử lý SearchEmployees event
  Future<void> _onSearchEmployees(
    SearchEmployees event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onSearchEmployees ===');
      _logger.i('Search term: ${event.searchTerm}');
      
      emit(const EmployeeLoading());
      
      final employees = await _employeeService.searchEmployees(event.searchTerm);
      
      if (employees.isEmpty) {
        emit(EmployeeEmpty(
          message: 'Không tìm thấy nhân viên nào với từ khóa: ${event.searchTerm}',
        ));
      } else {
        emit(EmployeesSearchLoaded(
          employees: employees,
          searchTerm: event.searchTerm,
          lastUpdated: DateTime.now(),
        ));
      }
      
      _logger.i('Search completed: ${employees.length} results found');
      _logger.i('=== END: EmployeeBloc._onSearchEmployees ===');
      
    } catch (e) {
      _logger.e('Error searching employees: $e');
      emit(EmployeeError(
        message: 'Không thể tìm kiếm nhân viên: $e',
        error: e,
      ));
    }
  }

  /// Xử lý LoadEmployeeById event
  Future<void> _onLoadEmployeeById(
    LoadEmployeeById event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onLoadEmployeeById ===');
      _logger.i('Employee ID: ${event.employeeId}');
      
      emit(const EmployeeLoading());
      
      // Load all employees first, then find by ID
      final allEmployees = await _employeeService.getAllEmployees();
      final employee = allEmployees.firstWhere(
        (emp) => emp.id == event.employeeId,
        orElse: () => throw Exception('Employee not found'),
      );
      
      emit(SingleEmployeeLoaded(
        employee: employee,
        lastUpdated: DateTime.now(),
      ));
      
      _logger.i('Employee by ID loaded: ${employee.fullName}');
      _logger.i('=== END: EmployeeBloc._onLoadAllEmployees ===');
      
    } catch (e) {
      _logger.e('Error loading employee by ID: $e');
      emit(EmployeeError(
        message: 'Không thể tải thông tin nhân viên: $e',
        error: e,
      ));
    }
  }

  /// Xử lý LoadEmployeeByUsername event
  Future<void> _onLoadEmployeeByUsername(
    LoadEmployeeByUsername event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onLoadEmployeeByUsername ===');
      _logger.i('Username: ${event.username}');
      
      emit(const EmployeeLoading());
      
      // Load all employees first, then find by username
      final allEmployees = await _employeeService.getAllEmployees();
      final employee = allEmployees.firstWhere(
        (emp) => emp.username == event.username,
        orElse: () => throw Exception('Employee not found'),
      );
      
      emit(SingleEmployeeLoaded(
        employee: employee,
        lastUpdated: DateTime.now(),
      ));
      
      _logger.i('Employee by username loaded: ${employee.fullName}');
      _logger.i('=== END: EmployeeBloc._onLoadEmployeeByUsername ===');
      
    } catch (e) {
      _logger.e('Error loading employee by username: $e');
      emit(EmployeeError(
        message: 'Không thể tải thông tin nhân viên: ${event.username}',
        error: e,
      ));
    }
  }

  /// Xử lý LoadEmployeeByCifNo event
  Future<void> _onLoadEmployeeByCifNo(
    LoadEmployeeByCifNo event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onLoadEmployeeByCifNo ===');
      _logger.i('CIF No: ${event.cifNo}');
      
      emit(const EmployeeLoading());
      
      // Load all employees first, then find by CIF
      final allEmployees = await _employeeService.getAllEmployees();
      final employee = allEmployees.firstWhere(
        (emp) => emp.cifNo == event.cifNo,
        orElse: () => throw Exception('Employee not found'),
      );
      
      emit(SingleEmployeeLoaded(
        employee: employee,
        lastUpdated: DateTime.now(),
      ));
      
      _logger.i('Employee by CIF loaded: ${employee.fullName}');
      _logger.i('=== END: EmployeeBloc._onLoadEmployeeByCifNo ===');
      
    } catch (e) {
      _logger.e('Error loading employee by CIF: $e');
      emit(EmployeeError(
        message: 'Không thể tải thông tin nhân viên: $e',
        error: e,
      ));
    }
  }

  /// Xử lý LoadEmployeeByEmail event
  Future<void> _onLoadEmployeeByEmail(
    LoadEmployeeByEmail event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onLoadEmployeeByEmail ===');
      _logger.i('Email: ${event.email}');
      
      emit(const EmployeeLoading());
      
      // Load all employees first, then find by email
      final allEmployees = await _employeeService.getAllEmployees();
      final employee = allEmployees.firstWhere(
        (emp) => emp.email == event.email,
        orElse: () => throw Exception('Employee not found'),
      );
      
      emit(SingleEmployeeLoaded(
        employee: employee,
        lastUpdated: DateTime.now(),
        
      ));
      
      _logger.i('Employee by email loaded: ${employee.fullName}');
      _logger.i('=== END: EmployeeBloc._onLoadEmployeeByEmail ===');
      
    } catch (e) {
      _logger.e('Error loading employee by email: $e');
      emit(EmployeeError(
        message: 'Không thể tải thông tin nhân viên: $e',
        error: e,
      ));
    }
  }

  /// Xử lý RefreshEmployees event
  Future<void> _onRefreshEmployees(
    RefreshEmployees event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onRefreshEmployees ===');
      
      await _employeeService.refreshEmployeeData();
      
      // Reload all employees after refresh
      final employees = await _employeeService.getAllEmployees();
      
      if (employees.isEmpty) {
        emit(const EmployeeEmpty());
      } else {
        emit(AllEmployeesLoaded(
          employees: employees,
          lastUpdated: DateTime.now(),
        ));
      }
      
      _logger.i('Employees refreshed successfully: ${employees.length} items');
      _logger.i('=== END: EmployeeBloc._onRefreshEmployees ===');
      
    } catch (e) {
      _logger.e('Error refreshing employees: $e');
      emit(EmployeeError(
        message: 'Không thể làm mới danh sách nhân viên: $e',
        error: e,
      ));
    }
  }

  /// Xử lý ClearEmployeeCache event
  Future<void> _onClearEmployeeCache(
    ClearEmployeeCache event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onClearEmployeeCache ===');
      
      _employeeService.clearCache();
      
      emit(const EmployeeCacheCleared());
      
      _logger.i('Employee cache cleared successfully');
      _logger.i('=== END: EmployeeBloc._onClearEmployeeCache ===');
      
    } catch (e) {
      _logger.e('Error clearing employee cache: $e');
      emit(EmployeeError(
        message: 'Không thể xóa cache: $e',
        error: e,
      ));
    }
  }

  /// Xử lý CheckEmployeeApiAvailability event
  Future<void> _onCheckEmployeeApiAvailability(
    CheckEmployeeApiAvailability event,
    Emitter<EmployeeState> emit,
  ) async {
    try {
      _logger.i('=== START: EmployeeBloc._onCheckEmployeeApiAvailability ===');
      
      final isAvailable = await _employeeService.checkEmployeeApiAvailability();
      
      final message = isAvailable 
        ? 'Employee API đang hoạt động bình thường'
        : 'Employee API không khả dụng';
      
      emit(EmployeeApiAvailabilityChecked(
        isAvailable: isAvailable,
        message: message,
      ));
      
      _logger.i('API availability checked: $isAvailable');
      _logger.i('=== END: EmployeeBloc._onCheckEmployeeApiAvailability ===');
      
    } catch (e) {
      _logger.e('Error checking API availability: $e');
      emit(EmployeeError(
        message: 'Không thể kiểm tra tính khả dụng của API: $e',
        error: e,
      ));
    }
  }

  @override
  Future<void> close() {
    _logger.i('EmployeeBloc closed');
    return super.close();
  }
}
