class DashboardStats {
  final String userCif;
  final String userType;
  final int totalCustomers;
  final int newCustomersThisWeek;
  final int totalProposals;
  final int rejectedProposals;
  final int totalRevenue;
  final int revenueThisWeek;

  const DashboardStats({
    required this.userCif,
    required this.userType,
    required this.totalCustomers,
    required this.newCustomersThisWeek,
    required this.totalProposals,
    required this.rejectedProposals,
    required this.totalRevenue,
    required this.revenueThisWeek,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      userCif: json['user_cif'] ?? '',
      userType: json['user_type'] ?? '',
      totalCustomers: json['total_customers'] ?? 0,
      newCustomersThisWeek: json['new_customers_this_week'] ?? 0,
      totalProposals: json['total_proposals'] ?? 0,
      rejectedProposals: json['rejected_proposals'] ?? 0,
      totalRevenue: json['total_revenue'] ?? 0,
      revenueThisWeek: json['revenue_this_week'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_cif': userCif,
      'user_type': userType,
      'total_customers': totalCustomers,
      'new_customers_this_week': newCustomersThisWeek,
      'total_proposals': totalProposals,
      'rejected_proposals': rejectedProposals,
      'total_revenue': totalRevenue,
      'revenue_this_week': revenueThisWeek,
    };
  }

  @override
  String toString() {
    return 'DashboardStats(userCif: $userCif, userType: $userType, totalCustomers: $totalCustomers, newCustomersThisWeek: $newCustomersThisWeek, totalProposals: $totalProposals, rejectedProposals: $rejectedProposals, totalRevenue: $totalRevenue, revenueThisWeek: $revenueThisWeek)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DashboardStats &&
        other.userCif == userCif &&
        other.userType == userType &&
        other.totalCustomers == totalCustomers &&
        other.newCustomersThisWeek == newCustomersThisWeek &&
        other.totalProposals == totalProposals &&
        other.rejectedProposals == rejectedProposals &&
        other.totalRevenue == totalRevenue &&
        other.revenueThisWeek == revenueThisWeek;
  }

  @override
  int get hashCode {
    return userCif.hashCode ^
        userType.hashCode ^
        totalCustomers.hashCode ^
        newCustomersThisWeek.hashCode ^
        totalProposals.hashCode ^
        rejectedProposals.hashCode ^
        totalRevenue.hashCode ^
        revenueThisWeek.hashCode;
  }
} 