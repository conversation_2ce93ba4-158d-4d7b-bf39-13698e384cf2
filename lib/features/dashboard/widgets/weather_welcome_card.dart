import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import '../../../core/theme/index.dart';
import '../../../shared/services/location_service.dart';
import '../../../shared/services/weather_service.dart';
import '../../../shared/models/weather_data.dart';

class WeatherWelcomeCard extends StatefulWidget {
  const WeatherWelcomeCard({super.key});

  @override
  State<WeatherWelcomeCard> createState() => _WeatherWelcomeCardState();
}

class _WeatherWelcomeCardState extends State<WeatherWelcomeCard> {
  WeatherData? _weatherData;
  bool _isLoadingWeather = true;
  String? _locationError;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _loadWeatherData();
  }

  Future<void> _loadWeatherData() async {
    setState(() {
      _isLoadingWeather = true;
      _locationError = null;
    });

    try {
      // Thử lấy vị trí hiện tại
      final position = await LocationService.getCurrentLocation();
      
      if (position != null) {
        // Có vị trí, lấy thời tiết theo vị trí
        _currentPosition = position;
        final weatherData = await WeatherService().getCurrentWeatherByLocation(
          position.latitude,
          position.longitude,
        );
        setState(() {
          _weatherData = weatherData;
          _isLoadingWeather = false;
        });
      } else {
        // Không có vị trí, fallback về Hà Nội
        _locationError = 'Không thể lấy vị trí hiện tại';
        final weatherData = await WeatherService().getHanoiWeather();
        setState(() {
          _weatherData = weatherData;
          _isLoadingWeather = false;
        });
      }
    } catch (e) {
      // Lỗi, thử fallback về thời tiết mặc định
      try {
        final weatherData = await WeatherService().getCurrentWeather();
        setState(() {
          _weatherData = weatherData;
          _isLoadingWeather = false;
          _locationError = 'Sử dụng vị trí mặc định';
        });
      } catch (fallbackError) {
        setState(() {
          _isLoadingWeather = false;
          _locationError = 'Không thể lấy thông tin thời tiết';
        });
      }
    }
  }

  String _getGreetingMessage() {
    final hour = DateTime.now().hour;
    if (hour >= 5 && hour < 12) {
      return 'Chào buổi sáng!';
    } else if (hour >= 12 && hour < 18) {
      return 'Chào buổi chiều!';
    } else {
      return 'Chào buổi tối!';
    }
  }

  IconData _getGreetingIcon() {
    final hour = DateTime.now().hour;
    if (hour >= 5 && hour < 12) {
      return TablerIcons.sun;
    } else if (hour >= 12 && hour < 18) {
      return TablerIcons.sun_high;
    } else {
      return TablerIcons.moon;
    }
  }

  // Lấy gradient colors theo thời gian trong ngày và dark/light theme
  List<Color> _getTimeBasedGradient(BuildContext context) {
    final hour = DateTime.now().hour;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (isDarkMode) {
      // Dark theme gradients - more muted and darker
      if (hour >= 5 && hour < 8) {
        // Sáng sớm: Dark sunrise gradient
        return [
          AppColors.kienlongDarkBlue,
          AppColors.neutral800,
          AppColors.kienlongDarkBlue.withValues(alpha: 0.9),
        ];
      } else if (hour >= 8 && hour < 12) {
        // Sáng: Dark blue gradient
        return [
          AppColors.backgroundDarkSecondary,
          AppColors.neutral700,
          AppColors.backgroundDarkTertiary,
        ];
      } else if (hour >= 12 && hour < 17) {
        // Chiều: Dark day gradient
        return [
          AppColors.neutral800,
          AppColors.backgroundDarkSecondary,
          AppColors.neutral700,
        ];
      } else if (hour >= 17 && hour < 19) {
        // Chiều tà: Dark sunset gradient
        return [
          AppColors.kienlongDarkBlue,
          AppColors.neutral800.withValues(alpha: 0.8),
          AppColors.backgroundDarkSecondary,
        ];
      } else {
        // Tối: Deep night gradient
        return [
          AppColors.backgroundDark,
          AppColors.kienlongDarkBlue,
          AppColors.neutral900,
        ];
      }
    } else {
      // Light theme gradients - bright and vibrant
      if (hour >= 5 && hour < 8) {
        // Sáng sớm: Sunrise gradient
        return [
          const Color(0xFFFF9A56), // Cam nhạt
          const Color(0xFFFFAD56), // Cam vàng
          AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
        ];
      } else if (hour >= 8 && hour < 12) {
        // Sáng: Blue sky gradient
        return [
          AppColors.kienlongSkyBlue,
          const Color(0xFF87CEEB), // Sky blue
          AppColors.kienlongSkyBlue.withValues(alpha: 0.7),
        ];
      } else if (hour >= 12 && hour < 17) {
        // Chiều: Bright day gradient
        return [
          const Color(0xFF4A90E2), // Blue
          AppColors.kienlongSkyBlue,
          const Color(0xFF7BB3F0), // Light blue
        ];
      } else if (hour >= 17 && hour < 19) {
        // Chiều tà: Sunset gradient
        return [
          const Color(0xFFFF6B6B), // Coral
          const Color(0xFFFFE066), // Light orange
          AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
        ];
      } else {
        // Tối: Night gradient
        return [
          const Color(0xFF2C3E50), // Dark blue
          const Color(0xFF34495E), // Darker blue
          AppColors.kienlongDarkBlue.withValues(alpha: 0.9),
        ];
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: isDarkMode 
              ? AppColors.shadowDark
              : AppColors.shadowLight,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: isDarkMode 
              ? Colors.black.withValues(alpha: 0.2)
              : Colors.black.withValues(alpha: 0.05),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background với gradient động
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: _getTimeBasedGradient(context),
                  stops: const [0.0, 0.5, 1.0],
                ),
                borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              ),
            ),
          ),
          
          // Geometric pattern background
          Positioned.fill(
            child: CustomPaint(
              painter: _GeometricPatternPainter(isDarkMode: isDarkMode),
            ),
          ),
          
          // Logo watermark
          Positioned(
            right: -20,
            top: -20,
            child: Opacity(
              opacity: isDarkMode ? 0.05 : 0.1,
              child: Transform.rotate(
                angle: 0.2,
                child: SvgPicture.asset(
                  'assets/images/logos/logo.svg',
                  width: 120,
                  height: 120,
                  colorFilter: const ColorFilter.mode(
                    Colors.white,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
          
          // Main content với padding
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row với greeting và refresh button
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getGreetingMessage(),
                            style: AppTypography.textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: isDarkMode ? 4 : 3,
                                  color: isDarkMode 
                                    ? Colors.black.withValues(alpha: 0.5)
                                    : Colors.black26,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Ngày ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                            style: AppTypography.textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.95),
                              shadows: [
                                Shadow(
                                  offset: const Offset(0, 1),
                                  blurRadius: isDarkMode ? 3 : 2,
                                  color: isDarkMode 
                                    ? Colors.black.withValues(alpha: 0.4)
                                    : Colors.black26,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        Icon(
                          _getGreetingIcon(),
                          color: Colors.white,
                          size: 32,
                        ),
                        const SizedBox(width: AppDimensions.spacingS),
                        if (!_isLoadingWeather)
                          GestureDetector(
                            onTap: _loadWeatherData,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: isDarkMode ? 0.1 : 0.15),
                                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                TablerIcons.refresh,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
                
                const SizedBox(height: AppDimensions.spacingM),
                
                // Weather information
                if (_isLoadingWeather)
                  Row(
                    children: [
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      const SizedBox(width: AppDimensions.spacingS),
                      Text(
                        'Đang tải thông tin thời tiết...',
                        style: AppTypography.textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.95),
                          shadows: [
                            Shadow(
                              offset: const Offset(0, 1),
                              blurRadius: isDarkMode ? 3 : 2,
                              color: isDarkMode 
                                ? Colors.black.withValues(alpha: 0.4)
                                : Colors.black26,
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                else if (_weatherData != null)
                  _buildWeatherInfo(isDarkMode)
                else
                  Row(
                    children: [
                      const Icon(
                        TablerIcons.alert_circle,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: AppDimensions.spacingS),
                      Expanded(
                        child: Text(
                          _locationError ?? 'Không thể tải thông tin thời tiết',
                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withValues(alpha: 0.95),
                            shadows: [
                              Shadow(
                                offset: const Offset(0, 1),
                                blurRadius: isDarkMode ? 3 : 2,
                                color: isDarkMode 
                                  ? Colors.black.withValues(alpha: 0.4)
                                  : Colors.black26,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherInfo(bool isDarkMode) {
    if (_weatherData == null) return const SizedBox.shrink();

    final weather = _weatherData!.currentWeather;
    
    return Row(
      children: [
        // Weather icon
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: isDarkMode ? 0.1 : 0.15),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: isDarkMode ? 0.2 : 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            weather.weatherIcon,
            color: Colors.white,
            size: 28,
          ),
        ),
        
        const SizedBox(width: AppDimensions.spacingM),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    weather.temperatureString,
                    style: AppTypography.textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: isDarkMode ? 4 : 3,
                          color: isDarkMode 
                            ? Colors.black.withValues(alpha: 0.5)
                            : Colors.black26,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Expanded(
                    child: Text(
                      weather.weatherDescription,
                      style: AppTypography.textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.95),
                        shadows: [
                          Shadow(
                            offset: const Offset(0, 1),
                            blurRadius: isDarkMode ? 3 : 2,
                            color: isDarkMode 
                              ? Colors.black.withValues(alpha: 0.4)
                              : Colors.black26,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 6),
              
              Row(
                children: [
                  Icon(
                    TablerIcons.wind,
                    color: Colors.white.withValues(alpha: 0.85),
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${weather.windspeedString} ${weather.windDirectionText}',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Colors.white.withValues(alpha: 0.85),
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 1),
                          blurRadius: isDarkMode ? 3 : 2,
                          color: isDarkMode 
                            ? Colors.black.withValues(alpha: 0.4)
                            : Colors.black26,
                        ),
                      ],
                    ),
                  ),
                  
                  if (_locationError == null) ...[
                    const SizedBox(width: AppDimensions.spacingM),
                    Icon(
                      TablerIcons.map_pin,
                      color: Colors.white.withValues(alpha: 0.7),
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _currentPosition != null ? 'Vị trí hiện tại' : 'Hà Nội',
                      style: AppTypography.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withValues(alpha: 0.7),
                        shadows: [
                          Shadow(
                            offset: const Offset(0, 1),
                            blurRadius: isDarkMode ? 3 : 2,
                            color: isDarkMode 
                              ? Colors.black.withValues(alpha: 0.4)
                              : Colors.black26,
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}

// Custom painter cho geometric patterns
class _GeometricPatternPainter extends CustomPainter {
  final bool isDarkMode;
  
  const _GeometricPatternPainter({required this.isDarkMode});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: isDarkMode ? 0.03 : 0.05)
      ..style = PaintingStyle.fill;

    // Vẽ các hình tròn với sizes khác nhau
    canvas.drawCircle(
      Offset(size.width * 0.8, size.height * 0.2),
      30,
      paint,
    );
    
    canvas.drawCircle(
      Offset(size.width * 0.15, size.height * 0.7),
      20,
      paint,
    );
    
    canvas.drawCircle(
      Offset(size.width * 0.9, size.height * 0.8),
      15,
      paint,
    );

    // Vẽ các hình elipse
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.1, size.height * 0.3),
        width: 40,
        height: 20,
      ),
      paint..color = Colors.white.withValues(alpha: isDarkMode ? 0.02 : 0.03),
    );
    
    canvas.drawOval(
      Rect.fromCenter(
        center: Offset(size.width * 0.7, size.height * 0.9),
        width: 60,
        height: 30,
      ),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 