import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../core/theme/index.dart';

class TodayTasksCard extends StatelessWidget {
  const TodayTasksCard({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: isDarkMode ? [
            // Dark theme gradient - more muted
            AppColors.kienlongDarkBlue,
            AppColors.neutral800,
            AppColors.backgroundDarkSecondary,
          ] : [
            // Light theme gradient - bright
            AppColors.kienlongSkyBlue,
            AppColors.kienlongSkyBlue.withValues(alpha: 0.8),
            AppColors.kienlongOrange.withValues(alpha: 0.3),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? AppColors.shadowDark.withValues(alpha: 0.4)
                : AppColors.kienlongSkyBlue.withValues(alpha: 0.4),
            blurRadius: 12,
            offset: const Offset(0, 6),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background Logo (watermark)
          Positioned(
            left: -20,
            top: -20,
            child: Opacity(
              opacity: isDarkMode ? 0.03 : 0.06,
              child: SvgPicture.asset(
                'assets/images/logos/icon.svg',
                width: 120,
                height: 120,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Content
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: isDarkMode ? 0.2 : 0.3),
                      ),
                    ),
                    child: Icon(
                      TablerIcons.calendar_check,
                      color: Colors.white,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Công việc hôm nay',
                          style: AppTypography.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            shadows: isDarkMode ? [
                              const Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3,
                                color: Colors.black54,
                              ),
                            ] : null,
                          ),
                        ),
                        Text(
                          '${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
                          style: AppTypography.textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: isDarkMode ? 0.85 : 0.9),
                            shadows: isDarkMode ? [
                              const Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 2,
                                color: Colors.black54,
                              ),
                            ] : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
          const SizedBox(height: AppDimensions.spacingL),
          _buildTaskItem(
            context,
            icon: TablerIcons.coins,
            title: 'Thu nợ',
            count: 5,
            subtitle: 'khách hàng',
            color: AppColors.success,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildTaskItem(
            context,
            icon: TablerIcons.clock_hour_4,
            title: 'Nhắc nợ',
            count: 3,
            subtitle: 'khách hàng',
            color: AppColors.warning,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildTaskItem(
            context,
            icon: TablerIcons.refresh,
            title: 'Đáo hạn',
            count: 2,
            subtitle: 'hồ sơ',
            color: AppColors.kienlongSkyBlue,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildTaskItem(
            context,
            icon: TablerIcons.phone,
            title: 'Liên hệ',
            count: 8,
            subtitle: 'leads mới',
            color: AppColors.kienlongOrange,
            isDarkMode: isDarkMode,
          ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTaskItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required int count,
    required String subtitle,
    required Color color,
    required bool isDarkMode,
  }) {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to task detail
      },
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          // Làm transparent hơn cho dark mode
          color: Colors.white.withValues(alpha: isDarkMode ? 0.08 : 0.15),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.3),
            width: 1,
          ),
          // Thêm shadow cho dark mode để tạo độ sâu
          boxShadow: isDarkMode ? [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    color,
                    color.withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: isDarkMode ? 0.2 : 0.3),
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: AppDimensions.iconS,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      shadows: isDarkMode ? [
                        const Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black54,
                        ),
                      ] : null,
                    ),
                  ),
                  Text(
                    '$count $subtitle',
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Colors.white.withValues(alpha: isDarkMode ? 0.75 : 0.8),
                      shadows: isDarkMode ? [
                        const Shadow(
                          offset: Offset(0, 1),
                          blurRadius: 2,
                          color: Colors.black54,
                        ),
                      ] : null,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingS,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    Colors.white.withValues(alpha: 0.9),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withValues(alpha: isDarkMode ? 0.2 : 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                count.toString(),
                style: AppTypography.textTheme.labelMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 