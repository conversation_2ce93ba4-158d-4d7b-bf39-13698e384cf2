import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';

class NotificationsCard extends StatelessWidget {
  const NotificationsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
              Row(
                children: [
                  Icon(
                    TablerIcons.bell,
                    color: AppColors.kienlongSkyBlue,
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Text(
                    'Thông báo & Tin nội bộ',
                    style: AppTypography.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '5',
                      style: AppTypography.textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingL),
          _buildNotificationItem(
            context,
            title: 'Chính sách lãi suất mới',
            content: 'Áp dụng từ ngày 1/12/2024, lãi suất tiết kiệm tăng 0.2%/năm',
            time: '2h trước',
            type: NotificationType.policy,
            isUnread: true,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildNotificationItem(
            context,
            title: 'Khuyến mãi tháng 12',
            content: 'Giảm phí chuyển khoản 50% cho khách hàng VIP',
            time: '1 ngày',
            type: NotificationType.promotion,
            isUnread: true,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildNotificationItem(
            context,
            title: 'Bảo trì hệ thống',
            content: 'Hệ thống sẽ bảo trì từ 23:00-02:00 ngày 15/12',
            time: '2 ngày',
            type: NotificationType.system,
            isUnread: false,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          GestureDetector(
            onTap: () {
              // TODO: Navigate to all notifications
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.paddingS,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Xem tất cả thông báo',
                    style: AppTypography.textTheme.bodyMedium?.copyWith(
                      color: AppColors.kienlongSkyBlue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingS),
                  Icon(
                    TablerIcons.arrow_right,
                    color: AppColors.kienlongSkyBlue,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context, {
    required String title,
    required String content,
    required String time,
    required NotificationType type,
    required bool isUnread,
  }) {
    return GestureDetector(
      onTap: () {
        // TODO: Mark as read and navigate to detail
      },
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: isUnread 
              ? _getTypeColor(type).withValues(alpha: 0.05)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isUnread 
                ? _getTypeColor(type).withValues(alpha: 0.2)
                : Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: _getTypeColor(type).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getTypeIcon(type),
                color: _getTypeColor(type),
                size: 16,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: AppTypography.textTheme.bodyMedium?.copyWith(
                            fontWeight: isUnread ? FontWeight.w600 : FontWeight.w500,
                          ),
                        ),
                      ),
                      if (isUnread)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: AppColors.error,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    content,
                    style: AppTypography.textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    time,
                    style: AppTypography.textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.policy:
        return AppColors.kienlongOrange;
      case NotificationType.promotion:
        return AppColors.success;
      case NotificationType.system:
        return AppColors.warning;
      case NotificationType.alert:
        return AppColors.error;
    }
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.policy:
        return TablerIcons.file_text;
      case NotificationType.promotion:
        return TablerIcons.gift;
      case NotificationType.system:
        return TablerIcons.settings;
      case NotificationType.alert:
        return TablerIcons.alert_triangle;
    }
  }
}

enum NotificationType {
  policy,
  promotion,
  system,
  alert,
} 