import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../core/theme/index.dart';
import '../../auth/services/auth_service.dart';
import '../../auth/models/user_profile.dart';

class DashboardHeader extends StatelessWidget implements PreferredSizeWidget {
  const DashboardHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return StreamBuilder<UserProfile?>(
      stream: AuthService().authStateStream.map((state) => state.user?.profile),
      builder: (context, snapshot) {
        final userProfile = snapshot.data;
        final userName = userProfile?.personFullName ?? userProfile?.displayName ?? 'Người dùng';
        final userRole = userProfile?.positionName ?? 'Chuyên viên';
        final avatarUrl = userProfile?.avatar ?? '';
        
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: const SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isDarkMode
                    ? [
                        AppColors.kienlongDarkBlue,
                        AppColors.kienlongDarkBlue.withValues(alpha: 0.9),
                      ]
                    : [
                        AppColors.kienlongOrange,
                        AppColors.kienlongOrange.withValues(alpha: 0.8),
                      ],
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowLight,
                  blurRadius: AppDimensions.shadowBlurRadiusS,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: SafeArea(
              bottom: false,
              child: Stack(
                children: [
                  // Background Logo (mờ)
                  _buildBackgroundLogo(),
                  
                  // Custom Dashboard Content
                  Container(
                    height: kToolbarHeight + 20,
                    padding: EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                    child: Row(
                      children: [
                        // Avatar
                        _buildAvatar(avatarUrl),
                        
                        SizedBox(width: AppDimensions.spacingM),
                        
                        // Greeting and Role
                        Expanded(
                          child: _buildGreetingSection(userName, userRole),
                        ),
                        
                        // Optional action button (notification bell)
                        _buildNotificationButton(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBackgroundLogo() {
    return Positioned(
      right: -20,
      top: -10,
      child: Opacity(
        opacity: 0.1,
        child: SvgPicture.asset(
          'assets/images/logos/icon.svg',
          width: 120,
          height: 120,
          colorFilter: const ColorFilter.mode(
            Colors.white,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(String avatarUrl) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: avatarUrl.isNotEmpty
            ? Image.network(
                avatarUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback to default avatar if network fails
                  return Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.2),
                    ),
                    child: Icon(
                      Icons.person,
                      size: 28,
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.2),
                    ),
                    child: Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              )
            : Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withValues(alpha: 0.2),
                ),
                child: Icon(
                  Icons.person,
                  size: 28,
                  color: Colors.white.withValues(alpha: 0.8),
                ),
              ),
      ),
    );
  }

  Widget _buildGreetingSection(String userName, String userRole) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'Xin chào, $userName',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 2),
        Text(
          userRole,
          style: AppTypography.textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
            fontWeight: FontWeight.w400,
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildNotificationButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withValues(alpha: 0.1),
      ),
      child: IconButton(
        icon: Icon(
          Icons.notifications_outlined,
          color: Colors.white.withValues(alpha: 0.9),
          size: 24,
        ),
        onPressed: () {
          // TODO: Navigate to notifications or show notification panel
        },
        splashRadius: 20,
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(64);
} 