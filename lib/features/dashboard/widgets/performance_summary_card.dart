import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../core/theme/index.dart';

class PerformanceSummaryCard extends StatelessWidget {
  const PerformanceSummaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDarkMode ? [
            // Dark theme gradient - more muted
            AppColors.kienlongDarkBlue,
            AppColors.neutral800,
            AppColors.backgroundDarkSecondary,
          ] : [
            // Light theme gradient - bright
            AppColors.kienlongOrange,
            AppColors.kienlongOrange.withValues(alpha: 0.8),
            AppColors.kienlongSkyBlue.withValues(alpha: 0.6),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
              ? AppColors.shadowDark.withValues(alpha: 0.4)
              : AppColors.kienlongOrange.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background Logo (watermark)
          Positioned(
            right: -30,
            bottom: -30,
            child: Opacity(
              opacity: isDarkMode ? 0.04 : 0.08,
              child: SvgPicture.asset(
                'assets/images/logos/icon.svg',
                width: 140,
                height: 140,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          
          // Content
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: isDarkMode ? 0.2 : 0.3),
                      ),
                    ),
                    child: Icon(
                      TablerIcons.trophy,
                      color: Colors.white,
                      size: AppDimensions.iconM,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Thành tích tháng này',
                          style: AppTypography.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            shadows: isDarkMode ? [
                              const Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 3,
                                color: Colors.black54,
                              ),
                            ] : null,
                          ),
                        ),
                        Text(
                          'Dashboard hiệu suất',
                          style: AppTypography.textTheme.bodySmall?.copyWith(
                            color: Colors.white.withValues(alpha: isDarkMode ? 0.85 : 0.9),
                            shadows: isDarkMode ? [
                              const Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 2,
                                color: Colors.black54,
                              ),
                            ] : null,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.spacingL),
          
          // Progress indicators with white text for gradient background
          _buildProgressItem(
            context,
            title: 'Doanh số',
            current: '450M',
            target: '600M',
            progress: 0.75,
            color: Colors.white,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildProgressItem(
            context,
            title: 'Khách hàng mới',
            current: '23',
            target: '30',
            progress: 0.77,
            color: Colors.white,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildProgressItem(
            context,
            title: 'Hồ sơ duyệt',
            current: '18',
            target: '25',
            progress: 0.72,
            color: Colors.white,
            isDarkMode: isDarkMode,
          ),
          const SizedBox(height: AppDimensions.spacingL),
          
          // Mini visual chart
          _buildMiniVisualChart(context, isDarkMode),
          const SizedBox(height: AppDimensions.spacingL),
          
          // Ranking info
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: Colors.white.withValues(alpha: isDarkMode ? 0.2 : 0.3),
              ),
              boxShadow: isDarkMode ? [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ] : null,
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.award,
                  color: Colors.white,
                  size: AppDimensions.iconS,
                ),
                const SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Xếp hạng: #3 / 50 nhân viên',
                  style: AppTypography.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    shadows: isDarkMode ? [
                      const Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 2,
                        color: Colors.black54,
                      ),
                    ] : null,
                  ),
                ),
              ],
            ),
          ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressItem(
    BuildContext context, {
    required String title,
    required String current,
    required String target,
    required double progress,
    required Color color,
    required bool isDarkMode,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: isDarkMode ? 0.08 : 0.12),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
          width: 1,
        ),
        boxShadow: isDarkMode ? [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ] : [
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.3),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: AppTypography.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                  shadows: isDarkMode ? [
                    const Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black54,
                    ),
                  ] : null,
                ),
              ),
              Text(
                '$current / $target',
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withValues(alpha: isDarkMode ? 0.75 : 0.8),
                  shadows: isDarkMode ? [
                    const Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 2,
                      color: Colors.black54,
                    ),
                  ] : null,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white.withValues(alpha: isDarkMode ? 0.2 : 0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              isDarkMode 
                ? Colors.white.withValues(alpha: 0.9)
                : Colors.white
            ),
            minHeight: 8,
          ),
          const SizedBox(height: 6),
          Text(
            '${(progress * 100).toInt()}%',
            style: AppTypography.textTheme.labelMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              shadows: isDarkMode ? [
                const Shadow(
                  offset: Offset(0, 1),
                  blurRadius: 2,
                  color: Colors.black54,
                ),
              ] : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiniVisualChart(BuildContext context, bool isDarkMode) {
    final monthlyData = [3.0, 4.2, 3.8, 5.1, 4.5, 6.0];
    final maxValue = 8.0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Xu hướng 6 tháng',
          style: AppTypography.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: Colors.white,
            shadows: isDarkMode ? [
              const Shadow(
                offset: Offset(0, 1),
                blurRadius: 2,
                color: Colors.black54,
              ),
            ] : null,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Container(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: isDarkMode ? 0.1 : 0.15),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: Colors.white.withValues(alpha: isDarkMode ? 0.15 : 0.2),
              width: 1,
            ),
            boxShadow: isDarkMode ? [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: SizedBox(
            height: 60,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: List.generate(monthlyData.length, (index) {
                final height = (monthlyData[index] / maxValue) * 50;
                return Container(
                  width: 12,
                  height: height,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: isDarkMode ? [
                        Colors.white.withValues(alpha: 0.8),
                        Colors.white.withValues(alpha: 0.5),
                      ] : [
                        Colors.white,
                        Colors.white.withValues(alpha: 0.7),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(6),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withValues(alpha: isDarkMode ? 0.2 : 0.3),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ),
        ),
      ],
    );
  }
} 