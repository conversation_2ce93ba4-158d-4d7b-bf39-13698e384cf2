import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../core/theme/index.dart';
import '../models/dashboard_stats.dart';
import '../services/dashboard_stats_service.dart';

class QuickStatsCards extends StatefulWidget {
  const QuickStatsCards({super.key});

  @override
  State<QuickStatsCards> createState() => _QuickStatsCardsState();
}

class _QuickStatsCardsState extends State<QuickStatsCards>
    with TickerProviderStateMixin {
  final DashboardStatsService _statsService = DashboardStatsService();
  DashboardStats? _dashboardStats;
  bool _isLoading = true;
  String? _errorMessage;
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadDashboardStats();
  }

  void _initializeAnimations() {
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );
    _shimmerController.repeat();
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  Future<void> _loadDashboardStats() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final stats = await _statsService.getUserBusinessDashboardStats();

      if (mounted) {
        setState(() {
          _dashboardStats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshStats() async {
    await _loadDashboardStats();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    if (_isLoading) {
      return _buildSkeletonCards(isDarkMode);
    }

    if (_errorMessage != null) {
      return _buildErrorState(isDarkMode);
    }

    if (_dashboardStats == null) {
      return _buildEmptyState(isDarkMode);
    }

    return RefreshIndicator(
      onRefresh: _refreshStats,
      child: _buildStatsCards(isDarkMode),
    );
  }

  Widget _buildSkeletonCards(bool isDarkMode) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Row(
          children: List.generate(
            3,
            (index) => Expanded(child: _buildSkeletonCard(isDarkMode, index)),
          ),
        );
      },
    );
  }

  Widget _buildSkeletonCard(bool isDarkMode, int index) {
    final shimmerValue = _shimmerAnimation.value;
    final shimmerColor = isDarkMode
        ? Colors.white.withValues(alpha: 0.1)
        : Colors.grey.withValues(alpha: 0.15);

    return Container(
      height: 140,
      margin: EdgeInsets.only(right: index < 2 ? AppDimensions.spacingM : 0),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.withValues(alpha: 0.1)
            : Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? Colors.grey.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.1),
        ),
      ),
      child: Stack(
        children: [
          // Shimmer effect
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(shimmerValue - 1, 0),
                    end: Alignment(shimmerValue, 0),
                    colors: [
                      Colors.transparent,
                      shimmerColor,
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                ),
              ),
            ),
          ),

          // Skeleton content
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Icon skeleton
                Container(
                  width: 28,
                  height: 28,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                const SizedBox(height: AppDimensions.spacingS),
                // Main value skeleton
                Container(
                  width: 60,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                // Title skeleton
                Container(
                  width: 80,
                  height: 16,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                // Sub value skeleton
                Container(
                  width: 70,
                  height: 20,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.withValues(alpha: 0.2)
                        : Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(bool isDarkMode) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 140),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.error.withValues(alpha: 0.1)
            : AppColors.error.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? AppColors.error.withValues(alpha: 0.3)
              : AppColors.error.withValues(alpha: 0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(TablerIcons.alert_circle, size: 32, color: AppColors.error),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Không thể tải dữ liệu',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingXS),
            Flexible(
              child: Text(
                'Vui lòng kiểm tra kết nối mạng và thử lại',
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: isDarkMode
                      ? Colors.grey.withValues(alpha: 0.7)
                      : Colors.grey.withValues(alpha: 0.6),
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingS),
            ElevatedButton.icon(
              onPressed: _refreshStats,
              icon: const Icon(TablerIcons.refresh, size: 16),
              label: const Text('Thử lại'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(bool isDarkMode) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(minHeight: 140),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.withValues(alpha: 0.1)
            : Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? Colors.grey.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.1),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.database_off,
              size: 32,
              color: isDarkMode
                  ? Colors.grey.withValues(alpha: 0.6)
                  : Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Chưa có dữ liệu thống kê',
              style: AppTypography.textTheme.titleMedium?.copyWith(
                color: isDarkMode
                    ? Colors.grey.withValues(alpha: 0.8)
                    : Colors.grey.withValues(alpha: 0.7),
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.spacingXS),
            Flexible(
              child: Text(
                'Dữ liệu sẽ xuất hiện khi bạn bắt đầu sử dụng ứng dụng',
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  color: isDarkMode
                      ? Colors.grey.withValues(alpha: 0.6)
                      : Colors.grey.withValues(alpha: 0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards(bool isDarkMode) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            title: 'Khách hàng',
            mainValue: _dashboardStats!.totalCustomers.toString(),
            subValue: _statsService.formatNewCustomersText(
              _dashboardStats!.newCustomersThisWeek,
            ),
            icon: TablerIcons.users,
            color: AppColors.kienlongOrange,
            isDarkMode: isDarkMode,
          ),
        ),
        const SizedBox(width: AppDimensions.spacingM),
        Expanded(
          child: _buildStatCard(
            context,
            title: 'Hồ sơ',
            mainValue: _dashboardStats!.totalProposals.toString(),
            subValue: _statsService.formatRejectedProposalsText(
              _dashboardStats!.rejectedProposals,
              _dashboardStats!.totalProposals,
            ),
            icon: TablerIcons.file_text,
            color: AppColors.warning,
            isDarkMode: isDarkMode,
          ),
        ),
        const SizedBox(width: AppDimensions.spacingM),
        Expanded(
          child: _buildStatCard(
            context,
            title: 'Doanh số',
            mainValue: _statsService.formatCurrency(
              _dashboardStats!.totalRevenue,
            ),
            subValue: _statsService.formatRevenueThisWeekText(
              _dashboardStats!.revenueThisWeek,
            ),
            icon: TablerIcons.trending_up,
            color: AppColors.success,
            isDarkMode: isDarkMode,
          ),
        ),
      ],
    );
  }

  // Helper method để lấy dark version của color
  List<Color> _getDarkModeGradient(Color originalColor) {
    // Tạo version tối hơn và muted hơn cho dark mode
    final hsl = HSLColor.fromColor(originalColor);
    final darkerColor = hsl.withLightness(
      (hsl.lightness * 0.3).clamp(0.0, 1.0),
    );
    final darkestColor = hsl.withLightness(
      (hsl.lightness * 0.2).clamp(0.0, 1.0),
    );

    return [
      darkerColor.toColor(),
      darkerColor.toColor().withValues(alpha: 0.9),
      darkestColor.toColor().withValues(alpha: 0.8),
    ];
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String mainValue,
    required String subValue,
    required IconData icon,
    required Color color,
    required bool isDarkMode,
  }) {
    // Chọn gradient colors dựa vào theme
    final gradientColors = isDarkMode
        ? _getDarkModeGradient(color)
        : [color, color.withValues(alpha: 0.8), color.withValues(alpha: 0.6)];

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors,
          stops: const [0.0, 0.5, 1.0],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.4)
                : color.withValues(alpha: 0.3),
            blurRadius: isDarkMode ? 6 : 8,
            offset: const Offset(0, 4),
            spreadRadius: isDarkMode ? 0 : 1,
          ),
          if (isDarkMode)
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: Stack(
        children: [
          // Background Logo (watermark)
          Positioned(
            right: -15,
            bottom: -15,
            child: Opacity(
              opacity: isDarkMode ? 0.05 : 0.1,
              child: SvgPicture.asset(
                'assets/images/logos/icon.svg',
                width: 40,
                height: 40,
                colorFilter: const ColorFilter.mode(
                  Colors.white,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),

          // Content
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(
                        alpha: isDarkMode ? 0.15 : 0.2,
                      ),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: Colors.white.withValues(
                          alpha: isDarkMode ? 0.2 : 0.3,
                        ),
                      ),
                      boxShadow: isDarkMode
                          ? [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ]
                          : null,
                    ),
                    child: Icon(icon, color: Colors.white, size: 16),
                  ),
                  const Spacer(),
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              Text(
                mainValue,
                style: AppTypography.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: isDarkMode
                      ? [
                          const Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black54,
                          ),
                        ]
                      : null,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                title,
                style: AppTypography.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Colors.white.withValues(
                    alpha: isDarkMode ? 0.85 : 0.9,
                  ),
                  shadows: isDarkMode
                      ? [
                          const Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 2,
                            color: Colors.black54,
                          ),
                        ]
                      : null,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(
                    alpha: isDarkMode ? 0.12 : 0.2,
                  ),
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: Colors.white.withValues(
                      alpha: isDarkMode ? 0.2 : 0.3,
                    ),
                  ),
                  boxShadow: isDarkMode
                      ? [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ]
                      : null,
                ),
                child: Text(
                  subValue,
                  style: AppTypography.textTheme.labelSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    shadows: isDarkMode
                        ? [
                            const Shadow(
                              offset: Offset(0, 1),
                              blurRadius: 2,
                              color: Colors.black54,
                            ),
                          ]
                        : null,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
