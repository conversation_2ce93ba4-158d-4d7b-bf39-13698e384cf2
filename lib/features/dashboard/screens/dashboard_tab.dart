import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../widgets/index.dart';

class DashboardTab extends StatelessWidget {
  const DashboardTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const DashboardHeader(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Section with Weather
            const WeatherWelcomeCard(),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Quick Stats Row
            const QuickStatsCards(),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Today's Tasks
            const TodayTasksCard(),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Performance Summary
            const PerformanceSummaryCard(),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Recent Activities
            const RecentActivitiesCard(),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Notifications
            const NotificationsCard(),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Quick Actions Grid
            _buildQuickActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Thao tác nhanh',
          style: AppTypography.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          crossAxisSpacing: AppDimensions.spacingM,
          mainAxisSpacing: AppDimensions.spacingM,
          children: [
            _buildQuickActionItem(context, 'Khách hàng', TablerIcons.user_plus),
            _buildQuickActionItem(context, 'Giao dịch', TablerIcons.receipt_2),
            _buildQuickActionItem(context, 'Sản phẩm', TablerIcons.building_bank),
            _buildQuickActionItem(context, 'Tra cứu', TablerIcons.search),
            _buildQuickActionItem(context, 'Báo cáo', TablerIcons.chart_bar),
            _buildQuickActionItem(context, 'Hỗ trợ', TablerIcons.help),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionItem(BuildContext context, String title, IconData icon) {
    return GestureDetector(
      onTap: () {
        // TODO: Navigate to respective screen
      },
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowLight,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: AppDimensions.iconL,
              color: AppColors.kienlongOrange,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              title,
              style: AppTypography.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
} 