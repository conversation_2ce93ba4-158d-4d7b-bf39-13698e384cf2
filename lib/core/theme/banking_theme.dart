import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';

/// Kiloba Business Banking Theme
/// Định nghĩa theme đặc thù ngân hàng cho ứng dụng Kiloba Business
class BankingTheme {
  // Account Card Decorations - Trang trí thẻ tài kho<PERSON>n
  
  /// Gradient decoration cho Account Card
  static BoxDecoration get accountCardDecoration => BoxDecoration(
    gradient: const LinearGradient(
      colors: [AppColors.kienlongOrange, AppColors.kienlongSkyBlue],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      stops: [0.0, 1.0],
    ),
    borderRadius: BorderRadius.circular(AppDimensions.accountCardRadius),
    boxShadow: [
      BoxShadow(
        color: AppColors.kienlongOrange.withValues(alpha: 0.3),
        blurRadius: AppDimensions.shadowBlurRadiusM,
        offset: const Offset(0, 4),
        spreadRadius: 0,
      ),
    ],
  );

  /// Gradient decoration cho VIP Account Card
  static BoxDecoration get vipAccountCardDecoration => BoxDecoration(
    gradient: LinearGradient(
      colors: [
        AppColors.cardGoldVip,
        AppColors.cardGoldVip.withValues(alpha: 0.8),
        AppColors.kienlongOrange,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      stops: const [0.0, 0.5, 1.0],
    ),
    borderRadius: BorderRadius.circular(AppDimensions.accountCardRadius),
    boxShadow: [
      BoxShadow(
        color: AppColors.cardGoldVip.withValues(alpha: 0.4),
        blurRadius: AppDimensions.shadowBlurRadiusL,
        offset: const Offset(0, 6),
        spreadRadius: 1,
      ),
    ],
  );

  /// Dark theme Account Card decoration
  static BoxDecoration get darkAccountCardDecoration => BoxDecoration(
    gradient: const LinearGradient(
      colors: [AppColors.kienlongDarkBlue, AppColors.neutral800],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    borderRadius: BorderRadius.circular(AppDimensions.accountCardRadius),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowDark,
        blurRadius: AppDimensions.shadowBlurRadiusM,
        offset: const Offset(0, 4),
      ),
    ],
  );

  // Bank Card Decorations - Trang trí thẻ ngân hàng

  /// Classic Bank Card decoration
  static BoxDecoration get classicBankCardDecoration => BoxDecoration(
    gradient: LinearGradient(
      colors: [
        AppColors.cardClassic,
        AppColors.cardClassic.withValues(alpha: 0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    borderRadius: BorderRadius.circular(AppDimensions.bankCardRadius),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowMedium,
        blurRadius: AppDimensions.shadowBlurRadiusS,
        offset: const Offset(0, 2),
      ),
    ],
  );

  /// Platinum Bank Card decoration
  static BoxDecoration get platinumBankCardDecoration => BoxDecoration(
    gradient: LinearGradient(
      colors: [
        AppColors.cardPlatinum,
        AppColors.cardPlatinum.withValues(alpha: 0.9),
        AppColors.neutral400,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      stops: const [0.0, 0.7, 1.0],
    ),
    borderRadius: BorderRadius.circular(AppDimensions.bankCardRadius),
    boxShadow: [
      BoxShadow(
        color: AppColors.cardPlatinum.withValues(alpha: 0.3),
        blurRadius: AppDimensions.shadowBlurRadiusM,
        offset: const Offset(0, 4),
      ),
    ],
  );

  // Transaction Item Decorations - Trang trí item giao dịch

  /// Transaction Item decoration
  static BoxDecoration get transactionItemDecoration => BoxDecoration(
    color: AppColors.backgroundPrimary,
    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
    border: Border.all(
      color: AppColors.borderLight,
      width: AppDimensions.borderWidthThin,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowLight,
        blurRadius: AppDimensions.shadowBlurRadiusS,
        offset: const Offset(0, 1),
      ),
    ],
  );

  /// Income Transaction decoration
  static BoxDecoration get incomeTransactionDecoration => BoxDecoration(
    color: AppColors.backgroundPrimary,
    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
    border: Border.all(
      color: AppColors.transactionIncome.withValues(alpha: 0.3),
      width: AppDimensions.borderWidthNormal,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColors.transactionIncome.withValues(alpha: 0.1),
        blurRadius: AppDimensions.shadowBlurRadiusS,
        offset: const Offset(0, 2),
      ),
    ],
  );

  /// Expense Transaction decoration
  static BoxDecoration get expenseTransactionDecoration => BoxDecoration(
    color: AppColors.backgroundPrimary,
    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
    border: Border.all(
      color: AppColors.transactionExpense.withValues(alpha: 0.3),
      width: AppDimensions.borderWidthNormal,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColors.transactionExpense.withValues(alpha: 0.1),
        blurRadius: AppDimensions.shadowBlurRadiusS,
        offset: const Offset(0, 2),
      ),
    ],
  );

  // Balance Display Decorations - Trang trí hiển thị số dư

  /// Balance Display decoration
  static BoxDecoration get balanceDisplayDecoration => BoxDecoration(
    gradient: LinearGradient(
      colors: [
        AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
        AppColors.kienlongOrange.withValues(alpha: 0.1),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
    borderRadius: BorderRadius.circular(AppDimensions.radiusL),
    border: Border.all(
      color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
      width: AppDimensions.borderWidthThin,
    ),
  );

  // Quick Action Button Decorations - Trang trí nút hành động nhanh

  /// Quick Action Button decoration
  static BoxDecoration get quickActionButtonDecoration => BoxDecoration(
    color: AppColors.backgroundPrimary,
    borderRadius: BorderRadius.circular(AppDimensions.quickActionButtonRadius),
    border: Border.all(
      color: AppColors.kienlongOrange.withValues(alpha: 0.2),
      width: AppDimensions.borderWidthNormal,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColors.kienlongOrange.withValues(alpha: 0.1),
        blurRadius: AppDimensions.shadowBlurRadiusS,
        offset: const Offset(0, 2),
      ),
    ],
  );

  /// Active Quick Action Button decoration
  static BoxDecoration get activeQuickActionButtonDecoration => BoxDecoration(
    gradient: LinearGradient(
      colors: [
        AppColors.kienlongOrange,
        AppColors.kienlongOrange.withValues(alpha: 0.8),
      ],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
    borderRadius: BorderRadius.circular(AppDimensions.quickActionButtonRadius),
    boxShadow: [
      BoxShadow(
        color: AppColors.kienlongOrange.withValues(alpha: 0.3),
        blurRadius: AppDimensions.shadowBlurRadiusM,
        offset: const Offset(0, 4),
      ),
    ],
  );

  // Security Level Colors - Màu mức độ bảo mật
  static Map<String, Color> get securityLevelColors => {
    'low': AppColors.securityLow,
    'medium': AppColors.securityMedium,
    'high': AppColors.securityHigh,
    'critical': AppColors.securityCritical,
  };

  // Transaction Status Colors - Màu trạng thái giao dịch
  static Map<String, Color> get transactionStatusColors => {
    'completed': AppColors.transactionIncome,
    'pending': AppColors.transactionPending,
    'failed': AppColors.transactionExpense,
    'cancelled': AppColors.neutral500,
  };

  // PIN Input Decoration - Trang trí nhập PIN
  static BoxDecoration get pinInputDecoration => BoxDecoration(
    color: AppColors.backgroundSecondary,
    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
    border: Border.all(
      color: AppColors.borderLight,
      width: AppDimensions.borderWidthNormal,
    ),
  );

  static BoxDecoration get activePinInputDecoration => BoxDecoration(
    color: AppColors.backgroundPrimary,
    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
    border: Border.all(
      color: AppColors.kienlongOrange,
      width: AppDimensions.borderWidthThick,
    ),
    boxShadow: [
      BoxShadow(
        color: AppColors.kienlongOrange.withValues(alpha: 0.2),
        blurRadius: AppDimensions.shadowBlurRadiusS,
        offset: const Offset(0, 0),
        spreadRadius: 1,
      ),
    ],
  );

  // Biometric Scanner Decoration - Trang trí máy quét sinh trắc học
  static BoxDecoration get biometricScannerDecoration => BoxDecoration(
    gradient: RadialGradient(
      colors: [
        AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
        AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
        Colors.transparent,
      ],
      stops: const [0.0, 0.7, 1.0],
    ),
    borderRadius: BorderRadius.circular(AppDimensions.biometricScannerSize / 2),
    border: Border.all(
      color: AppColors.kienlongSkyBlue.withValues(alpha: 0.3),
      width: AppDimensions.borderWidthThick,
    ),
  );

  // QR Scanner Overlay Decoration - Trang trí overlay máy quét QR
  static BoxDecoration get qrScannerOverlayDecoration => BoxDecoration(
    borderRadius: BorderRadius.circular(AppDimensions.radiusL),
    border: Border.all(
      color: AppColors.kienlongOrange,
      width: AppDimensions.borderWidthThick,
    ),
  );

  // Banking Text Styles với màu sắc - Text styles cho banking với màu

  /// Text style cho số dư tài khoản (Income)
  static TextStyle get positiveBalanceTextStyle => AppTypography.accountBalanceLarge.copyWith(
    color: AppColors.transactionIncome,
  );

  /// Text style cho số dư âm (Expense)  
  static TextStyle get negativeBalanceTextStyle => AppTypography.accountBalanceLarge.copyWith(
    color: AppColors.transactionExpense,
  );

  /// Text style cho số tiền giao dịch thu nhập
  static TextStyle get incomeAmountTextStyle => AppTypography.transactionAmount.copyWith(
    color: AppColors.transactionIncome,
    fontWeight: FontWeight.w600,
  );

  /// Text style cho số tiền giao dịch chi tiêu
  static TextStyle get expenseAmountTextStyle => AppTypography.transactionAmount.copyWith(
    color: AppColors.transactionExpense,
    fontWeight: FontWeight.w600,
  );

  /// Text style cho số tiền giao dịch đang chờ
  static TextStyle get pendingAmountTextStyle => AppTypography.transactionAmount.copyWith(
    color: AppColors.transactionPending,
    fontWeight: FontWeight.w500,
  );

  // Banking Component Padding - Padding cho component banking
  static const EdgeInsets accountCardPadding = EdgeInsets.all(AppDimensions.accountCardPadding);
  static const EdgeInsets transactionItemPadding = EdgeInsets.all(AppDimensions.transactionItemPadding);
  static const EdgeInsets balanceDisplayPadding = EdgeInsets.all(AppDimensions.balanceDisplayPadding);

  // Banking Animation Durations - Thời gian animation cho banking
  static const Duration cardAnimationDuration = Duration(milliseconds: 300);
  static const Duration transactionAnimationDuration = Duration(milliseconds: 200);
  static const Duration balanceAnimationDuration = Duration(milliseconds: 400);
  static const Duration pinInputAnimationDuration = Duration(milliseconds: 150);

  // Banking Animation Curves - Đường cong animation cho banking
  static const Curve cardAnimationCurve = Curves.easeInOut;
  static const Curve transactionAnimationCurve = Curves.easeOut;
  static const Curve balanceAnimationCurve = Curves.elasticOut;
  static const Curve pinInputAnimationCurve = Curves.easeIn;

  // Helper methods để tạo decoration động
  
  /// Tạo decoration cho thẻ tài khoản dựa vào loại
  static BoxDecoration getAccountCardDecoration({
    required String cardType,
    bool isDarkMode = false,
  }) {
    if (isDarkMode) return darkAccountCardDecoration;
    
    switch (cardType.toLowerCase()) {
      case 'vip':
      case 'premium':
        return vipAccountCardDecoration;
      default:
        return accountCardDecoration;
    }
  }

  /// Tạo decoration cho thẻ ngân hàng dựa vào loại
  static BoxDecoration getBankCardDecoration({required String cardType}) {
    switch (cardType.toLowerCase()) {
      case 'platinum':
        return platinumBankCardDecoration;
      case 'classic':
      default:
        return classicBankCardDecoration;
    }
  }

  /// Tạo decoration cho item giao dịch dựa vào loại
  static BoxDecoration getTransactionDecoration({required String transactionType}) {
    switch (transactionType.toLowerCase()) {
      case 'income':
      case 'deposit':
      case 'transfer_in':
        return incomeTransactionDecoration;
      case 'expense':
      case 'withdrawal':
      case 'transfer_out':
        return expenseTransactionDecoration;
      default:
        return transactionItemDecoration;
    }
  }

  /// Lấy màu cho trạng thái bảo mật
  static Color getSecurityLevelColor(String level) {
    return securityLevelColors[level.toLowerCase()] ?? AppColors.neutral500;
  }

  /// Lấy màu cho trạng thái giao dịch
  static Color getTransactionStatusColor(String status) {
    return transactionStatusColors[status.toLowerCase()] ?? AppColors.neutral500;
  }

  /// Lấy text style cho số tiền dựa vào loại giao dịch
  static TextStyle getAmountTextStyle({required String transactionType}) {
    switch (transactionType.toLowerCase()) {
      case 'income':
      case 'deposit':
      case 'transfer_in':
        return incomeAmountTextStyle;
      case 'expense':
      case 'withdrawal':
      case 'transfer_out':
        return expenseAmountTextStyle;
      case 'pending':
        return pendingAmountTextStyle;
      default:
        return AppTypography.transactionAmount;
    }
  }
} 