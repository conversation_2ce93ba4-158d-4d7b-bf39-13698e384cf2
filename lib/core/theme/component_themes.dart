import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';

/// Kiloba Business Component Themes
/// Định nghĩa theme cho các component của ứng dụng Kiloba Business
class AppComponentThemes {
  // AppBar Theme - Theme thanh điều hướng
  static AppBarTheme get appBarTheme => AppBarTheme(
    backgroundColor: AppColors.kienlongOrange,
    foregroundColor: AppColors.textOnPrimary,
    elevation: AppDimensions.appBarElevation,
    centerTitle: true,
    titleTextStyle: AppTypography.textTheme.titleLarge?.copyWith(
      color: AppColors.textOnPrimary,
      fontWeight: FontWeight.w600,
    ),
    iconTheme: const IconThemeData(
      color: AppColors.textOnPrimary,
      size: AppDimensions.iconM,
    ),
    actionsIconTheme: const IconThemeData(
      color: AppColors.textOnPrimary,
      size: AppDimensions.iconM,
    ),
    systemOverlayStyle: const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ),
  );

  // ElevatedButton Theme - Theme nút nâng
  static ElevatedButtonThemeData get elevatedButtonTheme => ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: AppColors.kienlongOrange,
      foregroundColor: AppColors.textOnPrimary,
      disabledBackgroundColor: AppColors.neutral300,
      disabledForegroundColor: AppColors.neutral500,
      elevation: AppDimensions.cardElevation,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.buttonPaddingHorizontalL,
        vertical: AppDimensions.buttonPaddingVerticalM,
      ),
      minimumSize: const Size(0, AppDimensions.buttonHeightL),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      textStyle: AppTypography.buttonLarge,
    ).copyWith(
      backgroundColor: WidgetStateProperty.resolveWith<Color>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) return AppColors.neutral300;
          if (states.contains(WidgetState.pressed)) return AppColors.primaryDark;
          if (states.contains(WidgetState.hovered)) return AppColors.primaryLight;
          return AppColors.kienlongOrange;
        },
      ),
      elevation: WidgetStateProperty.resolveWith<double>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) return 0;
          if (states.contains(WidgetState.pressed)) return AppDimensions.cardElevationPressed;
          if (states.contains(WidgetState.hovered)) return AppDimensions.cardElevationHover;
          return AppDimensions.cardElevation;
        },
      ),
    ),
  );

  // OutlinedButton Theme - Theme nút viền
  static OutlinedButtonThemeData get outlinedButtonTheme => OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: AppColors.kienlongOrange,
      disabledForegroundColor: AppColors.neutral500,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.buttonPaddingHorizontalL,
        vertical: AppDimensions.buttonPaddingVerticalM,
      ),
      minimumSize: const Size(0, AppDimensions.buttonHeightL),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      side: const BorderSide(
        color: AppColors.kienlongOrange,
        width: AppDimensions.borderWidthNormal,
      ),
      textStyle: AppTypography.buttonLarge,
    ).copyWith(
      side: WidgetStateProperty.resolveWith<BorderSide>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return const BorderSide(color: AppColors.neutral300, width: AppDimensions.borderWidthNormal);
          }
          if (states.contains(WidgetState.pressed)) {
            return const BorderSide(color: AppColors.primaryDark, width: AppDimensions.borderWidthThick);
          }
          return const BorderSide(color: AppColors.kienlongOrange, width: AppDimensions.borderWidthNormal);
        },
      ),
      foregroundColor: WidgetStateProperty.resolveWith<Color>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) return AppColors.neutral500;
          if (states.contains(WidgetState.pressed)) return AppColors.primaryDark;
          return AppColors.kienlongOrange;
        },
      ),
    ),
  );

  // TextButton Theme - Theme nút văn bản
  static TextButtonThemeData get textButtonTheme => TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: AppColors.kienlongOrange,
      disabledForegroundColor: AppColors.neutral500,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.buttonPaddingHorizontalM,
        vertical: AppDimensions.buttonPaddingVerticalM,
      ),
      minimumSize: const Size(0, AppDimensions.buttonHeightM),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      textStyle: AppTypography.buttonMedium,
    ),
  );

  // Card Theme - Theme thẻ
  static CardThemeData get cardTheme => CardThemeData(
    color: AppColors.backgroundPrimary,
    elevation: AppDimensions.cardElevation,
    shadowColor: AppColors.shadowMedium,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusL),
    ),
    margin: const EdgeInsets.symmetric(
      horizontal: AppDimensions.marginS,
      vertical: AppDimensions.marginXS,
    ),
  );

  // InputDecoration Theme - Theme trang trí input
  static InputDecorationTheme get inputDecorationTheme => InputDecorationTheme(
    filled: true,
    fillColor: AppColors.backgroundSecondary,
    contentPadding: const EdgeInsets.symmetric(
      horizontal: AppDimensions.paddingM,
      vertical: AppDimensions.paddingM,
    ),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      borderSide: const BorderSide(color: AppColors.borderLight),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      borderSide: const BorderSide(color: AppColors.borderLight),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      borderSide: const BorderSide(color: AppColors.kienlongOrange, width: AppDimensions.borderWidthThick),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      borderSide: const BorderSide(color: AppColors.error, width: AppDimensions.borderWidthThick),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      borderSide: const BorderSide(color: AppColors.error, width: AppDimensions.borderWidthThick),
    ),
    disabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      borderSide: const BorderSide(color: AppColors.neutral300),
    ),
    labelStyle: AppTypography.inputLabel,
    hintStyle: AppTypography.inputHint,
    errorStyle: AppTypography.errorText,
    helperStyle: AppTypography.helperText,
    prefixIconColor: AppColors.neutral600,
    suffixIconColor: AppColors.neutral600,
  );

  // FloatingActionButton Theme - Theme nút hành động nổi
  static FloatingActionButtonThemeData get floatingActionButtonTheme => FloatingActionButtonThemeData(
    backgroundColor: AppColors.kienlongOrange,
    foregroundColor: AppColors.textOnPrimary,
    elevation: AppDimensions.cardElevation,
    highlightElevation: AppDimensions.cardElevationHover,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.fabSize / 2),
    ),
  );

  // BottomNavigationBar Theme - Theme thanh điều hướng dưới
  static BottomNavigationBarThemeData get bottomNavigationBarTheme => BottomNavigationBarThemeData(
    backgroundColor: AppColors.backgroundPrimary,
    elevation: AppDimensions.bottomNavElevation,
    selectedItemColor: AppColors.kienlongOrange,
    unselectedItemColor: AppColors.neutral600,
    selectedLabelStyle: AppTypography.navigationLabel.copyWith(
      color: AppColors.kienlongOrange,
      fontWeight: FontWeight.w600,
    ),
    unselectedLabelStyle: AppTypography.navigationLabel.copyWith(
      color: AppColors.neutral600,
    ),
    type: BottomNavigationBarType.fixed,
    selectedIconTheme: const IconThemeData(
      color: AppColors.kienlongOrange,
      size: AppDimensions.iconM,
    ),
    unselectedIconTheme: const IconThemeData(
      color: AppColors.neutral600,
      size: AppDimensions.iconM,
    ),
  );

  // TabBar Theme - Theme thanh tab
  static TabBarThemeData get tabBarTheme => TabBarThemeData(
    labelColor: AppColors.kienlongOrange,
    unselectedLabelColor: AppColors.neutral600,
    labelStyle: AppTypography.tabLabel.copyWith(
      fontWeight: FontWeight.w600,
    ),
    unselectedLabelStyle: AppTypography.tabLabel,
    indicator: const UnderlineTabIndicator(
      borderSide: BorderSide(
        color: AppColors.kienlongOrange,
        width: AppDimensions.tabIndicatorHeight,
      ),
    ),
    indicatorSize: TabBarIndicatorSize.tab,
    dividerColor: AppColors.borderLight,
  );

  // Chip Theme - Theme chip
  static ChipThemeData get chipTheme => ChipThemeData(
    backgroundColor: AppColors.neutral100,
    selectedColor: AppColors.kienlongOrange,
    disabledColor: AppColors.neutral200,
    deleteIconColor: AppColors.neutral600,
    labelStyle: AppTypography.textTheme.labelMedium?.copyWith(
      color: AppColors.textPrimary,
    ),
    secondaryLabelStyle: AppTypography.textTheme.labelSmall?.copyWith(
      color: AppColors.textSecondary,
    ),
    padding: const EdgeInsets.symmetric(
      horizontal: AppDimensions.chipPadding,
      vertical: AppDimensions.paddingXS,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.chipRadius),
    ),
    side: const BorderSide(color: AppColors.borderLight),
  );

  // Switch Theme - Theme công tắc
  static SwitchThemeData get switchTheme => SwitchThemeData(
    thumbColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.disabled)) return AppColors.neutral400;
        if (states.contains(WidgetState.selected)) return AppColors.kienlongOrange;
        return AppColors.neutral100;
      },
    ),
    trackColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.disabled)) return AppColors.neutral200;
        if (states.contains(WidgetState.selected)) return AppColors.kienlongOrange.withValues(alpha: 0.5);
        return AppColors.neutral300;
      },
    ),
  );

  // Checkbox Theme - Theme hộp kiểm
  static CheckboxThemeData get checkboxTheme => CheckboxThemeData(
    fillColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.disabled)) return AppColors.neutral300;
        if (states.contains(WidgetState.selected)) return AppColors.kienlongOrange;
        return Colors.transparent;
      },
    ),
    checkColor: WidgetStateProperty.all(AppColors.textOnPrimary),
    side: const BorderSide(color: AppColors.borderMedium, width: AppDimensions.borderWidthNormal),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
    ),
  );

  // Radio Theme - Theme radio
  static RadioThemeData get radioTheme => RadioThemeData(
    fillColor: WidgetStateProperty.resolveWith<Color>(
      (Set<WidgetState> states) {
        if (states.contains(WidgetState.disabled)) return AppColors.neutral300;
        if (states.contains(WidgetState.selected)) return AppColors.kienlongOrange;
        return AppColors.borderMedium;
      },
    ),
  );

  // Progress Indicator Theme - Theme chỉ báo tiến trình
  static ProgressIndicatorThemeData get progressIndicatorTheme => const ProgressIndicatorThemeData(
    color: AppColors.kienlongOrange,
    linearTrackColor: AppColors.neutral200,
    circularTrackColor: AppColors.neutral200,
  );

  // Divider Theme - Theme đường phân cách
  static DividerThemeData get dividerTheme => const DividerThemeData(
    color: AppColors.borderLight,
    thickness: AppDimensions.dividerThickness,
    indent: AppDimensions.dividerIndent,
    endIndent: AppDimensions.dividerIndent,
  );

  // ListTile Theme - Theme mục danh sách
  static ListTileThemeData get listTileTheme => ListTileThemeData(
    contentPadding: const EdgeInsets.symmetric(
      horizontal: AppDimensions.paddingM,
      vertical: AppDimensions.paddingS,
    ),
    iconColor: AppColors.neutral600,
    textColor: AppColors.textPrimary,
    titleTextStyle: AppTypography.textTheme.bodyLarge,
    subtitleTextStyle: AppTypography.textTheme.bodyMedium?.copyWith(
      color: AppColors.textSecondary,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
    ),
  );

  // Snackbar Theme - Theme thanh thông báo
  static SnackBarThemeData get snackBarTheme => SnackBarThemeData(
    backgroundColor: AppColors.kienlongDarkBlue,
    contentTextStyle: AppTypography.textTheme.bodyMedium?.copyWith(
      color: AppColors.textOnPrimary,
    ),
    actionTextColor: AppColors.kienlongOrange,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
    ),
    behavior: SnackBarBehavior.floating,
  );

  // Dialog Theme - Theme hộp thoại
  static DialogThemeData get dialogTheme => DialogThemeData(
    backgroundColor: AppColors.backgroundPrimary,
    elevation: AppDimensions.cardElevationHover,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusXL),
    ),
    titleTextStyle: AppTypography.textTheme.headlineSmall?.copyWith(
      color: AppColors.textPrimary,
    ),
    contentTextStyle: AppTypography.textTheme.bodyLarge?.copyWith(
      color: AppColors.textPrimary,
    ),
  );
} 