/// Kiloba Business App Dimensions
/// <PERSON><PERSON><PERSON> ngh<PERSON>a kích thước và spacing cho ứng dụng Kiloba Business
class AppDimensions {
  // Spacing Scale - Thang đo khoảng cách
  static const double spacingXXS = 2.0;
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;
  static const double spacingXXXL = 64.0;

  // Padding - Khoảng cách padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;

  // Margin - Khoảng cách margin
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;

  // Border Radius - <PERSON><PERSON> bo góc
  static const double radiusXS = 2.0;
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 20.0;
  static const double radiusXXXL = 24.0;
  static const double radiusRound = 50.0; // For circular elements

  // Icon Sizes - Kích thước icon
  static const double iconXS = 12.0;
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  static const double iconXXL = 64.0;

  // Button Heights - Chiều cao button
  static const double buttonHeightS = 32.0;
  static const double buttonHeightM = 40.0;
  static const double buttonHeightL = 48.0;
  static const double buttonHeightXL = 56.0;

  // Button Padding - Padding của button
  static const double buttonPaddingHorizontalS = 12.0;
  static const double buttonPaddingHorizontalM = 16.0;
  static const double buttonPaddingHorizontalL = 24.0;
  static const double buttonPaddingVerticalS = 8.0;
  static const double buttonPaddingVerticalM = 12.0;
  static const double buttonPaddingVerticalL = 16.0;

  // Input Field Heights - Chiều cao ô input
  static const double inputHeightS = 40.0;
  static const double inputHeightM = 48.0;
  static const double inputHeightL = 56.0;

  // Card Properties - Thuộc tính card
  static const double cardElevation = 2.0;
  static const double cardElevationHover = 4.0;
  static const double cardElevationPressed = 1.0;

  // AppBar - Thanh điều hướng
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 0.0;

  // Bottom Navigation - Điều hướng dưới
  static const double bottomNavHeight = 56.0;
  static const double bottomNavElevation = 8.0;

  // Tab Bar - Thanh tab
  static const double tabBarHeight = 48.0;
  static const double tabIndicatorHeight = 3.0;

  // List Item Heights - Chiều cao item trong list
  static const double listItemHeightS = 48.0;
  static const double listItemHeightM = 56.0;
  static const double listItemHeightL = 72.0;
  static const double listItemHeightXL = 88.0;

  // Divider - Đường phân cách
  static const double dividerThickness = 1.0;
  static const double dividerIndent = 16.0;

  // Border Width - Độ dày viền
  static const double borderWidthThin = 0.5;
  static const double borderWidthNormal = 1.0;
  static const double borderWidthThick = 2.0;

  // Shadow - Bóng đổ
  static const double shadowBlurRadiusS = 4.0;
  static const double shadowBlurRadiusM = 8.0;
  static const double shadowBlurRadiusL = 16.0;
  static const double shadowSpreadRadius = 0.0;
  static const double shadowOffsetX = 0.0;
  static const double shadowOffsetY = 2.0;

  // Banking Specific Dimensions - Kích thước đặc thù ngân hàng

  // Account Card - Thẻ tài khoản
  static const double accountCardHeight = 200.0;
  static const double accountCardWidth = 320.0;
  static const double accountCardRadius = 16.0;
  static const double accountCardPadding = 20.0;

  // Credit/Debit Card - Thẻ tín dụng/ghi nợ
  static const double bankCardHeight = 180.0;
  static const double bankCardWidth = 300.0;
  static const double bankCardRadius = 12.0;
  static const double bankCardAspectRatio = 1.67; // Standard card ratio

  // Transaction Item - Item giao dịch
  static const double transactionItemHeight = 72.0;
  static const double transactionItemPadding = 16.0;
  static const double transactionIconSize = 40.0;

  // Balance Display - Hiển thị số dư
  static const double balanceDisplayHeight = 120.0;
  static const double balanceDisplayPadding = 24.0;

  // Quick Action Button - Nút hành động nhanh
  static const double quickActionButtonSize = 60.0;
  static const double quickActionButtonRadius = 30.0;
  static const double quickActionIconSize = 24.0;

  // Security PIN Input - Nhập mã PIN bảo mật
  static const double pinInputSize = 48.0;
  static const double pinDotSize = 12.0;
  static const double pinSpacing = 16.0;

  // Biometric Scanner - Máy quét sinh trắc học
  static const double biometricScannerSize = 120.0;
  static const double biometricIconSize = 48.0;

  // QR Code Scanner - Máy quét mã QR
  static const double qrScannerOverlaySize = 240.0;
  static const double qrScannerCornerLength = 24.0;
  static const double qrScannerCornerWidth = 4.0;

  // Progress Indicators - Chỉ báo tiến trình
  static const double progressIndicatorSizeS = 16.0;
  static const double progressIndicatorSizeM = 24.0;
  static const double progressIndicatorSizeL = 32.0;
  static const double progressIndicatorStrokeWidth = 3.0;

  // Floating Action Button - Nút hành động nổi
  static const double fabSize = 56.0;
  static const double fabMiniSize = 40.0;

  // Chip - Thẻ nhỏ
  static const double chipHeight = 32.0;
  static const double chipPadding = 12.0;
  static const double chipRadius = 16.0;

  // Badge - Huy hiệu
  static const double badgeSize = 16.0;
  static const double badgeLargeSize = 24.0;

  // Skeleton Loading - Tải khung xương
  static const double skeletonRadius = 4.0;
  static const double skeletonHeight = 16.0;

  // Responsive Breakpoints - Điểm ngắt responsive
  static const double mobileBreakpoint = 480.0;
  static const double tabletBreakpoint = 768.0;
  static const double desktopBreakpoint = 1024.0;
  static const double largeDesktopBreakpoint = 1440.0;

  // Screen Padding - Padding màn hình
  static const double screenPaddingMobile = 16.0;
  static const double screenPaddingTablet = 24.0;
  static const double screenPaddingDesktop = 32.0;

  // Maximum Content Width - Chiều rộng nội dung tối đa
  static const double maxContentWidth = 1200.0;

  // Grid Spacing - Khoảng cách lưới
  static const double gridSpacing = 16.0;
  static const double gridPadding = 16.0;
} 