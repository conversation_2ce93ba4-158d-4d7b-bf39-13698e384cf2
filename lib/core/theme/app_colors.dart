import 'package:flutter/material.dart';

/// Kiloba Business App Colors
/// Đ<PERSON>nh nghĩa màu sắc ch<PERSON>h của ứng dụng Kiloba Business
class AppColors {
  // Brand Colors - <PERSON><PERSON><PERSON> sắc thương hiệu (từ color.md)
  /// <PERSON><PERSON>u cam chính - Primary Brand Color
  /// CMYK: 00 85 100 00
  static const Color kienlongOrange = Color(0xFFFF2600);
  
  /// <PERSON><PERSON>u xanh da trời - Secondary Brand Color  
  /// CMYK: 75 35 00 00
  static const Color kienlongSkyBlue = Color(0xFF40A6FF);
  
  /// Màu xanh dương đậm - Background/Accent
  /// CMYK: 95 85 45 60
  static const Color kienlongDarkBlue = Color(0xFF050F38);

  // Color Variations - Biến thể màu
  static const Color primaryLight = Color(0xFFFF5722);   // Lighter orange
  static const Color primaryDark = Color(0xFFD32F2F);    // Darker orange
  
  static const Color secondaryLight = Color(0xFF81C7FF); // Lighter sky blue
  static const Color secondaryDark = Color(0xFF1976D2);  // Darker sky blue

  // Status Colors - Màu trạng thái
  static const Color success = Color(0xFF4CAF50);
  static const Color successLight = Color(0xFF81C784);
  static const Color successDark = Color(0xFF388E3C);
  
  static const Color warning = Color(0xFFFF9800);
  static const Color warningLight = Color(0xFFFFB74D);
  static const Color warningDark = Color(0xFFF57C00);
  
  static const Color error = Color(0xFFE53935);
  static const Color errorLight = Color(0xFFEF5350);
  static const Color errorDark = Color(0xFFC62828);
  
  static const Color info = Color(0xFF2196F3);
  static const Color infoLight = Color(0xFF64B5F6);
  static const Color infoDark = Color(0xFF1976D2);

  // Neutral Colors - Màu trung tính
  static const Color neutral50 = Color(0xFFFAFAFA);
  static const Color neutral100 = Color(0xFFF5F5F5);
  static const Color neutral200 = Color(0xFFEEEEEE);
  static const Color neutral300 = Color(0xFFE0E0E0);
  static const Color neutral400 = Color(0xFFBDBDBD);
  static const Color neutral500 = Color(0xFF9E9E9E);
  static const Color neutral600 = Color(0xFF757575);
  static const Color neutral700 = Color(0xFF616161);
  static const Color neutral800 = Color(0xFF424242);
  static const Color neutral900 = Color(0xFF212121);

  // Banking Specific Colors - Màu đặc thù ngân hàng
  static const Color cardGoldVip = Color(0xFFFFD700);
  static const Color cardPlatinum = Color(0xFFE5E4E2);
  static const Color cardClassic = Color(0xFF708090);
  
  static const Color transactionIncome = Color(0xFF4CAF50);
  static const Color transactionExpense = Color(0xFFE53935);
  static const Color transactionPending = Color(0xFFFF9800);

  // Security Level Colors - Màu mức độ bảo mật
  static const Color securityLow = Color(0xFFFF9800);
  static const Color securityMedium = Color(0xFF2196F3);
  static const Color securityHigh = Color(0xFF4CAF50);
  static const Color securityCritical = Color(0xFFE53935);

  // Background Colors - Màu nền
  static const Color backgroundPrimary = Color(0xFFF5F7FA);  // Light gray background
  static const Color backgroundSecondary = Color(0xFFFFFFFF); // White for cards
  static const Color backgroundTertiary = Color(0xFFF8F9FA);
  
  static const Color backgroundDark = Color(0xFF121212);
  static const Color backgroundDarkSecondary = Color(0xFF1E1E1E);
  static const Color backgroundDarkTertiary = Color(0xFF2D2D2D);

  // Text Colors - Màu văn bản
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textTertiary = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);

  // Border Colors - Màu viền
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF757575);

  // Shadow Colors - Màu bóng đổ
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);
} 