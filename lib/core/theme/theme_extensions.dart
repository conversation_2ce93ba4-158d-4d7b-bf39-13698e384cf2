import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Kiloba Business Theme Extensions
/// Mở rộng theme cho các thuộc tính custom không có trong Material 3
@immutable
class BankingThemeExtension extends ThemeExtension<BankingThemeExtension> {
  final Color accountCardGradientStart;
  final Color accountCardGradientEnd;
  final Color transactionIncome;
  final Color transactionExpense;
  final Color transactionPending;
  final Color securityLow;
  final Color securityMedium;
  final Color securityHigh;
  final Color securityCritical;
  final Color cardGoldVip;
  final Color cardPlatinum;
  final Color cardClassic;
  final BoxDecoration accountCardDecoration;
  final BoxDecoration transactionItemDecoration;
  final BoxDecoration quickActionButtonDecoration;

  const BankingThemeExtension({
    required this.accountCardGradientStart,
    required this.accountCardGradientEnd,
    required this.transactionIncome,
    required this.transactionExpense,
    required this.transactionPending,
    required this.securityLow,
    required this.securityMedium,
    required this.securityHigh,
    required this.securityCritical,
    required this.cardGoldVip,
    required this.cardPlatinum,
    required this.cardClassic,
    required this.accountCardDecoration,
    required this.transactionItemDecoration,
    required this.quickActionButtonDecoration,
  });

  @override
  BankingThemeExtension copyWith({
    Color? accountCardGradientStart,
    Color? accountCardGradientEnd,
    Color? transactionIncome,
    Color? transactionExpense,
    Color? transactionPending,
    Color? securityLow,
    Color? securityMedium,
    Color? securityHigh,
    Color? securityCritical,
    Color? cardGoldVip,
    Color? cardPlatinum,
    Color? cardClassic,
    BoxDecoration? accountCardDecoration,
    BoxDecoration? transactionItemDecoration,
    BoxDecoration? quickActionButtonDecoration,
  }) {
    return BankingThemeExtension(
      accountCardGradientStart: accountCardGradientStart ?? this.accountCardGradientStart,
      accountCardGradientEnd: accountCardGradientEnd ?? this.accountCardGradientEnd,
      transactionIncome: transactionIncome ?? this.transactionIncome,
      transactionExpense: transactionExpense ?? this.transactionExpense,
      transactionPending: transactionPending ?? this.transactionPending,
      securityLow: securityLow ?? this.securityLow,
      securityMedium: securityMedium ?? this.securityMedium,
      securityHigh: securityHigh ?? this.securityHigh,
      securityCritical: securityCritical ?? this.securityCritical,
      cardGoldVip: cardGoldVip ?? this.cardGoldVip,
      cardPlatinum: cardPlatinum ?? this.cardPlatinum,
      cardClassic: cardClassic ?? this.cardClassic,
      accountCardDecoration: accountCardDecoration ?? this.accountCardDecoration,
      transactionItemDecoration: transactionItemDecoration ?? this.transactionItemDecoration,
      quickActionButtonDecoration: quickActionButtonDecoration ?? this.quickActionButtonDecoration,
    );
  }

  @override
  BankingThemeExtension lerp(ThemeExtension<BankingThemeExtension>? other, double t) {
    if (other is! BankingThemeExtension) {
      return this;
    }

    return BankingThemeExtension(
      accountCardGradientStart: Color.lerp(accountCardGradientStart, other.accountCardGradientStart, t)!,
      accountCardGradientEnd: Color.lerp(accountCardGradientEnd, other.accountCardGradientEnd, t)!,
      transactionIncome: Color.lerp(transactionIncome, other.transactionIncome, t)!,
      transactionExpense: Color.lerp(transactionExpense, other.transactionExpense, t)!,
      transactionPending: Color.lerp(transactionPending, other.transactionPending, t)!,
      securityLow: Color.lerp(securityLow, other.securityLow, t)!,
      securityMedium: Color.lerp(securityMedium, other.securityMedium, t)!,
      securityHigh: Color.lerp(securityHigh, other.securityHigh, t)!,
      securityCritical: Color.lerp(securityCritical, other.securityCritical, t)!,
      cardGoldVip: Color.lerp(cardGoldVip, other.cardGoldVip, t)!,
      cardPlatinum: Color.lerp(cardPlatinum, other.cardPlatinum, t)!,
      cardClassic: Color.lerp(cardClassic, other.cardClassic, t)!,
      accountCardDecoration: BoxDecoration.lerp(accountCardDecoration, other.accountCardDecoration, t)!,
      transactionItemDecoration: BoxDecoration.lerp(transactionItemDecoration, other.transactionItemDecoration, t)!,
      quickActionButtonDecoration: BoxDecoration.lerp(quickActionButtonDecoration, other.quickActionButtonDecoration, t)!,
    );
  }

  // Light Theme Banking Extension
  static const BankingThemeExtension light = BankingThemeExtension(
    accountCardGradientStart: AppColors.kienlongOrange,
    accountCardGradientEnd: AppColors.kienlongSkyBlue,
    transactionIncome: AppColors.transactionIncome,
    transactionExpense: AppColors.transactionExpense,
    transactionPending: AppColors.transactionPending,
    securityLow: AppColors.securityLow,
    securityMedium: AppColors.securityMedium,
    securityHigh: AppColors.securityHigh,
    securityCritical: AppColors.securityCritical,
    cardGoldVip: AppColors.cardGoldVip,
    cardPlatinum: AppColors.cardPlatinum,
    cardClassic: AppColors.cardClassic,
    accountCardDecoration: BoxDecoration(),
    transactionItemDecoration: BoxDecoration(),
    quickActionButtonDecoration: BoxDecoration(),
  );

  // Dark Theme Banking Extension
  static const BankingThemeExtension dark = BankingThemeExtension(
    accountCardGradientStart: AppColors.kienlongDarkBlue,
    accountCardGradientEnd: AppColors.neutral800,
    transactionIncome: AppColors.successLight,
    transactionExpense: AppColors.errorLight,
    transactionPending: AppColors.warningLight,
    securityLow: AppColors.warningLight,
    securityMedium: AppColors.infoLight,
    securityHigh: AppColors.successLight,
    securityCritical: AppColors.errorLight,
    cardGoldVip: AppColors.cardGoldVip,
    cardPlatinum: AppColors.cardPlatinum,
    cardClassic: AppColors.neutral600,
    accountCardDecoration: BoxDecoration(),
    transactionItemDecoration: BoxDecoration(),
    quickActionButtonDecoration: BoxDecoration(),
  );
}

/// Responsive Theme Extension
/// Mở rộng theme cho responsive design
@immutable
class ResponsiveThemeExtension extends ThemeExtension<ResponsiveThemeExtension> {
  final double mobileBreakpoint;
  final double tabletBreakpoint;
  final double desktopBreakpoint;
  final EdgeInsets mobilePadding;
  final EdgeInsets tabletPadding;
  final EdgeInsets desktopPadding;
  final double mobileCardHeight;
  final double tabletCardHeight;
  final double desktopCardHeight;

  const ResponsiveThemeExtension({
    required this.mobileBreakpoint,
    required this.tabletBreakpoint,
    required this.desktopBreakpoint,
    required this.mobilePadding,
    required this.tabletPadding,
    required this.desktopPadding,
    required this.mobileCardHeight,
    required this.tabletCardHeight,
    required this.desktopCardHeight,
  });

  @override
  ResponsiveThemeExtension copyWith({
    double? mobileBreakpoint,
    double? tabletBreakpoint,
    double? desktopBreakpoint,
    EdgeInsets? mobilePadding,
    EdgeInsets? tabletPadding,
    EdgeInsets? desktopPadding,
    double? mobileCardHeight,
    double? tabletCardHeight,
    double? desktopCardHeight,
  }) {
    return ResponsiveThemeExtension(
      mobileBreakpoint: mobileBreakpoint ?? this.mobileBreakpoint,
      tabletBreakpoint: tabletBreakpoint ?? this.tabletBreakpoint,
      desktopBreakpoint: desktopBreakpoint ?? this.desktopBreakpoint,
      mobilePadding: mobilePadding ?? this.mobilePadding,
      tabletPadding: tabletPadding ?? this.tabletPadding,
      desktopPadding: desktopPadding ?? this.desktopPadding,
      mobileCardHeight: mobileCardHeight ?? this.mobileCardHeight,
      tabletCardHeight: tabletCardHeight ?? this.tabletCardHeight,
      desktopCardHeight: desktopCardHeight ?? this.desktopCardHeight,
    );
  }

  @override
  ResponsiveThemeExtension lerp(ThemeExtension<ResponsiveThemeExtension>? other, double t) {
    if (other is! ResponsiveThemeExtension) {
      return this;
    }

    return ResponsiveThemeExtension(
      mobileBreakpoint: lerpDouble(mobileBreakpoint, other.mobileBreakpoint, t) ?? mobileBreakpoint,
      tabletBreakpoint: lerpDouble(tabletBreakpoint, other.tabletBreakpoint, t) ?? tabletBreakpoint,
      desktopBreakpoint: lerpDouble(desktopBreakpoint, other.desktopBreakpoint, t) ?? desktopBreakpoint,
      mobilePadding: EdgeInsets.lerp(mobilePadding, other.mobilePadding, t) ?? mobilePadding,
      tabletPadding: EdgeInsets.lerp(tabletPadding, other.tabletPadding, t) ?? tabletPadding,
      desktopPadding: EdgeInsets.lerp(desktopPadding, other.desktopPadding, t) ?? desktopPadding,
      mobileCardHeight: lerpDouble(mobileCardHeight, other.mobileCardHeight, t) ?? mobileCardHeight,
      tabletCardHeight: lerpDouble(tabletCardHeight, other.tabletCardHeight, t) ?? tabletCardHeight,
      desktopCardHeight: lerpDouble(desktopCardHeight, other.desktopCardHeight, t) ?? desktopCardHeight,
    );
  }

  static const ResponsiveThemeExtension standard = ResponsiveThemeExtension(
    mobileBreakpoint: 480.0,
    tabletBreakpoint: 768.0,
    desktopBreakpoint: 1024.0,
    mobilePadding: EdgeInsets.all(12.0),
    tabletPadding: EdgeInsets.all(16.0),
    desktopPadding: EdgeInsets.all(20.0),
    mobileCardHeight: 180.0,
    tabletCardHeight: 200.0,
    desktopCardHeight: 220.0,
  );
}

/// Animation Theme Extension
/// Mở rộng theme cho animation
@immutable
class AnimationThemeExtension extends ThemeExtension<AnimationThemeExtension> {
  final Duration fastDuration;
  final Duration normalDuration;
  final Duration slowDuration;
  final Curve fastCurve;
  final Curve normalCurve;
  final Curve slowCurve;

  const AnimationThemeExtension({
    required this.fastDuration,
    required this.normalDuration,
    required this.slowDuration,
    required this.fastCurve,
    required this.normalCurve,
    required this.slowCurve,
  });

  @override
  AnimationThemeExtension copyWith({
    Duration? fastDuration,
    Duration? normalDuration,
    Duration? slowDuration,
    Curve? fastCurve,
    Curve? normalCurve,
    Curve? slowCurve,
  }) {
    return AnimationThemeExtension(
      fastDuration: fastDuration ?? this.fastDuration,
      normalDuration: normalDuration ?? this.normalDuration,
      slowDuration: slowDuration ?? this.slowDuration,
      fastCurve: fastCurve ?? this.fastCurve,
      normalCurve: normalCurve ?? this.normalCurve,
      slowCurve: slowCurve ?? this.slowCurve,
    );
  }

  @override
  AnimationThemeExtension lerp(ThemeExtension<AnimationThemeExtension>? other, double t) {
    if (other is! AnimationThemeExtension) {
      return this;
    }

    return AnimationThemeExtension(
      fastDuration: lerpDuration(fastDuration, other.fastDuration, t),
      normalDuration: lerpDuration(normalDuration, other.normalDuration, t),
      slowDuration: lerpDuration(slowDuration, other.slowDuration, t),
      fastCurve: t < 0.5 ? fastCurve : other.fastCurve,
      normalCurve: t < 0.5 ? normalCurve : other.normalCurve,
      slowCurve: t < 0.5 ? slowCurve : other.slowCurve,
    );
  }

  static const AnimationThemeExtension standard = AnimationThemeExtension(
    fastDuration: Duration(milliseconds: 150),
    normalDuration: Duration(milliseconds: 300),
    slowDuration: Duration(milliseconds: 500),
    fastCurve: Curves.easeOut,
    normalCurve: Curves.easeInOut,
    slowCurve: Curves.elasticOut,
  );
}

// Helper functions
Duration lerpDuration(Duration a, Duration b, double t) {
  return Duration(microseconds: (a.inMicroseconds + (b.inMicroseconds - a.inMicroseconds) * t).round());
}

double? lerpDouble(double? a, double? b, double t) {
  if (a == null && b == null) return null;
  a ??= 0.0;
  b ??= 0.0;
  return a + (b - a) * t;
}

// Extension methods để sử dụng dễ dàng hơn
extension ThemeExtensionHelper on BuildContext {
  BankingThemeExtension get bankingTheme {
    return Theme.of(this).extension<BankingThemeExtension>() ?? BankingThemeExtension.light;
  }

  ResponsiveThemeExtension get responsiveTheme {
    return Theme.of(this).extension<ResponsiveThemeExtension>() ?? ResponsiveThemeExtension.standard;
  }

  AnimationThemeExtension get animationTheme {
    return Theme.of(this).extension<AnimationThemeExtension>() ?? AnimationThemeExtension.standard;
  }

  bool get isMobile {
    return MediaQuery.of(this).size.width < responsiveTheme.tabletBreakpoint;
  }

  bool get isTablet {
    final width = MediaQuery.of(this).size.width;
    return width >= responsiveTheme.tabletBreakpoint && width < responsiveTheme.desktopBreakpoint;
  }

  bool get isDesktop {
    return MediaQuery.of(this).size.width >= responsiveTheme.desktopBreakpoint;
  }

  EdgeInsets get responsivePadding {
    if (isMobile) return responsiveTheme.mobilePadding;
    if (isTablet) return responsiveTheme.tabletPadding;
    return responsiveTheme.desktopPadding;
  }

  double get responsiveCardHeight {
    if (isMobile) return responsiveTheme.mobileCardHeight;
    if (isTablet) return responsiveTheme.tabletCardHeight;
    return responsiveTheme.desktopCardHeight;
  }
} 