import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';
import 'component_themes.dart';
import 'theme_extensions.dart';

/// Kiloba Business App Theme
/// Theme chính của ứng dụng Kiloba Business
class AppTheme {
  // Private constructor để prevent instantiation
  AppTheme._();

  // Light Theme - Theme sáng
  static ThemeData get lightTheme {
    return ThemeData(
      // Sử dụng Material 3
      useMaterial3: true,
      
      // Color Scheme
      colorScheme: _lightColorScheme,
      
      // Brightness
      brightness: Brightness.light,
      
      // Primary Swatch (simplified for compatibility)
      primarySwatch: Colors.orange,
      
      // Background colors
      scaffoldBackgroundColor: AppColors.backgroundPrimary, // Light gray
      canvasColor: AppColors.backgroundPrimary,
      cardColor: AppColors.backgroundSecondary, // White for cards
      
      // Typography
      textTheme: AppTypography.textTheme.apply(
        bodyColor: AppColors.textPrimary,
        displayColor: AppColors.textPrimary,
      ),
      
      // Primary Text Theme (for app bars, etc.)
      primaryTextTheme: AppTypography.textTheme.apply(
        bodyColor: AppColors.textOnPrimary,
        displayColor: AppColors.textOnPrimary,
      ),
      
      // Component Themes
      appBarTheme: AppComponentThemes.appBarTheme,
      elevatedButtonTheme: AppComponentThemes.elevatedButtonTheme,
      outlinedButtonTheme: AppComponentThemes.outlinedButtonTheme,
      textButtonTheme: AppComponentThemes.textButtonTheme,
      cardTheme: AppComponentThemes.cardTheme,
      inputDecorationTheme: AppComponentThemes.inputDecorationTheme,
      floatingActionButtonTheme: AppComponentThemes.floatingActionButtonTheme,
      bottomNavigationBarTheme: AppComponentThemes.bottomNavigationBarTheme,
      tabBarTheme: AppComponentThemes.tabBarTheme,
      chipTheme: AppComponentThemes.chipTheme,
      switchTheme: AppComponentThemes.switchTheme,
      checkboxTheme: AppComponentThemes.checkboxTheme,
      radioTheme: AppComponentThemes.radioTheme,
      progressIndicatorTheme: AppComponentThemes.progressIndicatorTheme,
      dividerTheme: AppComponentThemes.dividerTheme,
      listTileTheme: AppComponentThemes.listTileTheme,
      snackBarTheme: AppComponentThemes.snackBarTheme,
      dialogTheme: AppComponentThemes.dialogTheme,
      
      // Icon Theme
      iconTheme: const IconThemeData(
        color: AppColors.neutral700,
        size: AppDimensions.iconM,
      ),
      
      // Primary Icon Theme
      primaryIconTheme: const IconThemeData(
        color: AppColors.textOnPrimary,
        size: AppDimensions.iconM,
      ),
      
      // Highlight Color
      highlightColor: AppColors.kienlongOrange.withValues(alpha: 0.1),
      
      // Splash Color
      splashColor: AppColors.kienlongOrange.withValues(alpha: 0.2),
      
      // Focus Color
      focusColor: AppColors.kienlongOrange.withValues(alpha: 0.1),
      
      // Hover Color
      hoverColor: AppColors.kienlongOrange.withValues(alpha: 0.05),
      
      // Disabled Color
      disabledColor: AppColors.neutral400,
      
      // Unselected Widget Color
      unselectedWidgetColor: AppColors.neutral500,
      
      // Secondary Header Color
      secondaryHeaderColor: AppColors.backgroundSecondary,
      
      // Visual Density
      visualDensity: VisualDensity.adaptivePlatformDensity,
      
      // Page Transitions
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
      
      // Extensions
      extensions: [
        BankingThemeExtension.light,
        ResponsiveThemeExtension.standard,
        AnimationThemeExtension.standard,
      ],
    );
  }

  // Dark Theme - Theme tối
  static ThemeData get darkTheme {
    return ThemeData(
      // Sử dụng Material 3
      useMaterial3: true,
      
      // Color Scheme
      colorScheme: _darkColorScheme,
      
      // Brightness
      brightness: Brightness.dark,
      
      // Primary Swatch (simplified for compatibility)
      primarySwatch: Colors.orange,
      
      // Background colors
      scaffoldBackgroundColor: AppColors.backgroundDark,
      canvasColor: AppColors.backgroundDark,
      cardColor: AppColors.backgroundDarkSecondary,
      
      // Typography
      textTheme: AppTypography.textTheme.apply(
        bodyColor: AppColors.neutral100,
        displayColor: AppColors.neutral100,
      ),
      
      // Primary Text Theme (for app bars, etc.)
      primaryTextTheme: AppTypography.textTheme.apply(
        bodyColor: AppColors.textOnPrimary,
        displayColor: AppColors.textOnPrimary,
      ),
      
      // Component Themes (modified for dark mode)
      appBarTheme: AppComponentThemes.appBarTheme.copyWith(
        backgroundColor: AppColors.kienlongDarkBlue,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),
      elevatedButtonTheme: AppComponentThemes.elevatedButtonTheme,
      outlinedButtonTheme: AppComponentThemes.outlinedButtonTheme,
      textButtonTheme: AppComponentThemes.textButtonTheme,
      cardTheme: AppComponentThemes.cardTheme.copyWith(
        color: AppColors.backgroundDarkSecondary,
      ),
      inputDecorationTheme: AppComponentThemes.inputDecorationTheme.copyWith(
        fillColor: AppColors.backgroundDarkTertiary,
      ),
      floatingActionButtonTheme: AppComponentThemes.floatingActionButtonTheme,
      bottomNavigationBarTheme: AppComponentThemes.bottomNavigationBarTheme.copyWith(
        backgroundColor: AppColors.backgroundDarkSecondary,
      ),
      tabBarTheme: AppComponentThemes.tabBarTheme,
      chipTheme: AppComponentThemes.chipTheme.copyWith(
        backgroundColor: AppColors.backgroundDarkTertiary,
      ),
      switchTheme: AppComponentThemes.switchTheme,
      checkboxTheme: AppComponentThemes.checkboxTheme,
      radioTheme: AppComponentThemes.radioTheme,
      progressIndicatorTheme: AppComponentThemes.progressIndicatorTheme,
      dividerTheme: AppComponentThemes.dividerTheme.copyWith(
        color: AppColors.neutral700,
      ),
      listTileTheme: AppComponentThemes.listTileTheme.copyWith(
        textColor: AppColors.neutral100,
        iconColor: AppColors.neutral400,
      ),
      snackBarTheme: AppComponentThemes.snackBarTheme,
      dialogTheme: AppComponentThemes.dialogTheme.copyWith(
        backgroundColor: AppColors.backgroundDarkSecondary,
      ),
      
      // Icon Theme
      iconTheme: const IconThemeData(
        color: AppColors.neutral400,
        size: AppDimensions.iconM,
      ),
      
      // Primary Icon Theme
      primaryIconTheme: const IconThemeData(
        color: AppColors.textOnPrimary,
        size: AppDimensions.iconM,
      ),
      
      // Highlight Color
      highlightColor: AppColors.kienlongOrange.withValues(alpha: 0.1),
      
      // Splash Color
      splashColor: AppColors.kienlongOrange.withValues(alpha: 0.2),
      
      // Focus Color
      focusColor: AppColors.kienlongOrange.withValues(alpha: 0.1),
      
      // Hover Color
      hoverColor: AppColors.kienlongOrange.withValues(alpha: 0.05),
      
      // Disabled Color
      disabledColor: AppColors.neutral600,
      
      // Unselected Widget Color
      unselectedWidgetColor: AppColors.neutral500,
      
      // Secondary Header Color
      secondaryHeaderColor: AppColors.backgroundDarkTertiary,
      
      // Visual Density
      visualDensity: VisualDensity.adaptivePlatformDensity,
      
      // Page Transitions
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
      
      // Extensions
      extensions: [
        BankingThemeExtension.dark,
        ResponsiveThemeExtension.standard,
        AnimationThemeExtension.standard,
      ],
    );
  }

  // Light Color Scheme
  static ColorScheme get _lightColorScheme {
    return ColorScheme.fromSeed(
      seedColor: AppColors.kienlongOrange,
      brightness: Brightness.light,
      primary: AppColors.kienlongOrange,
      onPrimary: AppColors.textOnPrimary,
      secondary: AppColors.kienlongSkyBlue,
      onSecondary: AppColors.textOnSecondary,
      tertiary: AppColors.kienlongDarkBlue,
      surface: AppColors.backgroundSecondary, // White for cards
      onSurface: AppColors.textPrimary,
      error: AppColors.error,
      onError: AppColors.textOnPrimary,
      outline: AppColors.borderMedium,
      outlineVariant: AppColors.borderLight,
      surfaceContainerHighest: AppColors.backgroundTertiary,
      onSurfaceVariant: AppColors.textSecondary,
    );
  }

  // Dark Color Scheme
  static ColorScheme get _darkColorScheme {
    return ColorScheme.fromSeed(
      seedColor: AppColors.kienlongOrange,
      brightness: Brightness.dark,
      primary: AppColors.kienlongOrange,
      onPrimary: AppColors.textOnPrimary,
      secondary: AppColors.kienlongSkyBlue,
      onSecondary: AppColors.textOnSecondary,
      tertiary: AppColors.kienlongDarkBlue,
      surface: AppColors.backgroundDarkSecondary,
      onSurface: AppColors.neutral100,
      error: AppColors.errorLight,
      onError: AppColors.backgroundDark,
      outline: AppColors.neutral600,
      outlineVariant: AppColors.neutral700,
      surfaceContainerHighest: AppColors.backgroundDarkTertiary,
      onSurfaceVariant: AppColors.neutral300,
    );
  }



  // System UI Overlay Styles
  static SystemUiOverlayStyle get lightSystemUiOverlayStyle {
    return const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
      systemNavigationBarColor: AppColors.backgroundPrimary,
      systemNavigationBarDividerColor: AppColors.borderLight,
      systemNavigationBarIconBrightness: Brightness.dark,
    );
  }

  static SystemUiOverlayStyle get darkSystemUiOverlayStyle {
    return const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
      systemNavigationBarColor: AppColors.backgroundDark,
      systemNavigationBarDividerColor: AppColors.neutral700,
      systemNavigationBarIconBrightness: Brightness.light,
    );
  }

  // Helper methods để apply system UI overlay
  static void setLightSystemUIOverlay() {
    SystemChrome.setSystemUIOverlayStyle(lightSystemUiOverlayStyle);
  }

  static void setDarkSystemUIOverlay() {
    SystemChrome.setSystemUIOverlayStyle(darkSystemUiOverlayStyle);
  }

  // Method để apply theme dựa vào brightness
  static void setSystemUIOverlay(Brightness brightness) {
    if (brightness == Brightness.light) {
      setLightSystemUIOverlay();
    } else {
      setDarkSystemUIOverlay();
    }
  }

  // Banking specific theme getters
  static BoxDecoration getAccountCardDecoration(BuildContext context, {String cardType = 'normal'}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (isDarkMode) {
      return BoxDecoration(
        gradient: const LinearGradient(
          colors: [AppColors.kienlongDarkBlue, AppColors.neutral800],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.accountCardRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowDark,
            blurRadius: AppDimensions.shadowBlurRadiusM,
            offset: const Offset(0, 4),
          ),
        ],
      );
    }
    
    switch (cardType.toLowerCase()) {
      case 'vip':
      case 'premium':
        return BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.cardGoldVip,
              AppColors.cardGoldVip.withValues(alpha: 0.8),
              AppColors.kienlongOrange,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            stops: const [0.0, 0.5, 1.0],
          ),
          borderRadius: BorderRadius.circular(AppDimensions.accountCardRadius),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardGoldVip.withValues(alpha: 0.4),
              blurRadius: AppDimensions.shadowBlurRadiusL,
              offset: const Offset(0, 6),
              spreadRadius: 1,
            ),
          ],
        );
      default:
        return BoxDecoration(
          gradient: const LinearGradient(
            colors: [AppColors.kienlongOrange, AppColors.kienlongSkyBlue],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.accountCardRadius),
          boxShadow: [
            BoxShadow(
              color: AppColors.kienlongOrange.withValues(alpha: 0.3),
              blurRadius: AppDimensions.shadowBlurRadiusM,
              offset: const Offset(0, 4),
            ),
          ],
        );
    }
  }

  // Get transaction amount text style based on type
  static TextStyle getTransactionAmountStyle(String transactionType, {bool isDarkMode = false}) {
    switch (transactionType.toLowerCase()) {
      case 'income':
      case 'deposit':
      case 'transfer_in':
        return AppTypography.transactionAmount.copyWith(
          color: isDarkMode ? AppColors.successLight : AppColors.transactionIncome,
          fontWeight: FontWeight.w600,
        );
      case 'expense':
      case 'withdrawal':
      case 'transfer_out':
        return AppTypography.transactionAmount.copyWith(
          color: isDarkMode ? AppColors.errorLight : AppColors.transactionExpense,
          fontWeight: FontWeight.w600,
        );
      case 'pending':
        return AppTypography.transactionAmount.copyWith(
          color: isDarkMode ? AppColors.warningLight : AppColors.transactionPending,
          fontWeight: FontWeight.w500,
        );
      default:
        return AppTypography.transactionAmount;
    }
  }
} 