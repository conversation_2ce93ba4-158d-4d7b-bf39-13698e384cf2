import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';
import 'banking_theme.dart';
import 'theme_extensions.dart';

/// Example file showing how to use Kiloba Business Theme System
/// File ví dụ cho thấy cách sử dụng hệ thống theme Kiloba Business
class ThemeExamplePage extends StatelessWidget {
  const ThemeExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kiloba Business Theme Example'),
      ),
      body: SingleChildScrollView(
        padding: context.responsivePadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Banking Colors Demo
            _buildColorsDemoSection(context),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Typography Demo
            _buildTypographyDemoSection(context),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Banking Components Demo
            _buildBankingComponentsSection(context),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Buttons Demo
            _buildButtonsDemoSection(context),
            const SizedBox(height: AppDimensions.spacingL),
            
            // Cards Demo
            _buildCardsDemoSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildColorsDemoSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Brand Colors',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppDimensions.spacingM),
            Row(
              children: [
                _buildColorChip(
                  context,
                  'Orange',
                  AppColors.kienlongOrange,
                  AppColors.textOnPrimary,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                _buildColorChip(
                  context,
                  'Sky Blue',
                  AppColors.kienlongSkyBlue,
                  AppColors.textOnSecondary,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                _buildColorChip(
                  context,
                  'Dark Blue',
                  AppColors.kienlongDarkBlue,
                  AppColors.textOnPrimary,
                ),
              ],
            ),
            const SizedBox(height: AppDimensions.spacingM),
            Text(
              'Transaction Colors',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Row(
              children: [
                _buildColorChip(
                  context,
                  'Income',
                  AppColors.transactionIncome,
                  AppColors.textOnPrimary,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                _buildColorChip(
                  context,
                  'Expense',
                  AppColors.transactionExpense,
                  AppColors.textOnPrimary,
                ),
                const SizedBox(width: AppDimensions.spacingM),
                _buildColorChip(
                  context,
                  'Pending',
                  AppColors.transactionPending,
                  AppColors.textOnPrimary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorChip(BuildContext context, String label, Color color, Color textColor) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Text(
        label,
        style: Theme.of(context).textTheme.labelMedium?.copyWith(
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildTypographyDemoSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Typography',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppDimensions.spacingM),
            
            Text(
              'Display Large',
              style: Theme.of(context).textTheme.displayLarge,
            ),
            Text(
              'Headline Large',
              style: Theme.of(context).textTheme.headlineLarge,
            ),
            Text(
              'Title Large',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            Text(
              'Body Large - Regular text content',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            Text(
              'Body Medium - Secondary text content',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Label Large - Button text',
              style: Theme.of(context).textTheme.labelLarge,
            ),
            
            const SizedBox(height: AppDimensions.spacingM),
            Text(
              'Banking Typography',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.kienlongOrange,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '1,234,567,890 VND',
                    style: AppTypography.accountBalanceLarge,
                  ),
                  Text(
                    '1234 5678 9012 3456',
                    style: AppTypography.accountNumber,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppDimensions.spacingM),
            Row(
              children: [
                Text(
                  '+500,000 VND',
                  style: AppTypography.transactionAmount.copyWith(
                    color: AppColors.transactionIncome,
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingL),
                Text(
                  '-250,000 VND',
                  style: AppTypography.transactionAmount.copyWith(
                    color: AppColors.transactionExpense,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankingComponentsSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Banking Components',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppDimensions.spacingM),
            
            // Account Card Example
            Container(
              height: AppDimensions.accountCardHeight,
              decoration: BankingTheme.accountCardDecoration,
              padding: BankingTheme.accountCardPadding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tài khoản tiết kiệm',
                    style: AppTypography.textTheme.titleMedium?.copyWith(
                      color: AppColors.textOnPrimary,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '12,345,678 VND',
                    style: AppTypography.accountBalanceLarge,
                  ),
                  const SizedBox(height: AppDimensions.spacingS),
                  Text(
                    '1234 5678 9012 3456',
                    style: AppTypography.accountNumber,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppDimensions.spacingL),
            
            // Transaction Items Example
            Text(
              'Transaction Items',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: AppDimensions.spacingS),
            
            _buildTransactionItem(
              context,
              'Chuyển tiền',
              'income',
              '+1,000,000 VND',
              '15:30',
            ),
            const SizedBox(height: AppDimensions.spacingS),
            _buildTransactionItem(
              context,
              'Thanh toán hóa đơn',
              'expense',
              '-500,000 VND',
              '14:20',
            ),
            const SizedBox(height: AppDimensions.spacingS),
            _buildTransactionItem(
              context,
              'Chờ xử lý',
              'pending',
              '-200,000 VND',
              '13:10',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(
    BuildContext context,
    String title,
    String type,
    String amount,
    String time,
  ) {
    return Container(
      padding: BankingTheme.transactionItemPadding,
      decoration: BankingTheme.getTransactionDecoration(transactionType: type),
      child: Row(
        children: [
          Container(
            width: AppDimensions.transactionIconSize,
            height: AppDimensions.transactionIconSize,
            decoration: BoxDecoration(
              color: BankingTheme.getTransactionStatusColor(type).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.transactionIconSize / 2),
            ),
            child: Icon(
              type == 'income' ? Icons.arrow_downward : 
              type == 'expense' ? Icons.arrow_upward : Icons.access_time,
              color: BankingTheme.getTransactionStatusColor(type),
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: BankingTheme.getAmountTextStyle(transactionType: type),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonsDemoSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Buttons',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppDimensions.spacingM),
            
            ElevatedButton(
              onPressed: () {},
              child: const Text('Elevated Button'),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            
            OutlinedButton(
              onPressed: () {},
              child: const Text('Outlined Button'),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            
            TextButton(
              onPressed: () {},
              child: const Text('Text Button'),
            ),
            const SizedBox(height: AppDimensions.spacingM),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    child: const Text('Chuyển tiền'),
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: const Text('Lịch sử'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardsDemoSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cards',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: AppDimensions.spacingM),
            
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        children: [
                          Icon(
                            Icons.account_balance_wallet,
                            size: AppDimensions.iconL,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: AppDimensions.spacingS),
                          Text(
                            'Ví điện tử',
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(AppDimensions.paddingM),
                      child: Column(
                        children: [
                          Icon(
                            Icons.credit_card,
                            size: AppDimensions.iconL,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(height: AppDimensions.spacingS),
                          Text(
                            'Thẻ tín dụng',
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 