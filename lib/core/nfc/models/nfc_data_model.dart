import 'dart:typed_data';
import 'mrz_info.dart';
import 'citizen_info.dart';
import 'face_image_info.dart';

/// Model tổng hợp chứa tất cả thông tin đã parse từ NFC
class NfcDataModel {
  final MrzInfo? mrzInfo;
  final FaceImageInfo? faceImage;
  final CitizenInfo? citizenInfo;
  final Map<String, dynamic>? rawData;
  final DateTime? parsedAt;

  const NfcDataModel({
    this.mrzInfo,
    this.faceImage,
    this.citizenInfo,
    this.rawData,
    this.parsedAt,
  });

  /// Tạo từ raw DG data
  factory NfcDataModel.fromRawData(Map<String, dynamic> rawDgData) {
    return NfcDataModel(
      rawData: rawDgData,
      parsedAt: DateTime.now(),
    );
  }

  /// Kiểm tra có dữ liệu hợp lệ không
  bool get isValid => 
    mrzInfo != null || 
    citizenInfo != null || 
    faceImage != null;

  /// <PERSON><PERSON><PERSON> tra có thông tin cơ bản không
  bool get hasBasicInfo => 
    mrzInfo != null || 
    citizenInfo != null;

  /// Kiểm tra có ảnh khuôn mặt không
  bool get hasFaceImage => faceImage != null && faceImage!.hasImageData;

  /// Tên đầy đủ (ưu tiên từ DG13, fallback về MRZ)
  String? get fullName {
    if (citizenInfo?.fullName.isNotEmpty == true) {
      return citizenInfo!.fullName;
    }
    if (mrzInfo?.fullName.isNotEmpty == true) {
      return mrzInfo!.fullName;
    }
    return null;
  }

  /// Số giấy tờ (ưu tiên từ DG13, fallback về MRZ)
  String? get documentNumber {
    if (citizenInfo?.documentNumber.isNotEmpty == true) {
      return citizenInfo!.documentNumber;
    }
    if (mrzInfo?.documentNumber.isNotEmpty == true) {
      return mrzInfo!.documentNumber;
    }
    return null;
  }

  /// Ngày sinh (ưu tiên từ DG13, fallback về MRZ)
  DateTime? get dateOfBirth {
    final citizenDate = citizenInfo?.parsedDateOfBirth;
    if (citizenDate != null) {
      return citizenDate;
    }
    return mrzInfo?.dateOfBirth;
  }

  /// Giới tính (ưu tiên từ DG13, fallback về MRZ)
  String? get gender {
    if (citizenInfo?.sex.isNotEmpty == true) {
      return citizenInfo!.sex;
    }
    if (mrzInfo?.gender.isNotEmpty == true) {
      return mrzInfo!.gender;
    }
    return null;
  }

  /// Quốc tịch (ưu tiên từ DG13, fallback về MRZ)
  String? get nationality {
    if (citizenInfo?.nationality.isNotEmpty == true) {
      return citizenInfo!.nationality;
    }
    if (mrzInfo?.nationality.isNotEmpty == true) {
      return mrzInfo!.nationality;
    }
    return null;
  }

  /// Ngày hết hạn (ưu tiên từ DG13, fallback về MRZ)
  DateTime? get dateOfExpiry {
    final citizenDate = citizenInfo?.parsedDateOfExpiry;
    if (citizenDate != null) {
      return citizenDate;
    }
    return mrzInfo?.dateOfExpiry;
  }

  /// Kiểm tra thẻ đã hết hạn chưa
  bool get isExpired {
    final expiryDate = dateOfExpiry;
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate);
  }

  /// Thông tin chi tiết từ DG13
  String? get ethnicity => citizenInfo?.ethnicity;
  String? get religion => citizenInfo?.religion;
  String? get placeOfOrigin => citizenInfo?.placeOfOrigin;
  String? get placeOfResidence => citizenInfo?.placeOfResidence;
  String? get personalIdentification => citizenInfo?.personalIdentification;
  String? get fatherFullName => citizenInfo?.fatherFullName;
  String? get motherFullName => citizenInfo?.motherFullName;
  String? get spouseFullName => citizenInfo?.spouseFullName;

  /// Thông tin ảnh khuôn mặt
  Uint8List? get faceImageData => faceImage?.imageData;
  String? get faceImageFormat => faceImage?.imageFormat;
  String? get faceImageSize => faceImage?.imageSize;
  int? get faceImageQuality => faceImage?.quality;

  /// Validation status
  String get validationStatus {
    if (!isValid) return 'Không có dữ liệu hợp lệ';
    if (isExpired) return 'Thẻ đã hết hạn';
    return 'Hợp lệ';
  }

  /// Tạo copy với thông tin được cập nhật
  NfcDataModel copyWith({
    MrzInfo? mrzInfo,
    FaceImageInfo? faceImage,
    CitizenInfo? citizenInfo,
    Map<String, dynamic>? rawData,
    DateTime? parsedAt,
  }) {
    return NfcDataModel(
      mrzInfo: mrzInfo ?? this.mrzInfo,
      faceImage: faceImage ?? this.faceImage,
      citizenInfo: citizenInfo ?? this.citizenInfo,
      rawData: rawData ?? this.rawData,
      parsedAt: parsedAt ?? this.parsedAt,
    );
  }

  /// Chuyển đổi thành Map
  Map<String, dynamic> toMap() {
    return {
      'mrzInfo': mrzInfo?.toString(),
      'faceImage': faceImage?.toMap(),
      'citizenInfo': citizenInfo?.toMap(),
      'rawData': rawData,
      'parsedAt': parsedAt?.toIso8601String(),
      'isValid': isValid,
      'hasBasicInfo': hasBasicInfo,
      'hasFaceImage': hasFaceImage,
      'isExpired': isExpired,
      'validationStatus': validationStatus,
    };
  }

  /// Tạo từ Map
  factory NfcDataModel.fromMap(Map<String, dynamic> map) {
    return NfcDataModel(
      mrzInfo: null, // TODO: Implement from string
      faceImage: map['faceImage'] != null 
          ? FaceImageInfo.fromMap(Map<String, dynamic>.from(map['faceImage']))
          : null,
      citizenInfo: map['citizenInfo'] != null 
          ? CitizenInfo.fromMap(Map<String, dynamic>.from(map['citizenInfo']))
          : null,
      rawData: map['rawData'] != null 
          ? Map<String, dynamic>.from(map['rawData'])
          : null,
      parsedAt: map['parsedAt'] != null 
          ? DateTime.parse(map['parsedAt'])
          : null,
    );
  }

  @override
  String toString() {
    return '''
NfcDataModel:
- Valid: $isValid
- Has Basic Info: $hasBasicInfo
- Has Face Image: $hasFaceImage
- Is Expired: $isExpired
- Validation Status: $validationStatus
- Full Name: $fullName
- Document Number: $documentNumber
- Date of Birth: $dateOfBirth
- Gender: $gender
- Nationality: $nationality
- Date of Expiry: $dateOfExpiry
- Parsed At: $parsedAt
''';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NfcDataModel &&
        other.mrzInfo == mrzInfo &&
        other.faceImage == faceImage &&
        other.citizenInfo == citizenInfo &&
        other.parsedAt == parsedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      mrzInfo,
      faceImage,
      citizenInfo,
      parsedAt,
    );
  }
} 