/// Model chứa thông tin công dân từ DG13
class CitizenInfo {
  final String documentNumber;
  final String fullName;
  final String dateOfBirth;
  final String sex;
  final String nationality;
  final String ethnicity;
  final String religion;
  final String placeOfOrigin;
  final String placeOfResidence;
  final String personalIdentification;
  final String dateOfRelease;
  final String dateOfExpiry;
  final String fatherFullName;
  final String motherFullName;
  final String spouseFullName;
  final String oldDocumentNumber;

  const CitizenInfo({
    this.documentNumber = '',
    this.fullName = '',
    this.dateOfBirth = '',
    this.sex = '',
    this.nationality = '',
    this.ethnicity = '',
    this.religion = '',
    this.placeOfOrigin = '',
    this.placeOfResidence = '',
    this.personalIdentification = '',
    this.dateOfRelease = '',
    this.dateOfExpiry = '',
    this.fatherFullName = '',
    this.motherFullName = '',
    this.spouseFullName = '',
    this.oldDocumentNumber = '',
  });

  /// <PERSON><PERSON>m tra thông tin có rỗng không
  bool get isEmpty => 
    documentNumber.isEmpty && 
    fullName.isEmpty && 
    personalIdentification.isEmpty;

  /// Kiểm tra thông tin có hợp lệ không
  bool get isValid => 
    documentNumber.isNotEmpty && 
    fullName.isNotEmpty && 
    personalIdentification.isNotEmpty;

  /// Kiểm tra có thông tin đầy đủ không
  bool get hasCompleteInfo => 
    isValid && 
    dateOfBirth.isNotEmpty && 
    sex.isNotEmpty && 
    nationality.isNotEmpty;

  /// Parse date từ string format
  DateTime? get parsedDateOfBirth {
    if (dateOfBirth.isEmpty) return null;
    try {
      // Thử parse các format khác nhau
      if (dateOfBirth.contains('/')) {
        final parts = dateOfBirth.split('/');
        if (parts.length == 3) {
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          return DateTime(year, month, day);
        }
      } else if (dateOfBirth.length == 8) {
        // Format: DDMMYYYY
        final day = int.parse(dateOfBirth.substring(0, 2));
        final month = int.parse(dateOfBirth.substring(2, 4));
        final year = int.parse(dateOfBirth.substring(4, 8));
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return null;
  }

  /// Parse date of expiry
  DateTime? get parsedDateOfExpiry {
    if (dateOfExpiry.isEmpty) return null;
    try {
      if (dateOfExpiry.contains('/')) {
        final parts = dateOfExpiry.split('/');
        if (parts.length == 3) {
          final day = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final year = int.parse(parts[2]);
          return DateTime(year, month, day);
        }
      } else if (dateOfExpiry.length == 8) {
        final day = int.parse(dateOfExpiry.substring(0, 2));
        final month = int.parse(dateOfExpiry.substring(2, 4));
        final year = int.parse(dateOfExpiry.substring(4, 8));
        return DateTime(year, month, day);
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return null;
  }

  /// Kiểm tra thẻ đã hết hạn chưa
  bool get isExpired {
    final expiryDate = parsedDateOfExpiry;
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate);
  }

  /// Tạo copy với thông tin được cập nhật
  CitizenInfo copyWith({
    String? documentNumber,
    String? fullName,
    String? dateOfBirth,
    String? sex,
    String? nationality,
    String? ethnicity,
    String? religion,
    String? placeOfOrigin,
    String? placeOfResidence,
    String? personalIdentification,
    String? dateOfRelease,
    String? dateOfExpiry,
    String? fatherFullName,
    String? motherFullName,
    String? spouseFullName,
    String? oldDocumentNumber,
  }) {
    return CitizenInfo(
      documentNumber: documentNumber ?? this.documentNumber,
      fullName: fullName ?? this.fullName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      sex: sex ?? this.sex,
      nationality: nationality ?? this.nationality,
      ethnicity: ethnicity ?? this.ethnicity,
      religion: religion ?? this.religion,
      placeOfOrigin: placeOfOrigin ?? this.placeOfOrigin,
      placeOfResidence: placeOfResidence ?? this.placeOfResidence,
      personalIdentification: personalIdentification ?? this.personalIdentification,
      dateOfRelease: dateOfRelease ?? this.dateOfRelease,
      dateOfExpiry: dateOfExpiry ?? this.dateOfExpiry,
      fatherFullName: fatherFullName ?? this.fatherFullName,
      motherFullName: motherFullName ?? this.motherFullName,
      spouseFullName: spouseFullName ?? this.spouseFullName,
      oldDocumentNumber: oldDocumentNumber ?? this.oldDocumentNumber,
    );
  }

  /// Chuyển đổi thành Map
  Map<String, dynamic> toMap() {
    return {
      'documentNumber': documentNumber,
      'fullName': fullName,
      'dateOfBirth': dateOfBirth,
      'sex': sex,
      'nationality': nationality,
      'ethnicity': ethnicity,
      'religion': religion,
      'placeOfOrigin': placeOfOrigin,
      'placeOfResidence': placeOfResidence,
      'personalIdentification': personalIdentification,
      'dateOfRelease': dateOfRelease,
      'dateOfExpiry': dateOfExpiry,
      'fatherFullName': fatherFullName,
      'motherFullName': motherFullName,
      'spouseFullName': spouseFullName,
      'oldDocumentNumber': oldDocumentNumber,
    };
  }

  /// Tạo từ Map
  factory CitizenInfo.fromMap(Map<String, dynamic> map) {
    return CitizenInfo(
      documentNumber: map['documentNumber'] ?? '',
      fullName: map['fullName'] ?? '',
      dateOfBirth: map['dateOfBirth'] ?? '',
      sex: map['sex'] ?? '',
      nationality: map['nationality'] ?? '',
      ethnicity: map['ethnicity'] ?? '',
      religion: map['religion'] ?? '',
      placeOfOrigin: map['placeOfOrigin'] ?? '',
      placeOfResidence: map['placeOfResidence'] ?? '',
      personalIdentification: map['personalIdentification'] ?? '',
      dateOfRelease: map['dateOfRelease'] ?? '',
      dateOfExpiry: map['dateOfExpiry'] ?? '',
      fatherFullName: map['fatherFullName'] ?? '',
      motherFullName: map['motherFullName'] ?? '',
      spouseFullName: map['spouseFullName'] ?? '',
      oldDocumentNumber: map['oldDocumentNumber'] ?? '',
    );
  }

  @override
  String toString() {
    return '''
CitizenInfo:
- Document Number: $documentNumber
- Full Name: $fullName
- Date of Birth: $dateOfBirth
- Sex: $sex
- Nationality: $nationality
- Ethnicity: $ethnicity
- Religion: $religion
- Place of Origin: $placeOfOrigin
- Place of Residence: $placeOfResidence
- Personal Identification: $personalIdentification
- Date of Release: $dateOfRelease
- Date of Expiry: $dateOfExpiry
- Father Full Name: $fatherFullName
- Mother Full Name: $motherFullName
- Spouse Full Name: $spouseFullName
- Old Document Number: $oldDocumentNumber
''';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CitizenInfo &&
        other.documentNumber == documentNumber &&
        other.fullName == fullName &&
        other.dateOfBirth == dateOfBirth &&
        other.sex == sex &&
        other.nationality == nationality &&
        other.ethnicity == ethnicity &&
        other.religion == religion &&
        other.placeOfOrigin == placeOfOrigin &&
        other.placeOfResidence == placeOfResidence &&
        other.personalIdentification == personalIdentification &&
        other.dateOfRelease == dateOfRelease &&
        other.dateOfExpiry == dateOfExpiry &&
        other.fatherFullName == fatherFullName &&
        other.motherFullName == motherFullName &&
        other.spouseFullName == spouseFullName &&
        other.oldDocumentNumber == oldDocumentNumber;
  }

  @override
  int get hashCode {
    return Object.hash(
      documentNumber,
      fullName,
      dateOfBirth,
      sex,
      nationality,
      ethnicity,
      religion,
      placeOfOrigin,
      placeOfResidence,
      personalIdentification,
      dateOfRelease,
      dateOfExpiry,
      fatherFullName,
      motherFullName,
      spouseFullName,
      oldDocumentNumber,
    );
  }
} 