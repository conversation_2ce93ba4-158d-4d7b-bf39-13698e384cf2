import 'package:dmrtd/dmrtd.dart';

/// Model chứa thông tin MRZ (Machine Readable Zone) từ DG1
class MrzInfo {
  final String documentCode;
  final String issuingState;
  final String primaryIdentifier; // Last name
  final String secondaryIdentifier; // First name(s)
  final String nationality;
  final String documentNumber;
  final DateTime dateOfBirth;
  final String gender;
  final DateTime dateOfExpiry;
  final String personalNumber;
  final String optionalData1;
  final String optionalData2;
  final int documentType; // ID1, ID2, ID3

  const MrzInfo({
    required this.documentCode,
    required this.issuingState,
    required this.primaryIdentifier,
    required this.secondaryIdentifier,
    required this.nationality,
    required this.documentNumber,
    required this.dateOfBirth,
    required this.gender,
    required this.dateOfExpiry,
    this.personalNumber = '',
    this.optionalData1 = '',
    this.optionalData2 = '',
    required this.documentType,
  });

  /// Tạo MrzInfo từ MRZ object của DMRTD plugin
  factory MrzInfo.fromMrz(MRZ mrz) {
    return MrzInfo(
      documentCode: mrz.documentCode,
      issuingState: mrz.country,
      primaryIdentifier: mrz.lastName,
      secondaryIdentifier: mrz.firstName,
      nationality: mrz.nationality,
      documentNumber: mrz.documentNumber,
      dateOfBirth: mrz.dateOfBirth,
      gender: mrz.gender,
      dateOfExpiry: mrz.dateOfExpiry,
      personalNumber: mrz.optionalData,
      optionalData1: mrz.optionalData,
      optionalData2: mrz.optionalData2 ?? '',
      documentType: _getDocumentTypeFromVersion(mrz.version),
    );
  }

  /// Tạo MrzInfo từ raw MRZ string
  factory MrzInfo.fromString(String mrzString) {
    // Parse MRZ string theo format chuẩn
    // P<UTOD23145890<1233<<<<<<<<<<<<<
    // 6408125<2010315D<<<<<<<<<<<<<4
    // DOE<<JOHN<<<<<<<<<<<<<<<<<<<<<<
    
    final lines = mrzString.trim().split('\n');
    if (lines.length < 2) {
      throw ArgumentError('Invalid MRZ format: must have at least 2 lines');
    }

    final line1 = lines[0];
    final line2 = lines[1];
    
    // Parse line 1
    final documentCode = line1.substring(0, 2);
    final issuingState = line1.substring(2, 5);
    final documentNumber = line1.substring(5, 14).replaceAll('<', '');
    final optionalData1 = line1.substring(15, 30).replaceAll('<', '');
    
    // Parse line 2
    final dateOfBirthStr = line2.substring(0, 6);
    final gender = line2.substring(7, 8);
    final dateOfExpiryStr = line2.substring(8, 14);
    final nationality = line2.substring(15, 18);
    final optionalData2 = line2.substring(18, 29).replaceAll('<', '');
    
    // Parse names (line 3 nếu có, hoặc từ line 1 cho ID3)
    String primaryIdentifier = '';
    String secondaryIdentifier = '';
    
    if (lines.length >= 3) {
      // ID1 format (3 lines)
      final nameLine = lines[2];
      final nameParts = nameLine.split('<<');
      if (nameParts.length >= 2) {
        primaryIdentifier = nameParts[0].replaceAll('<', ' ').trim();
        secondaryIdentifier = nameParts[1].replaceAll('<', ' ').trim();
      }
    } else {
      // ID3 format (2 lines) - names in line 1
      final namePart = line1.substring(5);
      final nameParts = namePart.split('<<');
      if (nameParts.length >= 2) {
        primaryIdentifier = nameParts[0].replaceAll('<', ' ').trim();
        secondaryIdentifier = nameParts[1].replaceAll('<', ' ').trim();
      }
    }
    
    return MrzInfo(
      documentCode: documentCode,
      issuingState: issuingState,
      primaryIdentifier: primaryIdentifier,
      secondaryIdentifier: secondaryIdentifier,
      nationality: nationality,
      documentNumber: documentNumber,
      dateOfBirth: _parseMrzDate(dateOfBirthStr),
      gender: gender,
      dateOfExpiry: _parseMrzDate(dateOfExpiryStr),
      personalNumber: optionalData1,
      optionalData1: optionalData1,
      optionalData2: optionalData2,
      documentType: _getDocumentTypeFromCode(documentCode),
    );
  }

  /// Tên đầy đủ
  String get fullName => '$primaryIdentifier $secondaryIdentifier'.trim();
  
  /// Kiểm tra thẻ đã hết hạn chưa
  bool get isExpired => DateTime.now().isAfter(dateOfExpiry);
  
  /// Kiểm tra thông tin có hợp lệ không
  bool get isValid => 
    documentNumber.isNotEmpty && 
    fullName.isNotEmpty && 
    dateOfBirth.isBefore(DateTime.now());

  /// Chuyển đổi document type từ MRZ version
  static int _getDocumentTypeFromVersion(MRZVersion version) {
    switch (version) {
      case MRZVersion.td1:
        return 1; // ID1
      case MRZVersion.td2:
        return 2; // ID2
      case MRZVersion.td3:
        return 3; // ID3
    }
  }

  /// Chuyển đổi document type từ document code
  static int _getDocumentTypeFromCode(String documentCode) {
    if (documentCode.startsWith('I')) return 1; // ID1
    if (documentCode.startsWith('A')) return 2; // ID2
    if (documentCode.startsWith('P')) return 3; // ID3
    return 0; // UNSPECIFIED
  }

  /// Parse date từ MRZ format (YYMMDD)
  static DateTime _parseMrzDate(String dateStr) {
    if (dateStr.length != 6) {
      throw ArgumentError('Invalid MRZ date format: $dateStr');
    }
    
    final year = int.parse(dateStr.substring(0, 2));
    final month = int.parse(dateStr.substring(2, 4));
    final day = int.parse(dateStr.substring(4, 6));
    
    // MRZ sử dụng 2 chữ số cho năm, cần convert sang 4 chữ số
    final fullYear = year < 50 ? 2000 + year : 1900 + year;
    
    return DateTime(fullYear, month, day);
  }

  @override
  String toString() {
    return 'MrzInfo('
        'documentCode: $documentCode, '
        'issuingState: $issuingState, '
        'fullName: $fullName, '
        'documentNumber: $documentNumber, '
        'dateOfBirth: $dateOfBirth, '
        'gender: $gender, '
        'dateOfExpiry: $dateOfExpiry, '
        'nationality: $nationality)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MrzInfo &&
        other.documentCode == documentCode &&
        other.issuingState == issuingState &&
        other.primaryIdentifier == primaryIdentifier &&
        other.secondaryIdentifier == secondaryIdentifier &&
        other.nationality == nationality &&
        other.documentNumber == documentNumber &&
        other.dateOfBirth == dateOfBirth &&
        other.gender == gender &&
        other.dateOfExpiry == dateOfExpiry;
  }

  @override
  int get hashCode {
    return Object.hash(
      documentCode,
      issuingState,
      primaryIdentifier,
      secondaryIdentifier,
      nationality,
      documentNumber,
      dateOfBirth,
      gender,
      dateOfExpiry,
    );
  }
} 