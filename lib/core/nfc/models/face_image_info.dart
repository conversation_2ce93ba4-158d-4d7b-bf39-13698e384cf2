import 'dart:typed_data';

/// Model chứa thông tin ảnh khuôn mặt từ DG2
class FaceImageInfo {
  final Uint8List imageData;
  final String mimeType; // 'image/jpeg' hoặc 'image/jpeg2000'
  final int width;
  final int height;
  final int quality;
  final String gender;
  final String eyeColor;
  final String hairColor;
  final String expression;
  final int faceImageType;
  final int imageDataType;
  final int colorSpace;
  final int sourceType;
  final int deviceType;

  const FaceImageInfo({
    required this.imageData,
    required this.mimeType,
    required this.width,
    required this.height,
    required this.quality,
    required this.gender,
    required this.eyeColor,
    required this.hairColor,
    required this.expression,
    required this.faceImageType,
    required this.imageDataType,
    required this.colorSpace,
    required this.sourceType,
    required this.deviceType,
  });

  /// Kiểm tra ảnh có chất lượng cao không
  bool get isHighQuality => quality > 80;
  
  /// Format ảnh
  String get imageFormat => mimeType.split('/').last.toUpperCase();
  
  /// <PERSON><PERSON><PERSON> tra có dữ liệu ảnh không
  bool get hasImageData => imageData.isNotEmpty;
  
  /// Kích thước ảnh dưới dạng string
  String get imageSize => '${width}x$height';
  
  /// Kích thước file ảnh
  int get fileSize => imageData.length;
  
  /// Kích thước file dưới dạng string
  String get fileSizeString {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  /// Chuyển đổi gender code thành string
  String get genderString {
    switch (gender) {
      case 'M':
        return 'Nam';
      case 'F':
        return 'Nữ';
      case 'X':
        return 'Không xác định';
      default:
        return 'Không rõ';
    }
  }

  /// Chuyển đổi eye color code thành string
  String get eyeColorString {
    switch (eyeColor) {
      case 'BLK':
        return 'Đen';
      case 'BLU':
        return 'Xanh dương';
      case 'BRO':
        return 'Nâu';
      case 'GRY':
        return 'Xám';
      case 'GRN':
        return 'Xanh lá';
      case 'MUL':
        return 'Nhiều màu';
      case 'PNK':
        return 'Hồng';
      case 'XXX':
        return 'Không xác định';
      default:
        return 'Không rõ';
    }
  }

  /// Chuyển đổi hair color code thành string
  String get hairColorString {
    switch (hairColor) {
      case 'BAL':
        return 'Hói';
      case 'BLK':
        return 'Đen';
      case 'BLN':
        return 'Vàng';
      case 'BRO':
        return 'Nâu';
      case 'GRY':
        return 'Xám';
      case 'RED':
        return 'Đỏ';
      case 'SDY':
        return 'Nâu sẫm';
      case 'WHI':
        return 'Trắng';
      case 'XXX':
        return 'Không xác định';
      default:
        return 'Không rõ';
    }
  }

  /// Chuyển đổi expression code thành string
  String get expressionString {
    switch (expression) {
      case 'SMI':
        return 'Cười';
      case 'SER':
        return 'Nghiêm túc';
      case 'XXX':
        return 'Không xác định';
      default:
        return 'Không rõ';
    }
  }

  /// Chuyển đổi face image type thành string
  String get faceImageTypeString {
    switch (faceImageType) {
      case 0:
        return 'Cơ bản';
      case 1:
        return 'Toàn mặt trước';
      case 2:
        return 'Token mặt trước';
      default:
        return 'Không rõ';
    }
  }

  /// Chuyển đổi image data type thành string
  String get imageDataTypeString {
    switch (imageDataType) {
      case 0:
        return 'JPEG';
      case 1:
        return 'JPEG2000';
      default:
        return 'Không rõ';
    }
  }

  /// Chuyển đổi color space thành string
  String get colorSpaceString {
    switch (colorSpace) {
      case 0:
        return 'Không xác định';
      case 1:
        return 'RGB24';
      case 2:
        return 'YUV422';
      case 3:
        return 'GRAY8';
      case 4:
        return 'Khác';
      default:
        return 'Không rõ';
    }
  }

  /// Chuyển đổi source type thành string
  String get sourceTypeString {
    switch (sourceType) {
      case 0:
        return 'Không xác định';
      case 1:
        return 'Ảnh tĩnh - Nguồn không rõ';
      case 2:
        return 'Ảnh tĩnh - Camera số';
      case 3:
        return 'Ảnh tĩnh - Scanner';
      case 4:
        return 'Frame video - Nguồn không rõ';
      case 5:
        return 'Frame video - Camera analog';
      case 6:
        return 'Frame video - Camera số';
      case 7:
        return 'Không rõ';
      default:
        return 'Không xác định';
    }
  }

  /// Tạo copy với thông tin được cập nhật
  FaceImageInfo copyWith({
    Uint8List? imageData,
    String? mimeType,
    int? width,
    int? height,
    int? quality,
    String? gender,
    String? eyeColor,
    String? hairColor,
    String? expression,
    int? faceImageType,
    int? imageDataType,
    int? colorSpace,
    int? sourceType,
    int? deviceType,
  }) {
    return FaceImageInfo(
      imageData: imageData ?? this.imageData,
      mimeType: mimeType ?? this.mimeType,
      width: width ?? this.width,
      height: height ?? this.height,
      quality: quality ?? this.quality,
      gender: gender ?? this.gender,
      eyeColor: eyeColor ?? this.eyeColor,
      hairColor: hairColor ?? this.hairColor,
      expression: expression ?? this.expression,
      faceImageType: faceImageType ?? this.faceImageType,
      imageDataType: imageDataType ?? this.imageDataType,
      colorSpace: colorSpace ?? this.colorSpace,
      sourceType: sourceType ?? this.sourceType,
      deviceType: deviceType ?? this.deviceType,
    );
  }

  /// Chuyển đổi thành Map
  Map<String, dynamic> toMap() {
    return {
      'imageData': imageData,
      'mimeType': mimeType,
      'width': width,
      'height': height,
      'quality': quality,
      'gender': gender,
      'eyeColor': eyeColor,
      'hairColor': hairColor,
      'expression': expression,
      'faceImageType': faceImageType,
      'imageDataType': imageDataType,
      'colorSpace': colorSpace,
      'sourceType': sourceType,
      'deviceType': deviceType,
    };
  }

  /// Tạo từ Map
  factory FaceImageInfo.fromMap(Map<String, dynamic> map) {
    return FaceImageInfo(
      imageData: map['imageData'] ?? Uint8List(0),
      mimeType: map['mimeType'] ?? '',
      width: map['width'] ?? 0,
      height: map['height'] ?? 0,
      quality: map['quality'] ?? 0,
      gender: map['gender'] ?? '',
      eyeColor: map['eyeColor'] ?? '',
      hairColor: map['hairColor'] ?? '',
      expression: map['expression'] ?? '',
      faceImageType: map['faceImageType'] ?? 0,
      imageDataType: map['imageDataType'] ?? 0,
      colorSpace: map['colorSpace'] ?? 0,
      sourceType: map['sourceType'] ?? 0,
      deviceType: map['deviceType'] ?? 0,
    );
  }

  @override
  String toString() {
    return '''
FaceImageInfo:
- Image Format: $imageFormat
- Image Size: $imageSize
- File Size: $fileSizeString
- Quality: $quality
- Gender: $genderString
- Eye Color: $eyeColorString
- Hair Color: $hairColorString
- Expression: $expressionString
- Face Image Type: $faceImageTypeString
- Color Space: $colorSpaceString
- Source Type: $sourceTypeString
- Has Image Data: $hasImageData
- Is High Quality: $isHighQuality
''';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FaceImageInfo &&
        other.mimeType == mimeType &&
        other.width == width &&
        other.height == height &&
        other.quality == quality &&
        other.gender == gender &&
        other.eyeColor == eyeColor &&
        other.hairColor == hairColor &&
        other.expression == expression &&
        other.faceImageType == faceImageType &&
        other.imageDataType == imageDataType &&
        other.colorSpace == colorSpace &&
        other.sourceType == sourceType &&
        other.deviceType == deviceType;
  }

  @override
  int get hashCode {
    return Object.hash(
      mimeType,
      width,
      height,
      quality,
      gender,
      eyeColor,
      hairColor,
      expression,
      faceImageType,
      imageDataType,
      colorSpace,
      sourceType,
      deviceType,
    );
  }
} 