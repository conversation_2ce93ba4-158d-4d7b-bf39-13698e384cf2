import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';
import '../models/nfc_data_model.dart';

/// Widget hiển thị summary của NFC data
class NfcDataSummaryWidget extends StatelessWidget {
  final NfcDataModel nfcData;
  final VoidCallback? onTap;

  const NfcDataSummaryWidget({super.key, required this.nfcData, this.onTap});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.spacingM),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Ảnh khuôn mặt nhỏ
              if (nfcData.hasFaceImage) ...[
                _buildFaceImageThumbnail(context, isDarkMode),
                const SizedBox(width: AppDimensions.spacingM),
              ],
              // Thông tin
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(context, isDarkMode),
                    const SizedBox(height: AppDimensions.spacingS),
                    _buildBasicInfo(context, isDarkMode),
                    const SizedBox(height: AppDimensions.spacingS),
                    _buildValidationStatus(context, isDarkMode),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isDarkMode) {
    return Row(
      children: [
        Icon(Icons.credit_card, color: AppColors.kienlongOrange, size: 24),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Text(
            'Thông tin CCCD',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
        ),
        _buildStatusIcon(context, isDarkMode),
      ],
    );
  }

  Widget _buildStatusIcon(BuildContext context, bool isDarkMode) {
    IconData iconData;
    Color iconColor;

    if (nfcData.isExpired) {
      iconData = Icons.warning;
      iconColor = AppColors.error;
    } else if (nfcData.isValid) {
      iconData = Icons.check_circle;
      iconColor = AppColors.success;
    } else {
      iconData = Icons.error;
      iconColor = AppColors.error;
    }

    return Icon(iconData, color: iconColor, size: 20);
  }

  Widget _buildBasicInfo(BuildContext context, bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (nfcData.fullName != null)
          _buildInfoRow(
            context,
            'Họ tên',
            nfcData.fullName!,
            Icons.person,
            isDarkMode,
          ),
        if (nfcData.documentNumber != null)
          _buildInfoRow(
            context,
            'Số CCCD',
            nfcData.documentNumber!,
            Icons.numbers,
            isDarkMode,
          ),
        if (nfcData.dateOfBirth != null)
          _buildInfoRow(
            context,
            'Ngày sinh',
            _formatDate(nfcData.dateOfBirth!),
            Icons.cake,
            isDarkMode,
          ),
        if (nfcData.gender != null)
          _buildInfoRow(
            context,
            'Giới tính',
            nfcData.gender!,
            Icons.person_outline,
            isDarkMode,
          ),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    bool isDarkMode,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: isDarkMode ? AppColors.neutral400 : AppColors.textSecondary,
          ),
          const SizedBox(width: AppDimensions.spacingXS),
          Expanded(
            child: Text(
              '$label: $value',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isDarkMode
                    ? AppColors.neutral100
                    : AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationStatus(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingS,
        vertical: AppDimensions.spacingXS,
      ),
      decoration: BoxDecoration(
        color: _getValidationStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(
          color: _getValidationStatusColor().withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getValidationStatusIcon(),
            size: 16,
            color: _getValidationStatusColor(),
          ),
          const SizedBox(width: AppDimensions.spacingXS),
          Text(
            nfcData.validationStatus,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getValidationStatusColor(),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getValidationStatusColor() {
    if (nfcData.isExpired) {
      return AppColors.error;
    } else if (nfcData.isValid) {
      return AppColors.success;
    } else {
      return AppColors.warning;
    }
  }

  IconData _getValidationStatusIcon() {
    if (nfcData.isExpired) {
      return Icons.warning;
    } else if (nfcData.isValid) {
      return Icons.check_circle;
    } else {
      return Icons.error;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Hiển thị ảnh khuôn mặt thumbnail
  Widget _buildFaceImageThumbnail(BuildContext context, bool isDarkMode) {
    try {
      if (nfcData.faceImage == null || !nfcData.faceImage!.hasImageData) {
        return _buildThumbnailPlaceholder(isDarkMode);
      }

      return Container(
        width: 60,
        height: 80,
        decoration: BoxDecoration(
          border: Border.all(
            color: AppColors.kienlongOrange.withValues(alpha: 0.3),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          child: Image.memory(
            nfcData.faceImage!.imageData,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return _buildThumbnailPlaceholder(isDarkMode);
            },
          ),
        ),
      );
    } catch (e) {
      return _buildThumbnailPlaceholder(isDarkMode);
    }
  }

  /// Widget placeholder cho thumbnail
  Widget _buildThumbnailPlaceholder(bool isDarkMode) {
    return Container(
      width: 60,
      height: 80,
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkTertiary
            : AppColors.neutral100,
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.neutral300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Icon(
        Icons.face,
        size: 24,
        color: isDarkMode ? AppColors.neutral500 : AppColors.neutral400,
      ),
    );
  }
}
