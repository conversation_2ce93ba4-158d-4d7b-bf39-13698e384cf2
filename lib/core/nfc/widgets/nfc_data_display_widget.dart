import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_dimensions.dart';
import '../models/nfc_data_model.dart';

/// Widget hiển thị chi tiết NFC data
class NfcDataDisplayWidget extends StatelessWidget {
  final NfcDataModel nfcData;

  const NfcDataDisplayWidget({super.key, required this.nfcData});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context, isDarkMode),
          const SizedBox(height: AppDimensions.spacingM),
          _buildBasicInfoSection(context, isDarkMode),
          const SizedBox(height: AppDimensions.spacingM),
          _buildDetailedInfoSection(context, isDarkMode),
          const SizedBox(height: AppDimensions.spacingM),
          _buildFaceImageSection(context, isDarkMode),
          const SizedBox(height: AppDimensions.spacingM),
          _buildValidationSection(context, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isDarkMode) {
    return Row(
      children: [
        Icon(
          Icons.credit_card,
          color: AppColors.kienlongOrange,
          size: AppDimensions.iconM,
        ),
        const SizedBox(width: AppDimensions.spacingS),
        Expanded(
          child: Text(
            'Chi tiết thông tin CCCD',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
        ),
        _buildStatusChip(context, isDarkMode),
      ],
    );
  }

  Widget _buildStatusChip(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.spacingS,
        vertical: AppDimensions.spacingXS,
      ),
      decoration: BoxDecoration(
        color: _getStatusColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: _getStatusColor().withValues(alpha: 0.3)),
      ),
      child: Text(
        nfcData.validationStatus,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: _getStatusColor(),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection(BuildContext context, bool isDarkMode) {
    return _buildSection(context, 'Thông tin cơ bản', Icons.person, [
      if (nfcData.fullName != null)
        _buildInfoRow('Họ tên', nfcData.fullName!, isDarkMode),
      if (nfcData.documentNumber != null)
        _buildInfoRow('Số CCCD', nfcData.documentNumber!, isDarkMode),
      if (nfcData.dateOfBirth != null)
        _buildInfoRow(
          'Ngày sinh',
          _formatDate(nfcData.dateOfBirth!),
          isDarkMode,
        ),
      if (nfcData.gender != null)
        _buildInfoRow('Giới tính', nfcData.gender!, isDarkMode),
      if (nfcData.nationality != null)
        _buildInfoRow('Quốc tịch', nfcData.nationality!, isDarkMode),
      if (nfcData.dateOfExpiry != null)
        _buildInfoRow(
          'Ngày hết hạn',
          _formatDate(nfcData.dateOfExpiry!),
          isDarkMode,
        ),
    ], isDarkMode);
  }

  Widget _buildDetailedInfoSection(BuildContext context, bool isDarkMode) {
    final detailedInfo = <Widget>[];

    if (nfcData.citizenInfo != null) {
      final citizen = nfcData.citizenInfo!;
      if (citizen.ethnicity.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('Dân tộc', citizen.ethnicity, isDarkMode),
        );
      }
      if (citizen.religion.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('Tôn giáo', citizen.religion, isDarkMode),
        );
      }
      if (citizen.placeOfOrigin.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('Quê quán', citizen.placeOfOrigin, isDarkMode),
        );
      }
      if (citizen.placeOfResidence.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('Nơi thường trú', citizen.placeOfResidence, isDarkMode),
        );
      }
      if (citizen.personalIdentification.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('CMND cũ', citizen.personalIdentification, isDarkMode),
        );
      }
      if (citizen.fatherFullName.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('Họ tên cha', citizen.fatherFullName, isDarkMode),
        );
      }
      if (citizen.motherFullName.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('Họ tên mẹ', citizen.motherFullName, isDarkMode),
        );
      }
      if (citizen.spouseFullName.isNotEmpty) {
        detailedInfo.add(
          _buildInfoRow('Họ tên vợ/chồng', citizen.spouseFullName, isDarkMode),
        );
      }
    }

    if (detailedInfo.isNotEmpty) {
      return _buildSection(
        context,
        'Thông tin chi tiết',
        Icons.info,
        detailedInfo,
        isDarkMode,
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildFaceImageSection(BuildContext context, bool isDarkMode) {
    if (nfcData.faceImage == null || !nfcData.faceImage!.hasImageData) {
      return const SizedBox.shrink();
    }

    final faceImage = nfcData.faceImage!;
    return _buildSection(context, 'Ảnh khuôn mặt', Icons.face, [
      // Hiển thị ảnh khuôn mặt
      _buildFaceImageDisplay(context, faceImage, isDarkMode),
      const SizedBox(height: AppDimensions.spacingM),
      // Metadata
      _buildInfoRow('Kích thước', faceImage.imageSize, isDarkMode),
      _buildInfoRow('Format', faceImage.imageFormat, isDarkMode),
      _buildInfoRow('Dung lượng', faceImage.fileSizeString, isDarkMode),
      _buildInfoRow('Chất lượng', '${faceImage.quality}%', isDarkMode),
      _buildInfoRow('Giới tính', faceImage.genderString, isDarkMode),
      _buildInfoRow('Màu mắt', faceImage.eyeColorString, isDarkMode),
      _buildInfoRow('Màu tóc', faceImage.hairColorString, isDarkMode),
      _buildInfoRow('Biểu cảm', faceImage.expressionString, isDarkMode),
      _buildInfoRow('Loại ảnh', faceImage.faceImageTypeString, isDarkMode),
      _buildInfoRow('Không gian màu', faceImage.colorSpaceString, isDarkMode),
      _buildInfoRow('Nguồn', faceImage.sourceTypeString, isDarkMode),
    ], isDarkMode);
  }

  Widget _buildValidationSection(BuildContext context, bool isDarkMode) {
    return _buildSection(context, 'Thông tin xác thực', Icons.verified, [
      _buildInfoRow('Trạng thái', nfcData.validationStatus, isDarkMode),
      _buildInfoRow(
        'Có dữ liệu hợp lệ',
        nfcData.isValid ? 'Có' : 'Không',
        isDarkMode,
      ),
      _buildInfoRow(
        'Có thông tin cơ bản',
        nfcData.hasBasicInfo ? 'Có' : 'Không',
        isDarkMode,
      ),
      _buildInfoRow(
        'Có ảnh khuôn mặt',
        nfcData.hasFaceImage ? 'Có' : 'Không',
        isDarkMode,
      ),
      _buildInfoRow(
        'Đã hết hạn',
        nfcData.isExpired ? 'Có' : 'Không',
        isDarkMode,
      ),
      if (nfcData.parsedAt != null)
        _buildInfoRow(
          'Thời gian parse',
          _formatDateTime(nfcData.parsedAt!),
          isDarkMode,
        ),
    ], isDarkMode);
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Widget> children,
    bool isDarkMode,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkSecondary
            : AppColors.backgroundSecondary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconS,
              ),
              const SizedBox(width: AppDimensions.spacingS),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDarkMode
                      ? AppColors.neutral100
                      : AppColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingS),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.spacingXS),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: isDarkMode
                    ? AppColors.neutral400
                    : AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isDarkMode
                    ? AppColors.neutral100
                    : AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    if (nfcData.isExpired) {
      return AppColors.error;
    } else if (nfcData.isValid) {
      return AppColors.success;
    } else {
      return AppColors.warning;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// Hiển thị ảnh khuôn mặt
  Widget _buildFaceImageDisplay(
    BuildContext context,
    dynamic faceImage,
    bool isDarkMode,
  ) {
    try {
      if (faceImage.imageData == null || faceImage.imageData!.isEmpty) {
        return _buildImagePlaceholder('Không có dữ liệu ảnh', isDarkMode);
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Ảnh khuôn mặt:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: isDarkMode
                  ? AppColors.neutral400
                  : AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Center(
            child: Container(
              width: 200,
              height: 250,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                child: Image.memory(
                  faceImage.imageData!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildImagePlaceholder(
                      'Lỗi hiển thị ảnh',
                      isDarkMode,
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      );
    } catch (e) {
      return _buildImagePlaceholder('Lỗi: $e', isDarkMode);
    }
  }

  /// Widget placeholder khi không có ảnh hoặc lỗi
  Widget _buildImagePlaceholder(String message, bool isDarkMode) {
    return Container(
      width: 200,
      height: 250,
      decoration: BoxDecoration(
        color: isDarkMode
            ? AppColors.backgroundDarkTertiary
            : AppColors.neutral100,
        border: Border.all(
          color: isDarkMode
              ? AppColors.borderDark.withValues(alpha: 0.3)
              : AppColors.neutral300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.face,
            size: 48,
            color: isDarkMode ? AppColors.neutral500 : AppColors.neutral400,
          ),
          const SizedBox(height: AppDimensions.spacingS),
          Text(
            message,
            style: TextStyle(
              color: isDarkMode ? AppColors.neutral400 : AppColors.neutral500,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
