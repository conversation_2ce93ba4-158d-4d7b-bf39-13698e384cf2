import 'dart:async';
import 'dart:typed_data';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../shared/utils/app_logger.dart';
import '../models/nfc_data_model.dart';
import '../repositories/nfc_data_repository.dart';

// Events
abstract class NfcDataEvent {}

class ParseNfcDataEvent extends NfcDataEvent {
  final Map<String, dynamic> rawDgData;
  ParseNfcDataEvent(this.rawDgData);
}

class ParseNfcDataFromBytesEvent extends NfcDataEvent {
  final Map<String, Uint8List> rawDgBytes;
  ParseNfcDataFromBytesEvent(this.rawDgBytes);
}

class SaveNfcDataEvent extends NfcDataEvent {
  final NfcDataModel nfcData;
  SaveNfcDataEvent(this.nfcData);
}

class LoadSavedNfcDataEvent extends NfcDataEvent {}

class ClearSavedNfcDataEvent extends NfcDataEvent {}

class ValidateNfcDataEvent extends NfcDataEvent {
  final NfcDataModel nfcData;
  ValidateNfcDataEvent(this.nfcData);
}

// States
abstract class NfcDataState {}

class NfcDataInitial extends NfcDataState {}

class NfcDataLoading extends NfcDataState {}

class NfcDataParsed extends NfcDataState {
  final NfcDataModel nfcData;
  final Map<String, dynamic> stats;
  NfcDataParsed(this.nfcData, this.stats);
}

class NfcDataSaved extends NfcDataState {
  final NfcDataModel nfcData;
  NfcDataSaved(this.nfcData);
}

class NfcDataLoaded extends NfcDataState {
  final NfcDataModel nfcData;
  NfcDataLoaded(this.nfcData);
}

class NfcDataValidated extends NfcDataState {
  final NfcDataModel nfcData;
  final bool isValid;
  final String validationStatus;
  NfcDataValidated(this.nfcData, this.isValid, this.validationStatus);
}

class NfcDataError extends NfcDataState {
  final String message;
  NfcDataError(this.message);
}

/// BLoC để quản lý state của NFC data
class NfcDataBloc extends Bloc<NfcDataEvent, NfcDataState> {
  final AppLogger _logger = AppLogger();
  final NfcDataRepository _repository = NfcDataRepository();

  NfcDataBloc() : super(NfcDataInitial()) {
    on<ParseNfcDataEvent>(_onParseNfcData);
    on<ParseNfcDataFromBytesEvent>(_onParseNfcDataFromBytes);
    on<SaveNfcDataEvent>(_onSaveNfcData);
    on<LoadSavedNfcDataEvent>(_onLoadSavedNfcData);
    on<ClearSavedNfcDataEvent>(_onClearSavedNfcData);
    on<ValidateNfcDataEvent>(_onValidateNfcData);
  }

  /// Parse NFC data từ raw DG data
  Future<void> _onParseNfcData(
    ParseNfcDataEvent event,
    Emitter<NfcDataState> emit,
  ) async {
    try {
      emit(NfcDataLoading());
      await _logger.i('Parsing NFC data from raw DG data');

      final nfcData = await _repository.parseFromRawData(event.rawDgData);
      final stats = _repository.getParsingStats(nfcData);

      await _logger.i('Successfully parsed NFC data');
      emit(NfcDataParsed(nfcData, stats));

    } catch (e) {
      await _logger.e('Error parsing NFC data: $e');
      emit(NfcDataError('Lỗi khi parse dữ liệu NFC: $e'));
    }
  }

  /// Parse NFC data từ raw bytes
  Future<void> _onParseNfcDataFromBytes(
    ParseNfcDataFromBytesEvent event,
    Emitter<NfcDataState> emit,
  ) async {
    try {
      emit(NfcDataLoading());
      await _logger.i('Parsing NFC data from raw bytes');

      final nfcData = await _repository.parseFromRawBytes(event.rawDgBytes);
      final stats = _repository.getParsingStats(nfcData);

      await _logger.i('Successfully parsed NFC data from bytes');
      emit(NfcDataParsed(nfcData, stats));

    } catch (e) {
      await _logger.e('Error parsing NFC data from bytes: $e');
      emit(NfcDataError('Lỗi khi parse dữ liệu NFC từ bytes: $e'));
    }
  }

  /// Lưu NFC data
  Future<void> _onSaveNfcData(
    SaveNfcDataEvent event,
    Emitter<NfcDataState> emit,
  ) async {
    try {
      emit(NfcDataLoading());
      await _logger.i('Saving NFC data');

      await _repository.saveNfcData(event.nfcData);

      await _logger.i('Successfully saved NFC data');
      emit(NfcDataSaved(event.nfcData));

    } catch (e) {
      await _logger.e('Error saving NFC data: $e');
      emit(NfcDataError('Lỗi khi lưu dữ liệu NFC: $e'));
    }
  }

  /// Load saved NFC data
  Future<void> _onLoadSavedNfcData(
    LoadSavedNfcDataEvent event,
    Emitter<NfcDataState> emit,
  ) async {
    try {
      emit(NfcDataLoading());
      await _logger.i('Loading saved NFC data');

      final savedData = await _repository.getSavedNfcData();

      if (savedData != null) {
        await _logger.i('Successfully loaded saved NFC data');
        emit(NfcDataLoaded(savedData));
      } else {
        await _logger.w('No saved NFC data found');
        emit(NfcDataError('Không tìm thấy dữ liệu NFC đã lưu'));
      }

    } catch (e) {
      await _logger.e('Error loading saved NFC data: $e');
      emit(NfcDataError('Lỗi khi load dữ liệu NFC đã lưu: $e'));
    }
  }

  /// Clear saved NFC data
  Future<void> _onClearSavedNfcData(
    ClearSavedNfcDataEvent event,
    Emitter<NfcDataState> emit,
  ) async {
    try {
      emit(NfcDataLoading());
      await _logger.i('Clearing saved NFC data');

      await _repository.clearSavedNfcData();

      await _logger.i('Successfully cleared saved NFC data');
      emit(NfcDataInitial());

    } catch (e) {
      await _logger.e('Error clearing saved NFC data: $e');
      emit(NfcDataError('Lỗi khi xóa dữ liệu NFC đã lưu: $e'));
    }
  }

  /// Validate NFC data
  Future<void> _onValidateNfcData(
    ValidateNfcDataEvent event,
    Emitter<NfcDataState> emit,
  ) async {
    try {
      emit(NfcDataLoading());
      await _logger.i('Validating NFC data');

      final validatedData = await _repository.validateAndEnrich(event.nfcData);

      await _logger.i('Successfully validated NFC data');
      emit(NfcDataValidated(
        validatedData,
        validatedData.isValid,
        validatedData.validationStatus,
      ));

    } catch (e) {
      await _logger.e('Error validating NFC data: $e');
      emit(NfcDataError('Lỗi khi validate dữ liệu NFC: $e'));
    }
  }

  @override
  Future<void> close() {
    _repository.dispose();
    _logger.i('NfcDataBloc disposed');
    return super.close();
  }
} 