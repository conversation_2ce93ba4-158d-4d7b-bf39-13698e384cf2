import 'dart:convert';
import 'dart:typed_data';
import '../../../../shared/utils/app_logger.dart';
import '../models/nfc_data_model.dart';
import '../services/nfc_data_parser_service.dart';

/// Repository để quản lý dữ liệu NFC đã parse
class NfcDataRepository {
  final AppLogger _logger = AppLogger();
  final NfcDataParserService _parserService = NfcDataParserService();

  /// Lưu dữ liệu NFC đã parse
  Future<void> saveNfcData(NfcDataModel nfcData) async {
    try {
      await _logger.i('Saving NFC data to local storage');
      
      // TODO: Implement local storage (SharedPreferences, Hive, etc.)
      // Hiện tại chỉ log thông tin
      
      final stats = _parserService.getParsingStats(nfcData);
      await _logger.i('NFC data stats: $stats');
      
    } catch (e) {
      await _logger.e('Error saving NFC data: $e');
      rethrow;
    }
  }

  /// Lấy dữ liệu NFC đã lưu
  Future<NfcDataModel?> getSavedNfcData() async {
    try {
      await _logger.i('Retrieving saved NFC data from local storage');
      
      // TODO: Implement local storage retrieval
      // Hiện tại return null
      
      await _logger.w('No saved NFC data found');
      return null;
      
    } catch (e) {
      await _logger.e('Error retrieving saved NFC data: $e');
      return null;
    }
  }

  /// Validate và enrich dữ liệu
  Future<NfcDataModel> validateAndEnrich(NfcDataModel nfcData) async {
    try {
      await _logger.i('Validating and enriching NFC data');
      
      final enrichedData = await _parserService.validateAndEnrich(nfcData);
      
      await _logger.i('Validation and enrichment completed');
      return enrichedData;
      
    } catch (e) {
      await _logger.e('Error validating and enriching NFC data: $e');
      return nfcData; // Return original data if validation fails
    }
  }

  /// Parse dữ liệu từ raw DG data
  Future<NfcDataModel> parseFromRawData(Map<String, dynamic> rawDgData) async {
    try {
      await _logger.i('Parsing NFC data from raw DG data');
      
      final nfcData = await _parserService.parseAllData(rawDgData);
      
      await _logger.i('Successfully parsed NFC data from raw DG data');
      return nfcData;
      
    } catch (e) {
      await _logger.e('Error parsing NFC data from raw DG data: $e');
      rethrow;
    }
  }

  /// Parse dữ liệu từ raw bytes
  Future<NfcDataModel> parseFromRawBytes(Map<String, Uint8List> rawDgBytes) async {
    try {
      await _logger.i('Parsing NFC data from raw bytes');
      
      final nfcData = await _parserService.parseFromRawData(rawDgBytes);
      
      await _logger.i('Successfully parsed NFC data from raw bytes');
      return nfcData;
      
    } catch (e) {
      await _logger.e('Error parsing NFC data from raw bytes: $e');
      rethrow;
    }
  }

  /// Xóa dữ liệu NFC đã lưu
  Future<void> clearSavedNfcData() async {
    try {
      await _logger.i('Clearing saved NFC data');
      
      // TODO: Implement local storage clearing
      
      await _logger.i('Successfully cleared saved NFC data');
      
    } catch (e) {
      await _logger.e('Error clearing saved NFC data: $e');
      rethrow;
    }
  }

  /// Kiểm tra có dữ liệu NFC đã lưu không
  Future<bool> hasSavedNfcData() async {
    try {
      final savedData = await getSavedNfcData();
      return savedData != null;
    } catch (e) {
      await _logger.e('Error checking for saved NFC data: $e');
      return false;
    }
  }

  /// Export dữ liệu NFC dưới dạng JSON
  Future<String> exportNfcDataAsJson(NfcDataModel nfcData) async {
    try {
      await _logger.i('Exporting NFC data as JSON');
      
      final jsonData = nfcData.toMap();
      final jsonString = jsonEncode(jsonData);
      
      await _logger.i('Successfully exported NFC data as JSON');
      return jsonString;
      
    } catch (e) {
      await _logger.e('Error exporting NFC data as JSON: $e');
      rethrow;
    }
  }

  /// Import dữ liệu NFC từ JSON
  Future<NfcDataModel?> importNfcDataFromJson(String jsonString) async {
    try {
      await _logger.i('Importing NFC data from JSON');
      
      final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
      final nfcData = NfcDataModel.fromMap(jsonData);
      
      await _logger.i('Successfully imported NFC data from JSON');
      return nfcData;
      
    } catch (e) {
      await _logger.e('Error importing NFC data from JSON: $e');
      return null;
    }
  }

  /// Get parsing statistics
  Map<String, dynamic> getParsingStats(NfcDataModel nfcData) {
    return _parserService.getParsingStats(nfcData);
  }

  /// Clean up resources
  void dispose() {
    _parserService.dispose();
    _logger.i('NfcDataRepository disposed');
  }
} 