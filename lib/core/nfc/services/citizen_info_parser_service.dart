import 'dart:convert';
import 'dart:typed_data';
import 'package:dmrtd/dmrtd.dart';
import '../../../../shared/utils/app_logger.dart';
import '../models/citizen_info.dart';

/// Service để parse thông tin công dân từ DG13
class CitizenInfoParserService {
  final AppLogger _logger = AppLogger();

  // Các tag để định vị thông tin trong hex data (từ DG13File.kt)
  static const String _documentNumberTag = "0113";
  static const String _fullNameTag = "020C";
  static const String _dateOfBirthTag = "300F02010313";
  static const String _sexTag = "040C";
  static const String _nationalityTag = "050C";
  static const String _ethnicityTag = "060C";
  static const String _religionTag = "070C";
  static const String _placeOfOriginTag = "080C";
  static const String _placeOfResidenceTag = "090C";
  static const String _personalIdentificationTag = "0A0C";
  static const String _dateOfReleaseTag = "0B13";
  static const String _dateOfExpiryTag = "0C0C";
  static const String _parentsNameTag = "0D30";
  static const String _spouseNameTag = "0E30";
  static const String _oldDocumentNumberTag = "0F13";

  /// Parse thông tin công dân từ DG13 data
  Future<CitizenInfo?> parseFromDg13(dynamic dg13Data) async {
    try {
      if (dg13Data == null) {
        await _logger.w('DG13 data is null');
        return null;
      }

      Uint8List dg13Bytes;
      
      await _logger.d('DG13 data type: ${dg13Data.runtimeType}');
      
      // Convert sang Uint8List nếu cần
      if (dg13Data is Uint8List) {
        dg13Bytes = dg13Data;
        await _logger.d('DG13 is Uint8List: ${dg13Bytes.length} bytes');
      } else if (dg13Data is List<int>) {
        dg13Bytes = Uint8List.fromList(dg13Data);
        await _logger.d('DG13 is List<int>, converted to Uint8List: ${dg13Bytes.length} bytes');
      } else if (dg13Data is EfDG13) {
        dg13Bytes = dg13Data.toBytes();
        await _logger.d('DG13 is EfDG13, extracted bytes: ${dg13Bytes.length} bytes');
      } else {
        await _logger.w('Unsupported DG13 data type: ${dg13Data.runtimeType}');
        return null;
      }

      return await _parseFromBytes(dg13Bytes);

    } catch (e) {
      await _logger.e('Error parsing citizen info from DG13: $e');
      return null;
    }
  }

  /// Parse từ byte data
  Future<CitizenInfo?> _parseFromBytes(Uint8List dg13Bytes) async {
    try {
      if (dg13Bytes.isEmpty) {
        await _logger.w('DG13 bytes is empty');
        return null;
      }

      // Convert bytes sang hex string
      final hexData = _bytesToHex(dg13Bytes);
      await _logger.d('Parsing DG13 hex data: ${hexData.length} characters');

      // Parse từng field
      final documentNumber = _extractTextByTag(hexData, _documentNumberTag, 0);
      final fullName = _extractTextByTag(hexData, _fullNameTag, 0);
      final dateOfBirth = _extractTextByTag(hexData, _dateOfBirthTag, 0);
      final sex = _extractTextByTag(hexData, _sexTag, 0);
      final nationality = _extractTextByTag(hexData, _nationalityTag, 0);
      final ethnicity = _extractTextByTag(hexData, _ethnicityTag, 0);
      final religion = _extractTextByTag(hexData, _religionTag, 0);
      final placeOfOrigin = _extractTextByTag(hexData, _placeOfOriginTag, 0);
      final placeOfResidence = _extractTextByTag(hexData, _placeOfResidenceTag, 0);
      final personalIdentification = _extractTextByTag(hexData, _personalIdentificationTag, 0);
      final dateOfRelease = _extractTextByTag(hexData, _dateOfReleaseTag, 0);
      final dateOfExpiry = _extractTextByTag(hexData, _dateOfExpiryTag, 0);
      final fatherFullName = _extractTextByTag(hexData, _parentsNameTag, 4);
      final motherFullName = _extractMotherInfo(hexData, _parentsNameTag, 4, 6);
      final spouseFullName = _extractTextByTag(hexData, _spouseNameTag, 4);
      final oldDocumentNumber = _extractTextByTag(hexData, _oldDocumentNumberTag, 0);

      final citizenInfo = CitizenInfo(
        documentNumber: documentNumber,
        fullName: fullName,
        dateOfBirth: dateOfBirth,
        sex: sex,
        nationality: nationality,
        ethnicity: ethnicity,
        religion: religion,
        placeOfOrigin: placeOfOrigin,
        placeOfResidence: placeOfResidence,
        personalIdentification: personalIdentification,
        dateOfRelease: dateOfRelease,
        dateOfExpiry: dateOfExpiry,
        fatherFullName: fatherFullName,
        motherFullName: motherFullName,
        spouseFullName: spouseFullName,
        oldDocumentNumber: oldDocumentNumber,
      );

      if (citizenInfo.isValid) {
        await _logger.i('Successfully parsed citizen info: ${citizenInfo.fullName}');
        return citizenInfo;
      } else {
        await _logger.w('Parsed citizen info is not valid: ${citizenInfo.toString()}');
        return null;
      }

    } catch (e) {
      await _logger.e('Error parsing citizen info from bytes: $e');
      return null;
    }
  }

  /// Extract text từ hex data theo tag
  String _extractTextByTag(String hex, String tag, int extension) {
    try {
      final tagIndex = hex.indexOf(tag);
      if (tagIndex == -1) {
        return '';
      }

      final lengthIndex = tagIndex + tag.length + extension;
      if (lengthIndex + 2 > hex.length) {
        return '';
      }

      final objectLengthHex = hex.substring(lengthIndex, lengthIndex + 2);
      final objectLength = int.parse(objectLengthHex, radix: 16);

      if (objectLength <= 1) {
        return '';
      }

      final dataIndex = lengthIndex + 2;
      if (dataIndex + objectLength * 2 > hex.length) {
        return '';
      }

      final objectHex = hex.substring(dataIndex, dataIndex + objectLength * 2);
      return _hexToUtf8(objectHex);

    } catch (e) {
      return '';
    }
  }



  /// Extract thông tin mẹ từ parents name tag
  String _extractMotherInfo(String hex, String tag, int fatherExtension, int motherExtension) {
    try {
      final tagIndex = hex.indexOf(tag);
      if (tagIndex == -1) {
        return '';
      }

      // Đọc thông tin cha trước
      final fatherLengthIndex = tagIndex + tag.length + fatherExtension;
      if (fatherLengthIndex + 2 > hex.length) {
        return '';
      }

      final fatherLengthHex = hex.substring(fatherLengthIndex, fatherLengthIndex + 2);
      final fatherLength = int.parse(fatherLengthHex, radix: 16);

      final totalFatherLength = fatherLength != 1 ? fatherLength * 2 : 0;

      // Tính vị trí thông tin mẹ
      final motherLengthIndex = fatherLengthIndex + 2 + totalFatherLength + motherExtension;
      if (motherLengthIndex + 2 > hex.length) {
        return '';
      }

      final motherLengthHex = hex.substring(motherLengthIndex, motherLengthIndex + 2);
      final motherLength = int.parse(motherLengthHex, radix: 16);

      if (motherLength <= 1) {
        return '';
      }

      final dataIndex = motherLengthIndex + 2;
      if (dataIndex + motherLength * 2 > hex.length) {
        return '';
      }

      final objectHex = hex.substring(dataIndex, dataIndex + motherLength * 2);
      return _hexToUtf8(objectHex);

    } catch (e) {
      return '';
    }
  }

  /// Convert bytes sang hex string
  String _bytesToHex(Uint8List bytes) {
    return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join().toUpperCase();
  }

  /// Convert hex string sang UTF-8 string
  String _hexToUtf8(String hex) {
    try {
      final bytes = <int>[];
      for (int i = 0; i < hex.length; i += 2) {
        if (i + 1 < hex.length) {
          bytes.add(int.parse(hex.substring(i, i + 2), radix: 16));
        }
      }
      return utf8.decode(bytes);
    } catch (e) {
      return '';
    }
  }

  /// Validate DG13 data
  bool validateDg13Data(Uint8List dg13Bytes) {
    try {
      if (dg13Bytes.isEmpty) return false;
      
      // Kiểm tra cấu trúc cơ bản
      final hexData = _bytesToHex(dg13Bytes);
      
      // Kiểm tra có các tag cần thiết không
      final hasDocumentNumber = hexData.contains(_documentNumberTag);
      final hasFullName = hexData.contains(_fullNameTag);
      final hasPersonalId = hexData.contains(_personalIdentificationTag);
      
      return hasDocumentNumber && hasFullName && hasPersonalId;
      
    } catch (e) {
      return false;
    }
  }
} 