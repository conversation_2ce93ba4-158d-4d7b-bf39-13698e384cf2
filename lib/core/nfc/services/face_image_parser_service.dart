import 'dart:typed_data';
import 'package:dmrtd/dmrtd.dart';
import '../../../../shared/utils/app_logger.dart';
import '../models/face_image_info.dart';

/// Service để parse ảnh khuôn mặt từ DG2
class FaceImageParserService {
  final AppLogger _logger = AppLogger();

  /// Parse ảnh khuôn mặt từ DG2 object của DMRTD plugin
  Future<FaceImageInfo?> parseFromDg2(dynamic dg2Data) async {
    try {
      if (dg2Data == null) {
        await _logger.w('DG2 data is null');
        return null;
      }

      await _logger.d('DG2 data type: ${dg2Data.runtimeType}');

      // Sử dụng parser có sẵn từ DMRTD plugin
      if (dg2Data is EfDG2) {
        await _logger.d('DG2 is EfDG2, using built-in parser');
        return await _parseFromEfDg2(dg2Data);
      }

      // Fallback: parse từ raw data
      if (dg2Data is Uint8List) {
        await _logger.d('DG2 is Uint8List, parsing from raw data: ${dg2Data.length} bytes');
        return await _parseFromRawData(dg2Data);
      }

      await _logger.w('Unsupported DG2 data type: ${dg2Data.runtimeType}');
      return null;

    } catch (e) {
      await _logger.e('Error parsing face image from DG2: $e');
      return null;
    }
  }

  /// Parse từ EfDG2 object sử dụng parser có sẵn từ DMRTD plugin
  Future<FaceImageInfo?> _parseFromEfDg2(EfDG2 dg2) async {
    try {
      await _logger.d('Parsing EfDG2 with built-in parser');
      
      // Sử dụng metadata đã được parse sẵn từ EfDG2
      if (dg2.imageData == null) {
        await _logger.w('EfDG2 has no image data');
        return null;
      }

      // Convert metadata từ EfDG2 sang FaceImageInfo
      final faceImageInfo = FaceImageInfo(
        imageData: dg2.imageData!,
        mimeType: dg2.imageType == ImageType.jpeg ? 'image/jpeg' : 'image/jpeg2000',
        width: dg2.imageWidth,
        height: dg2.imageHeight,
        quality: dg2.quality,
        gender: _parseGender(dg2.gender),
        eyeColor: _parseEyeColor(dg2.eyeColor),
        hairColor: _parseHairColor(dg2.hairColor),
        expression: _parseExpression(dg2.expression),
        faceImageType: dg2.faceImageType,
        imageDataType: dg2.imageType == ImageType.jpeg ? 0 : 1,
        colorSpace: dg2.imageColorSpace,
        sourceType: dg2.sourceType,
        deviceType: dg2.deviceType,
      );

      if (faceImageInfo.hasImageData) {
        await _logger.i('Successfully parsed face image from EfDG2: ${faceImageInfo.imageSize}, ${faceImageInfo.fileSizeString}');
        return faceImageInfo;
      } else {
        await _logger.w('Parsed face image from EfDG2 has no data');
        return null;
      }

    } catch (e) {
      await _logger.e('Error parsing from EfDG2: $e');
      return null;
    }
  }

  /// Parse từ raw byte data
  Future<FaceImageInfo?> _parseFromRawData(Uint8List dg2Bytes) async {
    try {
      if (dg2Bytes.length < 20) {
        await _logger.w('DG2 data too short: ${dg2Bytes.length} bytes');
        return null;
      }

      // Parse theo cấu trúc DG2File.java và FaceImageInfo.java
      // CBEFF format với biometric data blocks
      
      final faceImageData = _extractFaceImageData(dg2Bytes);
      if (faceImageData == null) {
        await _logger.w('Could not extract face image data from DG2');
        return null;
      }

      // Parse metadata từ face image data
      final metadata = _parseFaceImageMetadata(faceImageData);
      
      final faceImageInfo = FaceImageInfo(
        imageData: faceImageData,
        mimeType: metadata['mimeType'] ?? 'image/jpeg',
        width: metadata['width'] ?? 0,
        height: metadata['height'] ?? 0,
        quality: metadata['quality'] ?? 0,
        gender: metadata['gender'] ?? '',
        eyeColor: metadata['eyeColor'] ?? '',
        hairColor: metadata['hairColor'] ?? '',
        expression: metadata['expression'] ?? '',
        faceImageType: metadata['faceImageType'] ?? 0,
        imageDataType: metadata['imageDataType'] ?? 0,
        colorSpace: metadata['colorSpace'] ?? 0,
        sourceType: metadata['sourceType'] ?? 0,
        deviceType: metadata['deviceType'] ?? 0,
      );

      if (faceImageInfo.hasImageData) {
        await _logger.i('Successfully parsed face image: ${faceImageInfo.imageSize}, ${faceImageInfo.fileSizeString}');
        return faceImageInfo;
      } else {
        await _logger.w('Parsed face image has no data');
        return null;
      }

    } catch (e) {
      await _logger.e('Error parsing face image from raw data: $e');
      return null;
    }
  }

  /// Extract face image data từ DG2 bytes
  Uint8List? _extractFaceImageData(Uint8List dg2Bytes) {
    try {
      // Tìm CBEFF structure và extract image data
      // Dựa trên DG2File.java và FaceImageInfo.java
      
      // Tìm Standard Biometric Header
      final sbhEnd = _findSbhEnd(dg2Bytes);
      if (sbhEnd == -1) {
        return null;
      }

      // Tìm Face Record Header
      final faceRecordStart = _findFaceRecordStart(dg2Bytes, sbhEnd);
      if (faceRecordStart == -1) {
        return null;
      }

      // Extract image data từ face record
      final imageData = _extractImageFromFaceRecord(dg2Bytes, faceRecordStart);
      return imageData;

    } catch (e) {
      return null;
    }
  }

  /// Parse metadata từ face image data
  Map<String, dynamic> _parseFaceImageMetadata(Uint8List faceImageData) {
    try {
      // Parse metadata theo FaceImageInfo.java structure
      // Facial Information Block (20 bytes)
      
      if (faceImageData.length < 20) {
        return {};
      }

      final metadata = <String, dynamic>{};
      
      // Parse basic metadata
      metadata['gender'] = _parseGender(faceImageData[6]);
      metadata['eyeColor'] = _parseEyeColor(faceImageData[7]);
      metadata['hairColor'] = _parseHairColor(faceImageData[8]);
      metadata['expression'] = _parseExpression(faceImageData[13]);
      
      // Parse image information (12 bytes từ cuối)
      if (faceImageData.length >= 32) {
        final imageInfoStart = faceImageData.length - 12;
        metadata['faceImageType'] = faceImageData[imageInfoStart];
        metadata['imageDataType'] = faceImageData[imageInfoStart + 1];
        metadata['width'] = (faceImageData[imageInfoStart + 2] << 8) | faceImageData[imageInfoStart + 3];
        metadata['height'] = (faceImageData[imageInfoStart + 4] << 8) | faceImageData[imageInfoStart + 5];
        metadata['colorSpace'] = faceImageData[imageInfoStart + 6];
        metadata['sourceType'] = faceImageData[imageInfoStart + 7];
        metadata['deviceType'] = (faceImageData[imageInfoStart + 8] << 8) | faceImageData[imageInfoStart + 9];
        metadata['quality'] = (faceImageData[imageInfoStart + 10] << 8) | faceImageData[imageInfoStart + 11];
        
        // Set mime type based on image data type
        metadata['mimeType'] = metadata['imageDataType'] == 0 ? 'image/jpeg' : 'image/jpeg2000';
      }

      return metadata;

    } catch (e) {
      return {};
    }
  }

  /// Tìm kết thúc của Standard Biometric Header
  int _findSbhEnd(Uint8List dg2Bytes) {
    try {
      // Tìm tag 0x7F2E (BIOMETRIC_DATA_BLOCK_CONSTRUCTED_TAG)
      for (int i = 0; i < dg2Bytes.length - 2; i++) {
        if (dg2Bytes[i] == 0x7F && dg2Bytes[i + 1] == 0x2E) {
          return i;
        }
      }
      return -1;
    } catch (e) {
      return -1;
    }
  }

  /// Tìm bắt đầu của Face Record
  int _findFaceRecordStart(Uint8List dg2Bytes, int sbhEnd) {
    try {
      // Tìm tag 0x46414300 (FORMAT_IDENTIFIER - "FAC\0")
      for (int i = sbhEnd; i < dg2Bytes.length - 4; i++) {
        if (dg2Bytes[i] == 0x46 && 
            dg2Bytes[i + 1] == 0x41 && 
            dg2Bytes[i + 2] == 0x43 && 
            dg2Bytes[i + 3] == 0x00) {
          return i;
        }
      }
      return -1;
    } catch (e) {
      return -1;
    }
  }

  /// Extract image data từ face record
  Uint8List? _extractImageFromFaceRecord(Uint8List dg2Bytes, int faceRecordStart) {
    try {
      // Skip face record header (14 bytes)
      final headerLength = 14;
      final dataStart = faceRecordStart + headerLength;
      
      if (dataStart >= dg2Bytes.length) {
        return null;
      }

      // Extract image data (phần còn lại)
      return dg2Bytes.sublist(dataStart);

    } catch (e) {
      return null;
    }
  }

  /// Parse gender code
  String _parseGender(int genderCode) {
    switch (genderCode) {
      case 1:
        return 'M';
      case 2:
        return 'F';
      case 9:
        return 'X';
      default:
        return '';
    }
  }

  /// Parse eye color code
  String _parseEyeColor(int eyeColorCode) {
    switch (eyeColorCode) {
      case 1:
        return 'BLK';
      case 2:
        return 'BLU';
      case 3:
        return 'BRO';
      case 4:
        return 'GRY';
      case 5:
        return 'GRN';
      case 6:
        return 'MUL';
      case 7:
        return 'PNK';
      case 8:
        return 'XXX';
      default:
        return '';
    }
  }

  /// Parse hair color code
  String _parseHairColor(int hairColorCode) {
    switch (hairColorCode) {
      case 1:
        return 'BAL';
      case 2:
        return 'BLK';
      case 3:
        return 'BLN';
      case 4:
        return 'BRO';
      case 5:
        return 'GRY';
      case 6:
        return 'WHI';
      case 7:
        return 'RED';
      case 8:
        return 'SDY';
      case 9:
        return 'XXX';
      default:
        return '';
    }
  }

  /// Parse expression code
  String _parseExpression(int expressionCode) {
    switch (expressionCode) {
      case 1:
        return 'SER';
      case 2:
        return 'SMI';
      case 8:
        return 'XXX';
      default:
        return '';
    }
  }

  /// Convert image format nếu cần
  Future<Uint8List?> convertImageFormat(Uint8List originalData, String targetFormat) async {
    try {
      // TODO: Implement image format conversion
      // Convert JPEG2000 sang JPEG nếu cần
      await _logger.w('Image format conversion not implemented yet');
      return originalData;
    } catch (e) {
      await _logger.e('Error converting image format: $e');
      return null;
    }
  }

  /// Validate DG2 data
  bool validateDg2Data(Uint8List dg2Bytes) {
    try {
      if (dg2Bytes.isEmpty) return false;
      
      // Kiểm tra cấu trúc cơ bản
      final hasSbh = _findSbhEnd(dg2Bytes) != -1;
      final hasFaceRecord = _findFaceRecordStart(dg2Bytes, 0) != -1;
      
      return hasSbh && hasFaceRecord;
      
    } catch (e) {
      return false;
    }
  }
} 