import 'dart:typed_data';
import 'package:dmrtd/dmrtd.dart';
import '../../../../shared/utils/app_logger.dart';
import '../models/mrz_info.dart';

/// Service để parse MRZ (Machine Readable Zone) data từ DG1
class MrzParserService {
  final AppLogger _logger = AppLogger();

  /// Parse MRZ data từ DG1 object của DMRTD plugin
  Future<MrzInfo?> parseFromDg1(dynamic dg1Data) async {
    try {
      if (dg1Data == null) {
        await _logger.w('DG1 data is null');
        return null;
      }

      await _logger.d('DG1 data type: ${dg1Data.runtimeType}');

      // Kiểm tra xem có phải là EfDG1 không
      if (dg1Data is EfDG1) {
        await _logger.d('DG1 is EfDG1, extracting MRZ');
        final mrz = dg1Data.mrz;
        await _logger.d('MRZ extracted: ${mrz.toString()}');
        final mrzInfo = MrzInfo.fromMrz(mrz);
        
        await _logger.i('Successfully parsed MRZ from DG1: ${mrzInfo.fullName}');
        return mrzInfo;
      }

      // Fallback: parse từ raw data
      if (dg1Data is Uint8List) {
        await _logger.d('DG1 is Uint8List, parsing from raw data: ${dg1Data.length} bytes');
        return await _parseFromRawData(dg1Data);
      }

      await _logger.w('Unsupported DG1 data type: ${dg1Data.runtimeType}');
      return null;

    } catch (e) {
      await _logger.e('Error parsing MRZ from DG1: $e');
      return null;
    }
  }

  /// Parse MRZ từ raw byte data
  Future<MrzInfo?> _parseFromRawData(Uint8List dg1Bytes) async {
    try {
      // Parse theo cấu trúc DG1File.java
      // TLV format: tag 0x5F1F -> MRZ string
      
      if (dg1Bytes.length < 4) {
        await _logger.w('DG1 data too short: ${dg1Bytes.length} bytes');
        return null;
      }

      // Tìm tag 0x5F1F (MRZ_INFO_TAG)
      final mrzData = _extractMrzData(dg1Bytes);
      if (mrzData == null) {
        await _logger.w('Could not extract MRZ data from DG1');
        return null;
      }

      // Parse MRZ string
      final mrzString = String.fromCharCodes(mrzData);
      final mrzInfo = MrzInfo.fromString(mrzString);
      
      await _logger.i('Successfully parsed MRZ from raw data: ${mrzInfo.fullName}');
      return mrzInfo;

    } catch (e) {
      await _logger.e('Error parsing MRZ from raw data: $e');
      return null;
    }
  }

  /// Extract MRZ data từ DG1 bytes
  Uint8List? _extractMrzData(Uint8List dg1Bytes) {
    try {
      // Tìm tag 0x5F1F trong TLV structure
      for (int i = 0; i < dg1Bytes.length - 2; i++) {
        if (dg1Bytes[i] == 0x5F && dg1Bytes[i + 1] == 0x1F) {
          // Tìm thấy MRZ_INFO_TAG
          final length = dg1Bytes[i + 2];
          if (i + 3 + length <= dg1Bytes.length) {
            return dg1Bytes.sublist(i + 3, i + 3 + length);
          }
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Parse MRZ string trực tiếp
  Future<MrzInfo?> parseFromString(String mrzString) async {
    try {
      if (mrzString.isEmpty) {
        await _logger.w('MRZ string is empty');
        return null;
      }

      final mrzInfo = MrzInfo.fromString(mrzString);
      
      await _logger.i('Successfully parsed MRZ from string: ${mrzInfo.fullName}');
      return mrzInfo;

    } catch (e) {
      await _logger.e('Error parsing MRZ from string: $e');
      return null;
    }
  }

  /// Validate MRZ checksum
  bool validateMrzChecksum(String mrzString) {
    try {
      // Implement MRZ checksum validation theo thuật toán chuẩn
      // Dựa trên MRZInfo.java từ JMRTD
      
      final lines = mrzString.trim().split('\n');
      if (lines.length < 2) return false;

      // Validate document number checksum
      // Validate date of birth checksum  
      // Validate date of expiry checksum
      // Validate composite checksum
      
      // TODO: Implement full checksum validation
      return true;
      
    } catch (e) {
      return false;
    }
  }

  /// Format MRZ string cho display
  String formatMrzString(String mrzString) {
    try {
      final lines = mrzString.trim().split('\n');
      if (lines.length >= 2) {
        return lines.take(2).join('\n');
      }
      return mrzString;
    } catch (e) {
      return mrzString;
    }
  }
} 