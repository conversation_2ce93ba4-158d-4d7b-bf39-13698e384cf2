import 'dart:typed_data';
import '../../../../shared/utils/app_logger.dart';
import '../models/nfc_data_model.dart';
import '../models/mrz_info.dart';
import '../models/citizen_info.dart';
import '../models/face_image_info.dart';
import 'mrz_parser_service.dart';
import 'citizen_info_parser_service.dart';
import 'face_image_parser_service.dart';

/// Service chính để parse tất cả dữ liệu từ các DG đã đọc được từ NFC
class NfcDataParserService {
  final AppLogger _logger = AppLogger();
  final MrzParserService _mrzParser = MrzParserService();
  final CitizenInfoParserService _citizenParser = CitizenInfoParserService();
  final FaceImageParserService _faceImageParser = FaceImageParserService();

  /// Parse tất cả dữ liệu từ các DG đã đọc được
  Future<NfcDataModel> parseAllData(Map<String, dynamic> rawDgData) async {
    try {
      await _logger.i('Starting to parse NFC data from ${rawDgData.length} data groups');
      await _logger.d('Available keys: ${rawDgData.keys.join(', ')}');

      // Parse từng DG
      final mrzInfo = await _parseMrzData(rawDgData['dg1']);
      final faceImage = await _parseFaceImage(rawDgData['dg2']);
      final citizenInfo = await _parseCitizenInfo(rawDgData['dg13']);

      // Tạo NfcDataModel tổng hợp
      final nfcData = NfcDataModel(
        mrzInfo: mrzInfo,
        faceImage: faceImage,
        citizenInfo: citizenInfo,
        rawData: rawDgData,
        parsedAt: DateTime.now(),
      );

      await _logger.i('Successfully parsed NFC data: ${nfcData.toString()}');
      return nfcData;

    } catch (e) {
      await _logger.e('Error parsing NFC data: $e');
      rethrow;
    }
  }

  /// Parse thông tin MRZ từ DG1
  Future<MrzInfo?> _parseMrzData(dynamic dg1Data) async {
    try {
      if (dg1Data == null) {
        await _logger.w('DG1 data is null, skipping MRZ parsing');
        return null;
      }

      await _logger.d('Parsing MRZ data from DG1');
      final mrzInfo = await _mrzParser.parseFromDg1(dg1Data);
      
      if (mrzInfo != null) {
        await _logger.i('Successfully parsed MRZ: ${mrzInfo.fullName}');
      } else {
        await _logger.w('Failed to parse MRZ from DG1');
      }
      
      return mrzInfo;

    } catch (e) {
      await _logger.e('Error parsing MRZ data: $e');
      return null;
    }
  }

  /// Parse ảnh khuôn mặt từ DG2
  Future<FaceImageInfo?> _parseFaceImage(dynamic dg2Data) async {
    try {
      if (dg2Data == null) {
        await _logger.w('DG2 data is null, skipping face image parsing');
        return null;
      }

      await _logger.d('Parsing face image data from DG2');
      final faceImage = await _faceImageParser.parseFromDg2(dg2Data);
      
      if (faceImage != null) {
        await _logger.i('Successfully parsed face image: ${faceImage.imageSize}, ${faceImage.fileSizeString}');
      } else {
        await _logger.w('Failed to parse face image from DG2');
      }
      
      return faceImage;

    } catch (e) {
      await _logger.e('Error parsing face image data: $e');
      return null;
    }
  }

  /// Parse thông tin công dân từ DG13
  Future<CitizenInfo?> _parseCitizenInfo(dynamic dg13Data) async {
    try {
      if (dg13Data == null) {
        await _logger.w('DG13 data is null, skipping citizen info parsing');
        return null;
      }

      await _logger.d('Parsing citizen info data from DG13');
      final citizenInfo = await _citizenParser.parseFromDg13(dg13Data);
      
      if (citizenInfo != null) {
        await _logger.i('Successfully parsed citizen info: ${citizenInfo.fullName}');
      } else {
        await _logger.w('Failed to parse citizen info from DG13');
      }
      
      return citizenInfo;

    } catch (e) {
      await _logger.e('Error parsing citizen info data: $e');
      return null;
    }
  }

  /// Parse dữ liệu từ raw DG data (fallback method)
  Future<NfcDataModel> parseFromRawData(Map<String, Uint8List> rawDgBytes) async {
    try {
      await _logger.i('Starting to parse NFC data from raw bytes');

      // Parse từng DG từ raw bytes
      final mrzInfo = await _parseMrzFromRawBytes(rawDgBytes['dg1']);
      final faceImage = await _parseFaceImageFromRawBytes(rawDgBytes['dg2']);
      final citizenInfo = await _parseCitizenInfoFromRawBytes(rawDgBytes['dg13']);

      // Tạo NfcDataModel tổng hợp
      final nfcData = NfcDataModel(
        mrzInfo: mrzInfo,
        faceImage: faceImage,
        citizenInfo: citizenInfo,
        rawData: rawDgBytes.map((key, value) => MapEntry(key, value.toList())),
        parsedAt: DateTime.now(),
      );

      await _logger.i('Successfully parsed NFC data from raw bytes: ${nfcData.toString()}');
      return nfcData;

    } catch (e) {
      await _logger.e('Error parsing NFC data from raw bytes: $e');
      rethrow;
    }
  }

  /// Parse MRZ từ raw bytes
  Future<MrzInfo?> _parseMrzFromRawBytes(Uint8List? dg1Bytes) async {
    try {
      if (dg1Bytes == null) {
        await _logger.w('DG1 raw bytes is null');
        return null;
      }

      await _logger.d('Parsing MRZ from raw DG1 bytes: ${dg1Bytes.length} bytes');
      return await _mrzParser.parseFromDg1(dg1Bytes);

    } catch (e) {
      await _logger.e('Error parsing MRZ from raw bytes: $e');
      return null;
    }
  }

  /// Parse face image từ raw bytes
  Future<FaceImageInfo?> _parseFaceImageFromRawBytes(Uint8List? dg2Bytes) async {
    try {
      if (dg2Bytes == null) {
        await _logger.w('DG2 raw bytes is null');
        return null;
      }

      await _logger.d('Parsing face image from raw DG2 bytes: ${dg2Bytes.length} bytes');
      return await _faceImageParser.parseFromDg2(dg2Bytes);

    } catch (e) {
      await _logger.e('Error parsing face image from raw bytes: $e');
      return null;
    }
  }

  /// Parse citizen info từ raw bytes
  Future<CitizenInfo?> _parseCitizenInfoFromRawBytes(Uint8List? dg13Bytes) async {
    try {
      if (dg13Bytes == null) {
        await _logger.w('DG13 raw bytes is null');
        return null;
      }

      await _logger.d('Parsing citizen info from raw DG13 bytes: ${dg13Bytes.length} bytes');
      return await _citizenParser.parseFromDg13(dg13Bytes);

    } catch (e) {
      await _logger.e('Error parsing citizen info from raw bytes: $e');
      return null;
    }
  }

  /// Validate và enrich dữ liệu đã parse
  Future<NfcDataModel> validateAndEnrich(NfcDataModel nfcData) async {
    try {
      await _logger.i('Validating and enriching NFC data');

      // Enrich thông tin nếu thiếu
      final enrichedData = await _enrichMissingData(nfcData);

      await _logger.i('Validation and enrichment completed');
      return enrichedData;

    } catch (e) {
      await _logger.e('Error validating and enriching NFC data: $e');
      return nfcData; // Return original data if enrichment fails
    }
  }

  /// Enrich thông tin thiếu
  Future<NfcDataModel> _enrichMissingData(NfcDataModel nfcData) async {
    try {
      // TODO: Implement data enrichment logic
      // Ví dụ: fill missing fields từ các nguồn khác nhau
      
      await _logger.d('Data enrichment not implemented yet');
      return nfcData;

    } catch (e) {
      await _logger.e('Error enriching missing data: $e');
      return nfcData;
    }
  }

  /// Get parsing statistics
  Map<String, dynamic> getParsingStats(NfcDataModel nfcData) {
    return {
      'hasMrzInfo': nfcData.mrzInfo != null,
      'hasFaceImage': nfcData.faceImage != null,
      'hasCitizenInfo': nfcData.citizenInfo != null,
      'isValid': nfcData.isValid,
      'isExpired': nfcData.isExpired,
      'validationStatus': nfcData.validationStatus,
      'parsedAt': nfcData.parsedAt?.toIso8601String(),
    };
  }

  /// Clean up resources
  void dispose() {
    // Clean up any resources if needed
    _logger.i('NfcDataParserService disposed');
  }
} 