import 'article_model.dart';

/// Model cho tham số tìm kiếm bài viết
class ArticleSearchParams {
  final String? search;
  final ArticleType? articleType;
  final String? status;
  final bool? isFeatured;
  final int? limit;
  final int? offset;
  final List<String>? tags;

  const ArticleSearchParams({
    this.search,
    this.articleType,
    this.status = 'PUBLISHED',
    this.isFeatured,
    this.limit = 20,
    this.offset = 0,
    this.tags,
  });

  /// Tạo params cho tìm kiếm cơ bản
  factory ArticleSearchParams.basic({
    String? search,
    ArticleType? articleType,
    int? limit,
    int? offset,
  }) {
    return ArticleSearchParams(
      search: search,
      articleType: articleType,
      limit: limit,
      offset: offset,
    );
  }

  /// Tạo params cho bài viết nổi bật
  factory ArticleSearchParams.featured({
    int? limit,
  }) {
    return ArticleSearchParams(
      isFeatured: true,
      limit: limit ?? 5,
    );
  }

  /// Tạo params cho tìm kiếm theo tags
  factory ArticleSearchParams.byTags({
    required List<String> tags,
    int? limit,
    int? offset,
  }) {
    return ArticleSearchParams(
      tags: tags,
      limit: limit,
      offset: offset,
    );
  }

  /// Chuyển đổi thành Map cho API request
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> params = {};

    if (search != null && search!.isNotEmpty) {
      params['p_search'] = search;
    }

    if (articleType != null) {
      params['p_article_type'] = articleType!.name;
    }

    if (status != null) {
      params['p_status'] = status;
    }

    if (isFeatured != null) {
      params['p_is_featured'] = isFeatured;
    }

    if (limit != null) {
      params['p_limit'] = limit;
    }

    if (offset != null) {
      params['p_offset'] = offset;
    }

    if (tags != null && tags!.isNotEmpty) {
      params['p_tags'] = tags;
    }

    return params;
  }

  /// Copy với các thay đổi
  ArticleSearchParams copyWith({
    String? search,
    ArticleType? articleType,
    String? status,
    bool? isFeatured,
    int? limit,
    int? offset,
    List<String>? tags,
  }) {
    return ArticleSearchParams(
      search: search ?? this.search,
      articleType: articleType ?? this.articleType,
      status: status ?? this.status,
      isFeatured: isFeatured ?? this.isFeatured,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
      tags: tags ?? this.tags,
    );
  }

  @override
  String toString() {
    return 'ArticleSearchParams(search: $search, articleType: $articleType, status: $status, isFeatured: $isFeatured, limit: $limit, offset: $offset, tags: $tags)';
  }
} 