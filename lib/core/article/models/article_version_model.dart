import 'package:json_annotation/json_annotation.dart';
import 'article_model.dart';

part 'article_version_model.g.dart';

/// Model cho phiên bản b<PERSON><PERSON> viết
@JsonSerializable(fieldRename: FieldRename.snake)
class ArticleVersionModel {
  final int version;
  final DateTime changedAt;
  final String? changedBy;
  final String? changeReason;
  final String title;
  final String? summary;
  final ArticleType type;
  final String status;

  const ArticleVersionModel({
    required this.version,
    required this.changedAt,
    this.changedBy,
    this.changeReason,
    required this.title,
    this.summary,
    required this.type,
    required this.status,
  });

  factory ArticleVersionModel.fromJson(Map<String, dynamic> json) =>
      _$ArticleVersionModelFromJson(json);

  Map<String, dynamic> toJson() => _$ArticleVersionModelToJson(this);

  @override
  String toString() {
    return 'ArticleVersionModel(version: $version, title: $title, type: $type, status: $status)';
  }
} 