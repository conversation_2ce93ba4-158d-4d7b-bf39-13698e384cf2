// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'article_version_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ArticleVersionModel _$ArticleVersionModelFromJson(Map<String, dynamic> json) =>
    ArticleVersionModel(
      version: (json['version'] as num).toInt(),
      changedAt: DateTime.parse(json['changed_at'] as String),
      changedBy: json['changed_by'] as String?,
      changeReason: json['change_reason'] as String?,
      title: json['title'] as String,
      summary: json['summary'] as String?,
      type: $enumDecode(_$ArticleTypeEnumMap, json['type']),
      status: json['status'] as String,
    );

Map<String, dynamic> _$ArticleVersionModelToJson(
  ArticleVersionModel instance,
) => <String, dynamic>{
  'version': instance.version,
  'changed_at': instance.changedAt.toIso8601String(),
  'changed_by': instance.changedBy,
  'change_reason': instance.changeReason,
  'title': instance.title,
  'summary': instance.summary,
  'type': _$ArticleTypeEnumMap[instance.type]!,
  'status': instance.status,
};

const _$ArticleTypeEnumMap = {
  ArticleType.productIntro: 'PRODUCT_INTRO',
  ArticleType.policy: 'POLICY',
  ArticleType.promotion: 'PROMOTION',
  ArticleType.competition: 'COMPETITION',
  ArticleType.news: 'NEWS',
  ArticleType.guideline: 'GUIDELINE',
  ArticleType.announcement: 'ANNOUNCEMENT',
  ArticleType.training: 'TRAINING',
  ArticleType.termsOfService: 'TERMS_OF_SERVICE',
  ArticleType.privacyPolicy: 'PRIVACY_POLICY',
};
