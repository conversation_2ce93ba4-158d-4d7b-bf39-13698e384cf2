import 'package:json_annotation/json_annotation.dart';

part 'article_model.g.dart';

/// Enum cho loại bài viết
enum ArticleType {
  @JsonValue('PRODUCT_INTRO')
  productIntro,      // Giớ<PERSON> thiệu sản phẩm
  @JsonValue('POLICY')
  policy,            // <PERSON><PERSON><PERSON> sách
  @JsonValue('PROMOTION')
  promotion,         // Chương trình khuyến mại
  @JsonValue('COMPETITION')
  competition,       // <PERSON>hi đua
  @JsonValue('NEWS')
  news,              // Tin tức
  @JsonValue('GUIDELINE')
  guideline,         // Hướng dẫn
  @JsonValue('ANNOUNCEMENT')
  announcement,      // Thông báo
  @JsonValue('TRAINING')
  training,          // Đ<PERSON>o tạo
  @JsonValue('TERMS_OF_SERVICE')
  termsOfService,    // Điều khoản sử dụng
  @JsonValue('PRIVACY_POLICY')
  privacyPolicy,     // <PERSON><PERSON><PERSON> sách bảo mật
}

/// Model cho bài viết
@JsonSerializable(fieldRename: FieldRename.snake)
class ArticleModel {
  final String id;
  final String code;
  final String title;
  final String? summary;
  final String? content;
  final ArticleType type;
  final String status;
  final DateTime? publishedAt;
  final DateTime? expiryAt;
  final int priority;
  final int viewCount;
  final bool isFeatured;
  final String? thumbnailUrl;
  final List<String>? tags;
  final String? targetAudience;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? createdBy;
  final String? updatedBy;

  const ArticleModel({
    required this.id,
    required this.code,
    required this.title,
    this.summary,
    this.content,
    required this.type,
    required this.status,
    this.publishedAt,
    this.expiryAt,
    required this.priority,
    required this.viewCount,
    required this.isFeatured,
    this.thumbnailUrl,
    this.tags,
    this.targetAudience,
    required this.createdAt,
    required this.updatedAt,
    this.createdBy,
    this.updatedBy,
  });

  factory ArticleModel.fromJson(Map<String, dynamic> json) =>
      _$ArticleModelFromJson(json);

  Map<String, dynamic> toJson() => _$ArticleModelToJson(this);

  /// Kiểm tra xem bài viết có đang được publish không
  bool get isPublished => status == 'PUBLISHED';

  /// Kiểm tra xem bài viết có hết hạn không
  bool get isExpired {
    if (expiryAt == null) return false;
    return DateTime.now().isAfter(expiryAt!);
  }

  /// Kiểm tra xem bài viết có thể hiển thị không
  bool get isDisplayable {
    if (!isPublished) return false;
    if (isExpired) return false;
    if (publishedAt != null && DateTime.now().isBefore(publishedAt!)) {
      return false;
    }
    return true;
  }

  /// Lấy thời gian publish để hiển thị
  DateTime get displayDate => publishedAt ?? createdAt;

  /// Copy với các thay đổi
  ArticleModel copyWith({
    String? id,
    String? code,
    String? title,
    String? summary,
    String? content,
    ArticleType? type,
    String? status,
    DateTime? publishedAt,
    DateTime? expiryAt,
    int? priority,
    int? viewCount,
    bool? isFeatured,
    String? thumbnailUrl,
    List<String>? tags,
    String? targetAudience,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
  }) {
    return ArticleModel(
      id: id ?? this.id,
      code: code ?? this.code,
      title: title ?? this.title,
      summary: summary ?? this.summary,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      publishedAt: publishedAt ?? this.publishedAt,
      expiryAt: expiryAt ?? this.expiryAt,
      priority: priority ?? this.priority,
      viewCount: viewCount ?? this.viewCount,
      isFeatured: isFeatured ?? this.isFeatured,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      tags: tags ?? this.tags,
      targetAudience: targetAudience ?? this.targetAudience,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ArticleModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ArticleModel(id: $id, code: $code, title: $title, type: $type, status: $status)';
  }
} 