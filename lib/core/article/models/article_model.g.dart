// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'article_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ArticleModel _$ArticleModelFromJson(Map<String, dynamic> json) => ArticleModel(
  id: json['id'] as String,
  code: json['code'] as String,
  title: json['title'] as String,
  summary: json['summary'] as String?,
  content: json['content'] as String?,
  type: $enumDecode(_$ArticleTypeEnumMap, json['type']),
  status: json['status'] as String,
  publishedAt: json['published_at'] == null
      ? null
      : DateTime.parse(json['published_at'] as String),
  expiryAt: json['expiry_at'] == null
      ? null
      : DateTime.parse(json['expiry_at'] as String),
  priority: (json['priority'] as num).toInt(),
  viewCount: (json['view_count'] as num).toInt(),
  isFeatured: json['is_featured'] as bool,
  thumbnailUrl: json['thumbnail_url'] as String?,
  tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
  targetAudience: json['target_audience'] as String?,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
  createdBy: json['created_by'] as String?,
  updatedBy: json['updated_by'] as String?,
);

Map<String, dynamic> _$ArticleModelToJson(ArticleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'code': instance.code,
      'title': instance.title,
      'summary': instance.summary,
      'content': instance.content,
      'type': _$ArticleTypeEnumMap[instance.type]!,
      'status': instance.status,
      'published_at': instance.publishedAt?.toIso8601String(),
      'expiry_at': instance.expiryAt?.toIso8601String(),
      'priority': instance.priority,
      'view_count': instance.viewCount,
      'is_featured': instance.isFeatured,
      'thumbnail_url': instance.thumbnailUrl,
      'tags': instance.tags,
      'target_audience': instance.targetAudience,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };

const _$ArticleTypeEnumMap = {
  ArticleType.productIntro: 'PRODUCT_INTRO',
  ArticleType.policy: 'POLICY',
  ArticleType.promotion: 'PROMOTION',
  ArticleType.competition: 'COMPETITION',
  ArticleType.news: 'NEWS',
  ArticleType.guideline: 'GUIDELINE',
  ArticleType.announcement: 'ANNOUNCEMENT',
  ArticleType.training: 'TRAINING',
  ArticleType.termsOfService: 'TERMS_OF_SERVICE',
  ArticleType.privacyPolicy: 'PRIVACY_POLICY',
};
