import '../models/article_model.dart';
import '../models/article_version_model.dart';
import '../models/article_search_params.dart';

/// Interface cho ArticleService để support dependency injection và testing
abstract class IArticleService {
  /// Lấy danh sách bài viết
  Future<List<ArticleModel>> getArticles({
    String? search,
    ArticleType? articleType,
    String status = 'PUBLISHED',
    bool? isFeatured,
    int limit = 20,
    int offset = 0,
  });

  /// Lấy bài viết theo ID
  Future<ArticleModel> getArticleById(String articleId);

  /// Lấy bài viết theo mã
  Future<ArticleModel> getArticleByCode(String articleCode);

  /// Lấy danh sách bài viết nổi bật
  Future<List<ArticleModel>> getFeaturedArticles({int limit = 10});

  /// Lấy lịch sử phiên bản của bài viết
  Future<List<ArticleVersionModel>> getArticleVersions(String articleId);

  /// Tìm kiếm bài viết theo tags
  Future<List<ArticleModel>> searchArticlesByTags({
    required List<String> tags,
    int limit = 20,
    int offset = 0,
  });

  /// Tìm kiếm bài viết với tham số tùy chỉnh
  Future<List<ArticleModel>> searchArticles(ArticleSearchParams params);

  /// Kiểm tra tính khả dụng của API bài viết
  Future<bool> checkArticleApiAvailability();

  /// Lấy danh sách các loại bài viết có sẵn
  List<ArticleType> getAvailableArticleTypes();

  /// Lấy tên hiển thị cho loại bài viết
  String getArticleTypeDisplayName(ArticleType type);
} 