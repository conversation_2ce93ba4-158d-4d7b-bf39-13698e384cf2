/// Custom exception cho Article service
class ArticleException implements Exception {
  final String message;
  final ArticleExceptionType type;
  final String? code;
  final Object? originalException;

  const ArticleException({
    required this.message,
    required this.type,
    this.code,
    this.originalException,
  });

  @override
  String toString() => 'ArticleException: $message (Type: $type${code != null ? ', Code: $code' : ''})';
}

/// Loại lỗi Article
enum ArticleExceptionType {
  notFound,
  apiError,
  invalidResponse,
  validationError,
  unauthorized,
  networkError,
  unknown,
} 