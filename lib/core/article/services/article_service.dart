import 'package:flutter/material.dart';
import 'package:kiloba_biz/shared/services/api/api_service_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import 'package:kiloba_biz/shared/services/api/api_service.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import '../models/article_model.dart';
import '../models/article_version_model.dart';
import '../models/article_search_params.dart';
import 'article_exception.dart';
import 'article_service_interface.dart';

/// Service để xử lý các thao tác với bài viết
class ArticleService implements IArticleService {
  // Singleton instance
  static ArticleService? _instance;
  
  // Dependencies - sử dụng getter để đảm bảo lấy singleton đã được khởi tạo
  IApiService get _apiService => getIt.get<IApiService>();
  IAppLogger get _logger => getIt.get<IAppLogger>();
  
  // Factory constructor - đơn giản hóa, không cần dependency injection
  factory ArticleService() {
    _instance ??= ArticleService._internal();
    return _instance!;
  }
  
  // Private constructor - không cần parameters
  ArticleService._internal();

  /// Reset singleton instance (for testing purposes)
  static void resetInstance() {
    _instance = null;
  }

  // API endpoints theo postgREST functions
  static const String _getArticlesEndpoint = '/rest/rpc/get_articles';
  static const String _getArticleByIdEndpoint = '/rest/rpc/get_article_by_id';
  static const String _getArticleByCodeEndpoint = '/rest/rpc/get_article_by_code';
  static const String _getFeaturedArticlesEndpoint = '/rest/rpc/get_featured_articles';
  static const String _getArticleVersionsEndpoint = '/rest/rpc/get_article_versions';
  static const String _searchArticlesByTagsEndpoint = '/rest/rpc/search_articles_by_tags';

  /// Lấy danh sách bài viết
  @override
  Future<List<ArticleModel>> getArticles({
    String? search,
    ArticleType? articleType,
    String status = 'PUBLISHED',
    bool? isFeatured,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.i('Getting articles with params: search=$search, type=$articleType, status=$status, limit=$limit, offset=$offset');

      final params = ArticleSearchParams(
        search: search,
        articleType: articleType,
        status: status,
        isFeatured: isFeatured,
        limit: limit,
        offset: offset,
      );

      final response = await _apiService.post(
        _getArticlesEndpoint,
        data: params.toMap(),
      );

      if (response.data is List) {
        final List<dynamic> articlesData = response.data as List<dynamic>;
        final articles = articlesData
            .map((json) => ArticleModel.fromJson(json as Map<String, dynamic>))
            .toList();

        _logger.i('Retrieved ${articles.length} articles');
        return articles;
      } else {
        throw ArticleException(
          message: 'Invalid response format for articles list',
          type: ArticleExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when getting articles: ${e.message}');
      throw ArticleException(
        message: 'Không thể lấy danh sách bài viết: ${e.message}',
        type: ArticleExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      debugPrint('Unknown error when getting articles: $e');
      _logger.e('Unknown error when getting articles: $e');
      throw ArticleException(
        message: 'Lỗi không xác định khi lấy danh sách bài viết',
        type: ArticleExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy bài viết theo ID
  @override
  Future<ArticleModel> getArticleById(String articleId) async {
    try {
      _logger.i('Getting article by ID: $articleId');

      final response = await _apiService.post(
        _getArticleByIdEndpoint,
        data: {'p_article_id': articleId},
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true) {
          final articleData = responseData['data'] as Map<String, dynamic>;
          final article = ArticleModel.fromJson(articleData);
          
          _logger.i('Retrieved article: ${article.title}');
          return article;
        } else {
          final errorMessage = responseData['message'] as String? ?? 'Article not found';
          throw ArticleException(
            message: errorMessage,
            type: ArticleExceptionType.notFound,
          );
        }
      } else {
        throw ArticleException(
          message: 'Invalid response format for article by ID',
          type: ArticleExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when getting article by ID: ${e.message}');
      throw ArticleException(
        message: 'Không thể lấy bài viết: ${e.message}',
        type: ArticleExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when getting article by ID: $e');
      throw ArticleException(
        message: 'Lỗi không xác định khi lấy bài viết',
        type: ArticleExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy bài viết theo code
  @override
  Future<ArticleModel> getArticleByCode(String articleCode) async {
    try {
      _logger.i('Getting article by code: $articleCode');

      final response = await _apiService.post(
        _getArticleByCodeEndpoint,
        data: {'p_article_code': articleCode},
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        
        if (responseData['success'] == true) {
          final articleData = responseData['data'] as Map<String, dynamic>;
          final article = ArticleModel.fromJson(articleData);
          
          _logger.i('Retrieved article by code: ${article.title}');
          return article;
        } else {
          final errorMessage = responseData['message'] as String? ?? 'Article not found';
          throw ArticleException(
            message: errorMessage,
            type: ArticleExceptionType.notFound,
          );
        }
      } else {
        throw ArticleException(
          message: 'Invalid response format for article by code',
          type: ArticleExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when getting article by code: ${e.message}');
      throw ArticleException(
        message: 'Không thể lấy bài viết: ${e.message}',
        type: ArticleExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when getting article by code: $e');
      throw ArticleException(
        message: 'Lỗi không xác định khi lấy bài viết',
        type: ArticleExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy bài viết nổi bật
  @override
  Future<List<ArticleModel>> getFeaturedArticles({int limit = 10}) async {
    try {
      _logger.i('Getting featured articles with limit: $limit');

      final response = await _apiService.post(
        _getFeaturedArticlesEndpoint,
        data: {'p_limit': limit},
      );

      if (response.data is List) {
        final List<dynamic> articlesData = response.data as List<dynamic>;
        final articles = articlesData
            .map((json) => ArticleModel.fromJson(json as Map<String, dynamic>))
            .toList();

        _logger.i('Retrieved ${articles.length} featured articles');
        return articles;
      } else {
        throw ArticleException(
          message: 'Invalid response format for featured articles',
          type: ArticleExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when getting featured articles: ${e.message}');
      throw ArticleException(
        message: 'Không thể lấy bài viết nổi bật: ${e.message}',
        type: ArticleExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when getting featured articles: $e');
      throw ArticleException(
        message: 'Lỗi không xác định khi lấy bài viết nổi bật',
        type: ArticleExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Lấy lịch sử phiên bản bài viết
  @override
  Future<List<ArticleVersionModel>> getArticleVersions(String articleId) async {
    try {
      _logger.i('Getting article versions for ID: $articleId');

      final response = await _apiService.post(
        _getArticleVersionsEndpoint,
        data: {
          'p_article_id': articleId,
          'p_limit': 10, // Default limit
        },
      );

      if (response.data is List) {
        final List<dynamic> versionsData = response.data as List<dynamic>;
        final versions = versionsData
            .map((json) => ArticleVersionModel.fromJson(json as Map<String, dynamic>))
            .toList();

        _logger.i('Retrieved ${versions.length} article versions');
        return versions;
      } else {
        throw ArticleException(
          message: 'Invalid response format for article versions',
          type: ArticleExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when getting article versions: ${e.message}');
      throw ArticleException(
        message: 'Không thể lấy lịch sử phiên bản: ${e.message}',
        type: ArticleExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when getting article versions: $e');
      throw ArticleException(
        message: 'Lỗi không xác định khi lấy lịch sử phiên bản',
        type: ArticleExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Tìm kiếm bài viết theo tags
  @override
  Future<List<ArticleModel>> searchArticlesByTags({
    required List<String> tags,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      _logger.i('Searching articles by tags: $tags, limit: $limit, offset: $offset');

      final response = await _apiService.post(
        _searchArticlesByTagsEndpoint,
        data: {
          'p_tags': tags,
          'p_limit': limit,
          'p_offset': offset,
        },
      );

      if (response.data is List) {
        final List<dynamic> articlesData = response.data as List<dynamic>;
        final articles = articlesData
            .map((json) => ArticleModel.fromJson(json as Map<String, dynamic>))
            .toList();

        _logger.i('Found ${articles.length} articles matching tags: $tags');
        return articles;
      } else {
        throw ArticleException(
          message: 'Invalid response format for articles search by tags',
          type: ArticleExceptionType.invalidResponse,
        );
      }
    } on ApiException catch (e) {
      _logger.e('API error when searching articles by tags: ${e.message}');
      throw ArticleException(
        message: 'Không thể tìm kiếm bài viết: ${e.message}',
        type: ArticleExceptionType.apiError,
        originalException: e,
      );
    } catch (e) {
      _logger.e('Unknown error when searching articles by tags: $e');
      throw ArticleException(
        message: 'Lỗi không xác định khi tìm kiếm bài viết',
        type: ArticleExceptionType.unknown,
        originalException: e,
      );
    }
  }

  /// Tìm kiếm bài viết với params
  @override
  Future<List<ArticleModel>> searchArticles(ArticleSearchParams params) async {
    return await getArticles(
      search: params.search,
      articleType: params.articleType,
      status: params.status ?? 'PUBLISHED',
      isFeatured: params.isFeatured,
      limit: params.limit ?? 20,
      offset: params.offset ?? 0,
    );
  }

  /// Kiểm tra tính khả dụng của article API
  @override
  Future<bool> checkArticleApiAvailability() async {
    try {
      await getFeaturedArticles(limit: 1);
      return true;
    } catch (e) {
      _logger.w('Article API not available: $e');
      return false;
    }
  }

  /// Lấy danh sách các loại bài viết có sẵn
  @override
  List<ArticleType> getAvailableArticleTypes() {
    return ArticleType.values;
  }

  /// Lấy tên hiển thị của loại bài viết
  @override
  String getArticleTypeDisplayName(ArticleType type) {
    switch (type) {
      case ArticleType.productIntro:
        return 'Giới thiệu sản phẩm';
      case ArticleType.policy:
        return 'Chính sách';
      case ArticleType.promotion:
        return 'Chương trình khuyến mại';
      case ArticleType.competition:
        return 'Thi đua';
      case ArticleType.news:
        return 'Tin tức';
      case ArticleType.guideline:
        return 'Hướng dẫn';
      case ArticleType.announcement:
        return 'Thông báo';
      case ArticleType.training:
        return 'Đào tạo';
      case ArticleType.termsOfService:
        return 'Điều khoản sử dụng';
      case ArticleType.privacyPolicy:
        return 'Chính sách bảo mật';
    }
  }
} 