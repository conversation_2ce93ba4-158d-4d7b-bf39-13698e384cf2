# Article Module

Module này cung cấp các model và service để tương tác với API bài viết thông qua postgREST functions.

## Cấu trúc

```
lib/core/article/
├── models/
│   ├── article_model.dart              # Model chính cho bài viết
│   ├── article_response_model.dart     # Model cho API response
│   ├── article_version_model.dart      # Model cho lịch sử phiên bản
│   └── article_search_params.dart      # Model cho tham số tìm kiếm
├── services/
│   ├── article_service.dart            # Service chính
│   └── article_exception.dart          # Exception classes
├── index.dart                          # Export tất cả modules
└── README.md                           # Hướng dẫn sử dụng
```

## Cách sử dụng

### 1. Import module

```dart
import 'package:kiloba_biz/core/article/index.dart';
```

### 2. Khởi tạo service

```dart
final articleService = ArticleService();
```

### 3. <PERSON><PERSON><PERSON> chức năng chính

#### L<PERSON><PERSON> danh sách bài viết
```dart
// L<PERSON>y tất cả bài viết
final articles = await articleService.getArticles();

// Lấy bài viết với tham số
final articles = await articleService.getArticles(
  search: 'từ khóa',
  articleType: ArticleType.news,
  limit: 10,
  offset: 0,
);
```

#### Lấy bài viết theo ID
```dart
final article = await articleService.getArticleById('article-id');
```

#### Lấy bài viết theo code
```dart
final article = await articleService.getArticleByCode('ARTICLE-001');
```

#### Lấy bài viết nổi bật
```dart
final featuredArticles = await articleService.getFeaturedArticles(limit: 5);
```

#### Tìm kiếm theo tags
```dart
final articles = await articleService.searchArticlesByTags(
  tags: ['training', 'finance'],
  limit: 10,
);
```

#### Lấy lịch sử phiên bản
```dart
final versions = await articleService.getArticleVersions(
  articleId: 'article-id',
  limit: 10,
);
```

### 4. Xử lý lỗi

```dart
try {
  final articles = await articleService.getArticles();
} on ArticleException catch (e) {
  switch (e.type) {
    case ArticleExceptionType.notFound:
      print('Không tìm thấy bài viết');
      break;
    case ArticleExceptionType.apiError:
      print('Lỗi API: ${e.message}');
      break;
    case ArticleExceptionType.networkError:
      print('Lỗi mạng');
      break;
    default:
      print('Lỗi không xác định: ${e.message}');
  }
}
```

### 5. Các loại bài viết

```dart
// Lấy danh sách loại bài viết
final types = articleService.getAvailableArticleTypes();

// Lấy tên hiển thị
final displayName = articleService.getArticleTypeDisplayName(ArticleType.news);
// Kết quả: "Tin tức"
```

## API Endpoints

Module này sử dụng các postgREST functions sau:

- `get_articles` - Lấy danh sách bài viết
- `get_article_by_id` - Lấy bài viết theo ID
- `get_article_by_code` - Lấy bài viết theo code
- `get_featured_articles` - Lấy bài viết nổi bật
- `get_article_versions` - Lấy lịch sử phiên bản
- `search_articles_by_tags` - Tìm kiếm theo tags

## Lưu ý

- Tất cả models đều sử dụng `@JsonSerializable(fieldRename: FieldRename.snake)` để phù hợp với dữ liệu từ postgREST
- Service sử dụng `appLogger` thay vì `print` theo quy tắc của dự án
- Các exception được xử lý chi tiết với các loại lỗi khác nhau
- API calls được thực hiện thông qua `ApiService` singleton 