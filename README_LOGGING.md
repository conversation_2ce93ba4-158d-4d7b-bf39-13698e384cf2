# Kiloba Business App - Logging System

## Tổng quan

Dự án sử dụng hệ thống logging thống nhất với `AppLogger` wrapper trên `logger` package để cung cấp logging an toàn, hiệu quả và dễ quản lý.

## 🚀 Quick Start

### 1. Import AppLogger

```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';
```

### 2. Sử dụng logging

```dart
// Basic logging
appLogger.i('User logged in successfully');
appLogger.e('API call failed', error: exception);

// Tagged logging
appLogger.logWithTag('Auth', 'Login attempt');

// API logging
appLogger.logApiRequest('POST', '/api/login', data: requestData);
appLogger.logApiResponse(200, '/api/login', data: responseData);
```

## 📁 File Structure

```
lib/
├── shared/
│   ├── utils/
│   │   ├── app_logger.dart          # Main AppLogger class
│   │   ├── index.dart               # Exports
│   │   └── README.md                # Usage guide
│   └── services/
│       └── ...                      # Services using AppLogger
├── features/
│   └── ...                          # Features using AppLogger
├── scripts/
│   └── migrate_logger.dart          # Migration script
└── docs/
    └── logging_migration_guide.md   # Migration guide
```

## 🔧 Features

### ✅ Đã hoàn thành

1. **AppLogger wrapper** - Interface logging thống nhất
2. **Security masking** - Tự động mask thông tin nhạy cảm
3. **Environment-aware** - Log level khác nhau cho debug/release
4. **Performance optimized** - Tối ưu cho production builds
5. **Migration tools** - Script và hướng dẫn migration
6. **Documentation** - Hướng dẫn sử dụng chi tiết

### 🔄 Đang thực hiện

1. **Migration từ Logger()** - Chuyển đổi dần dần từ Logger() trực tiếp
2. **Advanced logging** - Performance metrics, user actions
3. **Log aggregation** - Tích hợp với external logging services

## 📊 Migration Status

### Core Services
- [x] `main.dart` - ✅ Migrated
- [ ] `api_service.dart` - ⏳ Pending
- [ ] `auth_service.dart` - ⏳ Pending
- [ ] `token_manager.dart` - ⏳ Pending
- [ ] `firebase_service.dart` - ⏳ Pending

### Feature Services
- [ ] `user_profile_service.dart` - ⏳ Pending
- [ ] `auth_repository.dart` - ⏳ Pending
- [ ] `login_bloc.dart` - ⏳ Pending
- [ ] `dashboard_stats_service.dart` - ⏳ Pending

## 🛠️ Migration Tools

### 1. Migration Script

```bash
# Migrate single file
dart scripts/migrate_logger.dart lib/shared/services/api_service.dart

# Check migration status
dart scripts/migrate_logger.dart --status

# Scan for Logger usage
dart scripts/migrate_logger.dart --scan
```

### 2. Manual Migration

Xem hướng dẫn chi tiết trong `docs/logging_migration_guide.md`

## 📖 Usage Examples

### Service Class

```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

class UserService {
  Future<User> getUser(String id) async {
    try {
      appLogger.i('Fetching user: $id');
      
      final user = await _apiService.getUser(id);
      
      appLogger.i('User fetched successfully: ${user.name}');
      return user;
    } catch (e, stackTrace) {
      appLogger.e('Failed to fetch user: $id', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }
}
```

### Bloc Class

```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(AuthInitial()) {
    on<LoginRequested>((event, emit) async {
      try {
        appLogger.logWithTag('Auth', 'Login requested for user: ${event.username}');
        
        final user = await _authService.login(event.username, event.password);
        
        appLogger.logWithTag('Auth', 'Login successful for user: ${user.username}');
        emit(AuthSuccess(user));
      } catch (e, stackTrace) {
        appLogger.logWithTag('Auth', 'Login failed', LogLevel.error);
        appLogger.e('Login error', error: e, stackTrace: stackTrace);
        emit(AuthFailure(e.toString()));
      }
    });
  }
}
```

### Widget Class

```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

class LoginScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    appLogger.logUserAction('login_screen_viewed');
    
    return Scaffold(
      body: Column(
        children: [
          ElevatedButton(
            onPressed: () {
              appLogger.logUserAction('login_button_pressed');
              // ... login logic
            },
            child: Text('Login'),
          ),
        ],
      ),
    );
  }
}
```

## 🔒 Security Features

### Auto-masking

AppLogger tự động mask các thông tin nhạy cảm:

**Headers:**
- `authorization`
- `x-api-key`
- `x-auth-token`

**Data:**
- `password`
- `token`
- `access_token`
- `refresh_token`
- `secret`
- `key`
- `api_key`

### Example

```dart
// Input
appLogger.logApiRequest('POST', '/api/login', 
  headers: {'authorization': 'Bearer abc123xyz'},
  data: {'username': 'user', 'password': 'secret123'}
);

// Output (masked)
🚀 POST /api/login
📝 Headers: {authorization: Bearer abc1***...***xyz}
📦 Data: {username: user, password: ***MASKED***}
```

## ⚙️ Configuration

### Log Levels

- **Verbose**: Thông tin chi tiết nhất
- **Debug**: Thông tin debug
- **Info**: Thông tin quan trọng
- **Warning**: Cảnh báo
- **Error**: Lỗi

### Environment Behavior

- **Debug Mode**: Log tất cả levels
- **Release Mode**: Chỉ log Warning và Error

### Custom Configuration

```dart
// Trong main.dart
appLogger.initialize();

// Custom configuration (optional)
appLogger.logger.level = Level.debug;
```

## 📈 Performance Monitoring

### API Performance

```dart
final stopwatch = Stopwatch()..start();
try {
  final response = await apiService.getData();
  stopwatch.stop();
  appLogger.logPerformance('API call', stopwatch.elapsed);
} catch (e) {
  stopwatch.stop();
  appLogger.logPerformance('API call (failed)', stopwatch.elapsed);
  rethrow;
}
```

### Database Performance

```dart
appLogger.logDatabaseOp('query', 'users', data: {'filter': 'active'});
```

### Cache Performance

```dart
appLogger.logCacheOp('get', 'user_profile_123', data: cachedData);
```

## 🧪 Testing

### Unit Tests

```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

void main() {
  group('UserService', () {
    test('should log user fetch', () async {
      // Arrange
      final service = UserService();
      
      // Act
      await service.getUser('123');
      
      // Assert
      // Verify logs were called (using mock logger if needed)
    });
  });
}
```

### Integration Tests

```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

void main() {
  group('Auth Flow', () {
    testWidgets('should log login process', (tester) async {
      // Arrange
      await tester.pumpWidget(MyApp());
      
      // Act
      await tester.tap(find.text('Login'));
      await tester.pump();
      
      // Assert
      // Verify logs were generated
    });
  });
}
```

## 🚨 Troubleshooting

### Common Issues

1. **Import error**
   ```dart
   // ❌ Wrong
   import 'package:logger/logger.dart';
   
   // ✅ Correct
   import 'package:kiloba_biz/shared/utils/app_logger.dart';
   ```

2. **Logger not initialized**
   ```dart
   // Make sure AppLogger is initialized in main.dart
   appLogger.initialize();
   ```

3. **Logs not showing**
   - Check log level configuration
   - Verify debug mode is enabled
   - Check console output

### Debug Commands

```bash
# Check Logger usage
grep -r "Logger()" lib/

# Check AppLogger usage
grep -r "appLogger" lib/

# Check migration status
dart scripts/migrate_logger.dart --status
```

## 📚 Additional Resources

- [Logger Package Documentation](https://pub.dev/packages/logger)
- [Flutter Logging Best Practices](https://docs.flutter.dev/testing/debugging)
- [AppLogger Usage Guide](lib/shared/utils/README.md)
- [Migration Guide](docs/logging_migration_guide.md)

## 🤝 Contributing

Khi thêm logging mới:

1. Sử dụng `AppLogger` thay vì `Logger()` trực tiếp
2. Chọn log level phù hợp
3. Thêm context cho log messages
4. Mask thông tin nhạy cảm
5. Test với debug và release builds

## 📝 Changelog

### v1.0.0 (Current)
- ✅ AppLogger wrapper implementation
- ✅ Security masking
- ✅ Migration tools
- ✅ Documentation
- ✅ Main.dart migration

### v1.1.0 (Planned)
- 🔄 Complete service migration
- 🔄 Advanced logging features
- 🔄 Performance monitoring
- 🔄 Log aggregation 