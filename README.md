# KiloBa Business App 🏦

**Ứng dụng quản lý bán hàng và khách hàng cho Ngân hàng Kienlongbank**

[![Flutter Version](https://img.shields.io/badge/Flutter-3.0+-blue.svg)](https://flutter.dev/)
[![Platform](https://img.shields.io/badge/Platform-iOS%20%7C%20Android-green.svg)](https://flutter.dev/)
[![License](https://img.shields.io/badge/License-Private-red.svg)](LICENSE)

## 📋 Mô tả

KiloBa Business App là ứng dụng di động được phát triển cho đội ngũ bán hàng của Ngân hàng Kienlongbank, giúp quản lý khách hàng, tạo giao dịch và theo dõi hiệu suất kinh doanh một cách hiệu quả.

## ✨ Tính năng chính

### 🏠 Dashboard
- Tổng quan doanh số và KPI
- Thống kê giao dịch theo thời gian
- Thông báo quan trọng
- Shortcuts đến các chức năng chính

### 👥 Quản lý khách hàng
- **Thêm khách hàng mới**: Quy trình 4 bước với validation
- **Tìm kiếm & lọc**: Theo tên, điện thoại, email, trạng thái
- **Chi tiết khách hàng**: Thông tin đầy đủ, lịch sử giao dịch
- **Phân loại khách hàng**: VIP, Premium, Tiềm năng

### 💰 Quản lý giao dịch
- **Tạo giao dịch mới**: Wizard 5 bước trực quan
- **Sản phẩm đa dạng**: Vay tín chấp, thẻ tín dụng, vay thế chấp, tiết kiệm
- **Quản lý tài liệu**: Upload và theo dõi giấy tờ yêu cầu
- **Theo dõi trạng thái**: Từ khởi tạo đến hoàn thành

### 🏦 Sản phẩm ngân hàng
- Danh mục sản phẩm tín dụng
- Thông tin chi tiết lãi suất, điều kiện
- Tính toán khoản vay sơ bộ

### 🔔 Thông báo
- Cập nhật trạng thái giao dịch
- Thông báo từ hệ thống
- Reminder các task quan trọng

### 👤 Quản lý tài khoản
- Thông tin cá nhân
- Cài đặt ứng dụng
- Dark/Light theme

## 🚀 Cài đặt và chạy

### Yêu cầu hệ thống
- Flutter SDK: 3.0+
- Dart SDK: 3.0+
- iOS: 12.0+
- Android: API level 21+

### Bước 1: Clone repository
```bash
git clone https://git-internal.kienlongbank.co/dev/sales-app/kiloba-biz-app.git
cd kiloba-biz-app
```

### Bước 2: Cài đặt dependencies
```bash
flutter pub get
```

### Bước 3: Chạy ứng dụng
```bash
# Debug mode
flutter run

# Release mode  
flutter run --release

# Chạy trên device cụ thể
flutter run -d <device_id>
```

### Bước 4: Build cho production
```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release

# iOS
flutter build ios --release
```

## 📁 Cấu trúc dự án

```
lib/
├── core/                   # Core utilities & shared resources
│   ├── constants/          # App constants
│   ├── theme/             # Theme system (colors, typography, etc.)
│   ├── utils/             # Utility functions
│   └── widgets/           # Reusable core widgets
├── features/              # Feature-based modules
│   ├── auth/              # Authentication (login, register)
│   ├── dashboard/         # Dashboard & home screen
│   ├── customers/         # Customer management
│   ├── transactions/      # Transaction management
│   ├── products/          # Product catalog
│   ├── notifications/     # Notification system
│   └── account/           # User account & settings
├── shared/                # Shared across features
│   ├── models/            # Data models
│   ├── services/          # Business logic services
│   └── widgets/           # Reusable UI components
└── main.dart              # App entry point
```

### Feature Module Structure
Mỗi feature được tổ chức theo cấu trúc:
```
feature/
├── models/        # Data models for this feature
├── screens/       # UI screens
├── services/      # Business logic & API calls
├── widgets/       # Feature-specific widgets
└── index.dart     # Public exports
```

## 🎨 Theme System

Ứng dụng sử dụng hệ thống theme linh hoạt với:

### Colors
- **Kienlongbank Orange**: `#FF6B35` - Primary brand color
- **Kienlongbank Sky Blue**: `#4A90E2` - Secondary color
- **Success**: `#28A745` - Success states
- **Error**: `#DC3545` - Error states

### Typography
- **Heading**: Roboto Bold
- **Body**: Roboto Regular  
- **Caption**: Roboto Light

### Dark/Light Mode
- Tự động theo hệ thống hoặc thủ công
- Lưu preference của user
- Seamless switching

Xem chi tiết: [docs/theme/README.md](docs/theme/README.md)

## 🔧 Development Guidelines

### Code Style
- Tuân thủ [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Sử dụng `flutter analyze` để kiểm tra
- Format code với `dart format`

### Architecture
- **Feature-based**: Tách biệt các tính năng
- **SOLID principles**: Dependency injection, single responsibility
- **Clean Architecture**: UI - Business Logic - Data separation

### Testing
```bash
# Run all tests
flutter test

# Test coverage
flutter test --coverage
```

### Code Analysis
```bash
# Static analysis
flutter analyze

# Check formatting
dart format --set-exit-if-changed .
```

## 📚 Tài liệu

- [Theme Guide](docs/theme/README.md) - Hướng dẫn sử dụng theme
- [Design Specs](docs/design/) - Thiết kế màn hình chi tiết
- [Context Guide](docs/context/common.md) - Context và conventions

## 🤝 Đóng góp

### Git Workflow
1. Tạo branch từ `main`:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Commit với message rõ ràng:
   ```bash
   git commit -m "feat: add customer search functionality"
   ```

3. Push và tạo Merge Request:
   ```bash
   git push origin feature/your-feature-name
   ```

### Commit Convention
- `feat:` - Tính năng mới
- `fix:` - Sửa lỗi
- `docs:` - Cập nhật tài liệu
- `style:` - Format code, không ảnh hưởng logic
- `refactor:` - Refactor code
- `test:` - Thêm/sửa tests

## 📱 Screenshots

### Giao diện chính
| Dashboard | Khách hàng | Giao dịch |
|-----------|------------|-----------|
| ![Dashboard](docs/screenshoot/dashboard.png) | ![Customers](docs/screenshoot/customer.png) | ![Transaction](docs/screenshoot/transaction.png) |

### Giao diện phụ
| Thông báo | Tài khoản |
|-----------|-----------|
| ![Notifications](docs/screenshoot/notification.png) | ![Account](docs/screenshoot/account.png) |

## 🔒 Security & Privacy

- Tuân thủ quy định bảo mật ngân hàng
- Mã hóa dữ liệu nhạy cảm
- Secure API communication
- Biometric authentication support

## 📞 Liên hệ & Hỗ trợ

- **Team**: Sales App Development Team
- **Email**: <EMAIL>
- **GitLab**: [Project Repository](https://git-internal.kienlongbank.co/dev/sales-app/kiloba-biz-app)

## 📄 License

Copyright (c) 2024 Kienlongbank. All rights reserved.

---

**Made with ❤️ by Kienlongbank Development Team**
