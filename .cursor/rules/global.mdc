---
alwaysApply: true
---
- <PERSON><PERSON><PERSON><PERSON> sử dụng deprecated `withOpacity()` thay vào đó sử dụng `withValues(alpha:)`
- <PERSON><PERSON><PERSON> tuân thủ kiến trúc featured based của dự án
- Luôn tuân thủ theme của dự án, hãy đọc tài liệu `docs/theme/README.md`
- <PERSON><PERSON>n thiết kế dark và light theme.
- Luôn sử dụng appLogger `lib/shared/utils/app_logger.dart` thay vì print. <PERSON>y nhiên các log thêm để debug tạm thời thì có thể dùng `debugPrint(..)` và clean sau khi hoàn thành.

## 🔒 MUST - FVM (Flutter Version Management)

- Always use FVM to manage the Flutter SDK version.
  *(Luôn dùng FVM để quản lý phiên bản Flutter.)*

- Use `fvm flutter ...` instead of global Flutter commands.
  *(Dùng lệnh FVM thay cho Flutter toàn cục.)*

- Code must match the version declared in `.fvm/fvm_config.json`.
  *(Code phải tương thích với phiên bản Flutter đã cấu hình.)*

- Do not upgrade Flutter version without review and approval.
  *(Không được tự ý nâng cấp Flutter.)*

---

## 🔒 MUST - Code Quality Check (`flutter analyze`)

- Always run `fvm flutter analyze` after generating code.
  *(Luôn chạy `flutter analyze` sau khi sinh code.)*

- Fix all warnings and errors where possible (e.g., unused import, missing const).
  *(Fix toàn bộ cảnh báo/lỗi có thể tự động sửa được.)*

- Add `// TODO:` or `// ignore:` with reasons for unfixable issues.
  *(Thêm ghi chú rõ ràng nếu không thể fix.)*

- Code is not complete unless `flutter analyze` returns clean.
  *(Code chỉ được coi là hoàn chỉnh khi không còn lỗi.)*

---


## 🔒 MUST - Barrel Export Management (Quản lý Barrel Export)

> **Barrel Export** là file export tất cả các class/function từ một thư mục để giảm thiểu import statements.
> *(Barrel Export là file export tất cả class/function từ một thư mục để giảm import statements.)*

### 📦 **Barrel Export Rules (Quy tắc Barrel Export)**

- **ALWAYS** add new files to barrel export when creating them.
  *(Luôn thêm file mới vào barrel export khi tạo.)*

- **CREATE** barrel export file if it doesn't exist and the folder contains multiple related files.
  *(Tạo barrel export file nếu chưa có và thư mục chứa nhiều file liên quan.)*

- **USE** barrel export for imports to minimize import statements.
  *(Sử dụng barrel export để import để giảm thiểu import statements.)*

- **NAME** barrel export files after the folder name (e.g., `widgets.dart` for `widgets/` folder).
  *(Đặt tên barrel export file theo tên thư mục, ví dụ: `widgets.dart` cho thư mục `widgets/`.)*


### 📋 **Barrel Export Checklist (Danh sách kiểm tra)**

When creating a new file:
*(Khi tạo file mới:)*

1. ✅ Check if barrel export exists in the folder
   *(Kiểm tra barrel export có tồn tại trong thư mục không)*

2. ✅ If exists: Add export statement to barrel export
   *(Nếu có: Thêm export statement vào barrel export)*

3. ✅ If not exists: Create barrel export file
   *(Nếu không có: Tạo barrel export file)*

4. ✅ Update parent barrel export if needed
   *(Cập nhật barrel export cha nếu cần)*

5. ✅ Use barrel export for imports in other files
   *(Sử dụng barrel export để import trong file khác)*

## 🔶 SHOULD - Reuse & Code Organization

- Check existing files before creating new ones.
  *(Kiểm tra code có sẵn trước khi tạo mới.)*

- Reuse or refactor existing code when possible.
  *(Ưu tiên tái sử dụng/refactor.)*

- Keep `.gitignore` up to date to exclude unwanted generated files.
  *(Cập nhật `.gitignore` để loại bỏ file không cần thiết.)*

---

## 🔶 SHOULD - Code Style & Maintainability

- Use `const`, `final`, `@override`, and arrow syntax consistently.
  *(Dùng các từ khóa và cú pháp chuẩn để tối ưu code.)*

- Use trailing commas for clean formatting.
  *(Dùng dấu phẩy cuối để code dễ đọc hơn.)*

- Avoid nesting >3 widget levels. Extract widgets.
  *(Tách widget nếu nesting quá sâu.)*

- Remove unused code or dead branches.
  *(Xoá code không còn dùng.)*

---

## 🔶 SHOULD - UI & UX Guidelines

- Use `AppColor`, `AppTheme`, and `AppDimens` consistently.
  *(Luôn dùng theme/màu/kích thước từ file cấu hình.)*

- Ensure all UI is adaptive (responsive) across Android/iOS and screen sizes.
  *(Đảm bảo UI responsive cho mọi thiết bị.)*

- Test screens visually and structurally against existing ones.
  *(Thiết kế màn mới phải đồng bộ với màn cũ.)*

- Use `flutter_screenutil`, `LayoutBuilder`, or `MediaQuery` for responsive layout.
  *(Dùng thư viện/layout builder phù hợp để responsive.)*

---

### 🔶 SHOULD - State Management & `setState` Usage

- Avoid using `setState` unless absolutely necessary.  
  *(Hạn chế dùng `setState` nếu không thực sự cần thiết.)*

- If you must use `setState`, ensure it only affects a small part of the widget tree, not the entire screen.  
  *(Chỉ dùng `setState` nếu nó không làm build lại toàn bộ màn hình.)*
