# API Service Refactor

## Tổng quan

Đ<PERSON>y là kết quả của việc refactor `ApiService` từ một class lớn, phức tạp thành một kiến trúc clean, modular và dễ maintain.

## Cấu trúc mới

### 1. Configuration Management
```
lib/shared/services/api/
├── api_configuration.dart      # Quản lý cấu hình API
└── index.dart                  # Export tất cả services
```

### 2. Authentication Services
```
lib/shared/services/auth/
├── token_repository.dart       # Abstract repository cho token management
└── authentication_service.dart # Quản lý authentication logic
```

### 3. Queue Management
```
lib/shared/services/queue/
├── pending_request.dart        # Model cho pending request
└── request_queue_service.dart  # Quản lý queue requests
```

### 4. Error Handling
```
lib/shared/services/error_handling/
└── error_handler_strategy.dart # Strategy pattern cho error handling
```

### 5. Interceptors
```
lib/shared/services/interceptors/
└── interceptor_builder.dart    # Builder pattern cho interceptors
```

## Các Design Patterns được sử dụng

### 1. **Strategy Pattern** - Error Handling
```dart
abstract class ErrorHandlerStrategy {
  Future<void> handle(DioException error, ErrorInterceptorHandler handler);
  bool canHandle(DioException error);
  int get priority;
}
```

### 2. **Builder Pattern** - Interceptor Creation
```dart
final builder = InterceptorBuilder(logger: _logger)
  .addLogging()
  .addDeviceHeaders(deviceInfoService)
  .addAuthentication(tokenRepository, queueService, authService, dio)
  .addErrorHandling(errorHandlers)
  .build();
```

### 3. **Repository Pattern** - Token Management
```dart
abstract class TokenRepository {
  Future<String?> getAccessToken();
  Future<String?> getRefreshToken();
  Future<void> saveTokens(AuthResponse response);
  // ...
}
```

### 4. **Factory Pattern** - Configuration
```dart
ApiConfiguration.fromEnvironment()  // Production config
ApiConfiguration.forTesting()       // Testing config
```

## Lợi ích của Refactor

### 1. **Separation of Concerns**
- Mỗi service có trách nhiệm rõ ràng
- Authentication logic tách riêng
- Queue management độc lập
- Error handling modular

### 2. **Testability**
- Có thể test từng component riêng biệt
- Dễ mock dependencies
- Unit tests nhỏ và focused

### 3. **Maintainability**
- Code dễ đọc và hiểu
- Dễ thêm/sửa/xóa tính năng
- Giảm coupling giữa các components

### 4. **Scalability**
- Dễ thêm tính năng mới
- Có thể tái sử dụng components
- Flexible architecture

### 5. **Configuration Management**
- Environment-specific configs
- Runtime configuration changes
- Feature flags support

## Cách sử dụng

### 1. Khởi tạo RefactoredApiService
```dart
final apiService = RefactoredApiService();

await apiService.initialize(
  configuration: ApiConfiguration.fromEnvironment(),
  // Có thể inject custom dependencies
);
```

### 2. Sử dụng như API Service cũ
```dart
// GET request
final response = await apiService.get('/api/users');

// POST request
final response = await apiService.post('/api/users', data: userData);

// PUT request
final response = await apiService.put('/api/users/1', data: userData);

// DELETE request
final response = await apiService.delete('/api/users/1');
```

### 3. Debug và Monitoring
```dart
// Debug status
apiService.debugStatus();

// Get debug info
final info = apiService.getDebugInfo();

// Emergency cleanup
apiService.emergencyCleanup();
```

## Migration Strategy

### Phase 1: Parallel Implementation ✅
- Tạo RefactoredApiService song song với ApiService cũ
- Test và validate functionality

### Phase 2: Gradual Migration
- Thay thế từng service một
- Update imports trong codebase
- Maintain backward compatibility

### Phase 3: Cleanup
- Remove old ApiService
- Update documentation
- Performance optimization

## Testing

### Unit Tests
```dart
// Test AuthenticationService
test('should refresh token successfully', () async {
  final authService = AuthenticationService(...);
  final result = await authService.refreshToken(dio);
  expect(result, true);
});

// Test RequestQueueService
test('should queue requests correctly', () {
  final queueService = RequestQueueService(...);
  queueService.enqueue(error, handler);
  expect(queueService.pendingRequestsCount, 1);
});
```

### Integration Tests
```dart
// Test full API flow
test('should handle 401 and refresh token', () async {
  final apiService = RefactoredApiService();
  await apiService.initialize();
  
  // Mock 401 response
  // Verify token refresh
  // Verify request retry
});
```

## Performance Considerations

### 1. **Memory Management**
- Proper cleanup của pending requests
- Timeout handling
- Resource disposal

### 2. **Network Optimization**
- Request batching
- Connection pooling
- Caching strategies

### 3. **Error Recovery**
- Exponential backoff
- Circuit breaker pattern
- Graceful degradation

## Future Enhancements

### 1. **Event-Driven Architecture**
```dart
abstract class ApiEventListener {
  void onTokenRefresh(TokenRefreshEvent event);
  void onRequestQueued(RequestQueuedEvent event);
  void onAuthenticationRequired(AuthenticationRequiredEvent event);
}
```

### 2. **State Management**
```dart
abstract class ApiServiceState {
  Future<void> handleRequest(DioException error, ErrorInterceptorHandler handler);
}
```

### 3. **Dependency Injection**
```dart
class ServiceLocator {
  static ApiService get apiService => _apiService;
  static AuthenticationService get authService => _authService;
  // ...
}
```

## Conclusion

Refactor này đã chuyển đổi `ApiService` từ một monolithic class thành một kiến trúc clean, modular với:

- **Better separation of concerns**
- **Improved testability**
- **Enhanced maintainability**
- **Greater flexibility**
- **Cleaner code structure**

Việc refactor này tạo nền tảng vững chắc cho việc phát triển và mở rộng ứng dụng trong tương lai. 