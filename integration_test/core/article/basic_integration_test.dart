import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:kiloba_biz/core/article/services/article_service.dart';
import 'package:kiloba_biz/core/article/services/article_exception.dart';
import 'package:kiloba_biz/core/article/models/article_model.dart';
import 'package:kiloba_biz/core/article/models/article_version_model.dart';
import 'package:kiloba_biz/shared/services/api/api_service.dart';
import 'package:kiloba_biz/shared/services/api/api_configuration.dart';
import 'package:kiloba_biz/shared/utils/app_logger.dart';
import 'package:integration_test/integration_test.dart';

/// Basic integration test để kiểm tra cơ bản
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Basic ArticleService Integration Tests', () {
    late ArticleService articleService;
    late ApiService apiService;
    late AppLogger logger;

    setUpAll(() async {
      // Reset singleton instance
      ArticleService.resetInstance();
      
      // Khởi tạo AppLogger đúng cách
      logger = AppLogger();
      await logger.initialize();
      
      // Khởi tạo ApiService với configuration đơn giản cho testing
      apiService = ApiService();
      await apiService.initialize(
        configuration: ApiConfiguration.forTesting(
          baseUrl: 'http://192.168.2.61:8190',
          enableLogging: false,
          enableDeviceHeaders: false,
          enableAuthToken: false,
        ),
      );
      
      // Khởi tạo service với default dependencies
      articleService = ArticleService();
    });

    tearDownAll(() {
      // Cleanup
      ArticleService.resetInstance();
    });

    test('should initialize service successfully', () {
      expect(articleService, isNotNull);
      expect(articleService, isA<ArticleService>());
    });

    test('should get available article types', () {
      final types = articleService.getAvailableArticleTypes();
      expect(types, isNotEmpty);
      expect(types.length, greaterThan(0));
    });

    test('should get article type display names', () {
      final types = articleService.getAvailableArticleTypes();
      
      for (final type in types) {
        final displayName = articleService.getArticleTypeDisplayName(type);
        expect(displayName, isNotEmpty);
        expect(displayName, isA<String>());
      }
    });

    test('should check API availability', () async {
      try {
        final isAvailable = await articleService.checkArticleApiAvailability();
        expect(isAvailable, isA<bool>());
      } catch (e) {
        // API có thể không available trong test environment
        expect(e, isA<ArticleException>());
      }
    });

    test('should handle getArticles with error gracefully', () async {
      try {
        final articles = await articleService.getArticles(limit: 1);

        expect(articles, isA<List<ArticleModel>>());
      } catch (e) {
        // Expected error nếu API không available
        expect(e, isA<ArticleException>());
      }
    });

    test('should handle getFeaturedArticles with error gracefully', () async {
      try {
        final articles = await articleService.getFeaturedArticles(limit: 1);
        expect(articles, isA<List<ArticleModel>>());
      } catch (e) {
        // Expected error nếu API không available
        expect(e, isA<ArticleException>());
      }
    });

    test('should handle invalid ID gracefully', () async {
      const invalidId = 'invalid-id-12345';
      
      try {
        await articleService.getArticleById(invalidId);
        fail('Should throw exception for invalid ID');
      } catch (e) {
        expect(e, isA<ArticleException>());
      }
    });

    test('should handle invalid code gracefully', () async {
      const invalidCode = 'INVALID-CODE-12345';
      
      try {
        await articleService.getArticleByCode(invalidCode);
        fail('Should throw exception for invalid code');
      } catch (e) {
        expect(e, isA<ArticleException>());
      }
    });

    test('should handle search by tags gracefully', () async {
      const tags = ['test', 'news'];
      
      try {
        final articles = await articleService.searchArticlesByTags(tags: tags);
        expect(articles, isA<List<ArticleModel>>());
      } catch (e) {
        // Expected error nếu API không available
        expect(e, isA<ArticleException>());
      }
    });

    test('should handle article versions gracefully', () async {
      const invalidId = 'invalid-id-12345';
      
      try {
        final versions = await articleService.getArticleVersions(invalidId);
        expect(versions, isA<List<ArticleVersionModel>>());
      } catch (e) {
        expect(e, isA<ArticleException>());
      }
    });
  });
} 