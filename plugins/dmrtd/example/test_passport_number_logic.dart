// Pure Dart test for passport number logic without Flutter dependencies

/// Calculate checksum for MRZ key components
/// Uses the standard ICAO 9303 checksum algorithm with weights [7, 3, 1]
/// Compatible with Swift PassportUtils implementation
int calculateChecksum(String input) {
  const characterDict = {
    '0': 0,
    '1': 1,
    '2': 2,
    '3': 3,
    '4': 4,
    '5': 5,
    '6': 6,
    '7': 7,
    '8': 8,
    '9': 9,
    '<': 0,
    ' ': 0,
    'A': 10,
    'B': 11,
    'C': 12,
    'D': 13,
    'E': 14,
    'F': 15,
    'G': 16,
    'H': 17,
    'I': 18,
    'J': 19,
    'K': 20,
    'L': 21,
    'M': 22,
    'N': 23,
    'O': 24,
    'P': 25,
    'Q': 26,
    'R': 27,
    'S': 28,
    'T': 29,
    'U': 30,
    'V': 31,
    'W': 32,
    'X': 33,
    'Y': 34,
    'Z': 35
  };

  const multipliers = [7, 3, 1];
  int sum = 0;
  int m = 0;

  for (int i = 0; i < input.length; i++) {
    final char = input[i];
    final value = characterDict[char] ?? 0;
    final product = value * multipliers[m];
    sum += product;
    m = (m + 1) % 3;
  }

  return sum % 10;
}

/// Pad field using Swift-like logic
/// Modified to take last fieldLength characters when value is longer
String padField(String value, int fieldLength) {
  // If value is longer than fieldLength, take the last fieldLength characters
  if (value.length >= fieldLength) {
    return value.substring(value.length - fieldLength);
  }
  // Otherwise, pad with < to reach fieldLength
  final paddedValue = (value +
          String.fromCharCodes(List.filled(fieldLength, '<'.codeUnitAt(0))))
      .substring(0, fieldLength);
  return paddedValue;
}

/// Create MRZ key with checksums for testing/debugging
/// Format: <passport number (9 chars)><passport checksum><date of birth (YYMMDD)><dob checksum><expiry date (YYMMDD)><expiry checksum>
String createMRZKey({
  required String passportNumber,
  required String dateOfBirth,
  required String dateOfExpiry,
}) {
  // Parse dates and convert to YYMMDD format
  final dobParts = dateOfBirth.split('/');
  final doeParts = dateOfExpiry.split('/');

  final dobYYMMDD =
      '${dobParts[2].substring(2)}${dobParts[1].padLeft(2, '0')}${dobParts[0].padLeft(2, '0')}';
  final doeYYMMDD =
      '${doeParts[2].substring(2)}${doeParts[1].padLeft(2, '0')}${doeParts[0].padLeft(2, '0')}';

  // Format passport number to 9 characters using Swift-like padding
  final formattedPassportNumber = padField(passportNumber, 9);

  // Calculate checksums
  final passportChecksum = calculateChecksum(formattedPassportNumber);
  final dobChecksum = calculateChecksum(dobYYMMDD);
  final doeChecksum = calculateChecksum(doeYYMMDD);

  return '$formattedPassportNumber$passportChecksum$dobYYMMDD$dobChecksum$doeYYMMDD$doeChecksum';
}

void main() {
  print('=== Testing Passport Number Logic ===\n');

  // Test cases with correct expected values for 9-character passport number
  final testCases = [
    {
      'passportNumber': '12345678',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'expected': '12345678<898012772508304', // 9 chars: 12345678<
    },
    {
      'passportNumber': '123456789012',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'expected': '*********98012772508304', // Last 9 digits: *********
    },
    {
      'passportNumber': '12345678901234567890',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'expected': '*********98012772508304', // Last 9 digits: *********
    },
    {
      'passportNumber': '12345',
      'dateOfBirth': '27/01/1998',
      'dateOfExpiry': '30/08/2025',
      'expected': '12345<<<<998012772508304', // Padded with < to 9 chars
    },
  ];

  for (int i = 0; i < testCases.length; i++) {
    final testCase = testCases[i];
    print('Test Case ${i + 1}:');
    print('  Passport Number: ${testCase['passportNumber']}');
    print('  Date of Birth: ${testCase['dateOfBirth']}');
    print('  Date of Expiry: ${testCase['dateOfExpiry']}');

    final mrzKey = createMRZKey(
      passportNumber: testCase['passportNumber'] as String,
      dateOfBirth: testCase['dateOfBirth'] as String,
      dateOfExpiry: testCase['dateOfExpiry'] as String,
    );

    print('  Generated MRZ Key: $mrzKey');
    print('  Expected MRZ Key: ${testCase['expected']}');
    print('  Match: ${mrzKey == testCase['expected'] ? '✅' : '❌'}');
    print('');
  }

  // Test checksum calculation with correct expected values
  print('=== Testing Checksum Calculation ===\n');

  final checksumTests = [
    {
      'input': '12345678<',
      'expected': 8
    }, // 1*7 + 2*3 + 3*1 + 4*7 + 5*3 + 6*1 + 7*7 + 8*3 + 0*1 = 7+6+3+28+15+6+49+24+0 = 138 % 10 = 8
    {
      'input': '*********',
      'expected': 6
    }, // 5*7 + 6*3 + 7*1 + 8*7 + 9*3 + 0*1 + 1*7 + 2*3 + 6*1 = 35+18+7+56+27+0+7+6+6 = 156 % 10 = 6
    {
      'input': '*********',
      'expected': 4
    }, // 3*7 + 4*3 + 5*1 + 6*7 + 7*3 + 8*1 + 9*7 + 0*3 + 2*1 = 21+12+5+42+21+8+63+0+2 = 174 % 10 = 4
    {
      'input': '12345<<<<',
      'expected': 9
    }, // 1*7 + 2*3 + 3*1 + 4*7 + 5*3 + 0*1 + 0*7 + 0*3 + 0*1 = 7+6+3+28+15+0+0+0+0 = 59 % 10 = 9
  ];

  for (int i = 0; i < checksumTests.length; i++) {
    final test = checksumTests[i];
    final checksum = calculateChecksum(test['input'] as String);
    print('Test ${i + 1}:');
    print('  Input: ${test['input']}');
    print('  Calculated Checksum: $checksum');
    print('  Expected Checksum: ${test['expected']}');
    print('  Match: ${checksum == test['expected'] ? '✅' : '❌'}');
    print('');
  }

  // Test passport number formatting logic
  print('=== Testing Passport Number Formatting ===\n');

  final formattingTests = [
    {'input': '12345678', 'expected': '12345678<'}, // 8 chars + 1 <
    {'input': '123456789012', 'expected': '*********'}, // Take last 9
    {'input': '12345678901234567890', 'expected': '*********'}, // Take last 9
    {'input': '12345', 'expected': '12345<<<<'}, // Pad with < to 9 chars
    {'input': '123', 'expected': '123<<<<<<'}, // Pad with < to 9 chars
  ];

  for (int i = 0; i < formattingTests.length; i++) {
    final test = formattingTests[i];
    final input = test['input'] as String;
    final expected = test['expected'] as String;

    final formatted = padField(input, 9);

    print('Test ${i + 1}:');
    print('  Input: $input');
    print('  Formatted: $formatted');
    print('  Expected: $expected');
    print('  Match: ${formatted == expected ? '✅' : '❌'}');
    print('');
  }

  // Test with real example from documentation
  print('=== Testing Real Example from Documentation ===\n');
  print('Documentation example:');
  print('  Passport nr: 12345678');
  print('  Date of birth: 27-Jan-1998');
  print('  Expiry: 30-Aug-2025');
  print('  Expected MRZ Key: 12345678<898012772508304');

  final realExample = createMRZKey(
    passportNumber: '12345678',
    dateOfBirth: '27/01/1998',
    dateOfExpiry: '30/08/2025',
  );

  print('  Generated MRZ Key: $realExample');
  print('  Match: ${realExample == '12345678<898012772508304' ? '✅' : '❌'}');

  // Debug the difference
  if (realExample != '12345678<898012772508304') {
    print('\nDebugging difference:');
    print('  Our result: $realExample');
    print('  Expected:   12345678<898012772508304');

    // Check each component
    final ourPassport = '12345678<';
    final ourPassportChecksum = calculateChecksum(ourPassport);
    final ourDOB = '980127';
    final ourDOBChecksum = calculateChecksum(ourDOB);
    final ourDOE = '250830';
    final ourDOEChecksum = calculateChecksum(ourDOE);

    print('  Our components:');
    print('    Passport: $ourPassport (checksum: $ourPassportChecksum)');
    print('    DOB: $ourDOB (checksum: $ourDOBChecksum)');
    print('    DOE: $ourDOE (checksum: $ourDOEChecksum)');

    // Expected components from documentation
    print('  Expected components:');
    print('    Passport: 12345678< (checksum: 8)');
    print('    DOB: 980127 (checksum: 7)');
    print('    DOE: 250830 (checksum: 5)');
  }
}
