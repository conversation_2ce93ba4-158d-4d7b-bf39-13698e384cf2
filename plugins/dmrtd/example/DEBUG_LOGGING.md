# Debug Logging cho iOS Native NFC Passport Reader

## Tổng quan

Đã thiết lập comprehensive debug logging cho iOS native code sử dụng `OSLog` framework. Logs được chia thành các categories khác nhau để dễ dàng filter và debug.

## Categories được thiết lập

### 1. **NFCPassportReader**
- Logs cho quá trình đọc passport chính
- Input parameters và kết quả
- Success/error handling

### 2. **MRZKeyGenerator** 
- Logs cho việc tạo MRZ key
- Date conversion (dd/MM/yyyy → YYMMDD)
- Passport number formatting
- Checksum calculation

### 3. **ChecksumCalculator**
- Detailed logs cho việc tính toán checksum
- Character-by-character calculation với weights [7, 3, 1]

### 4. **BAC (Basic Access Control)**
- Comprehensive BAC authentication logs
- MRZ key derivation và SHA-1 hashing
- Session key generation
- Mutual authentication process
- Encrypted data và MAC calculation

### 5. **PACE (Password Authenticated Connection Establishment)**
- PACE authentication flow
- Step-by-step process logging
- Key exchange và agreement
- Error handling với detailed OpenSSL errors

## Cách xem Debug Logs

### 1. **Xcode Console**
```bash
# Mở Xcode → Window → Devices and Simulators
# Chọn device → View Device Logs
# Filter logs với subsystem: "com.example.mrtdeg"
```

### 2. **Console App (macOS)**
```bash
# Mở Console app
# Kết nối iPhone và chọn device
# Tìm kiếm: "com.example.mrtdeg"
```

### 3. **Terminal với log command**
```bash
# Kết nối iPhone và chạy:
log stream --predicate 'subsystem == "com.example.mrtdeg"'

# Hoặc filter theo category:
log stream --predicate 'subsystem == "com.example.mrtdeg" AND category == "NFCPassportReader"'
```

### 4. **Xcode Debug Console**
- Chạy app trong Xcode
- Mở Debug Console (View → Debug Area → Activate Console)
- Logs sẽ hiển thị real-time

## Log Levels

- **Info** (📋): Thông tin chung, parameters, success messages
- **Debug** (🔢): Chi tiết kỹ thuật, calculations
- **Error** (❌): Lỗi và exceptions

## Emoji Indicators

- 🏷️ Tag detected
- 📋 Card access read
- 🔐 PACE/BAC started
- ✅ Success
- ❌ Failure
- 📅 Date conversion
- 📄 Passport formatting
- 🔢 Checksum calculation
- 🔑 MRZ key generation
- 📸 Face image available
- 🎲 Random values generation
- 🔗 Data concatenation
- 🔓 Decryption process
- 🔒 MAC calculation
- 📤 Command data
- 📥 Response data
- 🚀 Process start
- 🎉 Process completion
- 🔧 Parameters setup

## Ví dụ Log Output

```
=== Starting NFC Passport Reading ===
📋 Input parameters:
   Passport Number: 034077010170
   Date of Birth: 09/09/1977
   Date of Expiry: 09/09/2037
✅ Dates parsed successfully:
   DOB: 1977-09-09 00:00:00 +0000
   DOE: 2037-09-09 00:00:00 +0000
📅 Date conversion:
   DOB: 1977-09-09 00:00:00 +0000 → 770909
   DOE: 2037-09-09 00:00:00 +0000 → 370909
📄 Passport number formatting:
   Original: 034077010170
   Formatted: 034077010170
🔢 Checksum calculation:
   Passport checksum: 2
   DOB checksum: 2
   DOE checksum: 4
🔑 Final MRZ Key: 034077010170277090923709094
🔑 MRZ Key length: 27 characters
🚀 Starting passport reading with BAC...

🔐 BAC Authentication Started
📋 MRZ Key: 034077010170277090923709094
🔢 Generating Initial Kseed from MRZ
🔢 SHA-1 Hash: A1B2C3D4E5F6789012345678901234567890ABCD
🔢 Kseed (first 16 bytes): A1B2C3D4E5F678901234567890123456
🔑 Deriving Document Basic Access Keys
🔑 Derived Keys:
   Kenc: 1A2B3C4D5E6F78901234567890123456
   Kmac: 7A8B9C0D1E2F34567890123456789012
🏷️ Getting initial challenge from passport
📥 Received challenge: 1A2B3C4D5E6F7890
🔐 Starting mutual authentication
🔐 Building Mutual Authentication Command
🎲 Generated Random Values:
   RND.IFD: 2B3C4D5E6F789012
   Kifd: 3C4D5E6F789012345678901234567890
🔗 Concatenated Data (S): 2B3C4D5E6F7890121A2B3C4D5E6F78903C4D5E6F789012345678901234567890
🔐 Encrypted Data (Eifd): 4D5E6F7890123456789012345678901234567890123456789012345678901234
🔒 MAC (Mifd): 5E6F7890123456789012345678901234
📤 Final Command Data: 4D5E6F78901234567890123456789012345678901234567890123456789012345E6F7890123456789012345678901234
📥 Mutual auth response: 6F7890123456789012345678901234567890123456789012345678901234567890
🔑 Calculating session keys
🔑 Calculating Session Keys from Response
🔓 Decrypted Response: 7A8B9C0D1E2F3456789012345678901234567890123456789012345678901234
📥 Kicc from Response: 8B9C0D1E2F3456789012345678901234
🔢 XOR Result (Kseed): 9C0D1E2F345678901234567890123456
🔑 Session Keys Generated:
   KSenc: 0D1E2F34567890123456789012345678
   KSmac: 1E2F3456789012345678901234567890
🔢 Send Sequence Counter (SSC): 2F34567890123456
✅ BAC Authentication Completed Successfully

🏷️ NFCPassportReader: Tag detected
📋 NFCPassportReader: Read card access
🔐 NFCPassportReader: BAC started
✅ NFCPassportReader: BAC succeeded
✅ Passport reading completed successfully
✅ NFCPassportReader: Successfully read passport
📸 Face image available
📋 Passport data extracted successfully
   Document Number: 034077010170
   Date of Birth: 09/09/1977
   Date of Expiry: 09/09/2037
   Nationality: VNM
   Surname: NGUYEN
   Given Names: VAN MINH
```

## Troubleshooting

### Nếu không thấy logs:
1. Kiểm tra device đã được kết nối
2. Đảm bảo app đang chạy
3. Thử filter với subsystem khác: `com.example.mrtdeg`
4. Restart Console app hoặc Xcode

### Nếu logs bị tràn:
```bash
# Clear logs cũ:
log delete --predicate 'subsystem == "com.example.mrtdeg"'

# Hoặc clear tất cả logs:
log delete --all
```

## Performance Notes

- Debug logs được enable thông qua OSLog framework
- Production build sẽ tự động disable debug logs
- Info và Error logs luôn được giữ lại
- PassportReader library có built-in logging system riêng 