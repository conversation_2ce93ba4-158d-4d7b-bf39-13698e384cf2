// Test padding logic with the passport number from the log
void main() {
  print('=== Testing Padding Logic ===\n');

  final passportNumber = '034077010170';
  print('Original passport number: $passportNumber');
  print('Length: ${passportNumber.length}');

  // Test our padding logic
  final padded = _padField(passportNumber, 9);
  print('Padded (9 chars): $padded');
  print('Padded length: ${padded.length}');

  // Test what happens if we take last 8 characters
  final last8 = passportNumber.length > 8
      ? passportNumber.substring(passportNumber.length - 8)
      : passportNumber.padRight(8, '<').substring(0, 8);
  print('Last 8 chars: $last8');
  print('Last 8 length: ${last8.length}');

  // Test what happens if we take first 9 characters
  final first9 = passportNumber.length >= 9
      ? passportNumber.substring(0, 9)
      : passportNumber.padRight(9, '<').substring(0, 9);
  print('First 9 chars: $first9');
  print('First 9 length: ${first9.length}');

  // Test what the log shows: 77010170
  print('\nLog shows: 77010170');
  print('This looks like it might be taking characters from position 2-9');
  final fromPos2 = passportNumber.length >= 9
      ? passportNumber.substring(2, 11)
      : passportNumber.padRight(9, '<').substring(0, 9);
  print('From position 2: $fromPos2');
}

/// Pad field using Swift-like logic
String _padField(String value, int fieldLength) {
  if (value.length >= fieldLength) {
    return value.substring(0, fieldLength);
  }
  final paddedValue = (value +
          String.fromCharCodes(List.filled(fieldLength, '<'.codeUnitAt(0))))
      .substring(0, fieldLength);
  return paddedValue;
}
