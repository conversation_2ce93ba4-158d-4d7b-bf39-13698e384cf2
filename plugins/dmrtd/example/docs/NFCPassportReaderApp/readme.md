# NFCPassportReaderApp

This is a sample app showing how to read a e-passport using the NFCPassportReader library 


This is purely a demonstration - you have to manually enter the passport number, the date of birth and passport expiry date but it does calculate the checksums for you.

Also, the last entered passport details are saved to UserDefaults to save you having to re-enter them each time.

It reads my passport (and others I've been able to test) fine, however your milage may vary.
