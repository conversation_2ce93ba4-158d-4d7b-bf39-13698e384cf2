
-[NFCTagReaderSession _connectTag:error:]:748 Error Domain=NFCError Code=2 "Missing required entitlement" UserInfo={NSLocalizedDescription=Missing required entitlement}
-[NFCISO7816Tag queryNDEFStatusWithCompletionHandler:]:202 Tag is not connected
flutter: passport FINE: 2025-07-26 13:35:58.806045: Reading EF.CardAccess
flutter: passport FINE: 2025-07-26 13:35:58.809748: Selecting MF
flutter: mrtd.api FINE: 2025-07-26 13:35:58.811245: Selecting root Master File
flutter: icc FINE: 2025-07-26 13:35:58.813651: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:00 Le:0 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 13:35:58.816495: Sending 4 byte(s) to ICC: data='00a40000'
flutter: mrtdeg.app SEVERE: 2025-07-26 13:35:58.820045: An exception was encountered while trying to read Passport: NfcProviderError: PlatformException(500, Communication error, Session invalidated, null)
flutter: nfc.provider FINE: 2025-07-26 13:35:58.821369: Disconnecting
=== Starting NFC Passport Reading ===
📋 Input parameters:
   Passport Number: *********
   Date of Birth: 09/09/1977
   Date of Expiry: 09/09/2037
✅ Dates parsed successfully:
   DOB: 1977-09-08 17:00:00 +0000
   DOE: 2037-09-08 17:00:00 +0000
📅 Date conversion:
   DOB: 1977-09-08 17:00:00 +0000 → 770909
   DOE: 2037-09-08 17:00:00 +0000 → 370909
📄 Passport number formatting:
   Original: *********
   Formatted: *********
🔢 Calculating checksum for: '*********'
   char: '0', weight: 7, value: 0, running sum: 0
   char: '7', weight: 3, value: 7, running sum: 21
   char: '7', weight: 1, value: 7, running sum: 28
   char: '0', weight: 7, value: 0, running sum: 28
   char: '1', weight: 3, value: 1, running sum: 31
   char: '0', weight: 1, value: 0, running sum: 31
   char: '1', weight: 7, value: 1, running sum: 38
   char: '7', weight: 3, value: 7, running sum: 59
   char: '0', weight: 1, value: 0, running sum: 59
   Final checksum: 9
🔢 Calculating checksum for: '770909'
   char: '7', weight: 7, value: 7, running sum: 49
   char: '7', weight: 3, value: 7, running sum: 70
   char: '0', weight: 1, value: 0, running sum: 70
   char: '9', weight: 7, value: 9, running sum: 133
   char: '0', weight: 3, value: 0, running sum: 133
   char: '9', weight: 1, value: 9, running sum: 142
   Final checksum: 2
🔢 Calculating checksum for: '370909'
   char: '3', weight: 7, value: 3, running sum: 21
   char: '7', weight: 3, value: 7, running sum: 42
   char: '0', weight: 1, value: 0, running sum: 42
   char: '9', weight: 7, value: 9, running sum: 105
   char: '0', weight: 3, value: 0, running sum: 105
   char: '9', weight: 1, value: 9, running sum: 114
   Final checksum: 4
🔢 Checksum calculation:
   Passport checksum: 9
   DOB checksum: 2
   DOE checksum: 4
🔑 Final MRZ Key: *********977090923709094
🔑 MRZ Key length: 24 characters
🔑 Generated MRZ Key: *********977090923709094
🔑 MRZ Key length: 24 characters
🚀 Starting passport reading with BAC...
tagReaderSessionDidBecomeActive

*********977090923709094
*********977090923709094
*********977090923709094
*********977090923709094




tagReaderSessionDidBecomeActive
tagReaderSession:didDetect - found [CoreNFC.NFCTag.iso7816(<NFCISO7816Tag: 0x11a628940>)]
-[NFCTagReaderSession _connectTag:error:]:748 Error Domain=NFCError Code=2 "Missing required entitlement" UserInfo={NSLocalizedDescription=Missing required entitlement}
tagReaderSession:connected to tag - starting authentication
🏷️ NFCPassportReader: Tag detected
🔐 NFCPassportReader: PACE started
TagReader - sending [0x00, 0xA4, 0x00, 0x0C, 0x02, 0x3F, 0x00]
❌ NFCPassportReader: PACE failed
PACE Failed - falling back to BAC
Re-selecting eMRTD Application
TagReader - sending [0x00, 0xA4, 0x04, 0x0C, 0x07, 0xA0, 0x00, 0x00, 0x02, 0x47, 0x10, 0x01]
tagReaderSession:failed to connect to tag - Session invalidated



tagReaderSessionDidBecomeActive
tagReaderSession:didDetect - found [CoreNFC.NFCTag.iso7816(<NFCISO7816Tag: 0x1156d3ac0>)]
tagReaderSession:connected to tag - starting authentication
-[NFCTagReaderSession setAlertMessage:]:101 (null)
TagReader - sending [0x00, 0xA4, 0x00, 0x0C, 0x02, 0x3F, 0x00]
TagReader - Received response, size 0b
TagReader [unprotected] [], sw1:0x6d sw2:0x00
TagReader - sending [0x00, 0xA4, 0x02, 0x0C, 0x02, 0x01, 0x1C]
TagReader - Received response, size 0b
TagReader [unprotected] [], sw1:0x69 sw2:0x82
Error reading tag: sw1 - 0x69, sw2 - 0x82
reason: Security status not satisfied
PACE Failed - falling back to BAC
Re-selecting eMRTD Application
TagReader - sending [0x00, 0xA4, 0x04, 0x0C, 0x07, 0xA0, 0x00, 0x00, 0x02, 0x47, 0x10, 0x01]
TagReader - Received response, size 0b
TagReader [unprotected] [], sw1:0x90 sw2:0x00
Starting Basic Access Control (BAC)
BACHandler - deriving Document Basic Access Keys
Calculate the SHA-1 hash of MRZ_information
	MRZ KEY - *********977090923709094
	sha1(MRZ_information): B8FAC257F6D4F6F973C33248661A03E0EEC55629
Take the most significant 16 bytes to form the Kseed
	Kseed: B8FAC257F6D4F6F973C33248661A03E0
Calculate the Basic Access Keys (Kenc and Kmac) using TR-SAC 1.01, 4.2
BACHandler - Getting initial challenge
TagReader - sending [0x00, 0x84, 0x00, 0x00, 0x08]
TagReader - Received response, size 8b
TagReader [unprotected] [0x44, 0x3a, 0x42, 0x7c, 0x20, 0xaa, 0xba, 0xc7, ], sw1:0x90 sw2:0x00
DATA - [68, 58, 66, 124, 32, 170, 186, 199]