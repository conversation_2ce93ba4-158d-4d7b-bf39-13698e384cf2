flutter: <PERSON><PERSON> pressed
flutter: passport FINE: 2025-07-26 14:59:15.305457: Reading EF.CardAccess
flutter: passport FINE: 2025-07-26 14:59:15.308750: Selecting MF
flutter: mrtd.api FINE: 2025-07-26 14:59:15.310166: Selecting root Master File
flutter: icc FINE: 2025-07-26 14:59:15.312160: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:00 Le:0 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:59:15.314430: Sending 4 byte(s) to ICC: data='00a40000'
-[NFCTagReaderSession setAlertMessage:]:101 (null)
flutter: icc FINE: 2025-07-26 14:59:15.358885: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:59:15.359198:  data='6d00'
flutter: icc FINE: 2025-07-26 14:59:15.359726: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:59:15.359756:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:59:15.361461: Couldn't select MF by P1: 0, P2: 0 sw=sw=6D00, re-trying to select MF with FileID=3F00
flutter: icc FINE: 2025-07-26 14:59:15.361519: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:00 Le:0 Lc:2 Data:3f00)
flutter: icc FINE: 2025-07-26 14:59:15.361715: Sending 7 byte(s) to ICC: data='00a40000023f00'
flutter: icc FINE: 2025-07-26 14:59:15.370593: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:59:15.370627:  data='6d00'
flutter: icc FINE: 2025-07-26 14:59:15.370652: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:59:15.370669:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:59:15.370800: Couldn't select MF by P1=0, P2=0, FileID=3F00 sw=sw=6D00, re-trying to select MF with P2=0x0C and FileID=3F00
flutter: icc FINE: 2025-07-26 14:59:15.370901: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:0C Le:0 Lc:2 Data:3f00)
flutter: icc FINE: 2025-07-26 14:59:15.370937: Sending 7 byte(s) to ICC: data='00a4000c023f00'
flutter: icc FINE: 2025-07-26 14:59:15.379351: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:59:15.379380:  data='6d00'
flutter: icc FINE: 2025-07-26 14:59:15.379401: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:59:15.379416:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:59:15.379548: Couldn't select MF by P1=0, P2=0x0C, FileID=3F00 sw=sw=6D00, re-trying to select MF with P2=0x0C
flutter: icc FINE: 2025-07-26 14:59:15.379580: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:0C Le:0 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:59:15.379607: Sending 4 byte(s) to ICC: data='00a4000c'
flutter: icc FINE: 2025-07-26 14:59:15.387835: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:59:15.387863:  data='6d00'
flutter: icc FINE: 2025-07-26 14:59:15.387885: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:59:15.387903:  data=null
flutter: passport FINE: 2025-07-26 14:59:15.388572: Starting session
flutter: passport FINE: 2025-07-26 14:59:15.388669: Selecting DF1
flutter: mrtd.api FINE: 2025-07-26 14:59:15.388791: Selecting eMRTD application
flutter: icc FINE: 2025-07-26 14:59:15.389601: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:04 P2:0C Le:0 Lc:7 Data:a0000002471001)
flutter: icc FINE: 2025-07-26 14:59:15.389644: Sending 12 byte(s) to ICC: data='00a4040c07a0000002471001'
-[NFCTagReaderSession setAlertMessage:]:101 (null)
-[NFCTagReaderSession setAlertMessage:]:101 (null)
flutter: icc FINE: 2025-07-26 14:59:15.400911: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:59:15.400947:  data='9000'
flutter: icc FINE: 2025-07-26 14:59:15.400971: Received response from ICC: sw=9000 data_len=0
flutter: icc FINE: 2025-07-26 14:59:15.400990:  data=null
flutter: mrtd.api FINE: 2025-07-26 14:59:15.401256: Initiating SM session using BAC protocol
flutter: bac FINER: 2025-07-26 14:59:15.408019: Key seed=552831dae4cfff8019f9a1123dd8299a
flutter: bac FINER: 2025-07-26 14:59:15.408063: Derived Kenc=5d436ce9c6200f8d3eac0ed08a428efa5d436ce9c6200f8d
flutter: bac FINER: 2025-07-26 14:59:15.408085: Derived Kmac=c374b85b0d88c7cf469b44b21a74f9b3c374b85b0d88c7cf
flutter: bac FINE: 2025-07-26 14:59:15.408108: Requesting challenge from ICC
flutter: icc FINE: 2025-07-26 14:59:15.408219: Transceiving to ICC: C-APDU(CLA:00 INS:84 P1:00 P2:00 Le:8 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:59:15.408262: Sending 5 byte(s) to ICC: data='0084000008'
flutter: icc FINE: 2025-07-26 14:59:15.418392: Received 10 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:59:15.418493:  data='8d2cdadd22a8cc2c9000'
flutter: icc FINE: 2025-07-26 14:59:15.419272: Received response from ICC: sw=9000 data_len=8
flutter: icc FINE: 2025-07-26 14:59:15.419309:  data=8d2cdadd22a8cc2c
flutter: bac FINER: 2025-07-26 14:59:15.419432: Received RND.IC=8d2cdadd22a8cc2c
flutter: bac FINER: 2025-07-26 14:59:15.420657: Generated RND.IFD=7a27cc1b339164eb
flutter: bac FINER: 2025-07-26 14:59:15.420688: Generated K.IFD=44bbd93e36dc8b40e2ca82d21f3000a9
flutter: bac FINER: 2025-07-26 14:59:15.421058: Generated S=7a27cc1b339164eb8d2cdadd22a8cc2c44bbd93e36dc8b40e2ca82d21f3000a9
flutter: ISO9797 FINER: 2025-07-26 14:59:15.426408: Data: ddf12c8b917e819bff095ff8a40fc41009c1736007b035293d1c8cb046f5ac10, size: 32, n: 8
flutter: ISO9797 FINER: 2025-07-26 14:59:15.426478: Padded data: ddf12c8b917e819bff095ff8a40fc41009c1736007b035293d1c8cb046f5ac108000000000000000, size: 40
flutter: bac FINE: 2025-07-26 14:59:15.426992: Sending EXTERNAL AUTHENTICATE command
flutter: bac FINER: 2025-07-26 14:59:15.427016:   Eifd=ddf12c8b917e819bff095ff8a40fc41009c1736007b035293d1c8cb046f5ac10
flutter: bac FINER: 2025-07-26 14:59:15.427031:   Mifd=caa0a213348077fb
flutter: icc FINE: 2025-07-26 14:59:15.427188: Transceiving to ICC: C-APDU(CLA:00 INS:82 P1:00 P2:00 Le:40 Lc:40 Data:ddf12c8b917e819bff095ff8a40fc41009c1736007b035293d1c8cb046f5ac10caa0a213348077fb)
flutter: icc FINE: 2025-07-26 14:59:15.427230: Sending 46 byte(s) to ICC: data='0082000028ddf12c8b917e819bff095ff8a40fc41009c1736007b035293d1c8cb046f5ac10caa0a213348077fb28'
flutter: icc FINE: 2025-07-26 14:59:15.443680: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:59:15.443765:  data='6300'
flutter: icc FINE: 2025-07-26 14:59:15.443798: Received response from ICC: sw=6300 data_len=0
flutter: icc FINE: 2025-07-26 14:59:15.443813:  data=null
flutter: mrtdeg.app SEVERE: 2025-07-26 14:59:15.444267: PassportError: The protocol (step) failed.
flutter: nfc.provider FINE: 2025-07-26 14:59:15.444448: Disconnecting