flutter: <PERSON><PERSON> pressed
flutter: passport FINE: 2025-07-26 14:51:22.586580: Reading EF.CardAccess
flutter: passport FINE: 2025-07-26 14:51:22.590244: Selecting MF
flutter: mrtd.api FINE: 2025-07-26 14:51:22.591419: Selecting root Master File
flutter: icc FINE: 2025-07-26 14:51:22.593708: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:00 Le:0 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:51:22.596912: Sending 4 byte(s) to ICC: data='00a40000'
-[NFCTagReaderSession setAlertMessage:]:101 (null)
flutter: icc FINE: 2025-07-26 14:51:22.648781: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:51:22.649252:  data='6d00'
flutter: icc FINE: 2025-07-26 14:51:22.649960: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:51:22.649992:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:51:22.651511: Couldn't select MF by P1: 0, P2: 0 sw=sw=6D00, re-trying to select MF with FileID=3F00
flutter: icc FINE: 2025-07-26 14:51:22.651562: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:00 Le:0 Lc:2 Data:3f00)
flutter: icc FINE: 2025-07-26 14:51:22.651860: Sending 7 byte(s) to ICC: data='00a40000023f00'
flutter: icc FINE: 2025-07-26 14:51:22.662488: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:51:22.662527:  data='6d00'
flutter: icc FINE: 2025-07-26 14:51:22.662550: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:51:22.662563:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:51:22.662689: Couldn't select MF by P1=0, P2=0, FileID=3F00 sw=sw=6D00, re-trying to select MF with P2=0x0C and FileID=3F00
flutter: icc FINE: 2025-07-26 14:51:22.662783: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:0C Le:0 Lc:2 Data:3f00)
flutter: icc FINE: 2025-07-26 14:51:22.662814: Sending 7 byte(s) to ICC: data='00a4000c023f00'
flutter: icc FINE: 2025-07-26 14:51:22.673204: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:51:22.673226:  data='6d00'
flutter: icc FINE: 2025-07-26 14:51:22.673242: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:51:22.673254:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:51:22.673362: Couldn't select MF by P1=0, P2=0x0C, FileID=3F00 sw=sw=6D00, re-trying to select MF with P2=0x0C
flutter: icc FINE: 2025-07-26 14:51:22.673385: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:0C Le:0 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:51:22.673404: Sending 4 byte(s) to ICC: data='00a4000c'
flutter: icc FINE: 2025-07-26 14:51:22.683439: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:51:22.683459:  data='6d00'
flutter: icc FINE: 2025-07-26 14:51:22.683474: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:51:22.683487:  data=null
flutter: passport FINE: 2025-07-26 14:51:22.683980: Starting session
flutter: passport FINE: 2025-07-26 14:51:22.684048: Selecting DF1
flutter: mrtd.api FINE: 2025-07-26 14:51:22.684137: Selecting eMRTD application
flutter: icc FINE: 2025-07-26 14:51:22.684716: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:04 P2:0C Le:0 Lc:7 Data:a0000002471001)
flutter: icc FINE: 2025-07-26 14:51:22.684752: Sending 12 byte(s) to ICC: data='00a4040c07a0000002471001'
-[NFCTagReaderSession setAlertMessage:]:101 (null)
-[NFCTagReaderSession setAlertMessage:]:101 (null)
flutter: icc FINE: 2025-07-26 14:51:22.699172: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:51:22.699212:  data='9000'
flutter: icc FINE: 2025-07-26 14:51:22.699230: Received response from ICC: sw=9000 data_len=0
flutter: icc FINE: 2025-07-26 14:51:22.699243:  data=null
flutter: mrtd.api FINE: 2025-07-26 14:51:22.699442: Initiating SM session using BAC protocol
flutter: bac FINER: 2025-07-26 14:51:22.704229: Key seed=552831dae4cfff8019f9a1123dd8299a
flutter: bac FINER: 2025-07-26 14:51:22.704263: Derived Kenc=5d436ce9c6200f8d3eac0ed08a428efa
flutter: bac FINER: 2025-07-26 14:51:22.704280: Derived Kmac=c374b85b0d88c7cf469b44b21a74f9b3c374b85b0d88c7cf
flutter: bac FINE: 2025-07-26 14:51:22.704298: Requesting challenge from ICC
flutter: icc FINE: 2025-07-26 14:51:22.704378: Transceiving to ICC: C-APDU(CLA:00 INS:84 P1:00 P2:00 Le:8 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:51:22.704413: Sending 5 byte(s) to ICC: data='0084000008'
flutter: icc FINE: 2025-07-26 14:51:22.718241: Received 10 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:51:22.718337:  data='1bf394c0fafefe189000'
flutter: icc FINE: 2025-07-26 14:51:22.719697: Received response from ICC: sw=9000 data_len=8
flutter: icc FINE: 2025-07-26 14:51:22.719727:  data=1bf394c0fafefe18
flutter: bac FINER: 2025-07-26 14:51:22.719886: Received RND.IC=1bf394c0fafefe18
flutter: bac FINER: 2025-07-26 14:51:22.721189: Generated RND.IFD=17da1721b808c669
flutter: bac FINER: 2025-07-26 14:51:22.721208: Generated K.IFD=66a9f04bf48871bb6bdbbd9e139a8192
flutter: bac FINER: 2025-07-26 14:51:22.721444: Generated S=17da1721b808c6691bf394c0fafefe1866a9f04bf48871bb6bdbbd9e139a8192
flutter: nfc.provider FINE: 2025-07-26 14:51:22.725489: Disconnecting
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: 'package:dmrtd/src/proto/bac.dart': Failed assertion: line 206 pos 12: 'Kmac.length == ISO9797.macAlg3_Key1Len': is not true.
#0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:67:4)
#1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:49:5)
#2      BAC.MAC (package:dmrtd/src/proto/bac.dart:206:12)
#3      BAC.initSession (package:dmrtd/src/proto/bac.dart:73:18)
<asynchronous suspension>
#4      MrtdApi.initSessionViaBAC (package:dmrtd/src/proto/mrtd_api.dart:65:5)
<asynchronous suspension>
#5      Passport._exec (package:dmrtd/src/passport.dart:428:14)
<asynchronous suspension>
#6      Passport.startSession (package:dmrtd/src/passport.dart:50:5)
<asynchronous suspension>
#7      _MrtdHomePageState._readMRTD (package:mrtdeg/main.dart:434:11)