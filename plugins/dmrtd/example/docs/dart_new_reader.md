lutter: <PERSON><PERSON> pressed
flutter: passport FINE: 2025-07-26 14:47:50.072377: Reading EF.CardAccess
flutter: passport FINE: 2025-07-26 14:47:50.076160: Selecting MF
flutter: mrtd.api FINE: 2025-07-26 14:47:50.077341: Selecting root Master File
flutter: icc FINE: 2025-07-26 14:47:50.079569: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:00 Le:0 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:47:50.082734: Sending 4 byte(s) to ICC: data='00a40000'
-[NFCTagReaderSession setAlertMessage:]:101 (null)
flutter: icc FINE: 2025-07-26 14:47:50.142186: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:47:50.142460:  data='6d00'
flutter: icc FINE: 2025-07-26 14:47:50.142889: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:47:50.142914:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:47:50.143851: Couldn't select MF by P1: 0, P2: 0 sw=sw=6D00, re-trying to select MF with FileID=3F00
flutter: icc FINE: 2025-07-26 14:47:50.143902: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:00 Le:0 Lc:2 Data:3f00)
flutter: icc FINE: 2025-07-26 14:47:50.144110: Sending 7 byte(s) to ICC: data='00a40000023f00'
flutter: icc FINE: 2025-07-26 14:47:50.154821: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:47:50.154854:  data='6d00'
flutter: icc FINE: 2025-07-26 14:47:50.154875: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:47:50.154889:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:47:50.155: Couldn't select MF by P1=0, P2=0, FileID=3F00 sw=sw=6D00, re-trying to select MF with P2=0x0C and FileID=3F00
flutter: icc FINE: 2025-07-26 14:47:50.155082: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:0C Le:0 Lc:2 Data:3f00)
flutter: icc FINE: 2025-07-26 14:47:50.155119: Sending 7 byte(s) to ICC: data='00a4000c023f00'
flutter: icc FINE: 2025-07-26 14:47:50.184482: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:47:50.184553:  data='6d00'
flutter: icc FINE: 2025-07-26 14:47:50.184575: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:47:50.184587:  data=null
flutter: mrtd.api WARNING: 2025-07-26 14:47:50.184727: Couldn't select MF by P1=0, P2=0x0C, FileID=3F00 sw=sw=6D00, re-trying to select MF with P2=0x0C
flutter: icc FINE: 2025-07-26 14:47:50.184753: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:00 P2:0C Le:0 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:47:50.184781: Sending 4 byte(s) to ICC: data='00a4000c'
flutter: icc FINE: 2025-07-26 14:47:50.194535: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:47:50.194573:  data='6d00'
flutter: icc FINE: 2025-07-26 14:47:50.194595: Received response from ICC: sw=6D00 data_len=0
flutter: icc FINE: 2025-07-26 14:47:50.194608:  data=null
flutter: passport FINE: 2025-07-26 14:47:50.195105: Starting session
flutter: passport FINE: 2025-07-26 14:47:50.195171: Selecting DF1
flutter: mrtd.api FINE: 2025-07-26 14:47:50.195249: Selecting eMRTD application
flutter: icc FINE: 2025-07-26 14:47:50.195739: Transceiving to ICC: C-APDU(CLA:00 INS:A4 P1:04 P2:0C Le:0 Lc:7 Data:a0000002471001)
flutter: icc FINE: 2025-07-26 14:47:50.195777: Sending 12 byte(s) to ICC: data='00a4040c07a0000002471001'
-[NFCTagReaderSession setAlertMessage:]:101 (null)
-[NFCTagReaderSession setAlertMessage:]:101 (null)
flutter: icc FINE: 2025-07-26 14:47:50.208581: Received 2 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:47:50.208661:  data='9000'
flutter: icc FINE: 2025-07-26 14:47:50.208688: Received response from ICC: sw=9000 data_len=0
flutter: icc FINE: 2025-07-26 14:47:50.208701:  data=null
flutter: mrtd.api FINE: 2025-07-26 14:47:50.208913: Initiating SM session using BAC protocol
flutter: bac FINER: 2025-07-26 14:47:50.212172: Key seed=552831dae4cfff8019f9a1123dd8299a
flutter: bac FINER: 2025-07-26 14:47:50.212192: Derived Kenc=5d436ce9c6200f8d3eac0ed08a428efa5d436ce9c6200f8d
flutter: bac FINER: 2025-07-26 14:47:50.212203: Derived Kmac=c374b85b0d88c7cf469b44b21a74f9b3c374b85b0d88c7cf
flutter: bac FINE: 2025-07-26 14:47:50.212211: Requesting challenge from ICC
flutter: icc FINE: 2025-07-26 14:47:50.212269: Transceiving to ICC: C-APDU(CLA:00 INS:84 P1:00 P2:00 Le:8 Lc:0 Data:null)
flutter: icc FINE: 2025-07-26 14:47:50.212298: Sending 5 byte(s) to ICC: data='0084000008'
flutter: icc FINE: 2025-07-26 14:47:50.222866: Received 10 byte(s) from ICC
flutter: icc FINE: 2025-07-26 14:47:50.222892:  data='4057dafbf79730f49000'
flutter: icc FINE: 2025-07-26 14:47:50.224328: Received response from ICC: sw=9000 data_len=8
flutter: icc FINE: 2025-07-26 14:47:50.224349:  data=4057dafbf79730f4
flutter: bac FINER: 2025-07-26 14:47:50.224412: Received RND.IC=4057dafbf79730f4
flutter: bac FINER: 2025-07-26 14:47:50.225134: Generated RND.IFD=1ec3c52bf631605e
flutter: bac FINER: 2025-07-26 14:47:50.225152: Generated K.IFD=d7653105bd5de379c10979a000a399ea
flutter: bac FINER: 2025-07-26 14:47:50.225337: Generated S=1ec3c52bf631605e4057dafbf79730f4d7653105bd5de379c10979a000a399ea
flutter: nfc.provider FINE: 2025-07-26 14:47:50.225559: Disconnecting
[ERROR:flutter/runtime/dart_vm_initializer.cc(40)] Unhandled Exception: 'package:dmrtd/src/proto/bac.dart': Failed assertion: line 184 pos 12: 'Kenc.length == kLen': is not true.
#0      _AssertionError._doThrowNew (dart:core-patch/errors_patch.dart:67:4)
#1      _AssertionError._throwNew (dart:core-patch/errors_patch.dart:49:5)
#2      BAC.E (package:dmrtd/src/proto/bac.dart:184:12)
#3      BAC.initSession (package:dmrtd/src/proto/bac.dart:72:18)
<asynchronous suspension>
#4      MrtdApi.initSessionViaBAC (package:dmrtd/src/proto/mrtd_api.dart:65:5)
<asynchronous suspension>
#5      Passport._exec (package:dmrtd/src/passport.dart:428:14)
<asynchronous suspension>
#6      Passport.startSession (package:dmrtd/src/passport.dart:50:5)
<asynchronous suspension>
#7      _MrtdHomePageState._readMRTD (package:mrtdeg/main.dart:434:11)
<asynchronous suspension>