// Test Swift-like logic for passport number formatting
void main() {
  print('=== Testing Swift-like Logic ===\n');

  final passportNumber = '034077010170';
  print('Original passport number: $passportNumber');
  print('Length: ${passportNumber.length}');

  // Swift-like padding logic
  final formatted = padField(passportNumber, 9);
  print('Swift-like formatted (9 chars): $formatted');
  print('Formatted length: ${formatted.length}');

  // Calculate checksum
  final checksum = calculateChecksum(formatted);
  print('Checksum: $checksum');

  // Test with dates from the log
  final dobString = '770909'; // 09/09/1977
  final doeString = '370909'; // 09/09/2037

  final dobChecksum = calculateChecksum(dobString);
  final doeChecksum = calculateChecksum(doeString);

  print('DOB: $dobString (checksum: $dobChecksum)');
  print('DOE: $doeString (checksum: $doeChecksum)');

  // Create MRZ key
  final mrzKey =
      '$formatted$checksum$dobString$dobChecksum$doeString$doeChecksum';
  print('\nGenerated MRZ Key: $mrzKey');
  print('MRZ Key length: ${mrzKey.length} characters');

  // Compare with log output
  print('\nLog shows: 77010170777090923709094');
  print('Our result: $mrzKey');
  print('Match: ${mrzKey == '77010170777090923709094' ? '✅' : '❌'}');
}

/// Swift-like padding logic
String padField(String value, int fieldLength) {
  // Swift logic: (value + String(repeating: "<", count: fieldLength)).prefix(fieldLength)
  // This means: append fieldLength number of "<" to value, then take first fieldLength characters
  final paddedValue = (value +
          String.fromCharCodes(List.filled(fieldLength, '<'.codeUnitAt(0))))
      .substring(0, fieldLength);
  return paddedValue;
}

/// Calculate checksum using ICAO 9303 algorithm
int calculateChecksum(String input) {
  const weights = [7, 3, 1];
  int sum = 0;

  for (int i = 0; i < input.length; i++) {
    final char = input[i];
    final weight = weights[i % 3];
    int value;

    if (char == '<' || char == ' ') {
      value = 0;
    } else if (RegExp(r'[0-9]').hasMatch(char)) {
      value = int.parse(char);
    } else {
      // A=10, B=11, etc.
      value = char.codeUnitAt(0) - 55;
    }

    sum += value * weight;
  }

  return sum % 10;
}
