// Created by <PERSON><PERSON>, copyright © 2022 ZeroPass. All rights reserved.
import 'dart:typed_data';
import 'package:crypto/crypto.dart';

/// Swift-style key derivation function that matches the NFCPassportReader implementation
class SwiftStyleKDF {
  /// Returns key for DESede derived from [keySeed] bytes using Swift-style derivation
  static Uint8List desEDE(Uint8List keySeed) {
    return _deriveSwiftStyle(keySeed, 1); // ENC mode = 1
  }

  /// Returns key for ISO9797 MAC algorithm 3 derived from [keySeed] bytes using Swift-style derivation
  static Uint8List iso9797MacAlg3(Uint8List keySeed) {
    return _deriveSwiftStyle(keySeed, 2); // MAC mode = 2
  }

  /// Swift-style key derivation: concatenate keySeed + [0x00, 0x00, 0x00, mode] and hash
  static Uint8List _deriveSwiftStyle(Uint8List keySeed, int mode) {
    // Create mode array: [0x00, 0x00, 0x00, mode]
    final modeArray = Uint8List.fromList([0x00, 0x00, 0x00, mode]);

    // Concatenate keySeed + modeArray
    final data = Uint8List.fromList(keySeed + modeArray);

    // Hash with SHA-1
    final hash = sha1.convert(data);

    // For DESede: [hashResult[0..<16] + hashResult[0..<8]]
    final key = Uint8List.fromList(
        hash.bytes.sublist(0, 16) + hash.bytes.sublist(0, 8));

    return key;
  }

  /// Swift-style key derivation for DESede (returns 16 bytes for compatibility)
  static Uint8List _deriveSwiftStyleDESede(Uint8List keySeed, int mode) {
    // Create mode array: [0x00, 0x00, 0x00, mode]
    final modeArray = Uint8List.fromList([0x00, 0x00, 0x00, mode]);

    // Concatenate keySeed + modeArray
    final data = Uint8List.fromList(keySeed + modeArray);

    // Hash with SHA-1
    final hash = sha1.convert(data);

    // For DESede compatibility: return only first 16 bytes
    return Uint8List.fromList(hash.bytes.sublist(0, 16));
  }

  /// Swift-style key derivation for ISO9797 MAC (returns 16 bytes for compatibility)
  static Uint8List _deriveSwiftStyleMAC(Uint8List keySeed, int mode) {
    // Create mode array: [0x00, 0x00, 0x00, mode]
    final modeArray = Uint8List.fromList([0x00, 0x00, 0x00, mode]);

    // Concatenate keySeed + modeArray
    final data = Uint8List.fromList(keySeed + modeArray);

    // Hash with SHA-1
    final hash = sha1.convert(data);

    // For ISO9797 MAC compatibility: return only first 16 bytes
    return Uint8List.fromList(hash.bytes.sublist(0, 16));
  }
}
