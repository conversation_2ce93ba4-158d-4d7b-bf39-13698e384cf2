// Created by <PERSON><PERSON>, copyright © 2022 ZeroPass. All rights reserved.
import 'dart:io';
import 'dart:typed_data';
import 'kdf.dart';
import 'swift_style_kdf.dart';

/// Platform-specific key derivation function
/// Uses Swift-style KDF on iOS and Dart-style KDF on Android
class PlatformKDF {
  /// Returns key for DESede derived from [keySeed] bytes using platform-specific derivation
  static Uint8List desEDE(Uint8List keySeed) {
    if (Platform.isIOS) {
      // Use Swift-style KDF on iOS
      return SwiftStyleKDF.desEDE(keySeed);
    } else {
      // Use Dart-style KDF on Android and other platforms
      return DeriveKey.desEDE(keySeed);
    }
  }

  /// Returns key for ISO9797 MAC algorithm 3 derived from [keySeed] bytes using platform-specific derivation
  static Uint8List iso9797MacAlg3(Uint8List keySeed) {
    if (Platform.isIOS) {
      // Use Swift-style KDF on iOS
      return SwiftStyleKDF.iso9797MacAlg3(keySeed);
    } else {
      // Use Dart-style KDF on Android and other platforms
      return DeriveKey.iso9797MacAlg3(keySeed);
    }
  }
}
