# Sự khác biệt giữa Android và iOS trong NFC Passport Reading

## Tổng quan

Tài liệu này mô tả các khác biệt quan trọng giữa Android và iOS trong việc đọc NFC passport và cách chúng ta đã giải quyết để đảm bảo tương thích trên cả hai nền tảng.

## 🔍 Khác biệt chính

### 1. Key Derivation Function (KDF)

**Android (Dart-style KDF):**
- Sử dụng KDF counter mode
- Tr<PERSON> về 16 bytes cho Kenc và Kmac
- Thuật toán: SHA-1 hash với counter mode

**iOS (Swift-style KDF):**
- Sử dụng concatenation + SHA-1 + specific slicing
- Tr<PERSON> về 24 bytes cho Kenc và Kmac
- Thuật toán: `keySeed + [0x00, 0x00, 0x00, mode]` → SHA-1 → `[hash[0..16] + hash[0..8]]`

### 2. Key Length

| Platform | Kenc Length | Kmac Length |
|----------|-------------|-------------|
| Android  | 16 bytes    | 16 bytes    |
| iOS      | 24 bytes    | 24 bytes    |

### 3. MRZ Key Format

**Android:**
- Có thể sử dụng toàn bộ passport number
- Format linh hoạt hơn

**iOS:**
- Chỉ sử dụng 9 ký tự cuối của passport number
- Format nghiêm ngặt theo chuẩn ePassport

## 🛠️ Giải pháp đã triển khai

### 1. Swift-style KDF Implementation

**File:** `lib/src/crypto/swift_style_kdf.dart`

```dart
class SwiftStyleKDF {
  /// Returns key for DESede derived from [keySeed] bytes using Swift-style derivation
  static Uint8List desEDE(Uint8List keySeed) {
    return _deriveSwiftStyle(keySeed, 1); // ENC mode = 1
  }

  /// Returns key for ISO9797 MAC algorithm 3 derived from [keySeed] bytes using Swift-style derivation
  static Uint8List iso9797MacAlg3(Uint8List keySeed) {
    return _deriveSwiftStyle(keySeed, 2); // MAC mode = 2
  }

  /// Swift-style key derivation: concatenate keySeed + [0x00, 0x00, 0x00, mode] and hash
  static Uint8List _deriveSwiftStyle(Uint8List keySeed, int mode) {
    // Create mode array: [0x00, 0x00, 0x00, mode]
    final modeArray = Uint8List.fromList([0x00, 0x00, 0x00, mode]);
    
    // Concatenate keySeed + modeArray
    final data = Uint8List.fromList(keySeed + modeArray);
    
    // Hash with SHA-1
    final hash = sha1.convert(data);
    
    // For DESede: [hashResult[0..<16] + hashResult[0..<8]]
    final key = Uint8List.fromList(hash.bytes.sublist(0, 16) + hash.bytes.sublist(0, 8));
    
    return key;
  }
}
```

### 2. Platform-specific KDF

**File:** `lib/src/crypto/platform_kdf.dart`

```dart
class PlatformKDF {
  /// Returns key for DESede derived from [keySeed] bytes using platform-specific derivation
  static Uint8List desEDE(Uint8List keySeed) {
    if (Platform.isIOS) {
      // Use Swift-style KDF on iOS
      return SwiftStyleKDF.desEDE(keySeed);
    } else {
      // Use Dart-style KDF on Android and other platforms
      return DeriveKey.desEDE(keySeed);
    }
  }

  /// Returns key for ISO9797 MAC algorithm 3 derived from [keySeed] bytes using platform-specific derivation
  static Uint8List iso9797MacAlg3(Uint8List keySeed) {
    if (Platform.isIOS) {
      // Use Swift-style KDF on iOS
      return SwiftStyleKDF.iso9797MacAlg3(keySeed);
    } else {
      // Use Dart-style KDF on Android and other platforms
      return DeriveKey.iso9797MacAlg3(keySeed);
    }
  }
}
```

### 3. BAC Implementation Updates

**File:** `lib/src/proto/bac.dart`

**Thay đổi chính:**
- Sử dụng `PlatformKDF` thay vì `DeriveKey`
- Cập nhật assertions để chấp nhận 24 bytes cho Kenc và Kmac

```dart
// Trước:
final KSenc = DeriveKey.desEDE(keySeed);
final KSmac = DeriveKey.iso9797MacAlg3(keySeed);

// Sau:
final KSenc = PlatformKDF.desEDE(keySeed);
final KSmac = PlatformKDF.iso9797MacAlg3(keySeed);
```

**Assertion updates:**
```dart
// Trước:
assert(Kenc.length == kLen); // kLen = 16

// Sau:
assert(Kenc.length == kLen || Kenc.length == 24); // Support both 16 and 24 bytes
```

### 4. DBA Key Updates

**File:** `lib/src/proto/dba_key.dart`

```dart
// Trước:
Uint8List get encKey => DeriveKey.desEDE(keySeed);
Uint8List get macKey => DeriveKey.iso9797MacAlg3(keySeed);

// Sau:
Uint8List get encKey => PlatformKDF.desEDE(keySeed);
Uint8List get macKey => PlatformKDF.iso9797MacAlg3(keySeed);
```

## 📊 So sánh kết quả

### Trước khi sửa:
- ✅ Android: Hoạt động bình thường
- ❌ iOS: Lỗi 6300 (Authentication failed)

### Sau khi sửa:
- ✅ Android: Hoạt động bình thường
- ✅ iOS: Hoạt động bình thường

## 🔧 Cách hoạt động

### Platform Detection
```dart
if (Platform.isIOS) {
  // Sử dụng Swift-style KDF (24 bytes)
  return SwiftStyleKDF.desEDE(keySeed);
} else {
  // Sử dụng Dart-style KDF (16 bytes)
  return DeriveKey.desEDE(keySeed);
}
```

### Key Derivation Flow
1. **Input**: keySeed (16 bytes)
2. **Platform Detection**: iOS vs Android
3. **KDF Selection**: Swift-style vs Dart-style
4. **Output**: Kenc và Kmac với độ dài phù hợp

## 🧪 Testing

### Test Cases
1. **Swift-style KDF**: Xác nhận tạo ra đúng 24 bytes keys
2. **Platform Detection**: Xác nhận chọn đúng KDF cho từng platform
3. **Backward Compatibility**: Xác nhận Android vẫn hoạt động
4. **Real Device Testing**: Xác nhận hoạt động trên iOS thực tế

### Test Results
- ✅ Swift-style KDF tạo ra chính xác keys như native iOS
- ✅ Platform-specific KDF chọn đúng algorithm
- ✅ BAC process hoạt động đúng trên cả hai platform
- ✅ NFC reading thành công trên iOS và Android

## 📝 Lưu ý quan trọng

### MRZ Key Format
- **iOS**: Chỉ sử dụng 9 ký tự cuối của passport number
- **Android**: Có thể sử dụng toàn bộ passport number
- **Giải pháp**: Đảm bảo MRZ key calculation nhất quán

### Key Length Compatibility
- **ISO9797.macAlg3**: Hỗ trợ cả 16 và 24 bytes cho key
- **DESedeEncrypt/DESedeDecrypt**: Hỗ trợ cả 16 và 24 bytes cho key
- **BAC assertions**: Đã được cập nhật để chấp nhận cả hai độ dài

### Performance
- **Swift-style KDF**: Nhanh hơn một chút (ít bước tính toán hơn)
- **Dart-style KDF**: Linh hoạt hơn (counter mode)
- **Impact**: Không đáng kể về performance

## 🚀 Kết luận

Giải pháp platform-specific KDF đã thành công giải quyết vấn đề NFC reading trên iOS trong khi vẫn duy trì tính tương thích với Android. Điều này đảm bảo rằng ứng dụng có thể hoạt động trên cả hai nền tảng mà không cần thay đổi logic nghiệp vụ.

### Key Takeaways
1. **Platform differences**: Android và iOS có cách tiếp cận khác nhau cho key derivation
2. **Backward compatibility**: Quan trọng để duy trì tính tương thích
3. **Testing**: Cần test trên cả hai platform để đảm bảo hoạt động đúng
4. **Documentation**: Cần ghi chép rõ ràng về sự khác biệt và giải pháp

---

*Tài liệu này được cập nhật lần cuối: 2025-07-26* 