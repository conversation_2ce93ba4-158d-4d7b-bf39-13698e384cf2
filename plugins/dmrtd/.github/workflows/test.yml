# This is a basic workflow to help you get started with Actions

name: test

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master & develop branch
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master, develop ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      - uses: subosito/flutter-action@v1
        with:
           channel: 'stable'
      - run: flutter pub get

      - name: Flutter test
        run: flutter test
