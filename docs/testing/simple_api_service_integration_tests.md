# SimpleApiService Integration Tests

## Tổng quan

`SimpleApiService` là một implementation đơn giản của `IApiService` được thiết kế đặc biệt cho testing. Nó cung cấp khả năng auto-login khi gặp lỗi 401, gi<PERSON><PERSON> việc test các service với real server trở nên dễ dàng hơn.

## Tính năng chính

### 🔐 Login Management
- **Login trước**: `login()` method để lấy token trước khi test
- **Auto-login**: Tự động login khi gặp lỗi 401 (Unauthorized)
- **Token management**: `isLoggedIn`, `accessToken` getters
- **Retry mechanism**: Retry request gốc với access token mới
- **Concurrent handling**: Xử lý multiple login requests

### 🧪 Test-Friendly
- **Setup helpers**: `setupAndLoginTestApiService()` cho login trước
- **Configuration**: Cấu hình đơn giản qua method `configure()`
- **Service locator**: <PERSON><PERSON><PERSON> hợp với `GetIt` service locator
- **Environment variables**: Hỗ trợ cho CI/CD
- **Logging**: Log chi tiết cho debugging

## Cấu trúc Files

```
lib/shared/services/api/
├── simple_api_service.dart          # SimpleApiService implementation
└── api_service_interface.dart       # Interface definition

test/
├── helpers/
│   ├── simple_api_test_helper.dart  # Test setup helper
│   └── test_config.dart             # Test configuration
├── features/dashboard/services/
│   └── dashboard_stats_service_test.dart  # Integration tests
└── run_integration_tests.sh         # Test runner script
```

## Cách sử dụng

### 1. Setup Test Environment

#### Setup với Auto-Login (Recommended)
```dart
import 'package:kiloba_biz/test/helpers/simple_api_test_helper.dart';

void main() {
  setUpAll(() async {
    // Setup và login trước để lấy token
    final loginSuccess = await SimpleApiTestHelper.setupAndLoginTestApiService(
      baseUrl: 'http://************:8190',
      username: 'testuser',
      password: 'password123',
    );
    
    if (!loginSuccess) {
      throw Exception('Failed to login with test credentials');
    }
  });

  tearDownAll(() async {
    await SimpleApiTestHelper.resetTestEnvironment();
  });
}
```

#### Setup cũ (không login trước)
```dart
void main() {
  setUpAll(() async {
    await SimpleApiTestHelper.setupTestApiService(
      baseUrl: 'http://************:8190',
      username: 'testuser',
      password: 'password123',
    );
  });
}
```

### 2. Viết Integration Test

```dart
test('should be logged in with valid token', () {
  final simpleApiService = getIt.get<IApiService>() as SimpleApiService;
  expect(simpleApiService.isLoggedIn, isTrue);
  expect(simpleApiService.accessToken, isNotEmpty);
  print('✅ Login verified - Token: ${simpleApiService.accessToken?.substring(0, 20)}...');
});

test('should fetch dashboard stats successfully', () async {
  final service = DashboardStatsService();
  
  final result = await service.getUserBusinessDashboardStats();
  
  expect(result, isA<DashboardStats>());
  expect(result.userCif, isNotEmpty);
  expect(result.userType, isNotEmpty);
  
  print('✅ Dashboard stats fetched successfully:');
  print('- User CIF: ${result.userCif}');
  print('- User Type: ${result.userType}');
  print('- Total Customers: ${result.totalCustomers}');
  print('- Total Revenue: ${result.totalRevenue}');
});
```

### 3. Chạy Tests

#### Sử dụng script (Recommended)
```bash
# Chạy với default configuration
./test/run_integration_tests.sh

# Chạy với custom configuration
TEST_BASE_URL="http://your-server.com" \
TEST_USERNAME="your_user" \
TEST_PASSWORD="your_pass" \
./test/run_integration_tests.sh
```

#### Sử dụng Flutter test command
```bash
flutter test test/features/dashboard/services/dashboard_stats_service_test.dart \
  --dart-define=TEST_BASE_URL="http://************:8190" \
  --dart-define=TEST_USERNAME="testuser" \
  --dart-define=TEST_PASSWORD="password123"
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `TEST_BASE_URL` | `http://************:8190` | Test server URL |
| `TEST_USERNAME` | `testuser` | Test username |
| `TEST_PASSWORD` | `password123` | Test password |

### Test Configuration Class

```dart
class TestConfig {
  static const String testBaseUrl = String.fromEnvironment(
    'TEST_BASE_URL',
    defaultValue: 'http://************:8190',
  );
  
  static const Duration testTimeout = Duration(seconds: 30);
  
  static bool get isTestEnvironment {
    return testBaseUrl.isNotEmpty && 
           testUsername.isNotEmpty && 
           testPassword.isNotEmpty;
  }
}
```

## Login Flow

### 1. Setup với Login trước (Recommended)

```mermaid
sequenceDiagram
    participant Test as Test Setup
    participant SimpleAPI as SimpleApiService
    participant Server as Test Server
    participant Dashboard as DashboardStatsService

    Test->>SimpleAPI: setupAndLoginTestApiService()
    SimpleAPI->>Server: POST /api/v1/auth/login
    Server-->>SimpleAPI: 200 OK + access_token
    SimpleAPI->>Test: Login SUCCESS
    
    Test->>Dashboard: getUserBusinessDashboardStats()
    Dashboard->>SimpleAPI: POST /rest/rpc/get_user_business_dashboard_stats
    SimpleAPI->>Server: Request + Authorization header
    Server-->>SimpleAPI: 200 OK + dashboard data
    SimpleAPI-->>Dashboard: DashboardStats object
    Dashboard-->>Test: Success with real data
```

### 2. Auto-Login khi gặp 401

```mermaid
sequenceDiagram
    participant Test as Test
    participant Service as DashboardStatsService
    participant SimpleAPI as SimpleApiService
    participant Server as Test Server

    Test->>Service: getUserBusinessDashboardStats()
    Service->>SimpleAPI: POST /rest/rpc/get_user_business_dashboard_stats
    SimpleAPI->>Server: Request (no token)
    Server-->>SimpleAPI: 401 Unauthorized
    SimpleAPI->>Server: POST /api/v1/auth/login
    Server-->>SimpleAPI: 200 OK + access_token
    SimpleAPI->>Server: Retry original request + token
    Server-->>SimpleAPI: 200 OK + data
    SimpleAPI-->>Service: Response data
    Service-->>Test: DashboardStats object
```

## Error Handling

### SimpleApiException
```dart
class SimpleApiException implements Exception {
  final String message;
  final int? statusCode;
  final dynamic data;
}
```

### DashboardStatsException
```dart
enum DashboardStatsExceptionType { 
  notFound, 
  apiError, 
  unauthorized, 
  unknown 
}
```

## Best Practices

### 1. Test Setup
- Luôn sử dụng `setUpAll()` để setup test environment
- Sử dụng `tearDownAll()` để cleanup
- Kiểm tra server connectivity trước khi chạy tests

### 2. Test Cases
- Test cả success và failure scenarios
- Sử dụng timeout phù hợp (30s cho integration tests)
- Log kết quả để debugging

### 3. CI/CD Integration
- Sử dụng environment variables cho credentials
- Không commit real credentials vào code
- Setup test server trong CI environment

## Troubleshooting

### Server không reachable
```bash
# Kiểm tra connectivity
curl -v http://************:8190

# Kiểm tra firewall/network
ping ************
```

### Authentication failed
```bash
# Test login manually
curl -X POST http://************:8190/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}'
```

### Test timeout
- Tăng timeout trong `TestConfig.testTimeout`
- Kiểm tra server performance
- Verify network latency

## Examples

### Test DashboardStatsService với Login trước
```dart
group('DashboardStatsService Integration Tests', () {
  setUpAll(() async {
    // Setup và login trước để lấy token
    final loginSuccess = await SimpleApiTestHelper.setupAndLoginTestApiService(
      baseUrl: TestConfig.testBaseUrl,
      username: TestConfig.testUsername,
      password: TestConfig.testPassword,
    );
    
    if (!loginSuccess) {
      throw Exception('Failed to login with test credentials');
    }
  });

  test('should be logged in with valid token', () {
    final simpleApiService = getIt.get<IApiService>() as SimpleApiService;
    expect(simpleApiService.isLoggedIn, isTrue);
    expect(simpleApiService.accessToken, isNotEmpty);
  });

  test('should fetch dashboard stats successfully', () async {
    final service = DashboardStatsService();
    final result = await service.getUserBusinessDashboardStats();
    
    expect(result, isA<DashboardStats>());
    expect(result.userCif, isNotEmpty);
    expect(result.userType, isNotEmpty);
    
    print('✅ Dashboard stats: ${result.userCif} (${result.userType})');
  });

  test('should handle 401 and auto-login', () async {
    final service = DashboardStatsService();
    final result = await service.getUserBusinessDashboardStats();
    
    expect(result, isA<DashboardStats>());
  });
});
```

### Test với custom configuration
```dart
test('should work with custom server', () async {
  final loginSuccess = await SimpleApiTestHelper.setupAndLoginTestApiService(
    baseUrl: 'http://custom-server.com',
    username: 'custom_user',
    password: 'custom_pass',
  );
  
  expect(loginSuccess, isTrue);
  
  final service = DashboardStatsService();
  final result = await service.getUserBusinessDashboardStats();
  
  expect(result, isA<DashboardStats>());
});
```

### Test Error Handling
```dart
group('Error Handling Tests', () {
  setUpAll(() async {
    // Setup với invalid credentials
    final loginSuccess = await SimpleApiTestHelper.setupAndLoginTestApiService(
      baseUrl: TestConfig.testBaseUrl,
      username: 'invalid_user',
      password: 'invalid_password',
    );
    
    // Login sẽ fail như mong đợi
    expect(loginSuccess, isFalse);
  });

  test('should handle authentication failure gracefully', () async {
    final service = DashboardStatsService();
    
    expect(
      () => service.getUserBusinessDashboardStats(),
      throwsA(isA<DashboardStatsException>()),
    );
  });
});
```

## SimpleApiService Methods

### Public Methods
```dart
class SimpleApiService implements IApiService {
  /// Configure SimpleApiService cho testing
  void configure({
    required String baseUrl,
    required String username,
    required String password,
    IAppLogger? logger,
  });

  /// Login trước để lấy token (public method cho testing)
  Future<bool> login();

  /// Check if đã login
  bool get isLoggedIn;

  /// Get current access token
  String? get accessToken;

  // IApiService methods
  Future<Response> get(String path, {...});
  Future<Response> post(String path, {...});
  Future<Response> put(String path, {...});
  Future<Response> delete(String path, {...});
  Future<Response> patch(String path, {...});
}
```

### Test Helper Methods
```dart
class SimpleApiTestHelper {
  /// Setup và login với test credentials
  static Future<bool> setupAndLoginTestApiService({
    required String baseUrl,
    required String username,
    required String password,
    IAppLogger? logger,
  });

  /// Setup và login với default test credentials
  static Future<bool> setupAndLoginDefaultTestApiService();

  /// Setup test environment (không login)
  static Future<void> setupTestApiService({...});

  /// Reset test environment
  static Future<void> resetTestEnvironment();

  /// Check if test environment is ready
  static bool isTestEnvironmentReady();
}
```

## Kết luận

`SimpleApiService` cung cấp một cách đơn giản và hiệu quả để test các service với real server data. Với **login management** (login trước + auto-login), **token management**, và **test-friendly design**, nó giúp việc viết integration tests trở nên dễ dàng và đáng tin cậy hơn.

### Key Benefits:
- ✅ **Login trước**: Đảm bảo có token trước khi test
- ✅ **Auto-login**: Tự động xử lý 401 errors
- ✅ **Token management**: Kiểm tra login status và access token
- ✅ **Error handling**: Xử lý authentication failures
- ✅ **Real data**: Test với data thật từ server
- ✅ **CI/CD ready**: Environment variables support 