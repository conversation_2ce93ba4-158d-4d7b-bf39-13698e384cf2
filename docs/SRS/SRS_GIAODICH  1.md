# SRS – MODULE GIAO DỊCH

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

### 1.1 <PERSON><PERSON><PERSON> đích 

### 1.2 <PERSON>ai trò sử dụng
Toàn hàng

---

# MODULE GIAO DỊCH
# 1. <PERSON>h sách giao dịch

## 1.1 UI/UX danh sách giao dịch

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Danh sách giao dịch - KienlongBank</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            max-width: 1500px;
            margin: 0 auto;
            border: 1px solid #e2e8f0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            height: 812px;
        }      
        /* Mobile Frame */
        .mobile-frame {
            width: 400px;
            height: 812px;
            position: relative;
            overflow: hidden;
            background: white;
        }        
        /* Status Bar */
        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }        
        .status-left {
            display: flex;
            align-items: center;
            gap: 5px;
        }      
        .signal-bars {
            display: flex;
            gap: 2px;
        }        
        .signal-bar {
            width: 3px;
            background: white;
            border-radius: 1px;
        }        
        .signal-bar:nth-child(1) { height: 4px; }
        .signal-bar:nth-child(2) { height: 6px; }
        .signal-bar:nth-child(3) { height: 8px; }
        .signal-bar:nth-child(4) { height: 10px; }        
        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }       
        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid white;
            border-radius: 2px;
            position: relative;
        }      
        .battery::after {
            content: '';
            width: 2px;
            height: 6px;
            background: white;
            position: absolute;
            right: -3px;
            top: 3px;
            border-radius: 0 1px 1px 0;
        }        
        .battery-fill {
            width: 80%;
            height: 100%;
            background: white;
            border-radius: 1px;
        }        
        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
        }        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }        
        .header h1 {
            font-size: 18px;
            font-weight: 600;
        }        
        .notification-badge {
            position: relative;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
        }        
        .notification-count {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }        
        /* Search Bar */
        .search-container {
            position: relative;
        }       
        .search-input {
            width: 100%;
            padding: 12px 40px 12px 40px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.95);
            font-size: 14px;
            color: #333;
        }        
        .search-input::placeholder {
            color: #9ca3af;
        }        
        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            font-size: 16px;
        }        
        .filter-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #8b5cf6;
            cursor: pointer;
            font-size: 16px;
        }       
        /* Scrollable Content */
        .content {
            height: calc(812px - 44px - 70px - 70px);
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }        
        /* Quick Actions */
        .quick-actions {
            background: white;
            margin: 15px;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }        
        .quick-actions-row {
            display: flex;
            gap: 8px;
        }        
        .quick-btn {
            flex: 1;
            padding: 10px 8px;
            border: none;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }        
        .btn-add {
            background: #10b981;
            color: white;
        }        
        .btn-distribute {
            background: #f59e0b;
            color: white;
        }        
        .btn-filter {
            background: #8b5cf6;
            color: white;
        }        
        /* Status Tabs */
        .status-tabs {
            background: white;
            margin: 0 15px 15px;
            padding: 12px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }        
        .status-row {
            display: flex;
            gap: 6px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }        
        .status-row::-webkit-scrollbar {
            display: none;
        }        
        .status-tab {
            padding: 8px 12px;
            border: none;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.2s;
            flex-shrink: 0;
        }        
        .status-tab.active {
            background: #667eea;
            color: white;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }        
        .status-tab:not(.active) {
            background: #f1f5f9;
            color: #64748b;
        }        
        /* Summary Stats */
        .summary-stats {
            background: white;
            margin: 0 15px 15px;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }        
        .stats-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: #1e293b;
            font-size: 14px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }        
        .stat-item {
            font-size: 11px;
            color: #64748b;
            line-height: 1.4;
        }        
        .stat-value {
            font-weight: 700;
            color: #1e293b;
            font-size: 12px;
            display: block;
            margin-top: 2px;
        }        
        /* Select All Bar */
        .select-all-bar {
            background: #eff6ff;
            margin: 0 15px 15px;
            padding: 12px 15px;
            border-radius: 10px;
            border: 1px solid #dbeafe;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }        
        .select-all-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }        
        .checkbox {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            border: 2px solid #667eea;
            background: #667eea;
            position: relative;
            cursor: pointer;
            flex-shrink: 0;
        }        
        .checkbox::after {
            content: '✓';
            color: white;
            font-size: 10px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }        
        .select-text {
            color: #1e40af;
            font-weight: 600;
            font-size: 12px;
        }        
        /* Transaction List */
        .transaction-list {
            margin: 0 15px;
        }        
        .transaction-item {
            background: white;
            margin-bottom: 12px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid transparent;
            transition: all 0.2s;
        }        
        .transaction-item.selected {
            border-left-color: #667eea;
            background: #f8faff;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }        
        .transaction-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }        
        .transaction-checkbox {
            margin-top: 2px;
            flex-shrink: 0;
        }       
        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 13px;
            flex-shrink: 0;
        }        
        .avatar-blue { background: #667eea; }
        .avatar-purple { background: #8b5cf6; }
        .avatar-orange { background: #f59e0b; }
        .avatar-green { background: #10b981; }
        .avatar-red { background: #ef4444; }       
        .customer-info {
            flex: 1;
            min-width: 0;
        }       
        .customer-name {
            font-weight: 600;
            margin-bottom: 4px;
            color: #1e293b;
            font-size: 14px;
        }        
        .product-name {
            font-size: 11px;
            color: #64748b;
            margin-bottom: 6px;
        }        
        .tags {
            display: flex;
            gap: 4px;
            margin-bottom: 8px;
            flex-wrap: wrap;
        }       
        .tag {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: 600;
        }        
        .tag-warning {
            background: #fef3c7;
            color: #d97706;
        }       
        .tag-success {
            background: #dcfce7;
            color: #16a34a;
        }        
        .tag-info {
            background: #dbeafe;
            color: #2563eb;
        }        
        .tag-danger {
            background: #fee2e2;
            color: #dc2626;
        }        
        .tag-purple {
            background: #f3e8ff;
            color: #8b5cf6;
        }        
        .transaction-actions {
            display: flex;
            gap: 6px;
            flex-shrink: 0;
        }        
        .action-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s;
        }        
        .btn-call {
            background: #10b981;
            color: white;
        }        
        .btn-message {
            background: #667eea;
            color: white;
        }        
        .transaction-details {
            font-size: 11px;
            color: #64748b;
            line-height: 1.5;
        }        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 3px;
        }        
        .detail-left {
            display: flex;
            align-items: center;
            gap: 6px;
        }        
        .amount {
            font-weight: 700;
            color: #1e293b;
            font-size: 12px;
        }        
        /* Load More */
        .load-more {
            background: #f8fafc;
            margin: 15px;
            padding: 16px;
            border-radius: 12px;
            border: 2px dashed #e2e8f0;
            text-align: center;
            color: #64748b;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
        }        
        .load-more:active {
            border-color: #667eea;
            color: #667eea;
            background: #f0f9ff;
        }       
        /* Bottom Navigation */
        .bottom-nav {
            background: white;
            padding: 12px 20px 20px;
            border-top: 1px solid #e2e8f0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 0;
        }        
        .nav-item.active {
            background: #667eea;
            color: white;
        }        
        .nav-item:not(.active) {
            color: #64748b;
        }      
        .nav-icon {
            font-size: 16px;
        }        
        .nav-text {
            font-size: 10px;
            font-weight: 600;
            white-space: nowrap;
        }        
        /* Touch Feedback */
        .quick-btn:active,
        .status-tab:active,
        .action-btn:active {
            transform: scale(0.95);
        }        
        .transaction-item:active {
            transform: scale(0.98);
        }        
        /* Custom Scrollbar */
        .content::-webkit-scrollbar {
            width: 3px;
        }        
        .content::-webkit-scrollbar-track {
            background: transparent;
        }        
        .content::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="mobile-frame">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
                <div class="signal-bars">
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                    <div class="signal-bar"></div>
                </div>
                <span>📶</span>
                <span>📶</span>
            </div>
            <div class="status-right">
                <span>🔵</span>
                <span>📶</span>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>
        <!-- Header -->
        <div class="header">
            <div class="header-top">
                <h1>Danh sách giao dịch</h1>
                <div class="notification-badge">
                    🔔
                    <div class="notification-count">3</div>
                </div>
            </div>            
            <!-- Search Bar -->
            <div class="search-container">
                <div class="search-icon">🔍</div>
                <input type="text" class="search-input" placeholder="Tìm họ tên, SĐT, GTTT, mã GD...">
                <div class="filter-icon">⚙️</div>
            </div>
        </div>
        <!-- Scrollable Content -->
        <div class="content">
            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="quick-actions-row">
                    <button class="quick-btn btn-add">+ Thêm mới</button>
                    <button class="quick-btn btn-distribute">Phân bổ</button>
                    <button class="quick-btn btn-filter">Lọc nâng cao</button>
                </div>
            </div>
            <!-- Status Filter Tabs -->
            <div class="status-tabs">
                <div class="status-row">
                    <button class="status-tab active">Tất cả</button>
                    <button class="status-tab">Đang xử lý</button>
                    <button class="status-tab">Thành công</button>
                    <button class="status-tab">Từ chối</button>
                    <button class="status-tab">Chờ duyệt</button>
                </div>
            </div>
            <!-- Summary Stats -->
            <div class="summary-stats">
                <div class="stats-title">📊 Tổng quan</div>
                <div class="stats-grid">
                    <div class="stat-item">
                        💰 Giá trị GD
                        <span class="stat-value">2.8 tỷ VNĐ</span>
                    </div>
                    <div class="stat-item">
                        ⏳ Đang xử lý
                        <span class="stat-value">23 hồ sơ</span>
                    </div>
                    <div class="stat-item">
                        📈 Số lượng
                        <span class="stat-value">145 giao dịch</span>
                    </div>
                    <div class="stat-item">
                        ✅ Thành công
                        <span class="stat-value">98 hồ sơ</span>
                    </div>
                </div>
            </div>
            <!-- Select All Bar -->
            <div class="select-all-bar">
                <div class="select-all-left">
                    <div class="checkbox"></div>
                    <span class="select-text">Chọn tất cả</span>
                </div>
                <span class="select-text">Đã chọn: 3</span>
            </div>
            <!-- Transaction List -->
            <div class="transaction-list">
                <!-- Transaction Item 1 -->
                <div class="transaction-item selected">
                    <div class="transaction-header">
                        <div class="checkbox transaction-checkbox"></div>
                        <div class="avatar avatar-blue">NV</div>
                        <div class="customer-info">
                            <div class="customer-name">Nguyễn Văn An</div>
                            <div class="product-name">Vay trả góp ngày</div>
                            <div class="tags">
                                <span class="tag tag-warning">CBBH</span>
                                <span class="tag tag-success">VIP</span>
                            </div>
                        </div>
                        <div class="transaction-actions">
                            <button class="action-btn btn-call">📞</button>
                            <button class="action-btn btn-message">💬</button>
                        </div>
                    </div>
                    <div class="transaction-details">
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>📞</span>
                                <span>0987654321</span>
                            </div>
                            <span class="amount">💰 50M</span>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>⏰</span>
                                <span>2 giờ trước</span>
                            </div>
                            <div class="detail-left">
                                <span>👤</span>
                                <span>Nguyễn Thị B</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>🏢</span>
                                <span>CN Hà Nội</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Transaction Item 2 -->
                <div class="transaction-item selected">
                    <div class="transaction-header">
                        <div class="checkbox transaction-checkbox"></div>
                        <div class="avatar avatar-purple">TM</div>
                        <div class="customer-info">
                            <div class="customer-name">Trần Minh Tuấn</div>
                            <div class="product-name">Vay trả góp ngày</div>
                            <div class="tags">
                                <span class="tag tag-success">Phê duyệt</span>
                                <span class="tag tag-danger">Gấp</span>
                            </div>
                        </div>
                        <div class="transaction-actions">
                            <button class="action-btn btn-call">📞</button>
                            <button class="action-btn btn-message">💬</button>
                        </div>
                    </div>
                    <div class="transaction-details">
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>📞</span>
                                <span>0912345678</span>
                            </div>
                            <span class="amount">💰 75M</span>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>⏰</span>
                                <span>1 ngày trước</span>
                            </div>
                            <div class="detail-left">
                                <span>👤</span>
                                <span>Lê Văn C</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>🏢</span>
                                <span>CN Đà Nẵng</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Transaction Item 3 -->
                <div class="transaction-item selected">
                    <div class="transaction-header">
                        <div class="checkbox transaction-checkbox"></div>
                        <div class="avatar avatar-orange">HT</div>
                        <div class="customer-info">
                            <div class="customer-name">Hoàng Thị Lan</div>
                            <div class="product-name">Vay trả góp ngày</div>
                            <div class="tags">
                                <span class="tag tag-warning">Chờ duyệt</span>
                                <span class="tag tag-info">Mới</span>
                            </div>
                        </div>
                        <div class="transaction-actions">
                            <button class="action-btn btn-call">📞</button>
                            <button class="action-btn btn-message">💬</button>
                        </div>
                    </div>
                    <div class="transaction-details">
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>📞</span>
                                <span>0898765432</span>
                            </div>
                            <span class="amount">💰 30M</span>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>⏰</span>
                                <span>3 giờ trước</span>
                            </div>
                            <div class="detail-left">
                                <span>👤</span>
                                <span>Phạm Văn D</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>🏢</span>
                                <span>CN HCM</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Transaction Item 4 -->
                <div class="transaction-item">
                    <div class="transaction-header">
                        <div class="checkbox transaction-checkbox" style="background: #f1f5f9; border-color: #d1d5db;"></div>
                        <div class="avatar avatar-green">PT</div>
                        <div class="customer-info">
                            <div class="customer-name">Phan Thanh Sơn</div>
                            <div class="product-name">Vay trả góp ngày</div>
                            <div class="tags">
                                <span class="tag tag-success">Thành công</span>
                            </div>
                        </div>
                        <div class="transaction-actions">
                            <button class="action-btn btn-call">📞</button>
                            <button class="action-btn btn-message">💬</button>
                        </div>
                    </div>
                    <div class="transaction-details">
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>📞</span>
                                <span>0923456789</span>
                            </div>
                            <span class="amount">💰 100M</span>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>⏰</span>
                                <span>1 tuần trước</span>
                            </div>
                            <div class="detail-left">
                                <span>👤</span>
                                <span>Nguyễn Thị E</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>🏢</span>
                                <span>CN Cần Thơ</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Transaction Item 5 -->
                <div class="transaction-item">
                    <div class="transaction-header">
                        <div class="checkbox transaction-checkbox" style="background: #f1f5f9; border-color: #d1d5db;"></div>
                        <div class="avatar avatar-red">LM</div>
                        <div class="customer-info">
                            <div class="customer-name">Lê Minh Quang</div>
                            <div class="product-name">Vay trả góp ngày</div>
                            <div class="tags">
                                <span class="tag tag-danger">Từ chối</span>
                            </div>
                        </div>
                        <div class="transaction-actions">
                            <button class="action-btn btn-call">📞</button>
                            <button class="action-btn btn-message">💬</button>
                        </div>
                    </div>
                    <div class="transaction-details">
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>📞</span>
                                <span>0934567890</span>
                            </div>
                            <span class="amount">💰 25M</span>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>⏰</span>
                                <span>2 ngày trước</span>
                            </div>
                            <div class="detail-left">
                                <span>👤</span>
                                <span>Trần Văn F</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-left">
                                <span>🏢</span>
                                <span>CN Hải Phòng</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Load More -->
                <div class="load-more">
                    ⬇️ Tải thêm giao dịch (5/145)
                </div>
            </div>
        </div>
        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">Trang chủ</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">📊</div>
                <div class="nav-text">Báo cáo</div>
            </div>
            <div class="nav-item">
                <div class="nav-icon">👤</div>
                <div class="nav-text">Cá nhân</div>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Prevent zoom on double tap
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
            // Checkbox interactions
            const checkboxes = document.querySelectorAll('.checkbox');
            const transactionItems = document.querySelectorAll('.transaction-item');            
            checkboxes.forEach((checkbox, index) => {
                checkbox.addEventListener('click', function(e) {
                    e.stopPropagation();                  
                    if (index === 0) {
                        // Select all checkbox
                        const isChecked = this.style.background === 'rgb(102, 126, 234)';
                        checkboxes.forEach((cb, i) => {
                            if (i > 0) {
                                cb.style.background = isChecked ? '#f1f5f9' : '#667eea';
                                cb.style.borderColor = isChecked ? '#d1d5db' : '#667eea';
                                if (transactionItems[i-1]) {
                                    transactionItems[i-1].classList.toggle9:41
                                    📶
                                    📶
                                    🔵
                                    📶
                                    Danh sách giao dịch
                                    🔔3
                                    🔍
                                    Tìm họ tên, SĐT, GTTT, mã GD...
                                    ⚙️
                                    + Thêm mới
                                    Phân bổ
                                    Lọc nâng cao
                                    Tất cả
                                    Đang xử lý
                                    Thành công
                                    Từ chối
                                    Chờ duyệt
                                    📊 Tổng quan
                                    💰 Giá trị GD
                                    2.8 tỷ VNĐ
                                    ⏳ Đang xử lý
                                    23 hồ sơ
                                    📈 Số lượng
                                    145 giao dịch
                                    ✅ Thành công
                                    98 hồ sơ
                                    Chọn tất cả
                                    Đã chọn: 3
                                    NV
                                    Nguyễn Văn An
                                    Vay trả góp ngày
                                    CBBH
                                    VIP
                                    📞
                                    💬('selected', !isChecked);
                                }
                            }
                        });
                    } else {
                        // Individual checkbox
                        const isChecked = this.style.background === 'rgb(102, 126, 234)';
                        this.style.background = isChecked ? '#f1f5f9' : '#667eea';
                        this.style.borderColor = isChecked ? '#d1d5db' : '#667eea';
                        if (transactionItems[index-1]) {
                            transactionItems[index-1].classList.toggle('selected', !isChecked);
                        }
                    }
                });
            });            
            // Status tab interactions
            const statusTabs = document.querySelectorAll('.status-tab');
            statusTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    statusTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });            
            // Action button interactions
            const actionBtns = document.querySelectorAll('.action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (this.classList.contains('btn-call')) {
                        // Simulate phone call
                        const phone = this.closest('.transaction-item').querySelector('.detail-left span:nth-child(2)').textContent;
                        alert(`Gọi điện: ${phone}`);
                    } else if (this.classList.contains('btn-message')) {
                        // Simulate SMS
                        const phone = this.closest('.transaction-item').querySelector('.detail-left span:nth-child(2)').textContent;
                        alert(`Nhắn tin: ${phone}`);
                    }
                });
            });           
            // Quick action buttons
            const quickBtns = document.querySelectorAll('.quick-btn');
            quickBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    alert(`Chức năng: ${action}`);
                });
            });           
            // Transaction item click
            transactionItems.forEach(item => {
                item.addEventListener('click', function() {
                    const customerName = this.querySelector('.customer-name').textContent;
                    alert(`Xem chi tiết giao dịch: ${customerName}`);
                });
            });            
            // Load more functionality
            document.querySelector('.load-more').addEventListener('click', function() {
                this.textContent = '⏳ Đang tải...';
                setTimeout(() => {
                    this.textContent = '⬇️ Tải thêm giao dịch (5/145)';
                    alert('Đã tải thêm 5 giao dịch');
                }, 1000);
            });
            // Search functionality
            const searchInput = document.querySelector('.search-input');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                transactionItems.forEach(item => {
                    const customerName = item.querySelector('.customer-name').textContent.toLowerCase();
                    const phone = item.querySelector('.detail-left span:nth-child(2)').textContent.toLowerCase();                    
                    if (customerName.includes(searchTerm) || phone.includes(searchTerm)) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
            // Filter icon click
            document.querySelector('.filter-icon').addEventListener('click', function() {
                alert('Mở màn hình lọc nâng cao');
            });
            // Notification badge click
            document.querySelector('.notification-badge').addEventListener('click', function() {
                alert('Hiển thị 3 thông báo mới');
            });
        });
    </script>
</body>
</html>

---
### Mô tả màn hình

| STT | Trường thông tin             | Kiểu hiển thị | Kiểu thao tác | Bắt buộc nhập                   | Mô tả chi tiết                                                                                                                                                                                                                                                                                                                                                                                     |
| --- | ---------------------------- | ------------- | ------------- | ------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | Thao tác nhanh               | Group button  | Click         | -                               | Gồm các nút thao tác nhanh như: Vay trả góp ngày, Phân bổ hồ sơ,...                                                                                                                                                                                                                                                                                                                                |
| 1   | Tìm kiếm                     | Textbox       | Nhập          | -                               | Tìm kiếm Họ tên, SĐT, Số GTTT, mã GD, số tiền. Tìm kiếm gần đúng.                                                                                                                                                                                                                                                                                                                                  |
| 2   | Lọc nâng cao                 | Button        | Click         | -                               | Click hiển thị màn hình lọc nâng cao giao dịch                                                                                                                                                                                                                                                                                                                                                     |
| 3   | Lọc nhanh trạng thái         | Group button  | Click         | Có                              | hiển thị danh sách trạng thái cho phép click tìm kiếm , mặc định tìm tất cả. mặc định hiển thị trạng thái chung gồm ("Tất cả","Đang xử lý", "Thành công", "Từ chối"). Trường hợp tại màn hình lọc nâng cao, người dùng chọn tìm kiếm theo sản phẩm Trả góp ngày danh sách trạng thái hiển thị gồm ("Tất cả", "Đang tạo", "CBBH", "GDV/HTTD", "Chờ phê duyệt", "Phê duyệt", "Giải ngân", "Từ chối") |
| 4   | Thêm mới giao dịch           | button        | Nhập          | Không                           | Click để thêm mới giao dịch                                                                                                                                                                                                                                                                                                                                                                        |
| 5   | Checkbox chọn tất cả         | Checkbox      | Click         | -                               | Click để tích chọn tất cả bản ghi tại màn hình danh sách                                                                                                                                                                                                                                                                                                                                           |
| 5   | Đã chọn (số lượng)           | Text          | Read only     | -                               | Hiển thị số lượng checkbox đã chọn                                                                                                                                                                                                                                                                                                                                                                 |
| 5   | Item thông tin giao dịch     | Item          | Click         | -                               | Mỗi giao dịch hiển thị thành 1 item trong danh sách thông tin giao dịch                                                                                                                                                                                                                                                                                                                            |
| 5   | Check box                    | Checkbox      | Click         | -                               | Click để chọn bản ghi thao tác                                                                                                                                                                                                                                                                                                                                                                     |
| 5   | Avatar                       | image         | Read only     | -                               |                                                                                                                                                                                                                                                                                                                                                                                                    |
| 6   | Họ tên khách hàng            | Text          | Read only     | Không                           |                                                                                                                                                                                                                                                                                                                                                                                                    |
| 7   | Tên sản phẩm                 | Text          | Read only     | Không                           |                                                                                                                                                                                                                                                                                                                                                                                                    |
| 8   | Tags                         | Text          | Read only     | Không                           | Hiển thị tối đa 2 tags từ list tags đã chọn                                                                                                                                                                                                                                                                                                                                                        |
| 7   | Số điện thoại                | Text          | Read only     | Không                           |                                                                                                                                                                                                                                                                                                                                                                                                    |
| 9   | Số tiền giao dịch            | Text          | Read only     | Không                           | Nếu chưa có số tiền giao dịch mặc định là 0M                                                                                                                                                                                                                                                                                                                                                       |
| 10  | Thời gian cập nhật giao dịch | Text          | Read only     | Không                           |                                                                                                                                                                                                                                                                                                                                                                                                    |
| 11  | Trạng thái giao dịch         | Text          | Read only     | Không                           |
| 11  | Thời gian tạo giao dịch      | Text          | Read only     | Không                           |
| 12  | Người tạo giao dịch          | Text          | Read only     | Họ và tên người tạo giao dịch   |
| 13  | Chi nhánh/PGD                | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch |
| 14  | Gọi điện                     | Button        | Click         | Không                           | Click để chuyển đến gọi vào số KH vay chính                                                                                                                                                                                                                                                                                                                                                        |
| 15  | Nhắn tin                     | Button        | Click         | Không                           | Click để chuyển đến nhắn tin khách hàng vay chính                                                                                                                                                                                                                                                                                                                                                  |

> **Lưu ý:** phần tổng quan header gồm: Giá trị giao dịch, số lượng giao dịch ở các trạng thái sẽ thay đổi theo khi sử dụng bộ lọc trạng thái nhanh


## 1.2 Mô tả UI/UX màn hình lọc nâng cao
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Lọc nâng cao - App Sale</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #212121;
            max-width: 1500px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            height: 100vh;
            border: 1px solid #e0e0e0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        .mobile-frame {
            width: 400px;
            height: 812px;
            position: relative;
            overflow: hidden;
            background: white;
        } 
        /* Material Design Variables */
        :root {
            --primary: #1976D2;
            --primary-variant: #1565C0;
            --secondary: #03DAC6;
            --background: #FAFAFA;
            --surface: #FFFFFF;
            --error: #B00020;
            --warning: #FF9800;
            --success: #4CAF50;
            --on-primary: #FFFFFF;
            --on-surface: #000000;
            --on-background: #000000;
            --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
            --elevation-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
        }
        /* App Container */
        .app-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }
        /* Material App Bar */
        .app-bar {
            background: var(--primary);
            color: var(--on-primary);
            min-height: 56px;
            box-shadow: var(--elevation-2);
            position: relative;
            z-index: 10;
        }
        .app-bar-content {
            display: flex;
            align-items: center;
            min-height: 56px;
            padding: 0 16px;
        }
        .app-bar-leading {
            margin-right: 16px;
        }
        .app-bar-title {
            font-size: 20px;
            font-weight: 500;
            line-height: 32px;
            flex: 1;
        }
        .app-bar-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .icon-button {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            border: none;
            background: transparent;
            color: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .icon-button:hover {
            background: rgba(255, 255, 255, 0.08);
        }
        .icon-button:active {
            background: rgba(255, 255, 255, 0.12);
        }
        .badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: var(--error);
            color: white;
            border-radius: 8px;
            min-width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 500;
            padding: 0 4px;
        }
        /* Content Container */
        .content-container {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 80px;
        }
        /* Filter Section Card */
        .filter-section {
            background: var(--surface);
            margin: 8px 16px;
            border-radius: 4px;
            box-shadow: var(--elevation-1);
            overflow: hidden;
        }
        .section-header {
            padding: 16px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);
            display: flex;
            align-items: center;
            background: rgba(25, 118, 210, 0.04);
        }
        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            display: flex;
            align-items: center;
            flex: 1;
        }
        .section-title .material-icons {
            margin-right: 8px;
            color: var(--primary);
            font-size: 20px;
        }
        .section-count {
            background: var(--primary);
            color: white;
            border-radius: 12px;
            padding: 2px 8px;
            font-size: 12px;
            font-weight: 500;
            min-width: 20px;
            text-align: center;
        }
        .section-content {
            padding: 16px;
        }
        /* Quick Filter Chips */
        .quick-filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .filter-chip {
            height: 32px;
            padding: 0 12px;
            border-radius: 16px;
            border: 1px solid #E0E0E0;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            font-size: 14px;
            font-weight: 400;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .filter-chip.selected {
            background: var(--primary);
            color: var(--on-primary);
            border-color: var(--primary);
        }
        .filter-chip:hover:not(.selected) {
            background: rgba(0, 0, 0, 0.04);
        }
        .filter-chip .material-icons {
            font-size: 16px;
            margin-right: 4px;
        }
        /* Time Range Buttons */
        .time-range-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .time-range-button {
            flex: 1;
            min-width: 70px;
            height: 36px;
            border: 1px solid rgba(0, 0, 0, 0.12);
            border-radius: 4px;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .time-range-button.selected {
            background: var(--primary);
            color: var(--on-primary);
            border-color: var(--primary);
        }
        .time-range-button:hover:not(.selected) {
            background: rgba(0, 0, 0, 0.04);
        }
        /* Form Fields */
        .form-field {
            margin-bottom: 16px;
        }
        .form-field:last-child {
            margin-bottom: 0;
        }
        .form-label {
            font-size: 12px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.6);
            margin-bottom: 8px;
            display: block;
            text-transform: uppercase;
            letter-spacing: 0.4px;
        }
        .text-field {
            position: relative;
        }
        .text-input {
            width: 100%;
            height: 56px;
            padding: 16px 48px 16px 16px;
            border: 1px solid rgba(0, 0, 0, 0.38);
            border-radius: 4px;
            font-size: 16px;
            font-family: 'Roboto', sans-serif;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            transition: border-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .text-input:focus {
            outline: none;
            border-color: var(--primary);
            border-width: 2px;
            padding-left: 15px;
            padding-right: 47px;
        }
        .text-input::placeholder {
            color: rgba(0, 0, 0, 0.6);
        }
        .input-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 0.54);
            pointer-events: none;
        }
        /* Date Range */
        .date-range-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .date-input {
            flex: 1;
            height: 56px;
            padding: 16px;
            border: 1px solid rgba(0, 0, 0, 0.38);
            border-radius: 4px;
            font-size: 16px;
            font-family: 'Roboto', sans-serif;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            transition: border-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .date-input:focus {
            outline: none;
            border-color: var(--primary);
            border-width: 2px;
            padding: 15px;
        }
        .date-separator {
            color: rgba(0, 0, 0, 0.6);
            font-weight: 500;
        }
        /* Selected Items */
        .selected-items-container {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }
        .selected-item {
            background: #E3F2FD;
            color: var(--primary-variant);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .selected-item .material-icons {
            font-size: 14px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.2s;
        }
        .selected-item .material-icons:hover {
            opacity: 1;
        }
        /* Action Buttons */
        .action-buttons {
            background: var(--surface);
            padding: 16px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            max-width: 400px;
            margin: 0 auto;
            display: flex;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        .outlined-button {
            flex: 1;
            height: 36px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .outlined-button:hover {
            background: rgba(25, 118, 210, 0.04);
        }
        .outlined-button:active {
            background: rgba(25, 118, 210, 0.12);
        }
        .contained-button {
            flex: 2;
            height: 36px;
            border: none;
            border-radius: 4px;
            background: var(--primary);
            color: var(--on-primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--elevation-1);
        }
        .contained-button:hover {
            box-shadow: var(--elevation-2);
        }
        .contained-button:active {
            box-shadow: var(--elevation-3);
        }
        /* Scrollbar */
        .content-container::-webkit-scrollbar {
            width: 2px;
        }
        .content-container::-webkit-scrollbar-track {
            background: transparent;
        }
        .content-container::-webkit-scrollbar-thumb {
            background: #E0E0E0;
            border-radius: 2px;
        }
        /* Ripple Effect */
        .ripple {
            position: relative;
            overflow: hidden;
        }
        .ripple::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }
        .ripple:active::before {
            width: 200px;
            height: 200px;
        }
        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .filter-section {
            animation: fadeInUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Material App Bar -->
        <div class="app-bar">
            <div class="app-bar-content">
                <div class="app-bar-leading">
                    <button class="icon-button ripple" onclick="goBack()">
                        <span class="material-icons">arrow_back</span>
                    </button>
                </div>
                <h1 class="app-bar-title">Lọc nâng cao</h1>
                <div class="app-bar-actions">
                    <button class="icon-button ripple" onclick="resetFilters()">
                        <span class="material-icons">refresh</span>
                    </button>
                    <div class="badge" id="filterCount" style="display: none;">0</div>
                </div>
            </div>
        </div>
        <!-- Content Container -->
        <div class="content-container">
            <!-- Quick Filters Section -->
            <div class="filter-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons">flash_on</span>
                        Lọc nhanh
                    </div>
                    <div class="section-count" id="quickFilterCount">0</div>
                </div>
                <div class="section-content">
                    <div class="quick-filter-container">
                        <div class="filter-chip ripple" data-filter="priority">
                            <span class="material-icons">star</span>
                            Ưu tiên
                        </div>
                        <div class="filter-chip ripple" data-filter="vip">
                            <span class="material-icons">diamond</span>
                            VIP
                        </div>
                        <div class="filter-chip ripple" data-filter="urgent">
                            <span class="material-icons">priority_high</span>
                            Gấp
                        </div>
                        <div class="filter-chip ripple" data-filter="new">
                            <span class="material-icons">fiber_new</span>
                            Mới
                        </div>
                        <div class="filter-chip ripple" data-filter="hot">
                            <span class="material-icons">whatshot</span>
                            Hot
                        </div>
                        <div class="filter-chip ripple" data-filter="pending">
                            <span class="material-icons">schedule</span>
                            Chờ duyệt
                        </div>
                        <div class="filter-chip ripple" data-filter="approved">
                            <span class="material-icons">check_circle</span>
                            Đã duyệt
                        </div>
                        <div class="filter-chip ripple" data-filter="rejected">
                            <span class="material-icons">cancel</span>
                            Từ chối
                        </div>
                    </div>
                </div>
            </div>
            <!-- Time Range Section -->
            <div class="filter-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons">date_range</span>
                        Thời gian
                    </div>
                    <div class="section-count" id="timeFilterCount">1</div>
                </div>
                <div class="section-content">
                    <div class="time-range-container">
                        <button class="time-range-button selected ripple" data-time="today">Hôm nay</button>
                        <button class="time-range-button ripple" data-time="7days">7 ngày</button>
                        <button class="time-range-button ripple" data-time="month">Tháng này</button>
                        <button class="time-range-button ripple" data-time="quarter">Quý này</button>
                    </div>                    
                    <div class="form-field" style="margin-top: 16px;">
                        <label class="form-label">Khoảng thời gian tùy chọn</label>
                        <div class="date-range-container">
                            <input type="date" class="date-input" id="fromDate" placeholder="Từ ngày">
                            <span class="date-separator">→</span>
                            <input type="date" class="date-input" id="toDate" placeholder="Đến ngày">
                        </div>
                    </div>
                </div>
            </div>
            <!-- Product Section -->
            <div class="filter-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons">inventory_2</span>
                        Sản phẩm
                    </div>
                    <div class="section-count" id="productFilterCount">0</div>
                </div>
                <div class="section-content">
                    <div class="form-field">
                        <label class="form-label">Loại sản phẩm</label>
                        <div class="text-field">
                            <input type="text" class="text-input" placeholder="Chọn loại sản phẩm..." id="productType" readonly>
                            <span class="material-icons input-icon">expand_more</span>
                        </div>
                        <div class="selected-items-container" id="selectedProducts">
                            <div class="selected-item">
                                Vay trả góp ngày
                                <span class="material-icons" onclick="removeSelectedItem(this)">close</span>
                            </div>
                            <div class="selected-item">
                                Vay tiêu dùng
                                <span class="material-icons" onclick="removeSelectedItem(this)">close</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Location Section -->
            <div class="filter-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons">location_on</span>
                        Khu vực
                    </div>
                    <div class="section-count" id="locationFilterCount">2</div>
                </div>
                <div class="section-content">
                    <div class="form-field">
                        <label class="form-label">Tỉnh/Thành phố</label>
                        <div class="text-field">
                            <input type="text" class="text-input" placeholder="Tìm kiếm tỉnh/thành phố..." id="province">
                            <span class="material-icons input-icon">search</span>
                        </div>
                        <div class="selected-items-container" id="selectedProvinces">
                            <div class="selected-item">
                                Hà Nội
                                <span class="material-icons" onclick="removeSelectedItem(this)">close</span>
                            </div>
                            <div class="selected-item">
                                TP. Hồ Chí Minh
                                <span class="material-icons" onclick="removeSelectedItem(this)">close</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-field">
                        <label class="form-label">Chi nhánh</label>
                        <div class="text-field">
                            <input type="text" class="text-input" placeholder="Tìm kiếm chi nhánh..." id="branch">
                            <span class="material-icons input-icon">search</span>
                        </div>
                        <div class="selected-items-container" id="selectedBranches">
                            <div class="selected-item">
                                CN Đống Đa
                                <span class="material-icons" onclick="removeSelectedItem(this)">close</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Staff Section -->
            <div class="filter-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons">people</span>
                        Nhân viên
                    </div>
                    <div class="section-count" id="staffFilterCount">1</div>
                </div>
                <div class="section-content">
                    <div class="form-field">
                        <label class="form-label">Cán bộ phụ trách</label>
                        <div class="text-field">
                            <input type="text" class="text-input" placeholder="Tìm kiếm sale..." id="saleStaff">
                            <span class="material-icons input-icon">search</span>
                        </div>
                        <div class="selected-items-container" id="selectedSales">
                            <div class="selected-item">
                                Nguyễn Văn A
                                <span class="material-icons" onclick="removeSelectedItem(this)">close</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Amount Range Section -->
        </div>
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="outlined-button ripple" onclick="resetFilters()">
                Xóa lọc
            </button>
            <button class="contained-button ripple" onclick="applyFilters()">
                Áp dụng
            </button>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Material Design Ripple Effect
            function createRipple(event) {
                const button = event.currentTarget;
                const circle = document.createElement('span');
                const diameter = Math.max(button.clientWidth, button.clientHeight);
                const radius = diameter / 2;
                circle.style.cssText = `
                    width: ${diameter}px;
                    height: ${diameter}px;
                    left: ${event.clientX - button.offsetLeft - radius}px;
                    top: ${event.clientY - button.offsetTop - radius}px;
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.3);
                    animation: ripple-animation 0.6s linear;
                    pointer-events: none;
                `;
                const ripple = button.querySelector('.ripple-effect');
                if (ripple) ripple.remove();
                button.appendChild(circle);
                circle.classList.add('ripple-effect');
                setTimeout(() => circle.remove(), 600);
            }
            // Add ripple effect to buttons
            document.querySelectorAll('.ripple').forEach(button => {
                button.addEventListener('click', createRipple);
            });
            // Quick filter chips
            const filterChips = document.querySelectorAll('.filter-chip');
            filterChips.forEach(chip => {
                chip.addEventListener('click', function() {
                    this.classList.toggle('selected');
                    updateFilterCounts();
                });
            });
            // Time range buttons
            const timeButtons = document.querySelectorAll('.time-range-button');
            timeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    timeButtons.forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                    updateFilterCounts();
                });
            });
            // Date inputs
            const dateInputs = document.querySelectorAll('.date-input');
            dateInputs.forEach(input => {
                input.addEventListener('change', function() {
                    updateFilterCounts();
                });
            });
            // Text inputs
            const textInputs = document.querySelectorAll('.text-input');
            textInputs.forEach(input => {
                input.addEventListener('input', function() {
                    updateFilterCounts();
                });
            });
            // Amount inputs
            const amountInputs = document.querySelectorAll('#fromAmount, #toAmount');
            amountInputs.forEach(input => {
                input.addEventListener('input', function() {
                    updateFilterCounts();
                });
            });
            function updateFilterCounts() {
                // Quick filters count
                const selectedQuickFilters = document.querySelectorAll('.filter-chip.selected').length;
                document.getElementById('quickFilterCount').textContent = selectedQuickFilters;
                // Time filter count
                const selectedTimeButton = document.querySelector('.time-range-button.selected');
                const fromDate = document.getElementById('fromDate').value;
                const toDate = document.getElementById('toDate').value;
                let timeCount = 0;
                if (selectedTimeButton && selectedTimeButton.dataset.time !== 'today') timeCount++;
                if (fromDate || toDate) timeCount++;
                document.getElementById('timeFilterCount').textContent = timeCount;
                // Product filter count
                const selectedProducts = document.querySelectorAll('#selectedProducts .selected-item').length;
                const productType = document.getElementById('productType').value;
                const productPackage = document.getElementById('productPackage').value;
                let productCount = selectedProducts;
                if (productType) productCount++;
                if (productPackage) productCount++;
                document.getElementById('productFilterCount').textContent = productCount;
                // Location filter count
                const selectedProvinces = document.querySelectorAll('#selectedProvinces .selected-item').length;
                const selectedBranches = document.querySelectorAll('#selectedBranches .selected-item').length;
                const district = document.getElementById('district').value;
                let locationCount = selectedProvinces + selectedBranches;
                if (district) locationCount++;
                document.getElementById('locationFilterCount').textContent = locationCount;
                // Staff filter count
                const selectedSales = document.querySelectorAll('#selectedSales .selected-item').length;
                const teamLead = document.getElementById('teamLead').value;
                let staffCount = selectedSales;
                if (teamLead) staffCount++;
                document.getElementById('staffFilterCount').textContent = staffCount;
                // Amount filter count
                const fromAmount = document.getElementById('fromAmount').value;
                const toAmount = document.getElementById('toAmount').value;
                let amountCount = 0;
                if (fromAmount || toAmount) amountCount = 1;
                document.getElementById('amountFilterCount').textContent = amountCount;
                // Total filter count
                const totalCount = selectedQuickFilters + timeCount + productCount + locationCount + staffCount + amountCount;
                const filterCountBadge = document.getElementById('filterCount');
                if (totalCount > 0) {
                    filterCountBadge.textContent = totalCount;
                    filterCountBadge.style.display = 'flex';
                } else {
                    filterCountBadge.style.display = 'none';
                }
            }
            // Initialize filter counts
            updateFilterCounts();
        });
        function removeSelectedItem(element) {
            element.parentElement.remove();
            updateFilterCounts();
        }
        function goBack() {
            alert('Quay lại màn hình trước');
            // In real app: Navigator.pop()
        }
        function resetFilters() {
            // Reset quick filters
            document.querySelectorAll('.filter-chip.selected').forEach(chip => {
                chip.classList.remove('selected');
            });
            // Reset time range
            document.querySelectorAll('.time-range-button').forEach(btn => {
                btn.classList.remove('selected');
            });
            document.querySelector('.time-range-button[data-time="today"]').classList.add('selected');
            // Reset date inputs
            document.getElementById('fromDate').value = '';
            document.getElementById('toDate').value = '';
            // Reset text inputs
            document.querySelectorAll('.text-input').forEach(input => {
                input.value = '';
            });
            // Reset amount inputs
            document.getElementById('fromAmount').value = '';
            document.getElementById('toAmount').value = '';
            // Reset selected items
            document.querySelectorAll('.selected-items-container').forEach(container => {
                container.innerHTML = '';
            });
            updateFilterCounts();
            alert('Đã xóa tất cả bộ lọc');
        }
        function applyFilters() {
            // Collect all filter data
            const filterData = {
                quickFilters: Array.from(document.querySelectorAll('.filter-chip.selected')).map(chip => chip.dataset.filter),
                timeRange: document.querySelector('.time-range-button.selected')?.dataset.time,
                customDateFrom: document.getElementById('fromDate').value,
                customDateTo: document.getElementById('toDate').value,
                productType: document.getElementById('productType').value,
                productPackage: document.getElementById('productPackage').value,
                province: document.getElementById('province').value,
                district: document.getElementById('district').value,
                branch: document.getElementById('branch').value,
                saleStaff: document.getElementById('saleStaff').value,
                teamLead: document.getElementById('teamLead').value,
                fromAmount: document.getElementById('fromAmount').value,
                toAmount: document.getElementById('toAmount').value,
                selectedItems: {
                    products: Array.from(document.querySelectorAll('#selectedProducts .selected-item')).map(item => item.textContent.replace('close', '').trim()),
                    provinces: Array.from(document.querySelectorAll('#selectedProvinces .selected-item')).map(item => item.textContent.replace('close', '').trim()),
                    branches: Array.from(document.querySelectorAll('#selectedBranches .selected-item')).map(item => item.textContent.replace('close', '').trim()),
                    sales: Array.from(document.querySelectorAll('#selectedSales .selected-item')).map(item => item.textContent.replace('close', '').trim())
                }
            };
            console.log('Applying filters:', filterData);
            alert('Áp dụng bộ lọc thành công!\nKết quả được hiển thị trong console.');
            // In real app: send filterData to API and navigate back with results
        }
        // Add ripple effect styles dynamically
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

----

### Mô tả màn hình 

| STT | Trường thông tin          | Kiểu hiển thị   | Kiểu thao tác | Mô tả chi tiết                                                                                                                                                   |
| --- | ------------------------- | --------------- | ------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | Lọc nhanh                 | Button          | Click         | Với tiêu chí lọc nhanh hiển thị danh sách các tag để tìm kiếm nhanh giao dịch                                                                                    |
| 2   | Thời gian                 | Group Button    | Click         | Với tiêu chí lọc Ngày tạo giao dịch hiển thị group button **Hôm nay** ; **7 ngày** ; **Tháng này** ; **3 tháng**                                                 |
| 3   | Khoảng thời gian tuỳ chọn | Datetime        | Click         | cho phép chọn khoảng thời gian từ ngày đến ngày cập nhật giao dịch                                                                                               |
| 4   | Sản phẩm                  | group Button    | Click         | Hiển thị danh sách tên sản phẩm sale. Nếu không chọn mặc định lọc theo trạng thái chung. Nếu chọn 1 sản phẩm thì cho phép lọc theo trạng thái của từng sản phẩm. |
| 5   | Khu vực                   | Droplist/search | Click         | Chọn 1 hoặc nhiều khu vực, tìm kiếm gần đúng. mặc định trống                                                                                                     |
| 6   | Đơn vị kinh doanh         | Droplist/search | Click         | Chọn 1 hoặc nhiều ĐVKD thuộc khu vực, tìm kiếm gần đúng. mặc định trống                                                                                          |
| 7   | Cán bộ phụ trách hồ sơ    | Droplist/search | Click         | Chọn 1 hoặc nhiều cán bộ phụ trách, tìm kiếm gần đúng. mặc định trống                                                                                            |


> **Lưu ý:**
> - Cấp Toàn hàng: Có thể chọn nhiều khu vực, nhiều ĐVKD, nhiều chức danh, tìm kiếm nhanh.
> - Cấp Khu vực: Khu vực được mặc định, không cho sửa; ĐVKD chọn nhiều trong khu vực đó, chức danh chọn nhiều, tìm kiếm nhanh.
> - Cấp ĐVKD: Khu vực và ĐVKD được mặc định, không cho sửa; chức danh chọn nhiều, tìm kiếm nhanh.
> - Các trường dropdown cho phép tìm kiếm nhanh khi có nhiều dữ liệu.
> - Nút "Áp dụng" chỉ thực hiện khi có thay đổi điều kiện lọc.
> - Nút "Đặt lại" giúp xóa toàn bộ điều kiện lọc đã chọn, trả về trạng thái mặc định.

---
# 2. Thêm mới Giao dịch

Mô tả khung chung cho chức năng tạo mới giao dịch. chức năng tạo giao dịch dùng chung cho tất cả các sản phẩm, và tạo giao dịch cho tất cả khách hàng. 

Mỗi giao dịch sẽ cần hoàn thành 5 step sau:
- Step 1: Chọn Sản phẩm
- Step 2: Chọn khách hàng
- Step 3: Thông tin chi tiết sản phẩm
- Step 4: Tài liệu yêu cầu
- Step 5: Xem lại và xác nhận

Trong đó Mỗi sản phẩm được chọn tại Step 1 sẽ có các thông tin chi tiết khác nhau tại Step 3 và các yêu cầu tài liệu khác nhau tại Step 4.

## 2.1 Mô tả UI UX màn hình thêm mới giao dịch từ màn hình Danh sách giao dịch

#### UI/UX Step 1:

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Thêm mới giao dịch - Bước 1/5</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #212121;
            max-width: 1500px;
            margin: 0 auto;
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;
            border: 1px solid #e0e0e0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        /* Material Design Variables */
        :root {
            --primary: #FF5722;
            --primary-variant: #E64A19;
            --secondary: #03DAC6;
            --background: #FAFAFA;
            --surface: #FFFFFF;
            --error: #B00020;
            --warning: #FF9800;
            --success: #4CAF50;
            --on-primary: #FFFFFF;
            --on-surface: #000000;
            --on-background: #000000;
            --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
        }
        /* App Container */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        /* Status Bar */
        .status-bar {
            height: 24px;
            background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        /* Material App Bar */
        .app-bar {
            background: var(--primary);
            color: var(--on-primary);
            min-height: 56px;
            box-shadow: var(--elevation-2);
            position: relative;
            z-index: 10;
        }
        .app-bar-content {
            display: flex;
            align-items: center;
            min-height: 56px;
            padding: 0 16px;
        }
        .app-bar-leading {
            margin-right: 16px;
        }
        .app-bar-title {
            font-size: 18px;
            font-weight: 500;
            line-height: 24px;
            flex: 1;
        }
        .app-bar-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .icon-button {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            border: none;
            background: transparent;
            color: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .icon-button:hover {
            background: rgba(255, 255, 255, 0.08);
        }
        /* Progress Indicator */
        .progress-container {
            background: var(--surface);
            padding: 16px;
            box-shadow: var(--elevation-1);
        }
        .progress-indicators {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 16px;
        }
        .step-indicator {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #E0E0E0;
            color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }
        .step-indicator.active {
            background: var(--primary);
            color: white;
        }
        .step-indicator.completed {
            background: var(--success);
            color: white;
        }
        .step-label {
            position: absolute;
            top: 36px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: rgba(0, 0, 0, 0.6);
            white-space: nowrap;
            text-align: center;
        }
        .step-indicator.active .step-label {
            color: var(--primary);
            font-weight: 500;
        }
        /* Content Container */
        .content-container {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 24px 16px;
            padding-bottom: 100px;
        }
        /* Header Section */
        .header-section {
            text-align: center;
            margin-bottom: 32px;
        }
        .header-title {
            font-size: 24px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            margin-bottom: 8px;
        }
        .header-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            line-height: 20px;
        }
        /* Search Section */
        .search-section {
            margin-bottom: 24px;
        }
        .search-container {
            position: relative;
        }
        .search-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px 12px 48px;
            border: 1px solid rgba(0, 0, 0, 0.38);
            border-radius: 24px;
            font-size: 16px;
            font-family: 'Roboto', sans-serif;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            transition: border-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            border-width: 2px;
            padding-left: 47px;
        }
        .search-input::placeholder {
            color: rgba(0, 0, 0, 0.6);
        }
        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 0.54);
            font-size: 20px;
        }
        /* Filter Chips */
        .filter-chips {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            padding-bottom: 4px;
        }
        .filter-chips::-webkit-scrollbar {
            display: none;
        }
        .filter-chip {
            height: 32px;
            padding: 0 16px;
            border-radius: 16px;
            border: 1px solid #E0E0E0;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            font-size: 14px;
            font-weight: 400;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            white-space: nowrap;
            flex-shrink: 0;
        }
        .filter-chip.selected {
            background: var(--primary);
            color: var(--on-primary);
            border-color: var(--primary);
        }
        .filter-chip:hover:not(.selected) {
            background: rgba(0, 0, 0, 0.04);
        }
        /* Product Grid */
        .product-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        .product-card {
            background: var(--surface);
            border-radius: 12px;
            box-shadow: var(--elevation-1);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            position: relative;
            border: 2px solid transparent;
        }
        .product-card:hover {
            box-shadow: var(--elevation-2);
        }
        .product-card.selected {
            border-color: var(--primary);
            box-shadow: 0 4px 12px rgba(255, 87, 34, 0.15);
        }
        .product-icon {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
            font-size: 40px;
            color: var(--primary);
        }
        .product-card.selected .product-icon {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-variant) 100%);
            color: white;
        }
        .product-info {
            padding: 16px;
        }
        .product-name {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            margin-bottom: 4px;
            line-height: 20px;
        }
        .product-description {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
            line-height: 16px;
        }
        .product-features {
            margin-top: 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        .feature-tag {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            background: #F5F5F5;
            color: rgba(0, 0, 0, 0.6);
        }
        .product-card.selected .feature-tag {
            background: rgba(255, 255, 255, 0.2);
            color: rgba(0, 0, 0, 0.87);
        }
        /* Selection Indicator */
        .selection-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            border-radius: 10px;
            background: var(--primary);
            color: white;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .product-card.selected .selection-indicator {
            display: flex;
        }
        /* Action Buttons */
        .action-buttons {
            background: var(--surface);
            padding: 16px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            max-width: 400px;
            margin: 0 auto;
            display: flex;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        .outlined-button {
            flex: 1;
            height: 40px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .outlined-button:hover {
            background: rgba(255, 87, 34, 0.04);
        }
        .contained-button {
            flex: 2;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: var(--primary);
            color: var(--on-primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--elevation-1);
        }
        .contained-button:hover {
            box-shadow: var(--elevation-2);
        }
        .contained-button:disabled {
            background: rgba(0, 0, 0, 0.12);
            color: rgba(0, 0, 0, 0.26);
            box-shadow: none;
            cursor: not-allowed;
        }
        /* Ripple Effect */
        .ripple {
            position: relative;
            overflow: hidden;
        }
        .ripple::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }
        .ripple:active::before {
            width: 200px;
            height: 200px;
        }
        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .product-card {
            animation: fadeInUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .product-card:nth-child(1) { animation-delay: 0.05s; }
        .product-card:nth-child(2) { animation-delay: 0.1s; }
        .product-card:nth-child(3) { animation-delay: 0.15s; }
        .product-card:nth-child(4) { animation-delay: 0.2s; }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span>04:48</span>
                <span>📶</span>
            </div>
            <div class="status-right">
                <span>🔋</span>
                <span>68</span>
            </div>
        </div>
        <!-- Material App Bar -->
        <div class="app-bar">
            <div class="app-bar-content">
                <div class="app-bar-leading">
                    <button class="icon-button ripple" onclick="goBack()">
                        <span class="material-icons">arrow_back</span>
                    </button>
                </div>
                <div class="app-bar-title">Tạo giao dịch mới</div>
                <div class="app-bar-actions">
                    <button class="icon-button ripple" onclick="saveBookmark()">
                        <span class="material-icons">bookmark_border</span>
                    </button>
                </div>
            </div>
        </div>
        <!-- Progress Indicator -->
        <div class="progress-container">
            <div class="progress-indicators">
                <div class="step-indicator active">
                    1
                    <div class="step-label">Chọn sản<br>phẩm</div>
                </div>
                <div class="step-indicator">
                    2
                    <div class="step-label">Chọn khách<br>hàng</div>
                </div>
                <div class="step-indicator">
                    3
                    <div class="step-label">Chi tiết sản<br>phẩm</div>
                </div>
            </div>
        </div>
        <!-- Content Container -->
        <div class="content-container">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-title">Chọn loại sản phẩm bạn muốn tạo giao dịch</div>
            </div>
            <!-- Search Section -->
            <div class="search-section">
                <div class="search-container">
                    <span class="material-icons search-icon">search</span>
                    <input type="text" class="search-input" placeholder="Tìm kiếm sản phẩm..." id="productSearch">
                </div>
            </div>
            <!-- Filter Chips -->
            <div class="filter-chips">
                <div class="filter-chip selected ripple" data-filter="all">Tất cả</div>
                <div class="filter-chip ripple" data-filter="popular">Phổ biến</div>
                <div class="filter-chip ripple" data-filter="tín dụng doanh nghiệp">Tín dụng doanh nghiệp</div>
                <div class="filter-chip ripple" data-filter="thẻ">Thẻ</div>
            </div>
            <!-- Product Grid -->
            <div class="product-grid">
                <!-- Hạn mức tín dụng -->
                <div class="product-card ripple" data-product="han-muc-tin-dung">
                    <div class="product-icon">
                        💳
                    </div>
                    <div class="product-info">
                        <div class="product-name">Hạn mức tín dụng</div>
                        <div class="product-description">Tín dụng doanh nghiệp<br>Hạn mức tín dụng<br>quay vòng</div>
                        <div class="product-features">
                            <div class="feature-tag">✓</div>
                        </div>
                    </div>
                    <div class="selection-indicator">
                        <span class="material-icons" style="font-size: 12px;">check</span>
                    </div>
                </div>
                <!-- Vay cầm vàng -->
                <div class="product-card selected ripple" data-product="vay-cam-vang">
                    <div class="product-icon">
                        🏆
                    </div>
                    <div class="product-info">
                        <div class="product-name">Vay cầm vàng</div>
                        <div class="product-description">Tín dụng tiêu dùng<br>Vay dưới hình thức<br>cầm vàng</div>
                        <div class="product-features"></div>
                    </div>
                    <div class="selection-indicator">
                        <span class="material-icons" style="font-size: 12px;">check</span>
                    </div>
                </div>
                <!-- Thẻ tín dụng cá nhân -->
                <div class="product-card ripple" data-product="the-tin-dung">
                    <div class="product-icon">
                        💳
                    </div>
                    <div class="product-info">
                        <div class="product-name">Thẻ tín dụng cá nhân</div>
                        <div class="product-description">Thẻ<br>Thẻ tín dụng<br>cá nhân</div>
                        <div class="product-features"></div>
                    </div>
                    <div class="selection-indicator">
                        <span class="material-icons" style="font-size: 12px;">check</span>
                    </div>
                </div>
                <!-- Sản phẩm khác -->
                <div class="product-card ripple" data-product="khac">
                    <div class="product-icon">
                        📋
                    </div>
                    <div class="product-info">
                        <div class="product-name">Khác</div>
                        <div class="product-description">Các sản phẩm<br>khác</div>
                        <div class="product-features"></div>
                    </div>
                    <div class="selection-indicator">
                        <span class="material-icons" style="font-size: 12px;">check</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Action Buttons -->
      <div class="action-buttons">
            <button type="button" class="contained-button ripple" onclick="nextStep()" id="nextButton" >
                Tiếp tục
            </button>
      </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const productCards = document.querySelectorAll('.product-card');
            const nextButton = document.getElementById('nextButton');
            const filterChips = document.querySelectorAll('.filter-chip');
            const searchInput = document.getElementById('productSearch');
            let selectedProduct = 'vay-cam-vang'; // Default selected
            updateNextButton();
            // Product selection
            productCards.forEach(card => {
                card.addEventListener('click', function() {
                    productCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedProduct = this.dataset.product;
                    updateNextButton();
                });
            });
            // Filter chips
            filterChips.forEach(chip => {
                chip.addEventListener('click', function() {
                    filterChips.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    filterProducts(this.dataset.filter);
                });
            });
            // Search functionality
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                productCards.forEach(card => {
                    const productName = card.querySelector('.product-name').textContent.toLowerCase();
                    const productDesc = card.querySelector('.product-description').textContent.toLowerCase();                    
                    if (productName.includes(searchTerm) || productDesc.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
            function filterProducts(filter) {
                productCards.forEach(card => {
                    const productDesc = card.querySelector('.product-description').textContent.toLowerCase();                  
                    if (filter === 'all') {
                        card.style.display = 'block';
                    } else if (filter === 'popular') {
                        // Show popular products
                        if (card.dataset.product === 'vay-cam-vang' || card.dataset.product === 'han-muc-tin-dung') {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    } else if (productDesc.includes(filter)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            }
            function updateNextButton() {
                nextButton.disabled = !selectedProduct;
            }
            // Ripple effect
            function createRipple(event) {
                const button = event.currentTarget;
                const circle = document.createElement('span');
                const diameter = Math.max(button.clientWidth, button.clientHeight);
                const radius = diameter / 2;
                circle.style.cssText = `
                    width: ${diameter}px;
                    height: ${diameter}px;
                    left: ${event.clientX - button.offsetLeft - radius}px;
                    top: ${event.clientY - button.offsetTop - radius}px;
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.3);
                    animation: ripple-animation 0.6s linear;
                    pointer-events: none;
                `;
                const ripple = button.querySelector('.ripple-effect');
                if (ripple) ripple.remove();
                button.appendChild(circle);
                circle.classList.add('ripple-effect');
                setTimeout(() => circle.remove(), 600);
            }
            document.querySelectorAll('.ripple').forEach(button => {
                button.addEventListener('click', createRipple);
            });
        });
        function goBack() {
            if (confirm('Bạn có chắc chắn muốn quay lại? Dữ liệu chưa lưu sẽ bị mất.')) {
                alert('Quay lại màn hình danh sách giao dịch');
            }
        }
        function saveBookmark() {
            alert('Đã lưu bookmark');
        }
        function nextStep() {
            const selectedCard = document.querySelector('.product-card.selected');
            if (selectedCard) {
                const productName = selectedCard.querySelector('.product-name').textContent;
                localStorage.setItem('selectedProduct', selectedCard.dataset.product);
                alert(`Đã chọn sản phẩm: ${productName}\nChuyển sang bước 2 - Chọn khách hàng`);
                // In real app: navigate to step 2
            }
        }
        // Add ripple effect styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

---
### UIUX step 2

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Thêm mới giao dịch - Bước 2/5</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #212121;
            max-width: 1500px;
            margin: 0 auto;
            position: relative;
            overflow-x: hidden;
            min-height: 100vh;
            border: 1px solid #e0e0e0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        /* Material Design Variables */
        :root {
            --primary: #FF5722;
            --primary-variant: #E64A19;
            --secondary: #03DAC6;
            --background: #FAFAFA;
            --surface: #FFFFFF;
            --error: #B00020;
            --warning: #FF9800;
            --success: #4CAF50;
            --on-primary: #FFFFFF;
            --on-surface: #000000;
            --on-background: #000000;
            --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
        }
        /* App Container */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        /* Status Bar */
        .status-bar {
            height: 24px;
            background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        /* Material App Bar */
        .app-bar {
            background: var(--primary);
            color: var(--on-primary);
            min-height: 56px;
            box-shadow: var(--elevation-2);
            position: relative;
            z-index: 10;
        }
        .app-bar-content {
            display: flex;
            align-items: center;
            min-height: 56px;
            padding: 0 16px;
        }
        .app-bar-leading {
            margin-right: 16px;
        }
        .app-bar-title {
            font-size: 18px;
            font-weight: 500;
            line-height: 24px;
            flex: 1;
        }
        .app-bar-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .icon-button {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            border: none;
            background: transparent;
            color: inherit;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .icon-button:hover {
            background: rgba(255, 255, 255, 0.08);
        }
        /* Progress Indicator */
        .progress-container {
            background: var(--surface);
            padding: 16px;
            box-shadow: var(--elevation-1);
        }
        .progress-indicators {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 16px;
        }
        .step-indicator {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #E0E0E0;
            color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }
        .step-indicator.completed {
            background: var(--success);
            color: white;
        }
        .step-indicator.active {
            background: var(--primary);
            color: white;
        }
        .step-label {
            position: absolute;
            top: 36px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: rgba(0, 0, 0, 0.6);
            white-space: nowrap;
            text-align: center;
        }
        .step-indicator.active .step-label {
            color: var(--primary);
            font-weight: 500;
        }
        .step-indicator.completed .step-label {
            color: var(--success);
            font-weight: 500;
        }
        /* Content Container */
        .content-container {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 24px 16px;
            padding-bottom: 100px;
        }
        /* Header Section */
        .header-section {
            margin-bottom: 24px;
        }
        .header-title {
            font-size: 16px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.6);
            line-height: 20px;
        }
        /* Search Section */
        .search-section {
            margin-bottom: 24px;
        }
        .search-container {
            position: relative;
        }
        .search-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px 12px 48px;
            border: 1px solid rgba(0, 0, 0, 0.38);
            border-radius: 24px;
            font-size: 16px;
            font-family: 'Roboto', sans-serif;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            transition: border-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            border-width: 2px;
            padding-left: 47px;
        }
        .search-input::placeholder {
            color: rgba(0, 0, 0, 0.6);
        }
        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(0, 0, 0, 0.54);
            font-size: 20px;
        }
        /* Action Cards */
        .action-cards {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }
        .action-card {
            flex: 1;
            background: var(--surface);
            border-radius: 12px;
            box-shadow: var(--elevation-1);
            padding: 20px 16px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            text-align: center;
        }
        .action-card:hover {
            box-shadow: var(--elevation-2);
        }
        .action-card.scan-qr {
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
        }
        .action-card.new-customer {
            background: linear-gradient(135deg, #FCE4EC 0%, #F8BBD9 100%);
        }
        .action-icon {
            font-size: 32px;
            margin-bottom: 8px;
            display: block;
        }
        .scan-qr .action-icon {
            color: #1976D2;
        }
        .new-customer .action-icon {
            color: #E91E63;
        }
        .action-title {
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
        }
        /* Recent Customers Section */
        .recent-section {
            margin-bottom: 24px;
        }
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            display: flex;
            align-items: center;
        }
        .section-title .material-icons {
            margin-right: 8px;
            color: rgba(0, 0, 0, 0.54);
            font-size: 20px;
        }
        .section-count {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
        }
        /* Customer Card */
        .customer-card {
            background: var(--surface);
            border-radius: 12px;
            box-shadow: var(--elevation-1);
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            position: relative;
            border: 2px solid transparent;
        }
        .customer-card:hover {
            box-shadow: var(--elevation-2);
        }
        .customer-card.selected {
            border-color: var(--primary);
            box-shadow: 0 4px 12px rgba(255, 87, 34, 0.15);
        }
        .customer-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .customer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #1976D2;
            font-size: 18px;
        }
        .customer-info {
            flex: 1;
        }
        .customer-name {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            margin-bottom: 2px;
        }
        .customer-type {
            display: inline-block;
            background: #FFF3E0;
            color: #F57C00;
            font-size: 10px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 8px;
            text-transform: uppercase;
        }
        .customer-details {
            margin-left: 52px;
        }
        .customer-detail {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
        }
        .customer-detail:last-child {
            margin-bottom: 0;
        }
        .customer-detail .material-icons {
            font-size: 16px;
            margin-right: 8px;
            color: rgba(0, 0, 0, 0.54);
        }
        .verification-badge {
            background: #E8F5E8;
            color: #2E7D32;
            font-size: 10px;
            font-weight: 500;
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: 8px;
        }
        /* Selection Indicator */
        .selection-indicator {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 20px;
            height: 20px;
            border-radius: 10px;
            background: var(--primary);
            color: white;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .customer-card.selected .selection-indicator {
            display: flex;
        }
        /* Action Buttons */
        .action-buttons {
            background: var(--surface);
            padding: 16px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            max-width: 400px;
            margin: 0 auto;
            display: flex;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        .outlined-button {
            flex: 1;
            height: 40px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .outlined-button:hover {
            background: rgba(255, 87, 34, 0.04);
        }
        .contained-button {
            flex: 2;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: var(--primary);
            color: var(--on-primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--elevation-1);
        }
        .contained-button:hover {
            box-shadow: var(--elevation-2);
        }
        .contained-button:disabled {
            background: rgba(0, 0, 0, 0.12);
            color: rgba(0, 0, 0, 0.26);
            box-shadow: none;
            cursor: not-allowed;
        }
        /* Ripple Effect */
        .ripple {
            position: relative;
            overflow: hidden;
        }
        .ripple::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
        }
        .ripple:active::before {
            width: 200px;
            height: 200px;
        }
        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .customer-card {
            animation: fadeInUp 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }
        .customer-card:nth-child(1) { animation-delay: 0.05s; }
        .customer-card:nth-child(2) { animation-delay: 0.1s; }
        .customer-card:nth-child(3) { animation-delay: 0.15s; }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span>05:09</span>
                <span>📶</span>
            </div>
            <div class="status-right">
                <span>🔋</span>
                <span>77</span>
            </div>
        </div>
        <!-- Material App Bar -->
        <div class="app-bar">
            <div class="app-bar-content">
                <div class="app-bar-leading">
                    <button class="icon-button ripple" onclick="goBack()">
                        <span class="material-icons">arrow_back</span>
                    </button>
                </div>
                <div class="app-bar-title">Tạo giao dịch mới</div>
                <div class="app-bar-actions">
                    <button class="icon-button ripple" onclick="saveBookmark()">
                        <span class="material-icons">bookmark_border</span>
                    </button>
                </div>
            </div>
        </div>
        <!-- Progress Indicator -->
        <div class="progress-container">
            <div class="progress-indicators">
                <div class="step-indicator completed">
                    <span class="material-icons" style="font-size: 16px;">check</span>
                    <div class="step-label">Chọn sản<br>phẩm</div>
                </div>
                <div class="step-indicator active">
                    2
                    <div class="step-label">Chọn khách<br>hàng</div>
                </div>
                <div class="step-indicator">
                    3
                    <div class="step-label">Chi tiết sản<br>phẩm</div>
                </div>
            </div>
        </div>
        <!-- Content Container -->
        <div class="content-container">
            <!-- Header Section -->
            <div class="header-section">
                <div class="header-title">Tìm kiếm hoặc chọn khách hàng có sẵn</div>
            </div>
            <!-- Search Section -->
            <div class="search-section">
                <div class="search-container">
                    <span class="material-icons search-icon">search</span>
                    <input type="text" class="search-input" placeholder="Tìm kiếm Họ tên, SĐT, Số GTTT" id="customerSearch">
                </div>
            </div>
            <!-- Quick Actions -->
            <div class="action-cards">
                <div class="action-card scan-qr ripple" onclick="scanQRCode()">
                    <span class="material-icons action-icon">qr_code_scanner</span>
                    <div class="action-title">Quét mã QR</div>
                </div>
                <div class="action-card new-customer ripple" onclick="createNewCustomer()">
                    <span class="material-icons action-icon">person_add</span>
                    <div class="action-title">Khách hàng mới</div>
                </div>
            </div>
            <!-- Recent Customers -->
            <div class="recent-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons">history</span>
                        Khách hàng gần đây
                    </div>
                    <div class="section-count">4 khách hàng</div>
                </div>
                <!-- Customer List -->
                <div class="customer-list">
                    <!-- Selected Customer -->
                    <div class="customer-card selected ripple" data-customer="lan-anh-2">
                        <div class="customer-header">
                            <div class="customer-avatar">
                                <span class="material-icons">person</span>
                            </div>
                            <div class="customer-info">
                                <div class="customer-name">Lan Anh 2</div>
                                <div class="customer-type">VIP</div>
                            </div>
                        </div>
                        <div class="customer-details">
                            <div class="customer-detail">
                                <span class="material-icons">phone</span>
                                0345467...
                                <span class="verification-badge">Đang chăm sóc</span>
                            </div>
                            <div class="customer-detail">
                                <span class="material-icons">business</span>
                                CBPT: Nguyễn Thị Lan Anh
                            </div>
                            <div class="customer-detail">
                                <span class="material-icons">location_on</span>
                                CN An Giang
                            </div>
                        </div>
                        <div class="selection-indicator">
                            <span class="material-icons" style="font-size: 12px;">check</span>
                        </div>
                    </div>
                    <!-- Other Customers -->
                    <div class="customer-card ripple" data-customer="nguyen-van-a">
                        <div class="customer-header">
                            <div class="customer-avatar">
                                <span class="material-icons">person</span>
                            </div>
                            <div class="customer-info">
                                <div class="customer-name">Nguyễn Văn A</div>
                                <div class="customer-type">Thường</div>
                            </div>
                        </div>
                        <div class="customer-details">
                            <div class="customer-detail">
                                <span class="material-icons">phone</span>
                                0987654321
                            </div>
                            <div class="customer-detail">
                                <span class="material-icons">business</span>
                                CBPT: Trần Văn B
                            </div>
                            <div class="customer-detail">
                                <span class="material-icons">location_on</span>
                                CN Hà Nội
                            </div>
                        </div>
                        <div class="selection-indicator">
                            <span class="material-icons" style="font-size: 12px;">check</span>
                        </div>
                    </div>
                    <div class="customer-card ripple" data-customer="le-thi-c">
                        <div class="customer-header">
                            <div class="customer-avatar">
                                <span class="material-icons">person</span>
                            </div>
                            <div class="customer-info">
                                <div class="customer-name">Lê Thị C</div>
                                <div class="customer-type">VIP</div>
                            </div>
                        </div>
                        <div class="customer-details">
                            <div class="customer-detail">
                                <span class="material-icons">phone</span>
                                0912345678
                                <span class="verification-badge">Đã xác minh</span>
                            </div>
                            <div class="customer-detail">
                                <span class="material-icons">business</span>
                                CBPT: Phạm Văn D
                            </div>
                            <div class="customer-detail">
                                <span class="material-icons">location_on</span>
                                CN TP.HCM
                            </div>
                        </div>
                        <div class="selection-indicator">
                            <span class="material-icons" style="font-size: 12px;">check</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button type="button" class="outlined-button ripple" onclick="goBack()">
                ← Quay lại
            </button>
            <button type="button" class="contained-button ripple" onclick="nextStep()" id="nextButton">
                → Tiếp tục
            </button>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const customerCards = document.querySelectorAll('.customer-card');
            const nextButton = document.getElementById('nextButton');
            const searchInput = document.getElementById('customerSearch');
            let selectedCustomer = 'lan-anh-2'; // Default selected
            // Customer selection
            customerCards.forEach(card => {
                card.addEventListener('click', function() {
                    customerCards.forEach(c => c.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedCustomer = this.dataset.customer;
                    updateNextButton();
                });
            });
            // Search functionality
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                customerCards.forEach(card => {
                    const customerName = card.querySelector('.customer-name').textContent.toLowerCase();
                    const customerPhone = card.querySelector('.customer-detail').textContent.toLowerCase();
                    if (customerName.includes(searchTerm) || customerPhone.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
            function updateNextButton() {
                nextButton.disabled = !selectedCustomer;
            }
            // Ripple effect
            function createRipple(event) {
                const button = event.currentTarget;
                const circle = document.createElement('span');
                const diameter = Math.max(button.clientWidth, button.clientHeight);
                const radius = diameter / 2;
                circle.style.cssText = `
                    width: ${diameter}px;
                    height: ${diameter}px;
                    left: ${event.clientX - button.offsetLeft - radius}px;
                    top: ${event.clientY - button.offsetTop - radius}px;
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.3);
                    animation: ripple-animation 0.6s linear;
                    pointer-events: none;
                `;
                const ripple = button.querySelector('.ripple-effect');
                if (ripple) ripple.remove();
                button.appendChild(circle);
                circle.classList.add('ripple-effect');
                setTimeout(() => circle.remove(), 600);
            }
            document.querySelectorAll('.ripple').forEach(button => {
                button.addEventListener('click', createRipple);
            });
            updateNextButton();
        });
        function goBack() {
            alert('Quay lại Step 1 - Chọn sản phẩm');
            // In real app: navigate back to step 1
        }
        function saveBookmark() {
            alert('Đã lưu bookmark');
        }
        function scanQRCode() {
            alert('Mở camera để quét mã QR khách hàng');
            // In real app: open camera for QR scanning
        }
        function createNewCustomer() {
            alert('Chuyển sang form tạo khách hàng mới');
            // In real app: navigate to new customer form
        }
        function nextStep() {
            const selectedCard = document.querySelector('.customer-card.selected');
            if (selectedCard) {
                const customerName = selectedCard.querySelector('.customer-name').textContent;
                localStorage.setItem('selectedCustomer', selectedCard.dataset.customer);
                alert(`Đã chọn khách hàng: ${customerName}\nChuyển sang bước 3 - Chi tiết sản phẩm`);
                // In real app: navigate to step 3
            }
        }
        // Add ripple effect styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple-animation {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>




---
#### UIUX Step 3 - Tổng hợp tất cả các card

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Step 3 - Chi tiết sản phẩm</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #212121;
            width: 100%;
            min-height: 100vh;
            border: 1px solid #e0e0e0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow-x: hidden;
        } 
        /* Material Design Variables */
        :root {
            --primary: #FF5722;
            --primary-variant: #E64A19;
            --secondary: #03DAC6;
            --background: #FAFAFA;
            --surface: #FFFFFF;
            --error: #B00020;
            --warning: #FF9800;
            --success: #4CAF50;
            --on-primary: #FFFFFF;
            --on-surface: #000000;
            --on-background: #000000;
            --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
        }        
        /* App Container */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 420px;
            margin: 0 auto;
            background: var(--surface);
        }        
        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-variant) 100%);
            color: var(--on-primary);
            padding: 16px 20px;
            box-shadow: var(--elevation-2);
            position: sticky;
            top: 0;
            z-index: 100;
        }        
        .header-content {
            display: flex;
            align-items: center;
            gap: 16px;
        }        
        .back-button {
            background: none;
            border: none;
            color: var(--on-primary);
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.2s;
        }        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }        
        .header-title {
            flex: 1;
            font-size: 18px;
            font-weight: 500;
        }        
        /* Progress Indicator */
        .progress-container {
            padding: 16px 20px;
            background: var(--surface);
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        }        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }        
        .step {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }        
        .step.completed {
            background: var(--success);
            color: white;
        }        
        .step.active {
            background: var(--primary);
            color: white;
        }        
        .step.pending {
            background: #E0E0E0;
            color: #9E9E9E;
        }        
        .step-line {
            flex: 1;
            height: 2px;
            background: #E0E0E0;
            margin: 0 8px;
        }        
        .step-line.completed {
            background: var(--success);
        }        
        .progress-text {
            text-align: center;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            margin-top: 8px;
        }        
        /* Content Container */
        .content-container {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 90px; /* Space for sticky buttons */
        }        
        /* Card Styles */
        .info-card {
            background: var(--surface);
            border-radius: 8px;
            box-shadow: var(--elevation-1);
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }        
        .info-card:hover {
            box-shadow: var(--elevation-2);
        }        
        .card-header {
            background: rgba(255, 87, 34, 0.05);
            border-left: 4px solid var(--primary);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: background 0.2s;
        }      
        .card-header:hover {
            background: rgba(255, 87, 34, 0.08);
        }        
        .card-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 500;
            color: var(--primary);
        }        
        .card-icon {
            font-size: 20px;
        }        
        .expand-icon {
            font-size: 20px;
            color: var(--primary);
            transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }        
        .card-content.expanded .card-header .expand-icon {
            transform: rotate(180deg);
        }        
        .card-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
        }        
        .card-content.expanded {
            max-height: 1000px;
        }        
        .card-body {
            padding: 20px;
        }        
        /* Form Elements */
        .form-section {
            margin-bottom: 20px;
        }        
        .form-row {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }        
        .form-field {
            flex: 1;
            margin-bottom: 16px;
        }        
        .form-label {
            font-size: 12px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.6);
            margin-bottom: 8px;
            display: block;
            text-transform: uppercase;
            letter-spacing: 0.4px;
        }        
        .required {
            color: var(--error);
        }        
        .text-input, .select-input {
            width: 100%;
            height: 48px;
            padding: 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.23);
            border-radius: 4px;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            transition: border-color 0.2s;
        }        
        .text-input:focus, .select-input:focus {
            outline: none;
            border-color: var(--primary);
            border-width: 2px;
            padding: 11px 15px;
        }        
        .textarea-input {
            width: 100%;
            min-height: 80px;
            padding: 12px 16px;
            border: 1px solid rgba(0, 0, 0, 0.23);
            border-radius: 4px;
            font-size: 14px;
            font-family: 'Roboto', sans-serif;
            background: var(--surface);
            color: rgba(0, 0, 0, 0.87);
            transition: border-color 0.2s;
            resize: vertical;
        }        
        .textarea-input:focus {
            outline: none;
            border-color: var(--primary);
            border-width: 2px;
            padding: 11px 15px;
        }        
        /* Radio Buttons */
        .radio-group {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background 0.2s;
        }        
        .radio-item:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        .radio-input {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(0, 0, 0, 0.54);
            border-radius: 50%;
            position: relative;
            cursor: pointer;
            flex-shrink: 0;
        }       
        .radio-input.checked {
            border-color: var(--primary);
        }        
        .radio-input.checked::after {
            content: '';
            width: 8px;
            height: 8px;
            background: var(--primary);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }        
        .radio-label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.87);
            cursor: pointer;
        }        
        /* Checkbox */
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            padding: 12px 0;
        }        
        .checkbox-input {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(0, 0, 0, 0.54);
            border-radius: 2px;
            position: relative;
            cursor: pointer;
            flex-shrink: 0;
        }        
        .checkbox-input.checked {
            background: var(--primary);
            border-color: var(--primary);
        }        
        .checkbox-input.checked::after {
            content: '✓';
            color: white;
            font-size: 12px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }        
        .checkbox-label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.87);
            cursor: pointer;
        }        
        /* Helper Text */
        .helper-text {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
            margin-top: 4px;
        }        
        .error-text {
            color: var(--error);
        }        
        /* Action Buttons - Sticky */
        .action-buttons {
            background: var(--surface);
            padding: 16px 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            position: sticky;
            bottom: 0;
            width: 100%;
            display: flex;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }        
        .outlined-button {
            flex: 1;
            height: 40px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }        
        .outlined-button:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        .contained-button {
            flex: 2;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: var(--primary);
            color: var(--on-primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--elevation-1);
        }        
        .contained-button:hover {
            box-shadow: var(--elevation-2);
        }        
        /* Responsive */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }            
            .radio-group {
                flex-direction: column;
                gap: 8px;
            }
        }        
        /* Scrollbar */
        .content-container::-webkit-scrollbar {
            width: 3px;
        }       
        .content-container::-webkit-scrollbar-track {
            background: transparent;
        }        
        .content-container::-webkit-scrollbar-thumb {
            background: #E0E0E0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <button class="back-button" onclick="goBack()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="header-title">Bước 3/5: Chi tiết sản phẩm</div>
            </div>
        </div>        
        <!-- Progress Indicator -->    
        <!-- Content Container -->
        <div class="content-container">
            <!-- Card 1: Thông tin người vay chính -->
            <div class="info-card">
                <div class="card-header" onclick="toggleCard('borrowerCard')">
                    <div class="card-title">
                        <span class="material-icons card-icon">person</span>
                        Thông tin người vay chính
                    </div>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>                
                <div class="card-content expanded" id="borrowerCard">
                    <div class="card-body">
                        <!-- Toggle Người đồng vay -->
                        <div class="checkbox-item" onclick="toggleCoBorrower()">
                            <div class="checkbox-input" id="coBorrowerCheck"></div>
                            <label class="checkbox-label">Có người đồng vay</label>
                        </div>                        
                        <!-- Thông tin nhận dạng -->
                        <div class="form-section">
                            <div class="form-label">Thông tin nhận dạng giấy tờ</div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Họ và tên <span class="required">*</span></label>
                                    <input type="text" class="text-input" placeholder="Nhập họ và tên" value="Nguyễn Văn An" required>
                                </div>
                            </div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Số GTTT <span class="required">*</span></label>
                                    <input type="text" class="text-input" placeholder="Nhập số GTTT" value="**********12" required>
                                </div>
                                <div class="form-field">
                                    <label class="form-label">Ngày cấp <span class="required">*</span></label>
                                    <input type="date" class="text-input" required>
                                </div>
                            </div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Ngày hết hạn <span class="required">*</span></label>
                                    <input type="date" class="text-input" required>
                                </div>
                                <div class="form-field">
                                    <label class="form-label">Nơi cấp <span class="required">*</span></label>
                                    <input type="text" class="text-input" placeholder="Nhập nơi cấp" required>
                                </div>
                            </div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Ngày sinh <span class="required">*</span></label>
                                    <input type="date" class="text-input" required>
                                </div>
                                <div class="form-field">
                                    <label class="form-label">Giới tính <span class="required">*</span></label>
                                    <select class="select-input" required>
                                        <option value="nam" selected>Nam</option>
                                        <option value="nu">Nữ</option>
                                        <option value="khac">Khác</option>
                                    </select>
                                </div>
                            </div>
                        </div>                        
                        <!-- Hộ khẩu thường trú -->
                        <div class="form-section">
                            <div class="form-label">Hộ khẩu thường trú</div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Tỉnh/Thành phố <span class="required">*</span></label>
                                    <select class="select-input" required>
                                        <option value="">Chọn tỉnh/thành</option>
                                        <option value="hanoi">Hà Nội</option>
                                        <option value="hcm">TP. Hồ Chí Minh</option>
                                        <option value="danang">Đà Nẵng</option>
                                    </select>
                                </div>
                            </div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Phường/Xã <span class="required">*</span></label>
                                    <select class="select-input" required>
                                        <option value="">Chọn phường/xã</option>
                                        <option value="dv">Dịch Vọng</option>
                                        <option value="dh">Dịch Vọng Hậu</option>
                                    </select>
                                </div>
                            </div>                            
                            <div class="form-field">
                                <label class="form-label">Địa chỉ cụ thể <span class="required">*</span></label>
                                <textarea class="textarea-input" placeholder="Nhập địa chỉ cụ thể" required></textarea>
                            </div>
                        </div>                        
                        <!-- Thông tin cá nhân -->
                        <div class="form-section">
                            <div class="form-label">Thông tin cá nhân</div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Tình trạng hôn nhân <span class="required">*</span></label>
                                    <select class="select-input" required>
                                        <option value="doc-than" selected>Độc thân</option>
                                        <option value="da-ket-hon">Đã kết hôn</option>
                                        <option value="ly-hon">Ly hôn</option>
                                        <option value="khac">Khác</option>
                                    </select>
                                </div>
                                <div class="form-field">
                                    <label class="form-label">Số điện thoại <span class="required">*</span></label>
                                    <input type="tel" class="text-input" placeholder="0987654321" value="0987654321" required>
                                </div>
                            </div>                            
                            <div class="checkbox-item">
                                <div class="checkbox-input checked" id="sameAddressCheck"></div>
                                <label class="checkbox-label">Địa chỉ hiện tại trùng với địa chỉ thường trú</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>            
            <!-- Card 2: Đề nghị và phương thức vay vốn -->
            <div class="info-card">
                <div class="card-header" onclick="toggleCard('loanCard')">
                    <div class="card-title">
                        <span class="material-icons card-icon">account_balance</span>
                        Đề nghị và phương thức vay vốn
                    </div>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>                
                <div class="card-content expanded" id="loanCard">
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-field">
                                <label class="form-label">Số tiền vay <span class="required">*</span></label>
                                <input type="text" class="text-input" placeholder="0" id="loanAmount" oninput="formatCurrency(this)" required>
                                <div class="helper-text">Tối thiểu 1 triệu VNĐ, tối đa 500 triệu VNĐ</div>
                            </div>
                            <div class="form-field">
                                <label class="form-label">Thời hạn vay <span class="required">*</span></label>
                                <select class="select-input" required>
                                    <option value="">Chọn thời hạn</option>
                                    <option value="12">12 tháng</option>
                                    <option value="24">24 tháng</option>
                                    <option value="36">36 tháng</option>
                                    <option value="48">48 tháng</option>
                                    <option value="60">60 tháng</option>
                                </select>
                            </div>
                        </div>                        
                        <div class="form-field">
                            <label class="form-label">Mục đích sử dụng vốn <span class="required">*</span></label>
                            <select class="select-input" required>
                                <option value="">Chọn mục đích</option>
                                <option value="kinh-doanh">Kinh doanh</option>
                                <option value="tieu-dung">Tiêu dùng</option>
                                <option value="xay-sua-nha">Xây sửa nhà</option>
                                <option value="mua-xe">Mua xe</option>
                                <option value="khac">Khác</option>
                            </select>
                        </div>                        
                        <div class="form-field">
                            <label class="form-label">Hình thức vay vốn <span class="required">*</span></label>
                            <div class="radio-group">
                                <div class="radio-item" onclick="selectRadio(this, 'loanType')">
                                    <div class="radio-input checked" data-value="co-tsbd"></div>
                                    <label class="radio-label">Có TSBĐ</label>
                                </div>
                                <div class="radio-item" onclick="selectRadio(this, 'loanType')">
                                    <div class="radio-input" data-value="khong-tsbd"></div>
                                    <label class="radio-label">Không TSBĐ</label>
                                </div>
                            </div>
                            <div class="helper-text">TSBĐ: Tài sản bảo đảm</div>
                        </div>                        
                        <div class="form-field">
                            <label class="form-label">Hình thức trả nợ <span class="required">*</span></label>
                            <select class="select-input" required>
                                <option value="">Chọn hình thức</option>
                                <option value="goc-lai-deu">Trả gốc và lãi đều hàng tháng</option>
                                <option value="cuoi-ky">Trả gốc cuối kỳ, lãi hàng tháng</option>
                                <option value="theo-doanh-thu">Theo doanh thu</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>            
            <!-- Card 3: Tình hình tài chính -->
            <div class="info-card">
                <div class="card-header" onclick="toggleCard('financialCard')">
                    <div class="card-title">
                        <span class="material-icons card-icon">trending_up</span>
                        Tình hình tài chính
                    </div>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>                
                <div class="card-content expanded" id="financialCard">
                    <div class="card-body">
                        <div class="form-field">
                            <label class="form-label">Nguồn thu chính <span class="required">*</span></label>
                            <select class="select-input" required>
                                <option value="">Chọn nguồn thu</option>
                                <option value="luong">Lương</option>
                                <option value="kinh-doanh">Kinh doanh</option>
                                <option value="nong-nghiep">Nông nghiệp</option>
                                <option value="dau-tu">Đầu tư</option>
                                <option value="khac">Khác</option>
                            </select>
                        </div>                        
                        <div class="form-row">
                            <div class="form-field">
                                <label class="form-label">Doanh số bình quân/tháng</label>
                                <input type="text" class="text-input" placeholder="0" oninput="formatCurrency(this)">
                                <div class="helper-text">VNĐ</div>
                            </div>
                            <div class="form-field">
                                <label class="form-label">Thu nhập bình quân/tháng <span class="required">*</span></label>
                                <input type="text" class="text-input" placeholder="0" oninput="formatCurrency(this)" required>
                                <div class="helper-text">VNĐ</div>
                            </div>
                        </div>                        
                        <!-- Địa chỉ kinh doanh -->
                        <div class="form-section">
                            <div class="form-label">Địa chỉ kinh doanh (nếu có)</div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Tỉnh/Thành phố</label>
                                    <select class="select-input">
                                        <option value="">Chọn tỉnh/thành</option>
                                        <option value="hanoi">Hà Nội</option>
                                        <option value="hcm">TP. Hồ Chí Minh</option>
                                    </select>
                                </div>
                            </div>                            
                            <div class="form-row">
                                <div class="form-field">
                                    <label class="form-label">Phường/Xã</label>
                                    <select class="select-input">
                                        <option value="">Chọn phường/xã</option>
                                    </select>
                                </div>
                            </div>                            
                            <div class="form-field">
                                <label class="form-label">Địa chỉ cụ thể</label>
                                <textarea class="textarea-input" placeholder="Nhập địa chỉ kinh doanh"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>            
            <!-- Card 4: Tài sản bảo đảm -->
            <div class="info-card" id="assetCollateralCard">
                <div class="card-header" onclick="toggleCard('assetCard')">
                    <div class="card-title">
                        <span class="material-icons card-icon">security</span>
                        Tài sản bảo đảm
                    </div>
                    <span class="material-icons expand-icon">expand_more</span>
                </div>                
                <div class="card-content expanded" id="assetCard">
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-field">
                                <label class="form-label">Loại tài sản <span class="required">*</span></label>
                                <select class="select-input" required>
                                    <option value="">Chọn loại tài sản</option>
                                    <option value="xe-may">Xe máy</option>
                                    <option value="xe-hoi">Xe hơi</option>
                                    <option value="nha-dat">Nhà đất</option>
                                    <option value="may-moc">Máy móc thiết bị</option>
                                </select>
                            </div>
                            <div class="form-field">
                                <label class="form-label">Tình trạng <span class="required">*</span></label>
                                <select class="select-input" required>
                                    <option value="">Chọn tình trạng</option>
                                    <option value="moi">Mới</option>
                                    <option value="cu">Cũ</option>
                                    <option value="dang-su-dung" selected>Đang sử dụng</option>
                                </select>
                            </div>
                        </div>                        
                        <div class="form-field">
                            <label class="form-label">Tổng giá trị tài sản bảo đảm <span class="required">*</span></label>
                            <input type="text" class="text-input" placeholder="0" id="assetValue" oninput="formatCurrency(this)" required>
                            <div class="helper-text">Tối đa 1 tỷ VNĐ, định dạng xxx,xxx</div>
                        </div>                        
                        <div class="form-field">
                            <label class="form-label">Mô tả chi tiết tài sản</label>
                            <textarea class="textarea-input" placeholder="Nhập mô tả chi tiết về tài sản bảo đảm..."></textarea>
                        </div>
                        <!-- Chi tiết tài sản bảo đảm -->
                <div class="form-section">
                    <div class="section-title">
                        <span class="material-icons">details</span>
                        Chi tiết tài sản bảo đảm
                    </div>                    
                    <div class="form-row">
                        <div class="form-field">
                            <label class="form-label">Số khung/Chassis</label>
                            <input type="text" class="text-input" placeholder="Nhập số khung">
                        </div>
                        <div class="form-field">
                            <label class="form-label">Số máy/Engine</label>
                            <input type="text" class="text-input" placeholder="Nhập số máy">
                        </div>
                    </div>                    
                    <div class="form-row">
                        <div class="form-field">
                            <label class="form-label">Biển số đăng ký</label>
                            <input type="text" class="text-input" placeholder="Nhập biển số">
                        </div>
                        <div class="form-field">
                            <label class="form-label">Màu sắc</label>
                            <input type="text" class="text-input" placeholder="Nhập màu sắc">
                        </div>
                    </div>                    
                    <div class="form-field">
                        <label class="form-label">Mô tả chi tiết</label>
                        <textarea class="text-input" style="height: 100px; resize: vertical;" placeholder="Mô tả chi tiết về tài sản..."></textarea>
                    </div>
                </div>
            </div>
                    </div>
                </div>
            </div>
        </div>        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="outlined-button" onclick="goBack()">
                ← Quay lại
            </button>
            <button class="contained-button" onclick="nextStep()">
                → Tiếp tục
            </button>
        </div>
    </div>
    <script>
        // Toggle card expand/collapse
        function toggleCard(cardId) {
            const content = document.getElementById(cardId);
            content.classList.toggle('expanded');
        }        
        // Radio button selection
        function selectRadio(element, groupName) {
            const group = element.closest('.radio-group');
            const radios = group.querySelectorAll('.radio-input');
            radios.forEach(radio => radio.classList.remove('checked'));
            element.querySelector('.radio-input').classList.add('checked');            
            // Handle asset collateral visibility
            if (groupName === 'loanType') {
                const value = element.querySelector('.radio-input').dataset.value;
                const assetCard = document.getElementById('assetCollateralCard');
                if (value === 'khong-tsbd') {
                    assetCard.style.display = 'none';
                } else {
                    assetCard.style.display = 'block';
                }
            }
        }        
        // Toggle checkbox
        function toggleCoBorrower() {
            const checkbox = document.getElementById('coBorrowerCheck');
            checkbox.classList.toggle('checked');
        }        
        // Currency formatting
        function formatCurrency(input) {
            let value = input.value.replace(/[^0-9]/g, '');
            if (value) {
                value = parseInt(value).toLocaleString('vi-VN');
                input.value = value;
            } else {
                input.value = '';
            }
        }        
        // Navigation functions
        function goBack() {
            if (confirm('Bạn có chắc chắn muốn quay lại? Dữ liệu chưa lưu sẽ bị mất.')) {
                alert('Quay lại Step 2 - Chọn khách hàng');
                // In real app: navigate to step 2
            }
        }        
        function nextStep() {
            if (validateForm()) {
                alert('Chuyển đến Step 4 - Tài liệu yêu cầu');
                // In real app: navigate to step 4
            }
        }        
        // Form validation
        function validateForm() {
            const requiredFields = document.querySelectorAll('[required]');
            let isValid = true;            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = 'var(--error)';
                    isValid = false;
                } else {
                    field.style.borderColor = 'rgba(0, 0, 0, 0.23)';
                }
            });            
            if (!isValid) {
                alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
            }            
            return isValid;
        }        
        // Auto-save functionality
        function autoSave() {
            const formData = {
                borrowerInfo: {
                    fullName: document.querySelector('input[placeholder="Nhập họ và tên"]').value,
                    idNumber: document.querySelector('input[placeholder="Nhập số GTTT"]').value,
                    phone: document.querySelector('input[placeholder="0987654321"]').value
                },
                loanInfo: {
                    amount: document.getElementById('loanAmount').value,
                    purpose: document.querySelector('select[required]').value
                }
            };            
            localStorage.setItem('step3Data', JSON.stringify(formData));
            console.log('Dữ liệu đã được lưu tự động');
        }        
        // Auto-save every 30 seconds
        setInterval(autoSave, 30000);
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved data if exists
            const savedData = localStorage.getItem('step3Data');
            if (savedData) {
                // Restore form data
                console.log('Đã tìm thấy dữ liệu đã lưu');
            }            
            // Add input event listeners for validation
            document.querySelectorAll('.text-input, .select-input, .textarea-input').forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.hasAttribute('required') && !this.value.trim()) {
                        this.style.borderColor = 'var(--error)';
                    } else {
                        this.style.borderColor = 'rgba(0, 0, 0, 0.23)';
                    }
                });
            });
        });
    </script>
</body>
</html>

---

#### UIUX Step 4 - Tài liệu yêu cầu

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Step 4 - Tài liệu yêu cầu</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #212121;
            width: 100%;
            min-height: 100vh;
            border: 1px solid #e0e0e0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow-x: hidden;
        }
        /* Material Design Variables */
        :root {
            --primary: #FF5722;
            --primary-variant: #E64A19;
            --secondary: #03DAC6;
            --background: #FAFAFA;
            --surface: #FFFFFF;
            --error: #B00020;
            --warning: #FF9800;
            --success: #4CAF50;
            --on-primary: #FFFFFF;
            --on-surface: #000000;
            --on-background: #000000;
            --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
            --elevation-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
        }
        /* App Container */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 420px;
            margin: 0 auto;
            background: var(--surface);
        }        
        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-variant) 100%);
            color: var(--on-primary);
            padding: 16px 20px;
            box-shadow: var(--elevation-2);
            position: sticky;
            top: 0;
            z-index: 100;
        }        
        .header-content {
            display: flex;
            align-items: center;
            gap: 16px;
        }       
        .back-button {
            background: none;
            border: none;
            color: var(--on-primary);
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.2s;
        }       
        .back-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }        
        .header-title {
            flex: 1;
            font-size: 18px;
            font-weight: 500;
        }        
        /* Progress Indicator */
        .progress-container {
            padding: 16px 20px;
            background: var(--surface);
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        }        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }        
        .step {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }        
        .step.completed {
            background: var(--success);
            color: white;
        }        
        .step.active {
            background: var(--primary);
            color: white;
        }        
        .step.pending {
            background: #E0E0E0;
            color: #9E9E9E;
        }       
        .step-line {
            flex: 1;
            height: 2px;
            background: #E0E0E0;
            margin: 0 8px;
        }        
        .step-line.completed {
            background: var(--success);
        }        
        .progress-text {
            text-align: center;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            margin-top: 8px;
        }        
        /* Content Container */
        .content-container {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 120px; /* Space for sticky buttons */
        }        
        /* Document Section */
        .document-section {
            background: var(--surface);
            border-radius: 8px;
            box-shadow: var(--elevation-1);
            margin-bottom: 16px;
            overflow: hidden;
        }        
        .section-header {
            background: rgba(255, 87, 34, 0.05);
            border-left: 4px solid var(--primary);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }      
        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }        
        .section-icon {
            font-size: 20px;
        }        
        .required-badge {
            background: var(--error);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }        
        .optional-badge {
            background: #757575;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }        
        .section-content {
            padding: 20px;
        }        
        /* Document Item */
        .document-item {
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        }        
        .document-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }        
        .document-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }        
        .document-title {
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            display: flex;
            align-items: center;
            gap: 8px;
        }        
        .document-icon {
            font-size: 16px;
            color: var(--primary);
        }        
        .status-badge {
            font-size: 10px;
            padding: 2px 8px;
            border-radius: 10px;
            font-weight: 500;
        }      
        .status-uploaded {
            background: #E8F5E8;
            color: var(--success);
        }        
        .status-pending {
            background: #FFF3E0;
            color: var(--warning);
        }        
        .status-error {
            background: #FFEBEE;
            color: var(--error);
        }        
        /* File Upload Area */
        .upload-area {
            border: 2px dashed rgba(0, 0, 0, 0.26);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: rgba(0, 0, 0, 0.02);
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
        }        
        .upload-area:hover {
            border-color: var(--primary);
            background: rgba(255, 87, 34, 0.04);
        }        
        .upload-area.dragover {
            border-color: var(--primary);
            background: rgba(255, 87, 34, 0.08);
            transform: scale(1.02);
        }        
        .upload-area.has-files {
            border-color: var(--success);
            background: rgba(76, 175, 80, 0.04);
        }        
        .upload-icon {
            font-size: 32px;
            color: rgba(0, 0, 0, 0.38);
            margin-bottom: 8px;
        }        
        .upload-text {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            margin-bottom: 4px;
        }        
        .upload-subtext {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.38);
        }        
        .upload-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }        
        /* Action Buttons */
        .upload-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }        
        .action-btn {
            flex: 1;
            height: 36px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }        
        .action-btn:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        .action-btn.primary {
            background: var(--primary);
            color: white;
        }        
        .action-btn.primary:hover {
            background: var(--primary-variant);
        }        
        /* File List */
        .file-list {
            margin-top: 12px;
        }        
        .file-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 4px;
            margin-bottom: 8px;
            transition: all 0.2s;
        }        
        .file-item:hover {
            background: rgba(0, 0, 0, 0.04);
        }        
        .file-icon {
            font-size: 20px;
            color: var(--primary);
        }        
        .file-info {
            flex: 1;
        }        
        .file-name {
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            margin-bottom: 2px;
        }        
        .file-details {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
        }        
        .file-actions {
            display: flex;
            gap: 4px;
        }        
        .icon-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: none;
            background: transparent;
            color: rgba(0, 0, 0, 0.54);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }        
        .icon-btn:hover {
            background: rgba(0, 0, 0, 0.04);
            color: rgba(0, 0, 0, 0.87);
        }        
        .icon-btn.delete:hover {
            color: var(--error);
            background: rgba(176, 0, 32, 0.04);
        }        
        /* Detail Modal Trigger */
        .detail-btn {
            background: none;
            border: none;
            color: var(--primary);
            font-size: 12px;
            text-decoration: underline;
            cursor: pointer;
            padding: 4px 0;
            margin-top: 8px;
        }        
        /* Helper Text */
        .helper-text {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
            margin-top: 8px;
            line-height: 1.4;
        }        
        .error-text {
            color: var(--error);
        }       
        /* Progress Bar */
        .upload-progress {
            width: 100%;
            height: 4px;
            background: #E0E0E0;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }        
        .progress-bar {
            height: 100%;
            background: var(--primary);
            border-radius: 2px;
            transition: width 0.3s ease;
        }        
        /* Action Buttons - Sticky */
        .action-buttons {
            background: var(--surface);
            padding: 16px 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            position: sticky;
            bottom: 0;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }        
        .button-row {
            display: flex;
            gap: 12px;
        }        
        .outlined-button {
            flex: 1;
            height: 40px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }        
        .outlined-button:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        .contained-button {
            flex: 2;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: var(--primary);
            color: var(--on-primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--elevation-1);
        }        
        .contained-button:hover {
            box-shadow: var(--elevation-2);
        }        
        .contained-button:disabled {
            background: #E0E0E0;
            color: #9E9E9E;
            cursor: not-allowed;
            box-shadow: none;
        }        
        .save-draft-button {
            width: 100%;
            height: 36px;
            border: 1px solid #757575;
            border-radius: 4px;
            background: transparent;
            color: #757575;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }        
        .save-draft-button:hover {
            background: rgba(117, 117, 117, 0.04);
            border-color: rgba(0, 0, 0, 0.6);
            color: rgba(0, 0, 0, 0.6);
        }        
        /* Completion Status */
        .completion-status {
            background: rgba(255, 87, 34, 0.04);
            border: 1px solid rgba(255, 87, 34, 0.2);
            border-radius: 4px;
            padding: 12px 16px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }        
        .status-icon {
            font-size: 20px;
            color: var(--primary);
        }        
        .status-text {
            flex: 1;
            font-size: 14px;
            color: var(--primary);
        }        
        .status-count {
            font-size: 12px;
            font-weight: 500;
            color: var(--primary);
        }        
        /* Responsive */
        @media (max-width: 768px) {
            .upload-actions {
                flex-direction: column;
            }            
            .file-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }            
            .file-actions {
                align-self: flex-end;
            }
        }        
        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }        
        .document-section {
            animation: fadeInUp 0.3s ease-out;
        }        
        /* Scrollbar */
        .content-container::-webkit-scrollbar {
            width: 3px;
        }        
        .content-container::-webkit-scrollbar-track {
            background: transparent;
        }        
        .content-container::-webkit-scrollbar-thumb {
            background: #E0E0E0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <button class="back-button" onclick="goBack()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="header-title">Bước 4/5: Tài liệu yêu cầu</div>
            </div>
        </div>         
        <!-- Content Container -->
        <div class="content-container">
            <!-- Completion Status -->
            <div class="completion-status">
                <span class="material-icons status-icon">assignment_turned_in</span>
                <div class="status-text">Tài liệu đã hoàn thành</div>
                <div class="status-count" id="completionCount">3/7</div>
            </div>            
            <!-- Required Documents Section -->
            <div class="document-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons section-icon">description</span>
                        Tài liệu bắt buộc
                    </div>
                    <span class="required-badge">BẮT BUỘC</span>
                </div>                
                <div class="section-content">
                    <!-- GTTT Người vay chính -->
                    <div class="document-item">
                        <div class="document-header">
                            <div class="document-title">
                                <span class="material-icons document-icon">badge</span>
                                GTTT người vay chính
                            </div>
                            <span class="status-badge status-uploaded">Đã tải</span>
                        </div>                      
                        <div class="upload-area has-files">
                            <div class="material-icons upload-icon">cloud_done</div>
                            <div class="upload-text">Mặt trước và mặt sau GTTT</div>
                            <div class="upload-subtext">PNG, JPG tối đa 5MB mỗi file</div>
                        </div>                        
                        <div class="file-list">
                            <div class="file-item">
                                <span class="material-icons file-icon">image</span>
                                <div class="file-info">
                                    <div class="file-name">CCCD_MatTruoc.jpg</div>
                                    <div class="file-details">2.1 MB • Đã tải lên 10:30</div>
                                </div>
                                <div class="file-actions">
                                    <button class="icon-btn" onclick="viewFile(this)" title="Xem">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="icon-btn delete" onclick="deleteFile(this)" title="Xóa">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </div>
                            <div class="file-item">
                                <span class="material-icons file-icon">image</span>
                                <div class="file-info">
                                    <div class="file-name">CCCD_MatSau.jpg</div>
                                    <div class="file-details">1.8 MB • Đã tải lên 10:31</div>
                                </div>
                                <div class="file-actions">
                                    <button class="icon-btn" onclick="viewFile(this)" title="Xem">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="icon-btn delete" onclick="deleteFile(this)" title="Xóa">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>                        
                        <div class="upload-actions">
                            <button class="action-btn" onclick="capturePhoto('gttt-main')">
                                <span class="material-icons">camera_alt</span>
                                Chụp ảnh
                            </button>
                            <button class="action-btn" onclick="selectFiles('gttt-main')">
                                <span class="material-icons">photo_library</span>
                                Thư viện
                            </button>
                            <button class="action-btn primary" onclick="uploadFiles('gttt-main')">
                                <span class="material-icons">cloud_upload</span>
                                Tải file
                            </button>
                        </div>
                    </div>                    
                    <!-- GTTT Người đồng vay -->
                    <div class="document-item">
                        <div class="document-header">
                            <div class="document-title">
                                <span class="material-icons document-icon">badge</span>
                                GTTT người đồng vay
                            </div>
                            <span class="status-badge status-pending">Chưa tải</span>
                        </div>                        
                        <div class="upload-area" onclick="triggerUpload('gttt-co')">
                            <div class="material-icons upload-icon">cloud_upload</div>
                            <div class="upload-text">Tải lên GTTT người đồng vay</div>
                            <div class="upload-subtext">PNG, JPG tối đa 5MB mỗi file</div>
                            <input type="file" id="gttt-co" class="upload-input" multiple accept="image/*" onchange="handleFileSelect(event, 'gttt-co')">
                        </div>                        
                        <div class="upload-actions">
                            <button class="action-btn" onclick="capturePhoto('gttt-co')">
                                <span class="material-icons">camera_alt</span>
                                Chụp ảnh
                            </button>
                            <button class="action-btn" onclick="selectFiles('gttt-co')">
                                <span class="material-icons">photo_library</span>
                                Thư viện
                            </button>
                            <button class="action-btn primary" onclick="uploadFiles('gttt-co')">
                                <span class="material-icons">cloud_upload</span>
                                Tải file
                            </button>
                        </div>
                    </div>                    
                    <!-- Giấy tờ tình trạng hôn nhân -->
                    <div class="document-item">
                        <div class="document-header">
                            <div class="document-title">
                                <span class="material-icons document-icon">favorite</span>
                                Giấy tờ tình trạng hôn nhân, mối quan hệ
                            </div>
                            <span class="status-badge status-uploaded">Đã tải</span>
                        </div>                        
                        <div class="upload-area has-files">
                            <div class="material-icons upload-icon">cloud_done</div>
                            <div class="upload-text">Giấy chứng nhận kết hôn</div>
                            <div class="upload-subtext">PNG, PDF tối đa 5MB</div>
                        </div>                        
                        <div class="file-list">
                            <div class="file-item">
                                <span class="material-icons file-icon">description</span>
                                <div class="file-info">
                                    <div class="file-name">GiayKetHon.pdf</div>
                                    <div class="file-details">1.2 MB • Đã tải lên 09:45</div>
                                </div>
                                <div class="file-actions">
                                    <button class="icon-btn" onclick="viewFile(this)" title="Xem">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="icon-btn delete" onclick="deleteFile(this)" title="Xóa">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>                    
                    <!-- Giấy tờ chứng minh nơi cư trú -->
                    <div class="document-item">
                        <div class="document-header">
                            <div class="document-title">
                                <span class="material-icons document-icon">home</span>
                                Giấy tờ chứng minh nơi cư trú
                            </div>
                            <span class="status-badge status-pending">Chưa tải</span>
                        </div>                       
                        <div class="upload-area" onclick="triggerUpload('residence')">
                            <div class="material-icons upload-icon">cloud_upload</div>
                            <div class="upload-text">Tải lên giấy tờ chứng minh nơi cư trú</div>
                            <div class="upload-subtext">PNG, PDF tối đa 5MB</div>
                            <input type="file" id="residence" class="upload-input" multiple accept=".png,.pdf,.jpg,.jpeg" onchange="handleFileSelect(event, 'residence')">
                        </div>                        
                        <button class="detail-btn" onclick="showResidenceDetail()">
                            <span class="material-icons" style="font-size: 14px;">info</span>
                            Chi tiết về giấy tờ chứng minh nơi cư trú
                        </button>
                    </div>                    
                    <!-- Tờ trình thẩm định xe (có TSBĐ) -->
                    <div class="document-item" id="appraisal-doc">
                        <div class="document-header">
                            <div class="document-title">
                                <span class="material-icons document-icon">assessment</span>
                                Tờ trình thẩm định xe (có TSBĐ)
                            </div>
                            <span class="status-badge status-uploaded">Đã tải</span>
                        </div>                        
                        <div class="upload-area has-files">
                            <div class="material-icons upload-icon">cloud_done</div>
                            <div class="upload-text">Tờ trình thẩm định xe mô tô, gắn máy</div>
                            <div class="upload-subtext">PNG, PDF tối đa 5MB</div>
                        </div>                        
                        <div class="file-list">
                            <div class="file-item">
                                <span class="material-icons file-icon">description</span>
                                <div class="file-info">
                                    <div class="file-name">ToTrinhThamDinh.pdf</div>
                                    <div class="file-details">3.2 MB • Đã tải lên 08:20</div>
                                </div>
                                <div class="file-actions">
                                    <button class="icon-btn" onclick="viewFile(this)" title="Xem">
                                        <span class="material-icons">visibility</span>
                                    </button>
                                    <button class="icon-btn delete" onclick="deleteFile(this)" title="Xóa">
                                        <span class="material-icons">delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>                    
                    <!-- Giấy đăng ký xe (có TSBĐ) -->
                    <div class="document-item" id="registration-doc">
                        <div class="document-header">
                            <div class="document-title">
                                <span class="material-icons document-icon">directions_car</span>
                                Giấy đăng ký xe (có TSBĐ)
                            </div>
                            <span class="status-badge status-pending">Chưa tải</span>
                        </div>                        
                        <div class="upload-area" onclick="triggerUpload('registration')">
                            <div class="material-icons upload-icon">cloud_upload</div>
                            <div class="upload-text">Tải lên giấy đăng ký xe bảo đảm</div>
                            <div class="upload-subtext">PNG, PDF tối đa 5MB</div>
                            <input type="file" id="registration" class="upload-input" multiple accept=".png,.pdf,.jpg,.jpeg" onchange="handleFileSelect(event, 'registration')">
                        </div>                       
                        <div class="upload-actions">
                            <button class="action-btn" onclick="scanQR('registration')">
                                <span class="material-icons">qr_code_scanner</span>
                                Quét QR
                            </button>
                            <button class="action-btn" onclick="capturePhoto('registration')">
                                <span class="material-icons">camera_alt</span>
                                Chụp ảnh
                            </button>
                            <button class="action-btn primary" onclick="uploadFiles('registration')">
                                <span class="material-icons">cloud_upload</span>
                                Tải file
                            </button>
                        </div>
                    </div>
                </div>
            </div>            
            <!-- Optional Documents Section -->
            <div class="document-section">
                <div class="section-header">
                    <div class="section-title">
                        <span class="material-icons section-icon">attach_file</span>
                        Tài liệu tùy chọn
                    </div>
                    <span class="optional-badge">TÙY CHỌN</span>
                </div>                
                <div class="section-content">
                    <!-- Chứng nhận hộ kinh doanh -->
                    <div class="document-item">
                        <div class="document-header">
                            <div class="document-title">
                                <span class="material-icons document-icon">business</span>
                                Chứng nhận hộ kinh doanh, khác
                            </div>
                            <span class="status-badge status-pending">Chưa tải</span>
                        </div>                        
                        <div class="upload-area" onclick="triggerUpload('business')">
                            <div class="material-icons upload-icon">cloud_upload</div>
                            <div class="upload-text">Tải lên giấy chứng nhận kinh doanh</div>
                            <div class="upload-subtext">PNG, PDF tối đa 5MB</div>
                            <input type="file" id="business" class="upload-input" multiple accept=".png,.pdf,.jpg,.jpeg" onchange="handleFileSelect(event, 'business')">
                        </div>                        
                        <div class="helper-text">
                            Tài liệu bổ sung giúp đánh giá hồ sơ tốt hơn (không bắt buộc)
                        </div>
                    </div>
                </div>
            </div>
        </div>        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <div class="button-row">
                <button class="outlined-button" onclick="goBack()">
                    ← Quay lại
                </button>
                <button class="contained-button" onclick="nextStep()" id="nextBtn">
                    → Tiếp tục
                </button>
            </div>
            <button class="save-draft-button" onclick="saveDraft()">
                <span class="material-icons">save</span>
                Lưu nháp
            </button>
        </div>
    </div>
    <script>
        // File upload state
        let uploadedFiles = {
            'gttt-main': ['CCCD_MatTruoc.jpg', 'CCCD_MatSau.jpg'],
            'gttt-co': [],
            'marriage': ['GiayKetHon.pdf'],
            'residence': [],
            'appraisal': ['ToTrinhThamDinh.pdf'],
            'registration': [],
            'business': []
        };        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            updateCompletionStatus();
            updateNextButtonState();
            setupDragAndDrop();
        });        
        // File upload handlers
        function triggerUpload(type) {
            document.getElementById(type).click();
        }        
        function handleFileSelect(event, type) {
            const files = event.target.files;
            const uploadArea = event.target.closest('.upload-area');           
            for (let file of files) {
                if (validateFile(file)) {
                    uploadFile(file, type, uploadArea);
                }
            }            
            // Reset input
            event.target.value = '';
        }        
        function validateFile(file) {
            const maxSize = 5 * 1024 * 1024; // 5MB
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'];            
            if (file.size > maxSize) {
                alert(`File ${file.name} vượt quá 5MB`);
                return false;
            }            
            if (!allowedTypes.includes(file.type)) {
                alert(`File ${file.name} không đúng định dạng`);
                return false;
            }            
            return true;
        }        
        function uploadFile(file, type, uploadArea) {
            // Show upload progress
            showUploadProgress(uploadArea, file.name);            
            // Simulate upload
            setTimeout(() => {
                addFileToList(file, type);
                updateUploadArea(uploadArea, true);
                updateDocumentStatus(type, 'uploaded');
                updateCompletionStatus();
                updateNextButtonState();
            }, 2000);
        }        
        function showUploadProgress(uploadArea, fileName) {
            uploadArea.innerHTML = `
                <div class="material-icons upload-icon">cloud_upload</div>
                <div class="upload-text">Đang tải lên ${fileName}...</div>
                <div class="upload-progress">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
            `;            
            // Animate progress
            const progressBar = uploadArea.querySelector('.progress-bar');
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 200);
        }        
        function addFileToList(file, type) {
            const fileList = document.querySelector(`#${type}`).closest('.document-item').querySelector('.file-list') || createFileList(type);            
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span class="material-icons file-icon">${getFileIcon(file.type)}</span>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-details">${formatFileSize(file.size)} • Vừa tải lên</div>
                </div>
                <div class="file-actions">
                    <button class="icon-btn" onclick="viewFile(this)" title="Xem">
                        <span class="material-icons">visibility</span>
                    </button>
                    <button class="icon-btn delete" onclick="deleteFile(this)" title="Xóa">
                        <span class="material-icons">delete</span>
                    </button>
                </div>
            `;            
            fileList.appendChild(fileItem);
            uploadedFiles[type].push(file.name);
        }        
        function createFileList(type) {
            const documentItem = document.querySelector(`#${type}`).closest('.document-item');
            const fileList = document.createElement('div');
            fileList.className = 'file-list';
            documentItem.querySelector('.upload-area').insertAdjacentElement('afterend', fileList);
            return fileList;
        }        
        function updateUploadArea(uploadArea, hasFiles) {
            if (hasFiles) {
                uploadArea.classList.add('has-files');
                uploadArea.innerHTML = `
                    <div class="material-icons upload-icon">cloud_done</div>
                    <div class="upload-text">Tài liệu đã được tải lên</div>
                    <div class="upload-subtext">Nhấn để thêm tài liệu khác</div>
                `;
            }
        }        
        function updateDocumentStatus(type, status) {
            const documentItem = document.querySelector(`#${type}`).closest('.document-item');
            const statusBadge = documentItem.querySelector('.status-badge');            
            statusBadge.className = `status-badge status-${status}`;
            statusBadge.textContent = status === 'uploaded' ? 'Đã tải' : 'Chưa tải';
        }        
        function getFileIcon(fileType) {
            if (fileType.startsWith('image/')) return 'image';
            if (fileType === 'application/pdf') return 'picture_as_pdf';
            return 'description';
        }        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }        
        // Action button handlers
        function capturePhoto(type) {
            alert(`Mở camera để chụp ảnh cho ${type}`);
            // In real app: open camera
        }        
        function selectFiles(type) {
            document.getElementById(type).click();
        }       
        function uploadFiles(type) {
            document.getElementById(type).click();
        }        
        function scanQR(type) {
            alert('Mở camera để quét mã QR');
            // In real app: open QR scanner
        }        
        function viewFile(button) {
            const fileName = button.closest('.file-item').querySelector('.file-name').textContent;
            alert(`Xem file: ${fileName}`);
            // In real app: open file viewer
        }        
        function deleteFile(button) {
            if (confirm('Bạn có chắc chắn muốn xóa file này?')) {
                const fileItem = button.closest('.file-item');
                const fileName = fileItem.querySelector('.file-name').textContent;
                fileItem.remove();                
                // Update uploaded files state
                for (let type in uploadedFiles) {
                    const index = uploadedFiles[type].indexOf(fileName);
                    if (index > -1) {
                        uploadedFiles[type].splice(index, 1);
                        break;
                    }
                }                
                updateCompletionStatus();
                updateNextButtonState();
            }
        }        
        function showResidenceDetail() {
            alert('Chi tiết về giấy tờ chứng minh nơi cư trú:\n\n• Hóa đơn điện/nước/gas\n• Hợp đồng thuê nhà\n• Giấy xác nhận tạm trú\n• Sổ hộ khẩu');
        }        
        // Status updates
        function updateCompletionStatus() {
            const requiredTypes = ['gttt-main', 'gttt-co', 'marriage', 'residence', 'appraisal', 'registration'];
            const completed = requiredTypes.filter(type => uploadedFiles[type].length > 0).length;
            const total = requiredTypes.length;            
            document.getElementById('completionCount').textContent = `${completed}/${total}`;
        }        
        function updateNextButtonState() {
            const requiredTypes = ['gttt-main', 'gttt-co', 'marriage', 'residence', 'appraisal', 'registration'];
            const allCompleted = requiredTypes.every(type => uploadedFiles[type].length > 0);            
            const nextBtn = document.getElementById('nextBtn');
            nextBtn.disabled = !allCompleted;            
            if (allCompleted) {
                nextBtn.textContent = '→ Tiếp tục';
            } else {
                nextBtn.textContent = '→ Hoàn thành tài liệu';
            }
        }        
        // Drag and drop setup
        function setupDragAndDrop() {
            document.querySelectorAll('.upload-area').forEach(uploadArea => {
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });                
                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });                
                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');                    
                    const files = e.dataTransfer.files;
                    const input = uploadArea.querySelector('.upload-input');
                    const type = input.id;                    
                    for (let file of files) {
                        if (validateFile(file)) {
                            uploadFile(file, type, uploadArea);
                        }
                    }
                });
            });
        }        
        // Navigation functions
        function goBack() {
            if (confirm('Bạn có chắc chắn muốn quay lại? Tài liệu đã tải sẽ được lưu.')) {
                alert('Quay lại Step 3 - Chi tiết sản phẩm');
                // In real app: navigate to step 3
            }
        }        
        function nextStep() {
            const requiredTypes = ['gttt-main', 'gttt-co', 'marriage', 'residence', 'appraisal', 'registration'];
            const missingDocs = requiredTypes.filter(type => uploadedFiles[type].length === 0); 
            if (missingDocs.length > 0) {
                alert('Vui lòng tải lên đầy đủ tài liệu bắt buộc!');
                return;
            }           
            alert('Chuyển đến Step 5 - Xem lại và xác nhận');
            // In real app: navigate to step 5
        }        
        function saveDraft() {
            const draftData = {
                uploadedFiles: uploadedFiles,
                timestamp: new Date().toISOString()
            };            
            localStorage.setItem('step4Draft', JSON.stringify(draftData));
            alert('Đã lưu nháp thành công!');
        }        
        // Auto-save every 30 seconds
        setInterval(saveDraft, 30000);
    </script>
</body>
</html>

---

#### Step 5: Xem lại và xác nhận

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Step 5 - Xem lại và xác nhận</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #212121;
            width: 100%;
            min-height: 100vh;
            border: 1px solid #e0e0e0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow-x: hidden;
        }        
        /* Material Design Variables */
        :root {
            --primary: #FF5722;
            --primary-variant: #E64A19;
            --secondary: #03DAC6;
            --background: #FAFAFA;
            --surface: #FFFFFF;
            --error: #B00020;
            --warning: #FF9800;
            --success: #4CAF50;
            --on-primary: #FFFFFF;
            --on-surface: #000000;
            --on-background: #000000;
            --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
            --elevation-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
        }        
        /* App Container */
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 420px;
            margin: 0 auto;
            background: var(--surface);
        }        
        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-variant) 100%);
            color: var(--on-primary);
            padding: 16px 20px;
            box-shadow: var(--elevation-2);
            position: sticky;
            top: 0;
            z-index: 100;
        }       
        .header-content {
            display: flex;
            align-items: center;
            gap: 16px;
        }        
        .back-button {
            background: none;
            border: none;
            color: var(--on-primary);
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.2s;
        }        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }        
        .header-title {
            flex: 1;
            font-size: 18px;
            font-weight: 500;
        }        
        /* Progress Indicator */
        .progress-container {
            padding: 16px 20px;
            background: var(--surface);
            border-bottom: 1px solid rgba(0, 0, 0, 0.12);
        }        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }        
        .step {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 500;
            position: relative;
        }        
        .step.completed {
            background: var(--success);
            color: white;
        }        
        .step.active {
            background: var(--primary);
            color: white;
        }        
        .step.pending {
            background: #E0E0E0;
            color: #9E9E9E;
        }        
        .step-line {
            flex: 1;
            height: 2px;
            background: #E0E0E0;
            margin: 0 8px;
        }        
        .step-line.completed {
            background: var(--success);
        }        
        .progress-text {
            text-align: center;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            margin-top: 8px;
        }        
        /* Content Container */
        .content-container {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 16px;
            padding-bottom: 120px; /* Space for sticky buttons */
        }        
        /* Summary Status */
        .summary-status {
            background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
            color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            text-align: center;
            box-shadow: var(--elevation-2);
        }        
        .status-icon {
            font-size: 48px;
            margin-bottom: 12px;
            opacity: 0.9;
        }        
        .status-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
        }        
        .status-subtitle {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }        
        /* Review Section */
        .review-section {
            background: var(--surface);
            border-radius: 8px;
            box-shadow: var(--elevation-1);
            margin-bottom: 16px;
            overflow: hidden;
        }        
        .section-header {
            background: rgba(255, 87, 34, 0.05);
            border-left: 4px solid var(--primary);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }        
        .section-header:hover {
            background: rgba(255, 87, 34, 0.08);
        }        
        .section-header.collapsed {
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        }        
        .section-title {
            flex: 1;
            font-size: 16px;
            font-weight: 500;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }        
        .section-icon {
            font-size: 20px;
        }       
        .expand-icon {
            font-size: 20px;
            color: rgba(0, 0, 0, 0.54);
            transition: transform 0.3s;
        }        
        .expand-icon.expanded {
            transform: rotate(180deg);
        }        
        .section-content {
            padding: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
        }        
        .section-content.collapsed {
            display: none;
        }        
        /* Info Item */
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        }        
        .info-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }        
        .info-label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            font-weight: 400;
            flex: 1;
            margin-right: 16px;
        }        
        .info-value {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.87);
            font-weight: 500;
            text-align: right;
            flex: 1.5;
            word-break: break-word;
        }        
        .info-value.highlight {
            color: var(--primary);
            font-weight: 600;
        }        
        .info-value.success {
            color: var(--success);
            font-weight: 500;
        }      
        .info-value.warning {
            color: var(--warning);
            font-weight: 500;
        }        
        /* Document Status */
        .document-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }        
        .status-badge {
            font-size: 10px;
            padding: 2px 8px;
            border-radius: 10px;
            font-weight: 500;
        }        
        .status-uploaded {
            background: #E8F5E8;
            color: var(--success);
        }        
        .status-pending {
            background: #FFF3E0;
            color: var(--warning);
        }        
        .status-icon-small {
            font-size: 16px;
        }        
        /* Summary Cards */
        .summary-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }       
        .summary-card {
            background: var(--surface);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            box-shadow: var(--elevation-1);
            border-left: 3px solid var(--primary);
        }        
        .summary-card-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 4px;
        }       
        .summary-card-label {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
            font-weight: 400;
        }        
        /* Edit Button */
        .edit-button {
            background: none;
            border: 1px solid var(--primary);
            color: var(--primary);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 4px;
        }        
        .edit-button:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        /* Action Buttons - Sticky */
        .action-buttons {
            background: var(--surface);
            padding: 16px 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            position: sticky;
            bottom: 0;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }        
        .button-row {
            display: flex;
            gap: 12px;
        }        
        .outlined-button {
            flex: 1;
            height: 40px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }        
        .outlined-button:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        .contained-button {
            flex: 2;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: var(--primary);
            color: var(--on-primary);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--elevation-1);
        }        
        .contained-button:hover {
            box-shadow: var(--elevation-2);
        }       
        .contained-button:disabled {
            background: #E0E0E0;
            color: #9E9E9E;
            cursor: not-allowed;
            box-shadow: none;
        }        
        .save-draft-button {
            width: 100%;
            height: 36px;
            border: 1px solid #757575;
            border-radius: 4px;
            background: transparent;
            color: #757575;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }        
        .save-draft-button:hover {
            background: rgba(117, 117, 117, 0.04);
            border-color: rgba(0, 0, 0, 0.6);
            color: rgba(0, 0, 0, 0.6);
        }        
        /* Responsive */
        @media (max-width: 768px) {
            .summary-cards {
                grid-template-columns: 1fr;
            }            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }            
            .info-value {
                text-align: left;
            }
        }        
        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }        
        .review-section {
            animation: fadeInUp 0.3s ease-out;
        }        
        /* Scrollbar */
        .content-container::-webkit-scrollbar {
            width: 3px;
        }        
        .content-container::-webkit-scrollbar-track {
            background: transparent;
        }        
        .content-container::-webkit-scrollbar-thumb {
            background: #E0E0E0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <button class="back-button" onclick="goBack()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="header-title">Bước 5/5: Xem lại và xác nhận</div>
            </div>
        </div>               
        <!-- Content Container -->
        <div class="content-container">
            <!-- Summary Status -->
            <div class="summary-status">
                <div class="material-icons status-icon">verified</div>
                <div class="status-title">Thông tin đã hoàn tất</div>
                <div class="status-subtitle">Vui lòng kiểm tra lại thông tin trước khi xác nhận gửi hồ sơ</div>
            </div>           
            <!-- Summary Cards -->
            <div class="summary-cards">
                <div class="summary-card">
                    <div class="summary-card-value" id="loanAmount">50,000,000₫</div>
                    <div class="summary-card-label">Số tiền vay</div>
                </div>
                <div class="summary-card">
                    <div class="summary-card-value" id="loanTerm">90 ngày</div>
                    <div class="summary-card-label">Thời hạn vay</div>
                </div>
            </div>            
            <!-- Step 1: Product Selection -->
            <div class="review-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <div class="section-title">
                        <span class="material-icons section-icon">shopping_cart</span>
                        Bước 1: Chọn sản phẩm
                    </div>
                    <span class="material-icons expand-icon">expand_less</span>
                </div>
                <div class="section-content">
                    <div class="info-item">
                        <div class="info-label">Sản phẩm đã chọn</div>
                        <div class="info-value highlight">Vay tiêu dùng có tài sản bảo đảm</div>
                    </div>
                </div>
            </div>            
            <!-- Step 2: Customer Selection -->
            <div class="review-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <div class="section-title">
                        <span class="material-icons section-icon">person_search</span>
                        Bước 2: Thông tin khách hàng
                    </div>
                    <span class="material-icons expand-icon">expand_less</span>
                </div>
                <div class="section-content">
                    <div class="info-item">
                        <div class="info-label">Phương thức chọn</div>
                        <div class="info-value">Thêm mới khách hàng</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Trạng thái</div>
                        <div class="info-value success">Đã hoàn thành</div>
                    </div>
                </div>
            </div>           
            <!-- Step 3: Borrower Information -->
            <div class="review-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <div class="section-title">
                        <span class="material-icons section-icon">badge</span>
                        Bước 3: Thông tin người vay chính
                    </div>
                    <button class="edit-button" onclick="editStep(3)">
                        <span class="material-icons" style="font-size: 14px;">edit</span>
                        Sửa
                    </button>
                    <span class="material-icons expand-icon">expand_less</span>
                </div>
                <div class="section-content">
                    <div class="info-item">
                        <div class="info-label">Họ và tên</div>
                        <div class="info-value">Nguyễn Văn An</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Số GTTT</div>
                        <div class="info-value">00**********</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Ngày sinh</div>
                        <div class="info-value">15/03/1985</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Giới tính</div>
                        <div class="info-value">Nam</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Số điện thoại</div>
                        <div class="info-value">0912345678</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Hộ khẩu thường trú</div>
                        <div class="info-value">123 Đường ABC, Phường XYZ, Quận 1, TP.HCM</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Địa chỉ hiện tại</div>
                        <div class="info-value">Trùng với địa chỉ thường trú</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Tình trạng hôn nhân</div>
                        <div class="info-value">Đã kết hôn</div>
                    </div>
                </div>
            </div>            
            <!-- Loan Proposal -->
            <div class="review-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <div class="section-title">
                        <span class="material-icons section-icon">request_quote</span>
                        Đề nghị và phương án vay vốn
                    </div>
                    <button class="edit-button" onclick="editStep(3)">
                        <span class="material-icons" style="font-size: 14px;">edit</span>
                        Sửa
                    </button>
                    <span class="material-icons expand-icon">expand_less</span>
                </div>
                <div class="section-content">
                    <div class="info-item">
                        <div class="info-label">Hình thức vay vốn</div>
                        <div class="info-value">Có tài sản bảo đảm</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Vốn tự có</div>
                        <div class="info-value">10,000,000₫</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Số tiền đề nghị vay</div>
                        <div class="info-value highlight">50,000,000₫</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Thời hạn vay</div>
                        <div class="info-value highlight">90 ngày</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Tổng nhu cầu</div>
                        <div class="info-value">60,000,000₫</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">CN/PGD</div>
                        <div class="info-value">CN Tân Bình</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Mục đích sử dụng vốn</div>
                        <div class="info-value">Phục vụ hoạt động kinh doanh</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Phương thức giải ngân</div>
                        <div class="info-value">Chuyển khoản</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Số tài khoản</div>
                        <div class="info-value">**********</div>
                    </div>
                </div>
            </div>            
            <!-- Financial Information -->
            <div class="review-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <div class="section-title">
                        <span class="material-icons section-icon">trending_up</span>
                        Tình hình tài chính
                    </div>
                    <button class="edit-button" onclick="editStep(3)">
                        <span class="material-icons" style="font-size: 14px;">edit</span>
                        Sửa
                    </button>
                    <span class="material-icons expand-icon">expand_less</span>
                </div>
                <div class="section-content">
                    <div class="info-item">
                        <div class="info-label">Nguồn thu</div>
                        <div class="info-value">Kinh doanh</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Doanh số bình quân/ngày</div>
                        <div class="info-value">2,000,000₫</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Thu nhập bình quân/ngày</div>
                        <div class="info-value">800,000₫</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Địa điểm kinh doanh</div>
                        <div class="info-value">456 Đường DEF, Phường GHI, Quận 3, TP.HCM</div>
                    </div>
                </div>
            </div>            
            <!-- Collateral Information -->
            <div class="review-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <div class="section-title">
                        <span class="material-icons section-icon">motorcycle</span>
                        Tài sản bảo đảm
                    </div>
                    <button class="edit-button" onclick="editStep(3)">
                        <span class="material-icons" style="font-size: 14px;">edit</span>
                        Sửa
                    </button>
                    <span class="material-icons expand-icon">expand_less</span>
                </div>
                <div class="section-content">
                    <div class="info-item">
                        <div class="info-label">Loại tài sản</div>
                        <div class="info-value">Mô tô/xe máy</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Giá trị tài sản</div>
                        <div class="info-value highlight">80,000,000₫</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Hiện trạng tài sản</div>
                        <div class="info-value">Đang sử dụng</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Chủ sở hữu</div>
                        <div class="info-value">Nguyễn Văn An</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Biển kiểm soát</div>
                        <div class="info-value">59A1-12345</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Tên tài sản</div>
                        <div class="info-value">Honda Wave Alpha 110cc</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Số khung</div>
                        <div class="info-value">RLHPC31E*EG123456</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Số máy</div>
                        <div class="info-value">PC31E-1234567</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Số giấy đăng ký</div>
                        <div class="info-value">*********</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Nơi cấp</div>
                        <div class="info-value">Phòng CSGT TP.HCM</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Ngày cấp</div>
                        <div class="info-value">15/01/2020</div>
                    </div>
                </div>
            </div>            
            <!-- Step 4: Documents -->
            <div class="review-section">
                <div class="section-header" onclick="toggleSection(this)">
                    <div class="section-title">
                        <span class="material-icons section-icon">description</span>
                        Bước 4: Tài liệu yêu cầu
                    </div>
                    <button class="edit-button" onclick="editStep(4)">
                        <span class="material-icons" style="font-size: 14px;">edit</span>
                        Sửa
                    </button>
                    <span class="material-icons expand-icon">expand_less</span>
                </div>
                <div class="section-content">
                    <div class="info-item">
                        <div class="info-label">GTTT người vay chính</div>
                        <div class="info-value">
                            <div class="document-status">
                                <span class="material-icons status-icon-small" style="color: var(--success);">check_circle</span>
                                <span class="status-badge status-uploaded">Đã nộp</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">GTTT người đồng vay</div>
                        <div class="info-value">
                            <div class="document-status">
                                <span class="material-icons status-icon-small" style="color: var(--warning);">schedule</span>
                                <span class="status-badge status-pending">Chờ nộp</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Giấy tờ tình trạng hôn nhân</div>
                        <div class="info-value">
                            <div class="document-status">
                                <span class="material-icons status-icon-small" style="color: var(--success);">check_circle</span>
                                <span class="status-badge status-uploaded">Đã nộp</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Giấy tờ chứng minh nơi cư trú</div>
                        <div class="info-value">
                            <div class="document-status">
                                <span class="material-icons status-icon-small" style="color: var(--warning);">schedule</span>
                                <span class="status-badge status-pending">Chờ nộp</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Tờ trình thẩm định xe</div>
                        <div class="info-value">
                            <div class="document-status">
                                <span class="material-icons status-icon-small" style="color: var(--success);">check_circle</span>
                                <span class="status-badge status-uploaded">Đã nộp</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Giấy đăng ký xe</div>
                        <div class="info-value">
                            <div class="document-status">
                                <span class="material-icons status-icon-small" style="color: var(--warning);">schedule</span>
                                <span class="status-badge status-pending">Chờ nộp</span>
                            </div>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Chứng nhận hộ kinh doanh</div>
                        <div class="info-value">
                            <div class="document-status">
                                <span class="material-icons status-icon-small" style="color: var(--warning);">schedule</span>
                                <span class="status-badge status-pending">Chờ nộp</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <div class="button-row">
                <button class="outlined-button" onclick="goBack()">
                    ← Quay lại
                </button>
                <button class="contained-button" onclick="confirmSubmit()" id="confirmBtn">
                    → Xác nhận gửi
                </button>
            </div>
            <button class="save-draft-button" onclick="saveDraft()">
                <span class="material-icons">save</span>
                Lưu nháp
            </button>
        </div>
    </div>
    <script>
        // Sample data
        const applicationData = {
            product: "Vay tiêu dùng có tài sản bảo đảm",
            borrower: {
                name: "Nguyễn Văn An",
                idNumber: "00**********",
                birthDate: "15/03/1985",
                gender: "Nam",
                phone: "0912345678",
                permanentAddress: "123 Đường ABC, Phường XYZ, Quận 1, TP.HCM",
                currentAddress: "Trùng với địa chỉ thường trú",
                maritalStatus: "Đã kết hôn"
            },
            loan: {
                type: "Có tài sản bảo đảm",
                ownCapital: "10,000,000₫",
                loanAmount: "50,000,000₫",
                term: "90 ngày",
                totalNeed: "60,000,000₫",
                branch: "CN Tân Bình",
                purpose: "Phục vụ hoạt động kinh doanh",
                disbursementMethod: "Chuyển khoản",
                accountNumber: "**********"
            },
            financial: {
                incomeSource: "Kinh doanh",
                dailyRevenue: "2,000,000₫",
                dailyIncome: "800,000₫",
                businessAddress: "456 Đường DEF, Phường GHI, Quận 3, TP.HCM"
            },
            collateral: {
                type: "Mô tô/xe máy",
                value: "80,000,000₫",
                condition: "Đang sử dụng",
                owner: "Nguyễn Văn An",
                plateNumber: "59A1-12345",
                assetName: "Honda Wave Alpha 110cc",
                frameNumber: "RLHPC31E*EG123456",
                engineNumber: "PC31E-1234567",
                registrationNumber: "*********",
                issuedBy: "Phòng CSGT TP.HCM",
                issuedDate: "15/01/2020"
            },
            documents: {
                borrowerID: { status: "uploaded", label: "Đã nộp" },
                coBorrowerID: { status: "pending", label: "Chờ nộp" },
                marriageCert: { status: "uploaded", label: "Đã nộp" },
                residenceProof: { status: "pending", label: "Chờ nộp" },
                appraisalReport: { status: "uploaded", label: "Đã nộp" },
                vehicleRegistration: { status: "pending", label: "Chờ nộp" },
                businessCert: { status: "pending", label: "Chờ nộp" }
            }
        };        
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadApplicationData();
            updateConfirmButtonState();
        });        
        // Load application data
        function loadApplicationData() {
            // Update summary cards
            document.getElementById('loanAmount').textContent = applicationData.loan.loanAmount;
            document.getElementById('loanTerm').textContent = applicationData.loan.term;            
            // Load saved draft if exists
            loadSavedDraft();
        }        
        // Toggle section expand/collapse
        function toggleSection(headerElement) {
            const content = headerElement.nextElementSibling;
            const expandIcon = headerElement.querySelector('.expand-icon');            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                expandIcon.classList.add('expanded');
                headerElement.classList.remove('collapsed');
            } else {
                content.classList.add('collapsed');
                expandIcon.classList.remove('expanded');
                headerElement.classList.add('collapsed');
            }
        }        
        // Edit step function
        function editStep(stepNumber) {
            const stepNames = {
                1: "Chọn sản phẩm",
                2: "Chọn khách hàng",
                3: "Chi tiết sản phẩm",
                4: "Tài liệu yêu cầu"
            };            
            if (confirm(`Bạn có muốn quay lại ${stepNames[stepNumber]} để chỉnh sửa thông tin?`)) {
                alert(`Chuyển đến Step ${stepNumber} - ${stepNames[stepNumber]}`);
                // In real app: navigate to specific step
            }
        }        
        // Update confirm button state
        function updateConfirmButtonState() {
            const documents = applicationData.documents;
            const requiredDocs = ['borrowerID', 'marriageCert', 'appraisalReport'];            
            // Check if all required documents are uploaded
            const allRequiredUploaded = requiredDocs.every(doc => 
                documents[doc].status === 'uploaded'
            );            
            const confirmBtn = document.getElementById('confirmBtn');          
            if (allRequiredUploaded) {
                confirmBtn.disabled = false;
                confirmBtn.textContent = '→ Xác nhận gửi';
            } else {
                confirmBtn.disabled = true;
                confirmBtn.textContent = '→ Hoàn thành tài liệu';
            }
        }        
        // Confirm submit
        function confirmSubmit() {
            // Check document completeness
            const documents = applicationData.documents;
            const pendingDocs = Object.keys(documents).filter(doc => 
                documents[doc].status === 'pending'
            );            
            if (pendingDocs.length > 0) {
                alert('Vui lòng hoàn thành tải lên tài liệu bắt buộc!');
                return;
            }            
            // Show confirmation dialog
            const confirmText = `
Xác nhận gửi hồ sơ vay vốn:
• Số tiền vay: ${applicationData.loan.loanAmount}
• Thời hạn: ${applicationData.loan.term}
• Người vay: ${applicationData.borrower.name}
Bạn có chắc chắn muốn gửi hồ sơ?
            `.trim();          
            if (confirm(confirmText)) {
                // Show success message
                showSubmissionSuccess();
            }
        }        
        // Show submission success
        function showSubmissionSuccess() {
            // Generate transaction ID
            const transactionId = 'GD' + Date.now().toString().slice(-8);            
            alert(`
✅ Gửi hồ sơ thành công!
Mã giao dịch: ${transactionId}
Trạng thái: Chờ xử lý
Hồ sơ đã được gửi đến bộ phận thẩm định. Bạn sẽ nhận được thông báo khi có kết quả xử lý.
            `.trim());            
            // In real app: navigate to transaction list or details
            console.log('Transaction submitted:', {
                id: transactionId,
                data: applicationData,
                timestamp: new Date().toISOString()
            });
        }        
        // Navigation functions
        function goBack() {
            if (confirm('Bạn có chắc chắn muốn quay lại? Thông tin đã nhập sẽ được lưu.')) {
                alert('Quay lại Step 4 - Tài liệu yêu cầu');
                // In real app: navigate to step 4
            }
        }        
        function saveDraft() {
            const draftData = {
                applicationData: applicationData,
                reviewedAt: new Date().toISOString(),
                step: 5
            };            
            localStorage.setItem('step5Draft', JSON.stringify(draftData));            
            // Show save confirmation
            const saveMessage = document.createElement('div');
            saveMessage.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--success);
                color: white;
                padding: 12px 24px;
                border-radius: 4px;
                font-size: 14px;
                z-index: 1000;
                box-shadow: var(--elevation-2);
            `;
            saveMessage.textContent = 'Đã lưu nháp thành công!';
            document.body.appendChild(saveMessage);            
            setTimeout(() => {
                document.body.removeChild(saveMessage);
            }, 2000);
        }        
        function loadSavedDraft() {
            const savedDraft = localStorage.getItem('step5Draft');
            if (savedDraft) {
                try {
                    const draftData = JSON.parse(savedDraft);
                    console.log('Loaded saved draft:', draftData);
                    // In real app: restore data from draft
                } catch (e) {
                    console.error('Error loading draft:', e);
                }
            }
        }        
        // Auto-save every 30 seconds
        setInterval(saveDraft, 30000);        
        // Initialize sections as expanded
        document.addEventListener('DOMContentLoaded', function() {
            // Collapse all sections except the first one by default
            const sections = document.querySelectorAll('.review-section');
            sections.forEach((section, index) => {
                if (index > 0) {
                    const header = section.querySelector('.section-header');
                    const content = section.querySelector('.section-content');
                    const expandIcon = section.querySelector('.expand-icon');                    
                    content.classList.add('collapsed');
                    expandIcon.classList.remove('expanded');
                    header.classList.add('collapsed');
                }
            });
        });
    </script>
</body>
</html>

---
### Mô tả màn hình

| STT | Trường thông tin                                                     | Kiểu hiển thị           | Kiểu thao tác                   | Bắt buộc nhập                                         | Mô tả chi tiết                                                                                                                                                               |
| --- | -------------------------------------------------------------------- | ----------------------- | ------------------------------- | ----------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | **Bước 1/5 Chọn sản phẩm**                                           |                         |                                 |                                                       |                                                                                                                                                                              |
| 2   | Chọn sản phẩm                                                        | Group button            | Click                           | Có                                                    | Chọn sản phẩm                                                                                                                                                                |
| 3   | Tiếp tục                                                             | Button                  | Click                           | Có                                                    | Nhấn để tiếp tục                                                                                                                                                             |
| 1   | **Bước 2/5 Chọn khách hàng**                                         |                         |                                 |                                                       |                                                                                                                                                                              |
| 4   | Tìm kiếm khách hàng                                                  | Textbox                 | Nhập                            | Không                                                 | Tìm kiếm gần đúng theo họ tên, sđt, số GTTT.                                                                                                                                 |
| 5   | Quét mã QR                                                           | Button                  | Nhập                            | Không                                                 | Quét QR CCCD hoặc thẻ căn cước                                                                                                                                               |
| 6   | Quét NFC                                                             | Button                  | Chọn                            | Không                                                 | Click để quét NFC                                                                                                                                                            |
| 7   | Thêm mới khách hàng                                                  | Button                  | Chọn                            | Không                                                 | Click để thêm mới khách hàng                                                                                                                                                 |
| 8   | Item thông tin khách hàng                                            | Item                    | Click                           | -                                                     | Hiển thị thông tin khách hàng sau tìm kiếm. Mỗi item hiển thị thông tin khách hàng để click chọn là người vay chính. Hiển thị danh sách khách hàng thêm mới gần nhất của CTV |
| 9   | Tiếp tục                                                             | Button                  | Click                           | Có                                                    | Nhấn để tiếp tục                                                                                                                                                             |
| 17  | Quay lại                                                             | Button                  | Click                           | Có                                                    | Nhấn để quay lại bước trước                                                                                                                                                  |
| 10  | **Bước 3/5 Chi tiết sản phẩm**                                       |                         |                                 |                                                       |                                                                                                                                                                              |
| 1   | **Card Người vay chính**                                             | Card                    | Expand/Collapse                 | Có                                                    | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin người vay chính                                                                                         |
| 1   | Có người đồng vay                                                    | toggle                  | Bật/tắt                         | ✔                                                     | Mặc định là tắt toggle. Bật toggle hiển thị card để nhập thông tin người đồng vay                                                                                            |
|     | Thông tin nhận dạng giấy tờ                                          | Lable (Read-only)       |                                 |                                                       |
| 2   | Họ và tên                                                            | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Tối đa 100 ký tự tính cả khoảng trắng.                                                                                                                                       |
| 3   | Số GTTT                                                              | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Cho phép chỉnh sửa, nhập số, tối đa 12 ký tự số. TH đọc từ QR hoặc NFC thì không được chỉnh sửa                                                                              |
| 4   | Ngày cấp                                                             | Datepicker              | Cho phép chỉnh sửa              | ✔                                                     | Định dạng dd/mm/yyyy                                                                                                                                                         |
| 5   | Ngày hết hạn                                                         | Datepicker              | Cho phép chỉnh sửa              | ✔                                                     | Định dạng dd/mm/yyyy                                                                                                                                                         |
| 6   | Nơi cấp                                                              | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Cho phép nhập nơi cấp. Tối đa 50 ký tự                                                                                                                                       |
| 6   | Ngày sinh                                                            | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Định dạng dd/mm/yyyy                                                                                                                                                         |
| 7   | Giới tính                                                            | Dropdown                | Cho phép chỉnh sửa              | ✔                                                     | Cho phép chọn Nam/Nữ/Khác . mặc định là nam                                                                                                                                  |
|     | Hộ khẩu thường trú                                                   |                         |                                 |                                                       |                                                                                                                                                                              |
| 12  | Tỉnh/ thành phố                                                      | Dropdown                | Chọn                            | Có                                                    | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Tỉnh/thành                                                                                                                      |
| 14  | Phường/Xã                                                            | Dropdown                | Tick                            | Có                                                    | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Phường/xã                                                                                                                       |
| 15  | Địa chỉ cụ thể                                                       | Textarea                | Nhập                            | Có                                                    | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký tự.                                                                                                                              |
|     | Thông tin cá nhân*                                                   | Lable(Read-only)        |                                 |                                                       |                                                                                                                                                                              |
| 9   | Tình trạng hôn nhân                                                  | Dropdown                | Chọn                            | ✔                                                     | Độc thân, Đã kết hôn, Ly hôn, Khác. Mặc định chọn Độc thân                                                                                                                   |
| 10  | Số điện thoại                                                        | Textbox                 | Nhập                            | ✔                                                     | Bắt đầu bằng số 0, 10 chữ số. Nếu là khách hàng cũ mặc định lấy ra số điện thoại KH                                                                                          |
| 11  | Địa chỉ hiện tại trùng với địa chỉ thường trú                        | Checkbox                | Click                           | ✘                                                     | Mặc định là tích chọn. Nếu không tích hiển thị thêm các trường thông tin nhập địa chỉ                                                                                        |
|     | Địa chỉ hiện tại                                                     |                         |                                 |                                                       |                                                                                                                                                                              |
| 12  | Tỉnh/ thành phố                                                      | Dropdown                | Chọn                            | Bắt buộc nếu không tích chọn trùng địa chỉ thường trú | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Tỉnh/thành                                                                                                                      |
| 14  | Phường/Xã                                                            | Dropdown                | Tick                            | Bắt buộc nếu không tích chọn trùng địa chỉ thường trú | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Phường/xã                                                                                                                       |
| 15  | Địa chỉ                                                              | Textarea                | Nhập                            | Bắt buộc nếu không tích chọn trùng địa chỉ thường trú | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký tự.                                                                                                                              |
| 5   | Quét mã QR                                                           | Button                  | Nhập                            | Không                                                 | Quét QR CCCD hoặc thẻ căn cước                                                                                                                                               |
| 6   | Quét NFC                                                             | Button                  | Chọn                            | Không                                                 | Click để quét NFC                                                                                                                                                            |
| 1   | **Card Người đồng vay**                                              | Card                    | Expand/Collapse                 | Có                                                    | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin người đồng vay                                                                                          |
|     | Thông tin nhận dạng giấy tờ                                          | Lable (Read-only)       |                                 |                                                       |
| 2   | Họ và tên                                                            | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Tối đa 100 ký tự tính cả khoảng trắng.                                                                                                                                       |
| 3   | Số GTTT                                                              | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Cho phép chỉnh sửa, nhập số, tối đa 12 ký tự số                                                                                                                              |
| 4   | Ngày cấp                                                             | Datepicker              | Cho phép chỉnh sửa              | ✔                                                     | Định dạng dd/mm/yyyy                                                                                                                                                         |
| 5   | Ngày hết hạn                                                         | Datepicker              | Cho phép chỉnh sửa              | ✔                                                     | Định dạng dd/mm/yyyy                                                                                                                                                         |
| 6   | Nơi cấp                                                              | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Cho phép nhập nơi cấp. Tối đa 50 ký tự                                                                                                                                       |
| 6   | Ngày sinh                                                            | Textbox                 | Cho phép chỉnh sửa              | ✔                                                     | Định dạng dd/mm/yyyy                                                                                                                                                         |
| 7   | Giới tính                                                            | Dropdown                | Cho phép chỉnh sửa              | ✔                                                     | Cho phép chọn Nam/Nữ/Khác . mặc định là nam                                                                                                                                  |
|     | Hộ khẩu thường trú                                                   |                         |                                 |                                                       |                                                                                                                                                                              |
| 12  | Tỉnh/ thành phố                                                      | Dropdown                | Chọn                            | Có                                                    | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Tỉnh/thành                                                                                                                      |
| 14  | Phường/Xã                                                            | Dropdown                | Tick                            | Có                                                    | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Phường/xã                                                                                                                       |
| 15  | Địa chỉ cụ thể                                                       | Textarea                | Nhập                            | Có                                                    | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký tự.                                                                                                                              |
|     | Thông tin cá nhân*                                                   | Lable(Read-only)        |                                 |                                                       |                                                                                                                                                                              |
| 9   | Tình trạng hôn nhân                                                  | Dropdown                | Chọn                            | ✔                                                     | Độc thân, Đã kết hôn, Ly hôn, Khác. Mặc định chọn Độc thân                                                                                                                   |
| 10  | Số điện thoại                                                        | Textbox                 | Nhập                            | ✔                                                     | Bắt đầu bằng số 0, 10 chữ số. Nếu là khách hàng cũ mặc định lấy ra số điện thoại KH                                                                                          |
| 11  | Địa chỉ hiện tại trùng với địa chỉ thường trú                        | Checkbox                | Click                           | ✘                                                     | Mặc định là tích chọn. Nếu không tích hiển thị thêm các trường thông tin nhập địa chỉ                                                                                        |
|     | Địa chỉ hiện tại                                                     |                         |                                 |                                                       |                                                                                                                                                                              |
| 12  | Tỉnh/ thành phố                                                      | Dropdown                | Chọn                            | Bắt buộc nếu không tích chọn trùng địa chỉ thường trú | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Tỉnh/thành                                                                                                                      |
| 14  | Phường/Xã                                                            | Dropdown                | Tick                            | Bắt buộc nếu không tích chọn trùng địa chỉ thường trú | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Phường/xã                                                                                                                       |
| 15  | Địa chỉ                                                              | Textarea                | Nhập                            | Bắt buộc nếu không tích chọn trùng địa chỉ thường trú | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký tự.                                                                                                                              |
| 5   | Quét mã QR                                                           | Button                  | Nhập                            | Không                                                 | Quét QR CCCD hoặc thẻ căn cước                                                                                                                                               |
| 6   | Quét NFC                                                             | Button                  | Chọn                            | Không                                                 | Click để quét NFC                                                                                                                                                            |
| 1   | **Card Đề nghị và phương án vay vốn**                                | Card                    | Expand/Collapse                 | Có                                                    | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin đề nghị và phương án vay vốn                                                                            |
| 1   | Hình thức vay vốn                                                    | Radio button            | Chọn 1 giá trị                  | ✔                                                     | Có TSĐB / Không TSĐB, mặc định có TSBĐ. Trường hợp tích chọn không TSBĐ => bỏ qua bước nhập tài sản bảo đảm                                                                  |
|     | Phương án vay vốn                                                    | Lable (read-only)       |                                 |                                                       |                                                                                                                                                                              |
| 2   | Vốn tự có                                                            | Textbox                 | Nhập số                         | X                                                     | Số tiền mà khách hàng hiện có để tham gia vào phương án vay. Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                          |
| 3   | Số tiền đề nghị vay                                                  | Textbox                 | Nhập số                         | ✔                                                     | Số tiền khách hàng muốn vay. Số tiền VND. Tối thiểu Min >= 1.000.000 vnd, tối đa 1 tỉ, định dạng xxx,xxx                                                                     |
| 4   | Thời hạn vay                                                         | Dropdown                | Chọn                            | ✔                                                     | Thời gian vay tính bằng ngày. Bao gồm (30,60,90,120,150,180,210,240,270). Mặc định 60 ngày                                                                                   |
| 5   | Tổng nhu cầu                                                         | Textbox                 | Tự động tính                    | ✔                                                     | Tổng nhu cầu vốn = Vốn tự có + Số tiền đề nghị vay. Số tiền VND, định dạng xxx,xxx                                                                                           |
| 6   | CN/PGD                                                               | Textbox (read-only)     | Không thao tác                  | ✔                                                     | Hệ thống tự điền theo mã chi nhánh của CTV ăn theo                                                                                                                           |
| 7   | Phương thức vay                                                      | Textbox (disabled)      | Không thao tác                  | ✔                                                     | Mặc định là “Vay trả góp”                                                                                                                                                    |
| 8   | Mục đích sử dụng vốn                                                 | Dropdown                | Chọn từ danh sách               | ✔                                                     | Bao gồm: Phục vụ nhu cầu đời sống/ Phục vụ hoạt động kinh doanh/ Khác                                                                                                        |
| 9   | Tên mục đích                                                         | Textbox                 | Nhập chữ                        | ✔                                                     | Nhập tên mục đích nếu Chọn mục đích sử dụng vốn là **khác** . Maxlength: 100 ký tự                                                                                           |
| 10  | Hình thức trả nợ                                                     | Textbox (read-only)     | Không thao tác                  | ✔                                                     | Mặc định hiển thị “Trả góp nợ gốc và lãi tiền vay hàng ngày”                                                                                                                 |
| 11  | Phương thức giải ngân                                                | Radio button            | Chọn 1 giá trị                  | ✔                                                     | Nhận tiền mặt / Chuyển khoản. mặc định chọn nhận tiền mặt                                                                                                                    |
| 12  | Số tài khoản nhận tiền                                               | Dropdown                | Chọn 1 giá trị                  | ✔                                                     | Nếu người dùng chọn phương thức giải ngân là chuyển khoản thì hiển thị danh sách số tài khoản thanh toán ứng với CIF KH đã mở tại KLB                                        |
| 1   | **Card Tình hình tài chính**                                         | Card                    | Expand/Collapse                 | Có                                                    | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin tình hình tài chính                                                                                     |
| 2   | Nguồn thu                                                            | Dropdown                | Chọn                            | ✔                                                     | Bao gồm: Kinh doanh/Từ lương/Khác . Mặc định: Kinh doanh                                                                                                                     |
| 3   | Doanh số bình quân/ ngày                                             | Textbox                 | Nhập số nguyên                  | ✔                                                     | Đơn vị VNĐ,  chỉ hiển thị nếu Nguồn thu là "Kinh doanh". Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                              |
| 4   | Thu nhập bình quân/Ngày                                              | Textbox                 | Nhập số nguyên                  | ✔                                                     | Đơn vị VNĐ, hiển thị với mọi nguồn thu. Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                                               |
| 5   | Địa điểm sản xuất - kinh doanh                                       | Lable                   |                                 |                                                       | chỉ hiển thị nếu Nguồn thu "Kinh doanh"                                                                                                                                      |
| 6   | Tỉnh/ thành phố                                                      | Dropdown                | Chọn                            | ✔                                                     | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Tỉnh/thành phố                                                                                                                  |
| 8   | Phường/Xã                                                            | Dropdown                | Chọn                            | ✔                                                     | Dropdown cho phép tìm kiếm tên địa chỉ phường xã                                                                                                                             |
| 9   | Địa chỉ                                                              | Textarea                | nhập                            | ✔                                                     | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký.                                                                                                                                 |
| 1   | **Card Tài sản bảo đảm**                                             | Card                    | Expand/Collapse                 | Có                                                    | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin tài sản bảo đảm                                                                                         |
| 1   | Loại tài sản                                                         | Textbox(read-only)      | không thao tác                  | Có                                                    | Mặc định là Mô tô/xe máy                                                                                                                                                     |
| 2   | Giá trị tài sản                                                      | Textbox số              | Nhập số nguyên                  | Có                                                    | Nhập giá trị tài sản bằng VNĐ, Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                                                        |
| 3   | Giá trị tài sản (bằng chữ)                                           | Textbox                 | Tự động hiển thị                | Không                                                 | Hiển thị tự động giá trị tài sản bằng chữ, không cho phép chỉnh sửa                                                                                                          |
| 4   | Hiện trạng tài sản                                                   | Textbox                 | Nhập văn bản                    | Có                                                    | Mô tả tình trạng hiện tại của tài sản (ví dụ: Mới, Cũ, Đã qua sử dụng)                                                                                                       |
| 5   | Chủ sở hữu tài sản                                                   | Textbox(Read-only)      | Không thao tác                  | Có                                                    | Hệ thống tự điền theo trường "họ và tên" người vay chính sản                                                                                                                 |
| 6   | Năm sinh                                                             | Date picker(read -only) | Không thao tác                  | Có                                                    | Hệ thống tự điền theo trường "ngày tháng năm sinh người vay chính, định dạng dd/mm/yyyy                                                                                      |
| 1   | **Card Chi tiết tài sản bảo đảm**                                    | Card                    | Expand/Collapse                 | Có                                                    | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin chi tiết tài sản bảo đảm                                                                                |
| 1   | Biển kiểm soát                                                       | Textbox                 | Nhập văn bản                    | Có                                                    | Đọc từ QR. Nhập biển số đăng ký của phương tiện bảo đảm                                                                                                                      |
| 2   | Tên tài sản                                                          | Textbox                 | Nhập văn bản                    | Có                                                    | Đọc từ QR. Nhập tên loại tài sản (ví dụ: Xe máy, Ô tô, Nhà đất, v.v.)                                                                                                        |
| 3   | Số khung                                                             | Textbox                 | Nhập văn bản                    | Có                                                    | Đọc từ QR. Nhập số khung của phương tiện hoặc mã định danh tài sản                                                                                                           |
| 4   | Số máy                                                               | Textbox                 | Nhập văn bản                    | Có                                                    | Đọc từ QR. Nhập số máy hoặc mã số động cơ của tài sản                                                                                                                        |
| 5   | Số giấy chứng nhận đăng ký xe                                        | Textbox                 | Nhập văn bản                    | Có                                                    | Đọc từ QR. Nhập số giấy chứng nhận đăng ký xe                                                                                                                                |
| 6   | Nơi cấp                                                              | Textbox                 | Nhập văn bản                    | Có                                                    | Đọc từ QR. Nhập nơi cấp giấy chứng nhận đăng ký xe                                                                                                                           |
| 7   | Ngày cấp                                                             | Date Picker             | Chọn ngày                       | Có                                                    | Đọc từ QR. Chọn ngày cấp giấy chứng nhận đăng ký xe . Định dạng dd/mm/yyyy                                                                                                   |
| 8   | Tình trạng tài sản khi giao                                          | Dropdown                | Chọn                            | Có                                                    | Hiển thị giá trị từ Cung cấp thông tin tài sản bảo đảm. Gồm: Mới, cũ. Mặc định đang sử dụng                                                                                  |
| 9   | Tổng trị giá tài sản bảo đảm                                         | Textbox số              | Nhập số                         | Có                                                    | Hiển thị giá trị từ Cung cấp thông tin tài sản bảo đảm. Nhập tổng giá trị tài sản bảo đảm (VNĐ) ,Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                      |
| 5   | Quét mã QR                                                           | Button                  | Nhập                            | Không                                                 | Quét QR đăng ký xe                                                                                                                                                           |
| 9   | Tiếp tục                                                             | Button                  | Click                           | Có                                                    | Nhấn để tiếp tục                                                                                                                                                             |
| 17  | Quay lại                                                             | Button                  | Click                           | Có                                                    | Nhấn để quay lại bước trước                                                                                                                                                  |
| 17  | Lưu nháp                                                             | Button                  | Click                           | Có                                                    | Nhấn để lưu nháp lại các thông tin khoản vay đang nhập                                                                                                                       |
| 18  | **Bước 4/5 Tài liệu yêu cầu**                                        |                         |                                 |                                                       |                                                                                                                                                                              |
| 1   | Tài liệu bắt buộc                                                    |                         |                                 |                                                       |                                                                                                                                                                              |
| 1   | GTTT người vay chính                                                 | image                   | chụp ảnh/ tải ảnh/upload file   | Có                                                    | Chụp ảnh/ tải ảnh mặt trước/ mặt sau/ or thông tin hộ chiếu người vay chính. Xem ở dạng scroll ảnh ngang                                                                     |
| 2   | GTTT người đồng vay                                                  | image                   | Chụp ảnh/ tải ảnh / upload file | Có                                                    | Chụp ảnh/tải ảnh mặt trước/ mặt sau/ or thông tin hộ chiếu người đồng chính. Xem ở dạng scroll ảnh ngang                                                                     |
| 3   | Giấy tờ tình trạng hôn nhân, mối quan hệ                             | File upload             | chụp ảnh/ tải ảnh / upload file | Có                                                    | Chụp ảnh/Tải lên giấy tờ chứng minh tình trạng hôn nhân hoặc quan hệ, file ảnh png, pdf  . Tối đa 5MB                                                                        |
| 4   | Giấy tờ chứng minh nơi cư trú                                        | File upload             | chụp ảnh/ tải ảnh / upload file | Có                                                    | Chụp ảnh/Tải lên giấy tờ chứng minh nơi cư trú của người vay hoặc đồng vay, file ảnh png, pdf . Tối đa 5MB                                                                   |
| 5   | Chi tiết                                                             | button                  | Click                           | không                                                 | Click để hiển thị màn hình chứa nội dung chi tiết mô tả thông tin Giấy tờ chứng minh nơi cư trú                                                                              |
| 6   | Tờ trình thẩm định xe mô tô, gắn máy (áp dụng với khoản vay có TSBĐ) | File upload             | chụp ảnh/ tải ảnh / upload file | Có                                                    | Chụp ảnh/Tải lên tờ trình thẩm định tài sản xe mô tô hoặc gắn máy , file ảnh png, pdf . Tối đa 5MB                                                                           |
| 7   | Giấy đăng ký xe  (áp dụng với khoản vay có TSBĐ)                     | File upload             | chụp ảnh/ tải ảnh / upload file | Có                                                    | Chụp ảnh/Tải lên giấy đăng ký xe bảo đảm . file ảnh png, pdf. Tối đa 5MB                                                                                                     |
| 1   | Tài liệu tuỳ chọn                                                    |                         |                                 |                                                       |
| 8   | Chứng nhận hộ kinh doanh, khác (nếu có)                              | File upload             | Chọn tệp để upload              | Không                                                 | Tải lên giấy chứng nhận hộ kinh doanh hoặc các giấy tờ khác liên quan . file ảnh png, pdf. Tối đa 5MB                                                                        |
| 9   | Tiếp tục                                                             | Button                  | Click                           | Có                                                    | Nhấn để tiếp tục                                                                                                                                                             |
| 17  | Quay lại                                                             | Button                  | Click                           | Có                                                    | Nhấn để quay lại bước trước                                                                                                                                                  |
| 17  | Lưu nháp                                                             | Button                  | Click                           | Có                                                    | Nhấn để lưu nháp lại các thông tin khoản vay đang nhập                                                                                                                       |
| 19  | **Bước 5/5 Xem lại và xác nhận**                                     |                         |                                 |                                                       |                                                                                                                                                                              |
|     | **Thông tin người vay chính**                                        | Lable                   |                                 |                                                       |
| 1   | Họ và tên                                                            | Lable                   | read-only                       |                                                       |
| 2   | Số giấy tờ                                                           | Lable                   | read-only                       |                                                       |
| 3   | Ngày cấp                                                             | Lable                   | read-only                       |                                                       |
| 4   | Ngày hết hạn                                                         | Lable                   | read-only                       |                                                       |
| 5   | Nơi cấp                                                              | Lable                   | read-only                       |                                                       |
| 6   | Ngày  sinh                                                           | Lable                   | read-only                       |                                                       |
| 7   | Giới tính                                                            | Lable                   | read-only                       |                                                       |
| 8   | Hộ khẩu thường trú                                                   | Lable                   | read-only                       |                                                       |
| 9   | Địa chỉ hiện tại                                                     | Lable                   | read-only                       |                                                       |
| 10  | Tình trạng hôn nhân                                                  | Lable                   | read-only                       |                                                       |
| 11  | Số điện thoại                                                        | Lable                   | read-only                       |                                                       |
|     | **Đề nghị và phương án vay vốn**                                     | Lable                   |                                 |                                                       |
| 1   | Hình thức vay vốn                                                    | Lable                   | read-only                       |                                                       |
| 2   | Vốn tự có                                                            | Lable                   | read-only                       |                                                       |
| 3   | Số tiền đề nghị vay                                                  | Lable                   | read-only                       |                                                       |
| 4   | Thời hạn vay                                                         | Lable                   | read-only                       |                                                       |
| 5   | Tổng nhu cầu                                                         | Lable                   | read-only                       |                                                       |
| 6   | CN/PGD                                                               | Lable                   | read-only                       |                                                       |
| 7   | Phương thức vay                                                      | Lable                   | read-only                       |                                                       |
| 8   | Mục đích sử dụng vốn                                                 | Lable                   | read-only                       |                                                       |
| 9   | Hình thức trả nợ                                                     | Lable                   | read-only                       |                                                       |
| 10  | Phương thức giải ngân                                                | Lable                   | read-only                       |                                                       |
|     | **Tình hình tài chính**                                              | Lable                   |                                 |                                                       |
| 1   | Nguồn thu                                                            | Lable                   | read-only                       |                                                       |
| 2   | Doanh số bình quân/Ngày                                              | Lable                   | read-only                       |                                                       |
| 3   | Thu nhập bình quân /Ngày                                             | Lable                   | read-only                       |                                                       |
| 4   | Tỉnh/ Thành phố                                                      | Lable                   | read-only                       |                                                       |
| 5   | Địa chỉ cụ thể                                                       | Lable                   | read-only                       |                                                       |
|     | **Tài sản bảo đảm**                                                  | Lable                   |                                 |                                                       |
| 1   | Loại tài sản                                                         | Lable                   | read-only                       |                                                       |
| 2   | Giá trị tài sản                                                      | Lable                   | read-only                       |                                                       |
| 3   | Giá trị tài sản( bằng chữ)                                           | Lable                   | read-only                       |                                                       |
| 4   | Hiện trạng tài sản                                                   | Lable                   | read-only                       |                                                       |
| 5   | Chủ sở hữu tài sản                                                   | Lable                   | read-only                       |                                                       |
| 6   | Năm sinh                                                             | Lable                   | read-only                       |                                                       |
| 7   | Loại đăng ký xe                                                      | Lable                   | read-only                       |                                                       |
| 8   | Biển kiểm soát                                                       | Lable                   | read-only                       |                                                       |
| 9   | Tên tài sản                                                          | Lable                   | read-only                       |                                                       |
| 10  | Số khung                                                             | Lable                   | read-only                       |                                                       |
| 11  | Số máy                                                               | Lable                   | read-only                       |                                                       |
| 12  | Số chứng nhận đăng ký xe                                             | Lable                   | read-only                       |                                                       |
| 13  | Nơi cấp                                                              | Lable                   | read-only                       |                                                       |
| 14  | Ngày cấp                                                             | Lable                   | read-only                       |                                                       |
| 15  | Tổng giá trị tài sản bảo đảm                                         | Lable                   | read-only                       |                                                       |
|     | **Tài liệu**                                                         | Lable                   |                                 |                                                       |
| 1   | GTTT người vay chính                                                 | Lable                   | read-only                       |                                                       | Đã nộp - Chờ nộp                                                                                                                                                             |
| 2   | GTTT người đồng vay                                                  | Lable                   | read-only                       |                                                       | Đã nộp - Chờ nộp                                                                                                                                                             |
| 3   | Giấy tờ tình trạng hôn nhân, mối quan hệ                             | Lable                   | read-only                       |                                                       | Đã nộp - Chờ nộp                                                                                                                                                             |
| 4   | Giấy tờ chứng minh nơi cư trú                                        | Lable                   | read-only                       |                                                       | Đã nộp - Chờ nộp                                                                                                                                                             |
| 6   | Tờ trình thẩm định xe mô tô, gắn máy                                 | Lable                   | read-only                       |                                                       | Đã nộp - Chờ nộp                                                                                                                                                             |
| 7   | Giấy đăng ký xe                                                      | Lable                   | read-only                       |                                                       | Đã nộp - Chờ nộp                                                                                                                                                             |
| 8   | Chứng nhận hộ kinh doanh, khác (nếu có)                              | Lable                   | read-only                       |                                                       | Đã nộp. Không bắt buộc, hỉ hiển thị nếu có                                                                                                                                   |
| 18  | Xác nhận                                                             | Button                  | Click                           |                                                       | Nhấn để xác nhận thông tin                                                                                                                                                   |
| 17  | Quay lại                                                             | Button                  | Click                           | Có                                                    | Nhấn để quay lại bước trước                                                                                                                                                  |
| 17  | Lưu nháp                                                             | Button                  | Click                           | Có                                                    | Nhấn để lưu nháp lại các thông tin khoản vay đang nhập                                                                                                                       |


---

# 3. Màn hình xem chi tiết Giao dịch



<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Chi tiết giao dịch</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        body {
            font-family: 'Roboto', sans-serif;
            background: #f5f5f5;
            color: #212121;
            width: 100%;
            min-height: 100vh;
            overflow-x: hidden;
        }        
        :root {
            --primary: #FF5722;
            --primary-variant: #E64A19;
            --secondary: #03DAC6;
            --background: #FAFAFA;
            --surface: #FFFFFF;
            --error: #B00020;
            --warning: #FF9800;
            --success: #4CAF50;
            --on-primary: #FFFFFF;
            --on-surface: #000000;
            --elevation-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --elevation-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
            --elevation-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
        }        
        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            max-width: 420px;
            margin: 0 auto;
            background: var(--surface);
        }        
        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-variant) 100%);
            color: var(--on-primary);
            padding: 16px 20px;
            box-shadow: var(--elevation-2);
            position: sticky;
            top: 0;
            z-index: 100;
        }        
        .header-content {
            display: flex;
            align-items: center;
            gap: 16px;
        }        
        .back-button {
            background: none;
            border: none;
            color: var(--on-primary);
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.2s;
        }        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }        
        .header-title {
            flex: 1;
            font-size: 18px;
            font-weight: 500;
        }        
        /* Transaction Summary */
        .transaction-summary {
            background: var(--surface);
            padding: 20px;
            margin: 16px;
            border-radius: 12px;
            box-shadow: var(--elevation-1);
            margin-bottom: 16px;
        }        
        .transaction-id {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 8px;
        }        
        .product-name {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            margin-bottom: 12px;
        }        
        .amount-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }        
        .amount {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary);
        }        
        .status-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
        }        
        .status-processing {
            background: #FFF3E0;
            color: var(--warning);
        }        
        .status-approved {
            background: #E8F5E8;
            color: var(--success);
        }       
        .status-rejected {
            background: #FFEBEE;
            color: var(--error);
        }        
        /* Customer Info */
        .customer-info {
            background: var(--surface);
            padding: 16px 20px;
            margin: 0 16px 16px;
            border-radius: 12px;
            box-shadow: var(--elevation-1);
        }        
        .customer-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }        
        .customer-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-variant) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: 600;
        }        
        .customer-details {
            flex: 1;
        }        
        .customer-name {
            font-size: 16px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.87);
            margin-bottom: 4px;
        }        
        .customer-phone {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
        }        
        .customer-actions {
            display: flex;
            gap: 8px;
        }        
        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }        
        .btn-call {
            background: var(--success);
            color: white;
        }        
        .btn-message {
            background: var(--primary);
            color: white;
        }        
        .customer-tags {
            display: flex;
            gap: 6px;
            margin-top: 8px;
        }        
        .tag {
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
        }        
        .tag-vip {
            background: #E3F2FD;
            color: #1976D2;
        }        
        .tag-urgent {
            background: #FFE0B2;
            color: #F57C00;
        }        
        /* Staff Info */
        .staff-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 12px;
        }        
        .staff-item {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
        }        
        .staff-value {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            margin-top: 2px;
        }        
        /* Tab Navigation */
        .tab-navigation {
            background: var(--surface);
            margin: 0 16px 16px;
            border-radius: 12px;
            box-shadow: var(--elevation-1);
            overflow: hidden;
        }        
        .tab-header {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }        
        .tab-header::-webkit-scrollbar {
            display: none;
        }        
        .tab-button {
            flex: 1;
            min-width: 80px;
            padding: 14px 8px;
            border: none;
            background: transparent;
            color: rgba(0, 0, 0, 0.6);
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            position: relative;
        }        
        .tab-button.active {
            color: var(--primary);
        }        
        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary);
        }        
        .tab-content {
            padding: 20px;
            display: none;
        }        
        .tab-content.active {
            display: block;
        }        
        /* Detail Cards */
        .detail-card {
            background: var(--surface);
            margin: 0 16px 16px;
            border-radius: 12px;
            box-shadow: var(--elevation-1);
            overflow: hidden;
        }        
        .card-header {
            background: rgba(255, 87, 34, 0.05);
            border-left: 4px solid var(--primary);
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: background 0.2s;
        }        
        .card-header:hover {
            background: rgba(255, 87, 34, 0.08);
        }        
        .card-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 500;
            color: var(--primary);
        }        
        .card-icon {
            font-size: 20px;
        }        
        .expand-icon {
            font-size: 20px;
            color: rgba(0, 0, 0, 0.54);
            transition: transform 0.3s;
        }        
        .expand-icon.expanded {
            transform: rotate(180deg);
        }        
        .card-content {
            padding: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
        }        
        .card-content.collapsed {
            display: none;
        }        
        /* Info Items */
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.08);
        }        
        .info-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }        
        .info-label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            font-weight: 400;
            flex: 1;
            margin-right: 16px;
        }        
        .info-value {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.87);
            font-weight: 500;
            text-align: right;
            flex: 1.5;
            word-break: break-word;
        }        
        .info-value.highlight {
            color: var(--primary);
            font-weight: 600;
        }        
        /* Process Timeline */
        .process-timeline {
            padding: 20px;
        }        
        .timeline-item {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
            position: relative;
        }        
        .timeline-item:last-child {
            margin-bottom: 0;
        }        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 32px;
            bottom: -8px;
            width: 2px;
            background: #E0E0E0;
        }        
        .timeline-item:last-child::before {
            display: none;
        }       
        .timeline-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }        
        .timeline-icon.completed {
            background: var(--success);
            color: white;
        }        
        .timeline-icon.processing {
            background: var(--warning);
            color: white;
        }        
        .timeline-icon.pending {
            background: #E0E0E0;
            color: #9E9E9E;
        }        
        .timeline-content {
            flex: 1;
        }        
        .timeline-title {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
            margin-bottom: 4px;
        }        
        .timeline-subtitle {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            margin-bottom: 8px;
        }        
        .timeline-meta {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
        }        
        /* Document Grid */
        .document-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }        
        .document-item {
            background: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }        
        .document-item:hover {
            background: rgba(0, 0, 0, 0.04);
        }        
        .document-icon {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 8px;
        }        
        .document-name {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.87);
            font-weight: 500;
            margin-bottom: 4px;
        }        
        .document-status {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }        
        .doc-uploaded {
            background: #E8F5E8;
            color: var(--success);
        }        
        .doc-pending {
            background: #FFF3E0;
            color: var(--warning);
        }        
        /* Action Buttons */
        .action-buttons {
            background: var(--surface);
            padding: 16px 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.12);
            position: sticky;
            bottom: 0;
            width: 100%;
            display: flex;
            gap: 12px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
            z-index: 50;
        }        
        .outlined-button {
            flex: 1;
            height: 40px;
            border: 1px solid var(--error);
            border-radius: 4px;
            background: transparent;
            color: var(--error);
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }        
        .outlined-button:hover {
            background: rgba(176, 0, 32, 0.04);
        }        
        .contained-button {
            flex: 1;
            height: 40px;
            border: none;
            border-radius: 4px;
            background: var(--success);
            color: white;
            font-size: 14px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--elevation-1);
        }        
        .contained-button:hover {
            box-shadow: var(--elevation-2);
        }        
        /* CIC Section */
        .cic-section {
            margin-bottom: 16px;
        }        
        .cic-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }        
        .cic-title {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
        }        
        .check-cic-btn {
            padding: 8px 16px;
            border: 1px solid var(--primary);
            border-radius: 4px;
            background: transparent;
            color: var(--primary);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }        
        .check-cic-btn:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        .cic-report {
            background: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            padding: 16px;
            margin-top: 12px;
        }        
        .report-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }        
        .report-name {
            font-size: 14px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
        }        
        .report-status {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }        
        .status-success {
            background: #E8F5E8;
            color: var(--success);
        }        
        .status-error {
            background: #FFEBEE;
            color: var(--error);
        }        
        /* Notes Section */
        .notes-section {
            padding: 20px;
        }        
        .add-note-btn {
            width: 100%;
            padding: 12px;
            border: 1px dashed var(--primary);
            border-radius: 8px;
            background: transparent;
            color: var(--primary);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 16px;
        }        
        .add-note-btn:hover {
            background: rgba(255, 87, 34, 0.04);
        }        
        .note-item {
            background: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
        }        
        .note-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }        
        .note-author {
            font-size: 12px;
            font-weight: 500;
            color: var(--primary);
        }        
        .note-date {
            font-size: 10px;
            color: rgba(0, 0, 0, 0.4);
        }        
        .note-content {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.87);
            line-height: 1.4;
        }        
        /* CTV Info */
        .ctv-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }        
        .stat-card {
            background: rgba(0, 0, 0, 0.02);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }        
        .stat-value {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 4px;
        }        
        .stat-label {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
        }        
        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(var(--primary) 0deg 216deg, #E0E0E0 216deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            position: relative;
        }        
        .progress-circle::before {
            content: '';
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }       
        .progress-text {
            position: relative;
            z-index: 1;
            font-size: 12px;
            font-weight: 600;
            color: var(--primary);
        }        
        /* Responsive */
        @media (max-width: 480px) {
            .amount-status {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }            
            .staff-info {
                grid-template-columns: 1fr;
            }            
            .document-grid {
                grid-template-columns: 1fr;
            }            
            .ctv-stats {
                grid-template-columns: 1fr;
            }
        }        
        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }        
        .detail-card {
            animation: fadeInUp 0.3s ease-out;
        }        
        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 3px;
        }        
        ::-webkit-scrollbar-track {
            background: transparent;
        }        
        ::-webkit-scrollbar-thumb {
            background: #E0E0E0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <button class="back-button" onclick="goBack()">
                    <span class="material-icons">arrow_back</span>
                </button>
                <div class="header-title">Chi tiết giao dịch</div>
            </div>
        </div>        
        <!-- Transaction Summary -->
        <div class="transaction-summary">
            <div class="transaction-id">GD00**********</div>
            <div class="product-name">Vay trả góp ngày</div>
            <div class="amount-status">
                <div class="amount">50,000,000₫</div>
                <div class="status-badge status-processing">Đang xử lý</div>
            </div>
        </div>        
        <!-- Customer Info -->
        <div class="customer-info">
            <div class="customer-header">
                <div class="customer-avatar">NA</div>
                <div class="customer-details">
                    <div class="customer-name">Nguyễn Văn An</div>
                    <div class="customer-phone">0987654321</div>
                </div>
                <div class="customer-actions">
                    <button class="action-btn btn-call" onclick="makeCall('0987654321')">
                        <span class="material-icons">phone</span>
                    </button>
                    <button class="action-btn btn-message" onclick="sendMessage('0987654321')">
                        <span class="material-icons">message</span>
                    </button>
                </div>
            </div>            
            <div class="customer-tags">
                <span class="tag tag-vip">VIP</span>
                <span class="tag tag-urgent">Gấp</span>
            </div>            
            <div class="staff-info">
                <div class="staff-item">
                    <div>Người tạo hồ sơ</div>
                    <div class="staff-value">Nguyễn Thị Lan</div>
                </div>
                <div class="staff-item">
                    <div>Người đang xử lý</div>
                    <div class="staff-value">Trần Văn Minh</div>
                </div>
                <div class="staff-item">
                    <div>Chi nhánh/PGD</div>
                    <div class="staff-value">CN Hà Nội</div>
                </div>
                <div class="staff-item">
                    <div>Ngày tạo</div>
                    <div class="staff-value">15/11/2024</div>
                </div>
            </div>
        </div>        
        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <div class="tab-header">
                <button class="tab-button active" onclick="switchTab('chi-tiet')">Chi tiết</button>
                <button class="tab-button" onclick="switchTab('tien-trinh')">Tiến trình</button>
                <button class="tab-button" onclick="switchTab('ho-so')">Hồ sơ</button>
                <button class="tab-button" onclick="switchTab('cic')">CIC</button>
                <button class="tab-button" onclick="switchTab('ghi-chu')">Ghi chú</button>
                <button class="tab-button" onclick="switchTab('ctv')">Thông tin CTV</button>
            </div>            
            <!-- Tab Chi tiết -->
            <div class="tab-content active" id="chi-tiet">
                <div class="detail-card">
                    <div class="card-header" onclick="toggleCard(this)">
                        <div class="card-title">
                            <span class="material-icons card-icon">person</span>
                            Thông tin người vay chính
                        </div>
                        <span class="material-icons expand-icon">expand_less</span>
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <div class="info-label">Họ và tên</div>
                            <div class="info-value">Nguyễn Văn An</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Loại giấy tờ</div>
                            <div class="info-value">CCCD</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Số giấy tờ</div>
                            <div class="info-value">00**********</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Ngày cấp</div>
                            <div class="info-value">15/03/2020</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Ngày hết hạn</div>
                            <div class="info-value">15/03/2035</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Nơi cấp</div>
                            <div class="info-value">Cục Cảnh sát QLHC về TTXH</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Ngày sinh</div>
                            <div class="info-value">15/03/1985</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Giới tính</div>
                            <div class="info-value">Nam</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Hộ khẩu thường trú</div>
                            <div class="info-value">123 Đường ABC, Phường XYZ, Quận 1, TP.HCM</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Địa chỉ hiện tại</div>
                            <div class="info-value">Trùng với địa chỉ thường trú</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Tình trạng hôn nhân</div>
                            <div class="info-value">Đã kết hôn</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Số điện thoại</div>
                            <div class="info-value">0987654321</div>
                        </div>
                    </div>
                </div>                
                <div class="detail-card">
                    <div class="card-header" onclick="toggleCard(this)">
                        <div class="card-title">
                            <span class="material-icons card-icon">request_quote</span>
                            Đề nghị và phương án vay vốn
                        </div>
                        <span class="material-icons expand-icon">expand_less</span>
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <div class="info-label">Hình thức vay vốn</div>
                            <div class="info-value">Có tài sản bảo đảm</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Vốn tự có</div>
                            <div class="info-value">10,000,000₫</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Số tiền đề nghị vay</div>
                            <div class="info-value highlight">50,000,000₫</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Thời hạn vay</div>
                            <div class="info-value">90 ngày</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Tổng nhu cầu</div>
                            <div class="info-value">60,000,000₫</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Chi nhánh/PGD</div>
                            <div class="info-value">CN Hà Nội</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Phương thức vay</div>
                            <div class="info-value">Vay trả góp</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Mục đích sử dụng vốn</div>
                            <div class="info-value">Phục vụ hoạt động kinh doanh</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Hình thức trả nợ</div>
                            <div class="info-value">Trả góp gốc và lãi hàng ngày</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Phương thức giải ngân</div>
                            <div class="info-value">Chuyển khoản</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Số tài khoản nhận tiền</div>
                            <div class="info-value">**********</div>
                        </div>
                    </div>
                </div>                
                <div class="detail-card">
                    <div class="card-header" onclick="toggleCard(this)">
                        <div class="card-title">
                            <span class="material-icons card-icon">trending_up</span>
                            Tình hình tài chính
                        </div>
                        <span class="material-icons expand-icon">expand_less</span>
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <div class="info-label">Nguồn thu</div>
                            <div class="info-value">Kinh doanh</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Doanh số bình quân/ngày</div>
                            <div class="info-value">2,000,000₫</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Thu nhập bình quân/ngày</div>
                            <div class="info-value">800,000₫</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Địa điểm sản xuất - kinh doanh</div>
                            <div class="info-value">456 Đường DEF, Phường GHI, Quận 3, TP.HCM</div>
                        </div>
                    </div>
                </div>                
                <div class="detail-card">
                    <div class="card-header" onclick="toggleCard(this)">
                        <div class="card-title">
                            <span class="material-icons card-icon">security</span>
                            Tài sản bảo đảm
                        </div>
                        <span class="material-icons expand-icon">expand_less</span>
                    </div>
                    <div class="card-content">
                        <div class="info-item">
                            <div class="info-label">Loại tài sản</div>
                            <div class="info-value">Mô tô/xe máy</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Giá trị tài sản</div>
                            <div class="info-value highlight">80,000,000₫</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Giá trị tài sản (bằng chữ)</div>
                            <div class="info-value">Tám mươi triệu đồng</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Hiện trạng tài sản</div>
                            <div class="info-value">Đang sử dụng</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Chủ sở hữu tài sản</div>
                            <div class="info-value">Nguyễn Văn An</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Năm sinh</div>
                            <div class="info-value">1985</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Biển kiểm soát</div>
                            <div class="info-value">59A1-12345</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Tên tài sản</div>
                            <div class="info-value">Honda Wave Alpha 110cc</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Số khung</div>
                            <div class="info-value">RLHPC31E*EG123456</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Số máy</div>
                            <div class="info-value">PC31E-1234567</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Số giấy chứng nhận đăng ký xe</div>
                            <div class="info-value">*********</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Nơi cấp</div>
                            <div class="info-value">Phòng CSGT TP.HCM</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Ngày cấp</div>
                            <div class="info-value">15/01/2020</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Tổng trị giá tài sản bảo đảm</div>
                            <div class="info-value highlight">80,000,000₫</div>
                        </div>
                    </div>
                </div>
            </div>            
            <!-- Tab Tiến trình -->
            <div class="tab-content" id="tien-trinh">
                <div class="process-timeline">
                    <div class="timeline-item">
                        <div class="timeline-icon completed">
                            <span class="material-icons">check</span>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">Tạo hồ sơ</div>
                            <div class="timeline-subtitle">Tạo hồ sơ thành công</div>
                            <div class="timeline-meta">15/11/2024 09:30 - Nguyễn Thị Lan (CTV001)</div>
                        </div>
                    </div>                    
                    <div class="timeline-item">
                        <div class="timeline-icon processing">
                            <span class="material-icons">hourglass_empty</span>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">CBBH xử lý hồ sơ</div>
                            <div class="timeline-subtitle">Đang xử lý</div>
                            <div class="timeline-meta">15/11/2024 10:15 - Trần Văn Minh (CBBH002)</div>
                        </div>
                    </div>                    
                    <div class="timeline-item">
                        <div class="timeline-icon pending">
                            <span class="material-icons">schedule</span>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">Phê duyệt tại ĐVKD</div>
                            <div class="timeline-subtitle">Chờ phê duyệt</div>
                            <div class="timeline-meta">Chưa xử lý</div>
                        </div>
                    </div>                    
                    <div class="timeline-item">
                        <div class="timeline-icon pending">
                            <span class="material-icons">schedule</span>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">Phê duyệt tại khu vực</div>
                            <div class="timeline-subtitle">Chờ phê duyệt</div>
                            <div class="timeline-meta">Chưa xử lý</div>
                        </div>
                    </div>                    
                    <div class="timeline-item">
                        <div class="timeline-icon pending">
                            <span class="material-icons">schedule</span>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-title">Xác nhận giải ngân</div>
                            <div class="timeline-subtitle">Chờ xác nhận</div>
                            <div class="timeline-meta">Chưa xử lý</div>
                        </div>
                    </div>
                </div>
            </div>            
            <!-- Tab Hồ sơ -->
            <div class="tab-content" id="ho-so">
                <div class="detail-card">
                    <div class="card-header" onclick="toggleCard(this)">
                        <div class="card-title">
                            <span class="material-icons card-icon">description</span>
                            Tài liệu gốc
                        </div>
                        <span class="material-icons expand-icon">expand_less</span>
                    </div>
                    <div class="card-content">
                        <div class="document-grid">
                            <div class="document-item" onclick="viewDocument('gttt-main')">
                                <div class="material-icons document-icon">badge</div>
                                <div class="document-name">GTTT người vay chính</div>
                                <div class="document-status doc-uploaded">Đã nộp</div>
                            </div>
                            <div class="document-item" onclick="viewDocument('gttt-co')">
                                <div class="material-icons document-icon">badge</div>
                                <div class="document-name">GTTT người đồng vay</div>
                                <div class="document-status doc-pending">Chờ nộp</div>
                            </div>
                            <div class="document-item" onclick="viewDocument('marriage')">
                                <div class="material-icons document-icon">favorite</div>
                                <div class="document-name">Giấy tờ hôn nhân</div>
                                <div class="document-status doc-uploaded">Đã nộp</div>
                            </div>
                            <div class="document-item" onclick="viewDocument('residence')">
                                <div class="material-icons document-icon">home</div>
                                <div class="document-name">Chứng minh nơi cư trú</div>
                                <div class="document-status doc-pending">Chờ nộp</div>
                            </div>
                            <div class="document-item" onclick="viewDocument('appraisal')">
                                <div class="material-icons document-icon">assessment</div>
                                <div class="document-name">Tờ trình thẩm định xe</div>
                                <div class="document-status doc-uploaded">Đã nộp</div>
                            </div>
                            <div class="document-item" onclick="viewDocument('registration')">
                                <div class="material-icons document-icon">directions_car</div>
                                <div class="document-name">Giấy đăng ký xe</div>
                                <div class="document-status doc-pending">Chờ nộp</div>
                            </div>
                        </div>
                    </div>
                </div>                
                <div class="detail-card">
                    <div class="card-header" onclick="toggleCard(this)">
                        <div class="card-title">
                            <span class="material-icons card-icon">attach_file</span>
                            Tài liệu bổ sung
                        </div>
                        <span class="material-icons expand-icon">expand_more</span>
                    </div>
                    <div class="card-content collapsed">
                        <div class="document-grid">
                            <div class="document-item" onclick="viewDocument('business')">
                                <div class="material-icons document-icon">business</div>
                                <div class="document-name">Chứng nhận kinh doanh</div>
                                <div class="document-status doc-uploaded">Đã nộp</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>            
            <!-- Tab CIC -->
            <div class="tab-content" id="cic">
                <div class="cic-section">
                    <div class="cic-header">
                        <div class="cic-title">Người vay chính - Nguyễn Văn An</div>
                        <button class="check-cic-btn" onclick="checkCIC('main')">Kiểm tra CIC</button>
                    </div>
                    <div class="info-item">
                        <div class="info-label">CIF</div>
                        <div class="info-value">CIF*********</div>
                    </div>                    
                    <div class="cic-report">
                        <div class="report-header">
                            <div class="report-name">Bản tin R11A</div>
                            <div class="report-status status-success">Thành công</div>
                        </div>
                        <div class="info-item" style="border: none; padding: 4px 0;">
                            <div class="info-label">Ngày tra cứu</div>
                            <div class="info-value">15/11/2024</div>
                        </div>
                        <div class="info-item" style="border: none; padding: 4px 0;">
                            <div class="info-label">Ngày hết hạn</div>
                            <div class="info-value">15/12/2024</div>
                        </div>
                        <div class="info-item" style="border: none; padding: 4px 0;">
                            <div class="info-label">Người tra cứu</div>
                            <div class="info-value">Trần Văn Minh (CBBH002)</div>
                        </div>
                    </div>
                </div>                
                <div class="cic-section">
                    <div class="cic-header">
                        <div class="cic-title">Người đồng vay - Nguyễn Thị Bình</div>
                        <button class="check-cic-btn" onclick="checkCIC('co')">Kiểm tra CIC</button>
                    </div>
                    <div class="info-item">
                        <div class="info-label">CIF</div>
                        <div class="info-value">CIF001234568</div>
                    </div>                    
                    <div class="cic-report">
                        <div class="report-header">
                            <div class="report-name">Bản tin R11A</div>
                            <div class="report-status status-error">Lỗi</div>
                        </div>
                        <div class="info-item" style="border: none; padding: 4px 0;">
                            <div class="info-label">Ngày tra cứu</div>
                            <div class="info-value">15/11/2024</div>
                        </div>
                        <div class="info-item" style="border: none; padding: 4px 0;">
                            <div class="info-label">Trạng thái</div>
                            <div class="info-value">Kết nối thất bại</div>
                        </div>
                        <button class="check-cic-btn" style="margin-top: 8px;" onclick="retryCIC('co')">Thực hiện lại</button>
                    </div>
                </div>
            </div>            
            <!-- Tab Ghi chú -->
            <div class="tab-content" id="ghi-chu">
                <div class="notes-section">
                    <button class="add-note-btn" onclick="addNote()">
                        <span class="material-icons" style="vertical-align: middle; margin-right: 8px;">add</span>
                        Thêm ghi chú
                    </button>                    
                    <div class="note-item">
                        <div class="note-meta">
                            <div class="note-author">Trần Văn Minh</div>
                            <div class="note-date">15/11/2024 14:30</div>
                        </div>
                        <div class="note-content">Khách hàng cần bổ sung thêm giấy tờ chứng minh thu nhập. Đã liên hệ CTV để hướng dẫn.</div>
                    </div>                    
                    <div class="note-item">
                        <div class="note-meta">
                            <div class="note-author">Nguyễn Thị Lan</div>
                            <div class="note-date">15/11/2024 10:15</div>
                        </div>
                        <div class="note-content">Đã tải lên đầy đủ tài liệu theo yêu cầu. Khách hàng có nhu cầu giải ngân gấp.</div>
                    </div>
                </div>
            </div>            
            <!-- Tab Thông tin CTV -->
            <div class="tab-content" id="ctv">
                <div class="customer-info" style="margin: 0;">
                    <div class="customer-header">
                        <div class="customer-avatar">NL</div>
                        <div class="customer-details">
                            <div class="customer-name">Nguyễn Thị Lan</div>
                            <div class="customer-phone">Mã CTV: CTV001</div>
                        </div>
                    </div>
                </div>                
                <!-- Thống kê -->
                <div style="padding: 20px;">
                    <h3 style="margin-bottom: 16px; color: var(--primary);">Thống kê</h3>                    
                    <div class="ctv-stats">
                        <div class="stat-card">
                            <div class="stat-value">500M</div>
                            <div class="stat-label">Hạn mức được cấp</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">200M</div>
                            <div class="stat-label">Giá trị TSBĐ</div>
                        </div>
                        <div class="stat-card">
                            <div class="progress-circle">
                                <div class="progress-text">60%</div>
                            </div>
                            <div class="stat-label">HMBL tín chấp còn lại</div>
                        </div>
                        <div class="stat-card">
                            <div class="progress-circle" style="background: conic-gradient(var(--primary) 0deg 288deg, #E0E0E0 288deg 360deg);">
                                <div class="progress-text">80%</div>
                            </div>
                            <div class="stat-label">HMBL TSBĐ còn lại</div>
                        </div>
                    </div>                    
                    <h3 style="margin: 24px 0 16px; color: var(--primary);">Báo cáo nhanh</h3>                    
                    <div class="ctv-stats">
                        <div class="stat-card">
                            <div class="stat-value">5M</div>
                            <div class="stat-label">Tiền chậm nộp</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">3</div>
                            <div class="stat-label">Ngày chậm nộp</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">150M</div>
                            <div class="stat-label">Dư nợ BQ/tháng</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">25</div>
                            <div class="stat-label">Tổng khách hàng</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="outlined-button" onclick="rejectTransaction()">
                Từ chối
            </button>
            <button class="contained-button" onclick="approveTransaction()">
                Chuyển cấp PD
            </button>
        </div>
    </div>    
    <script>
        // Tab switching
        function switchTab(tabId) {
            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));            
            // Add active class to clicked tab
            event.target.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }        
        // Toggle card expand/collapse
        function toggleCard(headerElement) {
            const content = headerElement.nextElementSibling;
            const expandIcon = headerElement.querySelector('.expand-icon');            
            if (content.classList.contains('collapsed')) {
                content.classList.remove('collapsed');
                expandIcon.classList.add('expanded');
            } else {
                content.classList.add('collapsed');
                expandIcon.classList.remove('expanded');
            }
        }        
        // Action functions
        function goBack() {
            if (confirm('Bạn có muốn quay lại danh sách giao dịch?')) {
                alert('Quay lại danh sách giao dịch');
            }
        }        
        function makeCall(phone) {
            alert(`Gọi điện đến số: ${phone}`);
        }        
        function sendMessage(phone) {
            alert(`Gửi tin nhắn đến số: ${phone}`);
        }        
        function viewDocument(docType) {
            alert(`Xem tài liệu: ${docType}`);
        }        
        function checkCIC(type) {
            alert('Hiển thị popup chọn bản tin CIC để tra cứu');
        }        
        function retryCIC(type) {
            alert('Thực hiện lại tra cứu CIC');
        }        
        function addNote() {
            const note = prompt('Nhập nội dung ghi chú (tối đa 200 ký tự):');
            if (note && note.length <= 200) {
                alert('Đã thêm ghi chú thành công!');
                // In real app: add note to list
            } else if (note) {
                alert('Ghi chú không được vượt quá 200 ký tự!');
            }
        }        
        function rejectTransaction() {
            const reason = prompt('Nhập lý do từ chối (tối đa 200 ký tự):');
            if (reason && reason.length <= 200) {
                if (confirm('Bạn có chắc chắn muốn từ chối giao dịch này?')) {
                    alert('Đã từ chối giao dịch thành công!');
                }
            } else if (reason) {
                alert('Lý do từ chối không được vượt quá 200 ký tự!');
            }
        }        
        function approveTransaction() {
            if (confirm('Bạn có chắc chắn muốn chuyển cấp phê duyệt?')) {
                alert('Hiển thị màn hình chuyển cấp phê duyệt');
                // In real app: navigate to approval transfer screen
            }
        }        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-collapse some cards for better UX
            const cards = document.querySelectorAll('.detail-card');
            cards.forEach((card, index) => {
                if (index > 0) { // Keep first card expanded
                    const content = card.querySelector('.card-content');
                    const expandIcon = card.querySelector('.expand-icon');
                    content.classList.add('collapsed');
                    expandIcon.classList.remove('expanded');
                }
            });
        });
    </script>
</body>
</html>

## 3.1 Mô tả UI/UX màn hình xem chi tiết giao dịch

| STT | Trường thông tin       | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                          |
| --- | ---------------------- | ------------- | ------------- | --------------------------------------- |
| 1   | Mã giao dịch           | Text          | Read only     |                                         |
| 2   | Tên sản phẩm           | Text          | Read only     |                                         |
| 3   | Số tiền giao dịch      | Text          | Read only     | Hiển thị số tiền của giao dịch          |
| 7   | Trạng thái giao dịch   | Text          | Read only     | Hiển thị trạng thái chung của giao dịch |
| 8   | Component khách hàng   |               |               |                                         |
| 9   | Họ và tên              | Text          | Read only     |                                         |
| 10  | Gọi điện               | Text          | Read only     |                                         |
| 11  | Nhắn tin               | Text          | Read only     |                                         |
| 12  | Tag                    | Text          | Read only     |                                         |
| 13  | Người tạo hồ sơ        | Text          | Read only     | Họ tên - user                           |
| 14  | Người đang xử lý hồ sơ | Text          | Read only     | Họ tên - user                           |
| 15  | Chi nhánh/PGD          | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch         |
| 15  | Tab chi tiết           | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch         |
| 15  | Tab tiến trình         | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch         |
| 15  | Tab hồ sơ              | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch         |
| 15  | Tab CIC                | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch         |
| 15  | Tab ghi chú            | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch         |
| 15  | Tab thông tin CTV      | Text          | Read only     | Chi nhánh/PGD quản lý giao dịch         |
---

**Mô tả action button phê duyệt màn hình chi tiết giao dịch với các role user**
>> **Chú ý**: Theo luồng phê duyệt mới: CBBH thẩm định phê duyệt hồ sơ, GDV chỉ thực hiện In hồ sơ và xác nhận giải ngân sau khi hồ sơ đã ký KH thành công

>> Lưu ý: Sau khi bộ hồ sơ được phê duyệt cuối (PGD/GĐCN hoặc GĐKV), hệ thống tự động sinh bộ hồ sơ gen ký cho khách hàng. Sau khi đã ký với KH, GDV vào app/web xác nhận giải ngân cho khách hàng.

| STT | Role                 | Thao tác                   | Mô tả                                                                                                   | Xử lý chi tiết                                                                                                                                                                               |
| --- | -------------------- | -------------------------- | ------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | CTV và các role khác | -                          | Không hiển thị button phê duyệt, từ chối. Chỉ cho phép view thông tin giao dịch                         | -                                                                                                                                                                                            |
| 2   | CBBH                 | 'Từ chối'; 'Chuyển cấp PD' | Hiển thị 2 button để từ chối hoặc chuyển cấp PD                                                         | Nếu CBBH click chuyển cấp PD, hiển thị **màn hình chuyển cấp phê duyệt**                                                                                                                     |
| 4   | PGĐ/GĐCN             | 'Từ chối'; 'Phê duyệt'     | Hiển thị button từ chối và phê duyệt giao dịch                                                          | Nếu PGĐ/GĐCN click phê duyệt, hiển thị popup cho phép chọn ('Phê duyệt trực tiếp'; 'Chuyển cấp phê duyệt'). Nếu chọn button "Chuyển cấp phê duyệt" -> hệ thống chuyển giao dịch lên cấp GĐKV |
| 5   | GĐKV                 | 'Từ chối'; 'Phê duyệt'     | Hiển thị button từ chối và phê duyệt giao dịch                                                          | Click từ chối hiển thị popup cho nhập lý do từ chối, tối đa 200 ký tự. Click phê duyệt để duyệt giao dịch                                                                                    |
| 2   | GDV                  | 'Xác nhận giải ngân'       | Sau khi hồ sơ đã ký kết khách hàng, GDV vào xác nhận để đánh dấu khoản vay hoàn thành, có thể giải ngân |                                                                                                                                                                                              |

---

**Mô tả UI/UX màn hình màn hình chuyển cấp phê duyệt**
| STT | Trường thông tin                  | Kiểu hiển thị      | Kiểu thao tác | Bắt buộc nhập | Mô tả chi tiết                                                                                                                                                                         |
| --- | --------------------------------- | ------------------ | ------------- | ------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | Header                            | Text + Icon        | Read only     | Không         | Tiêu đề "Thông tin đơn vị tiếp nhận" với nút back ở góc trái                                                                                                                           |
| 2   | Thông tin cấp phê duyệt           | Dropdown/search    | Chọn          | Có            | Chọn người phê duyệt từ danh sách, danh sách gồm họ tên chức danh, hiển thị placeholder "Người phê duyệt". Danh sách bao gồm user PGĐCN/ GĐCN/GĐKV quản lý CN/PGD phát sinh giao dịch. |
| 3   | Tên đơn vị đại diện               | Textbox (disabled) | Read only     | Có            | Hiển thị tên chi nhánh/PGD nhận hồ sơ, không cho phép chỉnh sửa                                                                                                                        |
| 4   | Mã CN/PGD                         | Textbox (disabled) | Read only     | Có            | Hiển thị mã chi nhánh/PGD, không cho phép chỉnh sửa                                                                                                                                    |
| 5   | Họ tên người đại diện ký hợp đồng | Textbox (disabled) | Read only     | Có            | Hiển thị họ tên người đại diện, không cho phép chỉnh sửa                                                                                                                               |
| 6   | Chức vụ                           | Textbox (disabled) | Read only     | Có            | Hiển thị chức vụ người đại diện, không cho phép chỉnh sửa                                                                                                                              |
| 7   | Số quyết định ủy quyền            | Textbox            | Nhập          | Không         | Nhập số quyết định ủy quyền nếu có, trường này không bắt buộc                                                                                                                          |
| 8   | Địa chỉ CN/PGD                    | Textbox            | Nhập          | Có            | Nhập địa chỉ đơn vị tiếp nhận                                                                                                                                                          |
| 9   | Nút xác nhận                      | Button             | Click         | Có            | Nút "Xác nhận" ở cuối màn hình, xác nhận chuyển cấp phê duyệt                                                                                                                          |

---

## 3.2 Mô tả UI/UX Tab chi tiết 


| STT | Trường thông tin                      | Kiểu hiển thị | Kiểu thao tác   | Mô tả chi tiết                                                                          |
| --- | ------------------------------------- | ------------- | --------------- | --------------------------------------------------------------------------------------- |
|     | **Tab Chi tiết**                      |               |                 |                                                                                         |
| 1   | **Card thông tin người vay chính**    | Card          | Expand/Collapse | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin người vay chính    |
| 2   | Họ và tên                             | Text          | Read only       | Hiển thị họ và tên người vay chính                                                      |
| 2   | Loại giấy tờ                          | Text          | Read only       | Hiển thị tên loại giất tờ                                                               |
| 3   | Số giấy tờ                            | Text          | Read only       | Hiển thị số giấy tờ của người vay chính                                                 |
| 4   | Ngày cấp                              | Text          | Read only       | Hiển thị ngày cấp giấy tờ                                                               |
| 5   | Ngày hết hạn                          | Text          | Read only       | Hiển thị ngày hết hạn giấy tờ                                                           |
| 6   | Nơi cấp                               | Text          | Read only       | Hiển thị nơi cấp giấy tờ                                                                |
| 7   | Ngày sinh                             | Text          | Read only       | Hiển thị ngày sinh người vay chính                                                      |
| 8   | Giới tính                             | Text          | Read only       | Hiển thị giới tính người vay chính                                                      |
| 9   | Hộ khẩu thường trú                    | Text          | Read only       | Hiển thị địa chỉ hộ khẩu thường trú                                                     |
| 10  | Địa chỉ hiện tại                      | Text          | Read only       | Hiển thị địa chỉ hiện tại của người vay                                                 |
| 11  | Tình trạng hôn nhân                   | Text          | Read only       | Hiển thị tình trạng hôn nhân của người vay                                              |
| 12  | Số điện thoại                         | Text          | Read only       | Hiển thị số điện thoại của người vay chính                                              |
| 14  | **Card thông tin người đồng vay**     | Card          | Expand/Collapse | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường thông tin người đồng vay     |
| 15  | Họ và tên (đồng vay)                  | Text          | Read only       | Hiển thị họ và tên người đồng vay                                                       |
| 16  | Số giấy tờ (đồng vay)                 | Text          | Read only       | Hiển thị số giấy tờ của người đồng vay                                                  |
| 17  | Ngày cấp (đồng vay)                   | Text          | Read only       | Hiển thị ngày cấp giấy tờ của người đồng vay                                            |
| 18  | Ngày hết hạn (đồng vay)               | Text          | Read only       | Hiển thị ngày hết hạn giấy tờ của người đồng vay                                        |
| 19  | Nơi cấp (đồng vay)                    | Text          | Read only       | Hiển thị nơi cấp giấy tờ của người đồng vay                                             |
| 20  | Ngày sinh (đồng vay)                  | Text          | Read only       | Hiển thị ngày sinh người đồng vay                                                       |
| 21  | Giới tính (đồng vay)                  | Text          | Read only       | Hiển thị giới tính người đồng vay                                                       |
| 22  | Hộ khẩu thường trú (đồng vay)         | Text          | Read only       | Hiển thị địa chỉ hộ khẩu thường trú của người đồng vay                                  |
| 23  | Địa chỉ hiện tại (đồng vay)           | Text          | Read only       | Hiển thị địa chỉ hiện tại của người đồng vay                                            |
| 24  | Tình trạng hôn nhân (đồng vay)        | Text          | Read only       | Hiển thị tình trạng hôn nhân của người đồng vay                                         |
| 25  | Số điện thoại (đồng vay)              | Text          | Read only       | Hiển thị số điện thoại của người đồng vay                                               |
| 27  | **Card Đề nghị và phương án vay vốn** | Card          | Expand/Collapse | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường đề nghị và phương án vay vốn |
| 28  | Hình thức vay vốn                     | Text          | Read only       | Hiển thị hình thức vay vốn                                                              |
| 29  | Vốn tự có                             | Text          | Read only       | Hiển thị số tiền vốn tự có                                                              |
| 30  | Số tiền đề nghị vay                   | Text          | Read only       | Hiển thị số tiền đề nghị vay                                                            |
| 31  | Tổng nhu cầu                          | Text          | Read only       | Hiển thị tổng nhu cầu vay vốn                                                           |
| 32  | Thời hạn vay                          | Text          | Read only       | Hiển thị thời hạn vay                                                                   |
| 33  | Chi nhánh/PGD                         | Text          | Read only       | Hiển thị chi nhánh/PGD đăng ký vay                                                      |
| 34  | Phương thức vay                       | Text          | Read only       | Hiển thị phương thức vay                                                                |
| 35  | Mục đích sử dụng vốn                  | Text          | Read only       | Hiển thị mục đích sử dụng vốn                                                           |
| 36  | Mục đích khác                         | Text          | Read only       | Hiển thị mục đích khác nếu khách hàng chọn "Khác"                                       |
| 37  | Hình thức trả nợ                      | Text          | Read only       | Hiển thị hình thức trả nợ                                                               |
| 38  | Phương thức giải ngân                 | Text          | Read only       | Hiển thị phương thức giải ngân                                                          |
| 39  | Số tài khoản nhận tiền                | Text          | Read only       | Hiển thị số tài khoản nhận giải ngân                                                    |
| 41  | **Card Tài sản bảo đảm**              | Card          | Expand/Collapse | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường tài sản bảo đảm              |
| 42  | Loại tài sản                          | Text          | Read only       | Hiển thị loại tài sản bảo đảm                                                           |
| 43  | Giá trị tài sản                       | Text          | Read only       | Hiển thị giá trị tài sản bảo đảm                                                        |
| 44  | Giá trị tài sản (bằng chữ)            | Text          | Read only       | Hiển thị giá trị tài sản bảo đảm bằng chữ                                               |
| 45  | Hiện trạng tài sản                    | Text          | Read only       | Hiển thị hiện trạng tài sản bảo đảm                                                     |
| 46  | Chủ sở hữu tài sản                    | Text          | Read only       | Hiển thị chủ sở hữu tài sản bảo đảm                                                     |
| 47  | Năm sinh                              | Text          | Read only       | Hiển thị năm sinh chủ sở hữu tài sản                                                    |
| 48  | Biển kiểm soát                        | Text          | Read only       | Hiển thị biển kiểm soát tài sản (nếu là xe)                                             |
| 49  | Tên tài sản                           | Text          | Read only       | Hiển thị tên tài sản bảo đảm                                                            |
| 50  | Số khung                              | Text          | Read only       | Hiển thị số khung tài sản (nếu là xe)                                                   |
| 51  | Số máy                                | Text          | Read only       | Hiển thị số máy tài sản (nếu là xe)                                                     |
| 52  | Số giấy chứng nhận đăng ký xe         | Text          | Read only       | Hiển thị số giấy chứng nhận đăng ký xe (nếu là xe)                                      |
| 53  | Nơi cấp                               | Text          | Read only       | Hiển thị nơi cấp giấy chứng nhận đăng ký tài sản                                        |
| 54  | Ngày cấp                              | Text          | Read only       | Hiển thị ngày cấp giấy chứng nhận đăng ký tài sản                                       |
| 55  | Tổng trị giá tài sản bảo đảm          | Text          | Read only       | Hiển thị tổng trị giá tài sản bảo đảm                                                   |
| 57  | **Card Tình hình tài chính**          | Card          | Expand/Collapse | Nhóm thông tin có thể thu gọn/mở rộng, hiển thị các trường tình hình tài chính          |
| 58  | Nguồn thu                             | Text          | Read only       | Hiển thị nguồn thu của khách hàng                                                       |
| 59  | Doanh số bình quân/ngày               | Text          | Read only       | Hiển thị doanh số bình quân/ngày                                                        |
| 60  | Thu nhập bình quân/ngày               | Text          | Read only       | Hiển thị thu nhập bình quân/ngày                                                        |
| 61  | Địa điểm sản xuất - kinh doanh        | Text          | Read only       | Hiển thị địa điểm sản xuất - kinh doanh                                                 |

---
## 3.3 Mô tả UI/UX Tab tiến trình
**Tiến trình khoản vay gồm có:**
1- Tạo hồ sơ (Tạo hồ sơ hành công)
2- CBBH xử lý hồ sơ (Trạng thái gồm: Đang xử lý - Từ chối hồ sơ - Chuyển cấp phê duyệt thành công)
3- Phê duyệt tại ĐVKD (Trạng thái gồm: Chờ phê duyệt - Từ chối hồ sơ - Phê duyệt thành công - Chuyển cấp phê duyệt thành công)
4- Phê duyệt tại khu vực (Trạng thái gồm: Chờ phê duyệt - Từ chối hồ sơ - Phê duyệt thành công )
5- Xác nhận giải ngân (Đang xử lý - Xác nhận giải ngân thành công)

Mỗi bước thành công hiển thị **thông tin ngày giờ xử lý - Họ tên + User xử lý tại mỗi bước**

---

## 3.4 Mô tả UI/UX Tab hồ sơ
| STT | Trường thông tin                         | Kiểu hiển thị | Kiểu thao tác      | Mô tả chi tiết                                                                                                                                         |
| --- | ---------------------------------------- | ------------- | ------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------ |
|     | **Tab hồ sơ**                            |               |                    |                                                                                                                                                        |
|     | Tải lên tài liệu                         | button        | Click              | Cho phép bổ sung tài liệu, bằng cách chụp ảnh, chọn ảnh từ thư viện, chọn file. chọn 1 phương thức bổ sung, hiển thị popup nhập lý do bổ sung tài liệu |
| 1   | **Card tài liệu gốc**                    | Card          | Expand/Collapse    | Mỗi tài liệu hiển thị trạng thái hoàn thành hồ sơ (Đã nộp - chờ nộp)                                                                                   |
| 1   | GTTT người vay chính                     | image         | View               | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 2   | GTTT người đồng vay                      | image         | View               | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 3   | Giấy tờ tình trạng hôn nhân, mối quan hệ | File upload   | Chọn tệp để upload | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 4   | Giấy tờ chứng minh nơi cư trú            | File upload   | Chọn tệp để upload | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 5   | Chi tiết                                 | button        | Click              | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 6   | Tờ trình thẩm định xe mô tô, gắn máy     | File upload   | Chọn tệp để upload | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 7   | Giấy đăng ký xe                          | File upload   | Chọn tệp để upload | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 8   | Chứng nhận hộ kinh doanh, khác (nếu có)  | File upload   | Chọn tệp để upload | Click vào file để xem ảnh và tệp tải lên                                                                                                               |
| 9   | **Card tài liệu bổ sung**                | Card          | Expand/Collapse    | Các file ảnh và và tài liệu bổ sung được hiển thị ở đây                                                                                                |
| 10  | Tên tài liệu bổ sung                     |               |                    | Click vào file để xem ảnh và tệp tải lên. Mỗi tài liệu tải lên hiển thị thêm lý do bổ sung, họ tên user bổ sung, thời gian bổ sung                     |
| 11  | **Card Hồ sơ trình ký**                  | Card          | Expand/Collapse    | Các file tài liệu tự động gen ra sau khi phê duyệt được hiển thị ở đây                                                                                 |
| 12  | Tên tài liệu tự động gen                 |               |                    | Click vào file để xem tệp tải lên                                                                                                                      |

---

## 3.5 Mô tả UI/UX Tab CIC
| STT | Trường thông tin          | Kiểu hiển thị | Kiểu thao tác   | Mô tả chi tiết                                                                                                                                                    |
| --- | ------------------------- | ------------- | --------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | **Tab Người vay chính**   |               |                 |                                                                                                                                                                   |
| 2   | Kiểm tra CIC              | button        | Click           | Click button -> hiển thị popup danh sách bản tin thực hiện tra cứu, click 1 bản tin bất kỳ để thực hiện tra cứu có thể tra cứu 4 bản tin sau: R11A; R14;R21; S11A |
| 3   | Họ và tên                 | Text          | Read only       |                                                                                                                                                                   |
| 4   | CIF                       | Text          | Read only       |                                                                                                                                                                   |
| 5   | Component bản tin tra cứu | Card          | Expand/Collapse | Tên component là tên bản tin tra cứu, tra cứu nhiều bản tin thì hiển thị thành nhiều component bản tin tương ứng                                                  |
| 6   | Trạng thái                | Text          | Read only       |                                                                                                                                                                   |
| 7   | Ngày tra cứu              | date          | Read only       |                                                                                                                                                                   |
| 8   | Ngày hết hạn              | date          | Read only       |                                                                                                                                                                   |
| 9   | Người tra cứu             | Text          | Read only       | Hiển thị họ tên +user người thực hiện tra cứu                                                                                                                     |
| 10  | Button : Thực hiện lại    | Button        |                 | enable trên component của bản tin tra cứu bị lỗi                                                                                                                  |
| 11  | file kết quả tra cứu      | File PDF      |                 | File kết quả tra cứu có thể click vào xem, download file                                                                                                          |
| 11  | **Tab Người đồng vay**    |               |                 |                                                                                                                                                                   |
| 12  | Kiểm tra CIC              | button        | Click           | Click button -> hiển thị popup danh sách bản tin thực hiện tra cứu, click 1 bản tin bất kỳ để thực hiện tra cứu có thể tra cứu 4 bản tin sau: R11A; R14;R21; S11A |
| 13  | Họ và tên                 | Text          | Read only       |                                                                                                                                                                   |
| 14  | CIF                       | Text          | Read only       |                                                                                                                                                                   |
| 15  | Component bản tin tra cứu | Card          | Expand/Collapse | Tên component là tên bản tin tra cứu, tra cứu nhiều bản tin thì hiển thị thành nhiều component bản tin tương ứng                                                  |
| 16  | Trạng thái                | Text          | Read only       |                                                                                                                                                                   |
| 17  | Ngày tra cứu              | date          | Read only       |                                                                                                                                                                   |
| 18  | Ngày hết hạn              | date          | Read only       |                                                                                                                                                                   |
| 19  | Người tra cứu             | Text          | Read only       | Hiển thị họ tên người thực hiện tra cứu                                                                                                                           |
| 20  | Button : Thực hiện lại    | Button        |                 | enable trên component của bản tin tra cứu bị lỗi                                                                                                                  |
| 21  | file kết quả tra cứu      | File PDF      |                 | File kết quả tra cứu có thể click vào xem, download file                                                                                                          |

---
## 3.6 Mô tả UI/UX Tab Ghi chú
| STT | Trường thông tin  | Kiểu hiển thị | Kiểu thao tác | Mô tả chi tiết                                                                                                       |
| --- | ----------------- | ------------- | ------------- | -------------------------------------------------------------------------------------------------------------------- |
| 1   | Thêm ghi chú      | Button        | Click         | Click button hiển thị popup thêm mới ghi chú, tối đa 200 ký tự                                                       |
| 7   | thông tin ghi chú | Text          | Read only     | Mỗi ghi chú thành công hiển thị thông tin gồm: **Ngày tháng ghi chú**, **Họ tên user ghi chú**, **Nội dung ghi chú** |

---

## 3.7 Mô tả UI/UX Tab thông tin CTV
Màn hình hiển thị thông tin CTV gồm avatar, mã CTV, Họ tên cộng tác viên, thông tin mục thống kê, thông tin mục báo cáo nhanh.

### a. Thống kê

**Tab Hạn mức**

| Trường dữ liệu          | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                                            |
| ----------------------- | ---------- | ------------- | ---------------------------------------------------------------------------------------- |
| Số hạn mức được cấp     | Triệu đồng | Read-only     | Tổng hạn mức tín chấp được cấp cho CTV                                                   |
| Giá trị TSBĐ            | Triệu đồng | Read-only     | Tổng giá trị tài sản bảo đảm mà CTV đã cung cấp                                          |
| HMBL tín chấp còn lại   | Triệu đồng | Read-only     | Hiển thị dạng a/x: a là số hạn mức tín chấp còn lại, x là số hạn mức tín chấp được cấp   |
| % HMBL tín chấp còn lại | %          | Biểu đồ tròn  | Biểu đồ tròn thể hiện tỷ lệ hạn mức tín chấp còn lại trên tổng hạn mức tín chấp được cấp |
| HMBL TSBĐ còn lại       | Triệu đồng | Read-only     | Hiển thị dạng b/y: b là số hạn mức TSBĐ còn lại, y là số hạn mức TSBĐ được cấp           |
| % HMBL TSBĐ còn lại     | %          | Biểu đồ tròn  | Biểu đồ tròn thể hiện tỷ lệ hạn mức TSBĐ còn lại trên tổng hạn mức TSBĐ được cấp         |

> **Lưu ý:**  
> - Tất cả các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.
> - Các biểu đồ tròn giúp trực quan hóa tỷ lệ sử dụng hạn mức còn lại.
**Tab Dư nợ**

| Trường dữ liệu          | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                                         |
| ----------------------- | ---------- | ------------- | ------------------------------------------------------------------------------------- |
| Tổng dư nợ              | Triệu đồng | Read-only     | Tổng số tiền dư nợ của tất cả các khoản vay trả góp ngày do CTV quản lý/giới thiệu    |
| Dư nợ có TSBĐ           | Triệu đồng | Read-only     | Hiển thị dạng a/x: a là số dư nợ có TSBĐ, x là tổng dư nợ                             |
| Dư nợ không TSBĐ        | Triệu đồng | Read-only     | Hiển thị dạng b/x: b là số dư nợ không TSBĐ, x là tổng dư nợ                          |
| Biểu đồ tròn tổng dư nợ | %          | Biểu đồ tròn  | Biểu đồ tròn thể hiện tỷ lệ dư nợ có TSBĐ và dư nợ không TSBĐ trên tổng dư nợ của CTV |
> **Lưu ý:**  
> - Tất cả các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.  
> - Biểu đồ tròn giúp trực quan hóa tỷ lệ dư nợ có TSBĐ và không TSBĐ trên tổng dư nợ.

**Tab dư nợ**

| Trường dữ liệu   | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                   |
| ---------------- | ---------- | ------------- | --------------------------------------------------------------- |
| Tiền gốc/ngày    | Triệu đồng | Read-only     | Tổng số tiền gốc khách hàng phải nộp mỗi ngày cho các khoản vay |
| Tiền lãi/ngày    | Triệu đồng | Read-only     | Tổng số tiền lãi khách hàng phải nộp mỗi ngày cho các khoản vay |
| Số tiền nộp/ngày | Triệu đồng | Read-only     | Tổng số tiền khách hàng đã thực tế nộp vào hệ thống trong ngày  |
> **Lưu ý:**  
> - Tất cả các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.
> - Các trường này giúp CTV theo dõi sát sao nghĩa vụ thanh toán hàng ngày của khách hàng. nộp**


### b. Báo cáo nhanh

| Trường dữ liệu        | Đơn vị     | Kiểu hiển thị | Mô tả/Ý nghĩa                                                          |
| --------------------- | ---------- | ------------- | ---------------------------------------------------------------------- |
| Số tiền chậm nộp      | Triệu đồng | Read-only     | Tổng số tiền gốc và lãi khách hàng chưa nộp đúng hạn cho các khoản vay |
| Số ngày chậm nộp      | Ngày       | Read-only     | Tổng số ngày khách hàng bị chậm nộp tiền gốc                           |
| Bình quân dư nợ/tháng | Triệu đồng | Read-only     | Giá trị dư nợ bình quân của các khoản vay trong 1 tháng                |
| Tổng số khách hàng    | Khách hàng | Read-only     | Tổng số khách hàng do CTV quản lý/giới thiệu                           |

> **Lưu ý:**  
> - Các trường tài chính đều hiển thị ở đơn vị **triệu đồng**.
> - Báo cáo nhanh giúp CTV kiểm soát rủi ro và hiệu quả hoạt động.



# 4. Màn hình chỉnh sửa giao dịch

## 4.1 Mô tả UI/UX màn hình chỉnh sửa chi tiết giao dịch
Tại tab chi tiết hiển thị button "Cập nhật" góc cuối màn hình phía trên action chính của màn hình giúp người dùng dễ dàng thực hiện các thay đổi.
Các role được thao tác chỉnh sửa thông tin tại tab chi tiết gồm có: CBBH; PGĐ/GĐCN; GĐKV.

| STT | Trường thông tin                      | Kiểu hiển thị           | Kiểu thao tác            | Bắt buộc nhập | Mô tả chi tiết                                                                                                                                          |
| --- | ------------------------------------- | ----------------------- | ------------------------ | ------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1   | **Card Người vay chính**              | Card                    | Expand/Collapse          | Có            | Hiển thị các trường thông tin người vay chính                                                                                                           |
| 2   | Họ và tên                             | Textbox                 | Cho phép chỉnh sửa       | ✔             | Tối đa 100 ký tự tính cả khoảng trắng.                                                                                                                  |
| 2   | Loại giấy tờ                          | Droplist                | Cho phép chỉnh sửa       | ✔             | Chọn loại giấy tờ                                                                                                                                       |
| 3   | Số giấy tờ                            | Textbox                 | Không cho phép chỉnh sửa | ✔             |                                                                                                                                                         |
| 4   | Ngày cấp                              | Datepicker              | Cho phép chỉnh sửa       | ✔             | Định dạng dd/mm/yyyy                                                                                                                                    |
| 5   | Ngày hết hạn                          | Datepicker              | Cho phép chỉnh sửa       | ✔             | Định dạng dd/mm/yyyy                                                                                                                                    |
| 6   | Nơi cấp                               | Textbox                 | Cho phép chỉnh sửa       | ✔             | Cho phép nhập nơi cấp. Tối đa 50 ký tự                                                                                                                  |
| 6   | Ngày sinh                             | Textbox                 | Cho phép chỉnh sửa       | ✔             | Định dạng dd/mm/yyyy                                                                                                                                    |
| 7   | Giới tính                             | Dropdown                | Cho phép chỉnh sửa       | ✔             | Cho phép chọn Nam/Nữ/Khác . mặc định là nam                                                                                                             |
| 8   | Hộ khẩu thường trú                    | Textbox                 | Cho phép chỉnh sửa       | ✔             | Cho phép nhập địa chỉ thường trú, Tối đa 250 ký tự                                                                                                      |
| 8   | Địa chỉ hiện tại                      | Textbox                 | Cho phép chỉnh sửa       | ✔             | Cho phép nhập địa chỉ hiện tại, Tối đa 250 ký tự                                                                                                        |
| 9   | Tình trạng hôn nhân                   | Dropdown                | Chọn                     | ✔             | Độc thân, Đã kết hôn, Ly hôn, Khác. Mặc định chọn Độc thân                                                                                              |
| 10  | Số điện thoại                         | Textbox                 | Nhập                     | ✔             | Bắt đầu bằng số 0, 10 chữ số.                                                                                                                           |
| 1   | **Card Người đồng vay**               | Card                    | Expand/Collapse          | Có            | Hiển thị các trường thông tin người đồng vay                                                                                                            |
| 2   | Họ và tên                             | Textbox                 | Cho phép chỉnh sửa       | ✔             | Tối đa 100 ký tự tính cả khoảng trắng.                                                                                                                  |
| 2   | Loại giấy tờ                          | Droplist                | Cho phép chỉnh sửa       | ✔             | Chọn loại giấy tờ                                                                                                                                       |
| 3   | Số giấy tờ                            | Textbox                 | Cho phép chỉnh sửa       | ✔             | Cho phép chỉnh sửa, nhập số, tối đa 12 ký tự số                                                                                                         |
| 4   | Ngày cấp                              | Datepicker              | Cho phép chỉnh sửa       | ✔             | Định dạng dd/mm/yyyy                                                                                                                                    |
| 5   | Ngày hết hạn                          | Datepicker              | Cho phép chỉnh sửa       | ✔             | Định dạng dd/mm/yyyy                                                                                                                                    |
| 6   | Nơi cấp                               | Textbox                 | Cho phép chỉnh sửa       | ✔             | Cho phép nhập nơi cấp. Tối đa 50 ký tự                                                                                                                  |
| 6   | Ngày sinh                             | Textbox                 | Cho phép chỉnh sửa       | ✔             | Định dạng dd/mm/yyyy                                                                                                                                    |
| 7   | Giới tính                             | Dropdown                | Cho phép chỉnh sửa       | ✔             | Cho phép chọn Nam/Nữ/Khác . mặc định là nam                                                                                                             |
| 8   | Hộ khẩu thường trú                    | Textbox                 | Cho phép chỉnh sửa       | ✔             | Cho phép nhập địa chỉ thường trú, Tối đa 250 ký tự                                                                                                      |
| 9   | Tình trạng hôn nhân                   | Dropdown                | Chọn                     | ✔             | Độc thân, Đã kết hôn, Ly hôn, Khác. Mặc định chọn Độc thân                                                                                              |
| 10  | Số điện thoại                         | Textbox                 | Nhập                     | ✔             | Bắt đầu bằng số 0, 10 chữ số. Nếu là khách hàng cũ mặc định lấy ra số điện thoại KH                                                                     |
| 8   | Địa chỉ hiện tại                      | Textbox                 | Cho phép chỉnh sửa       | ✔             | Cho phép nhập địa chỉ hiện tại, Tối đa 250 ký tự                                                                                                        |
| 1   | **Card Đề nghị và phương án vay vốn** | Card                    | Expand/Collapse          | Có            | Hiển thị các trường thông tin đề nghị và phương án vay vốn                                                                                              |
| 1   | Hình thức vay vốn                     | Radio button            | Chọn 1 giá trị           | ✔             | Có TSĐB / Không TSĐB, mặc định có TSBĐ. Trường hợp tích chọn không TSBĐ => bỏ qua bước nhập tài sản bảo đảm                                             |
| 2   | Vốn tự có                             | Textbox                 | Nhập số                  | X             | Số tiền mà khách hàng hiện có để tham gia vào phương án vay. Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                     |
| 3   | Số tiền đề nghị vay                   | Textbox                 | Nhập số                  | ✔             | Số tiền khách hàng muốn vay. Số tiền VND. Tối thiểu Min >= 1.000.000 vnd, tối đa 1 tỉ, định dạng xxx,xxx                                                |
| 4   | Thời hạn vay                          | Dropdown                | Chọn                     | ✔             | Thời gian vay tính bằng ngày. Bao gồm (30,60,90,120,150,180,210,240,270). Mặc định 60 ngày                                                              |
| 5   | Tổng nhu cầu                          | Textbox                 | Tự động tính             | ✔             | Tổng nhu cầu vốn = Vốn tự có + Số tiền đề nghị vay. Số tiền VND, định dạng xxx,xxx                                                                      |
| 6   | Chi nhánh/PGD                         | Textbox (read-only)     | Không thao tác           | ✔             | Hệ thống tự điền theo mã chi nhánh của CTV ăn theo                                                                                                      |
| 7   | Phương thức vay                       | Textbox (read-only)     | Không thao tác           | ✔             | Mặc định là “Vay trả góp”                                                                                                                               |
| 8   | Mục đích sử dụng vốn                  | Dropdown                | Chọn từ danh sách        | ✔             | Bao gồm: Phục vụ nhu cầu đời sống/ Phục vụ hoạt động kinh doanh/ Khác                                                                                   |
| 9   | Mục đích khác                         | Textbox                 | Nhập chữ                 | ✔             | Nhập tên mục đích nếu Chọn mục đích sử dụng vốn là **khác** . Maxlength: 100 ký tự                                                                      |
| 10  | Hình thức trả nợ                      | Textbox (read-only)     | Không thao tác           | ✔             | Mặc định hiển thị “Trả góp nợ gốc và lãi tiền vay hàng ngày”                                                                                            |
| 11  | Phương thức giải ngân                 | Textbox (read-only)     | Không thao tác           | ✔             |                                                                                                                                                         |
| 12  | Số tài khoản nhận tiền                | Textbox (read-only)     | Không thao tác           | ✔             |                                                                                                                                                         |
| 1   | **Card Tình hình tài chính**          | Card                    | Expand/Collapse          | Có            | Hiển thị các trường thông tin tình hình tài chính                                                                                                       |
| 2   | Nguồn thu                             | Dropdown                | Chọn                     | ✔             | Bao gồm: Kinh doanh/Từ lương/Khác . Mặc định: Kinh doanh                                                                                                |
| 3   | Doanh số bình quân/ ngày              | Textbox                 | Nhập số nguyên           | ✔             | Đơn vị VNĐ,  chỉ hiển thị nếu Nguồn thu là "Kinh doanh". Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                         |
| 4   | Thu nhập bình quân/Ngày               | Textbox                 | Nhập số nguyên           | ✔             | Đơn vị VNĐ, hiển thị với mọi nguồn thu. Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                          |
| 5   | Địa điểm sản xuất - kinh doanh        | Lable                   |                          |               | chỉ hiển thị nếu Nguồn thu "Kinh doanh"                                                                                                                 |
| 6   | Tỉnh/ thành phố                       | Dropdown                | Chọn                     | ✔             | Dropdown cho phép tìm kiếm nhanh tên địa chỉ Tỉnh/thành phố                                                                                             |
| 8   | Phường/Xã                             | Dropdown                | Chọn                     | ✔             | Dropdown cho phép tìm kiếm tên địa chỉ phường xã                                                                                                        |
| 9   | Địa chỉ                               | Textarea                | nhập                     | ✔             | Cho phép nhập địa chỉ cụ thể, tối đa 250 ký.                                                                                                            |
| 1   | **Card Tài sản bảo đảm**              | Card                    | Expand/Collapse          | Có            | Hiển thị các trường thông tin tài sản bảo đảm                                                                                                           |
| 1   | Loại tài sản                          | Textbox(read-only)      | không thao tác           | Có            | Mặc định là Mô tô/xe máy                                                                                                                                |
| 2   | Giá trị tài sản                       | Textbox số              | Nhập số nguyên           | Có            | Nhập giá trị tài sản bằng VNĐ, Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx                                                                   |
| 3   | Giá trị tài sản (bằng chữ)            | Textbox(read-only)      | Tự động hiển thị         | Có            | Hiển thị tự động giá trị tài sản bằng chữ, không cho phép chỉnh sửa                                                                                     |
| 4   | Hiện trạng tài sản                    | Textbox                 | Nhập văn bản             | Có            | Mô tả tình trạng hiện tại của tài sản (ví dụ: Mới, Cũ, Đã qua sử dụng)                                                                                  |
| 5   | Chủ sở hữu tài sản                    | Textbox(Read-only)      | Không thao tác           | Có            | Hệ thống tự điền theo trường "họ và tên" người vay chính sản                                                                                            |
| 6   | Năm sinh                              | Date picker(read -only) | Không thao tác           | Có            | Hệ thống tự điền theo trường "ngày tháng năm sinh người vay chính, định dạng dd/mm/yyyy                                                                 |
| 1   | Biển kiểm soát                        | Textbox                 | Nhập văn bản             | Có            | Đọc từ QR. Nhập biển số đăng ký của phương tiện bảo đảm                                                                                                 |
| 2   | Tên tài sản                           | Textbox                 | Nhập văn bản             | Có            | Đọc từ QR. Nhập tên loại tài sản (ví dụ: Xe máy, Ô tô, Nhà đất, v.v.)                                                                                   |
| 3   | Số khung                              | Textbox                 | Nhập văn bản             | Có            | Đọc từ QR. Nhập số khung của phương tiện hoặc mã định danh tài sản                                                                                      |
| 4   | Số máy                                | Textbox                 | Nhập văn bản             | Có            | Đọc từ QR. Nhập số máy hoặc mã số động cơ của tài sản                                                                                                   |
| 5   | Số giấy chứng nhận đăng ký xe         | Textbox                 | Nhập văn bản             | Có            | Đọc từ QR. Nhập số giấy chứng nhận đăng ký xe                                                                                                           |
| 6   | Nơi cấp                               | Textbox                 | Nhập văn bản             | Có            | Đọc từ QR. Nhập nơi cấp giấy chứng nhận đăng ký xe                                                                                                      |
| 7   | Ngày cấp                              | Date Picker             | Chọn ngày                | Có            | Đọc từ QR. Chọn ngày cấp giấy chứng nhận đăng ký xe . Định dạng dd/mm/yyyy                                                                              |
| 9   | Tổng trị giá tài sản bảo đảm          | Textbox(Read-only)      | Không thao tác           | Có            | Hiển thị giá trị từ Cung cấp thông tin tài sản bảo đảm. Nhập tổng giá trị tài sản bảo đảm (VNĐ) ,Số tiền mặc định 0 VND, tối đa 1 tỉ, định dạng xxx,xxx |


# 5. Màn hình Phân bổ hồ sơ
Từ màn hình danh sách click checkbox 1 hoặc nhiều bản ghi => click chức năng phân bổ hồ sơ tại thanh thao tác nhanh => Hiển thị màn hình chọn cán bộ hỗ trợ

Danh sách giao dịch được chọn cần trong 1 chi nhánh, phân bổ cho cán bộ cùng chi nhánh => Nếu khác chi nhánh báo lỗi 

# 5.1 Mô tả UIUX màn hình phân bổ cán bộ hỗ trợ

Màn hình sẽ hiển thị danh sách các cán bộ hỗ trợ trong cùng chi nhánh với các thông tin sau:

| STT | Tên cán bộ hỗ trợ | Chức vụ              | Chi nhánh   | Hành động    |
| --- | ----------------- | -------------------- | ----------- | ------------ |
| 1   | Nguyễn Văn A      | Chuyên viên tín dụng | Chi nhánh 1 | Xác nhận/Hủy |


> Phân bổ cùng cấp (dành cho CBBH, GDV/HTTD): show các user có cùng chức danh
> Phân bổ khác cấp (Dành cho PGĐ/GĐ chi nhánh trở lên): show các user có chức danh được phép xử lý hồ sơ tại bước đó


---

# 6. Email và Noti Giao dịch
| STT | Email                    | Noti                     | Mô tả                                                |
| --- | ------------------------ | ------------------------ | ---------------------------------------------------- |
| 1   | Phân bổ hồ sơ thành công | Phân bổ hồ sơ thành công | Gửi thông báo đến cán bộ được gán phân bổ hỗ trợ     |
| 2   | Chuyển bước phê duyệt    | Chuyển bước phê duyệt    | Thông báo đến user nhận bước phê duyệt               |
| 3   | Phê duyệt thành công     | Phê duyệt thành công     | Thông báo đến CTV về hồ sơ được phê duyệt thành công |
| 4   | Từ chối hồ sơ            | Từ chối hồ sơ            | Thông báo đến CTV về hồ sơ bị từ chối phê duyệt      |


# 7. Thêm mới Giao dịch từ màn hình Chi tiết khách hàng
## 7.1 Mô tả UI UX màn hình thêm mới giao dịch (từ màn hình Chi tiết khách hàng)

Tương tự như Thêm mới giao dịch tại màn hình danh sách giao dịch, nhưng ở bước Chọn khách hàng sẽ lấy lên luôn thông tin của khách hàng đang xem chi tiết.

Các bước bao gồm:
Bước 1: Bước 1/4 Chọn sản phẩm
Bước 2: Bước 2/4 Chọn khách hàng
Bước 3: Bước 3/4 Chi tiết sản phẩm
Bước 4: Bước 4/4 Tài liệu yêu cầu
Bước 5: Bước 5/4 Xem lại và xác nhận

