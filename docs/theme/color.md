# Kiloba Business Color Palette

## <PERSON><PERSON><PERSON>ắ<PERSON> (Primary Colors)

### <PERSON><PERSON><PERSON> cam (Orange) - Primary Brand Color
- **CMYK**: 00 85 100 00
- **Hex**: `#FF2600`
- **Flutter**: `Color(0xFFFF2600)`
- **Đặc điểm**: <PERSON><PERSON><PERSON> động, hi<PERSON><PERSON> đại, trẻ trung nhiệt huyết

```dart
static const Color kienlongOrange = Color(0xFFFF2600);
```

### M<PERSON>u xanh da trời (Sky Blue) - Secondary Brand Color
- **CMYK**: 75 35 00 00
- **Hex**: `#40A6FF`
- **Flutter**: `Color(0xFF40A6FF)`
- **Đặc điểm**: C<PERSON>ng ngh<PERSON>, tin cậy thời thượng

```dart
static const Color kienlongSkyBlue = Color(0xFF40A6FF);
```

## Màu bổ trợ (Supporting Colors)

### M<PERSON>u xanh dương đậm (Dark Blue) - Background/Accent
- **CMYK**: 95 85 45 60
- **Hex**: `#050F38`
- **Flutter**: `Color(0xFF050F38)`
- **Đặc điểm**: Màu bổ trợ, làm nền để nổi bật logo

```dart
static const Color kienlongDarkBlue = Color(0xFF050F38);
```

## Flutter Color Class Implementation

```dart
class KilobaBusinessColors {
  // Primary Colors
  static const Color primary = Color(0xFFFF2600);        // Orange
  static const Color secondary = Color(0xFF40A6FF);      // Sky Blue
  
  // Supporting Colors
  static const Color accent = Color(0xFF050F38);         // Dark Blue
  
  // Color variations for different states
  static const Color primaryLight = Color(0xFFFF5722);   // Lighter orange
  static const Color primaryDark = Color(0xFFD32F2F);    // Darker orange
  
  static const Color secondaryLight = Color(0xFF81C7FF); // Lighter sky blue
  static const Color secondaryDark = Color(0xFF1976D2);  // Darker sky blue
}
```

## Sử dụng trong Flutter

```dart
// Example usage in widgets
Container(
  color: KilobaBusinessColors.primary,
  child: Text(
    'Kiloba Business',
    style: TextStyle(color: Colors.white),
  ),
)

// For Material Design
MaterialApp(
  theme: ThemeData(
    primarySwatch: MaterialColor(
      0xFFFF2600,
      <int, Color>{
        50: Color(0xFFFFEBEE),
        100: Color(0xFFFFCDD2),
        200: Color(0xFFEF9A9A),
        300: Color(0xFFE57373),
        400: Color(0xFFEF5350),
        500: Color(0xFFFF2600), // Primary
        600: Color(0xFFE53935),
        700: Color(0xFFD32F2F),
        800: Color(0xFFC62828),
        900: Color(0xFFB71C1C),
      },
    ),
    colorScheme: ColorScheme.fromSeed(
      seedColor: KilobaBusinessColors.primary,
    ),
  ),
)
```
