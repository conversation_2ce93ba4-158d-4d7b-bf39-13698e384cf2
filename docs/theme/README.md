# Kiloba Business Theme System

Hệ thống theme hoàn chỉnh cho ứng dụng Kiloba Business với thiết kế Material 3 và các component đặc thù ngân hàng.

## 📁 Cấu trúc Files

```
lib/core/theme/
├── app_colors.dart           # Màu sắc ứng dụng
├── app_typography.dart       # Typography & Text styles
├── app_dimensions.dart       # Kích thước & Spacing
├── component_themes.dart     # Theme cho Material components
├── banking_theme.dart        # Theme đặc thù ngân hàng
├── theme_extensions.dart     # Custom theme extensions
├── app_theme.dart           # Theme chính
├── theme_example.dart       # Ví dụ sử dụng
└── README.md               # Tài liệu này
```

## 🎨 Colors - Màu sắc

### Brand Colors (<PERSON>àu thương hiệu)
```dart
AppColors.kienlongOrange     // #FF2600 - Màu cam chính
AppColors.kienlongSkyBlue    // #40A6FF - Màu xanh da trời phụ
AppColors.kienlongDarkBlue   // #050F38 - <PERSON><PERSON>u xanh dương đậm
```

### Banking Colors (<PERSON><PERSON><PERSON> đặc thù ngân hàng)
```dart
AppColors.transactionIncome   // Màu thu nhập (xanh lá)
AppColors.transactionExpense  // Màu chi tiêu (đỏ)
AppColors.transactionPending  // Màu chờ xử lý (vàng)

AppColors.cardGoldVip        // Màu thẻ VIP vàng
AppColors.cardPlatinum       // Màu thẻ Platinum
AppColors.cardClassic        // Màu thẻ Classic
```

## ✍️ Typography - Kiểu chữ

### Material 3 Text Styles
```dart
Theme.of(context).textTheme.displayLarge    // Display text
Theme.of(context).textTheme.headlineLarge   // Headlines
Theme.of(context).textTheme.titleLarge      // Titles
Theme.of(context).textTheme.bodyLarge       // Body text
Theme.of(context).textTheme.labelLarge      // Labels
```

### Banking Specific Text Styles
```dart
AppTypography.accountBalanceLarge    // Số dư tài khoản lớn
AppTypography.accountNumber          // Số tài khoản
AppTypography.transactionAmount      // Số tiền giao dịch
AppTypography.cardNumber            // Số thẻ
```

## 📏 Dimensions - Kích thước

### Spacing
```dart
AppDimensions.spacingXS    // 4.0
AppDimensions.spacingS     // 8.0
AppDimensions.spacingM     // 16.0
AppDimensions.spacingL     // 24.0
AppDimensions.spacingXL    // 32.0
```

### Banking Specific
```dart
AppDimensions.accountCardHeight       // 200.0
AppDimensions.transactionItemHeight   // 72.0
AppDimensions.quickActionButtonSize   // 60.0
```

## 🏦 Banking Theme - Theme Ngân hàng

### Account Card Decorations
```dart
// Thẻ tài khoản thường
BankingTheme.accountCardDecoration

// Thẻ tài khoản VIP
BankingTheme.vipAccountCardDecoration

// Thẻ cho dark mode
BankingTheme.darkAccountCardDecoration
```

### Transaction Decorations
```dart
// Decoration theo loại giao dịch
BankingTheme.getTransactionDecoration(transactionType: 'income')
BankingTheme.getTransactionDecoration(transactionType: 'expense')
BankingTheme.getTransactionDecoration(transactionType: 'pending')
```

### Text Styles cho Banking
```dart
// Text style theo loại giao dịch
BankingTheme.getAmountTextStyle(transactionType: 'income')
BankingTheme.getAmountTextStyle(transactionType: 'expense')
```

## 🔧 Cách sử dụng

### 1. Áp dụng Theme trong MaterialApp

```dart
import 'package:flutter/material.dart';
import 'core/theme/app_theme.dart';

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Kiloba Business',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: MyHomePage(),
    );
  }
}
```

### 2. Sử dụng Colors

```dart
Container(
  color: AppColors.kienlongOrange,
  child: Text(
    'Kiloba Business',
    style: TextStyle(color: AppColors.textOnPrimary),
  ),
)
```

### 3. Sử dụng Typography

```dart
Text(
  'Tiêu đề chính',
  style: Theme.of(context).textTheme.headlineLarge,
)

Text(
  '1,234,567 VND',
  style: AppTypography.accountBalanceLarge,
)
```

### 4. Tạo Account Card

```dart
Container(
  height: AppDimensions.accountCardHeight,
  decoration: BankingTheme.accountCardDecoration,
  padding: BankingTheme.accountCardPadding,
  child: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Tài khoản tiết kiệm',
        style: AppTypography.textTheme.titleMedium?.copyWith(
          color: AppColors.textOnPrimary,
        ),
      ),
      Spacer(),
      Text(
        '12,345,678 VND',
        style: AppTypography.accountBalanceLarge,
      ),
      Text(
        '1234 5678 9012 3456',
        style: AppTypography.accountNumber,
      ),
    ],
  ),
)
```

### 5. Tạo Transaction Item

```dart
Container(
  padding: BankingTheme.transactionItemPadding,
  decoration: BankingTheme.getTransactionDecoration(
    transactionType: 'income',
  ),
  child: Row(
    children: [
      Icon(
        Icons.arrow_downward,
        color: BankingTheme.getTransactionStatusColor('income'),
      ),
      SizedBox(width: AppDimensions.spacingM),
      Expanded(
        child: Text(
          'Chuyển tiền',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ),
      Text(
        '+1,000,000 VND',
        style: BankingTheme.getAmountTextStyle(
          transactionType: 'income',
        ),
      ),
    ],
  ),
)
```

### 6. Responsive Design

```dart
// Sử dụng extension methods
Widget build(BuildContext context) {
  return Padding(
    padding: context.responsivePadding,
    child: Container(
      height: context.responsiveCardHeight,
      child: YourWidget(),
    ),
  );
}

// Kiểm tra breakpoints
if (context.isMobile) {
  // Mobile layout
} else if (context.isTablet) {
  // Tablet layout
} else {
  // Desktop layout
}
```

### 7. Theme Extensions

```dart
// Sử dụng Banking Theme Extension
final bankingTheme = context.bankingTheme;
Container(
  color: bankingTheme.transactionIncome,
  child: Text('Income'),
)

// Animation Theme Extension
final animationTheme = context.animationTheme;
AnimatedContainer(
  duration: animationTheme.normalDuration,
  curve: animationTheme.normalCurve,
  // ...
)
```

## 🌙 Dark Mode Support

Theme system tự động hỗ trợ dark mode:

```dart
// Theme sẽ tự động chuyển đổi dựa vào system setting
final isDarkMode = Theme.of(context).brightness == Brightness.dark;

// Hoặc sử dụng helper method
AppTheme.setSystemUIOverlay(Theme.of(context).brightness);
```

## 🖼️ Border & Divider Guidelines

### **QUAN TRỌNG: Quy tắc màu viền cho Dark Theme**

Khi thiết kế borders và dividers, **LUÔN** sử dụng alpha opacity cho dark theme để tránh quá sáng:

#### **1. Container Borders**
```dart
// ✅ ĐÚNG - Sử dụng alpha opacity cho dark theme
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: isDarkMode 
          ? AppColors.borderDark.withValues(alpha: 0.3)  // 30% opacity
          : AppColors.borderLight,
      width: 1,
    ),
  ),
)

// ❌ SAI - Không sử dụng alpha, sẽ quá sáng trong dark theme
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: isDarkMode ? AppColors.borderDark : AppColors.borderLight,
      width: 1,
    ),
  ),
)
```

#### **2. Dividers**
```dart
// ✅ ĐÚNG - Consistent với container borders
Divider(
  height: 1,
  color: isDarkMode 
      ? AppColors.borderDark.withValues(alpha: 0.3)
      : AppColors.borderLight,
)

// ❌ SAI - Không nhất quán
Divider(
  height: 1,
  color: isDarkMode ? AppColors.borderDark : AppColors.borderLight,
)
```

#### **3. Info Panels & Special Cards**
```dart
// ✅ ĐÚNG - Sử dụng alpha thấp hơn cho info panels
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: AppColors.info.withValues(alpha: 0.2),  // 20% opacity
      width: 1,
    ),
  ),
)

// ❌ SAI - Alpha quá cao, sẽ nổi bật quá mức
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: AppColors.info.withValues(alpha: 0.5),
      width: 1,
    ),
  ),
)
```

### **Alpha Values Reference**

| Component Type | Dark Theme Alpha | Light Theme |
|----------------|------------------|-------------|
| **Container Borders** | `0.3` (30%) | `AppColors.borderLight` |
| **Dividers** | `0.3` (30%) | `AppColors.borderLight` |
| **Info Panels** | `0.2` (20%) | `AppColors.borderLight` |
| **Transaction Cards** | `0.3` (30%) | `AppColors.borderLight` |
| **Account Cards** | `0.3` (30%) | `AppColors.borderLight` |

### **Consistency Rules**

1. **ALWAYS** use alpha opacity for dark theme borders
2. **NEVER** use full opacity `AppColors.borderDark` directly
3. **KEEP** light theme using `AppColors.borderLight` unchanged
4. **MATCH** divider colors with container border colors
5. **USE** lower alpha (0.2) for info/special panels
6. **MAINTAIN** visual hierarchy - borders should not compete with content

### **Examples from Log Settings**

```dart
// Main settings container
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: isDarkMode 
          ? AppColors.borderDark.withValues(alpha: 0.3)
          : AppColors.borderLight,
      width: 1,
    ),
  ),
)

// System info widget
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: isDarkMode 
          ? AppColors.borderDark.withValues(alpha: 0.3)
          : AppColors.borderLight,
      width: 1,
    ),
  ),
)

// Info panels (actions, selectors)
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: AppColors.info.withValues(alpha: 0.2),
      width: 1,
    ),
  ),
)

// Dividers between sections
Divider(
  height: 1,
  color: isDarkMode 
      ? AppColors.borderDark.withValues(alpha: 0.3)
      : AppColors.borderLight,
)
```

### **Log Viewer Consistency**

**QUAN TRỌNG:** Các card trong Log Settings phải **NHẤT QUÁN** với Log Viewer:

#### **Log Viewer Border Pattern**
```dart
// Log Item Widget sử dụng BankingTheme
Container(
  decoration: BankingTheme.getTransactionDecoration(
    transactionType: _getTransactionTypeFromLevel(log.level),
  ).copyWith(
    color: isDarkMode
        ? AppColors.backgroundDarkSecondary
        : AppColors.backgroundSecondary,
  ),
)
```

#### **BankingTheme Transaction Decorations**
```dart
// Từ banking_theme.dart
static BoxDecoration get transactionItemDecoration => BoxDecoration(
  color: AppColors.backgroundPrimary,
  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
  border: Border.all(
    color: AppColors.borderLight,  // Light theme
    width: AppDimensions.borderWidthThin,
  ),
  // ...
);
```

#### **Consistency Checklist**
- ✅ **Log Settings borders** = **Log Viewer borders**
- ✅ **Dark theme alpha** = `0.3` cho tất cả containers
- ✅ **Info panels alpha** = `0.2` cho special cards
- ✅ **Dividers** = cùng màu với container borders
- ✅ **Visual hierarchy** = borders không cạnh tranh với content

## 🎯 Best Practices

### 1. Sử dụng Theme thay vì hardcode colors
```dart
// ❌ Không nên
Container(color: Color(0xFFFF2600))

// ✅ Nên
Container(color: Theme.of(context).colorScheme.primary)
// hoặc
Container(color: AppColors.kienlongOrange)
```

### 2. Sử dụng responsive dimensions
```dart
// ❌ Không nên
Padding(padding: EdgeInsets.all(16))

// ✅ Nên
Padding(padding: EdgeInsets.all(AppDimensions.paddingM))
// hoặc
Padding(padding: context.responsivePadding)
```

### 3. Sử dụng banking-specific helpers
```dart
// ❌ Không nên tự implement logic màu sắc
Color getTransactionColor(String type) {
  if (type == 'income') return Colors.green;
  return Colors.red;
}

// ✅ Nên sử dụng helper có sẵn
Color color = BankingTheme.getTransactionStatusColor('income');
```

### 4. Consistent spacing
```dart
// ✅ Sử dụng spacing scale nhất quán
Column(
  children: [
    Widget1(),
    SizedBox(height: AppDimensions.spacingM),
    Widget2(),
    SizedBox(height: AppDimensions.spacingL),
    Widget3(),
  ],
)
```

## 📱 Material 3 Compliance

Theme system này tuân thủ các nguyên tắc Material 3:
- Dynamic color schemes
- Improved accessibility
- Enhanced visual hierarchy
- Modern component designs

## 🔧 Customization

### Thay đổi màu brand
Chỉnh sửa trong `app_colors.dart`:
```dart
static const Color kienlongOrange = Color(0xFFYOUR_COLOR);
```

### Thêm custom component theme
1. Thêm theme vào `component_themes.dart`
2. Apply vào `app_theme.dart`
3. Export qua main theme

### Tạo custom banking decoration
Thêm vào `banking_theme.dart`:
```dart
static BoxDecoration get yourCustomDecoration => BoxDecoration(
  // your decoration
);
```

## ⚡ Performance

- Sử dụng `const` constructors khi có thể
- Theme extensions được cache tự động
- Responsive breakpoints tính toán một lần

## 🧪 Testing

Xem file `theme_example.dart` để có ví dụ đầy đủ về cách sử dụng theme system.

## 🤖 AI Development Guidelines

### **QUICK REFERENCE cho AI**

Khi làm việc với borders và dividers, **LUÔN** nhớ:

#### **🚨 CRITICAL RULES**
1. **Dark Theme Borders**: `AppColors.borderDark.withValues(alpha: 0.3)`
2. **Light Theme Borders**: `AppColors.borderLight` (không đổi)
3. **Info Panels**: `AppColors.info.withValues(alpha: 0.2)`
4. **Dividers**: Cùng màu với container borders
5. **NEVER**: Sử dụng `AppColors.borderDark` trực tiếp

#### **🔧 CODE TEMPLATE**
```dart
// Container Border Template
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: isDarkMode 
          ? AppColors.borderDark.withValues(alpha: 0.3)
          : AppColors.borderLight,
      width: 1,
    ),
  ),
)

// Divider Template
Divider(
  height: 1,
  color: isDarkMode 
      ? AppColors.borderDark.withValues(alpha: 0.3)
      : AppColors.borderLight,
)

// Info Panel Template
Container(
  decoration: BoxDecoration(
    border: Border.all(
      color: AppColors.info.withValues(alpha: 0.2),
      width: 1,
    ),
  ),
)
```

#### **🎯 CONSISTENCY CHECK**
- ✅ **Log Settings** = **Log Viewer** border style
- ✅ **Dark theme** = 30% opacity cho containers
- ✅ **Info panels** = 20% opacity cho special cards
- ✅ **All dividers** = cùng alpha với containers
- ✅ **Visual comfort** = không quá sáng trong dark mode

#### **📋 COMMON MISTAKES TO AVOID**
- ❌ `color: isDarkMode ? AppColors.borderDark : AppColors.borderLight`
- ❌ `color: AppColors.info.withValues(alpha: 0.5)`
- ❌ Không nhất quán giữa dividers và borders
- ❌ Quên alpha opacity cho dark theme

## 📚 References

- [Material 3 Design System](https://m3.material.io/)
- [Flutter Theme Documentation](https://docs.flutter.dev/cookbook/design/themes)
- [Color Guidelines from docs/overview/color.md](../../docs/overview/color.md) 