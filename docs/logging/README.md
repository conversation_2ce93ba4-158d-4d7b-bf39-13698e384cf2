# Hệ thống Logging - <PERSON><PERSON><PERSON><PERSON> kế tổng quan

## 1. Ý tưởng

Xây dựng hệ thống logging toàn diện cho ứng dụng Kiloba Business, cho phép:
- Ghi log vào database riêng biệt (không ảnh hưởng database nghiệp vụ)
- <PERSON><PERSON>, tì<PERSON> kiếm, lọc log theo nhiều tiêu chí
- Chia sẻ log ra file hoặc qua các ứng dụng khác
- Dọn dẹp log tự động theo cấu hình
- Người dùng có thể tắt/bật logging theo ý muốn

## 2. Mục tiêu

### 2.1 Mục tiêu chính
- **Performance**: Không block UI khi ghi log
- **Reliability**: Log không bị mất, có retry mechanism
- **User Control**: Người dùng có thể tùy chỉnh logging behavior
- **Privacy**: <PERSON><PERSON><PERSON> v<PERSON> thông tin nhạy cảm trong log
- **Maintainability**: <PERSON><PERSON> dàng quản lý và bảo trì

### 2.2 Mục tiêu kỹ thuật
- Sử dụng SQLite database riêng cho logging
- Isolate-based processing để tránh block main thread
- Queue-based system với batch processing
- Graceful degradation khi có lỗi
- Memory-efficient với pagination và cleanup

## 3. Giải pháp

### 3.1 Kiến trúc tổng thể

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AppLogger     │    │ LogWriterIsolate│    │ LogDatabase     │
│   (Main Thread) │───▶│   (Background)  │───▶│   (SQLite)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Log Settings   │    │   Log Queue     │    │  Log Cleanup    │
│  (User Config)  │    │  (Batch Proc)   │    │  (Auto/Manual)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 Các thành phần chính

#### A. Database Layer
- **LogDatabaseHelper**: Quản lý SQLite database riêng cho logs
- **IsolateLogRepository**: CRUD operations trong isolate
- **Log Models**: LogEntry, LogFilter, LogStats

#### B. Processing Layer
- **LogWriterIsolate**: Background processing cho log writing
- **LogQueue**: Queue management với batch processing
- **AsyncLogWriter**: Interface giữa main thread và isolate

#### C. Service Layer
- **LogManagementService**: Business logic cho log operations
- **LogSettingsService**: Quản lý user preferences
- **LogExportService**: Export và share functionality

#### D. UI Layer
- **LogManagementSection**: Widget trong Account tab
- **LogViewerScreen**: Màn hình xem và quản lý log
- **LogFilterWidget**: Tìm kiếm và lọc log

### 3.3 Workflow

#### A. Log Writing Flow
1. AppLogger nhận log request
2. Kiểm tra user settings (enabled/disabled)
3. Gửi log entry đến LogWriterIsolate
4. Isolate xử lý và ghi vào database
5. Fallback to console nếu có lỗi

#### B. Log Reading Flow
1. User mở Log Viewer
2. Load logs từ database với pagination
3. Apply filters và search
4. Display với virtual scrolling
5. Cache results cho performance

#### C. Log Management Flow
1. User cấu hình settings
2. Apply retention policy
3. Auto-cleanup theo schedule
4. Manual cleanup khi cần

### 3.4 Database Schema

```sql
CREATE TABLE app_logs (
  id TEXT PRIMARY KEY,
  level TEXT NOT NULL,
  message TEXT NOT NULL,
  tag TEXT NULL,
  feature TEXT NULL,
  timestamp INTEGER NOT NULL,
  error_data TEXT NULL,
  stack_trace TEXT NULL,
  metadata TEXT NULL,
  user_id TEXT NULL,
  session_id TEXT NULL,
  device_info TEXT NULL
);

-- Indexes cho performance
CREATE INDEX idx_logs_timestamp ON app_logs(timestamp DESC);
CREATE INDEX idx_logs_level ON app_logs(level, timestamp DESC);
CREATE INDEX idx_logs_feature ON app_logs(feature, timestamp DESC);
```

### 3.5 Performance Optimizations

#### A. Database Optimizations
- WAL mode cho concurrent access
- Large cache size (10,000 pages)
- Memory-mapped I/O
- Optimized indexes

#### B. Processing Optimizations
- Batch processing (50 logs/batch)
- Queue-based buffering
- Background processing
- Lazy loading cho UI

#### C. Memory Optimizations
- Pagination (100 logs/page)
- Virtual scrolling
- Automatic cleanup
- Configurable retention

## 4. Cấu hình và Settings

### 4.1 User Settings
- **Enable/Disable**: Tắt/bật logging hoàn toàn
- **Database Logging**: Lưu vào database hay không
- **Console Logging**: Hiển thị trong console
- **Minimum Level**: Mức độ log tối thiểu
- **Retention Period**: Thời gian lưu trữ (7-90 ngày)
- **Auto Cleanup**: Tự động dọn dẹp

### 4.2 System Settings
- **Max Log Entries**: Giới hạn số lượng log (10,000)
- **Batch Size**: Kích thước batch processing (50)
- **Flush Interval**: Thời gian flush queue (2 giây)
- **Cleanup Interval**: Tần suất cleanup (1 ngày)

## 5. Security và Privacy

### 5.1 Data Protection
- Mask sensitive data (passwords, tokens)
- Encrypt log storage (optional)
- Secure export với password
- Audit trail cho access

### 5.2 Compliance
- GDPR compliance cho retention
- Data anonymization cho sharing
- User consent cho collection
- Right to be forgotten

## 6. Monitoring và Analytics

### 6.1 Performance Metrics
- Log writing performance
- Database query performance
- Memory usage
- Error rates

### 6.2 Usage Analytics
- Log management usage
- Export frequency
- Cleanup effectiveness
- User preferences

## 7. Testing Strategy

### 7.1 Unit Tests
- Log storage operations
- Filter logic
- Export functionality
- Settings management

### 7.2 Integration Tests
- Isolate communication
- Database operations
- UI interactions
- End-to-end workflows

### 7.3 Performance Tests
- High-volume logging
- Memory usage
- Database performance
- UI responsiveness 