# Implementation API Kiểm tra Đăng ký

## Tổng quan

Đã thêm thành công API kiểm tra đăng ký vào `RegistrationService` để kiểm tra xem một số CMND/CCCD đã được đăng ký trong hệ thống hay chưa.

## Files đã được tạo/sửa đổi

### 1. Model mới
- **File**: `lib/features/auth/models/check_registration_response_model.dart`
- **Mô tả**: Model để xử lý response từ API `check_register_by_id_card`

### 2. Service đã được cập nhật
- **File**: `lib/features/auth/services/registration_service.dart`
- **Thêm**:
  - Endpoint: `_checkRegisterEndpoint = '/rest/rpc/check_register_by_id_card'`
  - Method: `checkRegistrationByIdCard(String idCardNo)`
  - Helper methods:
    - `isRegistrationExists()`
    - `getRegistrationStatusDescription()`
    - `canUpdateRegistration()`
    - `canDeleteRegistration()`

### 3. Export đã được cập nhật
- **File**: `lib/features/auth/index.dart`
- **Thêm**: Export cho `CheckRegistrationResponseModel`

### 4. Test
- **File**: `test/features/auth/services/registration_service_test.dart`
- **Mô tả**: Test cases cho model và helper methods

### 5. Documentation
- **File**: `docs/api/CHECK_REGISTRATION_API.md`
- **Mô tả**: Hướng dẫn sử dụng API chi tiết

## API Endpoint

```
POST /rest/rpc/check_register_by_id_card
```

### Request Parameters
```json
{
  "p_id_card_no": "123456789012"
}
```

### Response Examples

#### Bản ghi tồn tại
```json
{
  "success": true,
  "message": "Registration record found",
  "data": {
    "id": "uuid-registration-id",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### Bản ghi không tồn tại
```json
{
  "success": false,
  "message": "Registration record not found with this ID card number",
  "data": null
}
```

## Cách sử dụng

### Import
```dart
import 'package:kiloba_biz/features/auth/services/registration_service.dart';
import 'package:kiloba_biz/features/auth/models/check_registration_response_model.dart';
```

### Kiểm tra đăng ký
```dart
final registrationService = RegistrationService();

try {
  final response = await registrationService.checkRegistrationByIdCard('123456789012');
  
  if (response.exists) {
    print('Đã tìm thấy đăng ký: ${response.id}');
    print('Trạng thái: ${response.status}');
    print('Ngày tạo: ${response.createdAt}');
  } else {
    print('Chưa có đăng ký cho số CMND/CCCD này');
  }
} catch (e) {
  print('Lỗi: $e');
}
```

### Helper Methods
```dart
// Kiểm tra tồn tại
bool exists = registrationService.isRegistrationExists(response);

// Mô tả trạng thái tiếng Việt
String statusDesc = registrationService.getRegistrationStatusDescription(response);

// Kiểm tra có thể cập nhật
bool canUpdate = registrationService.canUpdateRegistration(response);

// Kiểm tra có thể xóa
bool canDelete = registrationService.canDeleteRegistration(response);
```

## Validation

- Số CMND/CCCD phải có đúng 12 chữ số
- Số CMND/CCCD không được để trống
- Validation được thực hiện trước khi gọi API

## Error Handling

API trả về các loại lỗi:
- `validationError`: Dữ liệu đầu vào không hợp lệ
- `apiError`: Lỗi từ API server
- `invalidResponse`: Response không đúng định dạng
- `unknown`: Lỗi không xác định

## Trạng thái Registration

| Trạng thái | Mô tả | Có thể cập nhật | Có thể xóa |
|------------|-------|-----------------|------------|
| PENDING | Chờ xử lý | Có | Có |
| APPROVED | Đã duyệt | Không | Không |
| REJECTED | Đã từ chối | Có | Không |
| ACTIVE | Đang hoạt động | Không | Không |
| INACTIVE | Không hoạt động | Không | Không |

## Testing

Đã tạo test cases cho:
- Model `CheckRegistrationResponseModel`
- Helper methods
- ID card validation

Chạy test:
```bash
flutter test test/features/auth/services/registration_service_test.dart
```

## Backend Integration

API này tích hợp với PostgreSQL function:
```sql
mobile_api.check_register_by_id_card(p_id_card_no VARCHAR(20))
```

Function này:
- Tìm bản ghi trong bảng `registers` theo `id_card_no`
- Trả về JSON theo chuẩn REST API
- Chỉ tìm bản ghi chưa bị xóa (`is_deleted = false`)

## Kết luận

API kiểm tra đăng ký đã được implement thành công với:
- ✅ Model xử lý response
- ✅ Service method với validation
- ✅ Helper methods tiện ích
- ✅ Error handling đầy đủ
- ✅ Test cases
- ✅ Documentation chi tiết
- ✅ Integration với backend PostgreSQL function 