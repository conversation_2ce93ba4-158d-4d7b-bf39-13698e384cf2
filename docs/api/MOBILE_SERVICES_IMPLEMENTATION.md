# Mobile Services Implementation

## Tổng quan
Tài liệu này mô tả các services đã được implement dựa trên tài liệu Mobile API Integration để tích hợp với backend.

## Cấu trúc Services

### 1. Shared Services (dùng chung cho tất cả features)

#### **MasterDataService** - `lib/shared/services/master_data_service.dart`
**Chức năng**: Quản lý master data (provinces, positions, regions, branches)

**API Endpoints**:
- `/rest/rpc/get_provinces` - Lấy danh sách tỉnh/thành
- `/rest/rpc/get_positions` - Lấy danh sách chức vụ  
- `/rest/rpc/get_regions` - Lấy danh sách khu vực
- `/rest/rpc/get_branches` - Lấy danh sách chi nhánh

**Tính năng**:
- Cache data locally để tối ưu performance
- Search và filter master data
- Load branches theo province/region
- Refresh và clear cache
- Error handling với custom exceptions

**Sử dụng**:
```dart
final masterDataService = MasterDataService();

// Lấy danh sách tỉnh/thành
final provinces = await masterDataService.getProvinces();

// Lấy chi nhánh theo tỉnh
final branches = await masterDataService.getBranchesByProvince(provinceId);

// Lấy cache data
final cachedProvinces = masterDataService.getCachedProvinces();
```

#### **DocumentService** - `lib/shared/services/document_service.dart`
**Chức năng**: Quản lý document upload và metadata

**API Endpoints**:
- `/rest/rpc/insert_document` - Insert document record

**Tính năng**:
- Insert document với metadata đầy đủ
- Helper methods cho các loại document cụ thể:
  - `insertIdCardFrontDocument()` - CMND/CCCD mặt trước
  - `insertIdCardBackDocument()` - CMND/CCCD mặt sau
  - `insertPortraitDocument()` - Ảnh chân dung
  - `insertContractDocument()` - Hợp đồng
- File validation (size, mime type)
- Generate stored filename
- Error handling với custom exceptions

**Sử dụng**:
```dart
final documentService = DocumentService();

// Insert CMND mặt trước
final document = await documentService.insertIdCardFrontDocument(
  originalFilename: 'CMND_front.jpg',
  storedFilename: 'cmnd_nguyen_van_a_20241201_123456.jpg',
  filePath: '/documents/customers/customer_123/cmnd_nguyen_van_a_20241201_123456.jpg',
  fileSize: 2048576,
  mimeType: 'image/jpeg',
  metadata: {'customer_id': '123', 'upload_source': 'mobile_app'},
);
```

### 2. Auth Feature Services

#### **RegistrationService** - `lib/features/auth/services/registration_service.dart`
**Chức năng**: Xử lý đăng ký user mới

**API Endpoints**:
- `/rest/rpc/insert_register` - Insert registration record

**Tính năng**:
- Đăng ký collaborator và customer
- Validation data đầy đủ:
  - Email format
  - Vietnamese phone number format
  - ID card number format (12 digits)
  - Required fields validation
- Helper methods:
  - `registerCollaborator()` - Đăng ký cộng tác viên
  - `registerCustomer()` - Đăng ký khách hàng
- Format utilities cho phone number và ID card
- Error handling với custom exceptions

**Sử dụng**:
```dart
final registrationService = RegistrationService();

// Đăng ký collaborator
final response = await registrationService.registerCollaborator(
  fullName: 'Nguyễn Văn A',
  idCardType: 'CHIP_ID',
  idCardNo: '123456789012',
  issueDate: '2020-01-01',
  issuePlace: 'Hà Nội',
  permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
  phoneNumber: '0123456789',
  email: '<EMAIL>',
  provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
  branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
  expiryDate: '2025-01-01',
  positionId: 'fbe05dc8-cf05-4743-a71c-ffa71c9e68a0',
  frontCardDocumentId: '46221486-164e-42c8-8dfa-d7139aeb571c',
);
```

## Models

### Shared Models
- **DocumentModel** - `lib/shared/models/document_model.dart`
- **ProvinceModel** - `lib/shared/models/province_model.dart`
- **PositionModel** - `lib/shared/models/position_model.dart`
- **RegionModel** - `lib/shared/models/region_model.dart`
- **BranchModel** - `lib/shared/models/branch_model.dart`

### Auth Models
- **RegistrationModel** - `lib/features/auth/models/registration_model.dart`
- **RegistrationResponseModel** - `lib/features/auth/models/registration_response_model.dart`

## Error Handling

### Custom Exceptions
- **MasterDataException** - Cho master data errors
- **DocumentException** - Cho document errors
- **RegistrationException** - Cho registration errors

### Error Types
- `apiError` - API call failed
- `invalidResponse` - Invalid response format
- `validationError` - Data validation failed
- `notFound` - Resource not found
- `unauthorized` - Authentication required
- `unknown` - Unknown error

## Integration Flow

### 1. Document Upload Flow
```
1. Upload file → MinIO (via Spring Boot API)
2. Call DocumentService.insertDocument() → Get document_id
3. Use document_id cho registration
```

### 2. Registration Flow
```
1. Load provinces → User selects province
2. Load branches by province → User selects branch
3. Load positions → User selects position
4. Upload CMND front/back → Get document_ids
5. Call RegistrationService.registerUser() → Complete registration
```

### 3. Master Data Loading
```
1. Load provinces on app start (cache)
2. Load positions on app start (cache)
3. Load regions on app start (cache)
4. Load branches when province selected
```

## Caching Strategy

### Master Data Cache
- **Provinces**: Cache toàn bộ danh sách
- **Positions**: Cache toàn bộ danh sách
- **Regions**: Cache toàn bộ danh sách
- **Branches**: Cache theo provinceId

### Cache Management
- Auto-cache khi load data
- Manual clear cache
- Refresh all master data
- Get cached data methods

## Security Considerations

### Authentication
- JWT token được handle tự động qua ApiService interceptors
- Tất cả API calls đều có authentication

### Data Validation
- Client-side validation trước khi gửi API
- File validation (size, mime type)
- Format validation (email, phone, ID card)

### Error Handling
- Graceful error handling
- User-friendly error messages
- Logging cho debugging

## Usage Examples

### Complete Registration Flow
```dart
// 1. Load master data
final masterDataService = MasterDataService();
final provinces = await masterDataService.getProvinces();
final positions = await masterDataService.getPositions();

// 2. Load branches when user selects province
final branches = await masterDataService.getBranchesByProvince(selectedProvinceId);

// 3. Upload documents
final documentService = DocumentService();
final frontDoc = await documentService.insertIdCardFrontDocument(
  originalFilename: 'CMND_front.jpg',
  storedFilename: 'cmnd_front_${DateTime.now().millisecondsSinceEpoch}.jpg',
  filePath: '/documents/customers/customer_123/cmnd_front.jpg',
  fileSize: 2048576,
  mimeType: 'image/jpeg',
);

// 4. Register user
final registrationService = RegistrationService();
final registration = RegistrationModel(
  fullName: 'Nguyễn Văn A',
  idCardType: 'CHIP_ID',
  idCardNo: '123456789012',
  // ... other fields
  frontCardDocumentId: frontDoc.documentId,
);

final response = await registrationService.registerUser(registration);
```

## Notes

- Tất cả services đều sử dụng singleton pattern
- Sử dụng AppLogger cho logging thống nhất
- Tuân thủ pattern của dự án hiện tại
- Support cả dark và light theme
- Error handling đầy đủ với custom exceptions
- Cache strategy tối ưu cho performance 