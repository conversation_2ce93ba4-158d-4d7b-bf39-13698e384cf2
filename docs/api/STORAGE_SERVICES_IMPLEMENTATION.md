# Storage API Services Implementation

## Tổng quan

Storage API services đư<PERSON>c implement để quản lý file storage với MinIO backend, bao gồm upload, download, presigned URLs, và metadata management. Tất cả services đều tích hợp với BackendUrlManager để đảm bảo domain consistency.

## Các Components Chính

### 1. Models

#### StorageUploadResponse
```dart
class StorageUploadResponse {
  final String objectName;
  final String fileName;
  final String contentType;
  final int size;
  final String checksum;
  final DateTime uploadTime;
  final String? downloadUrl;
  final Map<String, dynamic> metadata;
}
```

#### StorageFileMetadata
```dart
class StorageFileMetadata {
  final String objectName;
  final String fileName;
  final String contentType;
  final int size;
  final DateTime lastModified;
  final String etag;
  final Map<String, dynamic> metadata;
}
```

#### StorageFileInfo
```dart
class StorageFileInfo {
  final String objectName;
  final String fileName;
  final String contentType;
  final int size;
  final DateTime lastModified;
}
```

#### PresignedUrlRequest
```dart
class PresignedUrlRequest {
  final String objectName;
  final String urlType;
  final int? expirationMinutes;
}
```

#### PresignedUrlResponse
```dart
class PresignedUrlResponse {
  final String url;
  final String objectName;
  final String urlType;
  final DateTime expirationTime;
  final int expirationMinutes;
  
  // Helper properties
  bool get isExpired;
  int get remainingMinutes;
}
```

### 2. StorageService

Service chính để quản lý file storage với MinIO.

#### Các Methods Chính

##### Upload File
```dart
Future<StorageUploadResponse> uploadFile({
  required File file,
  String? folderPath,
  bool generateDownloadUrl = false,
  Map<String, String>? metadata,
})
```

##### Upload File với Progress
```dart
Future<StorageUploadResponse> uploadFileWithProgress({
  required File file,
  String? folderPath,
  ProgressCallback? onProgress,
  bool generateDownloadUrl = false,
  Map<String, String>? metadata,
})
```

##### Generate Presigned URL
```dart
Future<String> generatePresignedUrl({
  required String objectName,
  required String urlType,
  int expirationMinutes = 60,
})
```

##### Download File
```dart
Future<File> downloadFile({
  required String objectName,
  String? localPath,
  ProgressCallback? onProgress,
})
```

##### Get File Metadata
```dart
Future<StorageFileMetadata> getFileMetadata(String objectName)
```

##### List Files
```dart
Future<List<StorageFileInfo>> listFiles({String? prefix})
```

##### Check File Exists
```dart
Future<bool> fileExists(String objectName)
```

##### Delete File
```dart
Future<void> deleteFile(String objectName)
```

##### Delete Multiple Files
```dart
Future<void> deleteMultipleFiles(List<String> objectNames)
```

### 3. StorageUrlHelper

Utility class để xử lý URL replacement và validation.

#### Các Methods Chính

##### Replace Domain in URL
```dart
static Future<String> replaceDomainInUrl(String presignedUrl)
```

##### Replace Domain in Multiple URLs
```dart
static Future<List<String>> replaceDomainInUrls(List<String> presignedUrls)
```

##### Get Current Backend Domain
```dart
static Future<String> getCurrentBackendDomain()
```

##### Extract Object Name from URL
```dart
static String? extractObjectNameFromUrl(String presignedUrl)
```

##### Validate Presigned URL
```dart
static bool isValidPresignedUrl(String url)
```

##### Get URL Expiration Time
```dart
static DateTime? getUrlExpirationTime(String presignedUrl)
```

##### Check if URL Needs Domain Replacement
```dart
static Future<bool> needsDomainReplacement(String url)
```

##### Validate File Size
```dart
static bool validateFileSize(int fileSize, {int maxSizeInMB = 50})
```

##### Validate File Type
```dart
static bool validateFileType(String mimeType, List<String> allowedTypes)
```

##### Generate Unique Filename
```dart
static String generateUniqueFilename(String originalName)
```

### 4. StorageConstants

Constants cho file types, size limits, và folder paths.

#### File Size Limits
```dart
static const int maxFileSizeInMB = 50;
static const int maxImageSizeInMB = 10;
static const int maxDocumentSizeInMB = 25;
```

#### Allowed File Types
```dart
static const List<String> allowedImageTypes = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];

static const List<String> allowedDocumentTypes = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];
```

#### Folder Paths
```dart
static const String documentsFolder = 'documents';
static const String customersFolder = 'customers';
static const String employeesFolder = 'employees';
static const String idCardsFolder = 'id_cards';
static const String contractsFolder = 'contracts';
static const String portraitsFolder = 'portraits';
```

#### Helper Methods
```dart
static String createCustomerFolderPath(String customerId);
static String createEmployeeFolderPath(String employeeId);
static String createIdCardFolderPath(String userId);
static String createContractFolderPath(String userId);
static String createPortraitFolderPath(String userId);
```

### 5. DocumentService Integration

DocumentService đã được cập nhật để tích hợp với StorageService.

#### Các Methods Mới

##### Upload và Insert Document
```dart
Future<DocumentModel> uploadAndInsertDocument({
  required File file,
  required String documentTypeCode,
  String? folderPath,
  Map<String, dynamic>? metadata,
})
```

##### Upload và Insert ID Card Document
```dart
Future<DocumentModel> uploadAndInsertIdCardDocument({
  required File file,
  String? folderPath,
  Map<String, dynamic>? metadata,
})
```

##### Upload và Insert ID Card Front Document
```dart
Future<DocumentModel> uploadAndInsertIdCardFrontDocument({
  required File file,
  String? folderPath,
  Map<String, dynamic>? metadata,
})
```

##### Upload và Insert ID Card Back Document
```dart
Future<DocumentModel> uploadAndInsertIdCardBackDocument({
  required File file,
  String? folderPath,
  Map<String, dynamic>? metadata,
})
```

##### Upload và Insert Portrait Document
```dart
Future<DocumentModel> uploadAndInsertPortraitDocument({
  required File file,
  String? folderPath,
  Map<String, dynamic>? metadata,
})
```

##### Upload và Insert Contract Document
```dart
Future<DocumentModel> uploadAndInsertContractDocument({
  required File file,
  String? folderPath,
  Map<String, dynamic>? metadata,
})
```

## Domain Replacement Strategy

### Vấn đề
MinIO backend trả về presigned URLs với MinIO domain, nhưng app cần sử dụng current backend domain để đảm bảo consistency và security.

### Giải pháp
1. **BackendUrlManager Integration**: Sử dụng BackendUrlManager để lấy current backend domain
2. **Automatic Replacement**: Tự động replace MinIO domain với current backend domain
3. **Fallback Strategy**: Nếu replacement fails, sử dụng original URL

### Implementation
```dart
static Future<String> replaceDomainInUrl(String presignedUrl) async {
  try {
    // Lấy current backend URL từ BackendUrlManager
    final currentDomain = await BackendUrlManager.getSelectedUrl();
    
    // Extract domain từ current backend URL
    final uri = Uri.parse(currentDomain);
    final appDomain = '${uri.scheme}://${uri.host}${uri.port != 80 && uri.port != 443 ? ':${uri.port}' : ''}';
    
    // Parse presigned URL
    final presignedUri = Uri.parse(presignedUrl);
    
    // Replace domain
    final newUrl = presignedUrl.replaceFirst(
      '${presignedUri.scheme}://${presignedUri.host}${presignedUri.port != 80 && presignedUri.port != 443 ? ':${presignedUri.port}' : ''}',
      appDomain,
    );
    
    return newUrl;
  } catch (e) {
    return presignedUrl; // Return original URL if replacement fails
  }
}
```

## Error Handling

### StorageException
```dart
class StorageException implements Exception {
  final String message;
  final StorageExceptionType type;
  final Object? originalException;
}
```

### Exception Types
```dart
enum StorageExceptionType {
  uploadFailed,
  downloadFailed,
  fileNotFound,
  invalidFile,
  presignedUrlFailed,
  deleteFailed,
  apiError,
  invalidResponse,
  unauthorized,
  unknown,
}
```

## Usage Examples

### 1. Upload File với Progress
```dart
final storageService = StorageService();

try {
  final uploadResponse = await storageService.uploadFileWithProgress(
    file: File('/path/to/document.jpg'),
    folderPath: 'documents/customers/customer_123',
    onProgress: (sent, total) {
      final progress = (sent / total * 100).round();
      print('Upload progress: $progress%');
    },
    generateDownloadUrl: true,
    metadata: {
      'customer_id': 'customer_123',
      'document_type': 'id_card',
    },
  );
  
  print('File uploaded: ${uploadResponse.objectName}');
  print('Download URL: ${uploadResponse.downloadUrl}');
} on StorageException catch (e) {
  print('Upload failed: ${e.message}');
}
```

### 2. Generate Presigned URL
```dart
try {
  final presignedUrl = await storageService.generatePresignedUrl(
    objectName: 'documents/customers/customer_123/document.jpg',
    urlType: 'DOWNLOAD',
    expirationMinutes: 120,
  );
  
  print('Presigned URL: $presignedUrl');
  // URL đã được replace domain theo current backend URL
} on StorageException catch (e) {
  print('Failed to generate presigned URL: ${e.message}');
}
```

### 3. Download File
```dart
try {
  final downloadedFile = await storageService.downloadFile(
    objectName: 'documents/customers/customer_123/document.jpg',
    localPath: '/tmp/downloaded_document.jpg',
    onProgress: (received, total) {
      final progress = (received / total * 100).round();
      print('Download progress: $progress%');
    },
  );
  
  print('File downloaded: ${downloadedFile.path}');
} on StorageException catch (e) {
  print('Download failed: ${e.message}');
}
```

### 4. Upload và Insert Document
```dart
final documentService = DocumentService();

try {
  final document = await documentService.uploadAndInsertIdCardFrontDocument(
    file: File('/path/to/id_card_front.jpg'),
    folderPath: StorageConstants.createIdCardFolderPath('user_123'),
    metadata: {
      'customer_id': 'customer_123',
      'card_side': 'front',
    },
  );
  
  print('Document uploaded and inserted: ${document.documentId}');
} catch (e) {
  print('Failed to upload and insert document: $e');
}
```

### 5. List Files
```dart
try {
  final files = await storageService.listFiles(
    prefix: 'documents/customers/customer_123/',
  );
  
  for (final file in files) {
    print('File: ${file.fileName}, Size: ${file.size} bytes');
  }
} on StorageException catch (e) {
  print('Failed to list files: ${e.message}');
}
```

### 6. Check File Exists
```dart
try {
  final exists = await storageService.fileExists(
    'documents/customers/customer_123/document.jpg',
  );
  
  if (exists) {
    print('File exists');
  } else {
    print('File does not exist');
  }
} on StorageException catch (e) {
  print('Failed to check file exists: ${e.message}');
}
```

### 7. Delete File
```dart
try {
  await storageService.deleteFile(
    'documents/customers/customer_123/document.jpg',
  );
  
  print('File deleted successfully');
} on StorageException catch (e) {
  print('Failed to delete file: ${e.message}');
}
```

## API Endpoints

### Upload File
- **Method**: POST
- **Endpoint**: `/api/v1/storage/upload`
- **Content-Type**: `multipart/form-data`
- **Parameters**:
  - `file`: File to upload
  - `folderPath` (optional): Folder path
  - `generateDownloadUrl` (optional): Generate download URL
  - `metadata` (optional): Additional metadata

### Generate Presigned URL
- **Method**: POST
- **Endpoint**: `/api/v1/storage/presigned-url`
- **Content-Type**: `application/json`
- **Body**:
  ```json
  {
    "objectName": "path/to/file.jpg",
    "urlType": "DOWNLOAD",
    "expirationMinutes": 60
  }
  ```

### Download File
- **Method**: GET
- **Endpoint**: `/api/v1/storage/download/{objectName}`

### Get File Metadata
- **Method**: GET
- **Endpoint**: `/api/v1/storage/metadata/{objectName}`

### List Files
- **Method**: GET
- **Endpoint**: `/api/v1/storage/list`
- **Query Parameters**:
  - `prefix` (optional): File prefix filter

### Check File Exists
- **Method**: GET
- **Endpoint**: `/api/v1/storage/exists/{objectName}`

### Delete File
- **Method**: DELETE
- **Endpoint**: `/api/v1/storage/download/{objectName}`

### Delete Multiple Files
- **Method**: DELETE
- **Endpoint**: `/api/v1/storage/download/batch`
- **Body**: Array of object names

## Integration với BackendUrlManager

### Automatic Domain Replacement
Tất cả presigned URLs được tự động replace domain để sử dụng current backend domain:

```dart
// Original MinIO URL
https://minio.example.com:9000/bucket/path/to/file.jpg?signature=...

// Replaced URL với current backend domain
http://*************:8097/api/v1/storage/download/path/to/file.jpg?signature=...
```

### Benefits
1. **Consistency**: Tất cả file access đi qua cùng một domain
2. **Security**: Không expose MinIO internal domain
3. **Flexibility**: Dễ dàng thay đổi backend domain
4. **User Customization**: Hỗ trợ user custom domain

## File Organization

### Folder Structure
```
documents/
├── customers/
│   └── customer_123/
│       ├── id_cards/
│       │   ├── front.jpg
│       │   └── back.jpg
│       ├── contracts/
│       │   └── contract.pdf
│       └── portraits/
│           └── portrait.jpg
├── employees/
│   └── employee_456/
└── temp/
    └── session_789/
```

### Naming Convention
- **Customer Documents**: `documents/customers/{customer_id}/{document_type}/{filename}`
- **Employee Documents**: `documents/employees/{employee_id}/{document_type}/{filename}`
- **Temporary Files**: `temp/{session_id}/{filename}`

## Performance Considerations

### File Size Limits
- **General Files**: 50MB max
- **Images**: 10MB max
- **Documents**: 25MB max

### Progress Tracking
- Upload và download progress tracking
- Real-time progress updates
- Progress callback support

### Caching Strategy
- File metadata caching
- Presigned URL caching (with expiration)
- Domain replacement caching

## Security Features

### File Validation
- File type validation
- File size validation
- File extension validation

### Access Control
- Presigned URL expiration
- Domain replacement for security
- Metadata-based access control

### Error Handling
- Comprehensive error types
- Detailed error messages
- Original exception preservation

## Testing

### Unit Tests
- Model serialization/deserialization
- URL replacement logic
- File validation logic

### Integration Tests
- API endpoint testing
- File upload/download testing
- Error handling testing

### Manual Testing
- File upload với progress
- Presigned URL generation
- Domain replacement verification
- Error scenario testing

## Future Enhancements

### Planned Features
1. **Batch Operations**: Upload/download multiple files
2. **File Compression**: Automatic image compression
3. **Thumbnail Generation**: Automatic thumbnail creation
4. **File Versioning**: File version management
5. **Advanced Metadata**: Extended metadata support

### Performance Improvements
1. **Chunked Upload**: Large file chunked upload
2. **Resumable Upload**: Resume interrupted uploads
3. **Parallel Processing**: Parallel file operations
4. **Caching Layer**: Advanced caching strategy

### Security Enhancements
1. **Encryption**: File encryption at rest
2. **Access Logging**: File access audit logs
3. **Rate Limiting**: API rate limiting
4. **Virus Scanning**: File virus scanning 