# API Kiểm tra Đăng ký

## Tổng quan

API kiểm tra đăng ký cho phép kiểm tra xem một số CMND/CCCD đã được đăng ký trong hệ thống hay chưa.

## Endpoint

```
POST /rest/rpc/check_register_by_id_card
```

## Tham số

| Tên | Kiểu | Bắt buộc | Mô tả |
|-----|------|----------|-------|
| p_id_card_no | String | Có | Số CMND/CCCD (12 chữ số) |

## Response

### Thành công - Bản ghi tồn tại

```json
{
  "success": true,
  "message": "Registration record found",
  "data": {
    "id": "uuid-registration-id",
    "status": "PENDING",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### Thành công - <PERSON><PERSON>n ghi không tồn tại

```json
{
  "success": false,
  "message": "Registration record not found with this ID card number",
  "data": null
}
```

### Lỗi

```json
{
  "success": false,
  "message": "Error message",
  "code": "ERROR_CODE"
}
```

## Sử dụng trong Flutter

### Import

```dart
import 'package:kiloba_biz/features/auth/services/registration_service.dart';
import 'package:kiloba_biz/features/auth/models/check_registration_response_model.dart';
```

### Kiểm tra đăng ký

```dart
final registrationService = RegistrationService();

try {
  final response = await registrationService.checkRegistrationByIdCard('123456789012');
  
  if (response.exists) {
    print('Đã tìm thấy đăng ký: ${response.id}');
    print('Trạng thái: ${response.status}');
    print('Ngày tạo: ${response.createdAt}');
  } else {
    print('Chưa có đăng ký cho số CMND/CCCD này');
  }
} catch (e) {
  print('Lỗi: $e');
}
```

### Helper Methods

```dart
// Kiểm tra xem có tồn tại hay không
bool exists = registrationService.isRegistrationExists(response);

// Lấy mô tả trạng thái bằng tiếng Việt
String statusDesc = registrationService.getRegistrationStatusDescription(response);

// Kiểm tra có thể cập nhật hay không
bool canUpdate = registrationService.canUpdateRegistration(response);

// Kiểm tra có thể xóa hay không
bool canDelete = registrationService.canDeleteRegistration(response);
```

## Trạng thái Registration

| Trạng thái | Mô tả | Có thể cập nhật | Có thể xóa |
|------------|-------|-----------------|------------|
| PENDING | Chờ xử lý | Có | Có |
| APPROVED | Đã duyệt | Không | Không |
| REJECTED | Đã từ chối | Có | Không |
| ACTIVE | Đang hoạt động | Không | Không |
| INACTIVE | Không hoạt động | Không | Không |

## Validation

- Số CMND/CCCD phải có đúng 12 chữ số
- Số CMND/CCCD không được để trống

## Error Handling

API sẽ trả về các loại lỗi sau:

- `validationError`: Dữ liệu đầu vào không hợp lệ
- `apiError`: Lỗi từ API server
- `invalidResponse`: Response không đúng định dạng
- `unknown`: Lỗi không xác định

## Ví dụ sử dụng đầy đủ

```dart
class RegistrationChecker {
  final RegistrationService _registrationService = RegistrationService();

  Future<void> checkRegistration(String idCardNo) async {
    try {
      final response = await _registrationService.checkRegistrationByIdCard(idCardNo);
      
      if (response.exists) {
        final statusDesc = _registrationService.getRegistrationStatusDescription(response);
        final canUpdate = _registrationService.canUpdateRegistration(response);
        final canDelete = _registrationService.canDeleteRegistration(response);
        
        print('Tìm thấy đăng ký:');
        print('- ID: ${response.id}');
        print('- Trạng thái: $statusDesc');
        print('- Ngày tạo: ${response.createdAt}');
        print('- Có thể cập nhật: $canUpdate');
        print('- Có thể xóa: $canDelete');
      } else {
        print('Chưa có đăng ký cho số CMND/CCCD: $idCardNo');
      }
    } on RegistrationException catch (e) {
      switch (e.type) {
        case RegistrationExceptionType.validationError:
          print('Lỗi validation: ${e.message}');
          break;
        case RegistrationExceptionType.apiError:
          print('Lỗi API: ${e.message}');
          break;
        default:
          print('Lỗi: ${e.message}');
      }
    } catch (e) {
      print('Lỗi không xác định: $e');
    }
  }
}
``` 