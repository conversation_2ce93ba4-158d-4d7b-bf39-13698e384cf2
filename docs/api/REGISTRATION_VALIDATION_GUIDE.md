# Registration Validation Guide

## Tổng quan

Hướng dẫn này giải thích các quy tắc validation cho Registration API và cách khắc phục các lỗi thường gặp.

## Các trường bắt buộc

### 1. Thông tin cá nhân
- **fullName**: <PERSON><PERSON> tên đầy đủ (không được để trống)
- **idCardType**: Loại giấy tờ (chỉ chấp nhận `CHIP_ID` hoặc `PASSPORT`)
- **idCardNo**: Số CMND/CCCD (phải có đúng 12 chữ số)
- **issueDate**: <PERSON><PERSON><PERSON> cấp (format: YYYY-MM-DD)
- **issuePlace**: <PERSON><PERSON><PERSON> cấp (không được để trống)
- **permanentAddress**: Địa chỉ thường trú (không được để trống)
- **phoneNumber**: Số điện thoại (format Việt Nam)
- **email**: <PERSON><PERSON> (phải đúng định dạng)

### 2. Thông tin đăng ký
- **registerType**: Loại đăng ký (chỉ chấp nhận `COLLABORATOR` hoặc `BANK_OFFICER`)
- **provinceId**: ID tỉnh/thành phố (UUID)
- **branchId**: ID chi nhánh (UUID)

## Quy tắc validation

### Email Format
```
^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$
```
**Ví dụ hợp lệ:**
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**Ví dụ không hợp lệ:**
- `invalid-email`
- `test@`
- `@example.com`
- `test.example.com`

### Phone Number Format (Việt Nam)
```
9-10 chữ số, có thể có format:
- 0xxxxxxxxx (10 số)
- +84xxxxxxxxx (11 số)
- xxxxxxxxx (9 số, tự động thêm 0)
- xxxxxxxxxx (10 số, có thể không bắt đầu bằng 0)
```
**Ví dụ hợp lệ:**
- `0*********`
- `+84*********`
- `*********` (tự động format thành 0*********)
- `*********0` (10 số)

**Ví dụ không hợp lệ:**
- `********` (thiếu số)
- `0*********01` (thừa số)
- `abc123def` (có ký tự không phải số)

### ID Card Number Format
```
^[0-9]{12}$
```
**Ví dụ hợp lệ:**
- `*********012`

**Ví dụ không hợp lệ:**
- `*********` (thiếu 3 số)
- `*********0123` (thừa 1 số)
- `*********01a` (có ký tự không phải số)

### Date Format
```
YYYY-MM-DD
```
**Ví dụ hợp lệ:**
- `2020-01-01`
- `2024-12-31`

**Ví dụ không hợp lệ:**
- `01/01/2020` (sai format)
- `2020-1-1` (thiếu số 0)
- `2020-13-01` (tháng không hợp lệ)

## Các lỗi thường gặp và cách khắc phục

### 1. Lỗi ID Card Type
**Lỗi:** `Loại giấy tờ phải là CHIP_ID hoặc PASSPORT`

**Nguyên nhân:** Sử dụng giá trị khác với `CHIP_ID` hoặc `PASSPORT`

**Cách khắc phục:**
```dart
// Sai
idCardType: 'CCCD'

// Đúng
idCardType: 'CHIP_ID'
```

### 2. Lỗi Phone Number Format
**Lỗi:** `Số điện thoại không đúng định dạng`

**Nguyên nhân:** Số điện thoại không đúng format Việt Nam

**Cách khắc phục:**
```dart
// Sử dụng helper function để format
String formatPhoneNumber(String? phone) {
  if (phone == null || phone.isEmpty) return '';
  
  // Remove all non-digit characters
  final digits = phone.replaceAll(RegExp(r'[^\d]'), '');
  
  // Handle Vietnamese phone numbers
  if (digits.startsWith('84') && digits.length == 11) {
    return '0${digits.substring(2)}';
  } else if (digits.startsWith('0') && digits.length == 10) {
    return digits;
  } else if (digits.length == 9) {
    return '0$digits';
  }
  
  return phone;
}
```

### 3. Lỗi Date Format
**Lỗi:** `Ngày cấp không đúng định dạng (YYYY-MM-DD)`

**Nguyên nhân:** Date không đúng format YYYY-MM-DD

**Cách khắc phục:**
```dart
// Helper function để convert date format
String convertDateFormat(String? dateStr) {
  if (dateStr == null || dateStr.isEmpty) return '';
  if (dateStr.contains('/')) {
    // Convert DD/MM/YYYY to YYYY-MM-DD
    final parts = dateStr.split('/');
    if (parts.length == 3) {
      return '${parts[2]}-${parts[1].padLeft(2, '0')}-${parts[0].padLeft(2, '0')}';
    }
  }
  return dateStr;
}
```

### 4. Lỗi Register Type
**Lỗi:** `Loại đăng ký phải là COLLABORATOR hoặc BANK_OFFICER`

**Nguyên nhân:** Sử dụng giá trị khác với `COLLABORATOR` hoặc `BANK_OFFICER`

**Cách khắc phục:**
```dart
// Sai
registerType: 'CUSTOMER'

// Đúng
registerType: 'COLLABORATOR'
// hoặc
registerType: 'BANK_OFFICER'
```

## Debug và Logging

### 1. Log Registration Data
```dart
// Log registration data trước khi gửi
_logger.d('Registration data: ${registration.toJson()}');
```

### 2. Validate trước khi gửi
```dart
// Validate registration data before submitting
final validationErrors = registrationService.getValidationErrors(registration);
if (validationErrors.isNotEmpty) {
  _logger.e('Registration validation failed: ${validationErrors.join(', ')}');
  throw Exception('Dữ liệu đăng ký không hợp lệ: ${validationErrors.join(', ')}');
}
```

### 3. Handle Validation Errors
```dart
try {
  final response = await registrationService.registerUser(registration);
} on RegistrationException catch (e) {
  switch (e.type) {
    case RegistrationExceptionType.validationError:
      // Hiển thị lỗi validation cho user
      showErrorDialog('Dữ liệu không hợp lệ', e.message);
      break;
    case RegistrationExceptionType.idCardAlreadyExists:
      // Hiển thị thông báo CMND đã tồn tại
      showErrorDialog('Lỗi', 'Số CMND/CCCD đã được đăng ký');
      break;
    default:
      // Xử lý lỗi khác
      showErrorDialog('Lỗi', 'Có lỗi xảy ra khi đăng ký');
  }
}
```

## Testing

### Test Cases cho Validation
```dart
// Test valid registration
final validRegistration = RegistrationModel(
  fullName: 'Nguyễn Văn A',
  idCardType: 'CHIP_ID',
  idCardNo: '*********012',
  issueDate: '2020-01-01',
  issuePlace: 'Hà Nội',
  permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
  phoneNumber: '0*********',
  email: '<EMAIL>',
  registerType: 'COLLABORATOR',
  provinceId: 'valid-province-id',
  branchId: 'valid-branch-id',
  dataSource: 'APP_SALE',
);

// Test invalid cases
final invalidRegistration = RegistrationModel(
  fullName: '', // Empty name
  idCardType: 'CCCD', // Invalid type
  idCardNo: '*********', // Invalid length
  issueDate: '01/01/2020', // Wrong format
  phoneNumber: '*********', // Invalid format
  email: 'invalid-email', // Invalid format
  registerType: 'CUSTOMER', // Invalid type
  // ... other fields
);
```

## Best Practices

1. **Validate dữ liệu ngay khi user nhập** để tránh lỗi khi submit
2. **Format dữ liệu trước khi gửi** (phone, date, etc.)
3. **Log dữ liệu trước khi gửi** để debug
4. **Handle tất cả các loại lỗi** và hiển thị thông báo phù hợp
5. **Test validation rules** với các test cases khác nhau 