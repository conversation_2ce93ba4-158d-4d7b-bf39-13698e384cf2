# Registration API Documentation

## Tổng quan

Registration API được sử dụng để đăng ký user mới thông qua mobile app. API này sử dụng PostgreSQL function `insert_register` để xử lý việc tạo registration record.

## PostgreSQL Function: `insert_register`

### Endpoint
```
POST /rest/rpc/insert_register
```

### Parameters

#### Required Parameters
- `p_full_name` (VARCHAR(255)): H<PERSON> tên đầy đủ
- `p_id_card_type` (VARCHAR(20)): Lo<PERSON>i giấy tờ (`CHIP_ID` hoặc `PASSPORT`)
- `p_id_card_no` (VARCHAR(20)): Số CMND/CCCD (12 chữ số)
- `p_issue_date` (DATE): Ng<PERSON><PERSON> cấp (format: YYYY-MM-DD)
- `p_issue_place` (VARCHAR(255)): <PERSON><PERSON><PERSON> cấp
- `p_permanent_address` (TEXT): Địa chỉ thường trú
- `p_phone_number` (VARCHAR(15)): <PERSON><PERSON> điện thoại
- `p_email` (VARCHAR(255)): Email
- `p_register_type` (VARCHAR(20)): Loại đăng ký (`COLLABORATOR` hoặc `BANK_OFFICER`)
- `p_province_id` (UUID): ID tỉnh/thành phố
- `p_branch_id` (UUID): ID chi nhánh

#### Optional Parameters
- `p_expiry_date` (DATE): Ngày hết hạn (format: YYYY-MM-DD)
- `p_position_id` (UUID): ID chức vụ
- `p_referrer_code` (VARCHAR(36)): Mã giới thiệu
- `p_front_card_document_id` (UUID): ID document mặt trước CMND
- `p_back_card_document_id` (UUID): ID document mặt sau CMND
- `p_data_source` (VARCHAR(20)): Nguồn dữ liệu (`APP_SALE`, `WEB_SALE`, `KPLUS`) - default: `APP_SALE`
- `p_metadata` (JSONB): Metadata bổ sung

### Response Format

#### Success Response
```json
{
  "success": true,
  "message": "Registration created successfully",
  "code": "SUCCESS",
  "data": {
    "register_id": "uuid",
    "register_type": "COLLABORATOR",
    "full_name": "Nguyễn Văn A",
    "id_card_type": "CHIP_ID",
    "id_card_no": "************",
    "phone_number": "**********",
    "email": "<EMAIL>",
    "status": "PENDING",
    "data_source": "APP_SALE",
    "province_id": "uuid",
    "branch_id": "uuid",
    "position_id": "uuid",
    "front_card_document_id": "uuid",
    "back_card_document_id": "uuid",
    "created_by": "MOBILE_APP",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "data": {
    "field": "field_name",
    "details": "Additional details"
  }
}
```

### Error Codes

- `VALIDATION_ERROR`: Lỗi validation dữ liệu
- `ID_CARD_ALREADY_EXISTS`: Số CMND/CCCD đã tồn tại
- `PROVINCE_NOT_FOUND`: Tỉnh/thành phố không tồn tại
- `BRANCH_NOT_FOUND`: Chi nhánh không tồn tại
- `POSITION_NOT_FOUND`: Chức vụ không tồn tại
- `FRONT_DOCUMENT_NOT_FOUND`: Document mặt trước không tồn tại
- `BACK_DOCUMENT_NOT_FOUND`: Document mặt sau không tồn tại
- `INTERNAL_ERROR`: Lỗi hệ thống

## Flutter Implementation

### RegistrationService

Service chính để xử lý đăng ký user:

```dart
final registrationService = RegistrationService();

// Đăng ký collaborator
final response = await registrationService.registerCollaborator(
  fullName: 'Nguyễn Văn A',
  idCardType: 'CHIP_ID',
  idCardNo: '************',
  issueDate: '2020-01-01',
  issuePlace: 'Hà Nội',
  permanentAddress: '123 Đường ABC, Quận XYZ, Hà Nội',
  phoneNumber: '**********',
  email: '<EMAIL>',
  provinceId: 'f10158fa-7171-48e6-b1eb-1aefcf3457a6',
  branchId: '525e4081-9674-44fe-9fd4-80e24c5bd75f',
  expiryDate: '2025-01-01',
  positionId: 'fbe05dc8-cf05-4743-a71c-ffa71c9e68a0',
  frontCardDocumentId: '********-164e-42c8-8dfa-d7139aeb571c',
);

// Đăng ký bank officer
final response = await registrationService.registerBankOfficer(
  // ... parameters tương tự
);
```

### Validation Rules

#### Required Fields
- Họ tên không được để trống
- Loại giấy tờ phải là `CHIP_ID` hoặc `PASSPORT`
- Số CMND/CCCD phải có 12 chữ số
- Ngày cấp không được để trống (format: YYYY-MM-DD)
- Nơi cấp không được để trống
- Địa chỉ thường trú không được để trống
- Số điện thoại phải đúng định dạng Việt Nam
- Email phải đúng định dạng
- Loại đăng ký phải là `COLLABORATOR` hoặc `BANK_OFFICER`
- Tỉnh/thành phố không được để trống
- Chi nhánh không được để trống

#### Format Validation
- **Email**: `^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$`
- **Phone**: `^(0|\+84)[0-9]{9}$`
- **ID Card**: `^[0-9]{12}$`
- **Date**: `YYYY-MM-DD`

### Error Handling

Service trả về `RegistrationException` với các loại lỗi:

```dart
try {
  final response = await registrationService.registerUser(registration);
} on RegistrationException catch (e) {
  switch (e.type) {
    case RegistrationExceptionType.validationError:
      // Xử lý lỗi validation
      break;
    case RegistrationExceptionType.idCardAlreadyExists:
      // Xử lý lỗi CMND đã tồn tại
      break;
    case RegistrationExceptionType.notFound:
      // Xử lý lỗi không tìm thấy (province, branch, etc.)
      break;
    case RegistrationExceptionType.apiError:
      // Xử lý lỗi API
      break;
    default:
      // Xử lý lỗi khác
  }
}
```

## Business Rules

1. **Register Types**: Chỉ cho phép `COLLABORATOR` và `BANK_OFFICER`
2. **ID Card Types**: Chỉ cho phép `CHIP_ID` và `PASSPORT`
3. **Data Sources**: Chỉ cho phép `APP_SALE`, `WEB_SALE`, `KPLUS`
4. **Unique ID Card**: Số CMND/CCCD phải là duy nhất
5. **Status**: Tất cả registration mới có status `PENDING`
6. **Audit**: Created by luôn là `MOBILE_APP` cho mobile registration

## Security

- Function sử dụng `SECURITY DEFINER` để chạy với quyền của owner
- Validation được thực hiện ở cả client và server
- UUID được sử dụng cho tất cả ID fields
- SQL injection được ngăn chặn thông qua parameterized queries

## Testing

### Test Cases

1. **Valid Registration**: Đăng ký với dữ liệu hợp lệ
2. **Invalid ID Card**: Số CMND không đúng định dạng
3. **Duplicate ID Card**: Số CMND đã tồn tại
4. **Invalid Email**: Email không đúng định dạng
5. **Invalid Phone**: Số điện thoại không đúng định dạng
6. **Missing Required Fields**: Thiếu các trường bắt buộc
7. **Invalid Register Type**: Loại đăng ký không hợp lệ
8. **Invalid Province/Branch**: Tỉnh/chi nhánh không tồn tại

### Test Data

```dart
// Valid test data
final testRegistration = RegistrationModel(
  fullName: 'Test User',
  idCardType: 'CHIP_ID',
  idCardNo: '************',
  issueDate: '2020-01-01',
  issuePlace: 'Test Place',
  permanentAddress: 'Test Address',
  phoneNumber: '**********',
  email: '<EMAIL>',
  registerType: 'COLLABORATOR',
  provinceId: 'valid-province-id',
  branchId: 'valid-branch-id',
  expiryDate: '2025-01-01',
  dataSource: 'APP_SALE',
);
``` 