# Rich Media Notification Implementation

## Tổng quan

Ứng dụng Kiloba Business đã được cập nhật để hỗ trợ hiển thị hình ảnh trong local notifications (rich media notifications) trên cả Android và iOS.

## Tính năng đã triển khai

### 1. Android Big Picture Notifications
- Sử dụng `BigPictureStyleInformation` để hiển thị hình ảnh lớn trong notification
- Hỗ trợ large icon (icon lớn) và big picture (hình ảnh chính)
- Tự động fallback về `BigTextStyleInformation` nếu không thể tải hình ảnh

### 2. iOS Rich Media Attachments
- Sử dụng `DarwinNotificationAttachment` để hiển thị hình ảnh
- Tự động download và lưu hình ảnh vào local storage
- Hỗ trợ các định dạng hình ảnh phổ biến

### 3. Notification Channels
- Channel chính: `kiloba_notifications` - cho notifications thông thường
- Channel rich media: `kiloba_rich_media` - cho notifications có hình ảnh
- Tự động chọn channel phù hợp dựa trên loại notification

## Cách sử dụng

### 1. Gửi FCM Message với hình ảnh

```json
{
  "notification": {
    "title": "Thông báo mới",
    "body": "Nội dung thông báo"
  },
  "data": {
    "image_url": "https://example.com/image.jpg",
    "large_icon_url": "https://example.com/icon.jpg",
    "action_url": "https://kiloba.biz/action"
  }
}
```

### 2. Test Rich Media Notification

```dart
// Gọi phương thức test
await FirebaseService().testRichMediaNotification();
```

### 3. Tùy chỉnh hình ảnh

- `image_url`: URL của hình ảnh chính (big picture)
- `large_icon_url`: URL của icon lớn (optional)
- Hình ảnh sẽ được tự động download và cache

## Cấu trúc code

### FirebaseService
- `_getStyleInformation()`: Tạo style cho Android notifications
- `_getIOSAttachments()`: Tạo attachments cho iOS notifications
- `_downloadAndSaveImage()`: Download và lưu hình ảnh
- `_getByteArrayFromUrl()`: Lấy byte array từ URL
- `_createNotificationChannels()`: Tạo notification channels

### Các phương thức hỗ trợ
- `testRichMediaNotification()`: Test rich media notifications
- Tự động fallback khi không thể tải hình ảnh
- Error handling và logging

## Dependencies

```yaml
dependencies:
  http: ^1.2.2
  path_provider: ^2.1.5
  flutter_local_notifications: ^19.3.0
```

## Lưu ý quan trọng

### 1. Performance
- Hình ảnh được download bất đồng bộ
- Có timeout và retry logic
- Fallback graceful khi lỗi

### 2. Storage
- Hình ảnh được lưu trong app documents directory
- Tự động cleanup (có thể implement thêm)
- Không ảnh hưởng đến app storage

### 3. Network
- Sử dụng HTTP client để download
- Error handling cho network issues
- Không block UI thread

### 4. Platform Differences
- Android: Sử dụng `BigPictureStyleInformation`
- iOS: Sử dụng `DarwinNotificationAttachment`
- Tự động detect platform và áp dụng phù hợp

## Testing

### 1. Test với hình ảnh thật
```dart
// Gửi FCM message với image_url thật
{
  "image_url": "https://your-server.com/notification-image.jpg"
}
```

### 2. Test fallback
```dart
// Gửi FCM message với image_url không hợp lệ
{
  "image_url": "https://invalid-url.com/image.jpg"
}
```

### 3. Test performance
- Monitor memory usage khi download nhiều hình ảnh
- Check network bandwidth
- Verify app performance không bị ảnh hưởng

## Troubleshooting

### 1. Hình ảnh không hiển thị
- Kiểm tra URL hình ảnh có hợp lệ không
- Check network connectivity
- Verify image format được hỗ trợ

### 2. Notification không hiển thị
- Kiểm tra notification permissions
- Verify notification channels được tạo
- Check app notification settings

### 3. Performance issues
- Monitor memory usage
- Check network requests
- Verify image sizes không quá lớn

## Future Improvements

### 1. Image Caching
- Implement proper image caching
- Add cache expiration
- Automatic cache cleanup

### 2. Image Optimization
- Compress images before display
- Support multiple image formats
- Progressive image loading

### 3. Advanced Features
- Video attachments support
- Interactive notifications
- Custom notification layouts

## References

- [Flutter Local Notifications Documentation](https://pub.dev/packages/flutter_local_notifications)
- [Android Big Picture Style](https://developer.android.com/training/notify-user/expanded#big-picture-style)
- [iOS Rich Notifications](https://developer.apple.com/documentation/usernotifications/unnotificationattachment)
- [Local Notification Big Picture Example](../local_notification_big_picture.md) 