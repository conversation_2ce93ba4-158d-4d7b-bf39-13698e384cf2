# Notification Detail Dialog Implementation

## Tổng quan

Hệ thống dialog hiển thị chi tiết thông báo khi người dùng nhấn vào push notification đã được implement với các tính năng:

1. **Dialog chuyên nghiệp**: Sử dụng các widget có sẵn từ `lib/features/notifications/widgets`
2. **Tích hợp Bloc**: Sử dụng NotificationBloc để quản lý state và logic
3. **API Integration**: Sử dụng `getNotificationById` từ NotificationService
4. **Auto mark as read**: Tự động đánh dấu thông báo đã đọc khi hiển thị dialog

## Các thành phần đã tạo/cập nhật

### 1. Widgets
- `notification_detail_dialog.dart`: Dialog chính để hiển thị chi tiết thông báo
- `notification_dialog_listener.dart`: Widget listener để lắng nghe Bloc state

### 2. Services
- `notification_dialog_service.dart`: Service để quản lý việc hiển thị dialog

### 3. Bloc Events & States
- `ShowNotificationDetailDialog`: Event để hiển thị dialog
- `NotificationDetailDialogState`: State chứa thông tin thông báo để hiển thị

### 4. FirebaseService Updates
- Thêm logic để xử lý notification tap với NotificationDialogService
- Hỗ trợ hiển thị dialog khi có notification_id

## Cách sử dụng

### 1. Setup trong main.dart

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kiloba_biz/shared/services/firebase_service.dart';
import 'package:kiloba_biz/shared/services/notification_dialog_service.dart';
import 'package:kiloba_biz/features/notifications/blocs/notification_bloc.dart';
import 'package:kiloba_biz/features/notifications/widgets/notification_dialog_listener.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Khởi tạo NotificationBloc
  final notificationBloc = NotificationBloc();
  
  // GlobalKey cho Navigator
  final navigatorKey = GlobalKey<NavigatorState>();
  
  // Setup NotificationDialogService
  NotificationDialogService().setup(
    navigatorKey: navigatorKey,
    notificationBloc: notificationBloc,
  );
  
  // Khởi tạo FirebaseService
  await FirebaseService().initialize();
  
  runApp(MyApp(
    notificationBloc: notificationBloc,
    navigatorKey: navigatorKey,
  ));
}

class MyApp extends StatelessWidget {
  final NotificationBloc notificationBloc;
  final GlobalKey<NavigatorState> navigatorKey;
  
  const MyApp({
    super.key,
    required this.notificationBloc,
    required this.navigatorKey,
  });

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigatorKey,
      title: 'Kiloba Business',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      home: BlocProvider(
        create: (context) => notificationBloc,
        child: NotificationDialogListener(
          child: HomeScreen(),
        ),
      ),
    );
  }
}
```

### 2. Push Notification Format

Để dialog hoạt động, push notification cần có `notification_id` trong data:

```json
{
  "notification": {
    "title": "Tiêu đề thông báo",
    "body": "Nội dung thông báo"
  },
  "data": {
    "notification_id": "your-notification-id-here",
    "action_url": "optional-action-url"
  }
}
```

## Luồng hoạt động

1. **Người dùng nhấn push notification**
2. **FirebaseService** kiểm tra có `notification_id` không
3. **Nếu có**: Gọi `NotificationDialogService.showNotificationDialog(notificationId)`
4. **NotificationDialogService** gửi event `ShowNotificationDetailDialog` đến NotificationBloc
5. **NotificationBloc** gọi API `getNotificationById` để lấy chi tiết
6. **Bloc emit** state `NotificationDetailDialogState` với thông tin thông báo
7. **NotificationDialogListener** lắng nghe state và gọi `NotificationDialogService.showNotificationDetailDialog()`
8. **Dialog** hiển thị chi tiết thông báo và tự động mark as read

## Tính năng

- ✅ Hiển thị dialog đẹp với dark/light theme
- ✅ Tự động mark as read khi mở dialog
- ✅ Sử dụng các widget có sẵn (header, content, metadata, actions)
- ✅ Responsive design với constraints
- ✅ Error handling và fallback logic
- ✅ Tích hợp với NotificationBloc
- ✅ Support cho rich media (hình ảnh)
- ✅ Service pattern để quản lý dialog

## Lưu ý

- Chỉ những push notification có `notification_id` mới hiển thị dialog
- Nếu không có NotificationDialogService setup, sẽ fallback về logic cũ
- Dialog có thể đóng bằng cách nhấn nút close hoặc tap outside
- Tự động mark as read sau 500ms để tránh race condition
- NotificationDialogService là singleton, chỉ cần setup một lần 