@connection.md 
1) truy cập vào db để xem các bảng liên quan đến khách hàng (customer)
2) căn cứ vào các chức năng trong features customers: lib/features/customers
3) căn cứ vào mẫu thiết kế các function trong postgREST: /Users/<USER>/Works/kilobabiz/kiloba_biz-backend/kilobabiz-postgREST/mobile_api/insert_register.sql (lưu ý phần comment đầy đủ trên mỗi hàm, bao gồm giá trị đầy đủ của các enum, có thể có phần tổng quan các enum dùng chung cho các hàm)

Hãy lên ý tưởng thiết kế các hàm, trước tiên lập danh sách các hàm trước để tôi xem.


-- Results

Tôi sẽ giúp bạn thiết kế các hàm cho tính năng customers. <PERSON><PERSON><PERSON><PERSON><PERSON> ti<PERSON>, hãy để tôi khám phá cấu trúc database và các tính năng customers hiện tại.

```bash
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT schemaname, tablename FROM pg_tables WHERE tablename ILIKE '%customer%' OR tablename ILIKE '%client%' ORDER BY schemaname, tablename;"
```
Ran tool
Ran tool
Ran tool
Ran tool
Read file: /Users/<USER>/Works/kilobabiz/kiloba_biz-backend/kilobabiz-postgREST/mobile_api/insert_register.sql
```bash
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "\d public.customers"
```
```bash
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT unnest(enum_range(NULL::customer_status)) as customer_status_values;"
```
```bash
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT unnest(enum_range(NULL::customer_level)) as customer_level_values;"
```
```bash
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT unnest(enum_range(NULL::customer_source)) as customer_source_values;"
```
```bash
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT unnest(enum_range(NULL::marital_status)) as marital_status_values;"
```
Read file: lib/features/customers/screens/customers_tab.dart
Read file: lib/features/customers/screens/add_customer_screen.dart
Read file: lib/features/customers/screens/customer_detail_screen.dart
Dựa trên việc khám phá cấu trúc database và các tính năng customers hiện tại, tôi sẽ thiết kế danh sách các hàm cho tính năng customers. Đây là danh sách các hàm được đề xuất:

## 📋 DANH SÁCH CÁC HÀM CHO TÍNH NĂNG CUSTOMERS

### �� **1. QUẢN LÝ KHÁCH HÀNG CƠ BẢN**

#### 1.1. **insert_customer**
- **Mô tả**: Tạo khách hàng mới
- **Chức năng**: Thêm khách hàng với thông tin cơ bản, địa chỉ, phân loại
- **Input**: Thông tin cá nhân, địa chỉ, phân loại, tags, notes
- **Output**: Customer ID và thông tin đã tạo

#### 1.2. **update_customer**
- **Mô tả**: Cập nhật thông tin khách hàng
- **Chức năng**: Cập nhật thông tin cá nhân, địa chỉ, phân loại
- **Input**: Customer ID + thông tin cần cập nhật
- **Output**: Thông tin đã cập nhật

#### 1.3. **get_customer_detail**
- **Mô tả**: Lấy chi tiết khách hàng
- **Chức năng**: Lấy toàn bộ thông tin khách hàng + related data
- **Input**: Customer ID
- **Output**: Thông tin chi tiết + activities + documents + notes

#### 1.4. **delete_customer**
- **Mô tả**: Xóa khách hàng (soft delete)
- **Chức năng**: Đánh dấu khách hàng đã xóa
- **Input**: Customer ID
- **Output**: Trạng thái xóa thành công

### �� **2. TÌM KIẾM VÀ LỌC KHÁCH HÀNG**

#### 2.1. **search_customers**
- **Mô tả**: Tìm kiếm khách hàng theo nhiều tiêu chí
- **Chức năng**: Tìm kiếm theo tên, SĐT, email, địa chỉ
- **Input**: Search text, filters, pagination
- **Output**: Danh sách khách hàng phù hợp

#### 2.2. **get_customers_by_status**
- **Mô tả**: Lấy khách hàng theo trạng thái
- **Chức năng**: Lọc theo potential, caring, transacted, closed, paused
- **Input**: Status, pagination
- **Output**: Danh sách khách hàng theo trạng thái

#### 2.3. **get_customers_by_level**
- **Mô tả**: Lấy khách hàng theo cấp độ
- **Chức năng**: Lọc theo bronze, silver, gold, platinum, diamond
- **Input**: Level, pagination
- **Output**: Danh sách khách hàng theo cấp độ

#### 2.4. **get_customers_by_location**
- **Mô tả**: Lấy khách hàng theo địa điểm
- **Chức năng**: Lọc theo province, district, ward
- **Input**: Location filters, pagination
- **Output**: Danh sách khách hàng theo địa điểm

#### 2.5. **get_customers_by_date_range**
- **Mô tả**: Lấy khách hàng theo khoảng thời gian
- **Chức năng**: Lọc theo ngày tạo, ngày liên hệ cuối
- **Input**: Date range, pagination
- **Output**: Danh sách khách hàng theo thời gian

### ��️ **3. QUẢN LÝ TAGS VÀ PHÂN LOẠI**

#### 3.1. **assign_customer_tags**
- **Mô tả**: Gán tags cho khách hàng
- **Chức năng**: Thêm/bớt tags cho khách hàng
- **Input**: Customer ID, list of tags
- **Output**: Tags đã gán

#### 3.2. **get_customer_tags**
- **Mô tả**: Lấy tags của khách hàng
- **Chức năng**: Lấy danh sách tags đã gán
- **Input**: Customer ID
- **Output**: Danh sách tags

#### 3.3. **update_customer_status**
- **Mô tả**: Cập nhật trạng thái khách hàng
- **Chức năng**: Thay đổi trạng thái + lưu lịch sử
- **Input**: Customer ID, new status, reason
- **Output**: Trạng thái mới + lịch sử

#### 3.4. **get_customer_status_history**
- **Mô tả**: Lấy lịch sử thay đổi trạng thái
- **Chức năng**: Xem lịch sử các lần thay đổi trạng thái
- **Input**: Customer ID
- **Output**: Lịch sử thay đổi trạng thái

### 📝 **4. QUẢN LÝ GHI CHÚ VÀ HOẠT ĐỘNG**

#### 4.1. **add_customer_note**
- **Mô tả**: Thêm ghi chú cho khách hàng
- **Chức năng**: Tạo ghi chú mới
- **Input**: Customer ID, note content, note type
- **Output**: Note ID và thông tin

#### 4.2. **get_customer_notes**
- **Mô tả**: Lấy ghi chú của khách hàng
- **Chức năng**: Lấy danh sách ghi chú
- **Input**: Customer ID, pagination
- **Output**: Danh sách ghi chú

#### 4.3. **add_customer_activity**
- **Mô tả**: Thêm hoạt động cho khách hàng
- **Chức năng**: Ghi lại các hoạt động tương tác
- **Input**: Customer ID, activity type, description
- **Output**: Activity ID và thông tin

#### 4.4. **get_customer_activities**
- **Mô tả**: Lấy hoạt động của khách hàng
- **Chức năng**: Xem lịch sử hoạt động
- **Input**: Customer ID, pagination
- **Output**: Danh sách hoạt động

### 📞 **5. QUẢN LÝ LIÊN HỆ VÀ NHẮC NHỞ**

#### 5.1. **add_customer_contact**
- **Mô tả**: Thêm thông tin liên hệ
- **Chức năng**: Lưu thông tin liên hệ bổ sung
- **Input**: Customer ID, contact type, contact info
- **Output**: Contact ID và thông tin

#### 5.2. **get_customer_contacts**
- **Mô tả**: Lấy thông tin liên hệ
- **Chức năng**: Xem tất cả thông tin liên hệ
- **Input**: Customer ID
- **Output**: Danh sách thông tin liên hệ

#### 5.3. **add_customer_reminder**
- **Mô tả**: Thêm nhắc nhở cho khách hàng
- **Chức năng**: Tạo nhắc nhở liên hệ
- **Input**: Customer ID, reminder date, description
- **Output**: Reminder ID và thông tin

#### 5.4. **get_customer_reminders**
- **Mô tả**: Lấy nhắc nhở của khách hàng
- **Chức năng**: Xem danh sách nhắc nhở
- **Input**: Customer ID, status (pending/completed)
- **Output**: Danh sách nhắc nhở

### �� **6. THỐNG KÊ VÀ BÁO CÁO**

#### 6.1. **get_customer_statistics**
- **Mô tả**: Lấy thống kê khách hàng
- **Chức năng**: Thống kê theo trạng thái, cấp độ, địa điểm
- **Input**: Date range, filters
- **Output**: Thống kê tổng quan

#### 6.2. **get_customer_revenue_summary**
- **Mô tả**: Tổng hợp doanh thu khách hàng
- **Chức năng**: Tính tổng doanh thu, trung bình
- **Input**: Customer ID, date range
- **Output**: Thống kê doanh thu

#### 6.3. **get_customer_contact_summary**
- **Mô tả**: Tổng hợp tương tác với khách hàng
- **Chức năng**: Thống kê số lần liên hệ, hoạt động
- **Input**: Customer ID, date range
- **Output**: Thống kê tương tác

### 📁 **7. QUẢN LÝ TÀI LIỆU**

#### 7.1. **add_customer_document**
- **Mô tả**: Thêm tài liệu cho khách hàng
- **Chức năng**: Liên kết tài liệu với khách hàng
- **Input**: Customer ID, document ID, document type
- **Output**: Document assignment ID

#### 7.2. **get_customer_documents**
- **Mô tả**: Lấy tài liệu của khách hàng
- **Chức năng**: Xem danh sách tài liệu
- **Input**: Customer ID, document type
- **Output**: Danh sách tài liệu

### 🔄 **8. QUẢN LÝ PHÂN ĐOẠN KHÁCH HÀNG**

#### 8.1. **assign_customer_segment**
- **Mô tả**: Gán khách hàng vào phân đoạn
- **Chức năng**: Phân loại khách hàng theo segment
- **Input**: Customer ID, segment ID
- **Output**: Assignment ID

#### 8.2. **get_customer_segments**
- **Mô tả**: Lấy phân đoạn của khách hàng
- **Chức năng**: Xem khách hàng thuộc phân đoạn nào
- **Input**: Customer ID
- **Output**: Danh sách phân đoạn

---

## 📋 **TỔNG QUAN ENUM VALUES**

### **Customer Status (customer_status)**
- `potential`: Tiềm năng
- `caring`: Đang chăm sóc  
- `transacted`: Đã giao dịch
- `closed`: Đã chốt
- `paused`: Tạm dừng

### **Customer Level (customer_level)**
- `bronze`: Đồng
- `silver`: Bạc
- `gold`: Vàng
- `platinum`: Bạch kim
- `diamond`: Kim cương

### **Customer Source (customer_source)**
- `referral`: Giới thiệu
- `website`: Website
- `facebook_ads`: Quảng cáo Facebook
- `google_ads`: Quảng cáo Google
- `event`: Sự kiện
- `walk_in`: Đến trực tiếp
- `phone`: Điện thoại
- `email`: Email
- `other`: Khác

### **Marital Status (marital_status)**
- `SINGLE`: Độc thân
- `MARRIED`: Đã kết hôn
- `DIVORCED`: Đã ly hôn
- `WIDOWED`: Góa

### **ID Card Type (id_card_type)**
- `CHIP_ID`: CCCD gắn chip
- `PASSPORT`: Hộ chiếu

---

Bạn có muốn tôi bắt đầu implement chi tiết từng hàm nào trước không? Tôi đề xuất bắt đầu với các hàm cơ bản như `insert_customer`, `update_customer`, và `get_customer_detail`.