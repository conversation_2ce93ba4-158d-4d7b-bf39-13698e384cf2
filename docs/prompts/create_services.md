xây dựng các model và service trong `lib/core/article` theo file sau
`/Users/<USER>/Works/kilobabiz/kiloba_biz-backend/kilobabiz-postgREST/article/02_update_mobile_functions.sql`, tham kh<PERSON>o cách làm trong `lib/features/auth/services/registration_service.dart` về cách gọi các postgREST api function. Các model cần `@JsonSerializable(fieldRename: FieldRename.snake)` để phù hợp với dữ liệu của postgREST.

các enum:
```
CREATE TYPE articletype AS ENUM (
    'PRODUCT_INTRO',      -- <PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> sản phẩm
    'POLICY',             -- <PERSON><PERSON><PERSON> sách
    'PROMOTION',          -- Ch<PERSON>ơng trình khuyến mại
    'COMPETITION',        -- <PERSON>hi đua
    'NEWS',               -- <PERSON> tứ<PERSON>
    'GUIDELINE',          -- Hướng dẫn
    'ANNOUNCEMENT',       -- <PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>
    'TRAINING',           -- <PERSON><PERSON><PERSON>
    'TERMS_OF_SERVICE',   -- <PERSON><PERSON><PERSON><PERSON> sử dụng
    'PRIVACY_P<PERSON>ICY'      -- Ch<PERSON>h sách b<PERSON>o mật
);
```