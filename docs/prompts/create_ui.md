trong lib/core/article/services/article_service.dart có hàm getArticleByCode để lấy nội dung 1 bà<PERSON> viết, chúng ta có 2 mã, ứng với điều khoản sử dụng và chính sách bảo mật.
TERMS_OF_SERVICE_REGISTER_V1, PRIVACY_POLICY_REGISTER_V1.
Trong lib/features/auth/screens/steps/introduction_step.dart của màn hình đăng ký, chúng ta có link về chính sách bảo mật và điều khoản sử dụng, 
1) <PERSON><PERSON>y thiết kế 1 màn hình dùng chung để hiển thị nội dung markdown đọc được từ article (sử dụng markdown_widget đã có sẵn trong dự án).
2) trong  introduction_step thì khi người dùng bấm vào chính sách bảo mật hoặc điều khoản sử dụng thì hiển thị nội dung để người dùng đọc.
3) yêu cầu viết mã chuyên nghiệp như kỹ sử Google, giao diện UI/UX chuyên nghiệp, hộ trợ dark, light và tuân thủ theme của dự án.  Sử dụng MCP tool context7 để tìm hiểu về markdown_widget và cách dùng dark theme.
4) có thể cải tiến link trong introduction_step để dễ bấm, vì hiện tại link thì chỉ dễ bấm trên web chứ không dễ trên app.

Chưa viết mã, hãy phân tích bài toán.
- 