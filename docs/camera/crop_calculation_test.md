# Test Logic Crop với AppBar - Cậ<PERSON> nhật theo mask_for_camera_view

## Tình huống test
- <PERSON><PERSON><PERSON> hình: 440.0x956.0 (iPhone 14)
- Status bar: 62.0px
- Bottom safe area: 34.0px
- AppBar height: 56.0px
- Displayable height: 860.0px (956 - 62 - 34)
- Body area: 804.0px (860 - 56)
- Overlay position: Center của body area
- Overlay size: 400.0x252.0

## Phân tích hệ quy chiếu thực tế

### Vấn đề phát hiện:
- **Screen size**: 440.0x956.0 (toàn màn hình)
- **Thực tế có thể hiển thị**: 860.0px (trừ status bar và safe area)
- **Body area**: 804.0px (trừ thêm AppBar)
- **Overlay trước đây**: Tính toán dựa trên toàn màn hình → sai vị trí

### Giải pháp:
Sử dụng hệ quy chiếu đúng dựa trên body area thay vì toàn màn hình.

## Cách tiếp cận mới từ mask_for_camera_view

### 1. Thay đổi vị trí overlay
- **Trước**: Overlay ở center của toàn màn hình (956px)
- **Sau**: Overlay ở center của body area (804px)

### 2. Sử dụng getVisiblePortion
- Lấy phần ảnh tương ứng với màn hình hiển thị
- Đảm bảo crop chính xác theo vùng hiển thị thực tế

### 3. Cập nhật flutterToImageRect
- Sử dụng `ui.window.physicalSize` và `ui.window.devicePixelRatio`
- Công thức: `flutterRect * devicePixelRatio * imagePixelRatio`

## Các thay đổi đã thực hiện

### 1. ImageCropUtils
- Thêm method `getVisiblePortion()` để lấy phần ảnh hiển thị
- Cập nhật `_flutterToImageRect()` theo công thức của mask_for_camera_view
- Thêm method `_flutterToImageSize()` để chuyển đổi kích thước

### 2. CccdCameraScreen
- Thay đổi vị trí overlay từ full screen sang body area center-based
- Tính toán overlay ở center của body area (không bao gồm AppBar)
- Sử dụng body area cho crop calculation

## Logic crop mới
```dart
// Calculate correct coordinate system
final statusBarHeight = MediaQuery.of(context).padding.top; // 62px
final bottomSafeArea = MediaQuery.of(context).padding.bottom; // 34px
final appBarHeight = kToolbarHeight; // 56px

// Calculate actual displayable height and body area
final displayableHeight = screenSize.height - statusBarHeight - bottomSafeArea; // 860px
final bodyHeight = displayableHeight - appBarHeight; // 804px

// Overlay position (body area center-based)
final overlayWidth = screenSize.width - 40; // 400px
final overlayHeight = overlayWidth * 0.63; // 252px
final overlayLeft = (screenSize.width - overlayWidth) / 2; // 20px
final overlayTop = appBarHeight + (bodyHeight - overlayHeight) / 2; // 56 + 276 = 332px

// Get visible portion first
final visibleImage = await ImageCropUtils.getVisiblePortion(
  imagePath: imagePath,
  flutterScreenSize: screenSize,
);

// Then crop using flutterToImageRect with new formula
final imageRect = _flutterToImageRect(overlayRect, visibleImage);
```

## Kết quả mong đợi
- Camera preview không bị méo (có AppBar) ✅
- Crop ảnh chính xác theo vùng overlay ✅
- Sử dụng cách tiếp cận đã được chứng minh từ mask_for_camera_view ✅
- Tọa độ crop phù hợp với phần ảnh hiển thị thực tế ✅
- Overlay nằm đúng vị trí so với camera preview ✅ 