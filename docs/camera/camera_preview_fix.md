# Sửa lỗi Camera Preview bị méo trong CccdCameraScreen

## Vấn đề
Camera preview trong `CccdCameraScreen` bị méo vì hiển thị full screen (bao gồm cả status bar), khiến tỷ lệ khung hình không đúng với camera sensor.

## Nguyên nhân
- Sử dụng `extendBodyBehindAppBar: true` 
- Camera preview chiếm toàn bộ màn hình với `Positioned.fill`
- Không có AppBar để giữ tỷ lệ khung hình đúng

## Giải pháp
Thay đổi từ full screen sang hiển thị camera preview chỉ trong body area (giống như file `preview.md`):

### 1. Thêm AppBar
```dart
return Scaffold(
  backgroundColor: Colors.black,
  appBar: AppBar(
    backgroundColor: Colors.black,
    foregroundColor: Colors.white,
    title: Text('Chụp mặt trước/sau CCCD'),
    elevation: 0,
    leading: IconButton(
      onPressed: () => Navigator.of(context).pop(),
      icon: Icon(TablerIcons.arrow_left),
    ),
  ),
  body: _buildBody(isDarkMode),
);
```

### 2. Loại bỏ full screen overlay
- Xóa `extendBodyBehindAppBar: true`
- Xóa `AnnotatedRegion<SystemUiOverlayStyle>`
- Camera preview chỉ chiếm body area

### 3. Cập nhật vị trí overlay
- Thay đổi từ tính toán dựa trên toàn màn hình sang body area
- `overlayTop = screenSize.height * 0.15` thay vì tính toán phức tạp với status bar

### 4. Loại bỏ các element không cần thiết
- Xóa `_buildBackButton()` và `_buildTitleOverlay()`
- Xóa các overlay button và title vì đã có AppBar

## Kết quả
- Camera preview giữ đúng tỷ lệ khung hình
- Không bị méo ảnh
- UI gọn gàng với AppBar
- Tương tự như file `preview.md` mẫu

## Vấn đề phát sinh và giải pháp
Sau khi thêm AppBar, phần crop ảnh theo vùng overlay bị sai vì:
- Overlay được tính toán dựa trên body area (không bao gồm AppBar)
- Nhưng ảnh chụp vẫn có kích thước đầy đủ màn hình
- Cần điều chỉnh tọa độ overlay để phù hợp với ảnh đầy đủ

### Giải pháp crop:
1. **Tính toán body area**: `bodyHeight = screenSize.height - kToolbarHeight`
2. **Điều chỉnh tọa độ overlay**: `absoluteOverlayTop = overlayTop + bodyTop`
3. **Cập nhật ImageCropUtils**: Thêm tham số `bodyArea` để hỗ trợ tính toán chính xác

## Files thay đổi
- `lib/features/auth/widgets/cccd_camera_screen.dart`
- `lib/shared/utils/image_crop_utils.dart` 