Usage

Creating an InputImage

From path:

final inputImage = InputImage.fromFilePath(filePath);
From file:

final inputImage = InputImage.fromFile(file);
From bytes:

final inputImage = InputImage.fromBytes(bytes: bytes, metadata: metadata);
from bitmap data:

final ui.Image image = await recorder.endRecording().toImage(width, height);
final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.rawRgba);
final InputImage inputImage = InputImage.fromBitmap(
  bitmap: byteData!.buffer.asUint8List(),
  width: width,
  height: height,
  rotation: 0, // optional, defaults to 0, only used on Android
);
If you are using the Camera plugin make sure to configure your CameraController to only use ImageFormatGroup.nv21 for Android and ImageFormatGroup.bgra8888 for iOS.

Notice that the image rotation is computed in a different way for both iOS and Android. Image rotation is used in Android to convert the InputImage from Dart to Java, but it is not used in iOS to convert from Dart to Obj-C. However, image rotation and camera.lensDirection can be used in both platforms to compensate x and y coordinates on a canvas.

import 'dart:io';

import 'package:camera/camera.dart';
import 'package:google_mlkit_commons/google_mlkit_commons.dart';
import 'package:flutter/services.dart';

final camera; // your camera instance
final controller = CameraController(
  camera,
  ResolutionPreset.max,
  enableAudio: false,
  imageFormatGroup: Platform.isAndroid
          ? ImageFormatGroup.nv21 // for Android
          : ImageFormatGroup.bgra8888, // for iOS
);

final _orientations = {
  DeviceOrientation.portraitUp: 0,
  DeviceOrientation.landscapeLeft: 90,
  DeviceOrientation.portraitDown: 180,
  DeviceOrientation.landscapeRight: 270,
};

InputImage? _inputImageFromCameraImage(CameraImage image) {
  // get image rotation
  // it is used in android to convert the InputImage from Dart to Java
  // `rotation` is not used in iOS to convert the InputImage from Dart to Obj-C
  // in both platforms `rotation` and `camera.lensDirection` can be used to compensate `x` and `y` coordinates on a canvas
  final camera = _cameras[_cameraIndex];
  final sensorOrientation = camera.sensorOrientation;
  InputImageRotation? rotation;
  if (Platform.isIOS) {
    rotation = InputImageRotationValue.fromRawValue(sensorOrientation);
  } else if (Platform.isAndroid) {
    var rotationCompensation =
        _orientations[_controller!.value.deviceOrientation];
    if (rotationCompensation == null) return null;
    if (camera.lensDirection == CameraLensDirection.front) {
      // front-facing
      rotationCompensation = (sensorOrientation + rotationCompensation) % 360;
    } else {
      // back-facing
      rotationCompensation =
          (sensorOrientation - rotationCompensation + 360) % 360;
    }
    rotation = InputImageRotationValue.fromRawValue(rotationCompensation);
  }
  if (rotation == null) return null;

  // get image format
  final format = InputImageFormatValue.fromRawValue(image.format.raw);
  // validate format depending on platform
  // only supported formats:
  // * nv21 for Android
  // * bgra8888 for iOS
  if (format == null ||
          (Platform.isAndroid && format != InputImageFormat.nv21) ||
          (Platform.isIOS && format != InputImageFormat.bgra8888)) return null;

  // since format is constraint to nv21 or bgra8888, both only have one plane
  if (image.planes.length != 1) return null;
  final plane = image.planes.first;

  // compose InputImage using bytes
  return InputImage.fromBytes(
    bytes: plane.bytes,
    metadata: InputImageMetadata(
      size: Size(image.width.toDouble(), image.height.toDouble()),
      rotation: rotation, // used only in Android
      format: format, // used only in iOS
      bytesPerRow: plane.bytesPerRow, // used only in iOS
    ),
  );
}

CameraImage image; // your image from camera/controller image stream
final inputImage = _inputImageFromCameraImage(image);