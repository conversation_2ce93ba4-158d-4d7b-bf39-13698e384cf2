# Phương Án Tích Hợp Notification Service vào UI

## 🎯 Mục tiêu

Tích hợp `NotificationService` vào màn hình `NotificationsTab` với:
- ✅ Bloc pattern cho state management
- ✅ Pull-to-refresh functionality
- ✅ FCM push notification handling
- ✅ Badge count chưa đọc ở bottom tab bar
- ✅ Responsive UI với loading states
- ✅ Error handling graceful

## 🏗️ Kiến Trúc Tổng Thể

```
📱 NotificationsTab (StatefulWidget)
├── 🧠 NotificationBloc (State Management)
├── 🔄 RefreshIndicator (Pull-to-refresh)
├── 📊 NotificationSummaryCard (Widget Component)
├── 🏷️ NotificationCategoriesGrid (Widget Component)
├── 📋 RecentNotificationsList (Widget Component)
└── 🔔 FCM Handler (Background Service)
```

## 🧠 Bloc Pattern Implementation

### NotificationBloc Events
```dart
abstract class NotificationEvent {}

class LoadNotificationData extends NotificationEvent {}
class RefreshNotificationData extends NotificationEvent {}
class MarkNotificationAsRead extends NotificationEvent {
  final String notificationId;
}
class MarkAllNotificationsAsRead extends NotificationEvent {
  final String? category;
}
class CategoryFilterChanged extends NotificationEvent {
  final String? category;
}
class FCMNotificationReceived extends NotificationEvent {
  final Map<String, dynamic> payload;
}
```

### NotificationBloc States
```dart
abstract class NotificationState {}

class NotificationInitial extends NotificationState {}
class NotificationLoading extends NotificationState {}
class NotificationLoaded extends NotificationState {
  final NotificationSummary? summary;
  final List<NotificationCategory> categories;
  final List<NotificationDetail> recentNotifications;
  final bool isRefreshing;
}
class NotificationError extends NotificationState {
  final String message;
  final NotificationErrorType type;
}
```

## 📱 Widget Components Breakdown

### 1. 📊 NotificationSummaryCard
**Mục đích**: Hiển thị tóm tắt thống kê thông báo
**Data**: `NotificationSummary` từ `getNotificationSummary()`

```dart
class NotificationSummaryCard extends StatelessWidget {
  final NotificationSummary? summary;
  final bool isLoading;
  
  // UI Features:
  // - Gradient background với brand colors
  // - Skeleton loading animation
  // - Responsive text với số liệu động
  // - Icon bell với animation nếu có unread
}
```

### 2. 🏷️ NotificationCategoriesGrid
**Mục đích**: Hiển thị grid các category với count
**Data**: `List<NotificationCategory>` từ `getNotificationCategories()`

```dart
class NotificationCategoriesGrid extends StatelessWidget {
  final List<NotificationCategory> categories;
  final bool isLoading;
  final Function(String category) onCategoryTap;
  
  // UI Features:
  // - 3 columns responsive grid
  // - Category cards với icon, count, tên
  // - Shimmer loading cho từng card
  // - Badge hiển thị unread count
  // - Tap handling để filter
}
```

### 3. 📋 RecentNotificationsList
**Mục đích**: Hiển thị danh sách thông báo gần đây
**Data**: `List<NotificationDetail>` từ `getRecentNotifications()`

```dart
class RecentNotificationsList extends StatelessWidget {
  final List<NotificationDetail> notifications;
  final bool isLoading;
  final Function(NotificationDetail) onNotificationTap;
  final Function(String) onMarkAsRead;
  
  // UI Features:
  // - ListView với NotificationListItem
  // - Swipe actions (mark as read, delete)
  // - Visual distinction cho read/unread
  // - Time ago formatting
  // - Action buttons nếu isActionable
}
```

### 4. 🔔 NotificationListItem
**Mục đích**: Single notification item component
**Data**: `NotificationDetail`

```dart
class NotificationListItem extends StatelessWidget {
  final NotificationDetail notification;
  final Function() onTap;
  final Function() onMarkAsRead;
  
  // UI Features:
  // - Icon theo notification type
  // - Title, message truncated
  // - Time ago display
  // - Read/unread visual state
  // - Action button nếu có
  // - Swipe gesture support
}
```

## 🔄 Pull-to-Refresh Implementation

```dart
RefreshIndicator(
  onRefresh: () async {
    context.read<NotificationBloc>().add(RefreshNotificationData());
    // Wait for completion
    await Future.delayed(Duration(milliseconds: 500));
  },
  child: CustomScrollView(...),
)
```

## 🔔 FCM Integration Strategy

### 1. FCM Message Handler
```dart
class FCMNotificationHandler {
  static void initialize() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);
    
    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }
  
  static void _handleForegroundMessage(RemoteMessage message) {
    // Update notification count
    // Show in-app notification
    // Trigger bloc refresh
  }
}
```

### 2. Badge Count Management
```dart
class NotificationBadgeCubit extends Cubit<int> {
  final NotificationService _notificationService;
  
  Future<void> loadBadgeCount() async {
    final summary = await _notificationService.getNotificationSummary();
    emit(summary?.unreadCount ?? 0);
  }
  
  void incrementBadge() => emit(state + 1);
  void decrementBadge() => emit(state > 0 ? state - 1 : 0);
  void clearBadge() => emit(0);
}
```

## 📐 UI/UX Design Specifications

### Trang Chủ Notification Layout

```
┌─────────────────────────────────────┐
│ 📱 App Header                      ⚙️│
├─────────────────────────────────────┤
│ 🔄 Pull to Refresh                  │
├─────────────────────────────────────┤
│ 📊 Summary Card                     │
│ ┌─────────────────────────────────┐ │
│ │ 🔔 Thông báo hôm nay            │ │
│ │ 📈 3 mới • 12 chưa đọc         │ │
│ │                           [📱] │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 🏷️ Categories (3 columns)          │
│ ┌─────┐ ┌─────┐ ┌─────┐           │
│ │ 🎯  │ │ 💰  │ │ ⚙️  │           │
│ │ 12  │ │ 5   │ │ 3   │           │
│ │Chỉ  │ │Hoa  │ │Hệ   │           │
│ │tiêu │ │hồng │ │thống│           │
│ └─────┘ └─────┘ └─────┘           │
├─────────────────────────────────────┤
│ 📋 Recent Notifications             │
│ ┌─────────────────────────────────┐ │
│ │ 🎯 Chỉ tiêu tháng 12...    •   │ │
│ │    Bạn đã hoàn thành 75%...    │ │
│ │    2 giờ trước              📄 │ │
│ └─────────────────────────────────┘ │
│ ┌─────────────────────────────────┐ │
│ │ 💳 Chương trình khuyến mãi...   │ │
│ │    Thẻ tín dụng Kiloba...       │ │
│ │    4 giờ trước                  │ │
│ └─────────────────────────────────┘ │
│                    👆 Xem tất cả   │
└─────────────────────────────────────┘
```

### Loading States
- **Skeleton Loading**: Summary card, category cards, notification items
- **Shimmer Effect**: Content areas đang load
- **Refresh Indicator**: Pull-to-refresh animation
- **Empty States**: Khi không có thông báo

### Error States
- **Network Error**: Retry button + offline message
- **API Error**: Error message với retry option
- **No Data**: Empty state với illustration

## 🎨 Theme Integration

### Colors
- **Unread**: `AppColors.kienlongOrange` border + bold text
- **Read**: Normal border + regular text
- **Important**: Red accent dot
- **Categories**: Brand color coding theo type

### Typography
- **Title**: `titleMedium` bold cho unread, regular cho read
- **Message**: `bodyMedium` với truncation
- **Time**: `bodySmall` muted color
- **Counts**: `headlineSmall` bold với brand colors

## 🔄 State Flow Diagram

```
[App Start] → [LoadNotificationData] → [NotificationLoading]
     ↓
[API Calls] → [NotificationLoaded] ← [RefreshNotificationData]
     ↓                ↑
[Error] → [NotificationError] → [Retry]
     ↓                ↑
[FCM Message] → [FCMNotificationReceived] → [Auto Refresh]
     ↓
[Mark as Read] → [Update Local State] → [Sync API]
```

## 📱 Bottom Tab Badge Integration

### BadgeNotificationTab Widget
```dart
class BadgeNotificationTab extends StatelessWidget {
  Widget build(BuildContext context) {
    return BlocBuilder<NotificationBadgeCubit, int>(
      builder: (context, badgeCount) {
        return Badge(
          isLabelVisible: badgeCount > 0,
          label: Text(badgeCount > 99 ? '99+' : '$badgeCount'),
          child: Icon(TablerIcons.bell),
        );
      },
    );
  }
}
```

## 🚀 Implementation Plan

### Phase 1: Core Structure (Week 1)
1. ✅ Tạo NotificationBloc + Events/States
2. ✅ Implement basic UI integration
3. ✅ Add pull-to-refresh functionality

### Phase 2: Components (Week 2)
1. ✅ Break down thành widget components
2. ✅ Add loading/error states
3. ✅ Implement mark as read functionality

### Phase 3: Advanced Features (Week 3)
1. ✅ FCM integration
2. ✅ Badge count management
3. ✅ Real-time updates
4. ✅ Performance optimization

### Phase 4: Polish (Week 4)
1. ✅ Animations và micro-interactions
2. ✅ Error handling improvements
3. ✅ Testing và bug fixes
4. ✅ Documentation

## 🧪 Testing Strategy

### Unit Tests
- NotificationBloc logic
- Widget component rendering
- State transitions

### Integration Tests
- API service integration
- FCM message handling
- Badge count updates

### UI Tests
- Pull-to-refresh functionality
- Navigation flows
- Error state handling

## 📊 Performance Considerations

### Optimization
- ✅ Lazy loading cho notification list
- ✅ Image caching cho notification icons
- ✅ Debouncing cho API calls
- ✅ Background sync với WorkManager

### Memory Management
- ✅ Dispose bloc streams properly
- ✅ Cache management với size limits
- ✅ Image memory optimization

## 🔐 Security & Privacy

### Data Handling
- ✅ Secure token storage
- ✅ Encrypted notification data
- ✅ User permission for notifications

### Privacy
- ✅ Notification settings control
- ✅ Data retention policies
- ✅ User consent management