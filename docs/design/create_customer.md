Dựa trên tài liệu và cấu trúc hiện tại, tôi đề xuất ý tưởng thiết kế màn hình **Thêm mới khách hàng** như sau:

## 🎨 **Thiết kế Tổng thể**

### **1. Layout Structure**
```
📱 Add Customer Screen
├── 🏠 App Header (Brand gradient + Back button)
├── 📝 Multi-step Form (Wizard style)
├── 💾 Bottom Action Bar (Save/Next buttons)
└── 🔄 Progress Indicator
```

### **2. Multi-Step Wizard Flow**

#### **Step 1: Thông tin cơ bản** 
- **Header:** "Thông tin khách hàng" với progress indicator (1/4)
- **Fields:**
  - Họ và tên* (required)
  - Số điện thoại* (format validation)
  - Email (optional)
  - G<PERSON><PERSON><PERSON> tính (dropdown: Nam/Nữ/Khác)
  - <PERSON><PERSON><PERSON><PERSON> (date picker)
- **Bottom Actions:** "Tiếp tục" (orange button)

#### **Step 2: Địa chỉ & <PERSON>ê<PERSON> hệ**
- **Header:** "Thông tin liên hệ" (2/4)
- **Fields:**
  - Đ<PERSON>a chỉ thường trú*
  - Tỉnh/Thành phố* (dropdown)
  - Quận/Huyện* (dropdown cascade)
  - Phường/Xã* (dropdown cascade)
  - Địa chỉ hiện tại (checkbox "Giống thường trú")
- **Bottom Actions:** "Quay lại" (outline) + "Tiếp tục" (orange)

#### **Step 3: Thông tin nghề nghiệp**
- **Header:** "Thông tin công việc" (3/4)
- **Fields:**
  - Nghề nghiệp/Công việc*
  - Nơi làm việc
  - Thu nhập hàng tháng (number input với format VND)
  - Kinh nghiệm làm việc (dropdown: <1 năm, 1-3 năm, >3 năm)
- **Bottom Actions:** "Quay lại" + "Tiếp tục"

#### **Step 4: Phân loại & Tags**
- **Header:** "Phân loại khách hàng" (4/4)
- **Fields:**
  - Trạng thái lead* (Radio buttons):
    - 🟡 Tiềm năng
    - 🔵 Đang chăm sóc  
    - 🟢 Đã giao dịch
  - Nguồn khách hàng* (dropdown):
    - Giới thiệu
    - Website
    - Quảng cáo
    - Walk-in
    - Khác
  - Tags (Multi-select chips):
    - VIP, Premium, Mới, Ưu tiên, etc.
  - Ghi chú (text area)
- **Bottom Actions:** "Quay lại" + "Lưu khách hàng" (orange)

## 🎯 **UX Experience Flow**

### **Progressive Disclosure**
- Chia form phức tạp thành 4 bước đơn giản
- Mỗi step focus vào 1 nhóm thông tin cụ thể
- Progress bar ở top để user biết vị trí hiện tại

### **Smart Defaults & Validation**
- Auto-format số điện thoại (0xxx-xxx-xxx)
- Cascade dropdown cho địa chỉ (Tỉnh → Quận → Phường)
- Real-time validation với error states
- Required fields được đánh dấu rõ ràng

### **Quick Actions**
- "Quét QR" để auto-fill từ CCCD/CMND
- "Import từ danh bạ" để lấy contact
- "Sao chép từ khách hàng khác" template

## 🎨 **Visual Design**

### **Header Design**
```
┌─────────────────────────────────────┐
│ ← Thêm khách hàng mới              │ ← Brand gradient
│   ●●●○ (Progress: 3/4)             │
└─────────────────────────────────────┘
```

### **Form Styling**
- **Cards:** White background với subtle shadow
- **Input Fields:** Material 3 outlined style
- **Colors:** Orange cho primary actions, Sky Blue cho secondary
- **Typography:** Tuân theo app_typography.dart

### **Bottom Action Bar**
```
┌─────────────────────────────────────┐
│  [Quay lại]     [Tiếp tục/Lưu] →   │ ← Sticky bottom
└─────────────────────────────────────┘
```

## 📱 **Component Architecture**

### **Proposed File Structure**
```
lib/features/customers/screens/
├── add_customer_screen.dart           # Main wizard container
└── steps/
    ├── basic_info_step.dart          # Step 1
    ├── address_info_step.dart        # Step 2  
    ├── career_info_step.dart         # Step 3
    └── classification_step.dart      # Step 4

lib/features/customers/widgets/
├── add_customer_header.dart          # Progress header
├── customer_form_field.dart          # Reusable input
├── address_selector.dart             # Cascade dropdown
├── tag_selector.dart                 # Multi-select chips
└── step_navigation_bar.dart          # Bottom actions
```

## 🔄 **Interactive Behaviors**

### **Navigation Flow**
- Swipe gestures để chuyển step (optional)
- Validation ngăn chặn next step nếu required fields empty
- Auto-save draft khi user thoát giữa chừng
- Confirm dialog khi user muốn hủy bỏ

### **Success Flow**
```
Save Success
    ↓
✅ Toast: "Đã thêm khách hàng thành công"
    ↓
Navigate to CustomerDetailScreen (newly created)
    ↓
Show action options: "Tạo giao dịch" hoặc "Thêm khách hàng khác"
```

## 🎯 **Business Logic Integration**

### **Data Model**
- Tương thích với mock data structure hiện tại
- Thêm fields: `dateAdded`, `source`, `notes`
- Auto-generate `customerId` và timestamps

### **Validation Rules**
- Phone: Vietnamese format validation
- Email: RFC compliant
- Required fields: Name, Phone, Address, Status
- Business rules: VIP tag chỉ cho khách hàng có thu nhập >50M

Thiết kế này đảm bảo:
✅ **Consistent** với brand identity và UX patterns hiện tại  
✅ **Professional** phù hợp với ngành ngân hàng  
✅ **User-friendly** với progressive disclosure  
✅ **Scalable** dễ maintain và extend

Bạn có muốn tôi detail hơn về bất kỳ phần nào không?