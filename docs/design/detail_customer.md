Tôi sẽ lên ý tưởng cho giao diện Chi tiết khách hàng dựa trên yêu cầu và design system hiện tại. Trướ<PERSON> tiên, hãy tôi xem thêm theme documentation để hiểu rõ design patterns.
Read file: lib/core/theme/README.md

Tôi sẽ lên ý tưởng cho giao diện **Chi tiết khách hàng** dựa trên yêu cầu nghiệp vụ và design system hiện tại. Đây sẽ là một màn hình toàn diện với UX banking chuyên nghiệp.

## 🎨 **Customer Detail Screen - Concept Design**

### **1. Header Section (Thông tin tổng quan)**
- **AppNavHeader** với gradient brand + nút back
- **Customer Avatar** hình tròn lớn với initial letters hoặc ảnh
- **Customer Name** - Typography `headlineLarge` 
- **Customer Tags** - Chips với màu phân biệt (VIP: gold, Premium: sky blue, Mới: orange)
- **Status Badge** - Pills với màu banking theme (Đã chốt: green, Tiềm năng: orange, etc.)

### **2. Quick Stats Cards (Cards thống kê nhanh)**
```
🏦 Tổng doanh số    📞 Cuộc gọi     📧 Tin nhắn    📋 Giao dịch
   25M VND           12 lần          8 lần         5 hồ sơ
```
- 4 cards ngang với `BankingTheme.accountCardDecoration`
- Icons với `AppColors.kienlongOrange`
- Numbers với `AppTypography.accountBalanceLarge`

### **3. Quick Actions Row (Hành động nhanh)**
- **Row of 4 circular buttons:**
  - 📞 **Gọi điện** (primary action - orange)
  - 💬 **Nhắn tin** (sky blue)
  - 📝 **Ghi chú** (gray)
  - ⚙️ **Chỉnh sửa** (gray)
- Buttons với `AppDimensions.quickActionButtonSize`

### **4. Tabbed Content (Nội dung theo tabs)**

#### **Tab 1: 📋 Thông tin cơ bản**
- **Contact Info Card:**
  - 📱 Số điện thoại (có thể tap để gọi)
  - 📧 Email (nếu có)
  - 🏠 Địa chỉ 
  - 🌍 Khu vực: HCM/HN/ĐN

- **Customer Profile Card:**
  - 👤 Loại khách hàng
  - 💰 Mức thu nhập dự kiến
  - 🎯 Nhu cầu sản phẩm
  - 📅 Ngày tạo hồ sơ

#### **Tab 2: 💬 Nhật ký chăm sóc**
- **Timeline Design** với:
  - 📅 **Date stamps** theo chronological order
  - 💬 **Activity types:** Gọi điện, Nhắn tin, Gặp mặt, Email
  - 📝 **Notes content** 
  - 👤 **Staff name** đã thực hiện
  - ⏰ **Duration** (for calls/meetings)

- **Add New Activity FAB** (Orange, bottom right)

#### **Tab 3: 🧾 Giao dịch**
- **Transaction List** với `BankingTheme.transactionItemPadding`:
  - 🏦 **Product type** (Vay, Gửi, Thẻ tín dụng)
  - 💰 **Amount** với `BankingTheme.getAmountTextStyle()`
  - 📊 **Status** với status colors
  - 📅 **Date created**
  - 🔗 **Tap to view detail**

- **Create New Transaction FAB**

#### **Tab 4: 🏷️ Tags & Status**
- **Current Tags Section:**
  - Display current tags as chips
  - ➕ **Add new tag button**
  - ✏️ **Edit existing tags**

- **Lead Status Management:**
  - **Current status** with large status badge
  - **Status History** mini timeline
  - **Change Status** dropdown với banking colors

### **5. Bottom Actions (Sticky bottom)**
- **2 main action buttons:**
  - 🆕 **Tạo giao dịch mới** (Primary button - Orange)
  - 📞 **Gọi ngay** (Secondary button - Sky Blue)

---

## 🎯 **UX Flow & Interactions**

### **Navigation Flow:**
```
Customers List → [Tap customer] → Customer Detail
                                     ↓
Customer Detail → [Quick actions] → Call/SMS/Edit
                → [Tabs] → Info/Timeline/Transactions/Tags
                → [Bottom actions] → New Transaction/Call
```

### **Key Interactions:**
1. **Swipe between tabs** - smooth animation
2. **Pull to refresh** - reload customer data
3. **Long press on transaction** - context menu
4. **Tap phone number** - direct call
5. **Tap activity in timeline** - expand details

### **Data Display Patterns:**
- **Empty states** cho từng tab nếu không có dữ liệu
- **Skeleton loading** khi fetch data
- **Error handling** với retry buttons
- **Offline mode** indication

---

## 📱 **Responsive Considerations**

- **Mobile first:** Single column layout
- **Tablet:** Có thể split content thành 2 columns
- **Scroll behavior:** Sticky tabs, collapsible header
- **Safe areas:** Proper padding cho iPhone notch

---

## 🎨 **Visual Hierarchy**

1. **Level 1:** Customer name + avatar (most prominent)
2. **Level 2:** Status badge + stats cards  
3. **Level 3:** Tab content
4. **Level 4:** Bottom actions (always visible)

### **Color Usage:**
- **Orange** - Primary actions, active states
- **Sky Blue** - Secondary features, information
- **Dark Blue** - Headers, important text
- **Banking colors** - Transaction statuses, amounts
- **Gray scale** - Supporting info, disabled states

---

## 🔧 **Technical Implementation Notes**

### **Suggested File Structure:**
```
features/customers/
├── screens/
│   ├── customer_detail_screen.dart
│   └── customers_tab.dart
├── widgets/
│   ├── customer_detail_header.dart
│   ├── customer_stats_cards.dart
│   ├── customer_quick_actions.dart
│   ├── customer_info_tab.dart
│   ├── customer_timeline_tab.dart
│   ├── customer_transactions_tab.dart
│   ├── customer_tags_tab.dart
│   └── activity_timeline_item.dart
```

### **State Management:**
- Customer detail data
- Tab state management  
- Activity timeline state
- Form states for editing

### **Navigation:**
- Pass customer ID từ list screen
- Handle back navigation với data changes
- Deep linking support cho customer detail

---

Concept này tạo ra một **customer detail screen chuyên nghiệp** tuân thủ hoàn toàn design system hiện tại, với UX banking tối ưu và đầy đủ chức năng quản lý khách hàng như yêu cầu. Bạn có muốn tôi elaborate thêm về bất kỳ section nào không?