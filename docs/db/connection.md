# Database Connection Information

## PostgreSQL Database Configuration

### Connection Parameters
- **Database Name**: `kilobabiz`
- **Username**: `postgres`
- **Password**: `postgres`
- **Host**: `localhost` (when accessing from host machine)
- **Port**: `5432`
- **Docker Container Name**: `kilobabiz-postgres`

### Connection Examples

#### JDBC URL
```
******************************************
```

#### psql Command Line
```bash
psql -h localhost -p 5432 -U postgres -d kilobabiz
```

#### Docker exec into container
```bash
docker exec -it kilobabiz-postgres psql -U postgres -d kilobabiz
```

#### Connection String Format
```
postgresql://postgres:postgres@localhost:5432/kilobabiz
```

### Environment Variables Format
```bash
DB_HOST=localhost
DB_PORT=5432
DB_NAME=kilobabiz
DB_USER=postgres
DB_PASSWORD=postgres
```

### Spring Boot application.yml Format
```yaml
spring:
  datasource:
    url: ******************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
```

### Docker Network Connection
If connecting from within Docker network:
- **Host**: `kilobabiz-postgres` (container name)
- **Connection String**: `******************************************************/kilobabiz`

---

## 🚀 Practical Usage Guide

### Quick Commands for AI Assistant

#### 1. Check Database Connection
```bash
# Test connection
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT version();"

# List all databases
docker exec kilobabiz-postgres psql -U postgres -c "\l"

# Check current database
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT current_database();"
```

#### 2. Database Structure Investigation
```bash
# List all schemas
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "\dn"

# List all tables in public schema
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "\dt public.*"

# List all tables across schemas
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT schemaname, tablename FROM pg_tables WHERE schemaname NOT IN ('information_schema', 'pg_catalog');"

# Describe specific table structure
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "\d public.users"
```

#### 3. Running SQL Scripts
```bash
# Run SQL script from file
docker exec -i kilobabiz-postgres psql -U postgres -d kilobabiz -f - < docs/db/design/users_table.sql

# Run single SQL command
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT * FROM public.users LIMIT 5;"

# Run multiple commands (multiline)
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
ORDER BY table_name, ordinal_position;
"
```

#### 4. Data Inspection and Testing
```bash
# Count records in table
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT COUNT(*) FROM public.users;"

# Show sample data
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT id, username, email, roles, status FROM public.users LIMIT 5;"

# Check indexes on table
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT indexname, indexdef FROM pg_indexes WHERE tablename = 'users' AND schemaname = 'public';"

# Show table sizes
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables WHERE schemaname = 'public';"
```

#### 5. Interactive vs Non-Interactive Mode
```bash
# Non-interactive (for scripts/automation) - PREFERRED for AI
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "COMMAND_HERE"

# Interactive mode (manual use)
docker exec -it kilobabiz-postgres psql -U postgres -d kilobabiz
# Type \q to exit interactive mode
```

---

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Container Not Running
```bash
# Check if container is running
docker ps | grep kilobabiz-postgres

# Start container if stopped
docker start kilobabiz-postgres

# Check container logs
docker logs kilobabiz-postgres
```

#### Connection Refused
```bash
# Check if port is accessible
nc -zv localhost 5432

# Check container network
docker inspect kilobabiz-postgres | grep IPAddress
```

#### Permission Denied
```bash
# Check if user has access
docker exec kilobabiz-postgres psql -U postgres -c "\du"

# Grant permissions if needed
docker exec kilobabiz-postgres psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE kilobabiz TO postgres;"
```

---

## 📝 AI Usage Notes

### Best Practices for AI Commands

1. **Always use non-interactive mode** with `-c` flag for single commands
2. **Use `-f -` with stdin** for running script files: `< script.sql`
3. **Escape quotes properly** in complex queries: `\"` for inner quotes
4. **Use RETURNING clause** to verify INSERT/UPDATE operations
5. **Always check connection** before running complex operations

### Command Patterns

```bash
# Pattern 1: Simple query
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "SIMPLE_QUERY"

# Pattern 2: Complex query with escaping
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "
SELECT column1, column2 
FROM table_name 
WHERE condition = 'value'
LIMIT 10;
"

# Pattern 3: Script execution
docker exec -i kilobabiz-postgres psql -U postgres -d kilobabiz -f - < path/to/script.sql

# Pattern 4: With output formatting
docker exec kilobabiz-postgres psql -U postgres -d kilobabiz -c "\x" -c "SELECT * FROM users WHERE id = '...';"
```

### Safety Reminders

- 🚨 **Always backup before destructive operations**
- 🔒 **Use transactions for multiple related changes**  
- 📊 **Test queries with LIMIT first**
- ✅ **Verify results with SELECT after INSERT/UPDATE**

---

## 🔗 Quick Reference Links

- **Project Database Scripts**: `docs/db/design/`
- **Users Table Script**: `docs/db/design/users_table.sql`
- **Schema Documentation**: `docs/db/design/README.md`