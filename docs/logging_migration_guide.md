# Logging Migration Guide

## Tổng quan

Dự án đã có sẵn `logger` package và được sử dụng rộng rãi. <PERSON><PERSON>, để có logging thống nhất và an toàn hơn, chúng ta đã tạo `AppLogger` wrapper. Hướng dẫn này sẽ giúp bạn migrate từ `Logger()` trực tiế<PERSON> sang `AppLogger`.

## Lợi ích của AppLogger

1. **Thống nhất**: Interface logging thống nhất cho toàn bộ app
2. **An toàn**: Tự động mask thông tin nhạy cảm
3. **Cấu hình**: Log level được quản lý tập trung
4. **Performance**: Tối ưu cho production builds
5. **Security**: Không log thông tin nhạy cảm

## Migration Steps

### Bước 1: Import AppLogger

Thay thế:
```dart
import 'package:logger/logger.dart';
```

Bằng:
```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';
```

### Bước 2: Thay thế Logger() instance

Thay thế:
```dart
final logger = Logger();
final Logger _logger = Logger();
```

Bằng:
```dart
// Sử dụng global instance
// Không cần tạo instance mới
```

### Bước 3: Cập nhật logging calls

Thay thế:
```dart
logger.i('Message');
_logger.e('Error', error: exception);
```

Bằng:
```dart
appLogger.i('Message');
appLogger.e('Error', error: exception);
```

## Files cần migration

### High Priority (Core Services)
- [ ] `lib/shared/services/api_service.dart`
- [ ] `lib/shared/services/auth_service.dart`
- [ ] `lib/shared/services/token_manager.dart`
- [ ] `lib/shared/services/firebase_service.dart`
- [ ] `lib/shared/services/global_services.dart`

### Medium Priority (Feature Services)
- [ ] `lib/features/auth/services/user_profile_service.dart`
- [ ] `lib/features/auth/repositories/auth_repository.dart`
- [ ] `lib/features/auth/blocs/login_bloc.dart`
- [ ] `lib/features/dashboard/services/dashboard_stats_service.dart`
- [ ] `lib/features/notifications/repositories/local_notification_repository.dart`

### Low Priority (Other Services)
- [ ] `lib/shared/services/device_info_service.dart`
- [ ] `lib/shared/services/navigation_service.dart`
- [ ] `lib/shared/services/performance_monitor.dart`
- [ ] `lib/shared/services/weather_service.dart`
- [ ] `lib/shared/database/database_helper.dart`

## Migration Examples

### Example 1: Service Class

**Before:**
```dart
import 'package:logger/logger.dart';

class UserService {
  final Logger _logger = Logger();

  Future<User> getUser(String id) async {
    try {
      _logger.i('Fetching user: $id');
      // ... code
    } catch (e) {
      _logger.e('Error fetching user', error: e);
      rethrow;
    }
  }
}
```

**After:**
```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

class UserService {
  Future<User> getUser(String id) async {
    try {
      appLogger.i('Fetching user: $id');
      // ... code
    } catch (e) {
      appLogger.e('Error fetching user', error: e);
      rethrow;
    }
  }
}
```

### Example 2: Bloc Class

**Before:**
```dart
import 'package:logger/logger.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final Logger _logger = Logger();

  AuthBloc() : super(AuthInitial()) {
    on<LoginRequested>((event, emit) async {
      _logger.i('Login requested');
      // ... code
    });
  }
}
```

**After:**
```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(AuthInitial()) {
    on<LoginRequested>((event, emit) async {
      appLogger.i('Login requested');
      // ... code
    });
  }
}
```

### Example 3: Widget Class

**Before:**
```dart
import 'package:logger/logger.dart';

class LoginScreen extends StatefulWidget {
  final Logger _logger = Logger();

  @override
  Widget build(BuildContext context) {
    _logger.i('Login screen built');
    // ... code
  }
}
```

**After:**
```dart
import 'package:kiloba_biz/shared/utils/app_logger.dart';

class LoginScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    appLogger.i('Login screen built');
    // ... code
  }
}
```

## Advanced Usage

### Log với tag
```dart
appLogger.logWithTag('Auth', 'User logged in');
appLogger.logWithTag('API', 'Request failed', LogLevel.error);
```

### Log API operations
```dart
appLogger.logApiRequest('POST', '/api/login', headers: headers, data: data);
appLogger.logApiResponse(200, '/api/login', data: responseData);
appLogger.logApiError('POST', '/api/login', error, responseData: errorData);
```

### Log performance
```dart
final stopwatch = Stopwatch()..start();
// ... operation
stopwatch.stop();
appLogger.logPerformance('API call', stopwatch.elapsed);
```

## Testing Migration

### 1. Kiểm tra imports
```bash
grep -r "import 'package:logger/logger.dart'" lib/
```

### 2. Kiểm tra Logger() instances
```bash
grep -r "Logger()" lib/
```

### 3. Kiểm tra logging calls
```bash
grep -r "logger\." lib/
grep -r "_logger\." lib/
```

## Rollback Plan

Nếu có vấn đề, bạn có thể rollback bằng cách:

1. Revert các thay đổi trong file đã migrate
2. Giữ nguyên AppLogger cho các file mới
3. Migrate từng file một cách cẩn thận

## Best Practices

1. **Migrate từng file một**: Không migrate tất cả cùng lúc
2. **Test sau mỗi migration**: Đảm bảo app vẫn hoạt động
3. **Sử dụng AppLogger cho code mới**: Tất cả code mới nên sử dụng AppLogger
4. **Giữ Logger() cho legacy code**: Nếu cần thiết, có thể giữ Logger() cho code cũ

## Checklist

- [ ] Import AppLogger thay vì Logger
- [ ] Xóa Logger() instances
- [ ] Thay thế logger calls bằng appLogger calls
- [ ] Test functionality
- [ ] Kiểm tra log output
- [ ] Update documentation nếu cần

## Support

Nếu gặp vấn đề trong quá trình migration:

1. Kiểm tra import statements
2. Đảm bảo AppLogger đã được initialize
3. Kiểm tra log level configuration
4. Test với debug và release builds 