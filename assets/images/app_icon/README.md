# Hướng dẫn tạo App Icon cho Flutter

## <PERSON><PERSON> tả
Thư mục này chứa các tài nguyên và hướng dẫn để tạo app icon cho ứng dụng Flutter Kiloba Biz.

## Cấu trúc thư mục
```
assets/images/app_icon/
├── app_icon.png          # File icon chính (1024x1024px)
├── readme.md            # File hướng dẫn này
└── .gitkeep            # File giữ thư mục trong git
```

## Yêu cầu
- File `app_icon.png` có kích thước tối thiểu **1024x1024 pixels**
- Định dạng PNG với background trong suốt (nếu cần)
- Thiết kế icon phù hợp với guidelines của iOS và Android

## Cách sử dụng

### Bước 1: Chuẩn bị icon
1. Đặt file icon với tên `app_icon.png` và<PERSON> thư mục này
2. <PERSON><PERSON><PERSON> bảo kích thước tối thiểu 1024x1024px
3. Thi<PERSON><PERSON> kế phù hợp với Material Design (Android) và Human Interface Guidelines (iOS)

### Bước 2: Kiểm tra cấu hình
Kiểm tra file `pubspec.yaml` ở thư mục gốc project:
```yaml
flutter_launcher_icons:
  image_path: "assets/images/app_icon/app_icon.png"
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
```

### Bước 3: Cài đặt package (nếu chưa có)
```bash
flutter pub add --dev flutter_launcher_icons
```

### Bước 4: Tạo icon cho tất cả platform
```bash
flutter pub get
flutter pub run flutter_launcher_icons
```

## Kết quả
Sau khi chạy lệnh, hệ thống sẽ tự động tạo:

### Android:
- Các file icon với nhiều độ phân giải khác nhau trong `android/app/src/main/res/`
- Từ mipmap-hdpi đến mipmap-xxxhdpi

### iOS:
- Cập nhật AppIcon.appiconset trong `ios/Runner/Assets.xcassets/`
- Tự động tạo các kích thước yêu cầu của iOS

### Các platform khác:
- macOS: Cập nhật icon trong `macos/Runner/Assets.xcassets/`
- Web: Tạo favicon và các icon khác trong thư mục `web/`
- Windows: Cập nhật icon trong `windows/runner/resources/`

## Lưu ý quan trọng
⚠️ **Trước khi chạy lệnh:**
- Đảm bảo đường dẫn trong `flutter_launcher_icons.yaml` chính xác
- Backup các icon hiện tại (nếu có)
- Test trên cả iOS và Android sau khi tạo

## Troubleshooting

### Lỗi "Image not found"
- Kiểm tra đường dẫn trong `flutter_launcher_icons.yaml`
- Đảm bảo file `app_icon.png` tồn tại và có quyền đọc

### Icon không hiển thị đúng
- Xóa app và cài lại (clean install)
- Trên iOS: `flutter clean && flutter build ios`
- Trên Android: `flutter clean && flutter build apk`

### Kích thước không phù hợp
- Sử dụng icon có kích thước tối thiểu 1024x1024px
- Đảm bảo tỷ lệ 1:1 (vuông)

## Liên hệ
Nếu gặp vấn đề, liên hệ team development để được hỗ trợ.