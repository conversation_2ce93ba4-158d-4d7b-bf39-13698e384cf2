# Hướng dẫn tạo Splash Screen cho Flutter

## <PERSON><PERSON> tả
Thư mục này chứa các tài nguyên và hướng dẫn để tạo splash screen cho ứng dụng Flutter Kiloba Biz.

## Cấu trúc thư mục
```
assets/images/splash/
├── splash_logo.png      # File logo splash screen chính
├── README.md           # File hướng dẫn này
└── .gitkeep           # File giữ thư mục trong git
```

## Yêu cầu
- File `splash_logo.png` có kích thước tối thiểu **512x512 pixels**
- Định dạng PNG với background trong suốt (khuyến nghị)
- Logo phù hợp với thiết kế tổng thể của ứng dụng

## Cách sử dụng

### Bước 1: Chuẩn bị splash logo
1. Đặt file logo với tên `splash_logo.png` vào thư mục này
2. <PERSON><PERSON><PERSON> bảo kích thước tối thiểu 512x512px
3. Thi<PERSON><PERSON> kế logo phù hợp với brand identity

### Bước 2: Kiểm tra cấu hình
Kiểm tra file `pubspec.yaml` ở thư mục gốc project:
```yaml
flutter_native_splash:
  color: "#FFFFFF"                         # Màu nền splash screen
  image: assets/images/splash/splash_logo.png  # Đường dẫn đến logo
  
  # Cấu hình cho Android
  android: true
  android_gravity: center                  # Vị trí logo trên Android
  android_12:
    image: assets/images/splash/splash_logo.png
    color: "#FFFFFF"
  
  # Cấu hình cho iOS  
  ios: true
  ios_content_mode: center                 # Vị trí logo trên iOS
  
  # Cấu hình cho Web
  web: true
  web_image_mode: center
  
  # Cấu hình cho desktop
  macos: true
  windows: true
  linux: true
```

### Bước 3: Cài đặt package (nếu chưa có)
```bash
flutter pub add --dev flutter_native_splash
```

### Bước 4: Tạo splash screen cho tất cả platform
```bash
flutter pub get
dart run flutter_native_splash:create
```

## Kết quả
Sau khi chạy lệnh, hệ thống sẽ tự động tạo:

### Android:
- Cập nhật `android/app/src/main/res/drawable/launch_background.xml`
- Tạo các drawable cho splash screen trong thư mục res
- Cập nhật styles.xml cho Android 12+

### iOS:
- Cập nhật `ios/Runner/Assets.xcassets/LaunchImage.imageset/`
- Cập nhật LaunchScreen.storyboard
- Tạo các kích thước LaunchImage phù hợp

### Web:
- Cập nhật `web/index.html` với splash screen
- Tạo các icon và splash assets trong thư mục web

### Desktop:
- macOS: Cập nhật splash assets
- Windows & Linux: Cấu hình splash screen tương ứng

## Các tùy chọn cấu hình nâng cao

### Màu nền tùy chỉnh
```yaml
color: "#1976D2"                    # Hex color code
# hoặc
color_dark: "#000000"               # Màu cho dark mode
```

### Vị trí logo
```yaml
android_gravity: center             # center, top, bottom
ios_content_mode: center            # center, scaleAspectFit, scaleAspectFill
```

### Full screen splash
```yaml
fullscreen: true                    # Ẩn status bar và navigation bar
```

## Lưu ý quan trọng
⚠️ **Trước khi chạy lệnh:**
- Đảm bảo đường dẫn trong `pubspec.yaml` chính xác
- Backup các file splash hiện tại (nếu có)
- Test trên tất cả các platform sau khi tạo

⚠️ **Sau khi tạo splash:**
- Chạy `flutter clean` trước khi build
- Test trên device thật để đảm bảo hiển thị đúng
- Kiểm tra thời gian hiển thị splash phù hợp

## Troubleshooting

### Lỗi "Image not found"
- Kiểm tra đường dẫn trong `pubspec.yaml`
- Đảm bảo file `splash_logo.png` tồn tại và có quyền đọc

### Splash không hiển thị
- Chạy `flutter clean && flutter pub get`
- Rebuild app hoàn toàn
- Trên iOS: Xóa app và cài lại

### Logo bị méo hoặc không đúng kích thước
- Sử dụng logo có tỷ lệ phù hợp
- Điều chỉnh `android_gravity` và `ios_content_mode`

### Màu nền không đúng
- Kiểm tra format màu (phải là hex code)
- Đảm bảo có dấu # ở đầu

## Xóa splash screen
Nếu muốn xóa splash screen:
```bash
dart run flutter_native_splash:remove
```

## Liên hệ
Nếu gặp vấn đề, liên hệ team development để được hỗ trợ. 